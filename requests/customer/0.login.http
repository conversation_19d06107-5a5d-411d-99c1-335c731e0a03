# For a quick start check out our HTTP Requests collection (Tools|HTTP Client|Open HTTP Requests Collection).
#
# Following HTTP Request Live Templates are available:
# * 'gtrp' and 'gtr' create a GET request with or without query parameters;
# * 'ptr' and 'ptrp' create a POST request with a simple or parameter-like body;
# * 'mptr' and 'fptr' create a POST request to submit a form with a text or file field (multipart/form-data);

//https://www.jetbrains.com/help/idea/http-client-in-product-code-editor.html

### 0, 发送短信
POST {{host}}/api/manage/sms
Content-Type: application/x-www-form-urlencoded

mobile=13812345678

### 1, 注册
POST {{host}}/api/manage/cus/reg
Content-Type: application/x-www-form-urlencoded

phoneId=a00001&mobile=13801234567&password=123456&code=123456

### 2, 登录
POST {{host}}/api/manage/cus/login2
Content-Type: application/x-www-form-urlencoded

phoneId=oC-ha5EYB-LrUEmPNz1MNQaJyzsg&username=15711411367&password=12345678

> {%
//client.event("response data: " + response.body.data.token);
client.global.set("auth_token_cus", response.body.data.token);
%}

### 3, 查询
POST {{host}}/api/cus/goods/goods_list_data
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_cus}}

goodsTime=&pointName=&type=2&page=1&rows=10

### 4.0, 查询客商对应企业服务器《合同，票号区间》 接口
POST {{host}}/api/cus/contract/biz_contract_code
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

unitCode=0010011001&bizType=1

### 4.1, 查询客商对应企业服务器《商品,二级单位，场区 三者的树形结构数据》 接口
POST {{host}}/api/cus/contract/variety_code2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

unitCode=&bizContractCode=

### 5.0, 查询客商对应企业服务器《商品,二级单位，场区 三者的树形结构数据》 接口
POST {{host}}/api/cus/contract/variety_code2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

unitCode=&bizContractCode=

### 5.1, 查询客商对应企业服务器《商品,二级单位，场区 三者的树形结构数据》 接口
POST {{host}}/api/cus/contract/variety_code2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

unitCode=&bizContractCode=


### 6.0,查询货运信息 接口
POST {{host}}/api/cus/goods/goods_list_data2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

page=1&rows=10

### 6.1,查询货运信息 接口
POST {{host}}/api/cus/goods/goods_list_data3
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

page=1&rows=10

### 6.2,查询货运信息 接口
POST {{host}}/api/cus/goods/need_net_weight
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

subCode=011001002

### 6.3,查询货运信息 接口
POST {{host}}/api/cus/goods/update_Goods_inNetWeight
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

gid=d7b60dfd60fd48f492df05dd0d10b11b&inNetWeight=
###gid=77300706c65c422abcbe8b4e4af234de&inNetWeight=3.2

### 7.0,查询订单物流信息 接口
POST {{host}}/api/cus/order_logistics/query_order_logistics
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

oid=811f95dd2f3444de8c5a5a607ebc8c6f
### oid=eeebebb1cecf4922bf245d98223b0197


### 8.1,查询订单物流信息 接口
POST {{host}}/api/cus/order/search_gid_order2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

gid=d7b60dfd60fd48f492df05dd0d10b11b&page=1&rows=10

### 8.2,查询订单物流信息 接口
POST {{host}}/api/cus/order/search_gid_order3
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

gid=d7b60dfd60fd48f492df05dd0d10b11b&page=1&rows=10

### 8.3,查询货运信息 接口
POST {{host}}/api/cus/order/update_order_inNetWeight
Content-Type: application/x-www-form-urlencoded
x-access-token-v2:{{auth_token_cus}}

oid=00354a8ce4974f30aa70a26f26b3b884&inNetWeight=
###