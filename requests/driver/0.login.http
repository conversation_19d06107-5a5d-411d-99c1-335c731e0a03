# For a quick start check out our HTTP Requests collection (Tools|HTTP Client|Open HTTP Requests Collection).
#
# Following HTTP Request Live Templates are available:
# * 'gtrp' and 'gtr' create a GET request with or without query parameters;
# * 'ptr' and 'ptrp' create a POST request with a simple or parameter-like body;
# * 'mptr' and 'fptr' create a POST request to submit a form with a text or file field (multipart/form-data);

//https://www.jetbrains.com/help/idea/http-client-in-product-code-editor.html

### 0, 发送短信
POST {{host}}/api/manage/sms
Content-Type: application/x-www-form-urlencoded

mobile=13245678900

### 1, 注册
POST {{host}}/api/manage/dri/reg
Content-Type: application/x-www-form-urlencoded

phoneId=a00001&carNum=京A12345&mobile=13812345678&code=123456&deviceId=deviceID

### 2, 登录 [oJxle5cfuQe-7yA6QVu8SsyxzDKI, 13848570238, 095436, o0jxe1Md0OVDAE9-M720hr9KQy1o]
POST {{host}}/api/manage/dri/login2
Content-Type: application/x-www-form-urlencoded

phoneId=ssd&mobile=13245678900&deviceId=o0jxe1Md0OVDAE9-M720hr9KQy1o&timestamp=1734567890&code=123456

> {%
//client.event("response data: " + response.body.data.token);
client.global.set("auth_token_dvr", response.body.data.token);
%}

### 3, 查询
POST {{host}}/api/dvr/selectOne_dvr_info
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

### 3, 查询
POST {{host}}/web/test/unique_5
Content-Type: application/x-www-form-urlencoded

sign=3a6d7c47cf5e4c158ece460829f22bde&type=1

### 4.0, 订单信息查询 分页 接口
POST {{host}}/api/dvr/order/order_query2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

finishTag=2&page=1&rows=10

### 4.1, 司机查询tranStatus3，5订单 接口
POST {{host}}/api/dvr/order/get_35tran_status
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

sign=3a6d7c47cf5e4c158ece460829f22bde&type=1

### 5.0, 维护订单物流信息 接口
POST {{host}}/api/dvr/order_logistics/add_ord_log
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

oid=811f95dd2f3444de8c5a5a607ebc8c6f&longitude=112.168478&latitude=37.36079

### 6.0, 订单号查询热线电话 接口
POST {{host}}/api/dvr/order/get_hotline_by_oid
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

oid=366be261669b4244a470dbe6e9543c0a

### 7.0, 司机查询订单是否需要录入对方净重 接口
POST {{host}}/api/dvr/order/check_in_net_weight
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

oid=366be261669b4244a470dbe6e9543c0a

### 8.0, 司机录入对方净重 接口
POST {{host}}/api/dvr/order/update_in_net_weight
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

oid=00354a8ce4974f30aa70a26f26b3b884&inNetWeight=2.1&inNetWeightPho=wwer&loadPound=1212wwe

### 9.0, 查询司机关联车辆信息 接口
POST {{host}}/api/dvr/get_driver_cars
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

### 10.0, 司机补充修改关联车辆皮重信息 接口
POST {{host}}/api/dvr/update_tare_weight
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

id=626a4cab10ad8220d7f634ce&tareWeight=6.7

### 11.0, 司机确定正在使用车辆车牌 接口
POST {{host}}/api/dvr/chose_car
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

carNum=浙BC9189

### 11.1, 司机确定正在使用车辆车牌 接口
POST {{host}}/api/dvr/chose_car2
Content-Type: application/x-www-form-urlencoded
x-access-token-v2: {{auth_token_dvr}}

carNum=浙BC9191


### 3, 查询
POST {{host}}/web/test/unique_6
Content-Type: application/x-www-form-urlencoded

sign=3a6d7c47cf5e4c158ece460829f22bde&oid=cc&inNetWeight=0.0
###
