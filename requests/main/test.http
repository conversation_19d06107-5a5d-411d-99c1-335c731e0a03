### 保存
POST {{host}}/web/sys/unit/unit_list
x-access-token: {{auth_token}}
Accept: application/json

//----------------------------------------------------------------------------------------------------------------------
### UUID 函数
GET http://httpbin.org/uuid

//======================================================================================================================
// GET, POST 有两种方式, 由 Content-Type 决定:
// 1, Content-Type: application/json (后台要用 RequestBody 接收)
// 可以在 Body 中使用 JSON 对象:
// {
//   "user": "admin",
//   "pass": "admin"
// }
// 2, Content-Type: application/x-www-form-urlencoded
// 可以在 Query 中使用拼接参数:
// user=admin&pass=admin
//======================================================================================================================

### 身份证
POST {{host}}/web/test/ocr/idcard
#请求类型
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="aaa";
Content-Type: text/plain

Name
--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="000.jpg";
Content-Type: image/jpeg

#文件地址
< D:\Desktop\ocr\idcard.jpg
--WebAppBoundary--

###

//======================================================================================================================

### 健康码
POST {{host}}/web/test/ocr/healthcode
#请求类型
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="aaa";
Content-Type: text/plain

Name
--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="000.jpg";
Content-Type: image/jpeg

#文件地址
< D:\Desktop\ocr\idcard.jpg
--WebAppBoundary--

###

//======================================================================================================================

### 行程卡
POST {{host}}/web/test/ocr/travelcard
#请求类型
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="aaa";
Content-Type: text/plain

Name
--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="000.jpg";
Content-Type: image/jpeg

#文件地址
< D:\Desktop\ocr\travelcard.jpg
--WebAppBoundary--

###