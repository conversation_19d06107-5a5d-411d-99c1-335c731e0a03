# For a quick start check out our HTTP Requests collection (Tools|HTTP Client|Open HTTP Requests Collection).
#
# Following HTTP Request Live Templates are available:
# * 'gtrp' and 'gtr' create a GET request with or without query parameters;
# * 'ptr' and 'ptrp' create a POST request with a simple or parameter-like body;
# * 'mptr' and 'fptr' create a POST request to submit a form with a text or file field (multipart/form-data);

//https://www.jetbrains.com/help/idea/http-client-in-product-code-editor.html

### 0, 发送短信
POST {{host}}/api/manage/sms
Content-Type: application/x-www-form-urlencoded

mobile=13801234567

### 1, 注册
POST {{host}}/api/manage/cus/reg
Content-Type: application/x-www-form-urlencoded

phoneId=a00001&mobile=13801234567&password=123456&code=123456

### 2, 登录
POST {{host}}/api/manage/cus/login
Content-Type: application/x-www-form-urlencoded

phoneId=a00001&username=13801234567&password=123456&code=123456

> {%
//client.event("response data: " + response.body.data.token);
client.global.set("auth_token", response.body.data);
%}

###


### 2, 企业登录
POST {{host}}/api/bus/biz_login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

> {%
client.global.set("auth_token", response.body.data.token);
%}

### 2.1, 查询单位数据
POST {{host}}/api/bus/syn_order/search_unit
Content-Type: application/json
x-access-token-v2:{{auth_token}}

{

  "rows": "300000",
  "startTime": "2021-01-01 00:00:00",
  "endTime": "2020-01-01 00:00"
}

### 2.2, 扫描司机二维码到平台修改订单(一单)检票状态 接口
POST {{host}}/api/bus/receive_car_msg/update_one_order3
Content-Type: application/json
x-access-token-v2:{{auth_token}}



### 2.3, 查询billCode
POST {{host}}/api/bus/receive_car_msg/search_bill_code_by_identity
Content-Type: application/json
x-access-token-v2:{{auth_token}}

{

  "identity": "330106200503287127",
  "unitCode": "0010011001",
  "subCode": ""
}

### 2.4, 按deviceCode修改deviceId
POST {{host}}/api/bus/receive_car_msg/update_device_id
Content-Type: application/json
x-access-token-v2:{{auth_token}}

{

  "deviceCode": "011001-d001"
}

### 3, 企业系统代客商线下下单2 接口
POST {{host}}/api/bus/goods/biz_push_goods2
Content-Type: application/json
x-access-token-v2:{{auth_token_cus}}

{"bizunitname":"国能蒙西煤化工股份有限公司","sign":"8HMqmnHnFmZaZB1K9W3Pljgnk+Q=","bizcontractcode":"TO0115320003",
  "oareacode":"","billtype":6,"startnum":"38401","bizunitcode":"UO0115320003","productname":"1/3焦煤","endnum":"38500",
  "unitcode":"011532","areaname":"棋盘井洗煤厂","subname":"国能蒙西煤化工股份有限公司","tradesubcode":"011532001",
  "limitvalue":"0.0","transportplace":"乌海市","bizcontractname":"国能蒙西煤化工股份有限公司","oworkshopcode":"",
  "payvalue":"0.0","oproducingcode":"","carrier":"","defaultarea":"011532001001","customerusercode":"0115320016"
}

### 4, 查询billCode
POST {{host}}/api/bus/receive_car_msg/search_same_order
Content-Type: application/json
x-access-token-v2:{{auth_token}}

{
  "oid": "6af6c70ffea9444188dd1d6b979fb4c0",
  "unitCode": "0010011065",
  "subCode": "011065001"
}

###