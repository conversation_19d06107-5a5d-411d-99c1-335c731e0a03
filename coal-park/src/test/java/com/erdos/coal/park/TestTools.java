package com.erdos.coal.park;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class TestTools {

    private MockMvc mockMvc;

    public String token = "";

    public TestTools(MockMvc mockMvc) {
        this.mockMvc = mockMvc;
    }

    public JSONObject doSend(String url, Map<String, String> param) throws Exception {

        MockHttpServletRequestBuilder mhsrr = post(url);
        for (Map.Entry<String, String> entry : param.entrySet()) {//参数...
            mhsrr.param(entry.getKey(), entry.getValue());
        }

        if (token != null)
            mhsrr.header("x-access-token", token);

        MvcResult result = mockMvc.perform(mhsrr).andExpect(status().isOk()).andReturn(); //.andDo(print())

        String content = result.getResponse().getContentAsString(); //得到返回结果
        //Assert.assertThat(content, containsString("成功")); //断言，包含文字
        //System.out.println(result.getResponse().getContentAsString());
        return JSON.parseObject(content);
    }

    public boolean login(String user, String pass) {
        Map<String, String> params = new HashMap<>();
        params.put("username", user);
        params.put("password", pass);
        JSONObject json = null;
        try {
            json = doSend("/api/manage/cus/login", params);

            if (json.containsKey("status") && json.getInteger("status") == 0) {
                //{"data":{"token":"jhM7uw....."},"msg":"登录成功","status":0}
                if (json.containsKey("data")) {
                    JSONObject obj = json.getJSONObject("data");
                    if (obj.containsKey("token")) {

                        token = obj.getString("token");

                        System.out.println(token);
                        System.out.println("完成");
                    }
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

}
