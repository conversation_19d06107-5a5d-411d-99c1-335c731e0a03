package com.erdos.coal.park.manage;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.Application;
import com.erdos.coal.park.TestTools;
import org.junit.After;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class ManageAndSMSTests {

    //参考: https://blog.csdn.net/xiaolyuh123/article/details/73281522/
    //关键字: springboot controller 测试

    @Autowired
    private MockMvc mockMvc;

    private static TestTools testTools = null;
    private Map<String, String> params = new HashMap<>();

    private static String m_Mobile = "13912345678";
    private static String m_UserName = m_Mobile;
    private static String m_Password = "12345";
    private static String m_Code = "123456";

    @Before
    public void onBefore() {
        params.clear();
    }

    @Test //初始化方法
    public void a() {
        testTools = new TestTools(mockMvc);
    }

    //TODO: ================================================================================================

    //TODO: 1，验证码
    @Test
    public void test1() throws Exception {
        params.put("mobile", m_Mobile);
        JSONObject json = testTools.doSend("/api/manage/sms", params);
        if (json.containsKey("status") && json.getInteger("status") == 0) {
            //"{"status":0,"msg":"发送成功","data":"123456"}"
            System.out.println("发送成功!");
        }
    }

    //TODO: 2, 注册
    @Test
    public void test2() throws Exception {
        params.put("mobile", m_Mobile);
        params.put("password", m_Password);
        params.put("code", m_Code);
        JSONObject json = testTools.doSend("/api/manage/cus/reg", params);
        if (json.containsKey("status") && json.getInteger("status") == 0) {
            //{"msg":"该号码已经存在","status":1}
            System.out.println("注册成功!");
        }
    }

    //TODO: 3, 登录
    @Test
    public void test3() throws Exception {
        testTools.login(m_Mobile, m_Password);
    }

    @After
    public void onAfter() {
    }


}
