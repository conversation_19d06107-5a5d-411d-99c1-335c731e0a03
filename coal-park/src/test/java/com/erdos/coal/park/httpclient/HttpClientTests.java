package com.erdos.coal.park.httpclient;

import com.erdos.coal.Application;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HttpClientTests {

    @Autowired
    private IHttpAPIService httpAPIService;

    @Test
    public void test() {
        String str = null;
        for (int i = 0; i < 2; i++) {
            try {
                str = httpAPIService.doGet("http://www.baidu.com");
            } catch (Throwable e) {
                System.out.println(e.getMessage());
            }
            System.out.println(i + "; " + str);
        }
    }
}
