package com.erdos.coal.park.manage;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.Application;
import com.erdos.coal.park.TestTools;
import org.junit.After;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AppLogTests {

    //参考: https://blog.csdn.net/xiaolyuh123/article/details/73281522/
    //关键字: springboot controller 测试

    @Autowired
    private MockMvc mockMvc;

    private static TestTools testTools = null;

    private Map<String, String> params = new HashMap<>();

    @Before
    public void onBefore() {
        params.clear();
    }

    @Test //初始化方法
    public void a() {
        testTools = new TestTools(mockMvc);
    }

    //TODO: 登录
    @Test
    public void test() throws Exception {
        testTools.login("13912345678", "12345");
    }

    //TODO: ================================================================================================

    @Test
    public void test1() throws Exception {

        JSONObject json = testTools.doSend("/api/cus/delete_log", params);
        System.out.println(json.toJSONString());

    }

    @After
    public void onAfter() {
    }


}
