package com.erdos.coal;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayCustomerConfig;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import dev.morphia.query.Query;
import org.junit.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class Tests extends Tester {

    @Resource
    private WXPayCustomerConfig config;
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private IOrderService orderService;

    /**
     * @BeforeClass: 这个注解表示这个方法会在所有测试方法执行之前执行
     * 因为是static修饰的静态方法，所有只会执行一次。通常用来进行一些
     * 资源的加载，如通过JUnit测试Spring相关的东西时，可以在这个方法
     * 中加载Spring的配置文件
     */
    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        System.out.println("this is before class");
    }

    /**
     * @AfterClass: 这个注解表示这个方法会在所有方法执行完毕之后执行，
     * 通常用来释放资源
     */
    @AfterClass
    public static void tearDownAfterClass() throws Exception {
        System.out.println("this is after class");
    }

    /**
     * @Before: 这个注解表示这个方法会在每个测试方法执行之前执行一次
     * 有多少个测试方法就会执行多少次
     */
    @Before
    public void testBefore() {
        System.out.println("before");
        beginDate = new Date();
    }

//    @TestObj
//    public void testOne() {
//        System.out.println("test hello 1");
//    }
//
//    @TestObj
//    public void testTwo() {
//        System.out.println("test hello 2");
//        TestCase.assertEquals(1, 1);
//    }

    /**
     * @After: 这个注解表示这个方法会在每个测试方法执行之后执行一次
     * 有多少个测试方法就会执行多少次
     */
    @After
    public void testAfter() {
        System.out.println("after");
        endDate = new Date();
        System.out.println("程序运行时间【" + (endDate.getTime() - beginDate.getTime()) + "】毫秒");
    }

    @Autowired
    private TestRestTemplate testRestTemplate;
    private Map<Integer, HttpEntity> mapHttpEntity = new HashMap<>();

    //HTTP TestObj --------------------------------------------------------------------------------------------------------

    //TODO: GET请求测试
//    @TestObj
//    public void get() throws Exception {
//        Map<String, String> multiValueMap = new HashMap<>();
//        multiValueMap.put("username", "lake");//传值，但要在url上配置相应的参数
//        String result = testRestTemplate.getForObject("/app/sample/main?username={username}", String.class, multiValueMap);
//        System.out.println(result);
//    }

    @Test
    public void driverPool() {
        DriverPool driverPool = new DriverPool();
        CustomerUser user = customerUserService.getByPK("5d021d984405952dff5be039");
        driverPool.setCustomerUser(user);
        DriverInfo driverInfo = driverInfoService.getByPK("5d4d1d25655f84239ee0b1db");
        driverPool.setDriverInfo(driverInfo);
        driverPool.setCid("5d021d984405952dff5be039");
        driverPool.setDid("5d4d1d25655f84239ee0b1db");
        driverPool.setWhiteOrBlack(0);
        driverPoolService.save(driverPool);
    }

    //TODO: POST请求测试
    @Test
    public void post() throws Exception {

        System.out.println("----程序开始运行----");

        int taskSize = 1000;

        //TODO: 1, 登录

        // 创建一个线程池
        ExecutorService pool = Executors.newFixedThreadPool(taskSize);
        // 创建多个有返回值的任务
        List<Future> list = new ArrayList<Future>();
        for (int i = 0; i < taskSize; i++) {
            Callable<String> c = new MyCallableLogin(i, "user");
            Future<String> f = pool.submit(c);// 执行任务并获取Future对象
            list.add(f);
        }
        pool.shutdown();// 关闭线程池

        // 获取所有并发任务的运行结果
        for (Future f : list) {
            // 从Future对象上获取任务的返回值，并输出到控制台
            System.out.println(">>>" + f.get().toString());
        }

        //TODO: 2, 处理业务
        // 创建一个线程池
        ExecutorService pool1 = Executors.newFixedThreadPool(taskSize);
        // 创建多个有返回值的任务
        List<Future> list1 = new ArrayList<Future>();
        for (int i = 0; i < taskSize; i++) {
            Callable<Object> c = new MyCallable(i);
            // 执行任务并获取Future对象
            Future f = pool1.submit(c);
            // System.out.println(">>>" + f.get().toString());
            list1.add(f);
        }
        // 关闭线程池
        pool1.shutdown();

        // 获取所有并发任务的运行结果
        for (Future f : list1) {
            // 从Future对象上获取任务的返回值，并输出到控制台
            System.out.println(">>>" + f.get().toString());
        }
    }

    //登录
    class MyCallableLogin implements Callable<String> {

        private Integer i;
        private String user;

        MyCallableLogin(Integer i, String user) {
            this.i = i;
            this.user = user;
        }

        public String call() throws Exception {
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("userCode", user + i);
            map.add("sign", Utils.md5(user + i));
            ResponseEntity<String> responseEntity = testRestTemplate.postForEntity("/api/bus/test", map, String.class);
            //添加cookie以保持状态
            HttpHeaders headers = new HttpHeaders();
            String headerValue = responseEntity.getHeaders().get("Set-Cookie").toString().replace("[", "");
            headerValue = headerValue.replace("]", "");
            headers.set("Cookie", headerValue);
            mapHttpEntity.put(i, new HttpEntity(headers));
            return responseEntity.getBody();
            //assertThat(responseEntity.getBody()).isEqualTo(expectStr);
        }
    }

    class MyCallable implements Callable<Object> {

        private int taskNum;

        MyCallable(int taskNum) {
            this.taskNum = taskNum;
        }

        public Object call() throws Exception {
            //System.out.println(">>>" + taskNum + "任务启动");
            Date dateTmp1 = new Date();

            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<String, String>();
            multiValueMap.add("e", String.valueOf(taskNum));
            multiValueMap.add("userCode", "user" + taskNum);
            multiValueMap.add("sign", Utils.md5("user" + taskNum));

            String params = "?e=" + String.valueOf(taskNum) + "&userCode=user" + taskNum + "&sign=" + Utils.md5("user" + taskNum);

            ResponseEntity<ServerResponse> r = testRestTemplate.exchange("/api/bus/test1" + params,
                    HttpMethod.POST,
                    mapHttpEntity.get(taskNum),
                    ServerResponse.class);

            ServerResponse result = r.getBody();

            Assert.assertEquals(Long.valueOf(result.getStatus()), Long.valueOf(ResponseCode.SUCCESS.getCode()));

            Date dateTmp2 = new Date();
            long time = dateTmp2.getTime() - dateTmp1.getTime();
            //System.out.println(">>>" + taskNum + "任务终止");
            return "线程: " + taskNum + "结果:" + result.getData() + "，用时【" + time + "】毫秒";
        }
    }

    @Test
    public void sign() throws Exception {
        Map<String, String> map = new HashMap<>();
        String xmlString = "<?xml version=\\\"1.0\\\" encoding=\\\"utf-8\\\"?><!DOCTYPE root [<!ENTITY xxe SYSTEM \\\"http://101.91.62.170:4837/wx_xxe_dbc_os_2019071003_216?********************************************************************\\\">]><foo><value>&xxe;</value></foo>";
        map = WXPayUtil.xmlToMap(xmlString);
        //  String sign = WXPayUtil.generateSignature(map, WXPayConstants.key, WXPayConstants.SignType.MD5);


        //Transaction transaction = transactionService.get("outTradeNo", map.get("out_trade_no"));
        //"507010865275C6F81F6EBCCBCD411CC2","5E9967DB0AE4AB3E08812F2EFCCEFB70""

    }


    @Test
    public void downloadBill() {
        WXPay wxpay = null;
        try {
            wxpay = new WXPay(config, true, false);
            Map<String, String> map = new HashMap<>();
            map.put("bill_date", "20190709");
            map.put("bill_type", "ALL");
            Map<String, String> billmap = new HashMap<>();
            billmap = wxpay.downloadBill(map);
            if (billmap.get("return_code").equals("success")) {
                System.out.println(billmap.get("data"));
            } else if (billmap.get("return_msg").equals("NO Bill Exist")) {
                System.out.println("账单不存在");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void transfers() throws Exception {
        WXPay wxpay = new WXPay(config, wxPayConstants.notifyUrl, true, false);
        //1.0 拼凑企业支付需要的参数
        String appid = config.getAppID();  //微信公众号的appid
        String mch_id = config.getMchID(); //商户号
        String nonce_str = WXPayUtil.generateNonceStr(32); //生成随机数
        String partner_trade_no = nonce_str; //生成商户订单号
        String openid = "omkA31gvoNTTZ7NH3vcLKAiBBK60"; // 支付给用户openid
        String check_name = "NO_CHECK"; //是否验证真实姓名呢
        String re_user_name = "小郑";   //收款用户姓名
        String amount = "1";                //企业付款金额，单位为分
        String desc = "测试开发，稍后会还给公司的";   //企业付款操作说明信息。必填。
        String spbill_create_ip = "*************";    //调用接口的机器Ip地址

        //2.0 生成map集合
        Map<String, String> packageParams = new HashMap<>();
        packageParams.put("mch_appid", appid);         //微信公众号的appid
        packageParams.put("mchid", mch_id);       //商户号
        packageParams.put("nonce_str", nonce_str);  //随机生成后数字，保证安全性

        packageParams.put("partner_trade_no", partner_trade_no); //生成商户订单号
        packageParams.put("openid", openid);            // 支付给用户openid
        packageParams.put("check_name", check_name);    //是否验证真实姓名呢
        packageParams.put("re_user_name", re_user_name);//收款用户姓名
        packageParams.put("amount", amount);            //企业付款金额，单位为分
        packageParams.put("desc", desc);                   //企业付款操作说明信息。必填。
        packageParams.put("spbill_create_ip", spbill_create_ip); //调用接口的机器Ip地址
        packageParams.put("sign", WXPayUtil.generateSignature(packageParams, config.getKey(), WXPayConstants.SignType.MD5));
        packageParams = wxpay.transfers(packageParams);
        System.out.println(packageParams.get("payment_no"));
    }
//    //TODO: file文件上传测试
//    @TestObj
//    public void upload() throws Exception {
//        Resource resource = new FileSystemResource("/home/<USER>/github/wopi/build.gradle");
//        MultiValueMap multiValueMap = new LinkedMultiValueMap();
//        multiValueMap.add("username", "lake");
//        multiValueMap.add("files", resource);
//        ServerResponse result = testRestTemplate.postForObject("/test/upload", multiValueMap, ServerResponse.class);
//        Assert.assertEquals(result.getStatus(), ResponseCode.SUCCESS);
//    }
//
//    //TODO: file文件下载测试
//    @TestObj
//    public void download() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxxx");
//        HttpEntity formEntity = new HttpEntity(headers);
//        String[] urlVariables = new String[]{"admin"};
//        ResponseEntity<byte[]> response = testRestTemplate.exchange("/test/download?username={1}",
//                HttpMethod.GET,
//                formEntity,
//                byte[].class,
//                urlVariables);
//
//        if (response.getStatusCode() == HttpStatus.OK) {
//            Files.write(Paths.get("c:/output.txt"), response.getBody());
//        }
//    }
//
//    //TODO: header请求头信息传输测试
//    @TestObj
//    public void getHeader() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxxx");
//        HttpEntity formEntity = new HttpEntity(headers);
//        String[] urlVariables = new String[]{"admin"};
//        ResponseEntity<ServerResponse> result = testRestTemplate.exchange("/test/getHeader?username={username}",
//                HttpMethod.GET,
//                formEntity,
//                ServerResponse.class,
//                urlVariables);
//        Assert.assertEquals(result.getBody().getStatus(), ResponseCode.SUCCESS);
//    }
//
//    //TODO: PUT信息修改
//    @TestObj
//    public void putHeader() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxxx");
//        MultiValueMap multiValueMap = new LinkedMultiValueMap();
//        multiValueMap.add("username", "lake");
//        HttpEntity formEntity = new HttpEntity(multiValueMap, headers);
//        ResponseEntity<ServerResponse> result = testRestTemplate.exchange("/test/putHeader",
//                HttpMethod.PUT,
//                formEntity,
//                ServerResponse.class);
//        Assert.assertEquals(result.getBody().getStatus(), ResponseCode.SUCCESS);
//    }
//
//    //TODO: DELETE删除信息
//    @TestObj
//    public void delete() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxx");
//        MultiValueMap multiValueMap = new LinkedMultiValueMap();
//        multiValueMap.add("username", "lake");
//        HttpEntity formEntity = new HttpEntity(multiValueMap, headers);
//        String[] urlVariables = new String[]{"admin"};
//        ResponseEntity<ServerResponse> result = testRestTemplate.exchange("/test/delete?username={username}",
//                HttpMethod.DELETE,
//                formEntity,
//                ServerResponse.class,
//                urlVariables);
//        Assert.assertEquals(result.getBody().getStatus(), ResponseCode.SUCCESS);
//    }

    @Test
    public void updUserPwd() throws Exception {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> conMap = new HashMap<>();
//        conMap.put("loginName", "admin");
        conMap.put("mobile", "13801234568");
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        //$2a$10$94/0TRrPSFQc0rWdnw5S2.i8atTsuFOXULFi5KC9q2KxLAZGTkoES
        String str = passwordEncoder.encode("123456");
        */
        String str = Utils.md5("123456");
        map.put("password", str);
//        userService.update(conMap, map);

        customerUserService.update(conMap, map);
    }

    @Test
    public void getWx() {
        Map<String, String> map = new HashMap<>();
//        map.put("out_trade_no","1587887077181GSqhRhgGbKPFZqcAvy");

//        map.put("out_trade_no", "1587878262510o3t5587qPkOH54XDrc");
//        WxResult wxResult = wechatOrderRecordService.payResult(map);
//        System.out.println(1);

        orderService.twoDimensionCode("411b3d481e1d48e9846546871de0975e");

    }

    @Test
    public void inList() {
        List<String> idList = new ArrayList<>();
        System.out.println(idList.size());

        idList.add("4d5a939fa48449b491158238d6a6699a");
        idList.add("811f95dd2f3444de8c5a5a607ebc8c6f");

        System.out.println(idList.size());
        Query<Order> query = orderService.createQuery();
        query.filter("oid in", idList);
        List<Order> orderList = orderService.list(query);
        System.out.println("1");
        Query<Order> query2 = orderService.createQuery();
        query2.filter("oid in", idList.toArray());
        List<Order> orderList2 = orderService.list(query2);
        System.out.println("3");
    }
}
