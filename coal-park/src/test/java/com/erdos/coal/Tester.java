package com.erdos.coal;

import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class Tester {

    protected Logger logger = LoggerFactory.getLogger(getClass());
    protected Date beginDate, endDate;

    @Before
    public void testBefore() {
        logger.info("开始");
        beginDate = new Date();
    }

    @After
    public void testAfter() {
        logger.warn("完成");
        endDate = new Date();
        logger.warn("程序运行时间【" + (endDate.getTime() - beginDate.getTime()) + "】毫秒");
    }

}
