package com.erdos.coal;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import org.junit.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class ApplicationTests extends Tester {

    /**
     * @BeforeClass: 这个注解表示这个方法会在所有测试方法执行之前执行
     * 因为是static修饰的静态方法，所有只会执行一次。通常用来进行一些
     * 资源的加载，如通过JUnit测试Spring相关的东西时，可以在这个方法
     * 中加载Spring的配置文件
     */
    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        System.out.println("this is before class");
    }

    /**
     * @AfterClass: 这个注解表示这个方法会在所有方法执行完毕之后执行，
     * 通常用来释放资源
     */
    @AfterClass
    public static void tearDownAfterClass() throws Exception {
        System.out.println("this is after class");
    }

    /**
     * @Before: 这个注解表示这个方法会在每个测试方法执行之前执行一次
     * 有多少个测试方法就会执行多少次
     */
    @Before
    public void testBefore() {
        System.out.println("before");
        beginDate = new Date();
    }

//    @TestObj
//    public void testOne() {
//        System.out.println("test hello 1");
//    }
//
//    @TestObj
//    public void testTwo() {
//        System.out.println("test hello 2");
//        TestCase.assertEquals(1, 1);
//    }

    /**
     * @After: 这个注解表示这个方法会在每个测试方法执行之后执行一次
     * 有多少个测试方法就会执行多少次
     */
    @After
    public void testAfter() {
        System.out.println("after");
        endDate = new Date();
        System.out.println("程序运行时间【" + (endDate.getTime() - beginDate.getTime()) + "】毫秒");
    }

    @Autowired
    private TestRestTemplate testRestTemplate;

    //HTTP TestObj --------------------------------------------------------------------------------------------------------

    //TODO: GET请求测试
//    @TestObj
//    public void get() throws Exception {
//        Map<String, String> multiValueMap = new HashMap<>();
//        multiValueMap.put("username", "lake");//传值，但要在url上配置相应的参数
//        String result = testRestTemplate.getForObject("/app/sample/main?username={username}", String.class, multiValueMap);
//        System.out.println(result);
//    }

    //TODO: POST请求测试
    @Test
    public void post() throws Exception {

        System.out.println("----程序开始运行----");

        int taskSize = 1000;
        // 创建一个线程池
        ExecutorService pool = Executors.newFixedThreadPool(taskSize);
        // 创建多个有返回值的任务
        List<Future> list = new ArrayList<Future>();
        for (int i = 0; i < taskSize; i++) {
            Callable c = new MyCallable(i);
            // 执行任务并获取Future对象
            Future f = pool.submit(c);
            // System.out.println(">>>" + f.get().toString());
            list.add(f);
        }
        // 关闭线程池
        pool.shutdown();

        // 获取所有并发任务的运行结果
        for (Future f : list) {
            // 从Future对象上获取任务的返回值，并输出到控制台
            System.out.println(">>>" + f.get().toString());
        }
    }

    class MyCallable implements Callable<Object> {

        private int taskNum;

        MyCallable(int taskNum) {
            this.taskNum = taskNum;
        }

        public Object call() throws Exception {
            //System.out.println(">>>" + taskNum + "任务启动");
            Date dateTmp1 = new Date();

            MultiValueMap multiValueMap = new LinkedMultiValueMap();
            multiValueMap.add("text", Utils.md5(String.valueOf(taskNum)));
            multiValueMap.add("content", Utils.md5(Utils.md5(String.valueOf(taskNum))));
            ServerResponse result = testRestTemplate.postForObject("/app/sample/sample_add_action",
                    multiValueMap,
                    ServerResponse.class);
            Assert.assertEquals(Long.valueOf(result.getStatus()), Long.valueOf(ResponseCode.SUCCESS.getCode()));

            Date dateTmp2 = new Date();
            long time = dateTmp2.getTime() - dateTmp1.getTime();
            //System.out.println(">>>" + taskNum + "任务终止");
            return "线程: " + taskNum + "，用时【" + time + "】毫秒";
        }
    }

//    //TODO: file文件上传测试
//    @TestObj
//    public void upload() throws Exception {
//        Resource resource = new FileSystemResource("/home/<USER>/github/wopi/build.gradle");
//        MultiValueMap multiValueMap = new LinkedMultiValueMap();
//        multiValueMap.add("username", "lake");
//        multiValueMap.add("files", resource);
//        ServerResponse result = testRestTemplate.postForObject("/test/upload", multiValueMap, ServerResponse.class);
//        Assert.assertEquals(result.getStatus(), ResponseCode.SUCCESS);
//    }
//
//    //TODO: file文件下载测试
//    @TestObj
//    public void download() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxxx");
//        HttpEntity formEntity = new HttpEntity(headers);
//        String[] urlVariables = new String[]{"admin"};
//        ResponseEntity<byte[]> response = testRestTemplate.exchange("/test/download?username={1}",
//                HttpMethod.GET,
//                formEntity,
//                byte[].class,
//                urlVariables);
//
//        if (response.getStatusCode() == HttpStatus.OK) {
//            Files.write(Paths.get("c:/output.txt"), response.getBody());
//        }
//    }
//
//    //TODO: header请求头信息传输测试
//    @TestObj
//    public void getHeader() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxxx");
//        HttpEntity formEntity = new HttpEntity(headers);
//        String[] urlVariables = new String[]{"admin"};
//        ResponseEntity<ServerResponse> result = testRestTemplate.exchange("/test/getHeader?username={username}",
//                HttpMethod.GET,
//                formEntity,
//                ServerResponse.class,
//                urlVariables);
//        Assert.assertEquals(result.getBody().getStatus(), ResponseCode.SUCCESS);
//    }
//
//    //TODO: PUT信息修改
//    @TestObj
//    public void putHeader() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxxx");
//        MultiValueMap multiValueMap = new LinkedMultiValueMap();
//        multiValueMap.add("username", "lake");
//        HttpEntity formEntity = new HttpEntity(multiValueMap, headers);
//        ResponseEntity<ServerResponse> result = testRestTemplate.exchange("/test/putHeader",
//                HttpMethod.PUT,
//                formEntity,
//                ServerResponse.class);
//        Assert.assertEquals(result.getBody().getStatus(), ResponseCode.SUCCESS);
//    }
//
//    //TODO: DELETE删除信息
//    @TestObj
//    public void delete() throws Exception {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("token", "xxxxx");
//        MultiValueMap multiValueMap = new LinkedMultiValueMap();
//        multiValueMap.add("username", "lake");
//        HttpEntity formEntity = new HttpEntity(multiValueMap, headers);
//        String[] urlVariables = new String[]{"admin"};
//        ResponseEntity<ServerResponse> result = testRestTemplate.exchange("/test/delete?username={username}",
//                HttpMethod.DELETE,
//                formEntity,
//                ServerResponse.class,
//                urlVariables);
//        Assert.assertEquals(result.getBody().getStatus(), ResponseCode.SUCCESS);
//    }

}
