package com.erdos.coal.mongo.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

@Entity(value = "test_member", noClassnameStored = true)
public class AggregationMember extends BaseMongoInfo {

    private Integer id;
    private String name;
    private Integer age;
    private String position;

    private Integer pid;

    private Integer status;

    public Integer getId() {
        return id;
    }

    public AggregationMember setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public AggregationMember setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getAge() {
        return age;
    }

    public AggregationMember setAge(Integer age) {
        this.age = age;
        return this;
    }

    public String getPosition() {
        return position;
    }

    public AggregationMember setPosition(String position) {
        this.position = position;
        return this;
    }

    public Integer getPid() {
        return pid;
    }

    public AggregationMember setPid(Integer pid) {
        this.pid = pid;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public AggregationMember setStatus(Integer status) {
        this.status = status;
        return this;
    }
}
