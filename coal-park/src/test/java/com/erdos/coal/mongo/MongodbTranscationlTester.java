package com.erdos.coal.mongo;

import com.erdos.coal.Tester;
import com.erdos.coal.mongo.service.IMongoDemoService;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;

public class MongodbTranscationlTester extends Tester {

    @Resource
    private IMongoDemoService mongoDemoService;

    @Test
    public void transcationl() {
        mongoDemoService.delete(mongoDemoService.createQuery());

        long result = mongoDemoService.transactionalTest();
        Assert.isTrue(result == 1, "事务测试失败");
    }

    @Test
    public void transcationl1() throws Exception {
        mongoDemoService.delete(mongoDemoService.createQuery());

        long result = mongoDemoService.transactionalTest1();
        Assert.isTrue(result == 1, "事务测试失败");
    }
}
