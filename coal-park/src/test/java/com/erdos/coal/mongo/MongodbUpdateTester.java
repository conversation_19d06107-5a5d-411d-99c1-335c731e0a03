package com.erdos.coal.mongo;

import com.erdos.coal.Tester;
import com.erdos.coal.mongo.entity.MongoDemo;
import com.erdos.coal.mongo.service.IMongoDemoService;
import dev.morphia.UpdateOptions;
import dev.morphia.query.UpdateResults;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MongodbUpdateTester extends Tester {

    @Resource
    private IMongoDemoService mongoDemoService;

    //------------------------------------------------------------------------------------------------------------------

    @Test
    public void updateTest() {
        mongoDemoService.delete(mongoDemoService.createQuery());

        UpdateResults updateResults;

        logger.info("增加 --------------------------------------------------------------------------");

        List<MongoDemo> list = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            list.add(MongoDemo.getInstance()
                    .setNum(i) // 1 -- 10
                    .setName("name" + i) // name1 -- name9
                    .setStatus(i % 2)
                    .setValue(new BigDecimal("10" + i)) // 101 -- 109
                    .setDate("Date" + i)
                    .setInTime(new Date())
            );
        }
        mongoDemoService.save(list); //TODO: save list

        logger.info("upsert 测试 ----------------------------------------------------------------------");

//        Query<MongoDemo> query = mongoDemoService.createQuery();
//        query.filter("num", 20);
//        UpdateOperations<MongoDemo> updateOperations = mongoDemoService.createUpdateOperations();
//        updateOperations.set("name", "test.upsert.name").set("date", "test.upsert.date");
//        updateResults = mongoDemoService.upsert(query, updateOperations);
//
//        Assert.isTrue(updateResults.getInsertedCount() == 1, "应该插入1条数据");
//
//        updateOperations.set("inTime", new Date());
//        updateResults = mongoDemoService.upsert(query, updateOperations);
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1, "应该更新1条数据");

        logger.info("update 测试 ----------------------------------------------------------------------");

        // update
        updateResults = mongoDemoService.update(
                mongoDemoService.createQuery().field("status").equal(1),
                mongoDemoService.createUpdateOperations().set("date", "update")
        );

        Assert.isTrue(updateResults.getUpdatedCount() == 5, "应该更新5条数据");

        // updateOne
        updateResults = mongoDemoService.updateOne(
                mongoDemoService.createQuery().field("status").equal(0),
                mongoDemoService.createUpdateOperations().set("date", "updateFirst1")
        );

        Assert.isTrue(updateResults.getUpdatedCount() == 1, "应该更新1条数据");

        updateResults = mongoDemoService.update(
                mongoDemoService.createQuery().field("status").equal(1),
                mongoDemoService.createUpdateOperations().set("value", new BigDecimal("999")),
                new UpdateOptions().upsert(true).multi(true)
        );

        Assert.isTrue(updateResults.getUpdatedCount() == 5, "应该更新5条数据");

//        updateResults = mongoDemoService.update("num", 8, "date", "updateConditionField");
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1, "应该更新1条数据");
//
//        updateResults = mongoDemoService.update("num", 20,
//                mongoDemoService.createUpdateOperations()
//                        .set("name", "name20")
//                        .set("status", 1)
//                        .set("value", 1011)
//                        .set("createTime", new Date())
//        );
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1, "应该更新1条数据");
        //Assert.notNull(resultObj, "返回空对象");
    }
}
