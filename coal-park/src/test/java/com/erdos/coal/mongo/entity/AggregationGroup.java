package com.erdos.coal.mongo.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.util.List;

@Entity(value = "test_group", noClassnameStored = true)
public class AggregationGroup extends BaseMongoInfo {

    private Integer id;
    private String name;

    private Integer status;

    private List<AggregationMember> mm; // 聚合数组, 类型为 List,Array 都可以

    public Integer getId() {
        return id;
    }

    public AggregationGroup setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public AggregationGroup setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public AggregationGroup setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public List<AggregationMember> getMm() {
        return mm;
    }

    public AggregationGroup setMm(List<AggregationMember> mm) {
        this.mm = mm;
        return this;
    }
}
