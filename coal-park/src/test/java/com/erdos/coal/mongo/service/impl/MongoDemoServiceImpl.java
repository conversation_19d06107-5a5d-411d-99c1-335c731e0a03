package com.erdos.coal.mongo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.mongo.dao.IMongoDemoDao;
import com.erdos.coal.mongo.entity.MongoDemo;
import com.erdos.coal.mongo.entity.TestObjSum;
import com.erdos.coal.mongo.service.IMongoDemoService;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.UpdateResult;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.aggregation.Projection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import static dev.morphia.aggregation.Group.*;

@Service
public class MongoDemoServiceImpl extends BaseMongoServiceImpl<MongoDemo, IMongoDemoDao> implements IMongoDemoService {

    @Override
    public List<MongoDemo> getTestList() {
        Query<MongoDemo> query = this.createQuery();

        //参考:
        //https://blog.csdn.net/u013753516/article/details/44775571

        //TODO: 首先确定范围
        //TODO: 注意过滤条件与创建的索引
        //select * from book where num>1 and num<=100;
        //query.filter("num > ", 1).filter("num <=", 100);
        //或
        //select * from book where num>10 and num<90;
        query.field("num").greaterThan(10); // 大于
        //query.field("num").greaterThanOrEq(10); // 大于等于
        query.field("num").lessThan(90); // 小于
        //query.field("num").lessThanOrEq(90); // 小于等于

        //TODO: and 条件
        //select * from book where text like 'text%' and text like '%7';
        query.and(
                query.criteria("text").startsWith("text"), //字段内容以 text 开头的
                //query.criteria("text").equalIgnoreCase("TEXT82")//字段内容等于 text82
                //query.criteria("text").equal("text85"), //字段内容等于 text85
                //query.criteria("text").contains("6"), //字段内容存在 6
                query.criteria("text").endsWith("7") //字段内容以 7 结尾的
        );

        //TODO: or 条件
        //select * from book where text = 'text7' or text = 'text17' or content = 'content87';
        query.or(
                query.criteria("text").equal("text7"),
                query.criteria("text").equal("text17"),
                query.criteria("content").equal("content87")
        );

        //TODO: 排序
        query.order(Sort.descending("text"));
        //或
        //query.order("text");
        //或
        //query.order("text, -content");

        return query.find().toList();
    }

    @Override
    public List<TestObjSum> getTestGroupList() {

//        DateTime dt;
//
//        dt = new DateTime(new Date()).minusDays(1).toDateTime(); // -1天
//        System.out.println(dt.toString("yyyy/MM/dd HH:mm:ss EE"));
//        System.out.println(dt.toString("yyyy/MM/dd 00:00:00"));
//
//        dt = new DateTime(new Date()).plusDays(1).toDateTime(); // +1天
//        System.out.println(dt.toString("yyyy/MM/dd HH:mm:ss EE"));
//        System.out.println(dt.toString("yyyy/MM/dd 00:00:00"));
//
//        for (int i = 1; i <= 3; i++) {
//            TestObj obj = new TestObj();
//            obj.setNum(i);
//            obj.setInTime(new DateTime(new Date()).minusDays(1).toDate());
//            this.save(obj);
//        }
//
//        for (int i = 4; i <= 10; i++) {
//            TestObj obj = new TestObj();
//            obj.setNum(i);
//            obj.setInTime(new Date());
//            this.save(obj);
//        }
//
//        for (int i = 11; i <= 20; i++) {
//            TestObj obj = new TestObj();
//            obj.setNum(i);
//            obj.setInTime(new DateTime(new Date()).plusDays(1).toDate());
//            this.save(obj);
//        }

        // TODO: 以上代码， 在空库中增加三组数据

        //https://blog.csdn.net/my_way666/article/details/82019615
        //分组统计

        Query<MongoDemo> query = this.createQuery();
        AggregationPipeline pipeline = this.createAggregation();

//        Iterator<TestObj> itObj;
//        List<TestObj> listObj;

        Iterator<TestObjSum> itSum;
        List<TestObjSum> listSum;

        // TODO: 1, 数据投影
        pipeline.project(
                Projection.projection("num"),
                Projection.projection("date", // TestObj.date

                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d 00:00:00")
                                        .append("date", "$inTime") //TestObj.inTime
                        )
                )
        ).match( //条件
                query.filter("num >", 0) //TestObj.num
        );

//        itObj = pipeline.aggregate(TestObj.class);
//        listObj = Lists.newArrayList(itObj); // obj

        // TODO: 2, 数据分组
        pipeline.group(
                id(grouping("date")), //TestObj.date
                grouping("date", first("date")), //TestObjSum.date, TestObj.date
                //求和
                grouping("totalScore", sum("num")) //TestObjSum.totalScore, TestObj.num
        ).sort(
                //排序[ 升:Sort.ascending， 降: Sort.descending]
                Sort.ascending("date") //TestObjSum.date
        );

        itSum = pipeline.aggregate(TestObjSum.class);
//        listSum = Lists.newArrayList(itSum);

        return null;//listSum;
    }

    @Resource
    private MongoTemplate mongoTemplate;

    @Transactional
    @Override
    public long transactionalTest() {
        // 事务中不能创建集合, 集合必须存在

        // mongoTemplate.remove(MongoDemo.class);

        MongoDemo demo = MongoDemo.getInstance();
        demo.setNum(100).setName("name100").setStatus(100).setValue(BigDecimal.valueOf(100)).setInTime(new Date());

        mongoTemplate.save(demo);

        int i = 1 / 0;

        String collName = mongoTemplate.getCollectionName(MongoDemo.class);

        System.out.println("======== " + collName);

        org.springframework.data.mongodb.core.query.Query query = new org.springframework.data.mongodb.core.query.Query(
                Criteria.where("num").is(100)
        );
        UpdateResult result = mongoTemplate.updateFirst(query, Update.update("status", 99), collName);

        return result.getModifiedCount();
    }

    @Document("test_demo1")
    class MongoDemo1 {

        // @Id
        private int id;
        private String name;

        public int getId() {
            return id;
        }

        public MongoDemo1 setId(int id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public MongoDemo1 setName(String name) {
            this.name = name;
            return this;
        }
    }

    @Resource
    private MongoClient client;

    @Transactional
    @Override
    public long transactionalTest1() {
        // 事务中不能创建集合, 集合必须存在

        // mongoTemplate.remove(MongoDemo.class);

        //mongoTemplate 操作多个集合，事务不能回滚
        //需要使用 MongoCollection<org.bson.Document> 的
        // insertOne, insertMany, updateOne updateMany,
        // deleteOne, deleteMany 等方式处理需要回滚的事务。

        ClientSession session = client.startSession();
        try {
            session.startTransaction(); //开始事务

            MongoCollection<org.bson.Document> collection1 = mongoTemplate.getCollection("test_demo");
            MongoCollection<org.bson.Document> collection2 = mongoTemplate.getCollection("test_demo1");

            MongoDemo demo = MongoDemo.getInstance();
            demo.setNum(100).setName("name100").setStatus(100).setValue(BigDecimal.valueOf(100)).setInTime(new Date());
            //mongoTemplate.save(demo);
            collection1.insertOne(session, org.bson.Document.parse(JSONObject.toJSONString(demo)));

            int i = 1 / 0;

            MongoDemo1 demo1 = new MongoDemo1();
            demo1.setId(2);
            demo1.setName("demo1");
            //mongoTemplate.save(demo1);
            collection2.insertOne(session, org.bson.Document.parse(JSONObject.toJSONString(demo1)));

            session.commitTransaction(); //提交事务
        } catch (Exception e) {
            session.abortTransaction(); //回滚事务
        } finally {
            session.close();
        }

        return 1;
    }
}
