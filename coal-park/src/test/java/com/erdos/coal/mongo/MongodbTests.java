package com.erdos.coal.mongo;

import com.erdos.coal.Tester;
import com.erdos.coal.mongo.entity.MongoDemo;
import com.erdos.coal.mongo.service.IMongoDemoService;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class MongodbTests extends Tester {

    @Resource
    private IMongoDemoService mongoDemoService;

    //------------------------------------------------------------------------------------------------------------------

    @Test
    public void test() {

        mongoDemoService.delete(mongoDemoService.createQuery());

        logger.info("增加 --------------------------------------------------------------------------");

        // save(T obj);
        // save(List<T> obj);

        MongoDemo testObj;
        MongoDemo resultObj;

        testObj = MongoDemo.getInstance();
        testObj.setNum(0).setValue(BigDecimal.valueOf(1.0)).setName("name8");
        resultObj = mongoDemoService.save(testObj); //TODO: save

        Assert.notNull(resultObj, "返回空对象");

        List<MongoDemo> list = new ArrayList<>();
        for (int i = 1; i <= 9; i++) {
            list.add(MongoDemo.getInstance()
                    .setNum(i) // 1 -- 9
                    .setValue(new BigDecimal("10" + i)) // 101 -- 109
                    .setName("name" + i) // name1 -- name9
            );
        }
        mongoDemoService.save(list); //TODO: save list

        resultObj = mongoDemoService.get("name", "name6");

        Assert.notNull(resultObj, "返回空对象");

        logger.info("修改 --------------------------------------------------------------------------");

        // findAndModify(Query<T> query, UpdateOperations<T> updateOperations);
        // findAndModify(String oid, Map<String, Object> updateFieldMap)
        // findAndModify(Map<String, Object> conditionFieldMap, Map<String, Object> updateFieldMap);
        // findAndModify(ObjectId oid, Map<String, Object> updateFieldMap)

        Query<MongoDemo> query = mongoDemoService.createQuery().filter("name", "name1");
        UpdateOperations<MongoDemo> updateOperations = mongoDemoService.createUpdateOperations();
        updateOperations.set("value", new BigDecimal("101.1"));
        resultObj = mongoDemoService.findAndModify(query, updateOperations);

        Assert.notNull(resultObj, "返回空对象");

//        String oid;
//        Map<String, Object> condition = new HashMap<>();
//        Map<String, Object> updateField = new HashMap<>();

        // 废弃的
        //        testObj = testService.get("name", "name2");
        //        oid = testObj.getObjectId().toHexString();
        //        condition.put("value", new BigDecimal("102.22"));
        //        resultObj = testService.findAndModify(oid, condition);
        //
        //        Assert.notNull(resultObj);

        // 废弃的
        //        condition.clear();
        //        condition.put("num", 3);
        //        condition.put("name", "name3");
        //        updateField.put("value", new BigDecimal("103.333"));
        //        resultObj = testService.findAndModify(condition, updateField);
        //
        //        Assert.notNull(resultObj);

        // 废弃的
        //        testObj = testService.get("name", "name4");
        //        updateField.clear();
        //        updateField.put("value", new BigDecimal("104.4444"));
        //        resultObj = testService.findAndModify(testObj.getObjectId().toHexString(), updateField);
        //
        //        Assert.notNull(resultObj);

        // update(Query<T> query, UpdateOperations<T> updateOperations);
        // update(String oid, Map<String, Object> updateFieldMap)
        // update(Map<String, Object> conditionFieldMap, Map<String, Object> updateFieldMap);
        // update(Map<String, Object> conditionFieldMap, Map<String, Object> updateFieldMap, boolean upsert);
        // update(ObjectId oid, Map<String, Object> updateFieldMap)

        UpdateResults updateResults;

        query = mongoDemoService.createQuery().filter("name", "name5");
        updateOperations = mongoDemoService.createUpdateOperations();
        updateOperations.set("value", new BigDecimal("105.5"));
        updateResults = mongoDemoService.update(query, updateOperations);

        Assert.isTrue(updateResults.getUpdatedCount() == 1, "修上改条数不为1");

        // 废弃的
//        testObj = testService.get("name", "name6");
//        oid = testObj.getObjectId().toHexString();
//        updateField.clear();
//        updateField.put("value", new BigDecimal("106.66"));
//        updateResults = testService.update(oid, updateField);
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1);

        // 废弃的
//        condition.clear();
//        condition.put("num", 7);
//        condition.put("name", "name7");
//        updateField.clear();
//        updateField.put("value", new BigDecimal("107.777"));
//        updateResults = testService.update(condition, updateField);
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1);

        // 废弃的
//        condition.clear();
//        condition.put("num", 8);
//        condition.put("name", "name8");
//        updateField.clear();
//        updateField.put("value", new BigDecimal("108.8888"));
//        updateResults = testService.update(condition, updateField);
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1);

        // 废弃的
//        testObj = testService.get("name", "name9");
//        updateField.clear();
//        updateField.put("value", new BigDecimal("109.99999"));
//        updateResults = testService.update(testObj.getObjectId().toHexString(), updateField);
//
//        Assert.isTrue(updateResults.getUpdatedCount() == 1);

        logger.info("查询 ---------------------------------------------------------------------------");

        // get(Query<T> query);
        // get(Map<String, Object> conditionFieldMap);
        // get(ObjectId oid)
        // get(String key, Object value)

        query = mongoDemoService.createQuery().filter("num", 1);
        testObj = mongoDemoService.get(query);

        Assert.isTrue(testObj.getValue().compareTo(new BigDecimal("101.1")) == 0, "返回值不为101.1");

        // 废弃的
        //        condition.clear();
        //        condition.put("num", 2);
        //        testObj = testService.get(condition);
        //
        //        Assert.isTrue(testObj.getValue().compareTo(new BigDecimal("102.22")) == 0);
        //
        //        testObj = testService.get(testObj.getObjectId());
        //
        //        Assert.isTrue(testObj.getValue().compareTo(new BigDecimal("102.22")) == 0);
        //
        //        testObj = testService.get("num", 3);
        //
        //        Assert.isTrue(testObj.getValue().compareTo(new BigDecimal("103.333")) == 0);

        // getByPK(ObjectId oid)
        // getByPK(String oid)

        testObj = mongoDemoService.get("num", 5);
        testObj = mongoDemoService.getByPK(testObj.getObjectId());

        Assert.isTrue(testObj.getValue().compareTo(new BigDecimal("105.5")) == 0);

        testObj = mongoDemoService.get("num", 5);
        testObj = mongoDemoService.getByPK(testObj.getObjectId().toHexString());

        Assert.isTrue(testObj.getValue().compareTo(new BigDecimal("105.5")) == 0, "返回值不为105.5");

        // list(Query<T> query)
        // list()
        // list(Map<String, Object> conditionFieldMap)

        // list(Map<String, Object> conditionFieldMap, String sorts)
        // list(Map<String, Object> conditionFieldMap, Sort... sorts);

        list.clear();

        query = mongoDemoService.createQuery();
        query.or(
                query.criteria("num").equal(6),
                query.criteria("num").equal(7)
        );
        list = mongoDemoService.list(query);

        Assert.isTrue(list.size() == 2, "返回记录条数不为2");
        testObj = list.get(0);
        Assert.isTrue(testObj.getName().equals("name6") || testObj.getName().equals("name7"), "返回值不正确");

        list.clear();
        list = mongoDemoService.list();

        Assert.isTrue(list.size() == 10, "返回记录条数不为10");

        // 废弃的
        //        condition.clear();
        //        condition.put("num", 8);
        //        list = testService.list(condition);
        //
        //        Assert.isTrue(list.size() == 1 && list.get(0).getName().equals("name8"));

        // 废弃的
        //        condition.clear();
        //        condition.put("name", "name8");
        //        list = testService.list(condition, "num");

        //        Assert.isTrue(list.size() == 2);
        //        Assert.isTrue(list.get(0).getNum() == 0);

        // 废弃的
        //        condition.clear();
        //        condition.put("name", "name8");
        //        list = testService.list(condition, Sort.descending("num"));
        //
        //        Assert.isTrue(list.size() == 2);
        //        Assert.isTrue(list.get(0).getNum() == 8);

        logger.info("删除 ---------------------------------------------------------------------------");

        // findAndDelete(Query<T> query);

        //int delete(Query<T> query);
        // delete(String oid)
        // delete(ObjectId oid)
        // delete(String key, Object value)
        // delete(Map<String, Object> conditionFieldMap);
        // delete(String key, List<String> values)
        // delete(String key, String... values)

        query = mongoDemoService.createQuery();
        query.filter("num", 0);
        resultObj = mongoDemoService.findAndDelete(query);

        Assert.isTrue(resultObj.getNum() == 0, "返回值不正确");

        testObj = mongoDemoService.get("num", 1);
        resultObj = mongoDemoService.delete(testObj.getObjectId().toHexString());

        Assert.isTrue(resultObj.getNum() == 1, "返回值不正确");

        testObj = mongoDemoService.get("num", 2);
        resultObj = mongoDemoService.delete(testObj.getObjectId());

        Assert.isTrue(resultObj.getNum() == 2, "返回值不正确");

        resultObj = mongoDemoService.delete("num", 3);

        Assert.isTrue(resultObj.getNum() == 3, "返回值不正确");

        //        int count = 0;

        // 废弃的
        //        condition.clear();
        //        condition.put("num", 4);
        //        int count = testService.delete(condition);
        //
        //        Assert.isTrue(count == 1);

        // 废弃的
        //        List<String> names = new ArrayList<>();
        //        names.add("name5");
        //        names.add("name6");
        //        count = testService.delete("name", names);
        //
        //        Assert.isTrue(count == 2);

        // 废弃的
        //        count = testService.delete("name", "name7", "name8", "name9");
        //
        //        Assert.isTrue(count == 3);

        //T inc(String oid, String key, Integer value);

        /**
         * 分页 EGridResult
         */

        // findPage(Integer page, Integer rows, Map<String, Object> conditionMap, String sorts)
        // findPage(Integer page, Integer rows, Map<String, Object> conditionMap) {
        // findPage(Integer page, Integer rows, Map<String, Object> conditionMap, Sort... sorts);
        // findPage(Integer page, Integer rows, Query<T> query);


        //TODO: ...
    }
}
