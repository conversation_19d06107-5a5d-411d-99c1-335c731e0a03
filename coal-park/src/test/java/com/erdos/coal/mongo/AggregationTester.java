package com.erdos.coal.mongo;

import com.erdos.coal.Tester;
import com.erdos.coal.mongo.entity.AggregationGroup;
import com.erdos.coal.mongo.entity.AggregationMember;
import com.erdos.coal.mongo.service.IAggregationGroupService;
import com.erdos.coal.mongo.service.IAggregationMemberService;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.Query;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static dev.morphia.aggregation.Projection.projection;

public class AggregationTester extends Tester {

    @Resource
    private IAggregationMemberService aggregationMemberService;

    @Resource
    private IAggregationGroupService aggregationGroupService;

    @Test
    public void addTest() {
        Query<AggregationGroup> queryGroup = this.aggregationGroupService.createQuery();
        Query<AggregationMember> queryMember = this.aggregationMemberService.createQuery();

        aggregationGroupService.delete(queryGroup);
        aggregationMemberService.delete(queryMember);

        //group
        for (int i = 1; i <= 5; i++) {
            AggregationGroup group = new AggregationGroup();
            group.setId(i);
            group.setName("Group" + i);
            group.setStatus(i % 2);
            aggregationGroupService.save(group);

            //member
            for (int k = 1; k <= 10; k++) {
                AggregationMember member = new AggregationMember();
                member.setId(i * 1000 + k); // 1001 ~ 5050
                member.setAge(i * 10 + k); // 11 ~ 60
                member.setName("Member" + member.getId());
                member.setPid(i);
                member.setStatus(k % 3);
                aggregationMemberService.save(member);
            }
        }
    }

    @Test
    public void doTest() {
        //db.test_group.aggregate([
        //    {
        //        $lookup: {
        //            from: "test_member", //注意： 这里是  orders， 需要关联的表
        //            localField: "id", //product表需要关联的键（主表中的key）
        //            foreignField: "pid", //orders表中对应的键 (关联表中的key)
        //            as: "mm" //集合名，存放关联的结果数组
        //        }
        //    },
        //    {
        //        $match: {
        //					"mm.status": 1
        //				}
        //    },
        //    {
        //        $project: {
        //            "_id": 0,
        //						"mm._id": 0
        //        }
        //    },
        //    {
        //        $skip: 0
        //    },
        //    {
        //        $limit: 10
        //    }
        //])
        AggregationPipeline pipeline = aggregationGroupService.createAggregation();

        Query<AggregationGroup> queryGroup = aggregationGroupService.createQuery();

        queryGroup.filter("status", 1);

        pipeline.lookup("test_member", "id", "pid", "mm")
                .match(queryGroup)
                .project( // 需要的字段
                        projection("id"),
                        projection("name"),
                        projection("status"),
                        projection("mm") // 要和 AggregationGroup 中的名称一致
                )
                .skip(0)
                .limit(10);

        Iterator<AggregationGroup> iterator = pipeline.aggregate(AggregationGroup.class);
//        List<AggregationGroup> list = Lists.newArrayList(iterator);
//        System.out.println(list.toString());
    }
}
