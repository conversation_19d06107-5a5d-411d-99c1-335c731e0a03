package com.erdos.coal.mongo.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Date;

@Document("test_demo")
@Entity(value = "test_demo", noClassnameStored = true)
public class MongoDemo extends BaseMongoInfo {

    //    @Indexed(options = @IndexOptions(name = "_idx_test_num", unique = true, background = true))
    private Integer num;

    private String name;

    private Integer status;

    private BigDecimal value;

    private Date inTime;

    private String date;

    public static MongoDemo getInstance() {
        return new MongoDemo();
    }

    public Integer getNum() {
        return num;
    }

    public MongoDemo setNum(Integer num) {
        this.num = num;
        return this;
    }

    public String getName() {
        return name;
    }

    public MongoDemo setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public MongoDemo setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public BigDecimal getValue() {
        return value;
    }

    public MongoDemo setValue(BigDecimal value) {
        this.value = value;
        return this;
    }

    public Date getInTime() {
        return inTime;
    }

    public MongoDemo setInTime(Date inTime) {
        this.inTime = inTime;
        return this;
    }

    public String getDate() {
        return date;
    }

    public MongoDemo setDate(String date) {
        this.date = date;
        return this;
    }
}
