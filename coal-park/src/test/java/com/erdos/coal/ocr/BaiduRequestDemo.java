package com.erdos.coal.ocr;

import com.baidubce.http.ApiExplorerClient;
import com.baidubce.http.HttpMethodName;
import com.baidubce.model.ApiExplorerRequest;
import com.baidubce.model.ApiExplorerResponse;
import com.erdos.coal.Tester;
import org.junit.Test;

// 行驶证识别 示例代码
// 参考: https://cloud.baidu.com/apiexplorer/index.html?Product=GWSE-DJAQ8YwekkQ&Api=GWAI-Q25jxNhtmTG
public class BaiduRequestDemo extends Tester {

    @Test
    public void test() {
        String path = "https://aip.baidubce.com/rest/2.0/ocr/v1/vehicle_license";
        ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);

        // 1, 设置header参数
        request.addHeaderParameter("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");

        // 2, 设置jsonBody参数
        String jsonBody = "url=";
        jsonBody += "https://baidu-ai.bj.bcebos.com/ocr/vehicle_license.jpeg"; // TODO: 行驶证图片URL, 换成 OSS 地址
        jsonBody += "&detect_direction=false";

        // 3, 增加 access_token 参数
        //获取 access_token
        // client_id, client_secret 分别对应 AipOcrService.API_KEY, AipOcrService.SECRET_KEY
        //URL = 'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=【官网获取的AK】&client_secret=【官网获取的SK】'
        //URL = 'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=ed29cKaTtyIj92DfWphN4VDE&client_secret=GwT9u0G7gQG4PNWqtRxFxdSVFAgWHvhU'
        // access_token=24.5c91bb8b59518c929a89ca7a5a2f5c6b.2592000.**********.282335-25014905
        jsonBody += "&access_token=24.5c91bb8b59518c929a89ca7a5a2f5c6b.2592000.**********.282335-25014905";

        request.setJsonBody(jsonBody);

        ApiExplorerClient client = new ApiExplorerClient();

        // 4, 得到结果
        try {
            ApiExplorerResponse response = client.sendRequest(request);
            // 返回结果格式为Json字符串
            System.out.println(response.getResult());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
