package com.erdos.coal.ocr;

import com.erdos.coal.Tester;
import com.erdos.coal.config.OcrConfig;
import com.erdos.coal.ocr.entity.HealthCode;
import com.erdos.coal.ocr.entity.IDCard;
import com.erdos.coal.ocr.entity.TravelCard;
import com.erdos.coal.park.web.TestService;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class OcrTest extends Tester {

    @Resource
    private OcrConfig ocrConfig;

    @Resource
    private TestService testService;

    // TODO: 身份证
    @Test
    public void idCardTest() {

        // 需要先加载 opencv 库
        System.load(ocrConfig.opencvLib);
        // logger.info("opencv lib version: {} ", Core.VERSION);

        try {
            InputStream is = new FileInputStream(new File(ocrConfig.testFilePath + "idcard.jpg"));
            IDCard idCard = testService.ocrIDCardTest(is);
            logger.info(idCard.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // TODO: 健康码
    @Test
    public void healthCodeTest() {
        // 需要先加载 opencv 库
        System.load(ocrConfig.opencvLib);
        // logger.info("opencv lib version: {} ", Core.VERSION);

        try {
            InputStream is = new FileInputStream(new File(ocrConfig.testFilePath + "healthcode.jpg"));
            HealthCode healthCode = testService.ocrHealthCodeTest(is);
            logger.info(healthCode.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // TODO: 行程卡
    @Test
    public void travelCardTest() {
        // 需要先加载 opencv 库
        System.load(ocrConfig.opencvLib);
        // logger.info("opencv lib version: {} ", Core.VERSION);

        try {
            InputStream is = new FileInputStream(new File(ocrConfig.testFilePath + "travelcard.jpg"));
            TravelCard travelCard = testService.ocrTravelCardTest(is);
            logger.info(travelCard.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
