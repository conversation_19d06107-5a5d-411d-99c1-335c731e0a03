package com.hdd.http;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.hdd.auth.Credentials;
import com.hdd.auth.HddSigner;
import com.hdd.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpMethod;
import org.springframework.util.DigestUtils;

import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HddEncryptRequest {
    public static String doRequest(String url, String appKey, String appSecret, Object body) throws Exception {
        URL urlObj = new URL(url);
        String bodyStr = JSON.toJSONString(body);
        String bodyMd5 = DigestUtils.md5DigestAsHex(bodyStr.getBytes()).toLowerCase();

        HttpRequest httpRequest = HttpUtil.createPost(url).body(bodyStr);

        httpRequest.header("Content-MD5", bodyMd5);
        httpRequest.header(Headers.HOST, urlObj.getAuthority());
        httpRequest.header(Headers.DATE, DateUtils.formatAlternateIso8601Date(new Date()));

        Map<String, List<String>> headers = httpRequest.headers();
        Map<String, String> headMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            headMap.put(entry.getKey(), CollectionUtils.isEmpty(entry.getValue()) ? "" : entry.getValue().get(0));
        }

//        String sign = new HddSigner().sign(HttpMethod.resolve("POST"), urlObj.getPath(), headMap, null, Credentials.builder().appKey(appKey).appSecret(appSecret).build());
        String sign = new HddSigner().sign(HttpMethod.resolve("POST"), urlObj.getPath(), headMap, null, new Credentials(appKey, appSecret));
        return httpRequest.header(Headers.AUTHORIZATION, sign).execute().body();
    }

    public static String doRequest(String url, String appKey, String appSecret, Object body, int timeout) throws Exception {
        URL urlObj = new URL(url);
        String bodyStr = JSON.toJSONString(body);
        String bodyMd5 = DigestUtils.md5DigestAsHex(bodyStr.getBytes()).toLowerCase();

        HttpRequest httpRequest = HttpUtil.createPost(url).body(bodyStr).setConnectionTimeout(timeout).setReadTimeout(timeout);

        httpRequest.header("Content-MD5", bodyMd5);
        httpRequest.header(Headers.HOST, urlObj.getAuthority());
        httpRequest.header(Headers.DATE, DateUtils.formatAlternateIso8601Date(new Date()));
        Map<String, List<String>> headers = httpRequest.headers();
        Map<String, String> headMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            headMap.put(entry.getKey(), CollectionUtils.isEmpty(entry.getValue()) ? "" : entry.getValue().get(0));
        }

//        String sign = new HddSigner().sign(HttpMethod.resolve("POST"), urlObj.getPath(), headMap, null, Credentials.builder().appKey(appKey).appSecret(appSecret).build());
        String sign = new HddSigner().sign(HttpMethod.resolve("POST"), urlObj.getPath(), headMap, null, new Credentials(appKey, appSecret));
        return httpRequest.header(Headers.AUTHORIZATION, sign).execute().body();
    }


    public static String doRequest(String url, String authorization) throws Exception {
        URL urlObj = new URL(url);

        HttpRequest httpRequest = HttpUtil.createPost(url);

        httpRequest.header(Headers.HOST, urlObj.getAuthority());
        httpRequest.header(Headers.DATE, DateUtils.formatAlternateIso8601Date(new Date()));

        Map<String, List<String>> headers = httpRequest.headers();
        Map<String, String> headMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            headMap.put(entry.getKey(), CollectionUtils.isEmpty(entry.getValue()) ? "" : entry.getValue().get(0));
        }

        return httpRequest.header(Headers.AUTHORIZATION, authorization).execute().body();
    }
}