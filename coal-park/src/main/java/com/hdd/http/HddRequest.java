package com.hdd.http;

import com.hdd.auth.SignOptions;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

public class HddRequest {

    private Map<String, String> parameters = new HashMap<>();

    private Map<String, String> headers = new HashMap<>();

    private String path;

    private HttpMethod httpMethod;

    private SignOptions signOptions;

    private boolean expectContinueEnabled;

    public void addHeader(String name, String value) {
        this.headers.put(name, value);
    }

    public void addParameter(String name, String value) {
        this.parameters.put(name, value);
    }

    public HddRequest() {
    }

    public HddRequest(Map<String, String> parameters, Map<String, String> headers, String path, HttpMethod httpMethod, SignOptions signOptions) {
        this.parameters = parameters;
        this.headers = headers;
        this.path = path;
        this.httpMethod = httpMethod;
        this.signOptions = signOptions;
    }

    public Map<String, String> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, String> parameters) {
        this.parameters = parameters;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public HttpMethod getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
    }

    public SignOptions getSignOptions() {
        return signOptions;
    }

    public void setSignOptions(SignOptions signOptions) {
        this.signOptions = signOptions;
    }

    public boolean isExpectContinueEnabled() {
        return expectContinueEnabled;
    }

    public void setExpectContinueEnabled(boolean expectContinueEnabled) {
        this.expectContinueEnabled = expectContinueEnabled;
    }
}
