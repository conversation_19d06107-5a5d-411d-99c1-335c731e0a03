package com.hdd.auth;

import java.util.Date;
import java.util.Set;

public class SignOptions {
    public static final SignOptions DEFAULT = new SignOptions();

    public static final int DEFAULT_EXPIRATION_IN_SECONDS = 1800;

    private Set<String> headersToSign = null;

    private Date timestamp = null;

    private int expirationInSeconds = DEFAULT_EXPIRATION_IN_SECONDS;

    public static SignOptions getDEFAULT() {
        return DEFAULT;
    }

    public static int getDefaultExpirationInSeconds() {
        return DEFAULT_EXPIRATION_IN_SECONDS;
    }

    public Set<String> getHeadersToSign() {
        return headersToSign;
    }

    public void setHeadersToSign(Set<String> headersToSign) {
        this.headersToSign = headersToSign;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public int getExpirationInSeconds() {
        return expirationInSeconds;
    }

    public void setExpirationInSeconds(int expirationInSeconds) {
        this.expirationInSeconds = expirationInSeconds;
    }
}
