package com.hdd.auth;

import cn.hutool.core.lang.Assert;
import com.hdd.constants.SignConstants;
import com.hdd.http.HddRequest;
import com.hdd.http.Headers;
import com.hdd.http.HttpUtils;
import org.apache.commons.codec.binary.Hex;
import org.springframework.http.HttpMethod;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.*;

public class HddSigner {

    private static final Charset UTF8 = Charset.forName(SignConstants.DEFAULT_ENCODING);

    private static final Set<String> defaultHeadersToSign = new HashSet<String>();

    static {
        HddSigner.defaultHeadersToSign.add(Headers.HOST.toLowerCase());
        HddSigner.defaultHeadersToSign.add(Headers.CONTENT_LENGTH.toLowerCase());
        HddSigner.defaultHeadersToSign.add(Headers.CONTENT_TYPE.toLowerCase());
        HddSigner.defaultHeadersToSign.add(Headers.CONTENT_MD5.toLowerCase());
    }

    public String sign(HttpMethod httpMethod, String path, Map<String, String> headers, Map<String, String> queryParams, Credentials credentials) throws Exception {
        return sign(httpMethod, path, headers, queryParams, credentials, null);
    }

    public String sign(HttpMethod httpMethod, String path, Map<String, String> headers, Map<String, String> queryParams, Credentials credentials, SignOptions options) throws Exception {
//        HddRequest hddRequest = HddRequest.builder().httpMethod(httpMethod).path(path).headers(headers).parameters(queryParams).signOptions(options).build();
        HddRequest hddRequest = new HddRequest(queryParams, headers, path, httpMethod, options);
        sign(hddRequest, credentials, options);
        return hddRequest.getHeaders().get(Headers.AUTHORIZATION);
    }

    public void sign(HddRequest request, Credentials credentials, SignOptions options) throws Exception {
        Assert.notNull(request, "request should not be null.");

        if (credentials == null) {
            return;
        }

        if (options == null) {
            if (request.getSignOptions() != null) {
                options = request.getSignOptions();
            } else {
                options = SignOptions.DEFAULT;
            }
        }

        String accessKeyId = credentials.getAppKey();
        String secretAccessKey = credentials.getAppSecret();

        String timestamp = request.getHeaders().get(Headers.DATE);

        String authString = String.join("/", SignConstants.AUTH_PREFIX, accessKeyId, timestamp, String.valueOf(options.getExpirationInSeconds()));

        String signingKey = this.sha256Hex(secretAccessKey, authString);
        // Formatting the URL with signing protocol.
        String canonicalURI = this.getCanonicalURIPath(request.getPath());
        // Formatting the query string with signing protocol.
        String canonicalQueryString = "";
        if (!request.getHttpMethod().equals(HttpMethod.POST)) {
            canonicalQueryString = HttpUtils.getCanonicalQueryString(request.getParameters(), true);
        }

        // Sorted the headers should be signed from the request.
        SortedMap<String, String> headersToSign =
                this.getHeadersToSign(request.getHeaders(), options.getHeadersToSign());
        // Formatting the headers from the request based on signing protocol.
        String canonicalHeader = this.getCanonicalHeaders(headersToSign);
        String signedHeaders = "";
        signedHeaders = String.join(";", headersToSign.keySet());
        signedHeaders = signedHeaders.trim().toLowerCase();

        String canonicalRequest =
                request.getHttpMethod() + "\n" + canonicalURI + "\n" + canonicalQueryString + "\n" + canonicalHeader;

        // Signing the canonical request using key with sha-256 algorithm.
        String signature = this.sha256Hex(signingKey, canonicalRequest);

        String authorizationHeader = String.join("/", authString, signedHeaders, signature);

        request.addHeader(Headers.AUTHORIZATION, authorizationHeader);
    }

    private String getCanonicalURIPath(String path) {
        if (path == null) {
            return "/";
        } else if (path.startsWith("/")) {
            return HttpUtils.normalizePath(path);
        } else {
            return "/" + HttpUtils.normalizePath(path);
        }
    }

    private String getCanonicalHeaders(SortedMap<String, String> headers) {
        if (headers.isEmpty()) {
            return "";
        }

        List<String> headerStrings = new ArrayList<>();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey();
            if (key == null) {
                continue;
            }
            String value = entry.getValue();
            if (value == null) {
                value = "";
            }
            headerStrings.add(HttpUtils.normalize(key.trim().toLowerCase()) + ':' + HttpUtils.normalize(value.trim()));
        }
        Collections.sort(headerStrings);

        return String.join("\n", headerStrings);
    }

    private SortedMap<String, String> getHeadersToSign(Map<String, String> headers, Set<String> headersToSign) {
        SortedMap<String, String> ret = new TreeMap<>();
        if (headersToSign != null) {
            Set<String> tempSet = new HashSet();
            for (String header : headersToSign) {
                tempSet.add(header.trim().toLowerCase());
            }
            headersToSign = tempSet;
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey();
            if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                if ((headersToSign == null && this.isDefaultHeaderToSign(key))
                        || (headersToSign != null && headersToSign.contains(key.toLowerCase())
                        && !Headers.AUTHORIZATION.equalsIgnoreCase(key))) {
                    ret.put(key, entry.getValue());
                }
            }
        }
        return ret;
    }

    private boolean isDefaultHeaderToSign(String header) {
        header = header.trim().toLowerCase();
        return header.startsWith(Headers.PREFIX) || defaultHeadersToSign.contains(header);
    }

    private String sha256Hex(String signingKey, String stringToSign) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(signingKey.getBytes(UTF8), "HmacSHA256"));
            return new String(Hex.encodeHex(mac.doFinal(stringToSign.getBytes(UTF8))));
        } catch (Exception e) {
            throw new Exception("Fail to generate the signature");
        }
    }

}
