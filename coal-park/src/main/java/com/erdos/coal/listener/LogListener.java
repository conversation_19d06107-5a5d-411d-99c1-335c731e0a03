package com.erdos.coal.listener;

import com.erdos.coal.core.event.LogDTO;
import com.erdos.coal.core.event.LogEvent;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.api.manage.service.IAppLogService;
import com.erdos.coal.utils.StrUtil;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class LogListener {

    private Logger logger = LoggerFactory.getLogger(LogListener.class);

    @Resource
    private IAppLogService appLogService;

    //TODO: 异步处理日志
    //@Order
    @Async("logTaskExecutor")
    @EventListener(LogEvent.class)
    public void saveLog(LogEvent logEvent) {

        try {
            LogDTO dto = (LogDTO) logEvent.getSource();

            AppLog appLog = dto2log(dto);

            appLogService.writeLog(appLog);
        } catch (Exception e) {
            logger.error("日志记录异常:{}", e.getMessage());
        }
    }

    private AppLog dto2log(LogDTO dto) {
        // 日志信息
        AppLog appLog = new AppLog();

        String agent = dto.getAgent();
        // 解析agent字符串
        UserAgent userAgent = UserAgent.parseUserAgentString(agent);
        // 获取浏览器对象
        Browser browser = userAgent.getBrowser();
        // 获取操作系统对象
        OperatingSystem operatingSystem = userAgent.getOperatingSystem();
        AppLog.Client client = new AppLog.Client();
        if (StrUtil.isNotEmpty(browser.getName()) && !browser.getName().toUpperCase().equals("UNKNOWN"))
            client.setBrowserName(browser.getName());
        if (StrUtil.isNotEmpty(userAgent.getBrowserVersion()) && !userAgent.getBrowserVersion().equals("UNKNOWN"))
            client.setBrowserVersion(userAgent.getBrowserVersion().toString());
        if (StrUtil.isNotEmpty(operatingSystem.getName()) && !operatingSystem.getName().toUpperCase().equals("UNKNOWN"))
            client.setSystemName(operatingSystem.getName());
        client.setIp(dto.getIp()); // 客户端 IP
        appLog.setClient(client);

        // 设置操作用户信息
        appLog.setUser(dto.getUid());//操作用户ID
        appLog.setUserType(dto.getType());//操作用户类型
        appLog.setMethod(dto.getName());//方法
        appLog.setDescription(dto.getDesc());//描述 全部记录，不截取
        appLog.setParameter(Arrays.deepToString(dto.getParameter()));//参数 全部记录，不截取
        appLog.setResult(dto.getReturnObject().toString());//返回 全部记录，不截取

        appLog.setConsumes(dto.getTotalTime());//耗时

        AppLog.Server server = new AppLog.Server();
        server.setHost(dto.getHost()); // 主机
        server.setThread(dto.getThreadName()); // 线程
        server.setPosition(dto.getPosition()); //位置
        appLog.setServer(server);

        return appLog;
    }

    private String asStringTruncatedTo254(Object o) {
        String s = null;
        if (o != null) {
            s = o.toString();
        }

        if (s == null) {
            return null;
        }
        if (s.length() <= 254) {
            return s;
        } else {
            return s.substring(0, 254);
        }
    }
}
