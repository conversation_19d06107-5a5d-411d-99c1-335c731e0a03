package com.erdos.coal.security.impl;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.web.sys.service.ISysSwitchService;
import com.erdos.coal.security.IUserLogin;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

//微信小程序用户
@Service("wechatUserLoginImpl")
public class WechatUserLoginImpl implements IUserLogin {

    @Resource
    private IHttpAPIService httpAPIService;

    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;
    @Resource
    private ISysSwitchService sysSwitchService;

    @Override
    public ShiroUser getShiroUser(String openid) {

        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("openid", openid);
        if (wechatDriverInfo == null) throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);

        //TODO: 组装返回对象----业务系统登录仅仅是做个token的安全验证，不参与用户详细信息和业务逻辑内容判断
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserId(wechatDriverInfo.getObjectId().toHexString());
        shiroUser.setUsername(wechatDriverInfo.getOpenid());
        shiroUser.setPassword(wechatDriverInfo.getOpenid().substring(7));
        shiroUser.setUserType(SysConstants.UserType.UT_WECHAT.toString());

        return shiroUser;


        /*String code = (String) jwtToken.getPrincipal();

        String requestUrl = WXPayConstants.sessionHost;
        requestUrl += "?appid=" + WXPayConstants.wechat_appid + "&secret=" + WXPayConstants.wechat_secret + "&js_code=" + code + "&grant_type=" + WXPayConstants.wechat_grant_type;
        JSONObject sessionKeyOpenid = null;
        try {
            sessionKeyOpenid = JSON.parseObject(httpAPIService.doGet(requestUrl));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String openid = sessionKeyOpenid.getString("openid");
        String sessionKey = sessionKeyOpenid.getString("session_key");
//        String errorCode = sessionKeyOpenid.getString("errcode");

        if (openid == null || sessionKey == null)
            throw new GlobalException(ServerResponse.createError(sessionKeyOpenid.getString("errmsg")));


        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("openid", openid);
        if (wechatDriverInfo == null) {
            wechatDriverInfo = new WechatDriverInfo();
            wechatDriverInfo.setOpenid(openid);
            wechatDriverInfo.setSessionKey(sessionKey);
            Map<String, String> photoMap = sysSwitchService.getPhoto(SysConstants.UserType.UT_WECHAT.toString()); // 查询设置账户是否可用
            if (photoMap.get("check").equals("0")) {
                wechatDriverInfo.setState(0);
            } else {
                wechatDriverInfo.setState(4);
            }
            wechatDriverInfoService.save(wechatDriverInfo);
        }

        //TODO: 2, 用户权限列表
        Set<String> permissions = wechatDriverInfoService.findPermissions(openid);
        List<GrantedAuthority> grantedAuthorities = permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());

        //TODO: 组装返回对象
        SelfUserEntity selfUserEntity = new SelfUserEntity();
        selfUserEntity.setUserType(SysConstants.UserType.UT_WECHAT.toString());
        selfUserEntity.setExp(JWTConfig.expWechat);
        selfUserEntity.setAccountNonExpired(true).setAccountNonLocked(true).setEnabled(true).setCredentialsNonExpired(true);

        //BeanUtils.copyProperties(cUser, selfUserEntity); // 不能完全处理, 字段名不是全部相同

        selfUserEntity.setUserId(wechatDriverInfo.getObjectId().toString());
        selfUserEntity.setUsername(openid); //TODO: 登录名, 各用户可能不同
        selfUserEntity.setAuthorities(grantedAuthorities);

        return selfUserEntity;*/

//        JWTUtil jwtUtil = new JWTUtil();
//        String token = jwtUtil.createTokenWithClaim(openid, SysConstants.UserType.UT_WECHAT.toString(), openid);
//        return ServerResponse.createSuccess("登录成功", new AccessToken(token));

    }

    @Override
    public List<String> findPermissionByUser(String loginName) {
        //TODO: 2, 用户权限列表
        List<String> permissions = wechatDriverInfoService.findPermissions(loginName);
        return permissions;
    }
}
