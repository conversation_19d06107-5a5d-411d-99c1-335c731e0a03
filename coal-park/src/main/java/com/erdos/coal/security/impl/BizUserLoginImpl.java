package com.erdos.coal.security.impl;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.security.IUserLogin;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("bizUserLoginImpl")
public class BizUserLoginImpl implements IUserLogin {
    @Resource
    private ISysUnitService sysUnitService;

    @Override
    public ShiroUser getShiroUser(String username) {

        /*
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserId(sysUser.getUserId());
        shiroUser.setUsername(sysUser.getUsername());
        shiroUser.setPassword(sysUser.getPassword());*/

        //TODO：1, 业务系统用户账号验证
        SysUnit sysUnit = sysUnitService.get("loginName", username);
        if (sysUnit == null)
            throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);   // BadCredentialsException

        //TODO: 组装返回对象----业务系统登录仅仅是做个token的安全验证，不参与用户详细信息和业务逻辑内容判断
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserType(SysConstants.UserType.UT_BIZ.toString());
        shiroUser.setUsername(sysUnit.getLoginName());
        shiroUser.setPassword(sysUnit.getPassword());
        shiroUser.setUserId(sysUnit.getId());

        return shiroUser;
    }

    @Override
    public List<String> findPermissionByUser(String loginName) {
        //TODO: 2, 用户权限列表
        List<String> permissions = sysUnitService.findPermissions(loginName);
        return permissions;
    }
}
