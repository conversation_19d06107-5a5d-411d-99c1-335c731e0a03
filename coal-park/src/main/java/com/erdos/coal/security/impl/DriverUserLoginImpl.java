package com.erdos.coal.security.impl;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.security.IUserLogin;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

//APP用户
@Service("driverUserLoginImpl")
public class DriverUserLoginImpl implements IUserLogin {
    @Resource
    private IDriverInfoService driverInfoService;

    @Override
    public ShiroUser getShiroUser(String mobile) {

        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
        if (driverInfo == null) throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);//用不名不存在

        //TODO: 组装返回对象----业务系统登录仅仅是做个token的安全验证，不参与用户详细信息和业务逻辑内容判断
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserType(SysConstants.UserType.UT_DRIVER.toString());
        shiroUser.setUsername(driverInfo.getMobile());
        shiroUser.setPassword(driverInfo.getMobile().substring(7));
        shiroUser.setUserId(driverInfo.getObjectId().toHexString());

        return shiroUser;

        /*//TODO: 1, 验证码校验
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null || !sms.getCode().equals(code))
            throw new GlobalException(1, "验证码错误");

        //TODO: 2, 按手机号查询
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);

        //车牌号为空，登录，否则注册
        if (carNum == null) {

            //用户是否存在
            if (driverInfo == null)
                throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);

            Query<DriverInfo> query = driverInfoService.createQuery().filter("mobile", mobile);
            UpdateOperations<DriverInfo> updateOperations = driverInfoService.createUpdateOperations();
            if (StrUtil.isEmpty(phoneId)) {
                throw new GlobalException(ServerResponse.createError("手机序列号不能为空"));
            } else if (driverInfo.getPhoneId() == null || !driverInfo.getPhoneId().equals(phoneId)) {
                updateOperations.set("phoneId", phoneId);
            }
            if (StrUtil.isEmpty(deviceId)) {
                throw new GlobalException(ServerResponse.createError("设备Id不能为空"));
            } else if (driverInfo.getDeviceId() == null || !driverInfo.getDeviceId().equals(deviceId)) {
                //修改用户 设备id
                updateOperations.set("deviceId", deviceId);
            }
            driverInfoService.update(query, updateOperations);

        } else {
            Query<DriverInfo> query = driverInfoService.createQuery();
            query.filter("carNum", carNum);
            query.filter("state nin", 2);
            DriverInfo driverInfo1 = driverInfoService.get(query);
            if (driverInfo == null && driverInfo1 == null) {   //未注册
                driverInfo = new DriverInfo();

                if (StrUtil.isEmpty(carNum)) {
                    throw new GlobalException(ServerResponse.createError("车牌号不能为空"));
                } else {
                    driverInfo.setCarNum(carNum);
                }

                driverInfo.setMobile(mobile);

                if (StrUtil.isEmpty(phoneId)) {
                    throw new GlobalException(ServerResponse.createError("手机序列号不能为空"));
                } else {
                    driverInfo.setPhoneId(phoneId);
                }

                if (StrUtil.isEmpty(deviceId)) {
                    throw new GlobalException(ServerResponse.createError("设备Id不能为空"));
                } else {
                    driverInfo.setDeviceId(deviceId);
                }

                if (StrUtil.isEmpty(carInfoId)) {
                    throw new GlobalException(ServerResponse.createError("车型不能为空"));
                } else {
                    driverInfo.setCarInfoId(carInfoId);
                    CarInfo carInfo = carInfoService.get("id", carInfoId);
                    if (ObjectUtil.isNotNull(carInfo)) {
                        driverInfo.setCarType(carInfo.getCarType());
                        driverInfo.setAxlesNumber(carInfo.getAxlesNumber());
                        driverInfo.setCapacity(carInfo.getCapacity());
                    }
                }

                Map<String, String> photoMap = sysSwitchService.getPhoto(SysConstants.UserType.UT_DRIVER.toString()); // 查询设置账户是否可用
                if (photoMap.get("check").equals("0")) {
                    driverInfo.setState(0);
                } else {
                    driverInfo.setState(4);
                }

                driverInfoService.save(driverInfo);
            } else {       //已注册
                throw new GlobalException(1, "该手机号或车牌号已注册");
            }
        }
        //TODO: 2, 用户权限列表
        Set<String> permissions = driverInfoService.findPermissions(mobile);
        List<GrantedAuthority> grantedAuthorities = permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());


        //TODO: 组装返回对象
        SelfUserEntity selfUserEntity = new SelfUserEntity();
        selfUserEntity.setUserType(SysConstants.UserType.UT_DRIVER.toString());
        selfUserEntity.setExp(JWTConfig.expDU);
        selfUserEntity.setAccountNonExpired(true).setAccountNonLocked(true).setEnabled(true).setCredentialsNonExpired(true);

        BeanUtils.copyProperties(driverInfo, selfUserEntity); // 不能完全处理, 字段名不是全部相同

        selfUserEntity.setUserId(driverInfo.getObjectId().toString());
        selfUserEntity.setUsername(driverInfo.getMobile()); //TODO: 登录名, 各用户可能不同
        selfUserEntity.setAuthorities(grantedAuthorities);

        return selfUserEntity;*/

//        JWTUtil jwtUtil = new JWTUtil();
//        String token = jwtUtil.createTokenWithClaim(mobile, SysConstants.UserType.UT_DRIVER.toString(), phoneId);
//
//        logger.info(token);
//
//        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }

    @Override
    public List<String> findPermissionByUser(String loginName) {
        //TODO: 2, 用户权限列表
        List<String> permissions = driverInfoService.findPermissions(loginName);
        return permissions;
    }
}
