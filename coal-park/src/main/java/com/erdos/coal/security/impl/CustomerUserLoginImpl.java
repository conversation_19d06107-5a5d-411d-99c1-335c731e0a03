package com.erdos.coal.security.impl;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.security.IUserLogin;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

//APP用户
@Service("customerUserLoginImpl")
public class CustomerUserLoginImpl implements IUserLogin {

    @Resource
    private ISMSService smsService;

    @Resource
    private ICustomerUserService customerUserService;

    @Override
    public ShiroUser getShiroUser(String mobile) {
        //TODO:1.验证用户登录信息是否正确
        CustomerUser cUser = customerUserService.get("mobile", mobile);
        if (cUser == null) throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS); //用户名不存在

        //TODO: 组装返回对象----业务系统登录仅仅是做个token的安全验证，不参与用户详细信息和业务逻辑内容判断
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserId(cUser.getObjectId().toHexString());
        shiroUser.setUserType(SysConstants.UserType.UT_CUSTOMER.toString());
        shiroUser.setUsername(cUser.getMobile());
        shiroUser.setPassword(cUser.getPassword());

        return shiroUser;

        /*//TODO:3.没有短信验证码，且登录的手机序列号 和 数据库存储的不一致 则需要发送短信验证码，并返回登录失败
        if (StrUtil.isEmpty(jwtToken.getCode()) && !cUser.getPhoneId().equals(jwtToken.getPhoneId())) {
            ServerResponse<String> sr = new ServerResponse<>();
            sr.setStatus(2);
            sr.setMsg("检测到新设备登录，需要短信验证");
            sr.setData(cUser.getMobile());
            throw new GlobalException(sr);
        }

        //TODO:4.没有设备id，返回登录失败，有设备Id和数据库存储不一致则更新数据库（阿里云推送）
//        if (StrUtil.isEmpty(deviceId)) {
//            return ServerResponse.createError("设备Id不能为空");
//        } else if (cUser.getDeviceId() == null || !cUser.getDeviceId().equals(deviceId)) {
//            //修改用户 设备id
//            Map<String, Object> mapParam = new HashMap<>();
//            mapParam.put("deviceId", deviceId);
//            this.update(cUser.getId(), mapParam);
//        }

        //TODO:5.有短信验证码，且登录的手机序列号 和 数据库存储的不一致 则修改数据存储的手机序列号
        if (!StrUtil.isEmpty(jwtToken.getCode()) && !cUser.getPhoneId().equals(jwtToken.getPhoneId())) {
            //验证码校验
            SMS sms = smsService.get("mobile", cUser.getMobile());
            if (sms == null || !sms.getCode().equals(jwtToken.getCode()))
                throw new GlobalException(ServerResponse.createError("验证码错误"));

            //修改用户 手机序列号
            UpdateOperations<CustomerUser> updateOperations = customerUserService.createUpdateOperations();
            updateOperations.set("phoneId", jwtToken.getPhoneId());
            customerUserService.update(customerUserService.createQuery().filter("_id", cUser.getObjectId()), updateOperations);
        }

        //TODO: 2, 用户权限列表
        Set<String> permissions = customerUserService.findPermissions(mobile);
        List<GrantedAuthority> grantedAuthorities = permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());

        //TODO: 组装返回对象
        SelfUserEntity selfUserEntity = new SelfUserEntity();
        selfUserEntity.setUserType(SysConstants.UserType.UT_CUSTOMER.toString());
        selfUserEntity.setExp(JWTConfig.expCU);
        selfUserEntity.setAccountNonExpired(true).setAccountNonLocked(true).setEnabled(true).setCredentialsNonExpired(true);

        BeanUtils.copyProperties(cUser, selfUserEntity); // 不能完全处理, 字段名不是全部相同

        selfUserEntity.setUserId(cUser.getObjectId().toString());
        selfUserEntity.setUsername(cUser.getMobile()); //TODO: 登录名, 各用户可能不同
        selfUserEntity.setAuthorities(grantedAuthorities);

        return selfUserEntity;*/
    }

    @Override
    public List<String> findPermissionByUser(String loginName) {
        //TODO: 2, 用户权限列表
        List<String> permissions = customerUserService.findPermissions(loginName);
        return permissions;
    }
}
