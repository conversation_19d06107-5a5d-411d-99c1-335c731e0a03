package com.erdos.coal.security;

import com.erdos.coal.constant.CacheConst;
import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.core.security.shiro.intf.IShiroUserSecurity;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ShiroUserSecurity implements IShiroUserSecurity {

    /*private final SysUserService sysUserService;
    private final SysRolePermissionService sysRolePermissionService;*/

    @Resource
    private CacheManager cacheManager;
    // 线程安全对象
    private final Map<String, IUserLogin> userLoginMap = new ConcurrentHashMap<>();

    public ShiroUserSecurity(Map<String, IUserLogin> userLoginMap) {
        this.userLoginMap.clear();
        userLoginMap.forEach(this.userLoginMap::put);
    }

    /*@Autowired
    public ShiroUserSecurity(SysUserService sysUserService, SysRolePermissionService sysRolePermissionService) {
        this.sysUserService = sysUserService;
        this.sysRolePermissionService = sysRolePermissionService;
    }*/

    @Override
    //不能使用注解方式
    // 因为这个方法会在 shiro 鉴权时调用，而 shiro 鉴权在 aop 中的拦截优先级高于缓存注解的优先级
    // 所以手动管理缓存
    // @Cacheable(cacheNames = "user_cache", key = "#username")
    public ShiroUser getUserByJwtToken(String userName, String userType) {
        /*Cache cache = cacheManager.getCache(CacheConst.UserKey);
        if (cache == null) return null;

        ShiroUser cacheUser = cache.get(userName + userType, ShiroUser.class);
        if (cacheUser != null) {
            return cacheUser;
        }*/

        UserType ut;
        try {
            ut = UserType.valueOf(userType);
        } catch (Exception e) {
            throw new GlobalException(ResponseCode.ERR_USER_TYPE);
        }
        /*if (ut == null)
            throw new GlobalException(ResponseCode.ERR_USER_TYPE);*/

        IUserLogin userLogin = getUserLogin(ut);
        if (userLogin == null)
            throw new GlobalException(ResponseCode.ERR_USER_TYPE);
        //登录操作
        ShiroUser shiroUser = userLogin.getShiroUser(userName);
        //cache.put(userName + userType, shiroUser);
        return shiroUser;
    }

    @Override
    public List<String> findPermissionByJwtToken(String userName, String userType) {
        UserType ut;
        try {
            ut = UserType.valueOf(userType);
        } catch (Exception e) {
            throw new GlobalException(ResponseCode.ERR_USER_TYPE);
        }
        /*if (ut == null)
            throw new GlobalException(ResponseCode.ERR_USER_TYPE);*/

        IUserLogin userLogin = getUserLogin(ut);
        return userLogin.findPermissionByUser(userName);
    }

    private IUserLogin getUserLogin(UserType ut) {
        IUserLogin userLogin = null;
        switch (ut) {
            case BIZ: //业务用户
                userLogin = userLoginMap.get("bizUserLoginImpl");
                break;
            case WEB: //WEB用户
                userLogin = userLoginMap.get("webUserLoginImpl");
                break;
            case CU: //客商用户
                userLogin = userLoginMap.get("customerUserLoginImpl");
                break;
            case DU: //司机用户
                userLogin = userLoginMap.get("driverUserLoginImpl");
                break;
            case WECHAT: //微信用户
                userLogin = userLoginMap.get("wechatUserLoginImpl");
                break;
            default:
        }
        return userLogin;
    }

    @Override
    @Async("loginTaskExecutor")
    public void putShiroUserToCache(String userId, String username, String password, String userType) {
        Cache cache = cacheManager.getCache(CacheConst.UserKey);

        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserId(userId);
        shiroUser.setUsername(username);
        shiroUser.setPassword(password);
        shiroUser.setUserType(userType);
        System.out.println("用户信息添加到缓存" + username);
        //assert cache != null;
        cache.put(username + userType, shiroUser);
    }
}
