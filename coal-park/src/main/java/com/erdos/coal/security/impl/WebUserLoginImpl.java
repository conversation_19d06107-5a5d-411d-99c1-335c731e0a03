package com.erdos.coal.security.impl;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.park.web.sys.service.ISysUserService;
import com.erdos.coal.security.IUserLogin;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

//Web或系统用户
@Service("webUserLoginImpl")
public class WebUserLoginImpl implements IUserLogin {

    @Resource
    private ISysUserService sysUserService;
    @Resource
    private ISMSService smsService;

    @Override
    public ShiroUser getShiroUser(String loginName) {

        SysUser sysUser = sysUserService.get("loginName", loginName);
        if (sysUser == null) throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);

        //TODO: 组装返回对象----业务系统登录仅仅是做个token的安全验证，不参与用户详细信息和业务逻辑内容判断
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setUserType(SysConstants.UserType.UT_SYSTEM.toString());
        shiroUser.setUserId(sysUser.getId());
        shiroUser.setUsername(sysUser.getLoginName());
        shiroUser.setPassword(sysUser.getPassword());

        return shiroUser;

        /*//当前web用户只用到了 用户名和密码
        String username = (String) jwtToken.getPrincipal();
        String password = (String) jwtToken.getCredentials();
        String code = jwtToken.getCode();

        //TODO: 1, 除了admin，其他用户需验证码校验
        if (!username.equals("admin")) {
            SMS sms = smsService.get("mobile", username);
            if (sms == null || !sms.getCode().equals(code))
                throw new GlobalException(1, "验证码错误");
        }

        //TODO: 2, 获取用户
        // 这里不用 MD5 的 hash 处理, 只按唯一名称取出用户对象就可以
        //params.put("password", Utils.md5(password));
        SysUser user = sysUserService.get(sysUserService.createQuery().filter("loginName", username));

        //用户是否存在
        if (user == null)
            throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);

        // 判断使用BCryptPasswordEncoder进行加密的密码是否正确
        if (!new BCryptPasswordEncoder().matches(password, user.getPassword()))
            throw new GlobalException(ResponseCode.ERR_FAIL); //密码不对, BadCredentialsException

        // 还可以加一些其他信息的判断，比如用户账号已停用等判断
        //if (user.getStatus().equals("PROHIBIT")) //禁用状态
        //   throw new GlobalException(-1, "该用户已被冻结"); //LockedException

        //TODO: 3, 用户权限列表
        // 根据用户拥有的权限标识与如 @PreAuthorize("hasAuthority('sys:menu:view')") 标注的接口对比，决定是否可以调用接口
        Set<String> permissions = sysUserService.findPermissions(username);

        *//*for (int i = 100; i < 195; i++) {
            permissions.add("aaa:" + "bbb:" + "cc" + i);
        }*//*

        List<GrantedAuthority> grantedAuthorities = permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());

        //TODO: 组装返回对象
        SelfUserEntity selfUserEntity = new SelfUserEntity();
        selfUserEntity.setUserType(SysConstants.UserType.UT_SYSTEM.toString());
        selfUserEntity.setExp(JWTConfig.expWeb);
        selfUserEntity.setAccountNonExpired(true).setAccountNonLocked(true).setEnabled(true).setCredentialsNonExpired(true);

        BeanUtils.copyProperties(user, selfUserEntity); // 不能完全处理, 字段名不是全部相同

        selfUserEntity.setUserId(user.getObjectId().toString());
        selfUserEntity.setUsername(user.getLoginName()); //TODO: 登录名, 各用户可能不同
        selfUserEntity.setAuthorities(grantedAuthorities);

        return selfUserEntity;*/
    }

    @Override
    public List<String> findPermissionByUser(String loginName) {
        //TODO: 2, 用户权限列表
        List<String> permissions = sysUserService.findPermissions(loginName);
        return permissions;
    }
}
