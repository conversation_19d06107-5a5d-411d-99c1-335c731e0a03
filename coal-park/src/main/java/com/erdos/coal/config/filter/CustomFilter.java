package com.erdos.coal.config.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebFilter(filterName = "customFilter", urlPatterns = "/*")
public class CustomFilter implements Filter {

    //TODO: Application 中添加 @ServletComponentScan

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        //logger.info("filter init...");
    }

    private boolean requestUriIsMatch(String uri) {

        //TODO: 目前只允许: [/api, /demo, /test, /web] 开头的
        String[] allowUris = {
                "/api/**",
                "/web/**",
                "/we_chat/small_pro/**"
        };

        PathMatcher pathMatcher = new AntPathMatcher();
        for (String s : allowUris) {
            if (pathMatcher.match(s, uri)) { //s 是否包含在 uri 中
                return true;
            }
        }
        return false;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        //TODO: 统一处理返回
        response.setCharacterEncoding("utf-8");
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        response.addHeader("X-FRAME-OPTIONS", "DENY");//处理X-FRAME-OPTIONS漏洞, SAMEORIGIN 或 DENY

        // 除指定前缀路径外，其它不允许
        if (requestUriIsMatch(request.getRequestURI())) {

            // logger.warn("***合法请求***: " + path);

            //TODO: 处理跨域请求, 设置header、判断预请求 OPTIONS, 否则直接 doFilter
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST");
            //response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Allow-Headers", "Accept, Content-Type, Origin, x-access-token-v2");
            response.addHeader("Access-Control-Expose-Headers", "x-access-token-v2");
            response.setHeader("Access-Control-Max-Age", "3600");
            if (request.getMethod().equals("OPTIONS")) {
                response.setStatus(HttpServletResponse.SC_OK);
            } else {
                filterChain.doFilter(request, servletResponse);
            }

        } else {

            logger.warn("***非法请求***: " + request.getRequestURI());

        }
    }

    @Override
    public void destroy() {
        //logger.info("filter destroy...");
    }
}
