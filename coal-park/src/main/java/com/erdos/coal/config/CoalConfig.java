package com.erdos.coal.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Configuration
public class CoalConfig {

    @Value("${coal.upload-path}")
    public String uploadPath; //上传路径

    @Value("${coal.log-path}")
    public String logPath; //日志路径

    @Value("${coal.white-list}")
    public List<String> whiteList; //白名单

    @Value("${coal.black-list}")
    public List<String> blackList; //黑名单

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public void setWhiteList(List<String> whiteList) {
        this.whiteList = whiteList;
    }

    public void setBlackList(List<String> blackList) {
        this.blackList = blackList;
    }
}
