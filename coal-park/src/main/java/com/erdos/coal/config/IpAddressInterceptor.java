package com.erdos.coal.config;

import com.erdos.coal.core.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by LIGX on 2018/11/17.
 * IP验证及处理
 */
@Configuration
public class IpAddressInterceptor extends HandlerInterceptorAdapter {

    private Logger logger = LoggerFactory.getLogger(IpAddressInterceptor.class);

    @Resource
    private CoalConfig coalConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) {

        //黑名单:
        //启用: 则加入黑名单的禁止通过

        //白名单:
        //启用: 则加入白名单的允许通过, 未加入白名单的不允许通过

        //当前请求的IP地址
        String ipAddress = Utils.getRealIP(request);
        //验证黑名单
        if (!CollectionUtils.isEmpty(coalConfig.blackList)) {
            //TODO: 是否加入黑名单
            if (coalConfig.blackList.contains(ipAddress)) {
                logger.info("地址 {} 在黑名单中，被限制请求.", ipAddress);
                return false;
            }
        }
        //验证白名单
        if (!CollectionUtils.isEmpty(coalConfig.whiteList)) {
            //TODO: 是否加入白名单
            if (coalConfig.whiteList.contains(ipAddress)) {
                return true;
            } else {
                logger.info("地址 {} 不在白名单中，被限制请求.", ipAddress);
                return false;
            }
        }

        return true;
    }
}
