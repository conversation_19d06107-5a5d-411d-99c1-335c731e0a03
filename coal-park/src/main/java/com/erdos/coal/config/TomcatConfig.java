package com.erdos.coal.config;

import org.apache.catalina.connector.Connector;
import org.apache.coyote.http11.Http11NioProtocol;
import org.apache.tomcat.util.descriptor.web.SecurityCollection;
import org.apache.tomcat.util.descriptor.web.SecurityConstraint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class TomcatConfig {

    private Logger logger = LoggerFactory.getLogger(TomcatConfig.class);

    @Bean
    public ConfigurableServletWebServerFactory configurableServletWebServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();

        //TODO: 除 GET、POST、OPTIONS 之外，不允许其它请求，其中 OPTIONS 跨域请求需要用到
        factory.addContextCustomizers(context -> {
            SecurityConstraint securityConstraint = new SecurityConstraint();
            // securityConstraint.setUserConstraint("CONFIDENTIAL");
            SecurityCollection collection = new SecurityCollection();
            collection.addPattern("/*");
            //  collection.addMethod("HEAD");
            //  collection.addMethod("PUT");
            //  collection.addMethod("DELETE");
            //  collection.addMethod("TRACE");
            //  collection.addMethod("COPY");
            //  collection.addMethod("SEARCH");
            //  collection.addMethod("PROPFIND");
            securityConstraint.addCollection(collection);
            context.addConstraint(securityConstraint);
        });

        // Tomcat 请求优化
        factory.addConnectorCustomizers(connector -> {
            Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
            //设置最大连接数
            protocol.setMaxConnections(20000);
            //设置最大线程数
            protocol.setMaxThreads(2000);
            protocol.setConnectionTimeout(30000);
        });
        //factory.setPort(8080);
        //factory.setContextPath("/api-aaa");

        factory.addConnectorCustomizers(gracefulShutdown());

        return factory;
    }

    /**
     * 用于接受 shutdown 事件
     *
     * @return
     */
    @Bean
    public GracefulShutdown gracefulShutdown() {
        return new GracefulShutdown();
    }

    private class GracefulShutdown implements TomcatConnectorCustomizer, ApplicationListener<ContextClosedEvent> {
        private volatile Connector connector;
        private final int waitTime = 10;

        @Override
        public void customize(Connector connector) {
            this.connector = connector;
        }

        @Override
        public void onApplicationEvent(ContextClosedEvent contextClosedEvent) {
            if (contextClosedEvent.getApplicationContext().getParent() == null) {
                logger.warn("Shutdown ...");

                this.connector.pause();
                Executor executor = this.connector.getProtocolHandler().getExecutor();
                try {
                    if (executor instanceof ThreadPoolExecutor) {
                        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) executor;
                        threadPoolExecutor.shutdown();
//                    if (!threadPoolExecutor.awaitTermination(waitTime, TimeUnit.SECONDS)) {
//                        logger.warn("Tomcat 进程在" + waitTime + " 秒内无法结束，尝试强制结束");
//                    }

                        while (!threadPoolExecutor.awaitTermination(1, TimeUnit.SECONDS)) {
                            logger.warn("Wait for completion ...");
                        }

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    Thread.currentThread().interrupt();
                } finally {
                    logger.warn("Completed!");
                }
            }
        }
    }
}
