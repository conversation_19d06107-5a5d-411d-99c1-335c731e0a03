package com.erdos.coal.config;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Configurable
public class OcrConfig {

    @Value("${ocr.language-path}")
    public String languagePath;

    @Value("${ocr.opencv-lib}")
    public String opencvLib;

    @Value("${ocr.test-file-path}")
    public String testFilePath;

    public void setLanguagePath(String languagePath) {
        this.languagePath = languagePath;
    }

    public void setOpencvLib(String opencvLib) {
        this.opencvLib = opencvLib;
    }

    public void setTestFilePath(String testFilePath) {
        this.testFilePath = testFilePath;
    }
}
