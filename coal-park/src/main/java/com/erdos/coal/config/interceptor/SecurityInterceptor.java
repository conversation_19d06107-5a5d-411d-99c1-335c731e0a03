package com.erdos.coal.config.interceptor;

import com.erdos.coal.core.exception.GlobalException;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Configuration
public class SecurityInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws GlobalException {

//        System.out.println("SecurityInterceptor 被调用");

//        System.out.println("url:" + request.getRequestURI());

//        // 获取token
//        String token = request.getHeader("token");
//        // 获取时间戳
//        String timestamp = request.getHeader("timestamp");
//        // 获取随机字符串
//        String nonceStr = request.getHeader("nonceStr");
//        // 获取请求地址
//        String url = request.getHeader("url");
//        // 获取签名
//        String signature = request.getHeader("signature");

        String url = request.getRequestURI();

        // 不存在 URI 时, 会有一次error请求
        if (url.equalsIgnoreCase("/error")) {
            //不再继续向下执行,返回数据了
            return false;
        }

        // 判断请求的url参数是否正确
//        if (!request.getRequestURI().equals(url)) {
//            //非法请求 (防止跨域攻击)
//            return;
//        }

        //继续下一个拦截器
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
        //System.out.println("postHandle被调用");
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {
        //System.out.println("afterCompletion被调用");
    }

}
