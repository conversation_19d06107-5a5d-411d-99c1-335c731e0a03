package com.erdos.coal.config;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.config.JWTConfig;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.manage.entity.UserTokenRecord;
import com.erdos.coal.park.api.manage.service.IUserTokenRecordService;
import com.erdos.coal.park.web.sys.service.ISysAppService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * Created by LIGX on 2018/11/17.
 * 验证及处理
 */
@Configuration
public class WebInterceptor extends HandlerInterceptorAdapter {

    private Logger logger = LoggerFactory.getLogger(WebInterceptor.class);
    @Resource
    private ISysAppService sysAppService;
    @Resource
    private IUserTokenRecordService userTokenRecordService;

    //TODO: ligx 把签名key， 签名允许时间阀值, 签名排除列表放到配置文件中
//    private final String secretKey = "ht123456";
//    private final long diffTime = 600000;

    private boolean signVerify(String url) {

        //TODO: 排除以下路径
        PathMatcher pathMatcher = new AntPathMatcher();
        for (String s : JWTConfig.signExcludeMatchers) { //遍历排除路径列表
            if (pathMatcher.match(s, url)) { //s 是否包含在 url 中
                return false; // 存在，不需要验签
            }
        }
        return true; // 不存在，需要验签
    }

    private boolean signVerify2(String url) {

        //TODO: 排除以下路径
        PathMatcher pathMatcher = new AntPathMatcher();
        for (String s : JWTConfig.antMatchers) { //遍历排除路径列表
            if (pathMatcher.match(s, url)) { //s 是否包含在 url 中
                return false; // 存在，不需要验token
            }
        }
        return true; // 不存在，需要验token
    }

    private boolean urlCurrentLimit(String url, UserTokenRecord record) {
        PathMatcher pathMatcher = new AntPathMatcher();

        UpdateOperations<UserTokenRecord> updateOption = userTokenRecordService.createUpdateOperations();
        Query<UserTokenRecord> query = userTokenRecordService.createQuery();
        query.filter("_id", record.getObjectId());
        if (pathMatcher.match("/api/cus/goods/add_goods7", url)) {
            query.criteria("time1").lessThan(System.currentTimeMillis() - JWTConfig.expTime1);
            updateOption.set("time1", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/del_goods4", url)) {
            query.criteria("time2").lessThan(System.currentTimeMillis() - JWTConfig.expTime2);
            updateOption.set("time2", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/use_bus_goods4", url)) {
            query.criteria("time3").lessThan(System.currentTimeMillis() - JWTConfig.expTime1);
            updateOption.set("time3", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/update_Goods2", url)) {
            query.criteria("time4").lessThan(System.currentTimeMillis() - JWTConfig.expTime4);
            updateOption.set("time4", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/update_goods_place2", url)) {
            query.criteria("time5").lessThan(System.currentTimeMillis() - JWTConfig.expTime5);
            updateOption.set("time5", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/goods_to_friend2", url)) {
            query.criteria("time6").lessThan(System.currentTimeMillis() - JWTConfig.expTime6);
            updateOption.set("time6", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/regain_friend_goods2", url)) {
            query.criteria("time7").lessThan(System.currentTimeMillis() - JWTConfig.expTime7);
            updateOption.set("time7", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/back_friend_goods2", url)) {
            query.criteria("time8").lessThan(System.currentTimeMillis() - JWTConfig.expTime8);
            updateOption.set("time8", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/order/update_orders2", url)) {
            query.criteria("time9").lessThan(System.currentTimeMillis() - JWTConfig.expTime9);
            updateOption.set("time9", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/order/update_orders_place2", url)) {
            query.criteria("time10").lessThan(System.currentTimeMillis() - JWTConfig.expTime10);
            updateOption.set("time10", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/order/appoint_order_car2", url)) {
            query.criteria("time11").lessThan(System.currentTimeMillis() - JWTConfig.expTime11);
            updateOption.set("time11", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/design_carNum4", url)) {
            query.criteria("time12").lessThan(System.currentTimeMillis() - JWTConfig.expTime12);
            updateOption.set("time12", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/sweep_code4", url)) {
            query.criteria("time13").lessThan(System.currentTimeMillis() - JWTConfig.expTime13);
            updateOption.set("time13", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/grab", url)) {
            query.criteria("time14").lessThan(System.currentTimeMillis() - JWTConfig.expTime14);
            updateOption.set("time14", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/appointment", url)) {
            query.criteria("time15").lessThan(System.currentTimeMillis() - JWTConfig.expTime15);
            updateOption.set("time15", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/update_order_carnum", url)) {
            query.criteria("time16").lessThan(System.currentTimeMillis() - JWTConfig.expTime16);
            updateOption.set("time16", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/punch_clock", url)) {
            query.criteria("time17").lessThan(System.currentTimeMillis() - JWTConfig.expTime17);
            updateOption.set("time17", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/upload_quarantine_pho", url)) {
            query.criteria("time18").lessThan(System.currentTimeMillis() - JWTConfig.expTime18);
            updateOption.set("time18", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/add_quarantine", url)) {
            query.criteria("time19").lessThan(System.currentTimeMillis() - JWTConfig.expTime19);
            updateOption.set("time19", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/push/save_delete_order4", url)) {
            query.criteria("time20").lessThan(System.currentTimeMillis() - JWTConfig.expTime20);
            updateOption.set("time20", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/add_goods6", url)) {
            query.or(
                    query.criteria("time21").lessThan(System.currentTimeMillis() - JWTConfig.expTime1),
                    query.criteria("time21").equal(null)
            );
            updateOption.set("time21", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/del_goods3", url)) {
            query.or(
                    query.criteria("time22").lessThan(System.currentTimeMillis() - JWTConfig.expTime2),
                    query.criteria("time22").equal(null)
            );
            updateOption.set("time22", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/push/save_delete_order3", url)) {
            query.or(
                    query.criteria("time23").lessThan(System.currentTimeMillis() - JWTConfig.expTime20),
                    query.criteria("time23").equal(null)
            );
            updateOption.set("time23", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/design_carNum5", url)) {
            query.or(
                    query.criteria("time24").lessThan(System.currentTimeMillis() - JWTConfig.expTime12),
                    query.criteria("time24").equal(null)
            );
            updateOption.set("time24", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/sweep_code5", url)) {
            query.or(
                    query.criteria("time25").lessThan(System.currentTimeMillis() - JWTConfig.expTime13),
                    query.criteria("time25").equal(null)
            );
            updateOption.set("time25", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/cus/goods/use_bus_goods3", url)) {
            query.or(
                    query.criteria("time26").lessThan(System.currentTimeMillis() - JWTConfig.expTime1),
                    query.criteria("time26").equal(null)
            );
            updateOption.set("time26", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/bus/biz_push_by_billcode", url)) {
            query.or(
                    query.criteria("time27").lessThan(System.currentTimeMillis() - JWTConfig.expTime18),
                    query.criteria("time27").equal(null)
            );
            updateOption.set("time27", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/design_carNum6", url)) {
            query.or(
                    query.criteria("time28").lessThan(System.currentTimeMillis() - JWTConfig.expTime12),
                    query.criteria("time28").equal(null)
            );
            updateOption.set("time28", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/dvr/order/sweep_code6", url)) {
            query.or(
                    query.criteria("time29").lessThan(System.currentTimeMillis() - JWTConfig.expTime13),
                    query.criteria("time29").equal(null)
            );
            updateOption.set("time29", System.currentTimeMillis());
        } else if (pathMatcher.match("/api/pay/refund/wx_refund_apply", url)) {
            query.or(
                    query.criteria("time30").lessThan(System.currentTimeMillis() - JWTConfig.expTime13),
                    query.criteria("time30").equal(null)
            );
            updateOption.set("time30", System.currentTimeMillis());
        }
        UpdateResults result = userTokenRecordService.update(query, updateOption);
        return !result.getUpdatedExisting();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) {

        String url = request.getRequestURI();
        if (url.contains("/api/file/driver.apk")) {
            sysAppService.update(sysAppService.createQuery().filter("type", 1), sysAppService.createUpdateOperations().inc("times", 1));
            logger.info("司机APP下载+1");
        } else if (url.contains("/api/file/customer.apk")) {
            sysAppService.update(sysAppService.createQuery().filter("type", 0), sysAppService.createUpdateOperations().inc("times", 1));
            logger.info("客商APP下载+1");
        }

        // 处理业务系统接口鉴权
        if (signVerify(url)) {   // web，业务系统，扫码下载app 无需验证

            //request的header区分新旧版本请求
           if (signVerify2(url)) {
                //String edition = request.getHeader("edition");
                String token = request.getHeader("x-access-token-v2");
                //if (StrUtil.isNotEmpty(edition) && edition.equals("new")) {
                UserTokenRecord record = userTokenRecordService.get("token", token);
                if (record == null) {
                    // logger.warn("令牌过期2,被新用户登录顶替后，无效token请求：" + url);
                    throw new GlobalException(ResponseCode.ERR_TOKEN_EXPIRE);
                }

                //部分接口 限制请求频率
               /*
                 * /api/cus/goods/add_goods2
                 * /api/cus/goods/del_goods2
                 * /api/cus/goods/add_goods_pl
                 * /api/cus/goods/update_Goods2
                 * /api/cus/goods/update_goods_place2
                 *
                 * /api/cus/goods/goods_to_friend2
                 * /api/cus/goods/regain_friend_goods2
                 * /api/cus/goods/back_friend_goods2
                 * /api/cus/order/update_orders2
                 * /api/cus/order/update_orders_place2
                 *
                 * /api/cus/order/appoint_order_car2
                 * /api/dvr/order/design_carNum2
                 * /api/dvr/order/sweep_code2
                 * /api/dvr/order/grab
                 * /api/dvr/order/appointment
                 *
                 * /api/dvr/order/update_order_carnum
                 * /api/dvr/order/punch_clock
                 * /api/dvr/order/upload_quarantine_pho
                 * /api/dvr/order/add_quarantine
                 * /api/dvr/push/save_delete_order2
                 * */
                boolean isLimit = urlCurrentLimit(url, record);
                if (isLimit) return false;

                //}
            }

            Map<String, String[]> paraMap = request.getParameterMap();
            Map<String, String> map = StrUtil.convertMap(paraMap);

            // TODO 1：判断时间戳是否超时
            String parTime = map.get("timestamp");
            if (StrUtil.isEmpty(parTime) || parTime.length() < 10 || parTime.length() > 13 || !StrUtil.isNumericInt(parTime)) {
                logger.warn("签名验证失败,time:" + parTime);
                throw new GlobalException(ResponseCode.ERR_TOKEN);
            }

            long time = Long.parseLong(parTime);
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (parTime.length() == 10) time = Long.valueOf(parTime + "000");

            long timeStamp = System.currentTimeMillis();
            if (timeStamp - time > JWTConfig.signDiffTime) {
                logger.warn("签名验证失败,时间差大于signDiffTime");
                throw new GlobalException(ResponseCode.ERR_TOKEN_EXPIRE);
            }

            // TODO 2：MD5（参数按字典序 + 密钥) 是否等于sign
            String parameter = StrUtil.sortParam(map, "utf-8");

            parameter = parameter + JWTConfig.signSecret;   // 参数 + 密钥
            //System.out.println(parameter);
            //System.out.println(Utils.md5(parameter));
            if (StrUtil.isEmpty(map.get("sign")) || !map.get("sign").equals(Utils.md5(parameter))) {
                logger.warn("签名验证失败");
                throw new GlobalException(ResponseCode.ERR_PERMISSION_DENIED);
            }
        }
        return true;
    }
}
