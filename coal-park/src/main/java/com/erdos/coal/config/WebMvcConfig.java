package com.erdos.coal.config;

import com.erdos.coal.config.interceptor.SecurityInterceptor;
import com.erdos.coal.config.interceptor.TokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.servlet.config.annotation.*;

import javax.annotation.Resource;

/**
 * Created by LIGX on 2018/12/17.
 * WebMvcConfig 拦截器
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurationSupport {

    @Resource
    private CoalConfig coalConfig;

    @Resource
    private IpAddressInterceptor ipAddressInterceptor;

    @Resource
    private WebInterceptor webInterceptor;

    @Resource
    private SecurityInterceptor securityInterceptor;

    @Resource
    private TokenInterceptor tokenInterceptor;

    @Autowired
    public void setWebInterceptor(IpAddressInterceptor ipAddressInterceptor, WebInterceptor webInterceptor) {
        this.ipAddressInterceptor = ipAddressInterceptor;
        this.webInterceptor = webInterceptor;
    }

    private final String[] notInterceptPaths = {
            "/static/**",
            "/test/**"
    };

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        //拦截全部, 配合过滤器使用
        registry.addInterceptor(securityInterceptor).addPathPatterns("/**").order(1);
        registry.addInterceptor(tokenInterceptor).addPathPatterns("/**").order(2);

        //对请求IP进行黑白名单处理
        registry.addInterceptor(ipAddressInterceptor).addPathPatterns("/**");

        registry.addInterceptor(webInterceptor).addPathPatterns("/**")
                .excludePathPatterns(notInterceptPaths);
    }

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }

    /**
     * 添加静态资源文件，外部可以直接访问地址
     *
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //设置静态资源路径
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");

        //设置文件下载路径
        registry.addResourceHandler("/web/file/**").addResourceLocations("file:" + coalConfig.uploadPath);
        registry.addResourceHandler("/api/file/**").addResourceLocations("file:" + coalConfig.uploadPath);

        //日志下载路径
        //registry.addResourceHandler("/web/log/**").addResourceLocations("file:" + coalConfig.logPath);
        registry.addResourceHandler("/web/log/app1/**").addResourceLocations("file:/root/projectv2/app1/logs/");
        registry.addResourceHandler("/web/log/app2/**").addResourceLocations("file:/root/projectv2/app2/logs/");

        //registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        //registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");

        super.addResourceHandlers(registry);
    }

    //设置超时
    @Override
    public void configureAsyncSupport(final AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(20000);
        configurer.registerCallableInterceptors(timeoutInterceptor());
    }

    @Bean
    public TimeoutCallableProcessingInterceptor timeoutInterceptor() {
        return new TimeoutCallableProcessingInterceptor();
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/web/**")//设置允许跨域的路径
                .allowedOrigins("*")//设置允许跨域请求的域名
                //.allowCredentials(true)//是否允许证书 不再默认开启
                .allowedMethods("GET", "POST", "OPTIONS")//设置允许的方法
                .maxAge(3600);//跨域允许时间
    }
}
