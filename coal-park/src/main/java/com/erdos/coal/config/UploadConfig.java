package com.erdos.coal.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;

@Configuration
public class UploadConfig {

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        // 单个文件最大
        factory.setMaxFileSize(DataSize.ofMegabytes(50)); //1M     客商和司机app包较大
        // 设置总上传数据总大小
        factory.setMaxRequestSize(DataSize.ofMegabytes(100)); //10M
        return factory.createMultipartConfig();
    }

}
