package com.erdos.coal;

import com.erdos.coal.core.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

@ServletComponentScan
@EnableTransactionManagement //事务管理
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableScheduling
public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        //System.setProperty("es.set.netty.runtime.available.processors", "false");//for elaticsearch

        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);

        try {
            Environment env = context.getEnvironment();
            String port = env.getProperty("server.port");
            // String path = ConvertTools.getString(env.getProperty("server.servlet.context-path"));

            Inet4Address addr = (Inet4Address) InetAddress.getLocalHost();
            String ip = addr.getHostAddress(); //获取本机ip
            String hostName = addr.getHostName(); //获取本机计算机名称
            logger.info("http://{}:{}/web/test", hostName, port);
            Utils.setStartupInfo(hostName, ip, port);
            logger.info("-= 启动完成 =-");
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

//        // 加载动态库
//        try {
//            // opencv lib
//            OcrConfig ocrConfig = context.getBean(OcrConfig.class);
//            System.load(ocrConfig.opencvLib);
//            logger.info("opencv lib version: {} ", Core.VERSION);
//        } catch (Exception e) {
//            throw new RuntimeException("未找到opencv库");
//        }
    }
}