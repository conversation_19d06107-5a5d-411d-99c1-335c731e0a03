package com.erdos.coal.park.api.driver.dao;

import com.erdos.coal.core.base.mongo.IBaseMongoDAO;
import com.erdos.coal.park.api.driver.entity.FileInfo;
import org.springframework.web.multipart.MultipartFile;

public interface IFileInfoDao extends IBaseMongoDAO<FileInfo> {
    //保存文件（图片）
    String fileSave(String id, MultipartFile inputStream);

    String fileSave(String id, MultipartFile inputStream, String bucket);

    //读取文件（图片）
    String readFile(String id, String bucket);

    String readFileName(String uid, String bucket);

    //删除文件（图片）
    void deleteByUId(String uid);

    void deleteByUId(String uid, String bucket);

    //保存文件（图片）到服务器磁盘
    String fileSaveDisc(String id, MultipartFile mFile);

    //服务器文件（图片）保存到数据库
    String fileSaveDB(String photo, String bucket);

    //删除数据库里文件（图片）不删除磁盘文件
    void deleteDB(String uid, String bucket);
}
