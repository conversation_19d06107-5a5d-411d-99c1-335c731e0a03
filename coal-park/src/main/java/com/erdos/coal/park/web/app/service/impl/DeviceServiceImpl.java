package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.entity.GeoPoint;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.app.dao.IDeviceDao;
import com.erdos.coal.park.web.app.entity.DeviceInfo;
import com.erdos.coal.park.web.app.service.IDeviceService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

@Service("deviceService")
public class DeviceServiceImpl extends BaseMongoServiceImpl<DeviceInfo, IDeviceDao> implements IDeviceService {

    @Resource
    private HttpServletRequest request;

    @Resource
    private IDeviceDao deviceDao;

    @Override
    public EGridResult deviceList(Integer page, Integer rows) {
        Query<DeviceInfo> query = deviceDao.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("name")))
            query.filter("name", request.getParameter("name"));
        if (!StrUtil.isEmpty(request.getParameter("mobile")))
            query.filter("mobile", request.getParameter("mobile"));
        query.order(Sort.ascending("unitCode"),Sort.ascending("subCode"),Sort.ascending("deviceCode"));
        return this.findPage(page, rows, query);
    }

    private boolean isDouble(String s) {
        Pattern pattern = Pattern.compile("[+-]?\\d+(.\\d+)?");
        return pattern.matcher(s).matches();
    }

    @Override
    public ServerResponse addDevice(DeviceInfo deviceInfo) {

//        if (!isDouble(deviceInfo.getLongitude()) || !isDouble(deviceInfo.getLatitude())) {
//            return ServerResponse.createError("经纬度参数错误");
//        }
//        //我国经度范围为:73°33′E-135°05′E, 纬度范围为:3°51′N-53°33′N。
//        double longitude = Double.parseDouble(deviceInfo.getLongitude());
//        double latitude = Double.parseDouble(deviceInfo.getLatitude());
//        if (longitude < 73 || longitude > 135) {
//            return ServerResponse.createError("经度参数错误,应在[73,135]之间");
//        }
//        if (latitude < 3 || latitude > 53) {
//            return ServerResponse.createError("纬度参数错误,应在[3,53]之间");
//        }

        // 检查
        if (ObjectUtils.isEmpty(deviceInfo.getDeviceId()) ||
                ObjectUtils.isEmpty(deviceInfo.getDeviceCode()) ||
                ObjectUtils.isEmpty(deviceInfo.getDeviceName())) {
            return ServerResponse.createError("缺少必要元素");
        }

        // 1, 检查重复
        Query<DeviceInfo> query = deviceDao.createQuery();
        query.or(
                query.criteria("deviceId").equal(deviceInfo.getDeviceId()),
                query.criteria("deviceCode").equal(deviceInfo.getDeviceCode()),
                query.criteria("deviceName").equal(deviceInfo.getDeviceName())
        );
        if (this.list(query).size() > 0) {
            return ServerResponse.createError("记录重复");
        }

        // 2024-4-11 增加二维码字段 qrCode, 并在保存时同步为 deviceId.
        deviceInfo.setQrCode(deviceInfo.getDeviceId());

//        double[] vis2 = new double[]{
//                Double.parseDouble(deviceInfo.getLongitude()),
//                Double.parseDouble(deviceInfo.getLatitude())
//        };
//        deviceInfo.setPoint(new GeoPoint(vis2));

        // 经纬度默认值
        deviceInfo.setLongitude("0");
        deviceInfo.setLatitude("0");
        deviceInfo.setPoint(new GeoPoint(new double[]{0, 0}));

        // 2, 保存
        this.save(deviceInfo);
        return ServerResponse.createSuccess();
    }

    @Override
    public ServerResponse editDevice(DeviceInfo deviceInfo) {
//        if (!isDouble(deviceInfo.getLongitude()) || !isDouble(deviceInfo.getLatitude())) {
//            return ServerResponse.createError("经纬度参数错误");
//        }
//        //我国经度范围为:73°33′E-135°05′E, 纬度范围为:3°51′N-53°33′N。
//        double longitude = Double.parseDouble(deviceInfo.getLongitude());
//        double latitude = Double.parseDouble(deviceInfo.getLatitude());
//        if (longitude < 73 || longitude > 135) {
//            return ServerResponse.createError("经度参数错误,应在[73,135]之间");
//        }
//        if (latitude < 3 || latitude > 53) {
//            return ServerResponse.createError("纬度参数错误,应在[3,53]之间");
//        }

        // 1, 检查重复
        Query<DeviceInfo> query = deviceDao.createQuery();
        query.field("_id").equal(deviceInfo.getObjectId());

        UpdateOperations<DeviceInfo> updateOperations = this.createUpdateOperations();
        updateOperations.set("deviceId", deviceInfo.getDeviceId());
        updateOperations.set("deviceName", deviceInfo.getDeviceName());
        updateOperations.set("deviceCode", deviceInfo.getDeviceCode());
        //updateOperations.set("geometry", deviceInfo.getGeometry());
        updateOperations.set("longitude", "0");
        updateOperations.set("latitude", "0");
        updateOperations.set("status", deviceInfo.getStatus());
        updateOperations.set("checkDistance", deviceInfo.getCheckDistance());
        updateOperations.set("unitCode", deviceInfo.getUnitCode());
        updateOperations.set("unitName", deviceInfo.getUnitName());
        updateOperations.set("subCode", deviceInfo.getSubCode());
        updateOperations.set("subName", deviceInfo.getSubName());
        updateOperations.set("updateTime", new Date().getTime());

        //不修改经纬度
//        double[] vis2 = new double[]{
//                Double.parseDouble(deviceInfo.getLongitude()),
//                Double.parseDouble(deviceInfo.getLatitude())
//        };
//        updateOperations.set("point", new GeoPoint(vis2));

        // 2, 保存
        UpdateResults results = this.update(query, updateOperations);
        if (results.getUpdatedCount() == 1)
            return ServerResponse.createSuccess();
        else
            return ServerResponse.createError("修改失败");
    }

    @Override
    public ServerResponse deleteDevice(DeviceInfo deviceInfo) {
        Query<DeviceInfo> query = deviceDao.createQuery();
        query.field("deviceId").equal(deviceInfo.getDeviceId());
        int result = this.delete(query);
        return result == 1 ? ServerResponse.createSuccess() : ServerResponse.createError();
    }

    @Override
    public ServerResponse updateDevice(DeviceInfo deviceInfo) {
        deviceDao.save(deviceInfo);
        return ServerResponse.createSuccess();
    }

    @Override
    public ServerResponse<List<DeviceInfo>> searchDevice(String unitCode, String subCode, String deviceCode) {
        Query<DeviceInfo> query = deviceDao.createQuery();
        if (!StrUtil.isEmpty(unitCode)) query.filter("unitCode", unitCode);
        if (!StrUtil.isEmpty(subCode)) query.filter("subCode", subCode);
        if (!StrUtil.isEmpty(deviceCode)) query.filter("deviceCode", deviceCode);

        List<DeviceInfo> list = this.list(query);
        return ServerResponse.createSuccess("查询成功", list);
    }

    @Override
    public ServerResponse<String> saveDeviceGeo(String deviceCode, String longitude, String latitude) {
        if (!isDouble(longitude) || !isDouble(latitude)) return ServerResponse.createError("经纬度参数错误");

        //我国经度范围为:73°33′E-135°05′E, 纬度范围为:3°51′N-53°33′N。
        double lon = Double.parseDouble(longitude);
        double lat = Double.parseDouble(latitude);
        if (lon < 0 || lon > 180) return ServerResponse.createError("经度参数错误,应在[-180,180]之间");
        if (lat < -90 || lat > 90) return ServerResponse.createError("纬度参数错误,应在[-90,90]之间");

        // 1, 检查重复
        Query<DeviceInfo> query = deviceDao.createQuery();
        query.field("deviceCode").equal(deviceCode);

        UpdateOperations<DeviceInfo> updateOperations = this.createUpdateOperations();
        updateOperations.set("longitude", longitude);
        updateOperations.set("latitude", latitude);
        updateOperations.set("updateTime", new Date().getTime());
        updateOperations.set("point", new GeoPoint(lon, lat));

        // 2, 保存
        UpdateResults results = this.update(query, updateOperations);
        if (results.getUpdatedCount() == 1) {
            return ServerResponse.createSuccess("上传成功");
        } else {
            return ServerResponse.createError("上传失败");
        }
    }
}
