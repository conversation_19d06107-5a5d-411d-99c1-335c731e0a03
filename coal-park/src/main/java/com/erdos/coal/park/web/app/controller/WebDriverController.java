package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.QuarantineInfo;
import com.erdos.coal.park.web.app.service.IWebDriverService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

@RestController
@RequestMapping("/web/app/driver")
public class WebDriverController extends BaseController {

    @Resource
    private IWebDriverService driverService;

    @PostMapping("/driver_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(driverService.loadGrid(page, rows));
    }

    @PostMapping("/driver_edit")
    public ServerResponse editHandler() throws GlobalException {
        return driverService.editDriver();
    }

    @PostMapping("/saveCheck")
    public ServerResponse saveCheckHandler(@RequestBody DriverInfo driverInfo) {
        return driverService.saveCheck(driverInfo);
    }

    @PostMapping("/account/list")
    public ServerResponse<EGridResult> accListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(driverService.accList(page, rows));
    }

    @PostMapping("/account/edit")
    public ServerResponse<EGridResult> accEditHandler(Integer page, Integer rows) {
        return ServerResponse.createSuccess(driverService.accEdit(page, rows));
    }

    @PostMapping("/account/getAvailableFee")
    public ServerResponse<BigDecimal> getAvailableFee() {
        return ServerResponse.createSuccess(driverService.getAvailableFee());
    }

    @PostMapping("/dri_list")
    public ServerResponse<EGridResult> driListHandler() throws GlobalException {
        return ServerResponse.createSuccess(driverService.bwlist());
    }

    @PostMapping("/dvr_sign_in_list")
    public ServerResponse<EGridResult> dvrSignInlistHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(driverService.loadSignInGrid(page, rows));
    }

    @PostMapping("/dvr_to_car_list")
    public ServerResponse<EGridResult> dvrToCarlistHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(driverService.loadDvrToCarGrid(page, rows));
    }

    @PostMapping("/update_dvr_to_car")
    public ServerResponse<String> updateDvrToCarHandler() throws GlobalException {
        return driverService.updateDvrToCarDel();
    }

    @PostMapping("/dvr_quarantine_list")
    public ServerResponse<EGridResult> dvrQuarantineListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(driverService.loadGridQuarantine(page, rows));
    }

    @PostMapping("/save_audit")
    public ServerResponse saveCheckHandler() {
        return driverService.saveAudit();
    }
}
