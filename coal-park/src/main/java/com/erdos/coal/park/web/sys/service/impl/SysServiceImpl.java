package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.manage.controller.SmsController;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.service.IImgVerifyTokenService;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.web.sys.dao.ISysUserDao;
import com.erdos.coal.park.web.sys.entity.SessionUser;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.park.web.sys.service.ISysService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

@Service("sysService")
public class SysServiceImpl implements ISysService {
    @Resource
    private ISMSService smsService;
    @Resource
    private ISysUserDao sysUserDao;

    @Resource
    private HttpSession session;
    @Resource
    private IImgVerifyTokenService iImgVerifyTokenService;

    @Value("${spring.coal.session_timeout:1800}")
    private int maxInactiveInterval;

    @Override
    public Map<String, Object> login(String uname, String pwd) {

        if (StrUtil.isEmpty(uname) || StrUtil.isEmpty(pwd)) {
            return null;
        }

        // TODO: 1, 查询用户基本信息
        Query<SysUser> query = sysUserDao.createQuery();
        query.filter("loginName", uname);
        query.filter("password", Utils.md5(pwd));
        SysUser sysUser = sysUserDao.get(query);
        if (sysUser == null) {
            return null;
        }

        SessionUser sessionUser = new SessionUser();
        sessionUser.setLoginName(sysUser.getLoginName());//必须
        sessionUser.setPassword(pwd);//必须

        sessionUser.setName(uname);
        sessionUser.setSuperAdmin(uname.equals("admin"));

        Map<String, Object> param = new HashMap<>();
        //param.put("uname", uname);
        //param.put("password", Utils.md5(password));

        setSessionObject(sessionUser);
        return param;
    }

    @Override
    public ServerResponse<AccessToken> login2(String username, String password) {
        if (StrUtil.isEmpty(username) || StrUtil.isEmpty(password)) {
            return null;
        }

        // TODO: 1, 查询用户基本信息
        SysUser sysUser = sysUserDao.get("loginName", username);
        if (sysUser == null || !Utils.md5(password).equals(sysUser.getPassword()))
            return ServerResponse.createError("用户名或密码错误");

        //异步添加用户信息到缓存
        //shiroUserSecurity.putShiroUserToCache(sysUser.getObjectId().toHexString(), sysUser.getLoginName(), sysUser.getPassword(), UserType.WEB.toString());

        String token = JwtUtil.sign(UserType.WEB, sysUser.getLoginName(), sysUser.getPassword());

        /*UsernamePasswordToken token = new UsernamePasswordToken(sysUser.getLoginName(), sysUser.getPassword());
        AccessToken accessToken = new AccessToken(token.toString());
        return ServerResponse.createSuccess("登录成功", accessToken);*/
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }

    @Override
    public ServerResponse<AccessToken> login2(String username, String password, String smsCode, String imgCode) {
        if (StrUtil.isEmpty(username) || StrUtil.isEmpty(password)) return null;
        if (!username.equals("admin")) {
            Query<SMS> query = smsService.createQuery();
            query.criteria("mobile").equal(username);
            query.criteria("code").equal(smsCode);
            SMS sms = smsService.get("code", smsCode);
            if (sms == null) return ServerResponse.createError("验证码错误");
        }
        /*else {
            if (!iImgVerifyTokenService.checkImaVerifyToken(imgCode))
                return ServerResponse.createError("验证失败");
        }*/

        // TODO: 1, 查询用户基本信息
        SysUser sysUser = sysUserDao.get("loginName", username);
        if (sysUser == null || !Utils.md5(password).equals(sysUser.getPassword()))
            return ServerResponse.createError("用户名或密码错误");

        String token = JwtUtil.sign(UserType.WEB, sysUser.getLoginName(), sysUser.getPassword());

        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }

    @Override
    public void logout() {
        //session.removeAttribute(Constant.CURRENT_USER_KEY);
    }

    private void setSessionObject(SessionUser user) {
//        session.setMaxInactiveInterval(maxInactiveInterval);
//        session.setAttribute(Constant.CURRENT_USER_KEY, user);
    }
}
