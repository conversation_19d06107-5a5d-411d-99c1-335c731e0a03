package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.IAppraiseService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP评价列表"
@RestController
@RequestMapping("/api/cus/appraise")
public class AppraiseController extends BaseController {
    @Resource
    private IAppraiseService appraiseService;

    @InvokeLog(description = "查询客商评价 接口") //日志
    @PostMapping(value = "/appraise_list")
    public ServerResponse<EGridResult> appraiseListHandler(
            @RequestParam(value = "type") Integer type,                     //"0-未评价,1-已评价,2-已投诉"
            @RequestParam(value = "page", required = false) Integer page,   //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows    //"每页多少条"
    ) throws GlobalException {
        return appraiseService.appraiseList2(type, page, rows);
    }

    @InvokeLog(description = "客商提交评价 接口") //日志
    @PostMapping(value = "/put_appraise")
    public ServerResponse<String> putAppraiseHandler(
            @RequestParam(value = "oid") String oid,                            //"单号"
            @RequestParam(value = "starsNum") Integer starsNum,                 //"服务评价(星级)"
            @RequestParam(value = "complain") boolean complain,                 //"是否投诉(是true,否false)"
            @RequestParam(value = "reasons", required = false) String reasons   //"投诉理由"
    ) throws GlobalException {
        return appraiseService.putAppraise(oid, starsNum, complain, reasons);
    }

}
