package com.erdos.coal.park.web.app.controller;


import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.service.ITradePriceService;
import com.erdos.coal.park.web.app.pojo.WebTradePriceData;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/trade")
public class WebTradeController extends BaseController {
    @Resource
    private ITradePriceService tradePriceService;

    @PostMapping("/price_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        String unitCode = request.getParameter("unitCode");
        if (StrUtil.isEmpty(unitCode)) return ServerResponse.createSuccess(new EGridResult());
        String unitCode_6 = unitCode.substring(4);
        Integer mold;
        if (StrUtil.isEmpty(request.getParameter("mold"))) {
            mold = 2;
        } else {
            mold = Integer.valueOf(request.getParameter("mold"));
        }

        return ServerResponse.createSuccess(tradePriceService.loadGrid(unitCode_6, mold, page, rows));
    }

    @PostMapping("/edit_trade_price")
    public ServerResponse editCarInfoHandler(@RequestBody WebTradePriceData webTradePriceData) throws GlobalException {
        return tradePriceService.editTradePrice(webTradePriceData);
    }
}
