package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.pojo.*;

import java.util.List;
import java.util.Map;

public interface ISynOrderService {

    //查询平台废除了的订单，返回给企业系统
    ServerResponse<List<OrderAggregate>> cancelOrderList(String unitCode);

    //查询平台订单部分信息
    ServerResponse<List<SynOrder>> searchOrderList(String objId, String startTime, String endTime, Integer rows);

    ServerResponse<List<SynOrder2>> searchOrderList2(String objId, String startTime, String endTime, Integer rows);

    //查询平台注册客商信息
    ServerResponse<List<SynCustomer>> searchCustomerUserList(String objId, String startTime, String endTime, Integer rows);

    //查询平台注册司机信息
    ServerResponse<List<SynDriverInfo>> searchDriverInoList(String objId, String startTime, String endTime, Integer rows);

    //查询平台注册车辆信息
    ServerResponse<List<SynCar>> searchCarList(String objId, String startTime, String endTime, Integer rows);

    //查询平台管理的单位信息
    ServerResponse<List<SynSysUnit>> searchSysUnitList(String objId, String startTime, String endTime, Integer rows);

    //****************************hdd*********************
    //查询订单车辆轨迹
    ServerResponse<String> getHistoricalTrack(String billCode, Integer bizType);

    //查询订单车辆过磅图片
    ServerResponse<Map<String, String>> getPhotoByBillCode(String billCode, Integer bizType);

}
