package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.entity.TradePrice;
import com.erdos.coal.park.web.app.pojo.WebTradePriceData;

import java.util.List;
import java.util.Map;

public interface ITradePriceService extends IBaseMongoService<TradePrice> {
    boolean add(List<TradePrice> tradePrices);

    List<Integer> edit(Map<Integer, TradePrice> updateTradePriceMap);

    boolean del(List<String> selfCodes);

    //
    EGridResult loadGrid(String unitCode, Integer mold, Integer page, Integer rows);

    ServerResponse editTradePrice(WebTradePriceData webTradePriceData);
}
