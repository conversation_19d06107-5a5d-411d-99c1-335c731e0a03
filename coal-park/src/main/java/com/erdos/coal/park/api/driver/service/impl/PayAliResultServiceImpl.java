package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.FundBillListEco;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.aliyuncs.utils.StringUtils;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.driver.dao.IPayAliResultDao;
import com.erdos.coal.park.api.driver.dao.IPayAliSResultDao;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import com.erdos.coal.park.api.driver.entity.PayAliPrepaid;
import com.erdos.coal.park.api.driver.entity.PayAliResult;
import com.erdos.coal.park.api.driver.entity.PayAliSResult;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IPayAliPrepaidService;
import com.erdos.coal.park.api.driver.service.IPayAliResultService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.transaction.alipay.bean.AlipayConfig;
import com.erdos.coal.transaction.alipay.service.AliPayService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("payAliResultService")
public class PayAliResultServiceImpl extends BaseMongoServiceImpl<PayAliResult, IPayAliResultDao> implements IPayAliResultService {

    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private IPayAliSResultDao payAliSResultDao;
    @Resource
    private MongoClient client;
    @Resource
    private AlipayConfig alipayConfig;
    @Resource
    private IPayAliPrepaidService payAliPrepaidService;
    @Resource
    private AliPayService aliPayService;
    @Resource
    private ILockedService lockedService;
    @Resource
    private IOrderService orderService;
    @Resource
    private ICustomerAccountService customerAccountService;

    @Override
    public PayAliPrepaid rsaCheckV1(Map<String, String> params) {
        String out_trade_no = params.get("out_trade_no");            // 商户订单号
        PayAliPrepaid prepaid = payAliPrepaidService.get("outTradeNo", out_trade_no);
        if (prepaid == null) return null;
        int type = prepaid.getType();

        String charset = params.get("charset");
        String signType = params.get("sign_type");

        //1.签名验证(对支付宝返回的数据验证，确定是支付宝返回的)
        boolean signVerified = false;
        if (type == 1) { //type=1 司机给自己充值 即司机黑金宝app
            try {
                //1.1调用SDK验证签名
                signVerified = AlipaySignature.rsaCheckV1(params, alipayConfig.ALIPAY_PUBLIC_KEY_DRIVER, charset, signType);
            } catch (AlipayApiException e) {
                e.printStackTrace();
            }
        } else if (type == 0 || type == 2 || type == 3) {   //0-客商给司机充值；；2-客商代付货运信息费；3-客商给自己充值 即客商黑金宝app
            try {
                //1.1调用SDK验证签名
                signVerified = AlipaySignature.rsaCheckV1(params, alipayConfig.ALIPAY_PUBLIC_KEY_CUSTOMER, charset, signType);
            } catch (AlipayApiException e) {
                e.printStackTrace();
            }
        } else {
            return null;
        }

        //2.对验签进行处理
        if (signVerified) {    //验签通过
            /*在签名验证通过后，必须严格按照如下的描述校验通知参数的合法性：
            1.商户需要验证该通知数据中的 out_trade_no 是否为商户系统中创建的订单号；
            2.判断 total_amount 是否确实为该订单的实际金额（即商户订单创建时的金额）；
            3.校验通知中的 seller_id（或者 seller_email) 是否为 out_trade_no 这笔单据对应的操作方（有的时候，一个商户可能有多个 seller_id/seller_email）；
            4.验证 app_id 是否为该商户本身。
            上述1、2、3、4有任何一个验证不通过，则表明同步校验结果是无效的，只有全部验证通过后，才可以认定买家付款成功
            */
            //String out_trade_no = params.get("out_trade_no");            // 商户订单号
            String total_amount = params.get("total_amount");
            String seller_id = params.get("seller_id");               //卖家支付宝用户号
            String app_id = params.get("app_id");
            //PayAliPrepaid prepaid = payAliPrepaidService.get("outTradeNo", out_trade_no);

            //if (ObjectUtil.isEmpty(prepaid)) return null;   //预下单不存在，校验失败
            if (StrUtil.isEmpty(total_amount) || new BigDecimal(prepaid.getTotalAmount()).compareTo(new BigDecimal(total_amount)) != 0)
                return null;   //预下单金额不一致，校验失败
            if (prepaid.getSellerId().equals(seller_id)) return null;   //卖家支付宝用户号和预下单不一致，校验失败
            if (prepaid.getAppId().equals(app_id)) return null;   //应用id不一致，校验失败

            return prepaid;
        } else {  //验签不通过
            return null;
        }
    }

    @Override
    @Transactional
    public String checkNotify(Map<String, String> params, String out_trade_no, String tradeStatus, PayAliPrepaid prepaid) {
        //用户重复提交加锁
        boolean lock = lockedService.getLock(prepaid.getMasterId(), SysConstants.LockType.ALI_PAY_RESULT.getType(), 0);
        if (!lock) {
            return "fail";
        } else {
            try {

                //排除主动查询支付宝后对交易结果的处理
                PayAliSResult payAliSResult = payAliSResultDao.get("out_trade_no", out_trade_no);
                if (payAliSResult != null && payAliSResult.getTrade_status().equals("TRADE_SUCCESS"))
                    return "success";

        /*
        交易状态说明
        WAIT_BUYER_PAY	交易创建，等待买家付款
        TRADE_CLOSED	未付款交易超时关闭，或支付完成后全额退款
        TRADE_SUCCESS	交易支付成功
        TRADE_FINISHED	交易结束，不可退款
        */
                if (tradeStatus.equals("TRADE_SUCCESS")) {    //支付成功的订单: 修改交易表状态,支付成功
                    //TODO: 这里做平台业务逻辑判断处理
                    String result;
                    switch (prepaid.getType()) {
                        case 0:
                            result = dataService(params, out_trade_no, prepaid, null);
                            break;
                        case 1:
                            result = dataService(params, out_trade_no, prepaid, null);
                            break;
                /*case 2:
                    result = dataService2(params, out_trade_no, prepaid, null);
                    break;*/
                        case 3:
                            result = dataService3(params, out_trade_no, prepaid, null);
                            break;
                        default:
                            result = "fail";
                            break;
                    }

                    return result;
                } else {
                    return "fail";
                }

            } finally {

                lockedService.unLock(prepaid.getMasterId(), SysConstants.LockType.ALI_PAY_RESULT.getType());
            }
        }
    }

    //客商给司机充值成功后业务数据处理 和 司机给自己充值成功后业务数据处理
    @Transactional
    String dataService(Map<String, String> params, String out_trade_no, PayAliPrepaid prepaid, AlipayTradeQueryResponse response) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> payAliPrepaidCollection = payAliPrepaidService.getCollection();
        MongoCollection<Document> payAliResultCollection = this.getCollection();
        MongoCollection<Document> payAliSResultCollection = payAliSResultDao.getCollection();
        MongoCollection<Document> driverAccountCollection = driverAccountService.getCollection();
        Date date = new Date();

        try {
            clientSession.startTransaction();

            //修改预支付记录的支付结果为“true”已支付
            Document prepaidFilter = new Document("outTradeNo", out_trade_no);
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            map.put("isPay", true);
            map.put("updateTime", date.getTime());
            upMap.put("$set", map);
            payAliPrepaidCollection.updateOne(clientSession, prepaidFilter, Document.parse(JSONObject.toJSONString(upMap)));

            //添加支付宝支付回调记录 或 请求支付宝支付记录
            if (response == null) {
                Document payAliResultDoc = Document.parse(JSONObject.toJSONString(addPayAliResult(params)));
                payAliResultDoc.append("updateTime", date.getTime());
                payAliResultDoc.append("createTime", date);
                payAliResultCollection.insertOne(clientSession, payAliResultDoc);
            } else {
                Document payAliSResultDoc = Document.parse(JSONObject.toJSONString(addPayAliSResult(response)));
                payAliSResultDoc.append("createTime", date);
                payAliSResultDoc.append("updateTime", date.getTime());
                payAliSResultCollection.insertOne(clientSession, payAliSResultDoc);
            }

            //支付金额 元计算单位 转为 分计算单位
            BigDecimal totalAmount = new BigDecimal(params.get("total_amount")).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            //添加司机账户信息
            DriverAccount dAccount = driverAccountService.get("outTradeNo", out_trade_no);
            if (ObjectUtil.isEmpty(dAccount)) {
                String transactionId = StrUtil.isEmpty(response) ? params.get("trade_no") : response.getTradeNo();
                Document dAccountDoc = driverAccountService.createDriAccountDoc(prepaid.getGuestId(), prepaid.getMasterId(), totalAmount, 1, out_trade_no, transactionId, date);
                driverAccountCollection.insertOne(clientSession, dAccountDoc);
            }

            clientSession.commitTransaction();
            return "success";
        } catch (Exception e) {
            clientSession.abortTransaction();
            //记录异常日志，用于开发调试
            logger.warn("支付宝交易异步通知，代码异常");
            logger.warn(e.toString());
            return "fail";
        } finally {
            clientSession.close();
        }
    }

    //客商代付货运信息费成功后业务数据处理
    @Transactional
    String dataService2(Map<String, String> params, String out_trade_no, PayAliPrepaid prepaid, AlipayTradeQueryResponse response) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> payAliPrepaidCollection = payAliPrepaidService.getCollection();
        MongoCollection<Document> payAliResultCollection = this.getCollection();
        MongoCollection<Document> payAliSResultCollection = payAliSResultDao.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        Date date = new Date();

        try {
            clientSession.startTransaction();

            //修改预支付记录的支付结果为“true”已支付
            Document prepaidFilter = new Document("outTradeNo", out_trade_no);
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            map.put("isPay", true);
            map.put("updateTime", date.getTime());
            upMap.put("$set", map);
            payAliPrepaidCollection.updateOne(clientSession, prepaidFilter, Document.parse(JSONObject.toJSONString(upMap)));

            //添加支付宝支付回调记录 或 请求支付宝支付记录
            if (response == null) {
                Document payAliResultDoc = Document.parse(JSONObject.toJSONString(addPayAliResult(params)));
                payAliResultDoc.append("createTime", date);
                payAliResultDoc.append("updateTime", date.getTime());
                payAliResultCollection.insertOne(clientSession, payAliResultDoc);
            } else {
                Document payAliSResultDoc = Document.parse(JSONObject.toJSONString(addPayAliSResult(response)));
                payAliSResultDoc.append("updateTime", date.getTime());
                payAliSResultDoc.append("createTime", date);
                payAliSResultCollection.insertOne(clientSession, payAliSResultDoc);
            }

            //支付金额 元计算单位 转为 分计算单位
            BigDecimal totalAmount = new BigDecimal(prepaid.getTotalAmount()).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            //order改为已经付费，修改
            /*Map<String, Object> map2 = new HashMap<>();
            map2.put("fees", totalAmount.divide(BigDecimal.valueOf(prepaid.getNumber()), 0, BigDecimal.ROUND_HALF_UP));
            map2.put("payerId", prepaid.getMasterId());
            map2.put("updateTime", date.getTime());
            Document orderFilter = new Document("gid", prepaid.getGuestId());
            Document upOptions2 = new Document("$set", map2);
            orderCollection.updateOne(clientSession, orderFilter, upOptions2);*/

            clientSession.commitTransaction();
            return "success";
        } catch (Exception e) {
            clientSession.abortTransaction();
            //记录异常日志，用于开发调试
            logger.warn("支付宝交易异步通知，代码异常");
            logger.warn(e.toString());
            return "fail";
        } finally {
            clientSession.close();
        }
    }

    //客商给自己充值成功后业务数据处理
    @Transactional
    String dataService3(Map<String, String> params, String out_trade_no, PayAliPrepaid prepaid, AlipayTradeQueryResponse response) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> payAliPrepaidCollection = payAliPrepaidService.getCollection();
        MongoCollection<Document> payAliResultCollection = this.getCollection();
        MongoCollection<Document> payAliSResultCollection = payAliSResultDao.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        Date date = new Date();

        try {
            clientSession.startTransaction();

            //修改预支付记录的支付结果为“true”已支付
            Document prepaidFilter = new Document("outTradeNo", out_trade_no);
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            map.put("isPay", true);
            map.put("updateTime", date.getTime());
            upMap.put("$set", map);
            payAliPrepaidCollection.updateOne(clientSession, prepaidFilter, Document.parse(JSONObject.toJSONString(upMap)));

            //添加支付宝支付回调记录 或 请求支付宝支付记录
            if (response == null) {
                Document payAliResultDoc = Document.parse(JSONObject.toJSONString(addPayAliResult(params)));
                payAliResultDoc.append("createTime", date);
                payAliResultDoc.append("updateTime", date.getTime());
                payAliResultCollection.insertOne(clientSession, payAliResultDoc);
            } else {
                Document payAliSResultDoc = Document.parse(JSONObject.toJSONString(addPayAliSResult(response)));
                payAliSResultDoc.append("createTime", date);
                payAliSResultDoc.append("updateTime", date.getTime());
                payAliSResultCollection.insertOne(clientSession, payAliSResultDoc);
            }

            //支付金额 元计算单位 转为 分计算单位
            BigDecimal totalAmount = new BigDecimal(prepaid.getTotalAmount()).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            //客商给自己充值,修改客商账户信息
            CustomerAccount cAccount = customerAccountService.get("outTradeNo", out_trade_no);
            if (ObjectUtil.isEmpty(cAccount)) {
                String transactionId = StrUtil.isEmpty(response) ? params.get("trade_no") : response.getTradeNo();
                Document dAccountDoc = driverAccountService.createDriAccountDoc(prepaid.getGuestId(), prepaid.getMasterId(), totalAmount, 2, out_trade_no, transactionId, date);
                cAccountCollection.insertOne(clientSession, dAccountDoc);
            }

            clientSession.commitTransaction();
            return "success";
        } catch (Exception e) {
            clientSession.abortTransaction();
            //记录异常日志，用于开发调试
            logger.warn("支付宝交易异步通知，代码异常");
            logger.warn(e.toString());
            return "fail";
        } finally {
            clientSession.close();
        }
    }

    private PayAliResult addPayAliResult(Map<String, String> params) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date notify_time = null;
        Date gmt_create = null;
        Date gmt_payment = null;
        Date gmt_refund = null;
        Date gmt_close = null;
        try {
            notify_time = sdf.parse(params.get("notify_time"));
            gmt_create = sdf.parse(params.get("gmt_create"));
            gmt_payment = sdf.parse(params.get("gmt_payment"));
            gmt_refund = sdf.parse(params.get("gmt_refund"));
            gmt_close = sdf.parse(params.get("gmt_close"));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        List<FundBillListEco> fundBillListEcoList = new ArrayList<>();
        String fund_bill_list = params.get("fund_bill_list");
        JSONArray jsonArray = JSONArray.parseArray(fund_bill_list);
        for (Object o : jsonArray) {
            String jsonStr = JSONObject.toJSONString(o);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            FundBillListEco fEco = new FundBillListEco();
            fEco.setAmount((String) jsonObject.get("amount"));
            fEco.setFundChannel((String) jsonObject.get("fundChannel"));
            fundBillListEcoList.add(fEco);
        }

        PayAliResult payAliResult = new PayAliResult();
        payAliResult.setCode(params.get("code"));
        payAliResult.setMsg(params.get("msg"));
        payAliResult.setNotify_time(notify_time);
        payAliResult.setNotify_type(params.get("notify_type"));
        payAliResult.setNotify_id(params.get("notify_id"));
        payAliResult.setApp_id(params.get("app_id"));
        payAliResult.setVersion(params.get("version"));
        payAliResult.setTrade_no(params.get("trade_no"));
        payAliResult.setOut_trade_no(params.get("out_trade_no"));
        payAliResult.setOut_biz_no(params.get("out_biz_no"));
        payAliResult.setBuyer_id(params.get("buyer_id"));
        payAliResult.setBuyer_logon_id(params.get("buyer_logon_id"));
        payAliResult.setSeller_id(params.get("seller_id"));
        payAliResult.setSeller_email(params.get("seller_email"));
        payAliResult.setTrade_status(params.get("trade_status"));
        payAliResult.setTotal_amount(params.get("total_amount"));
        payAliResult.setReceipt_amount(params.get("receipt_amount"));
        payAliResult.setInvoice_amount(params.get("invoice_amount"));
        payAliResult.setBuyer_pay_amount(params.get("buyer_pay_amount"));
        payAliResult.setPoint_amount(params.get("point_amount"));
        payAliResult.setRefund_fee(params.get("refund_fee"));
        payAliResult.setSubject(params.get("subject"));
        payAliResult.setBody(params.get("body"));
        payAliResult.setGmt_create(gmt_create);
        payAliResult.setGmt_payment(gmt_payment);
        payAliResult.setGmt_refund(gmt_refund);
        payAliResult.setGmt_close(gmt_close);
        payAliResult.setFund_bill_list(fundBillListEcoList);
        payAliResult.setPassback_params(params.get("passback_params"));
        //PayAliResult result = this.save(payAliResult);数据保存统一放在事务中处理了
        return payAliResult;
    }

    private PayAliSResult addPayAliSResult(AlipayTradeQueryResponse response) {
        PayAliSResult payAliSResult = new PayAliSResult();
        payAliSResult.setAlipay_sub_merchant_id(response.getAlipaySubMerchantId());
        payAliSResult.setAuth_trade_pay_mode(response.getAuthTradePayMode());
        payAliSResult.setBody(response.getBody());
        payAliSResult.setBuyer_logon_id(response.getBuyerLogonId());
        payAliSResult.setBuyer_pay_amount(response.getBuyerPayAmount());
        payAliSResult.setBuyer_user_id(response.getBuyerUserId());
        payAliSResult.setBuyer_user_name(response.getBuyerUserName());
        payAliSResult.setBuyer_user_type(response.getBuyerUserType());
        payAliSResult.setCharge_amount(response.getChargeAmount());
        payAliSResult.setCharge_flags(response.getChargeFlags());
        payAliSResult.setDiscount_amount(response.getDiscountAmount());
        payAliSResult.setExt_infos(response.getExtInfos());
        payAliSResult.setFund_bill_list(response.getFundBillList());
        payAliSResult.setInvoice_amount(response.getInvoiceAmount());
        payAliSResult.setMdiscount_amount(response.getMdiscountAmount());
        payAliSResult.setOut_trade_no(response.getOutTradeNo());
        payAliSResult.setPay_amount(response.getPayAmount());
        payAliSResult.setPay_currency(response.getPayCurrency());
        payAliSResult.setPoint_amount(response.getPointAmount());
        payAliSResult.setReceipt_amount(response.getReceiptAmount());
        payAliSResult.setSend_pay_date(response.getSendPayDate());
        payAliSResult.setSettle_amount(response.getSettleAmount());
        payAliSResult.setSettle_currency(response.getSettleCurrency());
        payAliSResult.setSettle_trans_rate(response.getSettleTransRate());
        payAliSResult.setSettlement_id(response.getSettlementId());
        payAliSResult.setStore_id(response.getStoreId());
        payAliSResult.setStore_name(response.getStoreName());
        payAliSResult.setSubject(response.getSubject());
        payAliSResult.setTerminal_id(response.getTerminalId());
        payAliSResult.setTotal_amount(response.getTotalAmount());
        payAliSResult.setTrade_no(response.getTradeNo());
        payAliSResult.setTrade_status(response.getTradeStatus());
        payAliSResult.setTrans_currency(response.getTransCurrency());
        payAliSResult.setTrans_pay_rate(response.getTransPayRate());
        return payAliSResult;
    }

    @Override
    public ServerResponse<String> driverSearchAliPayResult(String outTradeNo, String tradeNo) {
        String did = ShiroUtils.getUserId();

        //用户重复提交加锁
        boolean lock = lockedService.getLock(did, SysConstants.LockType.ALI_PAY_RESULT.getType());
        if (!lock) {
            ServerResponse<String> response = new ServerResponse<>(8);
            response.setMsg("系统忙，稍后重试！");
            return response;
        } else {
            try {
                //判断交易订单是否存在
                PayAliPrepaid prepaid = payAliPrepaidService.get("outTradeNo", outTradeNo);
                if (ObjectUtil.isEmpty(prepaid))
                    return ServerResponse.createError("交易不存在");

                //cusOrDri=1 表示司机黑金宝app
                ServerResponse<String> response = processAliPayInformation(did, outTradeNo, tradeNo, prepaid, 1);
                return response;

            } finally {
                lockedService.unLock(did, SysConstants.LockType.ALI_PAY_RESULT.getType());
            }
        }
    }

    //支付宝交易结果信息处理
    @Override
    @Transactional
    public ServerResponse<String> processAliPayInformation(String userCode, String outTradeNo, String tradeNo, PayAliPrepaid prepaid, Integer cusOrDri) {
        PayAliResult payAliResult;
        PayAliSResult payAliSResult;
        //如果支付宝订单号不为空，则优先使用tradeNo作为查询条件
        if (!StringUtils.isEmpty(tradeNo)) {
            payAliResult = this.get("trade_no", tradeNo);
            payAliSResult = payAliSResultDao.get("trade_no", tradeNo);
        } else {
            payAliResult = this.get("out_trade_no", outTradeNo);
            payAliSResult = payAliSResultDao.get("out_trade_no", outTradeNo);
        }

        //如果数据库中支付回调记录为空，则考虑支付宝异步通知存在延时
        if (ObjectUtil.isEmpty(payAliResult) && ObjectUtil.isEmpty(payAliSResult)) {
            AlipayTradeQueryResponse response = aliPayService.searchOrder(outTradeNo, tradeNo, cusOrDri);

            if (response == null) return ServerResponse.createError("订单号错误，交易不存在");
            if (!response.getCode().equals("10000")) return ServerResponse.createError(response.getSubMsg());

            if (response.getTradeStatus().equals("TRADE_SUCCESS")) {    //支付成功的订单: 修改交易表状态,支付成功

                if (StrUtil.isEmpty(response.getTotalAmount()) || new BigDecimal(response.getTotalAmount()).compareTo(new BigDecimal(prepaid.getTotalAmount())) != 0) {
                    logger.info(response.getTotalAmount());
                    return ServerResponse.createError("支付金额错误！");
                }

                //TODO: 这里做平台业务逻辑判断处理
                String result;
                switch (prepaid.getType()) {
                    case 0:
                        result = dataService(null, outTradeNo, prepaid, response);
                        break;
                    case 1:
                        result = dataService(null, outTradeNo, prepaid, response);
                        break;
                    /*case 2:
                        result = dataService2(null, outTradeNo, prepaid, response);
                        break;*/
                    case 3:
                        result = dataService3(null, outTradeNo, prepaid, response);
                        break;
                    default:
                        result = "fail";
                        logger.warn("订单号" + outTradeNo + "支付类型超出业务设定");
                        break;
                }

                if (result.equals("success")) {
                    return ServerResponse.createSuccess("交易成功");
                } else {
                    return ServerResponse.createError("查询异常，联系管理员");
                }
            } else {
                return ServerResponse.createError("支付未完成");
            }
        } else {
            if (payAliResult != null && payAliResult.getTrade_status().equals("TRADE_SUCCESS")) {
                return ServerResponse.createSuccess("交易成功");
            } else if (payAliSResult != null && payAliSResult.getTrade_status().equals("TRADE_SUCCESS")) {
                return ServerResponse.createSuccess("交易成功");
            } else {
                return ServerResponse.createSuccess("支付未完成");
            }
        }
    }

    @Override
    public ServerResponse<String> checkPayResult(String jsonParam) {
        Map map = JSON.parseObject(jsonParam, Map.class);

        Map contentMap = (Map) map.get("alipay_trade_app_pay_response");
        contentMap.put("sign", map.get("sign"));
        contentMap.put("sign_type", map.get("sign_type"));

        PayAliPrepaid prepaid = rsaCheckV1(contentMap);

        if (prepaid != null) {
            return ServerResponse.createSuccess("支付成功");
        } else {
            return ServerResponse.createError("支付异常");
        }
    }
}
