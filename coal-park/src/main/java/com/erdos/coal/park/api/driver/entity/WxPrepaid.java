package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.List;

@Entity(value = "t_pay_wx_prepaid", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("outTradeNo")}),
        @Index(fields = {@Field("createTime")})
})
//预订单
public class WxPrepaid extends BaseMongoInfo {

    private String appid;       //应用APPID
    private String mchId;       //商户id
    private String deviceInfo;  //设备号
    private String nonceStr;    //随机字符串
    private String sign;        //签名
    private String signType;    //签名类型
    private String body;        //商品描述
    private String detail;     //商品详情
    private String attach;      //附加数据

    //@Indexed(options = @IndexOptions(name = "_idx_out_trade_no", background = true))
    private String outTradeNo;  //商户订单号
    private String feeType;     //货币类型
    private Integer totalFee;    //总金额（分）
    private String spbillCreateIp;  //终端IP
    private String timeStart;   //交易起始时间
    private String timeExpire;  //交易结束时间
    private String goodsTag;    //订单优惠标记
    private String notifyUrl;  //通知地址
    private String tradeType;   //交易类型
    private String limitPay;    //指定支付方式
    private String receipt;     //开发票入口开放标识
    private String prepayId;    //预支付交易会话标识
    private boolean pay;       //是否支付成功
    // 充值后分账
    private String profitSharing;   //Y-需要分账，N或空-不分账
    private boolean separate;   //是否已执行分账
    private Integer frequencyLimitedLocked; //分账请求频率高锁

    //退款需要的字段
    private String transactionId;   //微信订单号
    private String outRefundNo; //商户退款单号
    private Integer refundFee;  //退款金额（分）
    private String refundDesc; //退款原因
    //————————————————————————

    private String cdid;        //充值人（司机或客商id)

    private String cid;     //被充值的客商编号

    private String did;         //司机id

    private String oid;         //订单号（小程序支付的订单号）
    private String carNum;         //车牌号
    private String driverId;       //车号对应的司机id
    private Integer shareFee;    //司机接单支付给客商或友商信息费（分）

    private String gid;     //代支付的货运信息编号
    private int number;     //商品数量（货运信息的车数）

    private Integer type = 0;   //类型    0：账户充值，1：订单付款，2：退款，3：提现， 4：分账, 5:司机账户充值后资金分账

    //企业付款到用户零钱需要的字段
    private String openid;  //用户openid
    private String checkName;   //校验用户姓名选项（NO_CHECK：不校验真实姓名 FORCE_CHECK：强校验真实姓名）
    private String reUserName;  //收款用户真实姓名
    private String desc;    //企业付款备注

    //订单有三方分账的情况，则司机直接微信付款接单
    private boolean overdue;            //司机放弃支付
    private List<String> billCodes;   //订单分账 三方账户关联单位编号
    private List<String> openids;       //订单分账 三方账户（个人账户）
    private List<String> merchantIds;   //订单分账 三方账户（商户账户）
    private String goodsId;             //司机完成支付后从次货运信息编号下分配订单给司机
    private String orderId;             //司机完成支付后分配给司机的订单号
    private String dvrId;               //进行微信支付接单的司机编号
    private String dvrCarNum;           //司机微信直接付款接单时车牌号
    private String dvrMobile;           //司机微信直接付款接单时车牌号
    private String dvrLon;              //司机微信直接付款接单时位置信息
    private String dvrLat;              //司机微信直接付款接单时位置信息
    private String cusUnorderedFee;     //司机接单时客商界面输入的收费金额

    //分账给第三方
    private String outOrderNo;          //商户系统内部的分账单号，商户系统内部唯一
    private String accountType;         //分账接收方类型。MERCHANT_ID：商户ID ；PERSONAL_OPENID：个人openid（由父商户APPID转换得到）
    private String account;             //分账接收方账号。类型是MERCHANT_ID时，是商户号；类型是PERSONAL_OPENID时，是个人openid
    private String name;                /*分账个人接收方姓名。分账接收方类型是MERCHANT_ID时，是商户全称（必传），当商户是小微商户或个体户时，是开户人姓名 分账接收方类型是PERSONAL_OPENID时，是个人姓名（选传，传则校验）
                                                                                    1、此字段需要加密，加密方法详见：敏感信息加密说明
                                                                                    2、使用微信支付平台证书中的公钥：获取平台证书
                                                                                    3、使用RSAES-OAEP算法进行加密
                                                                                    4、将请求中HTTP头部的Wechatpay-Serial设置为证书序列号*/
    private Integer amount;             //分账金额（分）
    private String description;         //分账描述
    private boolean unfreezeUnsplit;    //是否解冻剩余未分资金

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public Integer getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Integer totalFee) {
        this.totalFee = totalFee;
    }

    public String getSpbillCreateIp() {
        return spbillCreateIp;
    }

    public void setSpbillCreateIp(String spbillCreateIp) {
        this.spbillCreateIp = spbillCreateIp;
    }

    public String getTimeStart() {
        return timeStart;
    }

    public void setTimeStart(String timeStart) {
        this.timeStart = timeStart;
    }

    public String getTimeExpire() {
        return timeExpire;
    }

    public void setTimeExpire(String timeExpire) {
        this.timeExpire = timeExpire;
    }

    public String getGoodsTag() {
        return goodsTag;
    }

    public void setGoodsTag(String goodsTag) {
        this.goodsTag = goodsTag;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getLimitPay() {
        return limitPay;
    }

    public void setLimitPay(String limitPay) {
        this.limitPay = limitPay;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public String getPrepayId() {
        return prepayId;
    }

    public void setPrepayId(String prepayId) {
        this.prepayId = prepayId;
    }

    public boolean isPay() {
        return pay;
    }

    public void setPay(boolean pay) {
        this.pay = pay;
    }

    public String getProfitSharing() {
        return profitSharing;
    }

    public void setProfitSharing(String profitSharing) {
        this.profitSharing = profitSharing;
    }

    public boolean isSeparate() {
        return separate;
    }

    public void setSeparate(boolean separate) {
        this.separate = separate;
    }

    public Integer getFrequencyLimitedLocked() {
        return frequencyLimitedLocked;
    }

    public void setFrequencyLimitedLocked(Integer frequencyLimitedLocked) {
        this.frequencyLimitedLocked = frequencyLimitedLocked;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getCdid() {
        return cdid;
    }

    public void setCdid(String cdid) {
        this.cdid = cdid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public Integer getShareFee() {
        return shareFee;
    }

    public void setShareFee(Integer shareFee) {
        this.shareFee = shareFee;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public Integer getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Integer refundFee) {
        this.refundFee = refundFee;
    }

    public String getRefundDesc() {
        return refundDesc;
    }

    public void setRefundDesc(String refundDesc) {
        this.refundDesc = refundDesc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    public String getReUserName() {
        return reUserName;
    }

    public void setReUserName(String reUserName) {
        this.reUserName = reUserName;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isOverdue() {
        return overdue;
    }

    public void setOverdue(boolean overdue) {
        this.overdue = overdue;
    }

    public List<String> getBillCodes() {
        return billCodes;
    }

    public void setBillCodes(List<String> billCodes) {
        this.billCodes = billCodes;
    }

    public List<String> getOpenids() {
        return openids;
    }

    public void setOpenids(List<String> openids) {
        this.openids = openids;
    }

    public List<String> getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(List<String> merchantIds) {
        this.merchantIds = merchantIds;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getDvrId() {
        return dvrId;
    }

    public void setDvrId(String dvrId) {
        this.dvrId = dvrId;
    }

    public String getDvrCarNum() {
        return dvrCarNum;
    }

    public void setDvrCarNum(String dvrCarNum) {
        this.dvrCarNum = dvrCarNum;
    }

    public String getDvrMobile() {
        return dvrMobile;
    }

    public void setDvrMobile(String dvrMobile) {
        this.dvrMobile = dvrMobile;
    }

    public String getDvrLon() {
        return dvrLon;
    }

    public void setDvrLon(String dvrLon) {
        this.dvrLon = dvrLon;
    }

    public String getDvrLat() {
        return dvrLat;
    }

    public void setDvrLat(String dvrLat) {
        this.dvrLat = dvrLat;
    }

    public String getCusUnorderedFee() {
        return cusUnorderedFee;
    }

    public void setCusUnorderedFee(String cusUnorderedFee) {
        this.cusUnorderedFee = cusUnorderedFee;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isUnfreezeUnsplit() {
        return unfreezeUnsplit;
    }

    public void setUnfreezeUnsplit(boolean unfreezeUnsplit) {
        this.unfreezeUnsplit = unfreezeUnsplit;
    }
}
