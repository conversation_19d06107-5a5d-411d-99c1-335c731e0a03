package com.erdos.coal.park.web.sys.pojo;

import java.util.Date;

public class DefinedAddressData {
    private String id;
    private String addName;     //自定义名称
    private String fullName;    //地点全称

    private String coordinates;//经纬度
    private String copyCoordinates;//四舍五入经纬度

    private String cid;         //客商编号
    private String unitCode;    //企业单位对应后台维护的地址
    private Date createTime;
    private Long updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAddName() {
        return addName;
    }

    public void setAddName(String addName) {
        this.addName = addName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(String coordinates) {
        this.coordinates = coordinates;
    }

    public String getCopyCoordinates() {
        return copyCoordinates;
    }

    public void setCopyCoordinates(String copyCoordinates) {
        this.copyCoordinates = copyCoordinates;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
