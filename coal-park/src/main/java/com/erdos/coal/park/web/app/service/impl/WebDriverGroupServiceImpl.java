package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.customer.dao.ICustomerUserDao;
import com.erdos.coal.park.api.customer.dao.IDriverGroupDao;
import com.erdos.coal.park.api.customer.dao.IDriverPoolDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.DriverGroup;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.driver.dao.IDriverInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.web.app.pojo.WebDriverGroupData;
import com.erdos.coal.park.web.app.service.IWebDriverGroupService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service("webDriverGroupService")
public class WebDriverGroupServiceImpl extends BaseMongoServiceImpl<DriverGroup, IDriverGroupDao> implements IWebDriverGroupService {
    @Resource
    private ICustomerUserDao customerUserDao;
    @Resource
    private IDriverPoolDao driverPoolDao;
    @Resource
    private IDriverInfoDao driverInfoDao;
    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult group(Integer page, Integer rows) {
        Query<CustomerUser> query = customerUserDao.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("name")))
            query.filter("name", request.getParameter("name"));
        if (!StrUtil.isEmpty(request.getParameter("mobile")))
            query.filter("mobile", request.getParameter("mobile"));
//        query.order(Sort.ascending("mobile"));
//        List<CustomerUser> userList = query.find().toList();
        List<CustomerUser> userList = customerUserDao.list(query);
        List<String> cids = new ArrayList<>();
        for (CustomerUser user : userList) {
            cids.add(user.getObjectId().toHexString());
        }

        Query<DriverGroup> dgGroup = this.createQuery();
       /* if (userList.size() == 1)
            dgGroup.filter("cid", userList.get(0).getObjectId().toString());
        else if (userList.size() == 0)  //查不到无记录显示
            dgGroup.filter("cid", "");*/
        dgGroup.criteria("cid").in(cids);
        dgGroup.order(Sort.ascending("cid"));

        EGridResult<DriverGroup> eGridResult = this.findPage(page, rows, dgGroup);
        List<DriverGroup> driGroupList = eGridResult.getRows();
//        List<DriverGroup> driGroupList = this.list(dgGroup);

        List<WebDriverGroupData> driverGroupDataList = new ArrayList<>();
        for (DriverGroup group : driGroupList) {
            WebDriverGroupData driverGroupData = new WebDriverGroupData();
            driverGroupData.setId(group.getObjectId().toString());
            driverGroupData.setGroupName(group.getGroupName());
            driverGroupData.setGroupNo(group.getObjectId().toString());

            CustomerUser user = group.getCustomerUser();
            if (ObjectUtil.isNotEmpty(user)) {
                driverGroupData.setCusMobile(user.getMobile());
                driverGroupData.setCusName(user.getName());
            }

            driverGroupDataList.add(driverGroupData);
        }
        return new EGridResult(eGridResult.getTotal(), driverGroupDataList);

        /*int size = driverGroupDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(driverGroupDataList.size(), driverGroupDataList.subList(fromIndex, toIndex));*/
    }

    @Override
    public EGridResult driver(Integer page, Integer rows) {
        String id = request.getParameter("id");
        DriverGroup driverGroup = this.getByPK(id);

        List<WebDriverGroupData> driverGroupDataList = new ArrayList<>();

        CustomerUser user = driverGroup.getCustomerUser();

        String[] didArr = driverGroup.getDvrId();
        List<ObjectId> objectIds = new ArrayList<>();
        for (String did : didArr)
            objectIds.add(new ObjectId(did));

        Query<DriverInfo> driQuery = driverInfoDao.createQuery();

        driQuery.filter("objectId in ", objectIds);

        List<DriverInfo> driverInfoList = driQuery.find().toList();

        for (DriverInfo driverInfo : driverInfoList) {

            WebDriverGroupData driverGroupData = new WebDriverGroupData();
            driverGroupData.setDriverMobile(driverInfo.getMobile());
            driverGroupData.setDriverName(driverInfo.getName());
            driverGroupData.setCarNum(driverInfo.getCarNum());

            Query<DriverPool> query = driverPoolDao.createQuery();
            query.filter("customerUserID", user);
            query.filter("driverInfoID", driverInfo);
            DriverPool pool = driverPoolDao.get(query);
            if (pool != null)
                driverGroupData.setWhiteOrBlack(pool.getWhiteOrBlack());//从司机池中查询司机黑白名单

            driverGroupDataList.add(driverGroupData);
        }

        int size = driverGroupDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(driverGroupDataList.size(), driverGroupDataList.subList(fromIndex, toIndex));
    }

}
