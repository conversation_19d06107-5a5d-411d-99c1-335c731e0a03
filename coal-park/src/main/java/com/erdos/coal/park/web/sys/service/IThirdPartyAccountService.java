package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.ThirdPartyAccount;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface IThirdPartyAccountService extends IBaseMongoService<ThirdPartyAccount> {
    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
//    Document createAccountDoc(String id, String payerId, int fee, Integer type, String oid, Date time);
    Document createAccountDoc(String unitCode, String id, String name, String payerId, int fee, Integer type, String oid, Date time);
    Document createAccountDoc(SysUnit sysUnit, String payerId, int fee, Integer type, String oid, Date time);
    Document createAccountDoc(SysUnit sysUnit, String payerId, BigDecimal fee, Integer type, Date time, String preferentialRefundNo);

    //异步执行-向第三方账户微信赚钱
    void partyWXChange(String oid);

    //完善一级单位三方账户信息
    ServerResponse<String> editThirdParty(UnitData unit);

    //编辑三方账户信息前，需发送短信验证码
    ServerResponse<String> smsCode(UnitData unit);

    //查询三方账户金额
    ServerResponse<ThirdPartyAccount> getAccount();

    //三方账户申请提现
    ServerResponse<String> RefundPre(double amount, String name);

    //三方账户查询提现记录列表
    ServerResponse<List<RefundPreData>> queryRefundThirdPart();

    //三方账户查询交易明细
    ServerResponse<EGridResult> thirdPartAccountList(Integer page, Integer rows);
}
