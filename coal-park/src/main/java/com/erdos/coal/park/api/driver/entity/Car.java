package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.web.app.entity.CarInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_car", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")})
})
public class Car extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "_car_id", background = true))
    private String id;
    private String carNum;      //车牌
    private String engineNo;    //发动机号
    private String owner;       //所有人
    private String vin;         //车辆识别代号
    private String drivingPho1;      //行驶证照片（正页）
    private String drivingPho2;      //行驶证照片（副页正面）
    private String drivingPho3;      //行驶证照片（副页反面）
    private String carIdentityPhoBef;     //车主身份证照片正面
    private String carIdentityPhoBack;     //车主身份证照片背面
    private String roadTCPho;    //车辆运输许可证照片

    private CarInfo carInfo;    //车型信息

    private Integer verify;  //是否审核，0-未审核不可用，1-审核通过，2-审核未通过，3未审核可用，4停用
    private String verifyStr;

    private Double tareWeight;  // 车辆皮重

    public Car() {
    }

    public Car(Integer verify) {
        this.verify = verify;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getDrivingPho1() {
        return drivingPho1;
    }

    public void setDrivingPho1(String drivingPho1) {
        this.drivingPho1 = drivingPho1;
    }

    public String getDrivingPho2() {
        return drivingPho2;
    }

    public void setDrivingPho2(String drivingPho2) {
        this.drivingPho2 = drivingPho2;
    }

    public String getDrivingPho3() {
        return drivingPho3;
    }

    public void setDrivingPho3(String drivingPho3) {
        this.drivingPho3 = drivingPho3;
    }

    public String getCarIdentityPhoBef() {
        return carIdentityPhoBef;
    }

    public void setCarIdentityPhoBef(String carIdentityPhoBef) {
        this.carIdentityPhoBef = carIdentityPhoBef;
    }

    public String getCarIdentityPhoBack() {
        return carIdentityPhoBack;
    }

    public void setCarIdentityPhoBack(String carIdentityPhoBack) {
        this.carIdentityPhoBack = carIdentityPhoBack;
    }

    public String getRoadTCPho() {
        return roadTCPho;
    }

    public void setRoadTCPho(String roadTCPho) {
        this.roadTCPho = roadTCPho;
    }

    public CarInfo getCarInfo() {
        return carInfo;
    }

    public void setCarInfo(CarInfo carInfo) {
        this.carInfo = carInfo;
    }

    public Integer getVerify() {
        return verify;
    }

    public void setVerify(Integer verify) {
        this.verify = verify;
    }

    public String getVerifyStr() {
        return verifyStr;
    }

    public void setVerifyStr(String verifyStr) {
        this.verifyStr = verifyStr;
    }

    public Double getTareWeight() {
        return tareWeight;
    }

    public void setTareWeight(Double tareWeight) {
        this.tareWeight = tareWeight;
    }
}