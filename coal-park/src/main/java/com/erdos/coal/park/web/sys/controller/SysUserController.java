package com.erdos.coal.park.web.sys.controller;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.park.web.sys.service.ISysMenuService;
import com.erdos.coal.park.web.sys.service.ISysService;
import com.erdos.coal.park.web.sys.service.ISysUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/sys/user")
public class SysUserController extends BaseController {

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ISysService sysService;

    @Resource
    private ISysMenuService menuService;

    // for vue ------------------------------------------------------------

    @PostMapping("/user_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(sysUserService.loadUserGrid(page, rows));
    }

    @PostMapping(value = "/add_user")
    public ServerResponse addHandler(@RequestBody SysUser sysUser) throws GlobalException {
        return sysUserService.addUser(sysUser);
    }

    @PostMapping(value = "/edit_user")
    public ServerResponse editHandler(@RequestBody SysUser sysUser) throws GlobalException {
        return sysUserService.editUser(sysUser);
    }

    @PostMapping(value = "/del_user")
    public ServerResponse deleteHandler(@RequestBody SysUser sysUser) throws GlobalException {
        return sysUserService.deleteUser(sysUser);
    }

    // for vue ------------------------------------------------------------

    //--for vue---
    //登录
    @InvokeLog(name = "loginExHandler", description = "Web用户登录")
    @RequestMapping("/login")
    public ServerResponse<AccessToken> loginExHandler(@RequestBody JSONObject data) throws GlobalException {
        String username = data.getString("username");
        String password = data.getString("password");
        String code = data.getString("smsCode");
        String imgCode = data.getString("imgCode");
        return sysService.login2(username, password, code, imgCode);
        //return loginManageHandler.webUserLogin(username, password, code);
    }

    @RequestMapping("/info")
    public ServerResponse infoHandler(String token) throws GlobalException {
        return menuService.getLoginInfo();
    }

    //logout
    //登出
    @RequestMapping("/logout")
    public ServerResponse logout() throws Exception {
        sysService.logout();
        return ServerResponse.createSuccess("OK");
    }

    //---------------------------------------------
    @GetMapping("/test/list")
    public ServerResponse<EGridResult> testList(Integer page, Integer rows) throws Exception {

        //    id: '@id',
        //    display_time: '@datetime',
        //    title: '@sentence(10, 20)',
        //    'status|1': ['published', 'draft', 'deleted'],
        //    name: '@cname()',
        //    province: '@province()',
        //    city: '@city()',
        //    address: '@cword(10,20)',
        //    zip: '@integer(100000, 999999)'

        logger.info(page + "," + rows);

        EGridResult data = menuService.loadTreeGrid(page, rows);
        ServerResponse<EGridResult> result = ServerResponse.createSuccess();
        result.setData(data);
        return result;
    }

    // 文件 上传
    @RequestMapping("/test/upload_file")
    public String uploadHandler(MultipartFile file) throws GlobalException {
        if (file.isEmpty())
            return "file is null";

        logger.info("file_upload:" + file.getSize());
        return "file_upload";
    }
}
