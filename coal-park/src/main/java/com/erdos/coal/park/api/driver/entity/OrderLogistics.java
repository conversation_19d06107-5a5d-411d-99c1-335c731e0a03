package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;
import dev.morphia.geo.Geometry;
import dev.morphia.utils.IndexDirection;
import dev.morphia.utils.IndexType;

@Entity(value = "t_order_logistics", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("oid")}, options = @IndexOptions(unique = true)),
        @Index(fields = {@Field(value = "geometry",type = IndexType.GEO2DSPHERE)})
})
public class OrderLogistics extends BaseMongoInfo {

    //@Indexed(options = @IndexOptions(name = "_oid", unique = true, background = true))
    private String oid;     //订单号
    private String longitude;   //经度
    private String latitude;    //纬度
    private Integer finishTag;   //订单完成标识（1：运输中 2：完成）

    //@Indexed(value = IndexDirection.GEO2DSPHERE, name = "_geometry", background = true)
    private Geometry geometry;
    //点（Point）,线（LineString）,多边形（Polygon）,多点（MultiPoint）,多线（MultiLineString）,多个多边形（MultiPolygon）,几何集合（GeometryCollection）

    public Geometry getGeometry() {
        return geometry;
    }

    public void setGeometry(Geometry geometry) {
        this.geometry = geometry;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Integer getFinishTag() {
        return finishTag;
    }

    public void setFinishTag(Integer finishTag) {
        this.finishTag = finishTag;
    }
}
