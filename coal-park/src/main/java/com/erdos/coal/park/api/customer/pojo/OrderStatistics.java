package com.erdos.coal.park.api.customer.pojo;

import com.erdos.coal.utils.StrUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class OrderStatistics implements Serializable {

    private String cid;             //客商编号
    private Integer mold;           //物流类型
    private Integer tranStatus;     //物流类型
    private String tranDate;        //日期
    private Integer numberOfCars0;  //下单车数
    private Integer numberOfCars1;  //接单车数
    private Integer numberOfCars2;  //入场车数
    private Integer numberOfCars3;  //撤单车数
    private BigDecimal outNetWeight;    //发货完成净重
    private BigDecimal inNetWeight;     //收货完成净重
    private Integer numberOfCars4;  //完成车数

    private List<Integer> numberOfCars;  //车数
    private List<String> outGrossWeight;
    private List<String> outTareWeight;
    private List<String> inGrossWeight;
    private List<String> inTareWeight;

    public OrderStatistics() {
    }

    public OrderStatistics(Integer mold, Integer numberOfCars0, Integer numberOfCars1, Integer numberOfCars2, Integer numberOfCars3, BigDecimal outNetWeight, BigDecimal inNetWeight, Integer numberOfCars4) {
        this.mold = mold;
        this.numberOfCars0 = numberOfCars0;
        this.numberOfCars1 = numberOfCars1;
        this.numberOfCars2 = numberOfCars2;
        this.numberOfCars3 = numberOfCars3;
        this.outNetWeight = outNetWeight;
        this.inNetWeight = inNetWeight;
        this.numberOfCars4 = numberOfCars4;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public Integer getTranStatus() {
        return tranStatus;
    }

    public void setTranStatus(Integer tranStatus) {
        this.tranStatus = tranStatus;
    }

    public String getTranDate() {
        return tranDate;
    }

    public void setTranDate(String tranDate) {
        this.tranDate = tranDate;
    }

    public Integer getNumberOfCars0() {
        return numberOfCars0;
    }

    public void setNumberOfCars0(Integer numberOfCars0) {
        this.numberOfCars0 = numberOfCars0;
    }

    public Integer getNumberOfCars1() {
        return numberOfCars1;
    }

    public void setNumberOfCars1(Integer numberOfCars1) {
        this.numberOfCars1 = numberOfCars1;
    }

    public Integer getNumberOfCars2() {
        return numberOfCars2;
    }

    public void setNumberOfCars2(Integer numberOfCars2) {
        this.numberOfCars2 = numberOfCars2;
    }

    public Integer getNumberOfCars3() {
        return numberOfCars3;
    }

    public void setNumberOfCars3(Integer numberOfCars3) {
        this.numberOfCars3 = numberOfCars3;
    }

    public BigDecimal getOutNetWeight() {
        BigDecimal grossWeight = new BigDecimal(0.00);
        BigDecimal tareWeight = new BigDecimal(0.00);
        if (StrUtil.isNotEmpty(outGrossWeight)) {
            for (String weight : outGrossWeight) {
                if (StrUtil.isNotEmpty(weight))
                    grossWeight = grossWeight.add(new BigDecimal(weight));
            }
        }
        if (StrUtil.isNotEmpty(outTareWeight)) {
            for (String weight : outTareWeight) {
                if (StrUtil.isNotEmpty(weight))
                    tareWeight = tareWeight.add(new BigDecimal(weight));
            }
        }
        return grossWeight.subtract(tareWeight);
        //return outNetWeight;
    }

    public void setOutNetWeight(BigDecimal outNetWeight) {
        this.outNetWeight = outNetWeight;
    }

    public BigDecimal getInNetWeight() {
        BigDecimal grossWeight = new BigDecimal(0.00);
        BigDecimal tareWeight = new BigDecimal(0.00);
        if (StrUtil.isNotEmpty(inGrossWeight)) {
            for (String weight : inGrossWeight) {
                if (StrUtil.isNotEmpty(weight))
                    grossWeight = grossWeight.add(new BigDecimal(weight));
            }
        }
        if (StrUtil.isNotEmpty(inTareWeight)) {
            for (String weight : inTareWeight) {
                if (StrUtil.isNotEmpty(weight))
                    tareWeight = tareWeight.add(new BigDecimal(weight));
            }
        }
        return grossWeight.subtract(tareWeight);
        //return inNetWeight;
    }

    public void setInNetWeight(BigDecimal inNetWeight) {
        this.inNetWeight = inNetWeight;
    }

    public Integer getNumberOfCars4() {
        return numberOfCars4;
    }

    public void setNumberOfCars4(Integer numberOfCars4) {
        this.numberOfCars4 = numberOfCars4;
    }

    public List<Integer> getNumberOfCars() {
        return numberOfCars;
    }

    public void setNumberOfCars(List<Integer> numberOfCars) {
        this.numberOfCars = numberOfCars;
    }

    public List<String> getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(List<String> outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public List<String> getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(List<String> outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public List<String> getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(List<String> inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public List<String> getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(List<String> inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    @Override
    public String toString() {
        return "OrderStatistics{" +
                "cid='" + cid + '\'' +
                ", mold=" + mold +
                ", numberOfCars0=" + numberOfCars0 +
                ", numberOfCars1=" + numberOfCars1 +
                ", numberOfCars2=" + numberOfCars2 +
                ", numberOfCars3=" + numberOfCars3 +
                ", outNetWeight=" + outNetWeight +
                ", inNetWeight=" + inNetWeight +
                ", numberOfCars4=" + numberOfCars4 +
                '}';
    }
}
