package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.service.IWebMessageService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/message")
public class WebMessageController extends BaseController {

    @Resource
    private IWebMessageService webMessageService;

    @PostMapping("/sms/list")
    public ServerResponse<EGridResult> smsListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webMessageService.smsLoadGrid(page, rows));
    }
}
