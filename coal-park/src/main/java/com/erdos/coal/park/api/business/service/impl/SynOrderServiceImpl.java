package com.erdos.coal.park.api.business.service.impl;

import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.pojo.*;
import com.erdos.coal.park.api.business.service.IContractService;
import com.erdos.coal.park.api.business.service.ISynOrderService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.mongodb.BasicDBObject;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.aggregation.Projection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

@Service("synOrderService")
public class SynOrderServiceImpl implements ISynOrderService {

    @Resource
    private IUnitInfoService unitInfoService;
    @Resource
    private IGoodsService goodsService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private ICarDao carDao;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IContractService contractService;
    @Resource
    private IPhotoFileService photoFileService;

    /**
     * 查询平台废除了的订单，返回给企业系统
     */
    @Override
    public ServerResponse<List<OrderAggregate>> cancelOrderList(String unitCode) {
        //todo:一、查询删除了的订单。
        List<OrderAggregate> resultList = new ArrayList<>();

//        Date startTime = IdUnit.weeHours1(null, "00:00:00", 0);

        //当前日期 前推/后移 24小时
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR_OF_DAY, -24);
        Date startTime = calendar.getTime();    //修改为查询24小时内的作废订单

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("delete", true);
        oQuery.or(oQuery.criteria("outUnitCode").equal(Pattern.compile(unitCode + "$")),
                oQuery.criteria("inUnitCode").equal(Pattern.compile(unitCode + "$")));
//        oQuery.criteria("createTime").greaterThanOrEq(startTime);
        oQuery.criteria("time6").greaterThanOrEq(startTime.getTime());
        oQuery.order(Sort.ascending("cid"));
        List<Order> orderList = oQuery.find().toList();

        int size = 0;
        OrderAggregate orderAgg = null;
        String cid = "";
        String outMinMax = "";
        String inMinMax = "";
        for (Order order : orderList) {
            //当前cid不同于上次cid时，将统计的最大最小号拼接的字符串结束，加入orderAggregate对象，且对象加入list列表，当前对象结束。
            if (StrUtil.isNotEmpty(cid) && !cid.equals(order.getCid())) {
                orderAgg.setOutMinMax(outMinMax.equals("") ? "" : outMinMax.substring(0, outMinMax.length() - 1));
                orderAgg.setInMinMax(inMinMax.equals("") ? "" : inMinMax.substring(0, inMinMax.length() - 1));
                resultList.add(orderAgg);

                outMinMax = ""; //初始化字符串对象
                inMinMax = "";  //初始化字符串对象
            }
            //第一次进入循环 和 当前cid不同于上一次cid时，创建新的OrderAggregate对象
            if (!cid.equals(order.getCid())) {
                cid = order.getCid();
                Query<UnitInfo> queryUnitInfo = unitInfoService.createQuery();
                queryUnitInfo.filter("unitCode", Pattern.compile(unitCode + "$"));
                queryUnitInfo.filter("cid", cid);
                UnitInfo unitInfo = unitInfoService.get(queryUnitInfo);
                orderAgg = new OrderAggregate();
                if (unitInfo != null) orderAgg.setUserCode(unitInfo.getUserCode());
            }
            //将订单的最大最小号拼接到字符串中。
            if ((order.getOutMinMax() != null)) {
                outMinMax = outMinMax.concat(order.getOutMinMax());
                outMinMax = outMinMax.concat(",");
            }
            if ((order.getInMinMax() != null)) {
                inMinMax = inMinMax.concat(order.getInMinMax());
                inMinMax = inMinMax.concat(",");
            }

            //当到达最后一条数据时，结束对象orderAggregate，并加入list列表
            size++;
            if (size >= orderList.size()) {
                orderAgg.setOutMinMax(outMinMax.equals("") ? "" : outMinMax.substring(0, outMinMax.length() - 1));
                orderAgg.setInMinMax(inMinMax.equals("") ? "" : inMinMax.substring(0, inMinMax.length() - 1));
                resultList.add(orderAgg);
            }
        }

        /*for (UnitInfo unitInfo : unitInfos) {
            OrderAggregate orderAgg = new OrderAggregate();
            orderAgg.setUserCode(unitInfo.getUserCode());

            *//*Query<Goods> gQuery = goodsService.createQuery();
            gQuery.filter("cid", unitInfo.getCustomerUser().getObjectId().toString());
            gQuery.filter("createTime >=", startTime);
            List<Goods> goods = gQuery.find().toList();

            List<String> gids = new ArrayList<>();
            for (Goods g : goods) {
                gids.add(g.getGid());
            }*//*

            Query<Order> oQuery = orderService.createQuery();
//            oQuery.filter("gid in", gids.toArray());
            oQuery.filter("delete", true);
            oQuery.filter("cid", unitInfo.getCid());
            oQuery.criteria("createTime").greaterThanOrEq(startTime);
            List<Order> orderList = oQuery.find().toList();
            String outMinMax = "";
            String inMinMax = "";
            for (Order order : orderList) {
                if ((order.getOutMinMax() != null)) {
                    outMinMax = outMinMax.concat(order.getOutMinMax());
                    outMinMax = outMinMax.concat(",");
                }
                if ((order.getInMinMax() != null)) {
                    inMinMax = inMinMax.concat(order.getInMinMax());
                    inMinMax = inMinMax.concat(",");
                }
            }
            orderAgg.setOutMinMax(outMinMax.equals("") ? "" : outMinMax.substring(0, outMinMax.length() - 1));
            orderAgg.setInMinMax(inMinMax.equals("") ? "" : inMinMax.substring(0, inMinMax.length() - 1));

            resultList.add(orderAgg);
        }*/
        //todo:二、异步查询过期订单，并修改未删除状态
        return ServerResponse.createSuccess("查询成功", resultList);
    }

    //获取系统当前时间的 前一天00:00:00点到 后一天23:59:59点
    private String getSearchTime() {
        Long startTime = null;
        Long endTime = null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(System.currentTimeMillis()));//当前时间

        try {
            calendar.add(Calendar.DAY_OF_MONTH, -1);    //当前时间 -1 天
            String sTempTime = simpleDateFormat.format(calendar.getTime());
            sTempTime = sTempTime.substring(0, 10) + " 00:00:00";
            startTime = simpleDateFormat.parse(sTempTime).getTime();        //前一天00:00:00点

            calendar.add(Calendar.DAY_OF_MONTH, 2);     //前一天时间 +2 天
            String eTempTime = simpleDateFormat.format(calendar.getTime());
            eTempTime = eTempTime.substring(0, 10) + " 23:59:59";
            endTime = simpleDateFormat.parse(eTempTime).getTime();      //后一天23:59:59点
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return startTime + "-" + endTime;
    }

    //查询平台过期货运信息（72小时过期），删除过期货运信息
    @Async("busTaskExecutor")
    void searchOverdueGoods() {
        long time = IdUnit.weeHours(new Date().getTime(), "00:00:00", -3);

        //超过 72小时 未接单的订单，设为过期订单，主动删除。
        Query<Order> oQuery = orderService.createQuery();
        oQuery.or(
                oQuery.criteria("carNum").equal(""),
                oQuery.criteria("carNum").equal("null"),
                oQuery.criteria("carNum").equal(null)
        );
        oQuery.filter("updateTime <=", time);

        UpdateOperations<Order> oUp = orderService.createUpdateOperations();
        oUp.set("delete", true);

        orderService.update(oQuery, oUp);
    }

    @Override
    public ServerResponse<List<SynOrder>> searchOrderList(String objId, String startTime, String endTime, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<Order> query = orderService.createQuery();
        query.criteria("tranStatus").greaterThanOrEq(1);
        query.criteria("delete").equal(false);
        query.criteria("mold").equal(0);
        try {
            if (StrUtil.isNotEmpty(startTime)) query.criteria("createTime").greaterThanOrEq(sdf.parse(startTime));
            if (StrUtil.isNotEmpty(endTime)) query.criteria("createTime").lessThan(sdf.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (StrUtil.isNotEmpty(objId)) query.criteria("_id").greaterThan(new ObjectId(objId));
        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

//        pipeline.skip((page - 1) * rows);
        pipeline.limit(rows);

        Projection objIdProjection = Projection.projection(
                "objId",
                Projection.expression("$toString", "$_id")
        );
        Projection cidProjection = Projection.projection(
                "cid",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$cid").append("to", "objectId")
                )
        );
        Projection didProjection = Projection.projection(
                "did",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$did").append("to", "objectId")
                )
        );
        pipeline.project(
                objIdProjection,
                Projection.projection("oid"),
                Projection.projection("carNum"),
                Projection.projection("outUnitCode"),
                Projection.projection("outUnitName"),
                Projection.projection("outBizContractCode"),
                Projection.projection("outBizContractName"),
                Projection.projection("outSubName"),
                Projection.projection("outAreaName"),
                Projection.projection("outBillCode"),
                Projection.projection("outGrossWeight"),
                Projection.projection("outTareWeight"),
                Projection.projection("otherOutTime"),
                Projection.projection("outTradingUnitName"),
                Projection.projection("outWeight"),
                Projection.projection("outTradingUnit"),
                cidProjection,
                didProjection,
                Projection.projection("time1"),
                Projection.projection("time2"),
                Projection.projection("time3"),
                Projection.projection("outVariety")
        );

        pipeline.lookup("t_customer", "cid", "_id", "customerUser");
        pipeline.lookup("t_driver_info", "did", "_id", "driverInfo");

        Iterator<SynOrder> infos = pipeline.aggregate(SynOrder.class);

        List<SynOrder> resultList = new ArrayList<>();
        while (infos.hasNext()) {
            SynOrder synOrder = infos.next();
            resultList.add(synOrder);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<List<SynOrder2>> searchOrderList2(String objId, String startTime, String endTime, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<Order> query = orderService.createQuery();
        query.criteria("tranStatus").greaterThanOrEq(1);
        query.criteria("delete").equal(false);

        try {
            if (StrUtil.isNotEmpty(startTime)) query.criteria("createTime").greaterThanOrEq(sdf.parse(startTime));
            if (StrUtil.isNotEmpty(endTime)) query.criteria("createTime").lessThan(sdf.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (StrUtil.isNotEmpty(objId)) query.criteria("_id").greaterThan(new ObjectId(objId));
        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);
        pipeline.limit(rows);

        Projection objIdProjection = Projection.projection(
                "objId",
                Projection.expression("$toString", "$_id")
        );
        Projection cidProjection = Projection.projection(
                "cid",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$cid").append("to", "objectId")
                )
        );
        Projection didProjection = Projection.projection(
                "did",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$did").append("to", "objectId")
                )
        );
        pipeline.project(
                objIdProjection,
                Projection.projection("oid"),
                Projection.projection("carNum"),
                Projection.projection("outUnitCode"),
                Projection.projection("outUnitName"),
                Projection.projection("outBizContractCode"),
                Projection.projection("outBizContractName"),
                Projection.projection("outSubName"),
                Projection.projection("outAreaName"),
                Projection.projection("outBillCode"),
                Projection.projection("outGrossWeight"),
                Projection.projection("outTareWeight"),
                Projection.projection("otherOutTime"),
                Projection.projection("outTradingUnitName"),
                Projection.projection("outWeight"),
                Projection.projection("outTradingUnit"),
                cidProjection,
                didProjection,
                Projection.projection("time1"),
                Projection.projection("time2"),
                Projection.projection("time3"),
                Projection.projection("outVariety"),

                Projection.projection("inUnitCode"),
                Projection.projection("inUnitName"),
                Projection.projection("inBizContractCode"),
                Projection.projection("inBizContractName"),
                Projection.projection("inSubName"),
                Projection.projection("inAreaName"),
                Projection.projection("inBillCode"),
                Projection.projection("inGrossWeight"),
                Projection.projection("inTareWeight"),
                Projection.projection("otherInTime"),
                Projection.projection("inTradingUnitName"),
                Projection.projection("inWeight"),
                Projection.projection("inTradingUnit"),
                Projection.projection("time4"),
                Projection.projection("time5"),
                Projection.projection("inVariety"),
                Projection.projection("mold"),

                Projection.projection("createTime"),
                Projection.projection("updateTime")

        );

        pipeline.lookup("t_customer", "cid", "_id", "customerUser");
        pipeline.lookup("t_driver_info", "did", "_id", "driverInfo");

        Iterator<SynOrder2> infos = pipeline.aggregate(SynOrder2.class);

        List<SynOrder2> resultList = new ArrayList<>();
        while (infos.hasNext()) {
            SynOrder2 synOrder = infos.next();
            resultList.add(synOrder);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<List<SynCustomer>> searchCustomerUserList(String objId, String startTime, String endTime, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<CustomerUser> query = customerUserService.createQuery();
        if (StrUtil.isNotEmpty(objId)) query.criteria("_id").greaterThan(new ObjectId(objId));
        try {
            if (StrUtil.isNotEmpty(startTime)) query.criteria("createTime").greaterThanOrEq(sdf.parse(startTime));
            if (StrUtil.isNotEmpty(endTime)) query.criteria("createTime").lessThan(sdf.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        query.limit(rows);
        List<CustomerUser> list = query.find().toList();

        List<SynCustomer> resultList = new ArrayList<>();
        for (CustomerUser obj : list) {

            SynCustomer synCustomer = new SynCustomer();
            BeanUtils.copyProperties(obj, synCustomer);
            resultList.add(synCustomer);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<List<SynDriverInfo>> searchDriverInoList(String objId, String startTime, String endTime, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<DriverInfo> query = driverInfoService.createQuery();
        if (StrUtil.isNotEmpty(objId)) query.criteria("_id").greaterThan(new ObjectId(objId));
        try {
            if (StrUtil.isNotEmpty(startTime)) query.criteria("createTime").greaterThanOrEq(sdf.parse(startTime));
            if (StrUtil.isNotEmpty(endTime)) query.criteria("createTime").lessThan(sdf.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        query.limit(rows);
        List<DriverInfo> list = query.find().toList();

        List<SynDriverInfo> resultList = new ArrayList<>();
        for (DriverInfo obj : list) {

            SynDriverInfo synDriverInfo = new SynDriverInfo();
            BeanUtils.copyProperties(obj, synDriverInfo);
            resultList.add(synDriverInfo);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<List<SynCar>> searchCarList(String objId, String startTime, String endTime, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<Car> query = carDao.createQuery();
        if (StrUtil.isNotEmpty(objId)) query.criteria("_id").greaterThan(new ObjectId(objId));
        try {
            if (StrUtil.isNotEmpty(startTime)) query.criteria("createTime").greaterThanOrEq(sdf.parse(startTime));
            if (StrUtil.isNotEmpty(endTime)) query.criteria("createTime").lessThan(sdf.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        query.limit(rows);
        List<Car> list = query.find().toList();

        List<SynCar> resultList = new ArrayList<>();
        for (Car obj : list) {

            SynCar synCar = new SynCar();
            BeanUtils.copyProperties(obj, synCar);
            resultList.add(synCar);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<List<SynSysUnit>> searchSysUnitList(String objId, String startTime, String endTime, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<SysUnit> query = sysUnitService.createQuery();
        query.field("pCode").notEqual(null);
        query.field("pCode").notEqual("0010011001");
        if (StrUtil.isNotEmpty(objId)) query.criteria("_id").greaterThan(new ObjectId(objId));
        try {
            if (StrUtil.isNotEmpty(startTime)) query.criteria("createTime").greaterThanOrEq(sdf.parse(startTime));
            if (StrUtil.isNotEmpty(endTime)) query.criteria("createTime").lessThan(sdf.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        query.limit(rows);
        List<SysUnit> list = query.find().toList();

        List<SynSysUnit> resultList = new ArrayList<>();
        for (SysUnit obj : list) {

            SynSysUnit synSysUnit = new SynSysUnit();
            BeanUtils.copyProperties(obj, synSysUnit);
            resultList.add(synSysUnit);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<String> getHistoricalTrack(String billCode, Integer bizType) {
        Order order;
        Date endTimeDate = null;
        if (bizType == 5) {
            order = orderService.get("inBillCode", billCode);
            if (StrUtil.isNotEmpty(order.getTime5())) endTimeDate = order.getTime5();
        } else if (bizType == 6) {
            order = orderService.get("outBillCode", billCode);
            if (StrUtil.isNotEmpty(order.getTime3())) endTimeDate = order.getTime3();
        } else {
            return ServerResponse.createError("票据类型错误");
        }

        if ((StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutUnitCode().equals("0010011001")) || (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInUnitCode().equals("0010011001"))) {

            String carNum = order.getCarNum();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (StrUtil.isEmpty(order.getTime1())) return ServerResponse.createError("司机还未接单，无轨迹数据");
            String startTime = sdf.format(order.getTime1());

            if (StrUtil.isEmpty(endTimeDate)) endTimeDate = new Date(order.getUpdateTime());
            if (endTimeDate.equals(startTime)) endTimeDate = IdUnit.weeHours1(order.getUpdateTime(), "00:00:00", 1);

            String endTime = sdf.format(endTimeDate);
            String jsonStr = contractService.getHistoricalTrack(carNum, startTime, endTime);
            return ServerResponse.createSuccess("OK", jsonStr);
        } else {
            return ServerResponse.createError("服务暂未开启，敬请期待");
        }
    }

    @Override
    public ServerResponse<Map<String, String>> getPhotoByBillCode(String billCode, Integer bizType) {
        Map<String, String> result = new HashMap<>();
        result.put("pho1", photoFileService.signUrl("guobang1"));
        result.put("pho2", photoFileService.signUrl("guobang4"));
        return ServerResponse.createSuccess(result);
    }
}
