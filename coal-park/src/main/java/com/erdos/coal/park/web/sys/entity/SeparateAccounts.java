package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_separate_accounts", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("account")}),
        @Index(fields = {@Field("amount")}),
        @Index(fields = {@Field("createTime")})
})
public class SeparateAccounts extends BaseMongoInfo {
    private String oid;                         // 司机接单订单号
    private String did;                         // 司机编号
    private String dvrMobile;                   // 司机手机号
    private String outTradeNo;                  // 司机(接单/充值)支付时，商户订单号
    private String transactionId;               // 司机(接单/充值)支付时，微信订单号（这一笔支付，请求分账接口时，需要次参数）
    private String outOrderNo;                  // 商户系统内部的分账单号，商户系统内部唯一
    private String orderId;                     // 微信分账单号，微信分账唯一标识
    private String accountType;                 // 分账接收方类型。MERCHANT_ID：商户ID ；PERSONAL_OPENID：个人openid（由父商户APPID转换得到）
    private String account;                     // 分账接收方账号。类型是MERCHANT_ID时，是商户号；类型是PERSONAL_OPENID时，是个人openid
    private Integer amount;                     // 分账金额（分）（分账取30%且取整）
    private String detailId;                    // 分账明细单号

    private String result;                      // 分账结果
    private String failReason;                  // 分账失败原因

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getDvrMobile() {
        return dvrMobile;
    }

    public void setDvrMobile(String dvrMobile) {
        this.dvrMobile = dvrMobile;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }
}
