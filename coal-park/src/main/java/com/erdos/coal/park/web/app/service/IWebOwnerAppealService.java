package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.entity.OwnerAppeal;

public interface IWebOwnerAppealService extends IBaseMongoService<OwnerAppeal> {

    EGridResult loadGrid(Integer page, Integer rows);

    ServerResponse<OwnerAppeal> edit();

    ServerResponse saveCheck(OwnerAppeal ownerAppeal);
}
