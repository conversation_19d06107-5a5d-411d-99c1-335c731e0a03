package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.app.entity.HotLine;

public interface IHotLineService extends IBaseMongoService<HotLine> {
    // 热线电话列表（增删改）
    EGridResult loadHotLineGrid(Integer page, Integer rows);

    ServerResponse<String> deleteHotLine(HotLine hotLine);

    ServerResponse<String> addHotLine(HotLine hotLine);

    ServerResponse<String> editHotLine(HotLine hotLine);
}
