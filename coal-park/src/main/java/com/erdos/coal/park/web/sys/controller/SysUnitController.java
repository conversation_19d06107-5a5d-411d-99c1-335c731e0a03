package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SubUnitQuarantineDevice;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;
import com.erdos.coal.park.web.sys.pojo.PwdData;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import com.erdos.coal.park.web.sys.service.ISubUnitQuarantineDeviceService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.park.web.sys.service.IThirdPartyAccountService;
import dev.morphia.query.Query;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/web/sys/unit")
public class SysUnitController extends BaseController {

    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private ISubUnitQuarantineDeviceService subUnitQuarantineDeviceService;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    @RequestMapping("/unit_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows, Integer type) throws GlobalException {
        return ServerResponse.createSuccess(sysUnitService.loadGrid(page, rows, type));
    }

    @PostMapping("/del_unit")
    public ServerResponse<String> deleteHandler(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.deleteUnit(unit);
    }

    @PostMapping("/add_unit")
    public ServerResponse<String> addHandler(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.addUnit(unit);
    }

    @PostMapping("/edit_unit")
    public ServerResponse<String> editHandler(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.editUnit(unit);
    }

    @PostMapping("/add_unit2")
    public ServerResponse<String> add2Handler(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.addUnit2(unit);
    }

    @PostMapping("/edit_unit2")
    public ServerResponse<String> edit2Handler(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.editUnit2(unit);
    }

    @PostMapping("/save_geofence")
    public ServerResponse<String> addGeoFenceHandler(@RequestBody UnitData unit) throws GlobalException {
//        return sysUnitService.saveGeofenceUnit(unit);
        return sysUnitService.saveGeofenceUnit2(unit);
    }

    @PostMapping("/onOrOff_geofence")
    public ServerResponse<String> onOrOffGeoFenceHandler() throws GlobalException {
//        return sysUnitService.onOrOffGeofenceUnit();
        return sysUnitService.onOrOffGeofenceUnit2();
    }

    @PostMapping("/del_geofence")
    public ServerResponse<String> delGeoFenceHandler() throws GlobalException {
//        return sysUnitService.delGeofenceUnit();
        return sysUnitService.delGeofenceUnit2();
    }

    @PostMapping("/get_quarantine_info")
    public ServerResponse<List<CheckBoxData>> getQuarantineInfoHandler() throws GlobalException {
        return sysUnitService.getQuarantineInfo();
    }

    // 二级单位 防疫终端设备列表（增删改）
    @RequestMapping("/quarantine_device_list")
    public ServerResponse<EGridResult> quarantineDeviceListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(subUnitQuarantineDeviceService.loadQuarantineDeviceGrid(page, rows));
    }

    @PostMapping("/del_quarantine_device")
    public ServerResponse<String> deleteQuarantineDeviceHandler() throws GlobalException {
        return subUnitQuarantineDeviceService.deleteQuarantineDevice();
    }

    @PostMapping("/add_quarantine_device")
    public ServerResponse<String> addQuarantineDeviceHandler(@RequestBody SubUnitQuarantineDevice device) throws GlobalException {
        return subUnitQuarantineDeviceService.addQuarantineDevice(device);
    }

    @PostMapping("/edit_quarantine_device")
    public ServerResponse<String> editQuarantineDeviceHandler(@RequestBody SubUnitQuarantineDevice device) throws GlobalException {
        return subUnitQuarantineDeviceService.editQuarantineDevice(device);
    }

    @PostMapping("/get_sub_unit")
    public ServerResponse<List<SysUnit>> getSubUnitHandler(String subName) throws GlobalException {
        return sysUnitService.searchSubUnitByName(subName);
    }

    //-------------------三方账户接口
    @PostMapping("/edit_tp")
    public ServerResponse<String> editTPHandler(@RequestBody UnitData unit) throws GlobalException {
        return thirdPartyAccountService.editThirdParty(unit);
    }

    @PostMapping("/sms_code")
    public ServerResponse<String> smsCodeHandler(@RequestBody UnitData unit) throws GlobalException {
        return thirdPartyAccountService.smsCode(unit);
    }

    //一级单位修改用户名和密码
    @PostMapping("/edit_unit_pwd")
    public ServerResponse<String> editUnitPwdHandler(@RequestBody PwdData pwdData) throws GlobalException {
        return sysUnitService.editUNitPwd(pwdData);
    }

    //---------------------二级单位采掘区域
    @PostMapping("/save_sub_unit_excavate_area")
    public ServerResponse<String> addSubUnitExcavateAreaHandler(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.saveSubUnitExcavateArea(unit);
    }


    //所有一级单位查询 - 页面单位下拉框
    @PostMapping(value = "/select_unit")
    public ServerResponse<List<SysUnit>> selectUnitHandler() throws GlobalException {
        Query<SysUnit> query = sysUnitService.createQuery();
        query.or(
                query.criteria("pCode").equal(null),
                query.criteria("pCode").equal("")
        );
        return ServerResponse.createSuccess(sysUnitService.list(query));
    }

    // 一级单位 编辑 代收装卸费
    @PostMapping("/edit_unit_handling_cost")
    public ServerResponse<String> editUnitHandlingCost(@RequestBody UnitData unit) throws GlobalException {
        return sysUnitService.editUnitHandlingCost(unit);
    }
}
