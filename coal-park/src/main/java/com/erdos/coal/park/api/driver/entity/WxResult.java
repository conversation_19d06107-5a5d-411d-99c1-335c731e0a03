package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.driver.pojo.Receiver;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

import java.util.List;

@Entity(value = "t_pay_wx_result", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("transactionId")}),
        @Index(fields = {@Field("outTradeNo")})
})
//支付结果
public class WxResult extends BaseMongoInfo {

    private String returnCode;  //返回状态码
    private String returnMsg;   //返回信息
    private String deviceInfo;  //设备号
    private String nonceStr;    //随机字符串
    private String sign;        //签名
    private String resultCode;  //业务结果
    private String errCode;     //错误代码
    private String errCodeDes;  //错误代码描述
    private String openid;      //用户标识
    private String isSubscribe; //是否关注公众账号 Y-关注，N-未关注
    private String tradeType;   //交易类型
    private String bankType;    //付款银行
    private Integer totalFee = 0;      //总金额（分）
    private String feeType;     //货币种类
    private Integer cashFee = 0;       //现金支付金额（分）
    private String cashFeeType; //现金支付货币类型
    private Integer couponFee = 0;     //代金券金额
    private Integer couponCount = 0;   //代金券使用数量
    private String couponIdN;      //代金券id
    private Integer getCouponFeeN = 0;     //单个代金券支付金额

    //@Indexed(options = @IndexOptions(name = "_idx_transaction_id"))
    private String transactionId;   //微信支付订单号

    //@Indexed(options = @IndexOptions(name = "_idx_out_trade_no", background = true))
    private String outTradeNo;      //商户订单号
    private String attach;          //商家数据包
    private String timeEnd;         //支付完成时间

    //退款返回数据
    private String outRefundNo;     //商户退款单号
    private String refundId;    //微信退款单号
    private Integer refundFee;       //退款总金额(分)可以做部分退款
    private Integer settlementRefundFee; //应结退款金额  退款金额=申请退款金额-非充值代金券退款金额，退款金额<=申请退款金额
    private Integer settlementTotalFee; //应结订单金额   应结订单金额=订单金额-非充值代金券金额，应结订单金额<=订单金额
    private Integer cashRefundFee;      //现金退款金额（分）
    private String refundSuccessTime; //退款成功时间
    private String refundRecv;  //退款入账账户

    //企业付款到零钱返回数据
    private String paymentNo;   //微信付款单号
    private String paymentTime; //付款成功时间

    private String mchAppid;    //商户appid
    private String mchid;       //商户号

    //分账返回数据
    private String outOrderNo;
    private String orderId;     //微信分账单号，微信分账唯一标识
    private String state;
    private List<Receiver> receivers;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(String isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }


    public String getCashFeeType() {
        return cashFeeType;
    }

    public void setCashFeeType(String cashFeeType) {
        this.cashFeeType = cashFeeType;
    }

    public String getCouponIdN() {
        return couponIdN;
    }

    public void setCouponIdN(String couponIdN) {
        this.couponIdN = couponIdN;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(String timeEnd) {
        this.timeEnd = timeEnd;
    }

    public Integer getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Integer totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getCashFee() {
        return cashFee;
    }

    public void setCashFee(Integer cashFee) {
        this.cashFee = cashFee;
    }

    public Integer getCouponFee() {
        return couponFee;
    }

    public void setCouponFee(Integer couponFee) {
        this.couponFee = couponFee;
    }

    public Integer getCouponCount() {
        return couponCount;
    }

    public void setCouponCount(Integer couponCount) {
        this.couponCount = couponCount;
    }

    public Integer getGetCouponFeeN() {
        return getCouponFeeN;
    }

    public void setGetCouponFeeN(Integer getCouponFeeN) {
        this.getCouponFeeN = getCouponFeeN;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public Integer getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Integer refundFee) {
        this.refundFee = refundFee;
    }

    public Integer getSettlementRefundFee() {
        return settlementRefundFee;
    }

    public void setSettlementRefundFee(Integer settlementRefundFee) {
        this.settlementRefundFee = settlementRefundFee;
    }

    public Integer getSettlementTotalFee() {
        return settlementTotalFee;
    }

    public void setSettlementTotalFee(Integer settlementTotalFee) {
        this.settlementTotalFee = settlementTotalFee;
    }

    public Integer getCashRefundFee() {
        return cashRefundFee;
    }

    public void setCashRefundFee(Integer cashRefundFee) {
        this.cashRefundFee = cashRefundFee;
    }

    public String getRefundSuccessTime() {
        return refundSuccessTime;
    }

    public void setRefundSuccessTime(String refundSuccessTime) {
        this.refundSuccessTime = refundSuccessTime;
    }

    public String getRefundRecv() {
        return refundRecv;
    }

    public void setRefundRecv(String refundRecv) {
        this.refundRecv = refundRecv;
    }

    public String getPaymentNo() {
        return paymentNo;
    }

    public void setPaymentNo(String paymentNo) {
        this.paymentNo = paymentNo;
    }

    public String getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(String paymentTime) {
        this.paymentTime = paymentTime;
    }

    public String getMchAppid() {
        return mchAppid;
    }

    public void setMchAppid(String mchAppid) {
        this.mchAppid = mchAppid;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public List<Receiver> getReceivers() {
        return receivers;
    }

    public void setReceivers(List<Receiver> receivers) {
        this.receivers = receivers;
    }
}
