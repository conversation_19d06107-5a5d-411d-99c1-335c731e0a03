package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.entity.BusGoods;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.manage.entity.AppLog;

import java.util.List;

public interface IBusGoodsService extends IBaseMongoService<BusGoods> {

    // 企业系统线下下单（关联客商）
    ServerResponse<String> busPushGoods(String userCode, Integer min, Integer max, String carNum, String dvrMobile, Integer carType, BusGoods goods, String transportPlace);
    ServerResponse<String> busPushGoods2(String userCode, Integer min, Integer max, String carNum, String dvrMobile, Integer carType, BusGoods goods,String transportPlace);
    ServerResponse<String> busPushGoods3(String userCode, Integer min, Integer max, String carNum, String dvrMobile, Integer carType, BusGoods goods,String transportPlace);

    // 企业系统线下订单修改
    ServerResponse<List<String>> updateBusGoods(String startCode, String endCode, Integer billType, String variety, String billCodeList, String transportPlace);

    // 企业系统线下订单删除
    ServerResponse<List<String>> delBusGoods(String startCode, String endCode, Integer billType, String billCodeList);

    //客商查询企业订单
    ServerResponse<EGridResult> searchBusGoods(String unitCode, Integer mold, String busGid, Integer type, Integer rows, Integer page);
    ServerResponse<EGridResult> searchBusGoods2(String unitCode, Integer mold, String busGid, Integer type, Integer rows, Integer page);

    //客商拆分企业单使用（客商下单）
    ServerResponse<Object> useBusGoods(String busGid, Goods goods, String cusDesc);
    ServerResponse<Object> useBusGoods2(String busGid, Goods goods, String cusDesc);
    ServerResponse<Object> useBusGoods3(String busGid, Goods goods, String cusDesc);
    ServerResponse<Object> useBusGoods4(String busGid, Goods goods, String cusDesc, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime);

    ServerResponse<EGridResult> getBusGoods(String busGid, Integer type, Integer page, Integer rows);

    ServerResponse<String> busAddGoods(Integer min, Integer max, BusGoods goods);
}
