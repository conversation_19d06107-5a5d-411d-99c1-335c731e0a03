package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

//客商账户  金额单位 为 分
@Entity(value = "t_customer_account", noClassnameStored = true)
public class CustomerAccount extends BaseMongoInfo {
    private String cid;     //客商id
    private BigDecimal totalFee = new BigDecimal("0");   //金额(分)
    /*
     * type:
     * 1.客商给自己账号充值（微信）
     * 2.客商给自己账号充值（支付宝）
     * 3.客商账户代付货运信息费扣款
     * 4.客商多代付的订单退款
     * 5.客商已代付的订单退款
     * 6.平台代客商 代扣 司机的服务费（司机接单时需要支付给客商的钱）
     * 7.客商账户提现到微信
     * 8.客商账户提现到支付宝
     * 9.客商退回司机信息费
     * */
    private Integer type;   //账户明细类型

    private String outTradeNo;  //平台 与 微信或支付宝 交易时 平台保存的唯一商户订单号
    private String oid; //订单号

    private String transactionId;//微信订单号/支付宝订单号

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String cidAndCreateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //格式化规则
        Date date = getCreateTime();//获得你要处理的时间 Date型
        String strDate = sdf.format(date); //格式化成yyyy-MM-dd格式的时间字符串

        return getCid() + "_" + strDate;
    }
}
