package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Appraise;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.web.app.pojo.OrderData;

public interface IWebOrderService extends IBaseMongoService<Order> {

    //type:0 显示所有订单 type:1 显示抢单信息
    EGridResult listGoods(Integer page, Integer rows, String type);

    //订单列表
    EGridResult listOrders(Integer page, Integer rows);

    //订单评价列表
    EGridResult appraiseList(Integer page, Integer rows);

    //订单评价处理结果保存
    ServerResponse<String> appraiseSave(Appraise appraise);

    //撤销订单需退款的审核列表
    EGridResult checkOrders(Integer page, Integer rows);

    //审核撤销订单并退回支付金额,客商付款的金额全部退回账户，司机app-退回账户，司机小程序-退回微信零钱
    ServerResponse<String> checkSave(OrderData order);

    //微信小程序查询退款进度
    EGridResult refundQuery(Integer page, Integer rows);

    ServerResponse<EGridResult> getOrders(Integer page, Integer rows);
}
