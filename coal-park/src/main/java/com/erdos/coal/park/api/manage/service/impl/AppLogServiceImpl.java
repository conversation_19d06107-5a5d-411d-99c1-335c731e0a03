package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.dao.IAppLogDao;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.api.manage.pojo.AppLogData;
import com.erdos.coal.park.api.manage.service.IAppLogService;
import dev.morphia.query.FindOptions;
import dev.morphia.query.Query;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("appLogService")
public class AppLogServiceImpl extends BaseMongoServiceImpl<AppLog, IAppLogDao> implements IAppLogService {

    @Override
    @Async("logTaskExecutor") //异步处理日志
    public void writeLog(AppLog appLog) {
        // try {
        //     Thread.sleep(100000);
        // } catch (InterruptedException e) {
        //     e.printStackTrace();
        // }
        this.save(appLog);
    }

    /**
     * 查询慢数据(大于1秒的)
     *
     * @param page 第几页
     * @param st   时间
     * @return
     */
    @Override
    public ServerResponse getGreaterThanOneSecondList(Integer page, String st) {
        Query<AppLog> query = this.createQuery();
        query.field("consumes").greaterThanOrEq(1000);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date;
        try {
            if (StringUtils.isEmpty(st))
                throw new ParseException("日期格式错误", 0);
            date = format.parse(st); // "2022-05-08 11:59:49"
            query.field("createTime").greaterThanOrEq(date);

            //TODO: 总数
            long count = query.count();

            int pages = page == null ? 1 : page;
            int size = 1000; //rows == null ? (int) count : rows;

            int currPage = Math.max(pages, 1); //最小为1

            //TODO: 分页
            FindOptions findOptions = new FindOptions();
            findOptions.skip((currPage - 1) * size);//起始位置
            findOptions.limit(size);//查询条数

            //TODO: 结果
            List<AppLog> list = query.find(findOptions).toList();
            List<AppLogData> result = new ArrayList<>();

            // AppLog ==>> AppLogData
            list.forEach(v -> {
                AppLogData data = new AppLogData();
                BeanUtils.copyProperties(v, data);
                data.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(v.getCreateTime()));
                result.add(data);
            });

            return ServerResponse.createSuccess(new EGridResult<>(count, result));
        } catch (ParseException e) {
            return ServerResponse.createSuccess("日期格式错误: st 参数必须");
        } catch (Exception e) {
            return ServerResponse.createSuccess("查询异常:" + e.getMessage());
        }
    }

    @Override
    public List<AppLog> getAppLogList(Query<AppLog> query) {
        FindOptions findOptions = new FindOptions();
        findOptions.limit(100); // 限制 100
        return this.list(query, findOptions);
    }
}