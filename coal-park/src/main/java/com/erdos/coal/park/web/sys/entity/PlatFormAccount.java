package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Entity(value = "t_platform_account", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("preferentialRefundNo")}, options = @IndexOptions(unique = true, partialFilter = "{preferentialRefundNo:{$exists:true}}"))
})
public class PlatFormAccount extends BaseMongoInfo {
    private String payerId;
    private BigDecimal totalFee = new BigDecimal("0");   //金额(分)
    /**
     * 类型
     * 0：充值 - 单位充值
     * 1：充值 - 客商充值 - 微信
     * 2：充值 - 客商充值 - 支付宝
     * 3：充值 - 司机充值 - 微信
     * 4：充值 - 司机充值 - 支付宝
     * 5：下单 - 单位代付信息费
     * 6：下单 - 客商代付信息费
     * 7：接单 - 司机支付信息费
     * 8：接单 - 司机微信直接接单
     * 9：接单 - 退回单位多代付的信息费
     * 10：接单 - 退回客商多代付的信息费
     * 11：废单 - 订单作废退回单位代付信息费
     * 12：废单 - 订单作废退回客商代付信息费
     * 13：废单 - 订单作废退回司机支付信息费
     * 14：提现 - 客商提现到 - 微信零钱
     * 15：提现 - 客商提现到 - 支付宝零钱
     * 16：提现 - 司机提现到 - 微信零钱
     * 17：提现 - 司机提现到 - 支付宝零钱
     * 18: 返款 - 同一司机同一车号在二级单位当天付费车数大于短盘车数时，超过收费车数的费用优惠返款
     */
    private Integer type;   //类型
    private String oid;     //扣款订单号
    private String outTradeNo;  //平台 与 微信或支付宝 交易时 平台保存的唯一商户订单号

    private String preferentialRefundNo;    //优惠返款唯一订单号 由二级单位编号+日期+司机id+carNum组成

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String createDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //格式化规则
        Date date = getCreateTime();//获得你要处理的时间 Date型
        String strDate = sdf.format(date); //格式化成yyyy-MM-dd格式的时间字符串

        return strDate;
    }

    public String getPreferentialRefundNo() {
        return preferentialRefundNo;
    }

    public void setPreferentialRefundNo(String preferentialRefundNo) {
        this.preferentialRefundNo = preferentialRefundNo;
    }
}
