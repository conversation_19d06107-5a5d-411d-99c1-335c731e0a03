package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;
import com.erdos.coal.park.web.sys.service.ISysSwitchService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/web/sys/switch")
public class SysSwitchController extends BaseController {

    @Resource
    private ISysSwitchService sysSwitchService;

    @RequestMapping("/switch_list")
    public ServerResponse<EGridResult> listHand<PERSON>(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(sysSwitchService.loadGrid(page, rows));
    }

    @PostMapping("/del_switch")
    public ServerResponse deleteHandler(@RequestBody SysSwitch sysSwitch) throws GlobalException {
        return sysSwitchService.deleteSwitch(sysSwitch);
    }

    @PostMapping("/add_switch")
    public ServerResponse addHandler(@RequestBody SysSwitch sysSwitch) throws GlobalException {
        return sysSwitchService.addSwitch(sysSwitch);
    }

    @PostMapping("/get_dvr_pho_types")
    public ServerResponse<List<CheckBoxData>> getDvrPhoTypesHandler() throws GlobalException {
        return sysSwitchService.getDvrPhoTypes();
    }

    @PostMapping("/get_car_pho_types")
    public ServerResponse<List<CheckBoxData>> getCarPhoTypesHandler() throws GlobalException {
        return sysSwitchService.getCarPhoTypes();
    }
}
