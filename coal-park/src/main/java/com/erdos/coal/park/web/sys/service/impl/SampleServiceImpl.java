package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.entity.RestClassInfo;
import com.erdos.coal.core.utils.ReflectHelper;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.api.manage.service.IAppLogService;
import com.erdos.coal.park.web.sys.dao.ISampleDao;
import com.erdos.coal.park.web.sys.entity.Sample;
import com.erdos.coal.park.web.sys.service.ISampleService;
import dev.morphia.query.FindOptions;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class SampleServiceImpl extends BaseMongoServiceImpl<Sample, ISampleDao> implements ISampleService {

    @Resource
    private IAppLogService appLogService;

    // 大数据量分页查询优化
    // 1, 排序字段(Sort) 需建立合理的索引.
    // 2, 不要使用 skip, skip 会造成全集合扫描.
    // 3, 使用上（下）一页的数据做条件查询下（上）一页数据.

    //测试数据:
    //// 删除所有数据
    ////db.test.remove({});
    //
    //var total = db.test.count();
    //
    //// 添加数据
    //var prefix = '中华人民共和国,中华人民共和国';
    //for (var i = total + 1; i <= total + 10000; i++) {
    //    db.test.insert({
    //				"id": i, //id
    //
    //				"field1": i * 1, //Integer
    //				"field2": i / 100, //Double
    //				"field3": new Date(), //时间
    //				"field4": (i % 2) == 0, // Boolean 值
    //				"field5": new Date().getTime(), //Timestamp
    //			  "field6": [i, i+1, i+2, i+3], //Array
    //				"field7": ['aa' + i, 'bb' + i, 'cc' + i], //Array String
    //				"field8": { "f1": i, "f2": i + 1, "f3": i + 2 }, //Document
    //        "field9": prefix + Math.round(Math.random() * 100), //汉字
    //				"field10": null, // 空对象
    //
    //				"field11": i * 2, //Integer
    //				"field12": i / 100, //Double
    //				"field13": new Date(), //时间
    //				"field14": (i % 3) == 0, // Boolean 值
    //				"field15": new Date().getTime(), //Timestamp
    //			  "field16": [i, i+1, i+2, i+3], //Array
    //				"field17": ['aa' + i, 'bb' + i, 'cc' + i], //Array String
    //				"field18": { "f1": i, "f2": i + 1, "f3": i + 2 }, //Document
    //        "field19": prefix + Math.round(Math.random() * 100), //汉字
    //				"field20": null, // 空对象
    //
    //				"field21": i * 3, //Integer
    //				"field22": i / 100, //Double
    //				"field23": new Date(), //时间
    //				"field24": (i % 4) == 0, // Boolean 值
    //				"field25": new Date().getTime(), //Timestamp
    //			  "field26": [i, i+1, i+2, i+3], //Array
    //				"field27": ['aa' + i, 'bb' + i, 'cc' + i], //Array String
    //				"field28": { "f1": i, "f2": i + 1, "f3": i + 2 }, //Document
    //        "field29": prefix + Math.round(Math.random() * 100), //汉字
    //				"field30": null, // 空对象
    //
    //				"field31": i * 4, //Integer
    //				"field32": i / 100, //Double
    //				"field33": new Date(), //时间
    //				"field34": (i % 5) == 0, // Boolean 值
    //				"field35": new Date().getTime(), //Timestamp
    //			  "field36": [i, i+1, i+2, i+3], //Array
    //				"field37": ['aa' + i, 'bb' + i, 'cc' + i], //Array String
    //				"field38": { "f1": i, "f2": i + 1, "f3": i + 2 }, //Document
    //        "field39": prefix + Math.round(Math.random() * 100), //汉字
    //				"field40": null // 空对象
    //    })
    //}
    //
    //db.test.count();
    //
    //// navicat 结果 ---------------------------------------------------------------
    ////
    //// Intel(R) Core(TM) i7-6820HQ CPU @ 2.70GHz   32GB 内存
    ////
    //// 单个文档 1.25 KB (1,281)
    ////
    //// 默认 只有 ObjectId 一个索引
    ////
    //// 创建索引
    //// db.test.createIndex({id: 1, isActive: 1}, {unique: true});
    //// db.test.getIndexes();
    ////
    //// *** 资源占用情况 ***
    ////=============================================================================
    ////	文档数:        内存占用:    存储空间:    索引 (1个, 2个):        插入时间:
    ////=============================================================================
    ////   10000         12.05 MB      2.54 MB     104.00 KB, 244.00 KB          10S
    ////-----------------------------------------------------------------------------
    ////   20000         24.22 MB      5.13 MB     228.00 KB, 516.00 KB          20S
    ////-----------------------------------------------------------------------------
    ////   50000         60.75 MB     12.78 MB     488.00 KB, 1.11 MB            52S
    ////-----------------------------------------------------------------------------
    ////  100000        121.62 MB     25.57 MB     916.00 KB, 2.19 MB           104S
    ////-----------------------------------------------------------------------------
    ////  200000        244.52 MB     51.31 MB       1.76 MB, 4.34 MB           210S
    ////-----------------------------------------------------------------------------
    ////  300000        367.41 MB     77.05 MB       2.66 MB, 6.57 MB           350S
    ////-----------------------------------------------------------------------------
    ////  500000        613.20 MB    128.58 MB       4.59 MB, 123.00 MB         525S
    ////-----------------------------------------------------------------------------
    //// 1000000          1.20 GB    257.36 MB       9.47 MB, 219.9 MB         未记录
    ////-----------------------------------------------------------------------------
    //// 5000000          6.04 GB      1.27 GB      48.29 MB, 117.14 MB        5325S
    ////=============================================================================

    @Override
    public EGridResult slow(Integer page, Integer rows) {
        long start = System.currentTimeMillis();

        Query<Sample> query = this.createQuery();
        // query.order(Sort.ascending("id"), Sort.descending("field2"));
        query.order(Sort.ascending("id"));
        EGridResult<Sample> result = this.findPage(page, rows, query);

        logger.warn("SamplePage 分页查询. 用时{}", System.currentTimeMillis() - start);
        return result;
    }

    @Override
    public EGridResult<Sample> fast(Integer page, Integer rows) {
        long start = System.currentTimeMillis();

        Query<Sample> query = this.createQuery();

        //TODO: 总数
        long count = query.count();
        int pages = page == null ? 1 : page;
        int size = rows == null ? (int) count : rows;
        int currPage = Math.max(pages, 1); //最小为1

        query.and(
                query.criteria("id").greaterThan((currPage - 1) * size) // 条件
        );

        query.order(Sort.ascending("id"));
        //return this.findPage(page, rows, query);


        //TODO: 分页
        FindOptions findOptions = new FindOptions();
        //findOptions.skip((currPage - 1) * size);// 用 query 条件代替 skip
        findOptions.limit(size);//查询条数

        //TODO: 结果
        List<Sample> list = query.find(findOptions).toList();

        EGridResult<Sample> result = new EGridResult<>(count, list);

        logger.warn("SamplePage 分页查询. 用时{}", System.currentTimeMillis() - start);
        return result;
    }

    @Override
    public String getApiListHtmlString() {
        List<RestClassInfo> list = ReflectHelper.getRestControllerList("com.erdos.coal.park");

        StringBuilder str = new StringBuilder("<html>");

        str.append("<style>");
        str.append(" table{border-collapse:collapse;border-spacing:0;border-left:1px solid #888;border-top:1px solid #888;background:#efefef;width:100%}");
        str.append(" th,td{border-right:1px solid #888;border-bottom:1px solid #888;padding:5px 15px;}");
        str.append(" th{font-weight:bold;background:#ccc;}");
        str.append("</style>");

        for (RestClassInfo c : list) {
            if (CollectionUtils.isEmpty(c.getMethods())) continue;

            str.append(c.getName()).append(" ").append(c.getMapping());

            str.append("<table>");
            for (RestClassInfo.RestMethodInfo m : c.getMethods()) {
                str.append("<tr>");
                {
                    str.append("<td width='15%'>");
                    str.append(m.getDesc());
                    str.append("</td>");
                    str.append("<td width='5%'>");
                    str.append(m.getMethodType());
                    str.append("</td>");
                    str.append("<td width='30%'>");
                    str.append(c.getMapping()).append(m.getMapping());
                    str.append("</td>");
                    str.append("<td>");
                    str.append(m.getName());
                    str.append("</td>");
                }
                str.append("</tr>");
            }
            str.append("</table>");
            str.append("<br>");
        }
        str.append("</html>");
        return str.toString();
    }

    @Override
    public String getRequestListWithinAnHour() {
        Query<AppLog> query = appLogService.createQuery();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        query.field("createTime").greaterThanOrEq(calendar.getTime());//一小时内
        query.criteria("consumes").greaterThanOrEq(1000); // 大于1000ms
        query.order(Sort.descending("consumes"));
        List<AppLog> list = appLogService.getAppLogList(query);
        return this.outHtmlString(list);
    }

    @Override
    public String getRequestListWithinAnDay() {
        Query<AppLog> query = appLogService.createQuery();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        query.field("createTime").greaterThanOrEq(calendar.getTime()); //一天内
        query.field("consumes").greaterThanOrEq(1000); // 大于1000ms
        query.order(Sort.descending("consumes"));
        List<AppLog> list = appLogService.getAppLogList(query);
        return this.outHtmlString(list);
    }

    @Override
    public String getRequestListWithinAnYesterday() {
        Query<AppLog> query = appLogService.createQuery();

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date end = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date begin = calendar.getTime();
        query.field("createTime").greaterThanOrEq(begin); //昨天0点
        query.field("createTime").lessThan(end); // 今天0点
        query.field("consumes").greaterThanOrEq(1000); // 大于1000ms
        query.order(Sort.descending("consumes"));
        List<AppLog> list = appLogService.getAppLogList(query);
        return this.outHtmlString(list);
    }

    @Override
    public String getRequestListWithinAnToday() {
        Query<AppLog> query = appLogService.createQuery();

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date begin = calendar.getTime();
        query.field("createTime").greaterThanOrEq(begin); // 今天0点
        query.field("consumes").greaterThanOrEq(1000); // 大于1000ms
        query.order(Sort.descending("consumes"));
        List<AppLog> list = appLogService.getAppLogList(query);
        return this.outHtmlString(list);
    }

    public static void main(String[] args) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        System.out.println(calendar.getTime());
    }

}
