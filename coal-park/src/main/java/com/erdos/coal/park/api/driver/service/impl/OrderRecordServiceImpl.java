package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.map.service.MapService;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.business.service.ICompanyOrderService;
import com.erdos.coal.park.api.business.service.IContractService;
import com.erdos.coal.park.api.business.service.ITradeContractService;
import com.erdos.coal.park.api.customer.dao.ICusMessageDao;
import com.erdos.coal.park.api.customer.entity.*;
import com.erdos.coal.park.api.customer.pojo.DriverInfoData;
import com.erdos.coal.park.api.customer.pojo.GOrder;
import com.erdos.coal.park.api.customer.pojo.GOrderTaking;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.customer.service.*;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.dao.IOrderRecordDao;
import com.erdos.coal.park.api.driver.entity.*;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.pojo.HandlingCostPojo;
import com.erdos.coal.park.api.driver.pojo.PoundList;
import com.erdos.coal.park.api.driver.service.*;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.app.entity.HotLine;
import com.erdos.coal.park.web.app.service.IHotLineService;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.pojo.SubUnitGeo;
import com.erdos.coal.park.web.sys.service.*;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.transaction.wxpay.service.WXPayWechatConfig;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.UpdateResult;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service("orderRecordService")
public class OrderRecordServiceImpl extends BaseMongoServiceImpl<OrderRecord, IOrderRecordDao> implements IOrderRecordService {

    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    @Lazy
    private IGoodsService goodsService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    @Lazy
    private IOrderService orderService;
    @Resource
    private IOrderLogisticsService orderLogisticsService;
    @Resource
    private IGidAndDidService gidAndDidService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;
    @Resource
    private ISysUnitAccountService sysUnitAccountService;
    @Resource
    private ISysSwitchService sysSwitchService;
    @Resource
    private IWxPrepaidService wxPrepaidService;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private WXPayWechatConfig config;
    @Resource
    private MongoClient client;
    @Resource
    private IPlatFormAccountService platFormAccountService;
    @Resource
    private IContractService contractService;
    @Resource
    private IDriverSignInService driverSignInService;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    private IQuarantineInfoService quarantineInfoService;
    @Resource
    private ILockedService lockedService;
    @Resource
    private ICusMessageDao cusMessageDao;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private ICarDao carDao;
    @Resource
    private IWXsDvrGoodsService wXsDvrGoodsService;
    @Resource
    private IOrderPositionService orderPositionService;
    @Resource
    private IHotLineService hotLineService;
    @Resource
    private ICompanyOrderService companyOrderService;
    @Resource
    private IWxPrepaidCostService wxPrepaidCostService;
    @Resource
    private ITradeContractService tradeContractService;

    /**
     * 司机展示二维码接口
     * param:当前登陆的司机用户
     * return：driverInfoData
     */
    @Override
    public ServerResponse<DriverInfoData> twoDimensionCode(String carNum) {
        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);
        if (StrUtil.isEmpty(carNum) && StrUtil.isEmpty(dUser.getCarNum()))
            return ServerResponse.createError("请输入车号，或前往设置车牌号再接单");
        //TODO:验证司机的评价、黑名单等信息，判断是否有出示接单二维码的限制
        DriverInfoData di = new DriverInfoData();
        di.setId(dUser.getObjectId().toString());
        di.setName(dUser.getName());
        di.setCarNum(StrUtil.isNotEmpty(carNum) ? carNum : dUser.getCarNum());
        di.setMobile(dUser.getMobile());

        return ServerResponse.createSuccess("查询成功", di);
    }

    /**
     * 司机订单信息显示
     * param:手机号mobile 和 车牌号carNum
     * return: driverOrderData
     */
    public ServerResponse<List<DriverOrderData>> orderQuery(int[] finishTag, Long finishTime, String point, String tradePwd) {
        List<DriverOrderData> drList = new ArrayList<>();

        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);

        if (StrUtil.isNotEmpty(tradePwd)) {     //判断交易密码是否正确
            // 判断使用BCryptPasswordEncoder进行加密的密码是否正确
            //if (!new BCryptPasswordEncoder().matches(tradePwd, dUser.getTradePwd()))
            if (!Utils.md5(tradePwd).equals(dUser.getTradePwd()))
                return ServerResponse.createError("交易密码错误");
        }

        //TODO:1.根据手机号查询当前司机的所有订单信息
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("did", did);
        query.filter("finishTag in ", finishTag);
        query.filter("driverIsAgree != ", 2);
        if (finishTime != null) {
            query.or(
                    query.and(
                            query.criteria("updateTime").greaterThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 0)),
                            query.criteria("updateTime").lessThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 1))
                    ),
                    query.and(query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(finishTime, "00:00:00", 0)),
                            query.criteria("createTime").lessThanOrEq(IdUnit.weeHours1(finishTime, "00:00:00", 1))
                    )
            );
        }
        query.order(Sort.descending("createTime"));
        List<OrderTaking> updateList = query.find().toList();
        Query<Order> endQuery = orderService.createQuery();
        if (point != null) endQuery.criteria("endPoint").contains(point);
        List<Order> endOrder = endQuery.find().toList();

        for (OrderTaking taking : updateList) {
            for (Order order : endOrder) {
                if (taking.getOid().equals(order.getOid())) {
                    DriverOrderData driverOrderData = new DriverOrderData();
                    driverOrderData.setBeginPoint(order.getBeginPoint());
                    driverOrderData.setPrice(order.getPrice());
                    driverOrderData.setEndPoint(order.getEndPoint());
                    driverOrderData.setTradeName(order.getTradeName());
                    driverOrderData.setDistance(order.getDistance());
                    driverOrderData.setTolls(order.getTolls());
                    driverOrderData.setCarNum(order.getCarNum());
                    driverOrderData.setOid(taking.getOid());
                    driverOrderData.setOrderDate(taking.getUpdateTime());
                    driverOrderData.setCreateDate(taking.getCreateTime());
                    driverOrderData.setFinishTag(taking.getFinishTag());
                    driverOrderData.setMold(order.getMold());
                    driverOrderData.setDelete(taking.getDelete());//客商是否撤单
                    driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());
                    drList.add(driverOrderData);
                }
            }
        }
        return ServerResponse.createSuccess("查询成功", drList);
    }

    @Override
    public ServerResponse<EGridResult> orderQuery(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows) {

        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);

        if (StrUtil.isNotEmpty(tradePwd)) {     //判断交易密码是否正确
            if (!Utils.md5(tradePwd).equals(dUser.getTradePwd()))
                return ServerResponse.createError("交易密码错误");
        }

        //TODO:1.根据手机号查询当前司机的所有订单信息
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("did", did);
        query.filter("finishTag in ", finishTag);
        query.filter("driverIsAgree != ", 2);
        if (finishTime != null) {
            query.or(
                    query.and(
                            query.criteria("updateTime").greaterThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 0)),
                            query.criteria("updateTime").lessThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 1))
                    ),
                    query.and(query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(finishTime, "00:00:00", 0)),
                            query.criteria("createTime").lessThanOrEq(IdUnit.weeHours1(finishTime, "00:00:00", 1))
                    )
            );
        }
        query.order(Sort.descending("createTime"));
        EGridResult<OrderTaking> gridResult = orderTakingService.findPage(page, rows, query);    //分页
        List<OrderTaking> takingList = gridResult.getRows();

        Query<Order> endQuery = orderService.createQuery();
        if (point != null) endQuery.criteria("endPoint").contains(point);
        // 添加过滤条件 List<orderTaking>中oid
        List<String> oids = new ArrayList<>();
        for (OrderTaking ot : takingList) {
            oids.add(ot.getOid());
        }
        if (StrUtil.isNotEmpty(oids) && oids.size() > 0)
            endQuery.filter("oid in ", oids);

        List<Order> endOrder = endQuery.find().toList();

        List<DriverOrderData> drList = new ArrayList<>();
        for (OrderTaking taking : takingList) {
            for (Order order : endOrder) {
                if (taking.getOid().equals(order.getOid())) {
                    DriverOrderData driverOrderData = new DriverOrderData();

                    checkAppointment(driverOrderData, order, taking);

                    driverOrderData.setBeginPoint(order.getBeginPoint());
                    driverOrderData.setPrice(order.getPrice());
                    driverOrderData.setEndPoint(order.getEndPoint());
                    //driverOrderData.setTradeName(order.getTradeName());
                    driverOrderData.setTradeName(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());
                    driverOrderData.setDistance(order.getDistance());
                    driverOrderData.setTolls(order.getTolls());
                    driverOrderData.setCarNum(order.getCarNum());
                    driverOrderData.setOid(taking.getOid());
                    driverOrderData.setOrderDate(taking.getUpdateTime());
                    driverOrderData.setCreateDate(taking.getCreateTime());
                    driverOrderData.setFinishTag(taking.getFinishTag());
                    driverOrderData.setMold(order.getMold());
                    driverOrderData.setDelete(taking.getDelete());//客商是否撤单
                    driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());
                    driverOrderData.setOutGrossWeight(order.getOutGrossWeight());
                    driverOrderData.setOutTareWeight(order.getOutTareWeight());
                    driverOrderData.setInGrossWeight(order.getInGrossWeight());
                    driverOrderData.setInTareWeight(order.getInTareWeight());
                    driverOrderData.setCallStartTime(order.getCallStartTime());
                    driverOrderData.setCallEndTime(order.getCallEndTime());
                    drList.add(driverOrderData);
                }
            }
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(gridResult.getTotal(), drList));
    }

    @Override
    public ServerResponse<EGridResult> orderQuery2(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows) {

        if (finishTag.length == 1 && finishTag[0] == 2) return ServerResponse.createError("暂不支持查询历史订单");

        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);

        if (StrUtil.isNotEmpty(tradePwd) && !Utils.md5(tradePwd).equals(dUser.getTradePwd()))     //判断交易密码是否正确
            return ServerResponse.createError("交易密码错误");

        //TODO:1.根据手机号查询当前司机的所有订单信息
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("did", did);
        query.filter("finishTag in ", finishTag);
        query.filter("driverIsAgree != ", 2);
        query.filter("finishButton != ", 1);
        if (finishTime != null) {
            query.or(
                    query.and(
                            query.criteria("updateTime").greaterThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 0)),
                            query.criteria("updateTime").lessThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 1))
                    ),
                    query.and(query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(finishTime, "00:00:00", 0)),
                            query.criteria("createTime").lessThanOrEq(IdUnit.weeHours1(finishTime, "00:00:00", 1))
                    )
            );
        }
        query.order(Sort.descending("objectId"));
        EGridResult<OrderTaking> gridResult = orderTakingService.findPage(page, rows, query);    //分页
        List<OrderTaking> takingList = gridResult.getRows();

        List<DriverOrderData> drList = new ArrayList<>();
        if (StrUtil.isEmpty(takingList) || takingList.size() <= 0)
            return ServerResponse.createSuccess("查询成功", new EGridResult<>(gridResult.getTotal(), drList));

        Query<Order> endQuery = orderService.createQuery();
        if (point != null) endQuery.criteria("endPoint").contains(point);
        // 添加过滤条件 List<orderTaking>中oid
        List<String> oids = new ArrayList<>();
        for (OrderTaking ot : takingList) {
            oids.add(ot.getOid());
        }
        //if (StrUtil.isNotEmpty(oids) && oids.size() > 0) endQuery.filter("oid in ", oids.toArray());
        endQuery.filter("oid", takingList.get(0).getOid());
        List<Order> endOrder = orderService.list(endQuery);

        for (OrderTaking taking : takingList) {
            for (Order order : endOrder) {
                if (taking.getOid().equals(order.getOid())) {
                    DriverOrderData driverOrderData = new DriverOrderData();
                    BeanUtils.copyProperties(order, driverOrderData);

                    checkAppointment(driverOrderData, order, taking);

                    driverOrderData.setTradeName(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());

                    driverOrderData.setOrderDate(taking.getUpdateTime());
                    driverOrderData.setCreateDate(taking.getCreateTime());
                    //因为finishTag为1时，司机端 订单会显示完成卸货按钮,司机不点击，则一直显示订单 --- 22年4月19号
                    driverOrderData.setFinishTag(taking.getFinishTag());
                    //driverOrderData.setFinishTag(taking.getFinishButton() == 0 && taking.getFinishTag() == 2 ? 1 : taking.getFinishTag());
                    driverOrderData.setDelete(taking.getDelete());//客商是否撤单
                    driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());

                    if (order.getOutChecking() == 3) {
                        driverOrderData.setOutPoundList(new PoundList(order.getOutSubName(), order.getOutBillCode(), order.getCarNum(), order.getOutVariety(), order.getOutGrossWeight(), order.getOutTareWeight(), order.getInSubName(), order.getOtherOutTime()));
                        driverOrderData.setOutCoalTicket(order.getCoalTicket());
                    }
                    if (order.getInChecking() == 3) {
                        driverOrderData.setInPoundList(new PoundList(order.getOutSubName(), order.getInBillCode(), order.getCarNum(), order.getInVariety(), order.getInGrossWeight(), order.getInTareWeight(), order.getInSubName(), order.getOtherInTime()));
                        driverOrderData.setInCoalTicket(order.getCoalTicket());
                    }

                    drList.add(driverOrderData);
                }
            }
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(gridResult.getTotal(), drList));
    }

    @Override
    public ServerResponse<EGridResult> orderQuery3(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows) {

        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);

        if (StrUtil.isNotEmpty(tradePwd) && !Utils.md5(tradePwd).equals(dUser.getTradePwd()))     //判断交易密码是否正确
            return ServerResponse.createError("交易密码错误");

        //TODO:1.根据手机号查询当前司机的所有订单信息
        Query<Order> query = orderService.createQuery();
        query.filter("did", did);
        query.criteria("gid").notEqual(null);
        if (finishTag.length == 1 && finishTag[0] == 2) {
            query.filter("tranStatus", 5);
        } else {
            query.criteria("tranStatus").greaterThan(0);
            query.criteria("tranStatus").lessThan(5);
        }
        query.filter("delete", false);
//        query.filter("finishButton != ", 1);
        if (point != null) query.criteria("endPoint").contains(point);
        if (finishTime != null) {
            query.or(
                    query.and(
                            query.criteria("updateTime").greaterThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 0)),
                            query.criteria("updateTime").lessThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 1))
                    )
            );
        }
        query.order(Sort.descending("objectId"));
        EGridResult<Order> gridResult = orderService.findPage(page, rows, query);
        List<Order> orders = gridResult.getRows();

        List<DriverOrderData> drList = new ArrayList<>();
        for (Order order : orders) {
            OrderTaking taking = orderTakingService.get("oid", order.getOid());
            DriverOrderData driverOrderData = new DriverOrderData();
            BeanUtils.copyProperties(order, driverOrderData);

            checkAppointment(driverOrderData, order, taking);

            driverOrderData.setTradeName(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());
            driverOrderData.setOrderDate(taking.getUpdateTime());
            driverOrderData.setCreateDate(taking.getCreateTime());
            //因为finishTag为1时，司机端 订单会显示完成卸货按钮,司机不点击，则一直显示订单 --- 22年4月19号
            driverOrderData.setFinishTag(taking.getFinishTag());
            driverOrderData.setDelete(taking.getDelete());//客商是否撤单
            driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());
            if (order.getOutChecking() == 3) {
                driverOrderData.setOutPoundList(new PoundList(order.getOutSubName(), order.getOutBillCode(), order.getCarNum(), order.getOutVariety(), order.getOutGrossWeight(), order.getOutTareWeight(), order.getInSubName(), order.getOtherOutTime()));
                driverOrderData.setOutCoalTicket(order.getCoalTicket());
            }
            if (order.getInChecking() == 3) {
                driverOrderData.setInPoundList(new PoundList(order.getOutSubName(), order.getInBillCode(), order.getCarNum(), order.getInVariety(), order.getInGrossWeight(), order.getInTareWeight(), order.getInSubName(), order.getOtherInTime()));
                driverOrderData.setInCoalTicket(order.getCoalTicket());
            }

            drList.add(driverOrderData);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(gridResult.getTotal(), drList));
    }

    @Override
    public ServerResponse<EGridResult> orderQuery4(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows) {

        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);

        if (StrUtil.isNotEmpty(tradePwd) && !Utils.md5(tradePwd).equals(dUser.getTradePwd()))     //判断交易密码是否正确
            return ServerResponse.createError("交易密码错误");

        //TODO:1.根据手机号查询当前司机的所有订单信息
        Query<Order> query = orderService.createQuery();
        query.filter("did", did);
        query.criteria("gid").notEqual(null);
        if (finishTag.length == 1 && finishTag[0] == 2) {
//            query.filter("tranStatus", 5);
            query.or(
                    query.criteria("tranStatus").equal(5),  //完成的订单
                    query.criteria("delete").equal(true)    //作废的订单
            );
        } else {
            query.criteria("tranStatus").greaterThan(0);
            query.criteria("tranStatus").lessThan(5);
            query.filter("delete", false);
        }
//        query.filter("delete", false);
//        query.filter("finishButton != ", 1);
        if (point != null) query.criteria("endPoint").contains(point);
        if (finishTime != null) {
            query.or(
                    query.and(
                            query.criteria("updateTime").greaterThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 0)),
                            query.criteria("updateTime").lessThanOrEq(IdUnit.weeHours(finishTime, "00:00:00", 1))
                    )
            );
        }
//        query.order(Sort.descending("objectId"));
        query.order(Sort.descending("time1"));
        EGridResult<Order> gridResult = orderService.findPage(page, rows, query);
        List<Order> orders = gridResult.getRows();

        List<DriverOrderData> drList = new ArrayList<>();
        for (Order order : orders) {
            OrderTaking taking = orderTakingService.get("oid", order.getOid());
            DriverOrderData driverOrderData = new DriverOrderData();
            BeanUtils.copyProperties(order, driverOrderData);

            if (null != taking) checkAppointment(driverOrderData, order, taking);

            driverOrderData.setTradeName(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());
            if (null != taking) driverOrderData.setOrderDate(taking.getUpdateTime());
            driverOrderData.setCreateDate(null != null ? taking.getCreateTime() : order.getTime1());
            //因为finishTag为1时，司机端 订单会显示完成卸货按钮,司机不点击，则一直显示订单 --- 22年4月19号
            if (null != taking){
                driverOrderData.setFinishTag(taking.getFinishTag());
                driverOrderData.setDelete(taking.getDelete());//客商是否撤单
                driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());
            }
            if (order.getOutChecking() == 3) {
                driverOrderData.setOutPoundList(new PoundList(order.getOutSubName(), order.getOutBillCode(), order.getCarNum(), order.getOutVariety(), order.getOutGrossWeight(), order.getOutTareWeight(), order.getInSubName(), order.getOtherOutTime()));
                driverOrderData.setOutCoalTicket(order.getCoalTicket());
            }
            if (order.getInChecking() == 3) {
                driverOrderData.setInPoundList(new PoundList(order.getOutSubName(), order.getInBillCode(), order.getCarNum(), order.getInVariety(), order.getInGrossWeight(), order.getInTareWeight(), order.getInSubName(), order.getOtherInTime()));
                driverOrderData.setInCoalTicket(order.getCoalTicket());
            }

            // 判断订单一级单位是否要求代收装卸费 (以下逻辑基于 "司机必须先做出车型选择，厂区app确认车型在后" 使用流程做出判断)
            Integer[] cost = new Integer[]{0, 0};
            if (StrUtil.isNotEmpty(order.getOutUnitCode())) {
                SysUnit outSubUnit = sysUnitService.get("code", order.getOutDefaultDownUnit());
                if (StrUtil.isNotEmpty(outSubUnit.getHandlingCost()) && outSubUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(outSubUnit.getMchId()) && StrUtil.isNotEmpty(outSubUnit.getKey())) {
                    TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getOutBizContractCode());
                    if (tradeContract.isVehicleFee()) {
                        if (!order.getPay1()) {
                            cost[0] = 1;
                        } else if (StrUtil.isNotEmpty(order.getHandlingCostOutAPP()) && (order.getHandlingCostOutAPP() - order.getHandlingCostOutDvr()) > 0 && !order.getPay2()) {
                            cost[0] = 2;
                        }
                    }
                } else {
                    SysUnit outUnit = sysUnitService.get("code", order.getOutUnitCode());
                    if (StrUtil.isNotEmpty(outUnit.getHandlingCost()) && outUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(outUnit.getMchId()) && StrUtil.isNotEmpty(outUnit.getKey())) {
                        TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getOutBizContractCode());
                        if (tradeContract.isVehicleFee()) {
                            if (!order.getPay1()) {
                                cost[0] = 1;
                            } else if (StrUtil.isNotEmpty(order.getHandlingCostOutAPP()) && (order.getHandlingCostOutAPP() - order.getHandlingCostOutDvr()) > 0 && !order.getPay2()) {
                                cost[0] = 2;
                            }
                        }
                    }
                }
            }
            if (StrUtil.isNotEmpty(order.getInUnitCode())) {
                SysUnit inSubUnit = sysUnitService.get("code", order.getInDefaultDownUnit());
                if (StrUtil.isNotEmpty(inSubUnit.getHandlingCost()) && inSubUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(inSubUnit.getMchId()) && StrUtil.isNotEmpty(inSubUnit.getKey())) {
                    TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getInBizContractCode());
                    if (tradeContract.isVehicleFee()) {
                        if (!order.getPay3()) {
                            cost[1] = 1;
                        } else if (StrUtil.isNotEmpty(order.getHandlingCostInAPP()) && (order.getHandlingCostInAPP() - order.getHandlingCostInDvr()) > 0 && !order.getPay4()) {
                            cost[1] = 2;
                        }
                    }
                } else {
                    SysUnit inUnit = sysUnitService.get("code", order.getInUnitCode());
                    if (StrUtil.isNotEmpty(inUnit.getHandlingCost()) && inUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(inUnit.getMchId()) && StrUtil.isNotEmpty(inUnit.getKey())) {
                        TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getInBizContractCode());
                        if (tradeContract.isVehicleFee()) {
                            if (!order.getPay3()) {
                                cost[1] = 1;
                            } else if (StrUtil.isNotEmpty(order.getHandlingCostInAPP()) && (order.getHandlingCostInAPP() - order.getHandlingCostInDvr()) > 0 && !order.getPay4()) {
                                cost[1] = 2;
                            }
                        }
                    }
                }
            }
            driverOrderData.setIsCost(cost);
            driverOrderData.setIsCancel(order.getDelete());

            drList.add(driverOrderData);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(gridResult.getTotal(), drList));
    }

    private void checkAppointment(DriverOrderData driverOrderData, Order order, OrderTaking orderTaking) {
        /*
         * 前提条件：订单未完成、司机同意接单、后台维护的二级单位需要预约。
         * orderTaking中的sa字段默认是0，表示司机新接的订单，还没有进行预约操作
         * 发货物流：司机提交预约前，sa=0，返回app 端需要预约的二级单位编号和名称；司机提交预约后，sa=1，返回app 端预约成功的二级单位编号、名称和预约时间段
         * 收货物流：司机提交预约前，sa=0，返回app 端需要预约的二级单位编号和名称；司机提交预约后，sa=2，返回app 端预约成功的二级单位编号、名称和预约时间段
         * 收发货物流：订单分为两个阶段：1。发货单位未进厂；2。发货单位出场（当发货单位的检票状态为3时，才会进行收货二级单位是否显示预约的判断）
         * */
        if (orderTaking.getFinishTag() != 2 && orderTaking.getDriverIsAgree() == 1) {
            switch (order.getMold()) {
                case 0:
                    editDriverOrderDataAppointment(driverOrderData, order.getOutDefaultDownUnit(), order.getOutSubName(), orderTaking, 1);
                    break;
                case 1:
                    editDriverOrderDataAppointment(driverOrderData, order.getInDefaultDownUnit(), order.getInSubName(), orderTaking, 2);
                    break;
                case 2:
                    if (order.getOutChecking() < 3) {
                        editDriverOrderDataAppointment(driverOrderData, order.getOutDefaultDownUnit(), order.getOutSubName(), orderTaking, 1);
                    } else if (order.getOutChecking() == 3) {
                        editDriverOrderDataAppointment(driverOrderData, order.getInDefaultDownUnit(), order.getInSubName(), orderTaking, 2);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    //检查订单中的二级单位是否需要 预约、 签到、防疫申报、抢号
    private void editDriverOrderDataAppointment(DriverOrderData driverOrderData, String defaultDownUnit, String subName, OrderTaking orderTaking, int outOrIn) { //1-out,2-in
        driverOrderData.setaSubCode(defaultDownUnit);
        driverOrderData.setaSubName(subName);
        SysUnit sysUnit = sysUnitService.get("code", defaultDownUnit);
        if (sysUnit.getIsAppointment() == 0) {
            driverOrderData.setAppointment(0);
        } else if (sysUnit.getIsAppointment() == 1 && orderTaking.getSa() < outOrIn) {
            driverOrderData.setAppointment(1);
        } else if (sysUnit.getIsAppointment() == 1 && orderTaking.getSa() == outOrIn) {
            driverOrderData.setAppointment(2);
            driverOrderData.setaStartTime(orderTaking.getaStartTime());
            driverOrderData.setaEndTime(orderTaking.getaEndTime());
        }

        if (sysUnit.getIsPunchClock() == 0) {
            driverOrderData.setIsPunchClock(0);
        } else if (sysUnit.getIsPunchClock() == 1 && orderTaking.getIsPunchClock() < outOrIn) {
            driverOrderData.setIsPunchClock(1);
        } else if (sysUnit.getIsPunchClock() == 1 && orderTaking.getIsPunchClock() == outOrIn) {
            driverOrderData.setIsPunchClock(2);
        }

        if (sysUnit.getIsQuarantine() == 0 || sysUnit.getQuarantineType() == 1) {
            driverOrderData.setIsQuarantine(0);
        } else if (sysUnit.getIsQuarantine() == 1 && sysUnit.getQuarantineType() == 0 && orderTaking.getIsQuarantine() < outOrIn) {
            driverOrderData.setIsQuarantine(1);
            driverOrderData.setQuarantineInfo(sysUnit.getQuarantineInfo());
        } else if (sysUnit.getIsQuarantine() == 1 && orderTaking.getIsQuarantine() == outOrIn) {
            driverOrderData.setIsQuarantine(2);
        }

        if (sysUnit.getIsGrabNumber() == 0) {
            driverOrderData.setIsGrabNumber(0);
        } else if (sysUnit.getIsGrabNumber() == 1 && orderTaking.getIsGrabNumber() < outOrIn) {
            driverOrderData.setIsGrabNumber(1);
        } else if (sysUnit.getIsGrabNumber() == 1 && orderTaking.getIsGrabNumber() == outOrIn) {
            driverOrderData.setIsGrabNumber(2);
        }
    }

    /**
     * 客商发布的未抢订单信息显示
     * param:目的地
     * return: driverOrderData
     */
    public ServerResponse<List<GoodsInfoData>> lookGoods(String point) {
        String did = ShiroUtils.getUserId();
        String userType = ShiroUtils.getUserType();

        List<GoodsInfoData> resultList = new ArrayList<>();

        Query<Goods> dateQuery = goodsService.createQuery();
        if (!StrUtil.isEmpty(point)) {
            dateQuery.and(dateQuery.criteria("endPoint").contains(point));
        }
        dateQuery.filter("isWeChat", 0);
        dateQuery.filter("status", 0);      //未被禁止接单的
        dateQuery.order(Sort.descending("updateTime"));

        List<Goods> goodsList = dateQuery.find().toList();//查询发布的app端货物信息 未被撤销
        for (Goods goods : goodsList) {
            //超过72小时订单过期，不可接单
            if (new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000) continue;

            Query<Order> orderQuery = orderService.createQuery();
            orderQuery.filter("gid", goods.getGid());
            orderQuery.or(
                    orderQuery.criteria("carNum").equal(""),
                    orderQuery.criteria("carNum").equal(null)
            );
            orderQuery.filter("status", 0);//订单状态可用
            List<Order> orders = orderQuery.find().toList();//查询未接单且可用的订单信息

            if (orders.size() > 0) {
                int totalFee = totalFee(goods, orders.get(0), did, userType, String.valueOf(goods.getFees3()));

                Query<GidAndDid> query = gidAndDidService.createQuery();
                query.filter("gid", goods.getGid());
                query.filter("did in ", did);
                List<GidAndDid> gidAndDidList = query.find().toList();//查询司机是否在货物发布接单组里
                for (GidAndDid gidAndDid : gidAndDidList) {
                    Query<DriverPool> driverPoolQuery = driverPoolService.createQuery();
                    driverPoolQuery.filter("did", did);
                    driverPoolQuery.filter("cid", gidAndDid.getCid());
                    driverPoolQuery.criteria("whiteOrBlack").notEqual(0);//不在黑名单
                    List<DriverPool> driverPools = driverPoolQuery.find().toList();
                    if (driverPools.size() > 0) {
                        //TODO:处理要返回的数据包装到GoodsInfoData对象中
                        GoodsInfoData result = new GoodsInfoData();
                        BeanUtils.copyProperties(goods, result);
                        result.setFees(new BigDecimal(totalFee).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                        resultList.add(result);
                    }
                }
            }
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    public ServerResponse<List<GoodsInfoData>> lookGoods2(String point) {
        String did = ShiroUtils.getUserId();
        String userType = ShiroUtils.getUserType();

        Query<GidAndDid> gadQuery = gidAndDidService.createQuery();
        gadQuery.criteria("did").hasThisOne(did);
        gadQuery.criteria("updateTime").greaterThanOrEq(new Date().getTime() - 72 * 60 * 60 * 1000);//超过72小时订单过期，不可接单
        List<GidAndDid> gidAndDidList = gidAndDidService.list(gadQuery);
        List<String> gids = new ArrayList<>();
        for (GidAndDid gad : gidAndDidList) {
            gids.add(gad.getGid());
        }

        List<GoodsInfoData> resultList = new ArrayList<>();
        if (gids.size() > 0) {      //hasAnyOf方法list参数不能为空
            Query<Goods> query = goodsService.createQuery();
            query.criteria("gid").hasAnyOf(gids);
            if (!StrUtil.isEmpty(point)) {
                query.and(query.criteria("endPoint").contains(point));
            }
            query.criteria("isWeChat").equal(0);
            query.criteria("status").equal(0);      //未被禁止接单的

            Query<Goods> oQuery = goodsService.createQuery();
            oQuery.or(
                    oQuery.criteria("orders.carNum").equal(null),
                    oQuery.criteria("orders.carNum").equal("")
            );
            oQuery.criteria("orders.status").equal(0);
            AggregationPipeline pipeline = goodsService.createAggregation();
            pipeline.match(query)
                    .lookup("t_order", "gid", "gid", "orders")
                    .match(oQuery)
                    .sort(Sort.descending("updateTime"));

            Iterator<Goods> iterator = pipeline.aggregate(Goods.class);

            while (iterator.hasNext()) {
                Goods goods = iterator.next();
                List<GOrder> orders = goods.getOrders();//x货运信息下未被接单且可用的订单

                if (orders != null && orders.size() > 0) {
                    int totalFee = totalFee2(goods, orders.get(0), did, userType, String.valueOf(goods.getFees3()));

                    //TODO:处理要返回的数据包装到GoodsInfoData对象中
                    GoodsInfoData result = new GoodsInfoData();
                    BeanUtils.copyProperties(goods, result);
                    result.setFees(new BigDecimal(totalFee).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                    resultList.add(result);
                }
            }
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    public int totalFee2(Goods goods, GOrder order, String userId, String type, String fee) {
        //1。 司机已接单，显示司机已经支付金额
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", order.getOid());
        query.filter("did", userId);
        query.filter("driverIsAgree", 1);
        //query.filter("finishTag != ", 2);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking != null) {
            int driverFees = 0;//司机在订单中支付的总费用
            if (!distinguishPayerIsCusOrDri(order.getPayerId1Out(), order.getPayerId2Out(), order.getPayerId1In(), order.getPayerId2In()))
                driverFees += (order.getFees1Out() + order.getFees2Out() + order.getFees1In() + order.getFees2In());

            if (StrUtil.isNotEmpty(order.getDriverPayerId()) && order.getDriverPayerId().equals(userId) && order.getBalanceFees() > 0)
                driverFees += order.getBalanceFees();
            if (order.getShareFees() > 0) driverFees += order.getShareFees();
            return driverFees;
        }

        //2。 司机未接单，显示司机接单将需要支付的金额
        int[] tempFee = getPTFee(goods, userId, type);
        int ptFee = tempFee[0] + tempFee[1];    // 平台收取信息费
        int totalFee = ptFee + goods.getFees1In() + goods.getFees1Out() + goods.getFees2In() + goods.getFees2Out(); // + 收发单位收取的信息费
        // + 客商（友商）收取的信息费
        if (StrUtil.isEmpty(fee)) { // 扫码的直接包含客商收取的信息费，分享的客商收取信息费从goods表中获取
            totalFee += goods.getFees3();
        } else {
            totalFee = sumFees(totalFee, fee);
        }
        //  总费用 - 客商或单位已代付金额
        totalFee = totalFee - order.getFees() - order.getFeesIn() - order.getFeesOut() - order.getFees1In() - order.getFees1Out() - order.getFees2In() - order.getFees2Out();
        return totalFee;
    }

    @Transactional
    @Override
    public ServerResponse<String> grab(String gid, String carNum, String longitude, String latitude) {
        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);
        if (StrUtil.isEmpty(carNum) && StrUtil.isEmpty(dUser.getCarNum()))
            return ServerResponse.createError("请选择车牌号，或前往设置车牌号再抢单");
        if (StrUtil.isNotEmpty(dUser.getState()) && (dUser.getState() != 1 && dUser.getState() != 4))
            return ServerResponse.createError("未通过审核，不能抢单");

        if (orderTakingService.searchById(did, 2).size() > 0)
//        if (dUser.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成的订单，抢单失败");

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        // if (g.getIsWeChat() == 1) return ServerResponse.createError("请使用微信小程序接单");
        if (new Date().getTime() - g.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");
        //禁止接单的货运信息不可抢单
        if (g.getStatus() == 1) return ServerResponse.createError("货运信息已客商禁止接单！");

        Query<Order> query = orderService.createQuery();
        query.filter("gid", gid);
        query.filter("carNum", null);
        query.filter("delete", false);
        query.filter("share", 0);
        Order order = orderService.get(query);

        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
        //当mold为1时，即收货物流模式，经纬度参数不能为空
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //TODO:2.判断司机是否存在司机池中
        //暂时屏蔽司机池添加司机
        /*String cid = g.getCid();
        Query<DriverPool> driverPoolQuery = driverPoolService.createQuery();
        driverPoolQuery.filter("cid", cid);
        driverPoolQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(driverPoolQuery);

        if (dp == null) {
            return ServerResponse.createError("该司机未在客商发布司机池中");
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        //TODO:3.接单
        String oid = order.getOid();
        carNum = StrUtil.isNotEmpty(carNum) ? carNum : dUser.getCarNum();
        int updFeeResult = updateFee(did, oid, carNum, 0, "", SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("抢单失败");
        }

        //TODO:4.要返回的订单信息 即（订单号）
        //抢单成功推送消息
        pushInfoService.addAndSendPushInfo("抢单成功", "抢单成功", dUser.getObjectId().toString(), 2, dUser.getDeviceId());
        return ServerResponse.createSuccess("抢单成功", oid);
    }

    @Override
    public ServerResponse<String> isTradePwd() {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        if (StrUtil.isEmpty(driverInfo.getTradePwd())) {
            return ServerResponse.createError("交易密码未设置");
        }
        return ServerResponse.createSuccess();
    }

    @Override
    public ServerResponse<String> checkTradePwd(String pwd) {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        // 判断使用BCryptPasswordEncoder进行加密的密码是否正确
        //if (!new BCryptPasswordEncoder().matches(pwd, driverInfo.getTradePwd()))
        if (!Utils.md5(pwd).equals(driverInfo.getTradePwd()))
            return ServerResponse.createError("交易密码输入错误");
        else
            return ServerResponse.createSuccess();
    }

    @Override
    public void deleteOrder(List<String> oids) {
        List<DriverInfo> driverInfos = new ArrayList<>();

        for (String oid : oids) {
            OrderTaking ot = orderTakingService.get("oid", oid);

            if (ot == null) continue;
            //司机已接单，客商申请订单撤销，orderTaking表更新标记
            Query<OrderTaking> query = orderTakingService.createQuery().filter("objectId", ot.getObjectId());
            UpdateOperations<OrderTaking> updateOperations = orderTakingService.createUpdateOperations();
            updateOperations.set("delete", 0);
            orderTakingService.update(query, updateOperations);

            String did = ot.getDid();
            DriverInfo driverInfo = driverInfoService.getByPK(did);
            driverInfos.add(driverInfo);
        }
        pushInfoService.addAndSendPushInfos("撤销订单", "客商申请撤销订单", 6, driverInfos, oids);

    }

    @Override
    public ServerResponse<Double> searchFee(String oid, String gid, String fee) {
        Goods goods;
        Order order;
        if (StrUtil.isNotEmpty(gid)) {
            goods = goodsService.get("gid", gid);
            Query<Order> oQuery = orderService.createQuery();
            oQuery.filter("gid", gid);
            List<Order> orders = orderService.list(oQuery);
            if (orders.size() <= 0) return ServerResponse.createError("参数错误，订单不存在");
            order = orders.get(0);  //同货运批次下订单 需支付金额相同，可取第一个值为代表计算接单金额。
        } else if (StrUtil.isNotEmpty(oid)) {
            order = orderService.get("oid", oid);
            goods = goodsService.get("gid", order.getGid());
        } else {
            return ServerResponse.createError("参数错误，不能全空");
        }
        if (goods == null || order == null) return ServerResponse.createError("参数错误，货运信息不存在");

        Double dFee = searchOrderFee(goods, order, fee) / 100.0;     //分转为元

        return ServerResponse.createSuccess("查询成功", dFee);
    }

    @Override
    public ServerResponse<Map<String, Object>> searchFee2(String oid, String gid, String fee) {
        Goods goods;
        Order order;
        if (StrUtil.isNotEmpty(gid)) {
            goods = goodsService.get("gid", gid);
            Query<Order> oQuery = orderService.createQuery();
            oQuery.filter("gid", gid);
            List<Order> orders = orderService.list(oQuery);
            if (orders.size() <= 0) return ServerResponse.createError("参数错误，订单不存在");
            order = orders.get(0);  //同货运批次下订单 需支付金额相同，可取第一个值为代表计算接单金额。
        } else if (StrUtil.isNotEmpty(oid)) {
            order = orderService.get("oid", oid);
            goods = goodsService.get("gid", order.getGid());
        } else {
            return ServerResponse.createError("参数错误，不能全空");
        }
        if (goods == null || order == null) return ServerResponse.createError("参数错误，货运信息不存在");

        Double dFee = searchOrderFee(goods, order, fee) / 100.0;     //分转为元

        SysSwitch sysSwitch = sysSwitchService.get("code", 4);
        Map<String, Object> result = new HashMap<>();
        result.put("fee", dFee);
        if ((sysSwitch != null && sysSwitch.getPayType() == 1) && dFee > 0 && (goods.getFee5Out() > 0 || goods.getFee5In() > 0)) {
            result.put("way", "WX");
        } else {
            result.put("way", "PF");
        }
        return ServerResponse.createSuccess("查询成功", result);
    }

    private int searchOrderFee(Goods goods, Order order, String fee) {
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        fee = isForbidNewCuFee ? "0" : fee;

        String type = ShiroUtils.getUserType();
        String userId = ShiroUtils.getUserId();
        int totalFee = totalFee3(goods, order, userId, type, fee);
        //Double dFee = totalFee / 100.0;     //分转为元

        return totalFee;
    }

    @Override
    public Double searchFee(String gid, String oid) {
        Goods goods;
        Order order;
        if (StrUtil.isNotEmpty(gid)) {
            goods = goodsService.get("gid", gid);
            Query<Order> oQuery = orderService.createQuery();
            oQuery.filter("gid", gid);
            List<Order> orders = orderService.list(oQuery);
            if (orders.size() <= 0) return -1.0;//ServerResponse.createError("数据异常，联系管理员");
            order = orders.get(0);  //同货运批次下订单 需支付金额相同，可取第一个值为代表计算接单金额。
        } else if (StrUtil.isNotEmpty(oid)) {
            order = orderService.get("oid", oid);
            goods = goodsService.get("gid", order.getGid());
        } else {
            return -2.0;//ServerResponse.createError("参数错误，不能全空");
        }
        if (goods == null || order == null) return -3.0;//ServerResponse.createError("数据异常，联系管理员");

        String type = ShiroUtils.getUserType();
        String userId = ShiroUtils.getUserId();
        int totalFee = totalFee(goods, order, userId, type, null);
        Double dFee = totalFee / 100.0;     //分转为元
        return dFee;
    }

    // 返回该司机接单 应付平台信息费
    public int[] getPTFee(Goods goods, String did, String type) {
        int[] totalFee = new int[]{0, 0};

        if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
            totalFee[0] = getUnitCodeFee(goods.getOutUnitCode(), did, type);
        if (StrUtil.isNotEmpty(goods.getInUnitCode()))
            totalFee[1] = getUnitCodeFee(goods.getInUnitCode(), did, type);

        return totalFee;
    }

    /**
     * 司机应付金额 = 平台收取信息费 （app：当天超过规定车数，降费；小程序：当天超过规定车数，增费）
     * + 发货一级单位收取信息费 + 发货二级单位收取信息费
     * + 收货一级单位收取信息费 + 收货二级单位收取信息费 + 客商收取信息费/友商收取信息费
     * - 客商代付 - 单位代付金额
     */
    @Override
    public int totalFee(Goods goods, Order order, String userId, String type, String fee) {
        //1。 司机已接单，显示司机已经支付金额
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", order.getOid());
        query.filter("did", userId);
        query.filter("driverIsAgree", 1);
        //query.filter("finishTag != ", 2);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking != null) {
            int driverFees = 0;//司机在订单中支付的总费用
            if (!distinguishPayerIsCusOrDri(order.getPayerId1Out(), order.getPayerId2Out(), order.getPayerId1In(), order.getPayerId2In()))
                driverFees += (order.getFees1Out() + order.getFees2Out() + order.getFees1In() + order.getFees2In());

            if (StrUtil.isNotEmpty(order.getDriverPayerId()) && order.getDriverPayerId().equals(userId) && order.getBalanceFees() > 0)
                driverFees += order.getBalanceFees();
            if (order.getShareFees() > 0) driverFees += order.getShareFees();
            return driverFees;
        }

        //2。 司机未接单，显示司机接单将需要支付的金额
        int[] tempFee = getPTFee(goods, userId, type);
        int ptFee = tempFee[0] + tempFee[1];    // 平台收取信息费
        int totalFee = ptFee + goods.getFees1In() + goods.getFees1Out() + goods.getFees2In() + goods.getFees2Out(); // + 收发单位收取的信息费
        // + 客商（友商）收取的信息费
        if (StrUtil.isEmpty(fee)) { // 扫码的直接包含客商收取的信息费，分享的客商收取信息费从goods表中获取
            totalFee += goods.getFees3();
        } else {
            totalFee = sumFees(totalFee, fee);
        }
        //  总费用 - 客商或单位已代付金额
        totalFee = totalFee - order.getFees() - order.getFeesIn() - order.getFeesOut() - order.getFees1In() - order.getFees1Out() - order.getFees2In() - order.getFees2Out();
        return totalFee;
    }

    @Override
    public int totalFee3(Goods goods, Order order, String userId, String type, String fee) {
        //todo:1。 司机已接单，显示司机已经支付金额
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", order.getOid());
        query.filter("did", userId);
        query.filter("driverIsAgree", 1);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking != null) {
            int driverFees = 0;//司机在订单中支付的总费用
            if (userId.equals(order.getOutPayer1())) driverFees = driverFees + order.getOutFee0();
            if (userId.equals(order.getOutPayer2())) driverFees = driverFees + order.getOutFee1() + order.getOutFee2();
            if (userId.equals(order.getOutPayer3()))
                driverFees = driverFees + order.getOutFee3() + order.getOutFee4() + order.getOutFee5();
            if (userId.equals(order.getInPayer1())) driverFees = driverFees + order.getInFee0();
            if (userId.equals(order.getInPayer2())) driverFees = driverFees + order.getInFee1() + order.getInFee2();
            if (userId.equals(order.getInPayer3()))
                driverFees = driverFees + order.getInFee3() + order.getInFee4() + order.getInFee5();
            return driverFees;
        }

        //todo:2。 司机未接单，显示司机接单将需要支付的金额
        int totalFee = 0;
        //a_0.查询并判断单位是否代付
        boolean outUnitIsReplacePay = true;
        boolean inUnitIsReplacePay = true;
        if (StrUtil.isEmpty(goods.getOutUnitPayId())) outUnitIsReplacePay = false;    //发货一、二级单位都不代付平台信息费，则费用由客商或司机支付
        if (StrUtil.isEmpty(goods.getInUnitPayId())) inUnitIsReplacePay = false;      //收货一、二级单位都不代付平台信息费，则费用由客商或司机支付
        //a_1.查询并判断客商是否代付
        if (goods.getIsPay() == 0) {
            if (!outUnitIsReplacePay) totalFee = totalFee + order.getOutFee0();
            if (!inUnitIsReplacePay) totalFee = totalFee + order.getInFee0();
            //b.单位要求代扣的，客商不代付时，由司机支付
            totalFee = totalFee + order.getOutFee1() + order.getOutFee2() + order.getInFee1() + order.getInFee2();
        }
        //c.客商或友商收费以及第三方费用均由司机支付
        if (StrUtil.isNotEmpty(fee)) {
            totalFee = totalFee + Integer.valueOf(fee) + order.getOutFee5() + order.getInFee5();
        } else {
            totalFee = totalFee + order.getOutFee3() + order.getOutFee4() + order.getOutFee5() + order.getInFee5();
        }
        return totalFee;
    }

    @Override
    public ServerResponse<Order> designCarNum(int driverIsAgree, String oid, String carNum, String longitude, String latitude) {
        if (driverIsAgree != 1 && driverIsAgree != 2) return ServerResponse.createError("参数错误");
        String userId = ShiroUtils.getUserId();
        Order order = orderService.get("oid", oid);
        if (order.getIsWeChat() == 1) return ServerResponse.createError("请使用微信小程序接单");
        if (order.getDelete()) return ServerResponse.createError("订单已废除");

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", oid);
        query.filter("driverIsAgree", 0);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null) return ServerResponse.createError("该订单已被拒绝或接单");
        if (orderTaking.getFinishTag() > 0) return ServerResponse.createError("非闲置订单，不可操作");
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("车辆已进场，不能操作订单！");

        // TODO 司机拒绝接单，更新orderTaking、order；收发单位和客商付款金额全部退回
        if (driverIsAgree == 2) return DriRefuseOrder(oid, order);

        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单被客商禁止运输！");

        if (StrUtil.isNotEmpty(orderTaking.getDelete()) && orderTaking.getDelete() == 0)
            return ServerResponse.createError("客商已经申请废除该订单,不能同意接单");
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude)) {
            return ServerResponse.createError("允许获取当前位置信息才可以接单");
        }

        int result = updateFee(userId, oid, carNum, driverIsAgree, null, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (result == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (result == -2) {
            return ServerResponse.createError("接受订单失败，请重试");
        } else {
            return ServerResponse.createSuccess("操作成功");
        }
    }

    @Transactional
    ServerResponse<Order> DriRefuseOrder(String oid, Order order) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();

        Map<String, Object> params = new HashMap<>();
        Map<String, Object> updMap = new HashMap<>();
        Date time = new Date();

        try {
            clientSession.startTransaction();

            params.put("driverIsAgree", 2);
            //params.put("finishTag", 1);
            updMap.put("$set", params);
            UpdateResult ota = otCollection.updateOne(clientSession, new Document("oid", oid), Document.parse(JSONObject.toJSONString(updMap)));
            if (ota.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("拒绝失败，请重试");
            }

            params.clear();
            updMap.clear();
            params.put("delete", true);
            params.put("delType", 2);
            params.put("isCheck", 3);   // 司机拒绝接单，无需审核，置为“审核通过”
            params.put("updateTime", time.getTime());
            updMap.put("$set", params);
            UpdateResult oa = oCollection.updateOne(clientSession, new Document("oid", oid), Document.parse(JSONObject.toJSONString(updMap)));
            if (oa.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("拒绝失败，请重试");
            }

            Goods goods = goodsService.get("gid", order.getGid());
            //退客商代钱（付平台信息费，单位要求代扣费用）
            List<Document> cusAccountDocs = new ArrayList<>();
            int cusFees = order.getFees() + order.getFees1Out() + order.getFees2Out() + order.getFees1In() + order.getFees2In();
            if (cusFees > 0) {
                String cid = probePayId(order.getPayerId(), order.getPayerId1Out(), order.getPayerId2Out(), order.getPayerId1In(), order.getPayerId2In());
                cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(cusFees), 5, order.getOid(), time));
            }

            //退单位代付平台信息费
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            int[] types = new int[]{3, 5};  //3-单位代付退回，5-单位代扣退出
            SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
            String[] outUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
                outUids = new String[]{order.getPayerIdOut(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
            String[] inUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getInUnitCode()))
                inUids = new String[]{order.getPayerIdIn(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};
            String[] outUserIds = new String[]{order.getPayerIdOut(), order.getPayerId1Out(), order.getPayerId2Out()};
            String[] inUserIds = new String[]{order.getPayerIdIn(), order.getPayerId1In(), order.getPayerId2In()};
            int[] outFees = new int[]{order.getFeesOut(), -order.getFees1Out(), -order.getFees2Out()};
            int[] inFees = new int[]{order.getFeesIn(), -order.getFees1In(), -order.getFees2In()};
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, order.getOid(), outFees, time);
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, order.getOid(), inFees, time);

            List<Document> pfAccountDocs = new ArrayList<>();
            if (order.getFees() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerId(), new BigDecimal(-order.getFees()), 12, order.getOid(), time));
            if (order.getFeesOut() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-order.getFeesOut()), 11, order.getOid(), time));
            if (order.getFeesIn() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-order.getFeesIn()), 11, order.getOid(), time));
            if (order.getBalanceFees() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getDriverPayerId(), new BigDecimal(-order.getBalanceFees()), 13, order.getOid(), time));

            if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);

            clientSession.commitTransaction();
            return ServerResponse.createSuccess("拒绝成功");
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("拒绝失败，请重试");
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<Order> designCarNum2(int driverIsAgree, String oid, String carNum, String longitude, String latitude) {
        if (driverIsAgree != 1 && driverIsAgree != 2) return ServerResponse.createError("参数错误");
        String userId = ShiroUtils.getUserId();
        Order order = orderService.get("oid", oid);
        if (order.getDelete()) return ServerResponse.createError("订单已废除");

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", oid);
        query.filter("driverIsAgree", 0);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null) return ServerResponse.createError("该订单已被拒绝或接单");
        if (orderTaking.getFinishTag() > 0) return ServerResponse.createError("非闲置订单，不可操作");
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("车辆已进场，不能操作订单！");

        String gid = order.getGid();
        // TODO 司机拒绝接单，更新orderTaking、order；收发单位和客商付款金额全部退回
        if (driverIsAgree == 2) {
            ServerResponse<Order> serverResponse = DriRefuseOrder2(oid, order, orderTaking);
            if (serverResponse.getStatus() == 0) {
                //司机撤单，客商消息列表添加消息
                DriverInfo driverInfo = driverInfoService.getByPK(userId);
                CusMessage cMsg = new CusMessage(order.getCid(), oid, "司机撤单", "司机拒绝订单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                cusMessageDao.save(cMsg);
                if (StrUtil.isNotEmpty(order.getShareCid())) {
                    CusMessage shareCMsg = new CusMessage(order.getShareCid(), oid, "司机撤单", "司机成功撤单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                    cusMessageDao.save(shareCMsg);
                }
            }
            return serverResponse;
        }

        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单被客商禁止运输！");

        if (StrUtil.isNotEmpty(orderTaking.getDelete()) && orderTaking.getDelete() == 0)
            return ServerResponse.createError("客商已经申请废除该订单,不能同意接单");
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude)) {
            return ServerResponse.createError("允许获取当前位置信息才可以接单");
        }

        int result = updateFee2(userId, oid, carNum, driverIsAgree, null, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (result == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (result == -2) {
            return ServerResponse.createError("接受订单失败，请重试");
        } else {
            return ServerResponse.createSuccess("操作成功");
        }
    }

    @Override
    public ServerResponse<Order> designCarNum3(int driverIsAgree, String oid, String carNum, String longitude, String latitude) {
        if (driverIsAgree != 1 && driverIsAgree != 2) return ServerResponse.createError("参数错误");
        String userId = ShiroUtils.getUserId();
        Order order = orderService.get("oid", oid);
        if (order.getDelete()) return ServerResponse.createError("订单已废除");

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", oid);
        query.filter("driverIsAgree", 0);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null) return ServerResponse.createError("该订单已被拒绝或接单");
        if (orderTaking.getFinishTag() > 0) return ServerResponse.createError("非闲置订单，不可操作");
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("车辆已进场，不能操作订单！");

        // TODO 司机拒绝接单，更新orderTaking、order；收发单位和客商付款金额全部退回
        if (driverIsAgree == 2) {
            ServerResponse<Order> serverResponse = DriRefuseOrder3(oid, order, orderTaking);
            if (serverResponse.getStatus() == 0) {
                //司机撤单，客商消息列表添加消息
                DriverInfo driverInfo = driverInfoService.getByPK(userId);
                CusMessage cMsg = new CusMessage(order.getCid(), oid, "司机撤单", "司机拒绝订单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                cusMessageDao.save(cMsg);
                if (StrUtil.isNotEmpty(order.getShareCid())) {
                    CusMessage shareCMsg = new CusMessage(order.getShareCid(), oid, "司机撤单", "司机成功撤单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                    cusMessageDao.save(shareCMsg);
                }
            }
            return serverResponse;
        }

        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单被客商禁止运输！");

        if (StrUtil.isNotEmpty(orderTaking.getDelete()) && orderTaking.getDelete() == 0)
            return ServerResponse.createError("客商已经申请废除该订单,不能同意接单");
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        String fee;
        Goods g = goodsService.get("gid", order.getGid());
        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = order.getOutFee4().toString();
        } else {
            fee = order.getOutFee3().toString();
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        int result = updateFee3(userId, oid, carNum, driverIsAgree, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (result == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (result == -2) {
            return ServerResponse.createError("接受订单失败，请重试");
        } else if (result == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (result == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (result == -7) {
            return ServerResponse.createError("客商余额不足");
        } else {
            return ServerResponse.createSuccess("操作成功");
        }
    }

    @Override
    public ServerResponse<Order> designCarNum4(int driverIsAgree, String oid, String carNum, String longitude, String latitude) {
        if (driverIsAgree != 1 && driverIsAgree != 2) return ServerResponse.createError("参数错误");
        String userId = ShiroUtils.getUserId();
        Order order = orderService.get("oid", oid);
        if (order.getDelete()) return ServerResponse.createError("订单已废除");

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", oid);
        query.filter("driverIsAgree", 0);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null) return ServerResponse.createError("该订单已被拒绝或接单");
        if (orderTaking.getFinishTag() > 0) return ServerResponse.createError("非闲置订单，不可操作");
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("车辆已进场，不能操作订单！");

        // TODO 司机拒绝接单，更新orderTaking、order；收发单位和客商付款金额全部退回
        DriverInfo driverInfo = driverInfoService.getByPK(userId);
        if (driverIsAgree == 2) {
            ServerResponse<Order> serverResponse = DriRefuseOrder3(oid, order, orderTaking);
            if (serverResponse.getStatus() == 0) {
                //司机撤单，客商消息列表添加消息
                CusMessage cMsg = new CusMessage(order.getCid(), oid, "司机撤单", "司机拒绝订单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                cusMessageDao.save(cMsg);
                if (StrUtil.isNotEmpty(order.getShareCid())) {
                    CusMessage shareCMsg = new CusMessage(order.getShareCid(), oid, "司机撤单", "司机成功撤单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                    cusMessageDao.save(shareCMsg);
                }
            }
            return serverResponse;
        }

        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单被客商禁止运输！");

        if (StrUtil.isNotEmpty(orderTaking.getDelete()) && orderTaking.getDelete() == 0)
            return ServerResponse.createError("客商已经申请废除该订单,不能同意接单");
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        String fee;
        Goods g = goodsService.get("gid", order.getGid());
        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = order.getOutFee4().toString();
        } else {
            fee = order.getOutFee3().toString();
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = newOrderFee2(userId, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        int result = updateFee4(userId, oid, carNum, driverIsAgree, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (result == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (result == -2) {
            return ServerResponse.createError("接受订单失败，请重试");
        } else if (result == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (result == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (result == -7) {
            return ServerResponse.createError("客商余额不足");
        } else {
            //异步三方转账
            //thirdPartyAccountService.partyWXChange(oid);
            // 司机完成接单，异步发送短信给司机(有收费的司机提供)
            // goodsService.sendSms_HuaWei(order, driverInfo, 0);
            // 订单有发货方、订单outVarietyCode不为空、发货方二级单位配置了智慧能源授权码， 则提交数据到智慧能源
            if (StrUtil.isNotEmpty(order.getOutDefaultDownUnit()) && StrUtil.isNotEmpty(order.getOutVarietyCode())) {
                SysUnit outSubUnit = sysUnits[1];
                String accessCode = outSubUnit.getSmartEnergyAccessCode();
                if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();
                if (StrUtil.isNotEmpty(accessCode) && StrUtil.isNotEmpty(carNum))
                    orderService.supervisoryElectVoucherContPosition(order, driverInfo, carNum, accessCode);
            }
            return ServerResponse.createSuccess("操作成功");
        }
    }

    @Override
    public ServerResponse<Order> designCarNum5(int driverIsAgree, String oid, String carNum, String longitude, String latitude) {
        if (driverIsAgree != 1 && driverIsAgree != 2) return ServerResponse.createError("参数错误");
        String userId = ShiroUtils.getUserId();

        Order order = orderService.get("oid", oid);
        if (order.getDelete()) return ServerResponse.createError("订单已废除");

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", oid);
        query.filter("driverIsAgree", 0);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null) return ServerResponse.createError("该订单已被拒绝或接单");
        if (orderTaking.getFinishTag() > 0) return ServerResponse.createError("非闲置订单，不可操作");
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("车辆已进场，不能操作订单！");

        // TODO 司机拒绝接单，更新orderTaking、order；收发单位和客商付款金额全部退回
        DriverInfo driverInfo = driverInfoService.getByPK(userId);
        if (driverIsAgree == 2) {
            ServerResponse<Order> serverResponse = DriRefuseOrder3(oid, order, orderTaking);
            if (serverResponse.getStatus() == 0) {
                //司机撤单，客商消息列表添加消息
                CusMessage cMsg = new CusMessage(order.getCid(), oid, "司机撤单", "司机拒绝订单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                cusMessageDao.save(cMsg);
                if (StrUtil.isNotEmpty(order.getShareCid())) {
                    CusMessage shareCMsg = new CusMessage(order.getShareCid(), oid, "司机撤单", "司机成功撤单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                    cusMessageDao.save(shareCMsg);
                }
            }
            return serverResponse;
        }

//        if (driverInfoService.checkCarTareWeight(userId, carNum)) return ServerResponse.createError("请先完善车辆皮重信息");

        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单被客商禁止运输！");

        if (StrUtil.isNotEmpty(orderTaking.getDelete()) && orderTaking.getDelete() == 0)
            return ServerResponse.createError("客商已经申请废除该订单,不能同意接单");
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        String fee;
        Goods g = goodsService.get("gid", order.getGid());
        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = order.getOutFee4().toString();
        } else {
            fee = order.getOutFee3().toString();
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = newOrderFee2(userId, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        // 单位设置delivery时，请求企业端，必须企业端返回成功，则可成功接单
        Map<String, Object> deliveryMap = delivery(sysUnits, order, driverInfo, null, carNum);
        boolean su = (boolean) deliveryMap.get("su");
        String deliveryMsg = (String) deliveryMap.get("msg");
        if (!su) return ServerResponse.createError(deliveryMsg);

        int result = updateFee4(userId, oid, carNum, driverIsAgree, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (result == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (result == -2) {
            return ServerResponse.createError("接受订单失败，请重试");
        } else if (result == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (result == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (result == -7) {
            return ServerResponse.createError("客商余额不足");
        } else {
            //异步三方转账
            //thirdPartyAccountService.partyWXChange(oid);
            // 司机完成接单，异步发送短信给司机(有收费的司机提供)
            // goodsService.sendSms_HuaWei(order, driverInfo, 0);
            // 订单有发货方、订单outVarietyCode不为空、发货方二级单位配置了智慧能源授权码， 则提交数据到智慧能源
            if (StrUtil.isNotEmpty(order.getOutDefaultDownUnit()) && StrUtil.isNotEmpty(order.getOutVarietyCode())) {
                SysUnit outSubUnit = sysUnits[1];
                String accessCode = outSubUnit.getSmartEnergyAccessCode();
                if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();
                if (StrUtil.isNotEmpty(accessCode) && StrUtil.isNotEmpty(carNum))
                    orderService.supervisoryElectVoucherContPosition(order, driverInfo, carNum, accessCode);
            }
//            return ServerResponse.createSuccess("操作成功");

            if (StrUtil.isNotEmpty(deliveryMsg)) {
                return ServerResponse.createSuccess(deliveryMsg);
            } else {
                return ServerResponse.createSuccess("操作成功");
            }
        }
    }

    @Override
    public ServerResponse<Order> designCarNum6(int driverIsAgree, String oid, String carNum, String longitude, String latitude) {
        if (driverIsAgree != 1 && driverIsAgree != 2) return ServerResponse.createError("参数错误");
        String userId = ShiroUtils.getUserId();
        Order order = orderService.get("oid", oid);
        if (order.getDelete()) return ServerResponse.createError("订单已废除");

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", oid);
        query.filter("driverIsAgree", 0);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null) return ServerResponse.createError("该订单已被拒绝或接单");
        if (orderTaking.getFinishTag() > 0) return ServerResponse.createError("非闲置订单，不可操作");
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("车辆已进场，不能操作订单！");

        // TODO 司机拒绝接单，更新orderTaking、order；收发单位和客商付款金额全部退回
        DriverInfo driverInfo = driverInfoService.getByPK(userId);
        if (driverIsAgree == 2) {
            ServerResponse<Order> serverResponse = DriRefuseOrder3(oid, order, orderTaking);
            if (serverResponse.getStatus() == 0) {
                //司机撤单，客商消息列表添加消息
                CusMessage cMsg = new CusMessage(order.getCid(), oid, "司机撤单", "司机拒绝订单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                cusMessageDao.save(cMsg);
                if (StrUtil.isNotEmpty(order.getShareCid())) {
                    CusMessage shareCMsg = new CusMessage(order.getShareCid(), oid, "司机撤单", "司机成功撤单", userId, driverInfo.getMobile(), order.getCarNum(), false, 1);
                    cusMessageDao.save(shareCMsg);
                }
            }
            return serverResponse;
        }

        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单被客商禁止运输！");

        if (StrUtil.isNotEmpty(orderTaking.getDelete()) && orderTaking.getDelete() == 0)
            return ServerResponse.createError("客商已经申请废除该订单,不能同意接单");
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        String fee;
        Goods g = goodsService.get("gid", order.getGid());
        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = order.getOutFee4().toString();
        } else {
            fee = order.getOutFee3().toString();
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = newOrderFee2(userId, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        // 单位设置delivery时，请求企业端，必须企业端返回成功，则可成功接单
        Map<String, Object> deliveryMap = delivery(sysUnits, order, driverInfo, null, carNum);
        boolean su = (boolean) deliveryMap.get("su");
        String deliveryMsg = (String) deliveryMap.get("msg");
        if (!su) return ServerResponse.createError(deliveryMsg);

        int result = updateFee4(userId, oid, carNum, driverIsAgree, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (result == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (result == -2) {
            return ServerResponse.createError("接受订单失败，请重试");
        } else if (result == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (result == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (result == -7) {
            return ServerResponse.createError("客商余额不足");
        } else {
            //异步三方转账
            //thirdPartyAccountService.partyWXChange(oid);
            // 司机完成接单，异步发送短信给司机(有收费的司机提供)
            // goodsService.sendSms_HuaWei(order, driverInfo, 0);
            // 订单有发货方、订单outVarietyCode不为空、发货方二级单位配置了智慧能源授权码， 则提交数据到智慧能源
            if (StrUtil.isNotEmpty(order.getOutDefaultDownUnit()) && StrUtil.isNotEmpty(order.getOutVarietyCode())) {
                SysUnit outSubUnit = sysUnits[1];
                String accessCode = outSubUnit.getSmartEnergyAccessCode();
                if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();
                if (StrUtil.isNotEmpty(accessCode) && StrUtil.isNotEmpty(carNum))
                    orderService.supervisoryElectVoucherContPosition(order, driverInfo, carNum, accessCode);
            }

            if (StrUtil.isNotEmpty(deliveryMsg)) {
                return ServerResponse.createSuccess(deliveryMsg);
            } else {
                return ServerResponse.createSuccess("操作成功");
            }
        }
    }

    @Override
    public Map<String, Object> delivery(SysUnit[] sysUnits, Order order, DriverInfo driverInfo, Car car, String carNum) {
        QuarantineInfo quarantineInfo = quarantineInfoService.get("oid", order.getOid());
        if (car == null) {
            if (StrUtil.isEmpty(carNum)) carNum = order.getCarNum();
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", carNum);
            dtcQuery.filter("driverId", driverInfo.getObjectId().toHexString());
            DriverToCar dtc = driverToCarDao.get(dtcQuery);
            car = carDao.getByPK(dtc.getCarId());
        }

        boolean su = true;

        String resultStr = "";
        if (StrUtil.isNotEmpty(order.getOutUnitCode()) && StrUtil.isNotEmpty(sysUnits[0].getDelivery()) && sysUnits[0].getDelivery() == 1) {
            Map<String, Object> map = new HashMap<>();
            map.put("billCode", order.getOutBillCode());
            map.put("oid", order.getOid());
            if (StrUtil.isNotEmpty(driverInfo.getName())) map.put("dvrName", driverInfo.getName());
            if (StrUtil.isNotEmpty(driverInfo.getMobile())) map.put("dvrMobile", driverInfo.getMobile());
            if (StrUtil.isNotEmpty(driverInfo.getIdentity())) map.put("dvrIdentity", driverInfo.getIdentity());
            map.put("checking", order.getOutChecking());
            map.put("carNum", carNum);
            if (car != null) {
                map.put("capacity", car.getCarInfo().getCapacity());
                map.put("axlesNumber", car.getCarInfo().getAxlesNumber());
                map.put("carType", car.getCarInfo().getCarTypeNum().toString());
            }
            if (StrUtil.isNotEmpty(order.getOutGrossWeight())) map.put("otherGross", order.getOutGrossWeight());
            if (StrUtil.isNotEmpty(order.getOutTareWeight())) map.put("otherTare", order.getOutTareWeight());
            if (StrUtil.isNotEmpty(order.getOtherOutTime())) map.put("otherOutTime", order.getOtherOutTime());
            if (StrUtil.isNotEmpty(order.getOtherInTime())) map.put("otherInTime", order.getOtherInTime());

            if (quarantineInfo != null) {
                map.put("isQua", "1");
                if (StrUtil.isNotEmpty(quarantineInfo.getAudit()) && quarantineInfo.getAudit() != 0) {
                    map.put("quaResult", String.valueOf(quarantineInfo.getAudit() % 2));
                } else {
                    map.put("quaResult", "0");
                }
            } else {
                map.put("isQua", "0");
                map.put("quaResult", "0");
            }

            if (StrUtil.isNotEmpty(order.getSpell())) map.put("spell", order.getSpell());
            if (StrUtil.isNotEmpty(order.getPlace())) map.put("place", order.getPlace());
            if (StrUtil.isNotEmpty(order.getCipcherText())) map.put("cipcherText", order.getCipcherText());
            if (StrUtil.isNotEmpty(car.getTareWeight())) {
                map.put("tareWeight", car.getTareWeight());
            } else {
                map.put("tareWeight", 0.0);
            }
            if (StrUtil.isNotEmpty(order.getInNetWeight())) map.put("inNetWeight", order.getInNetWeight());
            if (StrUtil.isNotEmpty(order.getInNetWeightPho())) {
                String phoUrl = photoFileService.publicReadUrl(order.getInNetWeightPho());
                map.put("inNetWeightPho", phoUrl);
            } else if (StrUtil.isNotEmpty(order.getLoadPoundPho())) {
                String phoUrl = photoFileService.publicReadUrl(order.getLoadPoundPho());
                map.put("inNetWeightPho", phoUrl);
            }
            if (StrUtil.isNotEmpty(order.getLoadPound())) map.put("loadPound", order.getLoadPound());
            if (StrUtil.isNotEmpty(order.getLoadTime())) map.put("loadTime", order.getLoadTime());

            Map<String, Object> map1 = companyOrderService.queryCompanyDelivery(sysUnits[0].getIp(), map);
            if (StrUtil.isNotEmpty(map1.get("successMsg"))) {
                resultStr = sysUnits[0].getName() + "：" + map1.get("successMsg");
            } else {
                su = false;
                resultStr = sysUnits[0].getName() + "：" + map1.get("errorMsg");
            }
        }
        if (su && StrUtil.isNotEmpty(order.getInUnitCode()) && StrUtil.isNotEmpty(sysUnits[2].getDelivery()) && sysUnits[2].getDelivery() == 1) {
            Map<String, Object> map = new HashMap<>();
            map.put("billCode", order.getInBillCode());
            map.put("oid", order.getOid());
            if (StrUtil.isNotEmpty(driverInfo.getName())) map.put("dvrName", driverInfo.getName());
            if (StrUtil.isNotEmpty(driverInfo.getMobile())) map.put("dvrMobile", driverInfo.getMobile());
            if (StrUtil.isNotEmpty(driverInfo.getIdentity())) map.put("dvrIdentity", driverInfo.getIdentity());
            map.put("checking", order.getInChecking());
            map.put("carNum", carNum);
            if (car != null) {
                map.put("capacity", car.getCarInfo().getCapacity());
                map.put("axlesNumber", car.getCarInfo().getAxlesNumber());
                map.put("carType", car.getCarInfo().getCarTypeNum().toString());
            }
            if (StrUtil.isNotEmpty(order.getOutGrossWeight())) map.put("otherGross", order.getInGrossWeight());
            if (StrUtil.isNotEmpty(order.getOutTareWeight())) map.put("otherTare", order.getInTareWeight());
            if (StrUtil.isNotEmpty(order.getOtherOutTime())) map.put("otherOutTime", order.getOtherOutTime());
            if (StrUtil.isNotEmpty(order.getOtherInTime())) map.put("otherInTime", order.getOtherInTime());

            if (quarantineInfo != null) {
                map.put("isQua", "1");
                if (StrUtil.isNotEmpty(quarantineInfo.getAudit()) && quarantineInfo.getAudit() != 0) {
                    map.put("quaResult", String.valueOf(quarantineInfo.getAudit() % 2));
                } else {
                    map.put("quaResult", "0");
                }
            } else {
                map.put("isQua", "0");
                map.put("quaResult", "0");
            }

            if (StrUtil.isNotEmpty(order.getSpell())) map.put("spell", order.getSpell());
            if (StrUtil.isNotEmpty(order.getPlace())) map.put("place", order.getPlace());
            if (StrUtil.isNotEmpty(order.getCipcherText())) map.put("cipcherText", order.getCipcherText());
            if (StrUtil.isNotEmpty(car.getTareWeight())) {
                map.put("tareWeight", car.getTareWeight());
            } else {
                map.put("tareWeight", 0.0);
            }
            if (StrUtil.isNotEmpty(order.getInNetWeight())) map.put("inNetWeight", order.getInNetWeight());
            if (StrUtil.isNotEmpty(order.getInNetWeightPho())) {
                String phoUrl = photoFileService.publicReadUrl(order.getInNetWeightPho());
                map.put("inNetWeightPho", phoUrl);
            } else if (StrUtil.isNotEmpty(order.getLoadPoundPho())) {
                String phoUrl = photoFileService.publicReadUrl(order.getLoadPoundPho());
                map.put("inNetWeightPho", phoUrl);
            }
            if (StrUtil.isNotEmpty(order.getLoadPound())) map.put("loadPound", order.getLoadPound());
            if (StrUtil.isNotEmpty(order.getLoadTime())) map.put("loadTime", order.getLoadTime());

            Map<String, Object> map2 = companyOrderService.queryCompanyDelivery(sysUnits[2].getIp(), map);
            if (StrUtil.isNotEmpty(map2.get("successMsg"))) {
                if (StrUtil.isNotEmpty(resultStr)) {
                    resultStr = resultStr + ";" + sysUnits[2].getName() + "：" + map2.get("successMsg");
                } else {
                    resultStr = sysUnits[2].getName() + "：" + map2.get("successMsg");
                }
            } else {
                su = false;
                if (StrUtil.isNotEmpty(resultStr)) {
                    resultStr = resultStr + ";" + sysUnits[2].getName() + "：" + map2.get("errorMsg");
                } else {
                    resultStr = sysUnits[2].getName() + "：" + map2.get("errorMsg");
                }
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("msg", resultStr);
        resultMap.put("su", su);
        return resultMap;
    }

    @Transactional
    ServerResponse<Order> DriRefuseOrder2(String oid, Order order, OrderTaking orderTaking) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        Map<String, Object> params = new HashMap<>();
        Map<String, Object> updMap = new HashMap<>();
        Date time = new Date();

        try {
            clientSession.startTransaction();

            params.put("driverIsAgree", 2);
            updMap.put("$set", params);
            UpdateResult ota = otCollection.updateOne(clientSession, new Document("oid", oid).append("updateTime", orderTaking.getUpdateTime()), Document.parse(JSONObject.toJSONString(updMap)));
            if (ota.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("拒绝失败，请重试");
            }

            params.clear();
            updMap.clear();
            params.put("delete", true);
            params.put("delType", 2);
            params.put("isCheck", 3);   // 司机拒绝接单，无需审核，置为“审核通过”
            params.put("updateTime", time.getTime());
            params.put("tranStatus", 7);
            updMap.put("$set", params);
            UpdateResult oa = oCollection.updateOne(clientSession, new Document("oid", oid), Document.parse(JSONObject.toJSONString(updMap)));
            if (oa.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("拒绝失败，请重试");
            }

            Goods goods = goodsService.get("gid", order.getGid());
            //退客商代钱（付平台信息费，单位要求代扣费用）
            List<Document> cusAccountDocs = new ArrayList<>();
            int cusFees = order.getFees() + order.getFees1Out() + order.getFees2Out() + order.getFees1In() + order.getFees2In();
            if (cusFees > 0) {
                String cid = probePayId(order.getPayerId(), order.getPayerId1Out(), order.getPayerId2Out(), order.getPayerId1In(), order.getPayerId2In());
                cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(cusFees), 5, order.getOid(), time));
            }

            //退单位代付平台信息费
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            int[] types = new int[]{3, 5};  //3-单位代付退回，5-单位代扣退出
            SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
            String[] outUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
                outUids = new String[]{order.getPayerIdOut(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
            String[] inUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getInUnitCode()))
                inUids = new String[]{order.getPayerIdIn(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};
            String[] outUserIds = new String[]{order.getPayerIdOut(), order.getPayerId1Out(), order.getPayerId2Out()};
            String[] inUserIds = new String[]{order.getPayerIdIn(), order.getPayerId1In(), order.getPayerId2In()};
            int[] outFees = new int[]{order.getFeesOut(), -order.getFees1Out(), -order.getFees2Out()};
            int[] inFees = new int[]{order.getFeesIn(), -order.getFees1In(), -order.getFees2In()};
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, order.getOid(), outFees, time);
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, order.getOid(), inFees, time);

            List<Document> pfAccountDocs = new ArrayList<>();
            if (order.getFees() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerId(), new BigDecimal(-order.getFees()), 12, order.getOid(), time));
            if (order.getFeesOut() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-order.getFeesOut()), 11, order.getOid(), time));
            if (order.getFeesIn() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-order.getFeesIn()), 11, order.getOid(), time));
            if (order.getBalanceFees() > 0)
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getDriverPayerId(), new BigDecimal(-order.getBalanceFees()), 13, order.getOid(), time));

            if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);


            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", 1);
            param.put("orderNum1", -1);
            Map<String, Object> upgMap = new HashMap<>();
            upgMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 0);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(orderTaking.getDid())), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
            return ServerResponse.createSuccess("拒绝成功");
        } catch (Exception e) {
            clientSession.abortTransaction();
            logger.error("DriRefuseOrder2" + e.getMessage());
            return ServerResponse.createError("拒绝失败，请重试");
        } finally {
            clientSession.close();
        }
    }

    @Transactional
    ServerResponse<Order> DriRefuseOrder3(String oid, Order order, OrderTaking orderTaking) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        Map<String, Object> params = new HashMap<>();
        Map<String, Object> updMap = new HashMap<>();
        Date time = new Date();

        try {
            clientSession.startTransaction();

            params.put("driverIsAgree", 2);
            updMap.put("$set", params);
            UpdateResult ota = otCollection.updateOne(clientSession, new Document("oid", oid).append("updateTime", orderTaking.getUpdateTime()), Document.parse(JSONObject.toJSONString(updMap)));
            if (ota.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("拒绝失败，请重试");
            }

            params.clear();
            updMap.clear();
            params.put("delete", true);
            params.put("delType", 2);
            params.put("isCheck", 3);   // 司机拒绝接单，无需审核，置为“审核通过”
            params.put("updateTime", time.getTime());
            params.put("tranStatus", 7);
            params.put("time6", time);
            updMap.put("$set", params);
            UpdateResult oa = oCollection.updateOne(clientSession, new Document("oid", oid), Document.parse(JSONObject.toJSONString(updMap)));
            if (oa.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("拒绝失败，请重试");
            }

            //修改货运信息
            Goods goods = goodsService.get("gid", order.getGid());
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", 1);
            param.put("orderNum1", -1);
            Map<String, Object> upgMap = new HashMap<>();
            upgMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 0);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(orderTaking.getDid())), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
            return ServerResponse.createSuccess("拒绝成功");
        } catch (Exception e) {
            clientSession.abortTransaction();
            logger.error("DriRefuseOrder2" + e.getMessage());
            return ServerResponse.createError("拒绝失败，请重试");
        } finally {
            clientSession.close();
        }
    }

    /**
     * app 司机接单
     * 司机付款费用 = 平台费用（客商/收 发 未代付或代付金额不足） + 客商/友商费用 + 单位代收费用
     * 1.账户余额扣款
     * 2.平台收取费用
     * 2.1 客商多付，退回客商账户
     * 2.2 平台 收/发货单位 多付，退回单位账户
     * 2.3 客商/平台 未付，司机付款
     * 3.客商/友商收取费用  司机付
     * return   -1：司机账户金额不足，-2：保存失败，1：成功
     */
    @Transactional
    public int updateFee(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude) {
        //BigDecimal availableFee = driverAccountService.getAvailableFee(userId);//司机账户可用余额

        Order order = orderService.get("oid", oid);
        Goods goods = goodsService.get("gid", order.getGid());
        /*int totalFee = totalFee(goods, order, userId, type, fee);//司机接单应付金额
        if (totalFee > 0 && availableFee.compareTo(new BigDecimal(totalFee)) < 0) return -1;//司机账户金额不足*/
        int totalFee = 0;   //屏蔽收款功能
        fee = "0";  //屏蔽客商收费

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> olCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();

        try {
            clientSession.startTransaction();
            Date date = new Date();
            DriverInfo driverInfo = driverInfoService.getByPK(userId);
            if (StrUtil.isNotEmpty(carNum)) driverInfo.setCarNum(carNum);
            int[] tempFee = getPTFee(goods, userId, type);
            int ptFees = tempFee[0] + tempFee[1];//司机接单应付平台费用

            Map<String, Object> orderConMap = new HashMap<>();
            if (driverIsAgree == 0) {   //0：扫码接单或抢单
                //1.构造要修改order的车牌字段 - 司机接单，修改订单车牌号
                orderConMap.put("carNum", driverInfo.getCarNum());

                //2添加orderTaking 和 orderLogistics物流信息
                otCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 0, 1, date));
            } else if (driverIsAgree == 1) {    //1：司机接受指定了车牌号的订单
                //修改orderTaking表中 driverIsAgree 司机是否接受订单 字段为接受
                Map<String, Object> conMap = new HashMap<>();
                Map<String, Object> valMap = new HashMap<>();
                conMap.put("driverIsAgree", driverIsAgree);
                // if (goods.getMold() == 1) conMap.put("finishTag", 1);       //收货物流模式
                valMap.put("$set", conMap);
                Document filter = new Document("oid", oid);
                filter.append("did", userId);
                filter.append("driverIsAgree", 0);
                UpdateResult a = otCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(valMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }
            //3.添加物流信息
            //物流表，oid添加主键，解决并发问题
            olCollection.insertOne(clientSession, orderLogisticsService.createOLDoc(oid, 1, longitude, latitude, date));

            SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
            String[] outUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
                outUids = new String[]{sysUnits[0].getObjectId().toString(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
            String[] inUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getInUnitCode()))
                inUids = new String[]{sysUnits[2].getObjectId().toString(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};
            String[] outUserIds = new String[]{order.getPayerIdOut(), userId, userId};
            String[] inUserIds = new String[]{order.getPayerIdIn(), userId, userId};
            int[] types = new int[]{3, 1};  //3-单位多代付退回，1-单位代扣
            //outFees / inFees: 发（收）货单位代付平台信息费差价(实际支付的 - 需要支付的)，发（收）货一级、二级单位要求代扣金额
            int[] outFees = new int[]{0, goods.getFees1Out() - order.getFees1Out(), goods.getFees2Out() - order.getFees2Out()};
            int[] inFees = new int[]{0, goods.getFees1In() - order.getFees1In(), goods.getFees2In() - order.getFees2In()};
            if (order.getFeesOut() > 0) outFees[0] = order.getFeesOut() - tempFee[0];
            if (order.getFeesIn() > 0) inFees[0] = order.getFeesIn() - tempFee[1];

            //司机账户表数据处理 - 扣款
            if (totalFee > 0)
                dAccountCollection.insertOne(clientSession, driverAccountService.createDriAccountDoc(userId, userId, new BigDecimal(-totalFee), 2, oid, date));

            //客商账户表数据处理
            List<Document> cusAccountDocs = new ArrayList<>();
            int cusPayFee = order.getFees();  //客商代付平台信息费差价(实际支付的 - 需要支付的)
            if (order.getFeesOut() > 0) cusPayFee -= tempFee[1];    //发货单位代付了自己的信息费，则客商实际支付的是收货单位的信息费
            if (order.getFeesIn() > 0) cusPayFee -= tempFee[0];     //收货单位代付了自己的信息费，则客商实际支付的是发货单位的信息费
            if (cusPayFee > 0)  //多付退差价
                cusAccountDocs.add(customerAccountService.createCusAccountDoc(order.getPayerId(), new BigDecimal(cusPayFee), 4, oid, date));
            int cusFee = StrUtil.isNotEmpty(fee) ? new BigDecimal(fee).multiply(new BigDecimal(100)).intValue() : goods.getFees3();
            if (cusFee > 0) //收司机信息费
                cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(cusFee), 6, oid, date));
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);

            //单位们的账户表数据处理
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, order.getOid(), outFees, date);
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, order.getOid(), inFees, date);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);

            //平台账户表处理数据
            List<Document> pfAccountDocs = new ArrayList<>();
            if (cusPayFee > 0)  //客商多付平台信息费，退回
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerId(), new BigDecimal(-cusPayFee), 10, oid, date));
            if (outFees[0] > 0) //发货单位多付平台信息费，退回
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-outFees[0]), 9, oid, date));
            if (inFees[0] > 0)  //收货单位多付平台信息费，退回
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-inFees[0]), 9, oid, date));
            int balanceFees = ptFees - order.getFees() - order.getFeesOut() - order.getFeesIn();
            if (balanceFees > 0) //司机支付平台信息费，加入
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(userId, new BigDecimal(balanceFees), 7, oid, date));
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);

            //1.构造要修改order的金额字段
            if (outFees[0] > 0) orderConMap.put("feesOut", tempFee[0]);
            if (inFees[0] > 0) orderConMap.put("feesIn", tempFee[1]);
            if (totalFee > 0) {
                if (outFees[1] > 0) {
                    orderConMap.put("fees1Out", outFees[1]);
                    orderConMap.put("payerId1Out", userId);
                }
                if (outFees[2] > 0) {
                    orderConMap.put("fees2Out", outFees[2]);
                    orderConMap.put("payerId2Out", userId);
                }
                if (inFees[1] > 0) {
                    orderConMap.put("fees1In", inFees[1]);
                    orderConMap.put("payerId1In", userId);
                }
                if (inFees[2] > 0) {
                    orderConMap.put("fees2In", inFees[2]);
                    orderConMap.put("payerId2In", userId);
                }
                if (balanceFees > 0) {
                    orderConMap.put("balanceFees", balanceFees);
                    orderConMap.put("driverPayerId", userId);
                }
                if (cusFee > 0) {
                    orderConMap.put("shareFees", cusFee);
                    orderConMap.put("driverPayerId", userId);
                }
            }
            //更新order
            if (orderConMap.size() > 0) {
                orderConMap.put("updateTime", date.getTime());
                Map<String, Object> orderValMap = new HashMap<>();
                orderValMap.put("$set", orderConMap);
                Document filter = new Document("oid", oid);
                filter.append("updateTime", order.getUpdateTime());
                UpdateResult a = oCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(orderValMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }

            clientSession.commitTransaction();
            return 1;//成功
        } catch (Exception e) {
            e.printStackTrace();
            clientSession.abortTransaction();
            return -2;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public int updateFee2(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude) {
        //BigDecimal availableFee = driverAccountService.getAvailableFee(userId);//司机账户可用余额

        Order order = orderService.get("oid", oid);
        Goods goods = goodsService.get("gid", order.getGid());

        /*int totalFee = totalFee(goods, order, userId, type, fee);//司机接单应付金额
        if (totalFee > 0 && availableFee.compareTo(new BigDecimal(totalFee)) < 0) return -1;//司机账户金额不足*/
        int totalFee = 0;   //屏蔽收款功能
        fee = "0";  //屏蔽客商收费

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> olCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date date = new Date();
            DriverInfo driverInfo = driverInfoService.getByPK(userId);
            if (StrUtil.isNotEmpty(carNum)) driverInfo.setCarNum(carNum);
            int[] tempFee = getPTFee(goods, userId, type);
            int ptFees = tempFee[0] + tempFee[1];//司机接单应付平台费用

            Map<String, Object> orderConMap = new HashMap<>();
            if (driverIsAgree == 0) {   //0：扫码接单或抢单
                //1.构造要修改order的车牌字段 - 司机接单，修改订单车牌号
                orderConMap.put("carNum", driverInfo.getCarNum());
                orderConMap.put("did", driverInfo.getObjectId().toHexString());

                //2添加orderTaking 和 orderLogistics物流信息
                otCollection.insertOne(clientSession, orderTakingService.createOTDoc(order, driverInfo, 0, 0, 1, date));

                //修改货运信息
                Map<String, Object> param = new HashMap<>();
                param.put("orderNum0", -1);
                param.put("orderNum1", 1);
                Map<String, Object> upgMap = new HashMap<>();
                upgMap.put("$inc", param);
                gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));
            } else if (driverIsAgree == 1) {    //1：司机接受指定了车牌号的订单
                //修改orderTaking表中 driverIsAgree 司机是否接受订单 字段为接受
                Map<String, Object> conMap = new HashMap<>();
                Map<String, Object> valMap = new HashMap<>();
                conMap.put("driverIsAgree", driverIsAgree);
                valMap.put("$set", conMap);
                Document filter = new Document("oid", oid);
                filter.append("did", userId);
                filter.append("driverIsAgree", 0);
                UpdateResult a = otCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(valMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }
            //3.添加物流信息
            //物流表，oid添加主键，解决并发问题
            olCollection.insertOne(clientSession, orderLogisticsService.createOLDoc(oid, 1, longitude, latitude, date));

            SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
            String[] outUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
                outUids = new String[]{sysUnits[0].getObjectId().toString(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
            String[] inUids = new String[3];
            if (StrUtil.isNotEmpty(goods.getInUnitCode()))
                inUids = new String[]{sysUnits[2].getObjectId().toString(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};
            String[] outUserIds = new String[]{order.getPayerIdOut(), userId, userId};
            String[] inUserIds = new String[]{order.getPayerIdIn(), userId, userId};
            int[] types = new int[]{3, 1};  //3-单位多代付退回，1-单位代扣
            //outFees / inFees: 发（收）货单位代付平台信息费差价(实际支付的 - 需要支付的)，发（收）货一级、二级单位要求代扣金额
            int[] outFees = new int[]{0, goods.getFees1Out() - order.getFees1Out(), goods.getFees2Out() - order.getFees2Out()};
            int[] inFees = new int[]{0, goods.getFees1In() - order.getFees1In(), goods.getFees2In() - order.getFees2In()};
            if (order.getFeesOut() > 0) outFees[0] = order.getFeesOut() - tempFee[0];
            if (order.getFeesIn() > 0) inFees[0] = order.getFeesIn() - tempFee[1];

            //司机账户表数据处理 - 扣款
            if (totalFee > 0)
                dAccountCollection.insertOne(clientSession, driverAccountService.createDriAccountDoc(userId, userId, new BigDecimal(-totalFee), 2, oid, date));

            //客商账户表数据处理
            List<Document> cusAccountDocs = new ArrayList<>();
            int cusPayFee = order.getFees();  //客商代付平台信息费差价(实际支付的 - 需要支付的)
            if (order.getFeesOut() > 0) cusPayFee -= tempFee[1];    //发货单位代付了自己的信息费，则客商实际支付的是收货单位的信息费
            if (order.getFeesIn() > 0) cusPayFee -= tempFee[0];     //收货单位代付了自己的信息费，则客商实际支付的是发货单位的信息费
            if (cusPayFee > 0)  //多付退差价
                cusAccountDocs.add(customerAccountService.createCusAccountDoc(order.getPayerId(), new BigDecimal(cusPayFee), 4, oid, date));
            int cusFee = StrUtil.isNotEmpty(fee) ? new BigDecimal(fee).multiply(new BigDecimal(100)).intValue() : goods.getFees3();
            if (cusFee > 0) //收司机信息费
                cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(cusFee), 6, oid, date));
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);

            //单位们的账户表数据处理
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, order.getOid(), outFees, date);
            sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, order.getOid(), inFees, date);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);

            //平台账户表处理数据
            List<Document> pfAccountDocs = new ArrayList<>();
            if (cusPayFee > 0)  //客商多付平台信息费，退回
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerId(), new BigDecimal(-cusPayFee), 10, oid, date));
            if (outFees[0] > 0) //发货单位多付平台信息费，退回
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-outFees[0]), 9, oid, date));
            if (inFees[0] > 0)  //收货单位多付平台信息费，退回
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-inFees[0]), 9, oid, date));
            int balanceFees = ptFees - order.getFees() - order.getFeesOut() - order.getFeesIn();
            if (balanceFees > 0) //司机支付平台信息费，加入
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(userId, new BigDecimal(balanceFees), 7, oid, date));
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);

            //1.构造要修改order的金额字段
            if (outFees[0] > 0) orderConMap.put("feesOut", tempFee[0]);
            if (inFees[0] > 0) orderConMap.put("feesIn", tempFee[1]);
            if (totalFee > 0) {
                if (outFees[1] > 0) {
                    orderConMap.put("fees1Out", outFees[1]);
                    orderConMap.put("payerId1Out", userId);
                }
                if (outFees[2] > 0) {
                    orderConMap.put("fees2Out", outFees[2]);
                    orderConMap.put("payerId2Out", userId);
                }
                if (inFees[1] > 0) {
                    orderConMap.put("fees1In", inFees[1]);
                    orderConMap.put("payerId1In", userId);
                }
                if (inFees[2] > 0) {
                    orderConMap.put("fees2In", inFees[2]);
                    orderConMap.put("payerId2In", userId);
                }
                if (balanceFees > 0) {
                    orderConMap.put("balanceFees", balanceFees);
                    orderConMap.put("driverPayerId", userId);
                }
                if (cusFee > 0) {
                    orderConMap.put("shareFees", cusFee);
                    orderConMap.put("driverPayerId", userId);
                }
            }
            //更新order
            if (orderConMap.size() > 0) {
                //2022年4月17号，+tranStatus字段
                orderConMap.put("tranStatus", 1);//表示订单为 已接单状态
                orderConMap.put("updateTime", date.getTime());
                Map<String, Object> orderValMap = new HashMap<>();
                orderValMap.put("$set", orderConMap);
                Document filter = new Document("oid", oid);
                filter.append("updateTime", order.getUpdateTime());
                UpdateResult a = oCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(orderValMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 1);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(userId)), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
            return 1;//成功
        } catch (Exception e) {
            logger.error("updateFee2" + e.getMessage());
            clientSession.abortTransaction();
            return -2;
        } finally {
            clientSession.close();
        }
    }

    private void orderFeePay(Goods goods, Order order, Map<String, Object> orderConMap, String userId) {
        if (order.getOutFee0() > 0) {
            orderConMap.put("feesOut", order.getOutFee0());
            if (StrUtil.isNotEmpty(goods.getOutUnitPayId())) {
                orderConMap.put("payerIdOut", goods.getOutUnitPayId());
                orderConMap.put("outPayer1", goods.getOutUnitPayId());
                orderConMap.put("outPayerType1", "SU");
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                orderConMap.put("payerIdOut", goods.getPayCid());
                orderConMap.put("outPayer1", goods.getPayCid());
                orderConMap.put("outPayerType1", "CU");
            } else {
                orderConMap.put("payerIdOut", userId);
                orderConMap.put("outPayer1", userId);
                orderConMap.put("outPayerType1", "DU");
            }
        }
        if (order.getInFee0() > 0) {
            orderConMap.put("feesIn", order.getInFee0());
            if (StrUtil.isNotEmpty(goods.getInUnitPayId())) {
                orderConMap.put("payerIdIn", goods.getInUnitPayId());
                orderConMap.put("inPayer1", goods.getInUnitPayId());
                orderConMap.put("inPayerType1", "SU");
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                orderConMap.put("payerIdIn", goods.getPayCid());
                orderConMap.put("inPayer1", goods.getPayCid());
                orderConMap.put("inPayerType1", "CU");
            } else {
                orderConMap.put("payerIdIn", userId);
                orderConMap.put("inPayer1", userId);
                orderConMap.put("inPayerType1", "DU");
            }
        }
        if (order.getOutFee1() > 0) {
            orderConMap.put("fees1Out", order.getOutFee1());
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                orderConMap.put("payerId1Out", goods.getPayCid());
                orderConMap.put("outPayer2", goods.getPayCid());
                orderConMap.put("outPayerType2", "CU");
            } else {
                orderConMap.put("payerId1Out", userId);
                orderConMap.put("outPayer2", userId);
                orderConMap.put("outPayerType2", "DU");
            }
        }
        if (order.getOutFee2() > 0) {
            orderConMap.put("fees2Out", order.getOutFee2());
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                orderConMap.put("payerId2Out", goods.getPayCid());
                orderConMap.put("outPayer2", goods.getPayCid());
                orderConMap.put("outPayerType2", "CU");
            } else {
                orderConMap.put("payerId2Out", userId);
                orderConMap.put("outPayer2", userId);
                orderConMap.put("outPayerType2", "DU");
            }
        }
        if (order.getInFee1() > 0) {
            orderConMap.put("fees1In", order.getInFee1());
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                orderConMap.put("payerId1In", goods.getPayCid());
                orderConMap.put("inPayer2", goods.getPayCid());
                orderConMap.put("inPayerType2", "CU");
            } else {
                orderConMap.put("payerId1In", userId);
                orderConMap.put("inPayer2", userId);
                orderConMap.put("inPayerType2", "DU");
            }
        }
        if (order.getInFee2() > 0) {
            orderConMap.put("fees2In", order.getInFee2());
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                orderConMap.put("payerId2In", goods.getPayCid());
                orderConMap.put("inPayer2", goods.getPayCid());
                orderConMap.put("inPayerType2", "CU");
            } else {
                orderConMap.put("payerId2In", userId);
                orderConMap.put("inPayer2", userId);
                orderConMap.put("inPayerType2", "DU");
            }
        }
        int balanceFees = 0;
        if (StrUtil.isEmpty(goods.getOutUnitPayId()) && StrUtil.isEmpty(goods.getPayCid()))
            balanceFees = balanceFees + order.getOutFee0();
        if (StrUtil.isEmpty(goods.getInUnitPayId()) && StrUtil.isEmpty(goods.getPayCid()))
            balanceFees = balanceFees + order.getInFee0();
        if (StrUtil.isEmpty(goods.getPayCid()))
            balanceFees = balanceFees + order.getOutFee1() + order.getOutFee2() + order.getInFee1() + order.getInFee2();
        if (balanceFees > 0) {
            orderConMap.put("balanceFees", balanceFees);
            orderConMap.put("driverPayerId", userId);
        }
        if (goods.getFees3() > 0) {
            orderConMap.put("shareFees", goods.getFees3());
            orderConMap.put("driverPayerId", userId);
            orderConMap.put("outPayer3", userId);
        }
        if (order.getOutFee5() > 0) orderConMap.put("outPayer3", userId);
        if (order.getInFee5() > 0) orderConMap.put("inPayer3", userId);

    }

    private Map<String, Object> newOrderFee(String did, Goods goods, Order order, Date date, Integer fee) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Document> pfAccountDocs = new ArrayList<>();
        List<Document> sysUnitAccountDocs = new ArrayList<>();
        List<Document> cusAccountDocs = new ArrayList<>();
        List<Document> dvrAccountDocs = new ArrayList<>();
        List<Document> thirdPartyAccountDocs = new ArrayList<>();

        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
        int cusPayFee = 0;  //客商支付的费用
        int dvrPayFee = 0;  //司机支付的费用
        //1.平台收取信息费 - 平台账户、代付单位账户、代付客商账户、司机账户
        if (order.getOutFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getOutUnitPayId())) {
                //判断单位余额
                //屏蔽单位代付业务
                /*BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getOutUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getOutFee0())) < 0) {
                    resultMap.put("msg1", "发货端余额不足");
                    return resultMap;
                }

                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getOutUnitPayId(), new BigDecimal(order.getOutFee0()), 5, order.getOid(), date));
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(goods.getOutUnitPayId(), null, 0, order.getOid(), -order.getOutFee0(), date));*/
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getPayCid(), new BigDecimal(order.getOutFee0()), 6, order.getOid(), date));
                cusPayFee = cusPayFee + order.getOutFee0();
            } else {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(did, new BigDecimal(order.getOutFee0()), 7, order.getOid(), date));
                dvrPayFee = dvrPayFee + order.getOutFee0();
            }
        }
        if (order.getInFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getInUnitPayId())) {
                //判断单位余额
                //屏蔽单位代付业务
                /*BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getInUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getInFee0())) < 0) {
                    resultMap.put("msg2", "收货端余额不足");
                    return null;
                }

                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getInUnitPayId(), new BigDecimal(order.getInFee0()), 5, order.getOid(), date));
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(goods.getInUnitPayId(), null, 0, order.getOid(), -order.getInFee0(), date));*/
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getPayCid(), new BigDecimal(order.getInFee0()), 6, order.getOid(), date));
                cusPayFee = cusPayFee + order.getInFee0();
            } else {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(did, new BigDecimal(order.getInFee0()), 7, order.getOid(), date));
                dvrPayFee = dvrPayFee + order.getInFee0();
            }
        }
        //2.单位代扣费 - 单位账户、代付账户、司机账户
        if (order.getOutFee1() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getOutFee1(), date));
                cusPayFee = cusPayFee + order.getOutFee1();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), did, 1, order.getOid(), order.getOutFee1(), date));
                dvrPayFee = dvrPayFee + order.getOutFee1();
            }
        }
        if (order.getOutFee2() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getOutFee2(), date));
                cusPayFee = cusPayFee + order.getOutFee2();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), did, 1, order.getOid(), order.getOutFee2(), date));
                dvrPayFee = dvrPayFee + order.getOutFee2();
            }
        }
        if (order.getInFee1() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getInFee1(), date));
                cusPayFee = cusPayFee + order.getInFee1();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), did, 1, order.getOid(), order.getInFee1(), date));
                dvrPayFee = dvrPayFee + order.getInFee1();
            }
        }
        if (order.getInFee2() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getInFee2(), date));
                cusPayFee = cusPayFee + order.getInFee2();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), did, 1, order.getOid(), order.getInFee2(), date));
                dvrPayFee = dvrPayFee + order.getInFee2();
            }
        }
        //3.客商或友商收费 - 客商或友商账户、司机账户
        if (StrUtil.isEmpty(goods.getShareCid()) && fee > 0) {
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(fee), 6, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getOutFee3();
        } else if (fee > 0) {
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getShareCid(), new BigDecimal(fee), 6, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getOutFee4();
        }
        //4.三方收费
        if (order.getOutFee5() > 0) {
            thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), did, order.getOutFee5(), 0, order.getOid(), date));
            //                                                                                          (goods.getFee5OutId(), did, order.getOutFee5(), 0, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getOutFee5();
        }
        if (order.getInFee5() > 0) {
            thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[2].getThirdPartyName(), did, order.getInFee5(), 0, order.getOid(), date));
            //                                                                                          (goods.getFee5InId(), did, order.getInFee5(), 0, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getInFee5();
        }
        //5.判断司机账户余额
        BigDecimal availableFee = driverAccountService.getAvailableFee(did);//司机账户可用余额
        if (dvrPayFee > 0 && availableFee.compareTo(new BigDecimal(dvrPayFee)) < 0) {
            resultMap.put("msg3", "余额不足，请充值");
            return resultMap;//司机账户金额不足
        }
        //6.判断客商账户余额
        BigDecimal cusAvailableFee = customerAccountService.getAvailableFee(goods.getPayCid());
        if (cusPayFee > 0 && cusAvailableFee.compareTo(new BigDecimal(cusPayFee)) < 0) {
            resultMap.put("msg4", "客商余额不足");
            return resultMap; //客商账户余额不足
        }

        if (cusPayFee > 0)
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getPayCid(), new BigDecimal(-cusPayFee), 3, order.getOid(), date));
        if (dvrPayFee > 0)
            dvrAccountDocs.add(driverAccountService.createDriAccountDoc(did, null, new BigDecimal(-dvrPayFee), 2, order.getOid(), date));

        resultMap.put("pfAccountDocs", pfAccountDocs);
        resultMap.put("sysUnitAccountDocs", sysUnitAccountDocs);
        resultMap.put("cusAccountDocs", cusAccountDocs);
        resultMap.put("dvrAccountDocs", dvrAccountDocs);
        resultMap.put("thirdPartyAccountDocs", thirdPartyAccountDocs);
        return resultMap;
    }

    @Override
    public Map<String, Object> newOrderFee2(String did, Goods goods, Order order, Date date, Integer fee) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Document> pfAccountDocs = new ArrayList<>();
        List<Document> sysUnitAccountDocs = new ArrayList<>();
        List<Document> cusAccountDocs = new ArrayList<>();
        List<Document> dvrAccountDocs = new ArrayList<>();
        List<Document> thirdPartyAccountDocs = new ArrayList<>();

        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
        int cusPayFee = 0;  //客商支付的费用
        int dvrPayFee = 0;  //司机支付的费用
        //1.平台收取信息费 - 平台账户、代付单位账户、代付客商账户、司机账户
        if (order.getOutFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getOutUnitPayId())) {
                //判断单位余额
                //屏蔽单位代付业务
                /*BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getOutUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getOutFee0())) < 0) {
                    resultMap.put("msg1", "发货端余额不足");
                    return resultMap;
                }

                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getOutUnitPayId(), new BigDecimal(order.getOutFee0()), 5, order.getOid(), date));
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(goods.getOutUnitPayId(), null, 0, order.getOid(), -order.getOutFee0(), date));*/
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getPayCid(), new BigDecimal(order.getOutFee0()), 6, order.getOid(), date));
                cusPayFee = cusPayFee + order.getOutFee0();
            } else {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(did, new BigDecimal(order.getOutFee0()), 7, order.getOid(), date));
                dvrPayFee = dvrPayFee + order.getOutFee0();
            }
        }
        if (order.getInFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getInUnitPayId())) {
                //判断单位余额
                //屏蔽单位代付业务
                /*BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getInUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getInFee0())) < 0) {
                    resultMap.put("msg2", "收货端余额不足");
                    return null;
                }

                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getInUnitPayId(), new BigDecimal(order.getInFee0()), 5, order.getOid(), date));
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(goods.getInUnitPayId(), null, 0, order.getOid(), -order.getInFee0(), date));*/
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getPayCid(), new BigDecimal(order.getInFee0()), 6, order.getOid(), date));
                cusPayFee = cusPayFee + order.getInFee0();
            } else {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(did, new BigDecimal(order.getInFee0()), 7, order.getOid(), date));
                dvrPayFee = dvrPayFee + order.getInFee0();
            }
        }
        //2.单位代扣费 - 单位账户、代付账户、司机账户
        if (order.getOutFee1() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getOutFee1(), date));
                cusPayFee = cusPayFee + order.getOutFee1();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), did, 1, order.getOid(), order.getOutFee1(), date));
                dvrPayFee = dvrPayFee + order.getOutFee1();
            }
        }
        if (order.getOutFee2() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getOutFee2(), date));
                cusPayFee = cusPayFee + order.getOutFee2();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), did, 1, order.getOid(), order.getOutFee2(), date));
                dvrPayFee = dvrPayFee + order.getOutFee2();
            }
        }
        if (order.getInFee1() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getInFee1(), date));
                cusPayFee = cusPayFee + order.getInFee1();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), did, 1, order.getOid(), order.getInFee1(), date));
                dvrPayFee = dvrPayFee + order.getInFee1();
            }
        }
        if (order.getInFee2() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getInFee2(), date));
                cusPayFee = cusPayFee + order.getInFee2();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), did, 1, order.getOid(), order.getInFee2(), date));
                dvrPayFee = dvrPayFee + order.getInFee2();
            }
        }
        //3.客商或友商收费 - 客商或友商账户、司机账户
        if (StrUtil.isEmpty(goods.getShareCid()) && fee > 0) {
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(fee), 6, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getOutFee3();
        } else if (fee > 0) {
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getShareCid(), new BigDecimal(fee), 6, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getOutFee4();
        }
        //4.三方收费
        if (order.getOutFee5() > 0) {
            thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), did, order.getOutFee5(), 0, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getOutFee5();
        }
        if (order.getInFee5() > 0) {
            thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[2].getThirdPartyName(), did, order.getInFee5(), 0, order.getOid(), date));
            dvrPayFee = dvrPayFee + order.getInFee5();
        }
        //5.判断司机账户余额
        BigDecimal availableFee = driverAccountService.getAvailableFee(did);//司机账户可用余额
        if (dvrPayFee > 0 && availableFee.compareTo(new BigDecimal(dvrPayFee)) < 0) {
            resultMap.put("msg3", "余额不足，请充值");
            return resultMap;//司机账户金额不足
        }
        //6.判断客商账户余额
        BigDecimal cusAvailableFee = customerAccountService.getAvailableFee(goods.getPayCid());
        if (cusPayFee > 0 && cusAvailableFee.compareTo(new BigDecimal(cusPayFee)) < 0) {
            resultMap.put("msg4", "客商余额不足");
            return resultMap; //客商账户余额不足
        }

        if (cusPayFee > 0)
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getPayCid(), new BigDecimal(-cusPayFee), 3, order.getOid(), date));
        if (dvrPayFee > 0)
            dvrAccountDocs.add(driverAccountService.createDriAccountDoc(did, null, new BigDecimal(-dvrPayFee), 2, order.getOid(), date));

        resultMap.put("pfAccountDocs", pfAccountDocs);
        resultMap.put("sysUnitAccountDocs", sysUnitAccountDocs);
        resultMap.put("cusAccountDocs", cusAccountDocs);
        resultMap.put("dvrAccountDocs", dvrAccountDocs);
        resultMap.put("thirdPartyAccountDocs", thirdPartyAccountDocs);
        return resultMap;
    }

    private Map<String, Object> newOrderFee3(String did, Goods goods, Order order, Date date, Integer fee) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Document> pfAccountDocs = new ArrayList<>();
        List<Document> sysUnitAccountDocs = new ArrayList<>();
        List<Document> cusAccountDocs = new ArrayList<>();
        List<Document> thirdPartyAccountDocs = new ArrayList<>();

        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
        int cusPayFee = 0;  //客商支付的费用
        //1.平台收取信息费 - 平台账户、代付单位账户、代付客商账户、司机账户
        if (order.getOutFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getOutUnitPayId())) {
                //判断单位余额
                BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getOutUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getOutFee0())) < 0) {
                    resultMap.put("msg1", "发货端余额不足");
                    return resultMap;
                }

                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getOutUnitPayId(), new BigDecimal(order.getOutFee0()), 5, order.getOid(), date));
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(goods.getOutUnitPayId(), null, 0, order.getOid(), -order.getOutFee0(), date));
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getPayCid(), new BigDecimal(order.getOutFee0()), 6, order.getOid(), date));
                cusPayFee = cusPayFee + order.getOutFee0();
            } else {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(did, new BigDecimal(order.getOutFee0()), 7, order.getOid(), date));
            }
        }
        if (order.getInFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getInUnitPayId())) {
                //判断单位余额
                BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getInUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getInFee0())) < 0) {
                    resultMap.put("msg2", "收货端余额不足");
                    return null;
                }

                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getInUnitPayId(), new BigDecimal(order.getInFee0()), 5, order.getOid(), date));
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(goods.getInUnitPayId(), null, 0, order.getOid(), -order.getInFee0(), date));
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getPayCid(), new BigDecimal(order.getInFee0()), 6, order.getOid(), date));
                cusPayFee = cusPayFee + order.getInFee0();
            } else {
                pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(did, new BigDecimal(order.getInFee0()), 7, order.getOid(), date));
            }
        }
        //2.单位代扣费 - 单位账户、代付账户、司机账户
        if (order.getOutFee1() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getOutFee1(), date));
                cusPayFee = cusPayFee + order.getOutFee1();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), did, 1, order.getOid(), order.getOutFee1(), date));
            }
        }
        if (order.getOutFee2() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getOutFee2(), date));
                cusPayFee = cusPayFee + order.getOutFee2();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), did, 1, order.getOid(), order.getOutFee2(), date));
            }
        }
        if (order.getInFee1() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getInFee1(), date));
                cusPayFee = cusPayFee + order.getInFee1();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), did, 1, order.getOid(), order.getInFee1(), date));
            }
        }
        if (order.getInFee2() > 0) {
            if (StrUtil.isNotEmpty(goods.getPayCid())) {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), goods.getPayCid(), 1, order.getOid(), order.getInFee2(), date));
                cusPayFee = cusPayFee + order.getInFee2();
            } else {
                sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), did, 1, order.getOid(), order.getInFee2(), date));
            }
        }
        //3.客商或友商收费 - 客商或友商账户、司机账户
        if (StrUtil.isEmpty(goods.getShareCid()) && fee > 0) {
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(fee), 6, order.getOid(), date));
        } else if (fee > 0) {
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getShareCid(), new BigDecimal(fee), 6, order.getOid(), date));
        }
        //4.三方收费
        if (order.getOutFee5() > 0) {
            thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), did, order.getOutFee5(), 0, order.getOid(), date));
        }
        if (order.getInFee5() > 0) {
            thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[2].getThirdPartyName(), did, order.getInFee5(), 0, order.getOid(), date));
        }
        //6.判断客商账户余额
        BigDecimal cusAvailableFee = customerAccountService.getAvailableFee(goods.getPayCid());
        if (cusPayFee > 0 && cusAvailableFee.compareTo(new BigDecimal(cusPayFee)) < 0) {
            resultMap.put("msg4", "客商余额不足");
            return resultMap; //客商账户余额不足
        }

        if (cusPayFee > 0)
            cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getPayCid(), new BigDecimal(-cusPayFee), 3, order.getOid(), date));

        resultMap.put("pfAccountDocs", pfAccountDocs);
        resultMap.put("sysUnitAccountDocs", sysUnitAccountDocs);
        resultMap.put("cusAccountDocs", cusAccountDocs);
        resultMap.put("thirdPartyAccountDocs", thirdPartyAccountDocs);
        return resultMap;
    }

    @Override
    public int updateFee3(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude) {
        Order order = orderService.get("oid", oid);
        Goods goods = goodsService.get("gid", order.getGid());

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> olCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();

        try {
            clientSession.startTransaction();
            Date date = new Date();
            DriverInfo driverInfo = driverInfoService.getByPK(userId);
            if (StrUtil.isNotEmpty(carNum)) driverInfo.setCarNum(carNum);

            Map<String, Object> orderConMap = new HashMap<>();
            orderConMap.put("time1", date);//司机接单时间
            if (driverIsAgree == 0) {   //0：扫码接单或抢单
                //1.构造要修改order的车牌字段 - 司机接单，修改订单车牌号
                orderConMap.put("carNum", driverInfo.getCarNum());
                orderConMap.put("did", driverInfo.getObjectId().toHexString());

                //2添加orderTaking 和 orderLogistics物流信息
                otCollection.insertOne(clientSession, orderTakingService.createOTDoc(order, driverInfo, 0, 0, 1, date));

                //修改货运信息
                Map<String, Object> param = new HashMap<>();
                param.put("orderNum0", -1);
                param.put("orderNum1", 1);
                Map<String, Object> upgMap = new HashMap<>();
                upgMap.put("$inc", param);
                gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));
            } else if (driverIsAgree == 1) {    //1：司机接受指定了车牌号的订单
                //修改orderTaking表中 driverIsAgree 司机是否接受订单 字段为接受
                Map<String, Object> conMap = new HashMap<>();
                Map<String, Object> valMap = new HashMap<>();
                conMap.put("driverIsAgree", driverIsAgree);
                valMap.put("$set", conMap);
                Document filter = new Document("oid", oid);
                filter.append("did", userId);
                filter.append("driverIsAgree", 0);
                UpdateResult a = otCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(valMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }
            //3.添加物流信息
            //物流表，oid添加主键，解决并发问题
            olCollection.insertOne(clientSession, orderLogisticsService.createOLDoc(oid, 1, longitude, latitude, date));

            //1.构造要修改order的金额字段
            orderFeePay(goods, order, orderConMap, userId);
            //更新order
            if (orderConMap.size() > 0) {
                //2022年4月17号，+tranStatus字段
                orderConMap.put("tranStatus", 1);//表示订单为 已接单状态
                orderConMap.put("updateTime", date.getTime());
                Map<String, Object> orderValMap = new HashMap<>();
                orderValMap.put("$set", orderConMap);
                Document filter = new Document("oid", oid);
                filter.append("updateTime", order.getUpdateTime());
                UpdateResult a = oCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(orderValMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 1);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(userId)), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            //todo: 判断各方账户余额，并收费
            Map<String, Object> resultMap = newOrderFee(userId, goods, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
            if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return -5;
            if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return -6;
            if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return -1;
            if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return -7;
            List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
            List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
            List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
            List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
            List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
            if (thirdPartyAccountDocs.size() > 0) thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);

            clientSession.commitTransaction();
            return 1;//成功
        } catch (Exception e) {
            loggerError(e, "updateFee3");
            clientSession.abortTransaction();
            return -2;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public int updateFee4(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude, Date date, List<Document> pfAccountDocs, List<Document> sysUnitAccountDocs, List<Document> cusAccountDocs, List<Document> dvrAccountDocs, List<Document> thirdPartyAccountDocs) {
        Order order = orderService.get("oid", oid);
        Goods goods = goodsService.get("gid", order.getGid());

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> olCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();

        try {
            clientSession.startTransaction();
            DriverInfo driverInfo = driverInfoService.getByPK(userId);
            if (StrUtil.isNotEmpty(carNum)) driverInfo.setCarNum(carNum);

            Map<String, Object> orderConMap = new HashMap<>();
            orderConMap.put("time1", date);//司机接单时间
            if (driverIsAgree == 0) {   //0：扫码接单或抢单
                //1.构造要修改order的车牌字段 - 司机接单，修改订单车牌号
                orderConMap.put("carNum", driverInfo.getCarNum());
                orderConMap.put("did", driverInfo.getObjectId().toHexString());

                //2添加orderTaking 和 orderLogistics物流信息
                otCollection.insertOne(clientSession, orderTakingService.createOTDoc(order, driverInfo, 0, 0, 1, date));

                //修改货运信息
                Map<String, Object> param = new HashMap<>();
                param.put("orderNum0", -1);
                param.put("orderNum1", 1);
                Map<String, Object> upgMap = new HashMap<>();
                upgMap.put("$inc", param);
                gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));
            } else if (driverIsAgree == 1) {    //1：司机接受指定了车牌号的订单
                //修改orderTaking表中 driverIsAgree 司机是否接受订单 字段为接受
                Map<String, Object> conMap = new HashMap<>();
                Map<String, Object> valMap = new HashMap<>();
                conMap.put("driverIsAgree", driverIsAgree);
                valMap.put("$set", conMap);
                Document filter = new Document("oid", oid);
                filter.append("did", userId);
                filter.append("driverIsAgree", 0);
                UpdateResult a = otCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(valMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }
            //3.添加物流信息
            //物流表，oid添加主键，解决并发问题
            olCollection.insertOne(clientSession, orderLogisticsService.createOLDoc(oid, 1, longitude, latitude, date));

            //1.构造要修改order的金额字段
            orderFeePay(goods, order, orderConMap, userId);
            //更新order
            if (orderConMap.size() > 0) {
                //2022年4月17号，+tranStatus字段
                orderConMap.put("tranStatus", 1);//表示订单为 已接单状态
                orderConMap.put("updateTime", date.getTime());
                Map<String, Object> orderValMap = new HashMap<>();
                orderValMap.put("$set", orderConMap);
                Document filter = new Document("oid", oid);
                filter.append("updateTime", order.getUpdateTime());
                UpdateResult a = oCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(orderValMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 1);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(userId)), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            //todo: 各方账户余额收费
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
            if (thirdPartyAccountDocs.size() > 0) thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);

            clientSession.commitTransaction();
            return 1;//成功
        } catch (Exception e) {
            clientSession.abortTransaction();
            loggerError(e, "updateFee4");
            return -2;
        } finally {
            clientSession.close();
        }
    }

    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public void loggerError(Exception e, String name) {
        for (int i = 0; i < e.getStackTrace().length; i++) {
            StackTraceElement element = e.getStackTrace()[i];
            if ("OrderRecordServiceImpl.java".equals(element.getFileName()) && name.equals(element.getMethodName()))
                logger.error("fileName:" + element.getFileName() + "lineNumber:" + element.getLineNumber() + ",methodName:" + element.getMethodName());
        }
        logger.error(e.getMessage());
    }

    /**
     * 处理微信订单支付成功信息
     * 1.分配订单给司机
     * 若分配失败，则微信退款
     */
    //加锁
    @Override
    @Transactional
    public int distributionOrder(WxPrepaid prepaid) {
        String oid = prepaid.getOrderId();
        Goods goods = goodsService.get("gid", prepaid.getGoodsId());
        Order order = orderService.get("oid", oid);
        String did = prepaid.getDvrId();
        String dvrCarNum = prepaid.getDvrCarNum();
        String dvrMobile = prepaid.getDvrMobile();
        OrderTaking ot = orderTakingService.get("oid", oid);
        int driverIsAgree = 0;
        if (ot != null) driverIsAgree = 1;

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> oCollection = orderService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> olCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();

        try {
            clientSession.startTransaction();
            Date date = new Date();

            Map<String, Object> orderConMap = new HashMap<>();
            orderConMap.put("time1", date);//司机接单时间
            if (driverIsAgree == 0) {   //0：扫码接单或抢单
                //1.构造要修改order的车牌字段 - 司机接单，修改订单车牌号
                orderConMap.put("carNum", dvrCarNum);
                orderConMap.put("did", did);

                //2添加orderTaking 和 orderLogistics物流信息
                otCollection.insertOne(clientSession, orderTakingService.createOTDoc(order, did, dvrMobile, dvrCarNum, 0, 0, 1, date));

                //修改货运信息
                Map<String, Object> param = new HashMap<>();
                param.put("orderNum0", -1);
                param.put("orderNum1", 1);
                Map<String, Object> upgMap = new HashMap<>();
                upgMap.put("$inc", param);
                gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));
            } else if (driverIsAgree == 1) {    //1：司机接受指定了车牌号的订单
                //修改orderTaking表中 driverIsAgree 司机是否接受订单 字段为接受
                Map<String, Object> conMap = new HashMap<>();
                Map<String, Object> valMap = new HashMap<>();
                conMap.put("driverIsAgree", driverIsAgree);
                valMap.put("$set", conMap);
                Document filter = new Document("oid", oid);
                filter.append("did", did);
                filter.append("driverIsAgree", 0);
                UpdateResult a = otCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(valMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }
            //3.添加物流信息
            //物流表，oid添加主键，解决并发问题
            olCollection.insertOne(clientSession, orderLogisticsService.createOLDoc(oid, 1, prepaid.getDvrLon(), prepaid.getDvrLat(), date));

            //1.构造要修改order的金额字段
            orderFeePay(goods, order, orderConMap, did);
            //更新order
            if (orderConMap.size() > 0) {
                orderConMap.put("locked", false);   //支付完成解锁
                //2022年4月17号，+tranStatus字段
                orderConMap.put("tranStatus", 1);//表示订单为 已接单状态
                orderConMap.put("updateTime", date.getTime());
                Map<String, Object> orderValMap = new HashMap<>();
                orderValMap.put("$set", orderConMap);
                Document filter = new Document("oid", oid);
                filter.append("updateTime", order.getUpdateTime());
                UpdateResult a = oCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(orderValMap)));
                if (a.getModifiedCount() <= 0) {
                    clientSession.abortTransaction();
                    return -2;
                }
            }

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 1);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(did)), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            String fee = prepaid.getCusUnorderedFee();
            if (StrUtil.isNotEmpty(goods.getShareCid())) {
                fee = StrUtil.isEmpty(fee) ? order.getOutFee4().toString() : fee;
            } else {
                fee = StrUtil.isEmpty(fee) ? order.getOutFee3().toString() : fee;
            }
            //todo:判断 是否禁止客商收费
            boolean isForbidNewCuFee = false;   //false-不禁止客商收费
            SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货单位
            if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
                isForbidNewCuFee = true;        //true-禁止客商收费
            if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
                isForbidNewCuFee = true;        //true-禁止客商收费
            fee = isForbidNewCuFee ? "0" : fee;

            //todo: 判断各方账户余额，并收费
            Map<String, Object> resultMap = newOrderFee3(did, goods, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
            if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return -5;
            if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return -6;
            if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return -7;
            List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
            List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
            List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
            List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (thirdPartyAccountDocs.size() > 0) thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);

            clientSession.commitTransaction();
            return 1;//成功
        } catch (Exception e) {
            logger.error("distributionOrder" + e.getMessage());
            clientSession.abortTransaction();
            return -2;
        } finally {
            clientSession.close();
        }
    }

    private int getUnitCodeFee(String unitCode, String did, String type) {
        //查询司机当天已经接单次数，计算优惠
        Query<OrderTaking> orderTakingQuery = orderTakingService.createQuery();
        long[] todayTime = StrUtil.time();
        orderTakingQuery.filter("did", did);
        orderTakingQuery.filter("finishTag", 2);
        orderTakingQuery.filter("driverIsAgree", 1);
        orderTakingQuery.filter("updateTime >=", todayTime[0]).filter("updateTime <=", todayTime[1]);
        long orderSize = orderTakingQuery.count() + 1;

        int totalFee = 0;
        WechatDriverInfo wechatDriverInfo;
        SysUnit sysUnit = sysUnitService.get("code", unitCode);
        if (type.equals(UserType.DU.toString())) {  //app 当天拉货是否达到次数，达到降费，否则按标准收费
            if (sysUnit.getCarsTime() > 0 && orderSize >= sysUnit.getCarsTime()) {
                totalFee += sysUnit.getCarDeduct();
            } else {
                totalFee += sysUnit.getStandardDeduct();
            }
        } else if (type.equals(UserType.WECHAT.toString())) {
            wechatDriverInfo = wechatDriverInfoService.getByPK(did);
            if (sysUnit.getWechatTimes() > 0 && wechatDriverInfo.getTimes() >= sysUnit.getWechatTimes()) {  //使用小程序没有超过次数按标准扣费，否则按小程序扣费
                totalFee += sysUnit.getWechatDeduct();
            } else {
                totalFee += sysUnit.getStandardDeduct();
            }
        }
        return totalFee;
    }

    // 信息费 + 友商收取信息费
    private int sumFees(Integer totalFee, String fee) {
        if (StrUtil.isNotEmpty(fee)) {
            BigDecimal bigDecimal = new BigDecimal(fee);
            //Integer cusFee = bigDecimal.multiply(new BigDecimal(100)).intValue();
            Integer cusFee = bigDecimal.intValue();
            totalFee = totalFee + cusFee;
        }
        return totalFee;
    }

    // 单位收取的信息费 退回 司机或客商账户
    public Map<String, Object> getUnitFees(int driverFees, int cusFees, String driverPayerId, String cusPayerId, int fees, String payerId) {
        Map<String, Object> map = new HashMap<>();

        if (StrUtil.isNotEmpty(driverPayerId) && payerId.equals(driverPayerId)) {
            driverFees += fees;
        } else if (StrUtil.isNotEmpty(cusPayerId) && payerId.equals(cusPayerId)) {
            cusFees += fees;
        } else {
            DriverInfo driverInfo = driverInfoService.getByPK(payerId);
            if (driverInfo != null) {
                driverPayerId = payerId;
                driverFees += fees;
            } else {
                CustomerUser user = customerUserService.getByPK(payerId);
                if (user != null) {
                    cusPayerId = payerId;
                    cusFees += fees;
                }
            }
        }
        map.put("driverFees", driverFees);
        map.put("cusFees", cusFees);
        map.put("driverPayerId", driverPayerId);
        map.put("cusPayerId", cusPayerId);
        return map;
    }

    @Override
    public ServerResponse<String> saveDeleteOrder(String oid) {
        //todo:运输后的订单，不再允许司机同意撤销订单
        OrderTaking taking = orderTakingService.get("oid", oid);
        if (taking == null) return ServerResponse.createError("订单编号错误！");
        if (taking.getFinishTag() != 0) return ServerResponse.createError("货物在运输中，无法撤消！");

        Order order = orderService.get("oid", oid);
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("订单车辆已进场，不可撤单");
        //orderTakingService.delete(oid);
        if (order.getDelete()) return ServerResponse.createError("该订单已经废除");
        Goods goods = goodsService.get("gid", order.getGid());
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
        String[] outUids = new String[3];
        if (StrUtil.isNotEmpty(order.getOutUnitCode()))
            outUids = new String[]{order.getPayerIdOut(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
        String[] inUids = new String[3];
        //TODO: ligx
        //if (StrUtil.isNotEmpty(order.getOutUnitCode()))
        if (StrUtil.isNotEmpty(order.getInUnitCode()))
            inUids = new String[]{order.getPayerIdIn(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};

        int switchCheck = 0;// 废除订单，订单中的退款默认是审核的
        int delType;
        Query<SysSwitch> switchQuery = sysSwitchService.createQuery();
        switchQuery.filter("code", 1);
        switchQuery.filter("type", 2);
        if (taking.getDelete() == null || taking.getDelete() != 0) {    //司机主动废除订单
            delType = 1;
            switchQuery.filter("refundType", 1);
        } else {    //司机同意客商废除订单
            delType = 3;
            switchQuery.filter("refundType", 0);
        }
        SysSwitch sysSwitch = sysSwitchService.get(switchQuery);    // 司机退款是否需要审核
        if (sysSwitch != null && StrUtil.isNotEmpty(sysSwitch.getCheck()) && sysSwitch.getCheck() == 1) switchCheck = 3;

        ServerResponse<String> response = refundOrder(taking, order, switchCheck, delType, outUids, inUids);
        if (response != null) {
            return response;
        }

        if (taking.getDelete() == null || taking.getDelete() != 0)
            return ServerResponse.createSuccess("客商未撤单，司机主动撤单");
        return ServerResponse.createSuccess("司机撤单成功");
    }

    @Override
    public ServerResponse<String> saveDeleteOrder2(String oid) {
        String did = ShiroUtils.getUserId();
        //todo:运输后的订单，不再允许司机同意撤销订单
        OrderTaking taking = orderTakingService.get("oid", oid);
        if (taking == null) return ServerResponse.createError("订单编号错误！或客商已废除订单");
        if (taking.getFinishTag() != 0) return ServerResponse.createError("货物在运输中，无法撤消！");

        Order order = orderService.get("oid", oid);
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("订单车辆已进场，不可撤单");
        //orderTakingService.delete(oid);
        if (order.getDelete()) return ServerResponse.createError("该订单已经废除");
        Goods goods = goodsService.get("gid", order.getGid());
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);
        String[] outUids = new String[3];
        if (StrUtil.isNotEmpty(order.getOutUnitCode()))
            outUids = new String[]{order.getPayerIdOut(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
        String[] inUids = new String[3];
        //TODO: ligx
        //if (StrUtil.isNotEmpty(order.getOutUnitCode()))
        if (StrUtil.isNotEmpty(order.getInUnitCode()))
            inUids = new String[]{order.getPayerIdIn(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};

        int switchCheck = 0;// 废除订单，订单中的退款默认是审核的
        int delType;
        Query<SysSwitch> switchQuery = sysSwitchService.createQuery();
        switchQuery.filter("code", 1);
        switchQuery.filter("type", 2);
        if (taking.getDelete() == null || taking.getDelete() != 0) {    //司机主动废除订单
            delType = 1;
            switchQuery.filter("refundType", 1);
        } else {    //司机同意客商废除订单
            delType = 3;
            switchQuery.filter("refundType", 0);
        }
        SysSwitch sysSwitch = sysSwitchService.get(switchQuery);    // 司机退款是否需要审核
        if (sysSwitch != null && StrUtil.isNotEmpty(sysSwitch.getCheck()) && sysSwitch.getCheck() == 1) switchCheck = 3;

        ServerResponse<String> response = refundOrder2(taking, order, switchCheck, delType, outUids, inUids);
        if (response != null) { //订单废除失败
            return response;
        }

        //司机撤单，客商消息列表添加消息
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        CusMessage cMsg = new CusMessage(goods.getCid(), oid, "司机撤单", "司机成功撤单", did, driverInfo.getMobile(), order.getCarNum(), false, 0);
        cusMessageDao.save(cMsg);
        if (StrUtil.isNotEmpty(goods.getShareCid())) {
            CusMessage shareCMsg = new CusMessage(goods.getShareCid(), oid, "司机撤单", "司机成功撤单", did, driverInfo.getMobile(), order.getCarNum(), false, 0);
            cusMessageDao.save(shareCMsg);
        }

        if (taking.getDelete() == null || taking.getDelete() != 0)
            return ServerResponse.createSuccess("客商未撤单，司机主动撤单");
        return ServerResponse.createSuccess("司机撤单成功");
    }

    @Override
    public ServerResponse<String> saveDeleteOrder3(String oid) {
        String did = ShiroUtils.getUserId();
        //todo:运输后的订单，不再允许司机同意撤销订单
        OrderTaking taking = orderTakingService.get("oid", oid);
        if (taking == null) return ServerResponse.createError("订单编号错误！或客商已废除订单");
        if (taking.getFinishTag() != 0) return ServerResponse.createError("货物在运输中，无法撤消！");

        Order order = orderService.get("oid", oid);
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("订单车辆已进场，不可撤单");

        if (order.getDelete()) return ServerResponse.createError("该订单已经废除");
        Goods goods = goodsService.get("gid", order.getGid());
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);

        Query<SysSwitch> query = sysSwitchService.createQuery();
        query.filter("code", 3); //订单
        SysSwitch sysSwitch = sysSwitchService.get(query);
        boolean pfIsRefund = sysSwitch == null || StrUtil.isEmpty(sysSwitch.getIsRefund()) || sysSwitch.getIsRefund() == 0; //true-表示撤单平台会退款
        boolean sysIsRefund = true; //true-表示撤单，单位会退款
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsDelOrderBackFee()) && sysUnits[0].getIsDelOrderBackFee() == 1)
            sysIsRefund = false;
        if (StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsDelOrderBackFee()) && sysUnits[2].getIsDelOrderBackFee() == 1)
            sysIsRefund = false;

        ServerResponse<String> response = refundOrder3(taking, order, pfIsRefund, pfIsRefund, sysUnits, sysIsRefund, sysIsRefund);
        if (response != null) { //订单废除失败
            return response;
        }

        //司机撤单，客商消息列表添加消息
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        CusMessage cMsg = new CusMessage(goods.getCid(), oid, "司机撤单", "司机成功撤单", did, driverInfo.getMobile(), order.getCarNum(), false, 0);
        cusMessageDao.save(cMsg);
        if (StrUtil.isNotEmpty(goods.getShareCid())) {
            CusMessage shareCMsg = new CusMessage(goods.getShareCid(), oid, "司机撤单", "司机成功撤单", did, driverInfo.getMobile(), order.getCarNum(), false, 0);
            cusMessageDao.save(shareCMsg);
        }

        if (taking.getDelete() == null || taking.getDelete() != 0)
            return ServerResponse.createSuccess("客商未撤单，司机主动撤单");
        return ServerResponse.createSuccess("司机撤单成功");
    }

    @Override
    public ServerResponse<String> saveDeleteOrder4(String oid) {
        String did = ShiroUtils.getUserId();
        //todo:运输后的订单，不再允许司机同意撤销订单
        OrderTaking taking = orderTakingService.get("oid", oid);
        if (taking == null) return ServerResponse.createError("订单编号错误！或客商已废除订单");
        if (taking.getFinishTag() != 0) return ServerResponse.createError("货物在运输中，无法撤消！");

        Order order = orderService.get("oid", oid);
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            return ServerResponse.createError("订单车辆已进场，不可撤单");

        if (order.getDelete()) return ServerResponse.createError("该订单已经废除");
        Goods goods = goodsService.get("gid", order.getGid());
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(goods);

        //回调请求企业
        Map<String, String> resMap = revokeDeliveryOrder(order, sysUnits);
        if (ObjectUtil.isNotEmpty(resMap)) {
            //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
            String outSynMsg = resMap.get("revokeDeliveryOutOrderMsg");
            String inSynMsg = resMap.get("revokeDeliveryInOrderMsg");
            return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
        }

        /*
        2024-05-24 修改，订单作废，平台费用退费开关 跟单位走
        Query<SysSwitch> query = sysSwitchService.createQuery();
        query.filter("code", 3); //订单
        SysSwitch sysSwitch = sysSwitchService.get(query);
        boolean pfIsRefund = sysSwitch == null || StrUtil.isEmpty(sysSwitch.getIsRefund()) || sysSwitch.getIsRefund() == 0; //true-表示撤单平台会退款*/
        boolean sysIsRefund = true; //true-表示撤单，单位会退款
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsDelOrderBackFee()) && sysUnits[0].getIsDelOrderBackFee() == 1)
            sysIsRefund = false;
        if (StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsDelOrderBackFee()) && sysUnits[2].getIsDelOrderBackFee() == 1)
            sysIsRefund = false;
        boolean pfIsRefund = sysIsRefund;
        ServerResponse<String> response = refundOrder4(taking, order, pfIsRefund, pfIsRefund, sysUnits, sysIsRefund, sysIsRefund);
        if (response != null) { //订单废除失败
            return response;
        }

        //司机撤单，客商消息列表添加消息
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        CusMessage cMsg = new CusMessage(goods.getCid(), oid, "司机撤单", "司机成功撤单", did, driverInfo.getMobile(), order.getCarNum(), false, 0);
        cusMessageDao.save(cMsg);
        if (StrUtil.isNotEmpty(goods.getShareCid())) {
            CusMessage shareCMsg = new CusMessage(goods.getShareCid(), oid, "司机撤单", "司机成功撤单", did, driverInfo.getMobile(), order.getCarNum(), false, 0);
            cusMessageDao.save(shareCMsg);
        }

        if (taking.getDelete() == null || taking.getDelete() != 0)
            return ServerResponse.createSuccess("客商未撤单，司机主动撤单");
        return ServerResponse.createSuccess("司机撤单成功");
    }

    /**
     * 司机撤单，回调通知企业
     * */
    private Map<String, String> revokeDeliveryOrder(Order order, SysUnit[] sysUnits) {
        Map<String, String> msgMap = new HashMap<>();

        CompletableFuture outMsg = new CompletableFuture<>();
        CompletableFuture inMsg = new CompletableFuture<>();

        if (StrUtil.isNotEmpty(order.getOutUnitCode()) && StrUtil.isNotEmpty(sysUnits[0].getDelivery()) && sysUnits[0].getDelivery() == 1) {
            Map<String, Object> map = new HashMap<>();
            map.put("billCode", order.getOutBillCode());
            outMsg = companyOrderService.queryCompanyRevokeDeliveryOrder(sysUnits[0].getIp(), map);
        }
        if (StrUtil.isNotEmpty(order.getInUnitCode()) && StrUtil.isNotEmpty(sysUnits[2].getDelivery()) && sysUnits[2].getDelivery() == 1) {
            Map<String, Object> map = new HashMap<>();
            map.put("billCode", order.getInBillCode());
            inMsg = companyOrderService.queryCompanyRevokeDeliveryOrder(sysUnits[2].getIp(), map);
        }

        if (StrUtil.isNotEmpty(order.getOutUnitCode()) && StrUtil.isNotEmpty(sysUnits[0].getDelivery()) && sysUnits[0].getDelivery() == 1) {
            try {
                String errMsg = (String) outMsg.get();    //get 方法会使当前线程阻塞,并且等待直到 future 完成,并且将返回 future 的值
                if (StrUtil.isNotEmpty(errMsg)) msgMap.put("revokeDeliveryOutOrderMsg", order.getOutUnitName() + "," + errMsg);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }
        if (StrUtil.isNotEmpty(order.getInUnitCode()) && StrUtil.isNotEmpty(sysUnits[2].getDelivery()) && sysUnits[2].getDelivery() == 1) {
            try {
                String errMsg = (String) inMsg.get();    //get 方法会使当前线程阻塞,并且等待直到 future 完成,并且将返回 future 的值
                if (StrUtil.isNotEmpty(errMsg)) msgMap.put("revokeDeliveryInOrderMsg", order.getOutUnitName() + "," + errMsg);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }

        return msgMap;
    }

    @Override
    public ServerResponse<String> submitAppointment(String oid, Long startTime, Long endTime) {
        Order order = orderService.get("oid", oid);
        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        if (order == null || orderTaking == null || order.getDelete())
            return ServerResponse.createError("参数错误！");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，稍后重试");
        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("客商禁止了订单运输");

        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        if (StrUtil.isEmpty(driverOrderData.getAppointment())) return ServerResponse.createError("参数错误!！");
        if (driverOrderData.getAppointment() == 1) {
            SysUnit sysUnit = sysUnitService.get("code", driverOrderData.getaSubCode());
            SysUnit sysUnitP = sysUnitService.get("code", sysUnit.getpCode());

            //调用业务系统接口，提交预约
            String msg = contractService.saveRushDriver(sysUnitP.getIp(), driverOrderData.getaSubCode(), order, startTime, endTime);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

            //调用预约接口成功后，修改orderTaking的sa字段，添加预约时间段。
            int sa = 0;//发货单位预约成功后sa改为1，收货单位预约成功后sa改为2
            if (driverOrderData.getaSubCode().equals(order.getOutDefaultDownUnit())) {
                sa = 1;
            } else if (driverOrderData.getaSubCode().equals(order.getInDefaultDownUnit())) {
                sa = 2;
            }
            Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", orderTaking.getOid());
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations().set("sa", sa);
            otUpdateOperations.set("aStartTime", startTime);
            otUpdateOperations.set("aEndTime", endTime);
            orderTakingService.update(otQuery, otUpdateOperations);
            return ServerResponse.createSuccess("预约成功");
        } else {
            return ServerResponse.createSuccess("重复预约");
        }
    }

    @Override
    public ServerResponse<Object> searchQueue(String oid) {
        Order order = orderService.get("oid", oid);
        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        if (order == null || orderTaking == null || order.getDelete())
            return ServerResponse.createError("参数错误！");
        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("请联系客商，恢复订单运输！");

        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        if (StrUtil.isEmpty(driverOrderData.getAppointment())) return ServerResponse.createError("参数错误!！");
        if (driverOrderData.getAppointment() == 0) return ServerResponse.createError("不需要预约");
        if (driverOrderData.getAppointment() == 1)
            return ServerResponse.createError("司机未在" + driverOrderData.getaSubName() + "预约");

        SysUnit sysUnit = sysUnitService.get("code", driverOrderData.getaSubCode());
        String billCode;
        if (driverOrderData.getaSubCode().equals(order.getOutDefaultDownUnit())) {
            billCode = order.getOutBillCode();
        } else if (driverOrderData.getaSubCode().equals(order.getInDefaultDownUnit())) {
            billCode = order.getInBillCode();
        } else {
            return ServerResponse.createError("参数错误");
        }

        return contractService.getQueuingReservation(sysUnit.getpCode(), billCode);
    }

    /**
     * 司机撤单
     * 1.客商，单位收取或扣除 的信息费退回
     * 2.司机端：app接单退回账户余额，微信小程序接单原路退回
     */
    @Transactional
    ServerResponse<String> refundOrder(OrderTaking taking, Order order, Integer switchCheck, Integer delType, String[] outUids, String[] inUids) {
        String oid = order.getOid();
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> pushInfoCollection = pushInfoService.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();

        Date date = new Date();
        try {
            clientSession.startTransaction();

            if (switchCheck == 1 || switchCheck == 3) {    // 审核通过 和 无需审核，退款
                //TODO:1.计算划分订单中个款项明细
                int cusFees = order.getFees();          // 客商代付平台信息金额
                String cusPayerId = order.getPayerId(); // 客商id

                int fees1Out = order.getFees1Out(); // 发货一级单位收取代扣费
                String payerId1Out = order.getPayerId1Out();// 支付人id（客商或司机）
                int fees1In = order.getFees1In();   // 收货一级单位收取代扣费
                String payerId1In = order.getPayerId1In();// 支付人id（客商或司机）
                int fees2Out = order.getFees2Out(); // 发货二级单位收取代扣费
                String payerId2Out = order.getPayerId2Out();// 支付人id（客商或司机）
                int fees2In = order.getFees2In();   // 收货二级单位收取代扣费
                String payerId2In = order.getPayerId2In();// 支付人id（客商或司机）

                int balanceFees = order.getBalanceFees();// 司机付款平台信息费金额
                int shareFees = order.getShareFees();   // 司机支付给客（友）商的费用
                String driverPayerId = probePayId(order.getDriverPayerId(), payerId1Out, payerId2Out, payerId1In, payerId2In);// 司机id

                int customerFees = 0;//客商在订单中可能代付单位代扣项的总费用
                int driverFees = 0;//司机在订单中支付的总费用
                if (distinguishPayerIsCusOrDri(payerId1Out, payerId2Out, payerId1In, payerId2In)) {
                    customerFees += (fees1Out + fees2Out + fees1In + fees2In);
                } else {
                    driverFees += (fees1Out + fees2Out + fees1In + fees2In);
                }
                if (delType == 3 && balanceFees > 0) driverFees += balanceFees;
                if (shareFees > 0) driverFees += shareFees;

                //TODO: 2.司机付款金额分两种
                if (driverFees > 0) {
                    //TODO: 2.1 app下单，平台信息费+友商或客商信息费+收发单位信息费 退回司机账户
                    if (order.getIsWeChat() == 0) {
                        dAccountCollection.insertOne(clientSession, driverAccountService.createDriAccountDoc(driverPayerId, driverPayerId, new BigDecimal(driverFees), 3, oid, date));
                    } else {
                        //TODO: 2.2 给小程序司机下单,退回微信，并更新order表
                        MongoCollection<Document> resultCollection = wxResultService.getCollection();
                        MongoCollection<Document> prepaidCollection = wxPrepaidService.getCollection();

                        Query<WxPrepaid> prepaidQuery = wxPrepaidService.createQuery();

                        Map<String, String> map = new HashMap<>();
                        String outTradeNo = order.getOutTradeNo();
                        String transactionId = order.getTransactionId();

                        WxPrepaid wxPrepaid = new WxPrepaid();
                        wxPrepaid.setDid(driverPayerId);
                        wxPrepaid.setType(2);//退款

                        if (StrUtil.isNotEmpty(outTradeNo)) {
                            map.put("out_trade_no", outTradeNo);
                            prepaidQuery.filter("outTradeNo", outTradeNo);
                            wxPrepaid.setOutTradeNo(date.getTime() + WXPayUtil.generateNonceStr(19));
                        } else if (StrUtil.isNotEmpty(transactionId)) {
                            map.put("transaction_id", transactionId);
                            prepaidQuery.filter("transactionId", transactionId);
                            wxPrepaid.setTransactionId(transactionId);
                        }
                        wxPrepaid.setAppid(config.getAppID());
                        wxPrepaid.setMchId(config.getMchID());
                        wxPrepaid.setOutRefundNo(order.getOid());//商户退款单号
                        wxPrepaid.setTotalFee(driverFees);//订单金额
                        wxPrepaid.setRefundFee(driverFees);//退款金额

                        wxPrepaid.setUpdateTime(date.getTime());

                        map.put("out_refund_no", order.getOid());//商户退款单号
                        map.put("total_fee", String.valueOf(driverFees));//订单金额
                        map.put("refund_fee", String.valueOf(driverFees));//退款金额
                        if (delType == 1) {
                            map.put("refund_desc", "司机主动撤单");
                            wxPrepaid.setRefundDesc("司机主动撤单");//退款原因
                        } else {
                            map.put("refund_desc", "客商撤单");
                            wxPrepaid.setRefundDesc("客商撤单");//退款原因
                        }

                        prepaidQuery.filter("outRefundNo", order.getOid());
                        prepaidQuery.filter("pay", true);
                        //根据“退款单号，微信订单号，商户订单号”判断订单是否多次重复提交
                        WxPrepaid prepaid = wxPrepaidService.get(prepaidQuery);
                        if (prepaid != null) {
                            clientSession.abortTransaction();
                            return ServerResponse.createSuccess("该订单已经退款");
                        }

                        WXPay wxpay = new WXPay(config, "", true, false);

                        map = wxpay.refund(map);//退款
                        String returnCode = map.get("return_code");

                        if (returnCode.equals("SUCCESS")) {

                            String resultCode = map.get("result_code");
                            if (resultCode.equals("SUCCESS")) {
                                //预支付表
                                wxPrepaid.setNonceStr(map.get("nonce_str"));
                                wxPrepaid.setSign(map.get("sign"));
                                wxPrepaid.setPay(true);
                                wxPrepaid.setTransactionId(map.get("transaction_id"));
                                Document prepaidDoc = Document.parse(JSONObject.toJSONString(wxPrepaid));
                                prepaidDoc.append("createTime", date);
                                prepaidCollection.insertOne(clientSession, prepaidDoc);

                                //支付结果保存
                                WxResult wxResult = new WxResult();
                                wxResult.setOutTradeNo(map.get("out_trade_no"));
                                wxResult.setTransactionId(map.get("transaction_id"));
                                wxResult.setSign(map.get("sign"));
                                wxResult.setNonceStr(map.get("nonce_str"));
                                wxResult.setOutRefundNo(map.get("out_refund_no"));//商户退款单号
                                wxResult.setRefundId(map.get("refund_id"));//微信退款单号
                                wxResult.setRefundFee(Utils.parseInt(map.get("refund_fee"), 0));//退款总金额
                                wxResult.setSettlementRefundFee(Utils.parseInt(map.get("settlement_refund_fee"), 0));//应结退款金额
                                wxResult.setTotalFee(Utils.parseInt(map.get("total_fee"), 0));
                                wxResult.setSettlementTotalFee(Utils.parseInt(map.get("settlement_total_fee"), 0));//应结订单金额
                                wxResult.setFeeType(map.get("fee_type"));
                                wxResult.setCashFee(Utils.parseInt(map.get("cash_fee"), 0));
                                wxResult.setCashFeeType(map.get("cash_fee_type"));
                                wxResult.setCashRefundFee(Utils.parseInt(map.get("cash_refund_fee"), 0));
                                wxResult.setResultCode(resultCode);
                                wxResult.setReturnCode(returnCode);
                                wxResult.setUpdateTime(date.getTime());
                                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                                document.append("createTime", date);
                                resultCollection.insertOne(clientSession, document);//保存支付成功结果

//                            return ServerResponse.createSuccess("审核通过，零钱支付的退款20分钟内到账，银行卡支付的退款3个工作日");
                            } else {
                                clientSession.abortTransaction();
                                return ServerResponse.createError(map.get("err_code_des"));
                            }
                        } else {
                            clientSession.abortTransaction();
                            return ServerResponse.createError(map.get("return_msg"));
                        }
                    }
                }

                //TODO: 3.退客商代付的钱，客商退收司机的信息费
                List<Document> cusAccountDocs = new ArrayList<>();
                if ((cusFees + customerFees) > 0) {
                    String cid = probePayId(cusPayerId, payerId1Out, payerId2Out, payerId1In, payerId2In);
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(cusFees + customerFees), 5, oid, date));
                }
                if (shareFees > 0)
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(taking.getCid(), new BigDecimal(-shareFees), 9, oid, date));
                if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);

                //TODO: 4.退单位代付的钱，单位退代扣的钱
                List<Document> sysUnitAccountDocs = new ArrayList<>();
                String[] outUserIds = {order.getPayerIdOut(), payerId1Out, payerId2Out};
                String[] inUserIds = {order.getPayerIdIn(), payerId1In, payerId2In};
                int[] types = {3, 5};   //3-退单位代付的钱，5-单位退出代扣的钱
                int[] outFees = {order.getFeesOut(), -fees1Out, -fees2Out};
                int[] inFees = {order.getFeesIn(), -fees1In, -fees2In};
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, oid, outFees, date);
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, oid, inFees, date);
                if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);

                //TODO：5.平台信息费退回
                List<Document> pfAccountDocs = new ArrayList<>();
                if (cusFees > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(cusPayerId, new BigDecimal(-cusFees), 12, oid, date));
                if (order.getFeesOut() > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-order.getFeesOut()), 11, oid, date));
                if (order.getFeesIn() > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-order.getFeesIn()), 11, oid, date));
                if (delType == 3 && balanceFees > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(driverPayerId, new BigDecimal(-balanceFees), 13, oid, date));
                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            }

            //orderTaking，pushInfo,orderLogistics表中删除记录
            Document filter = new Document("oid", oid);
            orderTakingCollection.deleteMany(clientSession, filter);
            pushInfoCollection.deleteOne(clientSession, filter);
            orderLogisticsCollection.deleteOne(clientSession, filter);
            //更新order表中删除标记
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("delete", true);
            orderMap.put("delType", delType);
            orderMap.put("isCheck", switchCheck);
            orderMap.put("updateTime", date.getTime());
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$set", orderMap);
            orderCollection.updateOne(clientSession, new Document("oid", oid), BsonDocument.parse(JSON.toJSONString(updMap)));//格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

            clientSession.commitTransaction();
            return null;
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("撤单失败，请重试");
        } finally {
            clientSession.close();
        }
    }

    @Transactional
    ServerResponse<String> refundOrder2(OrderTaking taking, Order order, Integer switchCheck, Integer delType, String[] outUids, String[] inUids) {
        String oid = order.getOid();
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> pushInfoCollection = pushInfoService.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        Date date = new Date();
        try {
            clientSession.startTransaction();

            if (switchCheck == 1 || switchCheck == 3) {    // 审核通过 和 无需审核，退款
                //TODO:1.计算划分订单中个款项明细
                int cusFees = order.getFees();          // 客商代付平台信息金额
                String cusPayerId = order.getPayerId(); // 客商id

                int fees1Out = order.getFees1Out(); // 发货一级单位收取代扣费
                String payerId1Out = order.getPayerId1Out();// 支付人id（客商或司机）
                int fees1In = order.getFees1In();   // 收货一级单位收取代扣费
                String payerId1In = order.getPayerId1In();// 支付人id（客商或司机）
                int fees2Out = order.getFees2Out(); // 发货二级单位收取代扣费
                String payerId2Out = order.getPayerId2Out();// 支付人id（客商或司机）
                int fees2In = order.getFees2In();   // 收货二级单位收取代扣费
                String payerId2In = order.getPayerId2In();// 支付人id（客商或司机）

                int balanceFees = order.getBalanceFees();// 司机付款平台信息费金额
                int shareFees = order.getShareFees();   // 司机支付给客（友）商的费用
                String driverPayerId = probePayId(order.getDriverPayerId(), payerId1Out, payerId2Out, payerId1In, payerId2In);// 司机id

                int customerFees = 0;//客商在订单中可能代付单位代扣项的总费用
                int driverFees = 0;//司机在订单中支付的总费用
                if (distinguishPayerIsCusOrDri(payerId1Out, payerId2Out, payerId1In, payerId2In)) {
                    customerFees += (fees1Out + fees2Out + fees1In + fees2In);
                } else {
                    driverFees += (fees1Out + fees2Out + fees1In + fees2In);
                }
                if (delType == 3 && balanceFees > 0) driverFees += balanceFees;
                if (shareFees > 0) driverFees += shareFees;

                //TODO: 2.司机付款金额分两种
                if (driverFees > 0) {
                    //TODO: 2.1 app下单，平台信息费+友商或客商信息费+收发单位信息费 退回司机账户
                    if (order.getIsWeChat() == 0) {
                        dAccountCollection.insertOne(clientSession, driverAccountService.createDriAccountDoc(driverPayerId, driverPayerId, new BigDecimal(driverFees), 3, oid, date));
                    } else {
                        //TODO: 2.2 给小程序司机下单,退回微信，并更新order表
                        MongoCollection<Document> resultCollection = wxResultService.getCollection();
                        MongoCollection<Document> prepaidCollection = wxPrepaidService.getCollection();

                        Query<WxPrepaid> prepaidQuery = wxPrepaidService.createQuery();

                        Map<String, String> map = new HashMap<>();
                        String outTradeNo = order.getOutTradeNo();
                        String transactionId = order.getTransactionId();

                        WxPrepaid wxPrepaid = new WxPrepaid();
                        wxPrepaid.setDid(driverPayerId);
                        wxPrepaid.setType(2);//退款

                        if (StrUtil.isNotEmpty(outTradeNo)) {
                            map.put("out_trade_no", outTradeNo);
                            prepaidQuery.filter("outTradeNo", outTradeNo);
                            wxPrepaid.setOutTradeNo(date.getTime() + WXPayUtil.generateNonceStr(19));
                        } else if (StrUtil.isNotEmpty(transactionId)) {
                            map.put("transaction_id", transactionId);
                            prepaidQuery.filter("transactionId", transactionId);
                            wxPrepaid.setTransactionId(transactionId);
                        }
                        wxPrepaid.setAppid(config.getAppID());
                        wxPrepaid.setMchId(config.getMchID());
                        wxPrepaid.setOutRefundNo(order.getOid());//商户退款单号
                        wxPrepaid.setTotalFee(driverFees);//订单金额
                        wxPrepaid.setRefundFee(driverFees);//退款金额

                        wxPrepaid.setUpdateTime(date.getTime());

                        map.put("out_refund_no", order.getOid());//商户退款单号
                        map.put("total_fee", String.valueOf(driverFees));//订单金额
                        map.put("refund_fee", String.valueOf(driverFees));//退款金额
                        if (delType == 1) {
                            map.put("refund_desc", "司机主动撤单");
                            wxPrepaid.setRefundDesc("司机主动撤单");//退款原因
                        } else {
                            map.put("refund_desc", "客商撤单");
                            wxPrepaid.setRefundDesc("客商撤单");//退款原因
                        }

                        prepaidQuery.filter("outRefundNo", order.getOid());
                        prepaidQuery.filter("pay", true);
                        //根据“退款单号，微信订单号，商户订单号”判断订单是否多次重复提交
                        WxPrepaid prepaid = wxPrepaidService.get(prepaidQuery);
                        if (prepaid != null) {
                            clientSession.abortTransaction();
                            return ServerResponse.createSuccess("该订单已经退款");
                        }

                        WXPay wxpay = new WXPay(config, "", true, false);

                        map = wxpay.refund(map);//退款
                        String returnCode = map.get("return_code");

                        if (returnCode.equals("SUCCESS")) {

                            String resultCode = map.get("result_code");
                            if (resultCode.equals("SUCCESS")) {
                                //预支付表
                                wxPrepaid.setNonceStr(map.get("nonce_str"));
                                wxPrepaid.setSign(map.get("sign"));
                                wxPrepaid.setPay(true);
                                wxPrepaid.setTransactionId(map.get("transaction_id"));
                                Document prepaidDoc = Document.parse(JSONObject.toJSONString(wxPrepaid));
                                prepaidDoc.append("createTime", date);
                                prepaidCollection.insertOne(clientSession, prepaidDoc);

                                //支付结果保存
                                WxResult wxResult = new WxResult();
                                wxResult.setOutTradeNo(map.get("out_trade_no"));
                                wxResult.setTransactionId(map.get("transaction_id"));
                                wxResult.setSign(map.get("sign"));
                                wxResult.setNonceStr(map.get("nonce_str"));
                                wxResult.setOutRefundNo(map.get("out_refund_no"));//商户退款单号
                                wxResult.setRefundId(map.get("refund_id"));//微信退款单号
                                wxResult.setRefundFee(Utils.parseInt(map.get("refund_fee"), 0));//退款总金额
                                wxResult.setSettlementRefundFee(Utils.parseInt(map.get("settlement_refund_fee"), 0));//应结退款金额
                                wxResult.setTotalFee(Utils.parseInt(map.get("total_fee"), 0));
                                wxResult.setSettlementTotalFee(Utils.parseInt(map.get("settlement_total_fee"), 0));//应结订单金额
                                wxResult.setFeeType(map.get("fee_type"));
                                wxResult.setCashFee(Utils.parseInt(map.get("cash_fee"), 0));
                                wxResult.setCashFeeType(map.get("cash_fee_type"));
                                wxResult.setCashRefundFee(Utils.parseInt(map.get("cash_refund_fee"), 0));
                                wxResult.setResultCode(resultCode);
                                wxResult.setReturnCode(returnCode);
                                wxResult.setUpdateTime(date.getTime());
                                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                                document.append("createTime", date);
                                resultCollection.insertOne(clientSession, document);//保存支付成功结果

//                            return ServerResponse.createSuccess("审核通过，零钱支付的退款20分钟内到账，银行卡支付的退款3个工作日");
                            } else {
                                clientSession.abortTransaction();
                                return ServerResponse.createError(map.get("err_code_des"));
                            }
                        } else {
                            clientSession.abortTransaction();
                            return ServerResponse.createError(map.get("return_msg"));
                        }
                    }
                }

                //TODO: 3.退客商代付的钱，客商退收司机的信息费
                List<Document> cusAccountDocs = new ArrayList<>();
                if ((cusFees + customerFees) > 0) {
                    String cid = probePayId(cusPayerId, payerId1Out, payerId2Out, payerId1In, payerId2In);
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(cusFees + customerFees), 5, oid, date));
                }
                if (shareFees > 0)
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(taking.getCid(), new BigDecimal(-shareFees), 9, oid, date));
                if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);

                //TODO: 4.退单位代付的钱，单位退代扣的钱
                List<Document> sysUnitAccountDocs = new ArrayList<>();
                String[] outUserIds = {order.getPayerIdOut(), payerId1Out, payerId2Out};
                String[] inUserIds = {order.getPayerIdIn(), payerId1In, payerId2In};
                int[] types = {3, 5};   //3-退单位代付的钱，5-单位退出代扣的钱
                int[] outFees = {order.getFeesOut(), -fees1Out, -fees2Out};
                int[] inFees = {order.getFeesIn(), -fees1In, -fees2In};
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, oid, outFees, date);
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, oid, inFees, date);
                if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);

                //TODO：5.平台信息费退回
                List<Document> pfAccountDocs = new ArrayList<>();
                if (cusFees > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(cusPayerId, new BigDecimal(-cusFees), 12, oid, date));
                if (order.getFeesOut() > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-order.getFeesOut()), 11, oid, date));
                if (order.getFeesIn() > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-order.getFeesIn()), 11, oid, date));
                if (delType == 3 && balanceFees > 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(driverPayerId, new BigDecimal(-balanceFees), 13, oid, date));
                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            }

            //orderTaking，pushInfo,orderLogistics表中删除记录
            Document filter = new Document("oid", oid);
            orderTakingCollection.deleteMany(clientSession, filter);
            pushInfoCollection.deleteOne(clientSession, filter);
            orderLogisticsCollection.deleteOne(clientSession, filter);
            //更新order表中删除标记
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("delete", true);
            orderMap.put("delType", delType);
            orderMap.put("isCheck", switchCheck);
            orderMap.put("updateTime", date.getTime());
            orderMap.put("tranStatus", 6);
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$set", orderMap);
            orderCollection.updateOne(clientSession, new Document("oid", oid), BsonDocument.parse(JSON.toJSONString(updMap)));//格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", 1);
            param.put("orderNum1", -1);
            Map<String, Object> upgMap = new HashMap<>();
            upgMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", order.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(taking.getDid())), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
            return null;
        } catch (Exception e) {
            clientSession.abortTransaction();
            logger.error("refundOrder2" + e.getMessage());
            return ServerResponse.createError("撤单失败");
        } finally {
            clientSession.close();
        }
    }

    @Transactional
    ServerResponse<String> refundOrder3(OrderTaking taking, Order order, boolean pfIsRefundOut, boolean pfIsRefundIn, SysUnit[] sysUnits, boolean sysIsRefundOut, boolean sysIsRefundIn) {
        String oid = order.getOid();
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> pushInfoCollection = pushInfoService.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();

        Date time = new Date();
        try {
            clientSession.startTransaction();

            //不涉及三方费用的时候，订单废除退款
            if ((StrUtil.isEmpty(order.getOutFee5()) || order.getOutFee5() <= 0) && (StrUtil.isEmpty(order.getInFee5()) || order.getInFee5() <= 0)) {
                //订单退款不需要审核，平台会配置撤单是否退费、收发货单位会配置撤单是否退费，
                List<Document> dvrAccountDocs = new ArrayList<>();
                List<Document> cusAccountDocs = new ArrayList<>();
                List<Document> sysUnitAccountDocs = new ArrayList<>();
                List<Document> pfAccountDocs = new ArrayList<>();
                List<Document> thirdPartyAccountDocs = new ArrayList<>();
                int dvrFee = 0; //订单中需要退还司机的总金额
                //平台信息费 退回，同时平台账户金额核减
                if (pfIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer1())) { //平台收取发货单位的信息费，符合条件则退回
                    String outPayer1 = order.getOutPayer1();
                    int outFee0 = order.getOutFee0();
                    switch (order.getOutPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer1, new BigDecimal(outFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 12, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + outFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 13, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(outPayer1, null, 3, order.getOid(), outFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                if (pfIsRefundIn && StrUtil.isNotEmpty(order.getInPayer1())) { //平台收取收货单位的信息费，符合条件则退回
                    String inPayer1 = order.getInPayer1();
                    int inFee0 = order.getInFee0();
                    switch (order.getInPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer1, new BigDecimal(inFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + inFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(inPayer1, null, 3, order.getOid(), inFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                //单位收取的代扣费 退回，同时单位账户金额核减
                if (sysIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer2())) {   //发货单位收取的代扣费，符合条件则退回
                    String outPayer2 = order.getOutPayer2();
                    int outFee1 = order.getOutFee1();
                    int outFee2 = order.getOutFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer2, new BigDecimal(outFee1 + outFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + outFee1 + outFee2;
                            break;
                        default:
                            break;
                    }

                    if (outFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), null, 5, order.getOid(), -outFee1, time));
                    if (outFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), null, 5, order.getOid(), -outFee2, time));
                }
                if (sysIsRefundIn && StrUtil.isNotEmpty(order.getInPayer2())) {   //收货单位收取的代扣费，符合条件则退回
                    String inPayer2 = order.getInPayer2();
                    int inFee1 = order.getInFee1();
                    int inFee2 = order.getInFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer2, new BigDecimal(inFee1 + inFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + inFee1 + inFee2;
                            break;
                        default:
                            break;
                    }

                    if (inFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), null, 5, order.getOid(), -inFee1, time));
                    if (inFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), null, 5, order.getOid(), -inFee2, time));
                }
                //客商或友商和第三方收取的信息费 退回司机,同时客商或友商和第三方账户金额核减
                if (StrUtil.isNotEmpty(order.getOutPayer3()) || StrUtil.isNotEmpty(order.getInPayer3())) {
//                String did = order.getOutPayer3();
                    int cusFee = order.getOutFee3() + order.getOutFee4();
                    int outFee5 = order.getOutFee5();
                    int inFee5 = order.getInFee5();
//                dvrAccountDocs.add(driverAccountService.createDriAccountDoc(did, null, new BigDecimal(dvrFee + cusFee + outFee5 + inFee5), 3, order.getOid(), time));
                    dvrFee = dvrFee + cusFee + outFee5 + inFee5;
                    Goods goods = goodsService.get("gid", order.getGid());
                    if (cusFee > 0) {
                        String cid = goods.getShare() == 2 ? goods.getShareCid() : goods.getCid();
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(-cusFee), 9, order.getOid(), time));
                    }
                    if (outFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), null, -outFee5, 1, order.getOid(), time));
                    //                                                                                          (goods.getFee5OutId(), null, -outFee5, 1, order.getOid(), time));
                    if (inFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[2].getThirdPartyName(), null, -inFee5, 1, order.getOid(), time));
                    //                                                                                          (goods.getFee5InId(), null, -inFee5, 1, order.getOid(), time));
                }

                if (dvrFee > 0)
                    dvrAccountDocs.add(driverAccountService.createDriAccountDoc(order.getDid(), null, new BigDecimal(dvrFee), 3, order.getOid(), time));

                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
                if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
                if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);
                if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
                if (thirdPartyAccountDocs.size() > 0)
                    thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);
            }
            //orderTaking，pushInfo,orderLogistics表中删除记录
            Document filter = new Document("oid", oid);
            orderTakingCollection.deleteMany(clientSession, filter);
            pushInfoCollection.deleteOne(clientSession, filter);
            orderLogisticsCollection.deleteOne(clientSession, filter);
            //更新order表中删除标记
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("delete", true);
            if (taking.getDelete() == null || taking.getDelete() != 0) {    //司机主动废除订单
                orderMap.put("delType", 1);//delType = 1;
            } else {    //司机同意客商废除订单
                orderMap.put("delType", 3);//delType = 3;
            }
            orderMap.put("isCheck", 3); //无需审核
            orderMap.put("updateTime", time.getTime());
            orderMap.put("tranStatus", 6);
            orderMap.put("time6", time);    //订单作废时间
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$set", orderMap);
            orderCollection.updateOne(clientSession, new Document("oid", oid), BsonDocument.parse(JSON.toJSONString(updMap)));//格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", 1);
            param.put("orderNum1", -1);
            Map<String, Object> upgMap = new HashMap<>();
            upgMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", order.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(taking.getDid())), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
            return null;
        } catch (Exception e) {
            clientSession.abortTransaction();
            logger.error("refundOrder2" + e.getMessage());
            return ServerResponse.createError("撤单失败");
        } finally {
            clientSession.close();
        }
    }

    @Transactional
    ServerResponse<String> refundOrder4(OrderTaking taking, Order order, boolean pfIsRefundOut, boolean pfIsRefundIn, SysUnit[] sysUnits, boolean sysIsRefundOut, boolean sysIsRefundIn) {
        String oid = order.getOid();
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> pushInfoCollection = pushInfoService.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();

        Date time = new Date();
        try {
            clientSession.startTransaction();

            //不涉及三方费用的时候，订单废除退款
            if ((StrUtil.isEmpty(order.getOutFee5()) || order.getOutFee5() <= 0) && (StrUtil.isEmpty(order.getInFee5()) || order.getInFee5() <= 0)) {
                //订单退款不需要审核，平台会配置撤单是否退费、收发货单位会配置撤单是否退费，
                List<Document> dvrAccountDocs = new ArrayList<>();
                List<Document> cusAccountDocs = new ArrayList<>();
                List<Document> sysUnitAccountDocs = new ArrayList<>();
                List<Document> pfAccountDocs = new ArrayList<>();
                List<Document> thirdPartyAccountDocs = new ArrayList<>();
                int dvrFee = 0; //订单中需要退还司机的总金额
                //平台信息费 退回，同时平台账户金额核减
                if (pfIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer1())) { //平台收取发货单位的信息费，符合条件则退回
                    String outPayer1 = order.getOutPayer1();
                    int outFee0 = order.getOutFee0();
                    switch (order.getOutPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer1, new BigDecimal(outFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 12, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + outFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 13, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(outPayer1, null, 3, order.getOid(), outFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                if (pfIsRefundIn && StrUtil.isNotEmpty(order.getInPayer1())) { //平台收取收货单位的信息费，符合条件则退回
                    String inPayer1 = order.getInPayer1();
                    int inFee0 = order.getInFee0();
                    switch (order.getInPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer1, new BigDecimal(inFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + inFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(inPayer1, null, 3, order.getOid(), inFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                //单位收取的代扣费 退回，同时单位账户金额核减
                if (sysIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer2())) {   //发货单位收取的代扣费，符合条件则退回
                    String outPayer2 = order.getOutPayer2();
                    int outFee1 = order.getOutFee1();
                    int outFee2 = order.getOutFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer2, new BigDecimal(outFee1 + outFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + outFee1 + outFee2;
                            break;
                        default:
                            break;
                    }

                    if (outFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), null, 5, order.getOid(), -outFee1, time));
                    if (outFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), null, 5, order.getOid(), -outFee2, time));
                }
                if (sysIsRefundIn && StrUtil.isNotEmpty(order.getInPayer2())) {   //收货单位收取的代扣费，符合条件则退回
                    String inPayer2 = order.getInPayer2();
                    int inFee1 = order.getInFee1();
                    int inFee2 = order.getInFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer2, new BigDecimal(inFee1 + inFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + inFee1 + inFee2;
                            break;
                        default:
                            break;
                    }

                    if (inFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), null, 5, order.getOid(), -inFee1, time));
                    if (inFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), null, 5, order.getOid(), -inFee2, time));
                }
                //客商或友商和第三方收取的信息费 退回司机,同时客商或友商和第三方账户金额核减
                if (StrUtil.isNotEmpty(order.getOutPayer3()) || StrUtil.isNotEmpty(order.getInPayer3())) {
//                String did = order.getOutPayer3();
                    int cusFee = order.getOutFee3() + order.getOutFee4();
                    int outFee5 = order.getOutFee5();
                    int inFee5 = order.getInFee5();
//                dvrAccountDocs.add(driverAccountService.createDriAccountDoc(did, null, new BigDecimal(dvrFee + cusFee + outFee5 + inFee5), 3, order.getOid(), time));
                    dvrFee = dvrFee + cusFee + outFee5 + inFee5;
                    Goods goods = goodsService.get("gid", order.getGid());
                    if (cusFee > 0) {
                        String cid = goods.getShare() == 2 ? goods.getShareCid() : goods.getCid();
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(-cusFee), 9, order.getOid(), time));
                    }
                    if (outFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), null, -outFee5, 1, order.getOid(), time));
                    //                                                                                      (goods.getFee5OutId(), null, -outFee5, 1, order.getOid(), time));
                    if (inFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[2].getThirdPartyName(), null, -inFee5, 1, order.getOid(), time));
                    //                                                                                      (goods.getFee5InId(), null, -inFee5, 1, order.getOid(), time));
                }

                if (dvrFee > 0)
                    dvrAccountDocs.add(driverAccountService.createDriAccountDoc(order.getDid(), null, new BigDecimal(dvrFee), 3, order.getOid(), time));

                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
                if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
                if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);
                if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
                if (thirdPartyAccountDocs.size() > 0)
                    thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);
            }
            //orderTaking，pushInfo,orderLogistics表中删除记录
            Document filter = new Document("oid", oid);
            orderTakingCollection.deleteMany(clientSession, filter);
            pushInfoCollection.deleteOne(clientSession, filter);
            orderLogisticsCollection.deleteOne(clientSession, filter);
            //更新order表中删除标记
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("delete", true);
            if (taking.getDelete() == null || taking.getDelete() != 0) {    //司机主动废除订单
                orderMap.put("delType", 1);//delType = 1;
            } else {    //司机同意客商废除订单
                orderMap.put("delType", 3);//delType = 3;
            }
            orderMap.put("isCheck", 3); //无需审核
            orderMap.put("updateTime", time.getTime());
            orderMap.put("tranStatus", 6);
            orderMap.put("time6", time);    //订单作废时间
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$set", orderMap);
            orderCollection.updateOne(clientSession, new Document("oid", oid), BsonDocument.parse(JSON.toJSONString(updMap)));//格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", 1);
            param.put("orderNum1", -1);
            Map<String, Object> upgMap = new HashMap<>();
            upgMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", order.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(taking.getDid())), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
            return null;
        } catch (Exception e) {
            clientSession.abortTransaction();
            logger.error("refundOrder2" + e.getMessage());
            return ServerResponse.createError("撤单失败");
        } finally {
            clientSession.close();
        }
    }

    private boolean distinguishPayerIsCusOrDri(String payerId1Out, String payerId2Out, String payerId1In, String payerId2In) {
        //true-是客商；false-是司机
        CustomerUser cus;
        if (StrUtil.isNotEmpty(payerId1Out)) {
            cus = customerUserService.getByPK(payerId1Out);
            if (cus != null) return true;
        }
        if (StrUtil.isNotEmpty(payerId2Out)) {
            cus = customerUserService.getByPK(payerId2Out);
            if (cus != null) return true;
        }
        if (StrUtil.isNotEmpty(payerId1In)) {
            cus = customerUserService.getByPK(payerId1In);
            if (cus != null) return true;
        }
        if (StrUtil.isNotEmpty(payerId2In)) {
            cus = customerUserService.getByPK(payerId2In);
            return cus != null;
        }
        return false;
    }

    private String probePayId(String payerId, String payerId1Out, String payerId2Out, String payerId1In, String payerId2In) {
        return StrUtil.isNotEmpty(payerId) ? payerId : (StrUtil.isNotEmpty(payerId1Out) ? payerId1Out : (StrUtil.isNotEmpty(payerId1In) ? payerId1In : (StrUtil.isNotEmpty(payerId2Out) ? payerId2Out : payerId2In)));
    }

    @Override
    public ServerResponse<String> updateOrderCarNum(String oid, String carNum) {
        Order order = orderService.get("oid", oid);
        if (order.getDelete()) return ServerResponse.createError("订单已废除，无效修改！");
        Goods goods = goodsService.get("gid", order.getGid());
        if (goods.getMold() == 1) {
            if (order.getInChecking() > 0) return ServerResponse.createError("车辆已经检票入场，不可修改车牌号！");
        } else {
            if (order.getOutChecking() > 0) return ServerResponse.createError("车辆已经检票入场，不可修改车牌号！");
        }

        OrderTaking ot = orderTakingService.get("oid", oid);
        if (ot != null) {
            if (ot.getIsPunchClock() > 0) {
                return ServerResponse.createError("已完成签到，不可修改车牌号");
            } else if (ot.getSa() > 0) {
                return ServerResponse.createError("已完成预约，不可修改车牌号");
            }
        }

        Query<Order> oQuery = orderService.createQuery().filter("oid", oid);
        UpdateOperations<Order> oUpO = orderService.createUpdateOperations().set("carNum", carNum);
        orderService.update(oQuery, oUpO);

        Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", oid);
        UpdateOperations<OrderTaking> OTUpO = orderTakingService.createUpdateOperations().set("carNum", carNum);
        orderTakingService.update(otQuery, OTUpO);

        return ServerResponse.createSuccess("车号修改成功");
    }

    @Override
    public ServerResponse<String> punchClock(String oid, String imei, String longitude, String latitude) {
        if (StrUtil.isEmpty(longitude) || StrUtil.isEmpty(latitude)) return ServerResponse.createError("请允许获取当前位置");
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");

        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        String defaultDownUnit = driverOrderData.getaSubCode();
        //过滤重复打卡
        Query<DriverSignIn> query = driverSignInService.createQuery();
        query.filter("oid", oid);
        query.filter("defaultDownUnit", defaultDownUnit);
        DriverSignIn record = driverSignInService.get(query);
        if (record == null) {
            //地理围栏打卡记录
            DriverSignIn signIn = new DriverSignIn();
            SysUnit sysUnit = sysUnitService.get("code", defaultDownUnit);
            if (sysUnit == null) return ServerResponse.createError("单位编码错误");
            if (StrUtil.isNotEmpty(sysUnit.getGid())) {
                //Map<String, List<String>> result = MapService.geoFenceStatus(imei, longitude + "," + latitude);
                Map<String, List<String>> result = MapService.fenceStatusBaiDu(null, longitude + "," + latitude);
                if (StrUtil.isNotEmpty(result.get("gids")) && result.get("gids").size() > 0) {    //存在围栏中，打卡成功
                    signIn.setGeoFenceId(result.get("gids").get(0));
                    //signIn.setGeoFenceName(result.get("names").get(0));
                    signIn.setGeofenceIds(result.get("gids"));
                    //signIn.setGeofenceNames(result.get("names"));
                } else {
                    return ServerResponse.createError("未在指定区域，打卡失败");
                }
            }
            signIn.setDriverId(did);
            signIn.setDriverName(driverInfo.getName());
            signIn.setMobile(driverInfo.getMobile());
            signIn.setOid(oid);
            signIn.setDefaultDownUnit(defaultDownUnit);
            signIn.setLocations(longitude + "," + latitude);
            driverSignInService.save(signIn);
        }

        //请求业务系统签到接口
        if (StrUtil.isEmpty(driverOrderData.getIsPunchClock())) return ServerResponse.createError("参数错误!！");
        if (driverOrderData.getIsPunchClock() == 1) {
            SysUnit subSysUnit = sysUnitService.get("code", driverOrderData.getaSubCode());
            SysUnit subSysUnitP = sysUnitService.get("code", subSysUnit.getpCode());

            //调用业务系统接口，提交签到
            String msg = contractService.saveLineCheckIn(subSysUnitP.getIp(), driverOrderData.getaSubCode(), order, longitude, latitude);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

            //修改orderTaking签到记录
            int isPunchClock = 0;
            if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) isPunchClock = 1;
            if (defaultDownUnit.equals(order.getInDefaultDownUnit())) isPunchClock = 2;
            Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", oid);
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
            otUpdateOperations.set("isPunchClock", isPunchClock);
            orderTakingService.update(otQuery, otUpdateOperations);

            return ServerResponse.createSuccess("打卡成功");
        } else {
            return ServerResponse.createSuccess("重复签到");
        }
    }

    public ServerResponse<String> punchClock2(String oid, String imei, String longitude, String latitude) {
//        if (StrUtil.isEmpty(longitude) || StrUtil.isEmpty(latitude)) return ServerResponse.createError("请允许获取当前位置");
        if (StrUtil.isEmpty(longitude) || "null".equals(longitude) || "undefined".equals(longitude)
                || StrUtil.isEmpty(latitude) || "null".equals(latitude) || "undefined".equals(latitude))
            return ServerResponse.createError("请允许获取当前位置");
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");

        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        String defaultDownUnit = driverOrderData.getaSubCode();
        //过滤重复打卡
        Query<DriverSignIn> query = driverSignInService.createQuery();
        query.filter("oid", oid);
        query.filter("defaultDownUnit", defaultDownUnit);
        DriverSignIn record = driverSignInService.get(query);
        if (record == null) {
            //地理围栏打卡记录
            DriverSignIn signIn = new DriverSignIn();
            SysUnit sysUnit = sysUnitService.get("code", defaultDownUnit);
            if (sysUnit == null) return ServerResponse.createError("单位编码错误");
            if (StrUtil.isNotEmpty(sysUnit.getGid())) {
                String[] unitLogLat = sysUnit.getGeoCenter().split(",");
                Double unitLog = Double.valueOf(unitLogLat[0]);
                Double unitLat = Double.valueOf(unitLogLat[1]);
                double s = getDistance(Double.valueOf(longitude), Double.valueOf(latitude), unitLog, unitLat);
                if (s <= Double.valueOf(sysUnit.getGeoRadius())) {
                    signIn.setGeoFenceId(sysUnit.getGid());
                } else {
                    return ServerResponse.createError("未在指定区域，打卡失败");
                }
            }
            signIn.setDriverId(did);
            signIn.setDriverName(driverInfo.getName());
            signIn.setMobile(driverInfo.getMobile());
            signIn.setOid(oid);
            signIn.setDefaultDownUnit(defaultDownUnit);
            signIn.setLocations(longitude + "," + latitude);
            driverSignInService.save(signIn);
        }

        //请求业务系统签到接口
        if (StrUtil.isEmpty(driverOrderData.getIsPunchClock())) return ServerResponse.createError("参数错误!！");
        if (driverOrderData.getIsPunchClock() == 1) {
            SysUnit subSysUnit = sysUnitService.get("code", driverOrderData.getaSubCode());
            SysUnit subSysUnitP = sysUnitService.get("code", subSysUnit.getpCode());

            //调用业务系统接口，提交签到
            String msg = contractService.saveLineCheckIn(subSysUnitP.getIp(), driverOrderData.getaSubCode(), order, longitude, latitude);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

            //修改orderTaking签到记录
            int isPunchClock = 0;
            if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) isPunchClock = 1;
            if (defaultDownUnit.equals(order.getInDefaultDownUnit())) isPunchClock = 2;
            Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", oid);
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
            otUpdateOperations.set("isPunchClock", isPunchClock);
            orderTakingService.update(otQuery, otUpdateOperations);

            //异步添加GPS，目前仅限于准泰测试单位使用。
            if ((StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutUnitCode().equals("**********")) || (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInUnitCode().equals("**********")))
                saveCurrentPosition(order, defaultDownUnit);

            return ServerResponse.createSuccess("打卡成功");
        } else {
            return ServerResponse.createSuccess("重复签到");
        }
    }

    public ServerResponse<String> punchClock3(String oid, String imei, String longitude, String latitude) {
        if (StrUtil.isEmpty(longitude) || "null".equals(longitude) || "undefined".equals(longitude)
                || StrUtil.isEmpty(latitude) || "null".equals(latitude) || "undefined".equals(latitude))
            return ServerResponse.createError("请允许获取当前位置");
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");

        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        String defaultDownUnit = driverOrderData.getaSubCode();
        //过滤重复打卡
        Query<DriverSignIn> query = driverSignInService.createQuery();
        query.filter("oid", oid);
        query.filter("defaultDownUnit", defaultDownUnit);
        DriverSignIn record = driverSignInService.get(query);
        if (record == null) {
            //地理围栏打卡记录
            DriverSignIn signIn = new DriverSignIn();
            SysUnit sysUnit = sysUnitService.get("code", defaultDownUnit);
            if (sysUnit == null) return ServerResponse.createError("单位编码错误");

            List<SubUnitGeo> subUnitGeoList = sysUnit.getSubUnitGeoList();
            if (StrUtil.isNotEmpty(subUnitGeoList)) {
                boolean isIn = false;
                for (SubUnitGeo geo : subUnitGeoList) {
                    String[] unitLogLat = geo.getGeoCenter().split(",");
                    Double unitLog = Double.valueOf(unitLogLat[0]);
                    Double unitLat = Double.valueOf(unitLogLat[1]);
                    double s = getDistance(Double.valueOf(longitude), Double.valueOf(latitude), unitLog, unitLat);
                    if (s <= Double.valueOf(geo.getGeoRadius())) {
                        signIn.setGeoFenceId(geo.getGid());
                        isIn = true;
                        break;
                    }
                }
                if (isIn) return ServerResponse.createError("未在指定区域，打卡失败");
            }
            signIn.setDriverId(did);
            signIn.setDriverName(driverInfo.getName());
            signIn.setMobile(driverInfo.getMobile());
            signIn.setOid(oid);
            signIn.setDefaultDownUnit(defaultDownUnit);
            signIn.setLocations(longitude + "," + latitude);
            driverSignInService.save(signIn);
        }

        //请求业务系统签到接口
        if (StrUtil.isEmpty(driverOrderData.getIsPunchClock())) return ServerResponse.createError("参数错误!！");
        if (driverOrderData.getIsPunchClock() == 1) {
            SysUnit subSysUnit = sysUnitService.get("code", driverOrderData.getaSubCode());
            SysUnit subSysUnitP = sysUnitService.get("code", subSysUnit.getpCode());

            //调用业务系统接口，提交签到
            String msg = contractService.saveLineCheckIn(subSysUnitP.getIp(), driverOrderData.getaSubCode(), order, longitude, latitude);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

            //修改orderTaking签到记录
            int isPunchClock = 0;
            if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) isPunchClock = 1;
            if (defaultDownUnit.equals(order.getInDefaultDownUnit())) isPunchClock = 2;
            Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", oid);
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
            otUpdateOperations.set("isPunchClock", isPunchClock);
            orderTakingService.update(otQuery, otUpdateOperations);

            //异步添加GPS，目前仅限于准泰测试单位使用。
            if ((StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutUnitCode().equals("**********")) || (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInUnitCode().equals("**********")))
                saveCurrentPosition(order, defaultDownUnit);

            return ServerResponse.createSuccess("打卡成功");
        } else {
            return ServerResponse.createSuccess("重复签到");
        }
    }

    /**
     * 司机签到时，记录车辆GPS坐标
     */
    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public void saveCurrentPosition(Order order, String defaultDownUnit) {
        Integer outOrIn = null;
        if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) {
            outOrIn = 0;
        } else if (defaultDownUnit.equals(order.getInDefaultDownUnit())) {
            outOrIn = 1;
        }
        if (outOrIn != null) orderPositionService.addPositionWherePunchClock(order, outOrIn);
    }

    private static final Double EARTH_RADIUS = 6371393.0;    //地球半径：6371.393km

    private Double getDistance(Double startLog, Double startLat, Double endLog, Double endLat) {
        double log1 = Math.toRadians(startLog);
        double lat1 = Math.toRadians(startLat);
        double log2 = Math.toRadians(endLog);
        double lat2 = Math.toRadians(endLat);

        double a = lat1 - lat2;
        double b = log1 - log2;

        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;

        return s;
    }

    @Override
    public ServerResponse<String> uploadQuarantinePho(MultipartFile uploadPho, String type) {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String filename = uploadPho.getOriginalFilename();
        String key = sdf.format(new Date()) + type + did + filename.substring(filename.lastIndexOf("."));
        String path = null;
        try {
            //校验图片是否属于司机本人
            /*String msg = quarantineInfoService.checkPho(uploadPho, type, driverInfo);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);*/

            //图片上传oss
            path = photoFileService.putObject(key, uploadPho.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (StrUtil.isNotEmpty(path)) {
            return ServerResponse.createSuccess("图片上传成功", path);
        } else {
            return ServerResponse.createError("图片上传失败");
        }
    }

    @Override
    public ServerResponse<String> addQuarantineInfo(String oid, String healthCodePho, String travelCardPho, String
            temperaturePro, String temperature) {
        /*
        private String did;             //司机编号
        private DriverInfo driverInfo;  //司机信息
        private String defaultDownUnit; //二级单位编码
        private String subName;         //二级单位名称
        private String areaCode;        //场区编码
        * */
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);

        //图片识别占资源，加锁，避免同一订单图片发生短时多次识别
        if (!lockedService.orderQuarantineLock(oid, 80)) return ServerResponse.createError("提交频繁！");

        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");

        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        String defaultDownUnit = driverOrderData.getaSubCode();

        //检查是否重复添加
        Query<QuarantineInfo> query = quarantineInfoService.createQuery();
        /*query.filter("defaultDownUnit", defaultDownUnit);
        query.filter("did", did);
        query.filter("createTime >= ", IdUnit.weeHours1(new Date().getTime(), "00:00:00", 0));*/
        query.filter("did", did);
        query.filter("oid", oid);
        QuarantineInfo quarantineInfo = quarantineInfoService.get(query);
        if (quarantineInfo != null && (StrUtil.isEmpty(quarantineInfo.getAudit()) || quarantineInfo.getAudit() == 0))
            return ServerResponse.createError("已提交申报，请等待审核");
        if (quarantineInfo != null && (quarantineInfo.getAudit() == 1 || quarantineInfo.getAudit() == 3))
            return ServerResponse.createError("重复申报");

        //异步添加并自动审核防疫申报信息
        this.quarantineInfoAsync(driverInfo, defaultDownUnit, healthCodePho, travelCardPho, temperaturePro, temperature, order);

        return ServerResponse.createSuccess("防疫申报成功");
    }

    @Async("busTaskExecutor")
    public void quarantineInfoAsync(DriverInfo driverInfo, String defaultDownUnit, String healthCodePho, String
            travelCardPho, String temperaturePro, String temperature, Order order) {
        try {
            QuarantineInfo quarantineInfo = quarantineInfoService.imageRecognition(driverInfo, healthCodePho, travelCardPho, temperaturePro, temperature);
            quarantineInfo.setDid(driverInfo.getObjectId().toHexString());
            quarantineInfo.setDriverInfo(driverInfo);
            quarantineInfo.setDefaultDownUnit(defaultDownUnit);
            quarantineInfo.setOid(order.getOid());
            quarantineInfo.setAudit(0);
            //String msg = quarantineInfoService.auditQuarantineInfo(quarantineInfo);
            quarantineInfoService.save(quarantineInfo);

            //审核成功的，修改orderTaking防疫申报记录
            //if (StrUtil.isEmpty(msg)) {
            int isQuarantine = 0;
            if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) isQuarantine = 1;
            if (defaultDownUnit.equals(order.getInDefaultDownUnit())) isQuarantine = 2;
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
            otUpdateOperations.set("isQuarantine", isQuarantine);
            Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", order.getOid());
            orderTakingService.update(otQuery, otUpdateOperations);
            //}
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            //图片识别异步执行结束后，放开锁
            lockedService.orderQuarantineUnLock(order.getOid(), 80);
        }

    }

    /**
     * 司机提交防疫申报信息
     * --防疫信息审核字段添加时设为null
     * 当司机提交信息后，司机端不再显示防疫按钮
     * 当企业端审核拒绝后，司机端再显示防疫申报按钮
     */
    @Override
    public ServerResponse<String> addQuarantineInfo2(String oid, String healthCodePho, String travelCardPho, String
            nucleicAcidPho, String vaccinationPho, String touchPro, String temperaturePro, String temperature) {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);

        //图片识别占资源，加锁，避免同一订单图片发生短时多次识别
        if (!lockedService.orderQuarantineLock(oid, 80)) return ServerResponse.createError("提交频繁！");

        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");

        OrderTaking orderTaking = orderTakingService.get("oid", oid);
        DriverOrderData driverOrderData = new DriverOrderData();
        checkAppointment(driverOrderData, order, orderTaking);
        String defaultDownUnit = driverOrderData.getaSubCode();

        //检查是否重复添加
        Query<QuarantineInfo> query = quarantineInfoService.createQuery();
        query.filter("did", did);
        query.filter("oid", oid);
        query.or(query.criteria("type").equal(null),
                query.criteria("type").equal(0));
        query.filter("defaultDownUnit", defaultDownUnit);
        query.order(Sort.descending("createTime"));
        List<QuarantineInfo> quarantineInfos = quarantineInfoService.list(query);
        if (quarantineInfos.size() > 0 && (StrUtil.isEmpty(quarantineInfos.get(0).getAudit()) || quarantineInfos.get(0).getAudit() == 0))
            return ServerResponse.createError("已提交申报，请等待审核");
        if (quarantineInfos.size() > 0 && (quarantineInfos.get(0).getAudit() == 1 || quarantineInfos.get(0).getAudit() == 3))
            return ServerResponse.createError("重复申报");

        //添加未审核的防疫申报信息
        QuarantineInfo result = new QuarantineInfo();
        result.setId(Utils.getUUID());
        result.setHealthCodePho(healthCodePho);
        result.setTravelCardPho(travelCardPho);
        result.setVaccinationPho(vaccinationPho);
        result.setNucleicAcidPho(nucleicAcidPho);
        result.setTouchPro(touchPro);
        result.setTemperaturePro(temperaturePro);
        result.setTemperature(temperature);
        result.setDid(driverInfo.getObjectId().toHexString());
        result.setDriverInfo(driverInfo);
        result.setDefaultDownUnit(defaultDownUnit);
        result.setOid(order.getOid());
        result.setAudit(0);
        quarantineInfoService.save(result);

        //添加完成 即 修改orderTaking防疫申报按钮
        int isQuarantine = 0;
        if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) isQuarantine = 1;
        if (defaultDownUnit.equals(order.getInDefaultDownUnit())) isQuarantine = 2;
        UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
        otUpdateOperations.set("isQuarantine", isQuarantine);
        Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", order.getOid());
        orderTakingService.update(otQuery, otUpdateOperations);

        return ServerResponse.createSuccess("防疫申报成功");
    }

    @Override
    public ServerResponse<String> grabNumber(String oid) {
        return null;
    }

    @Override
    public ServerResponse<DriverOrderData> get35TranStatusOrder() {
        String did = ShiroUtils.getUserId();
        Query<Order> query = orderService.createQuery();
        query.or(
                query.criteria("tranStatus").equal(3),
                query.criteria("tranStatus").equal(5)
        );
        Query<Order> otQuery = orderService.createQuery();
        otQuery.filter("orderTakings.did", did);
        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);
        pipeline.lookup("t_order_taking", "oid", "oid", "orderTakings");
        pipeline.match(otQuery);
        pipeline.sort(Sort.descending("updateTime"));
        pipeline.limit(1);

        Iterator<Order> iterator = pipeline.aggregate(Order.class);
        DriverOrderData driverOrderData = new DriverOrderData();
        while (iterator.hasNext()) {
            Order order = iterator.next();
            GOrderTaking taking = order.getOrderTakings().get(0);
            BeanUtils.copyProperties(order, driverOrderData);

            //checkAppointment(driverOrderData, order, taking);

            driverOrderData.setTradeName(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());

            driverOrderData.setOrderDate(taking.getUpdateTime());
            driverOrderData.setCreateDate(taking.getCreateTime());
            //因为finishTag为1时，司机端 订单会显示完成卸货按钮,司机不点击，则一直显示订单 --- 22年4月19号
            driverOrderData.setFinishTag(taking.getFinishTag());
            driverOrderData.setDelete(taking.getDelete());//客商是否撤单
            driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());

            if (order.getOutChecking() == 3) {
                driverOrderData.setOutPoundList(new PoundList(order.getOutSubName(), order.getOutBillCode(), order.getCarNum(), order.getOutVariety(), order.getOutGrossWeight(), order.getOutTareWeight(), order.getInSubName(), order.getOtherOutTime()));
                driverOrderData.setOutCoalTicket(order.getCoalTicket());
            }
            if (order.getInChecking() == 3) {
                driverOrderData.setInPoundList(new PoundList(order.getOutSubName(), order.getInBillCode(), order.getCarNum(), order.getInVariety(), order.getInGrossWeight(), order.getInTareWeight(), order.getInSubName(), order.getOtherInTime()));
                driverOrderData.setInCoalTicket(order.getCoalTicket());
            }
        }
        if (StrUtil.isNotEmpty(driverOrderData.getOid())) {
            return ServerResponse.createSuccess("查询成功", driverOrderData);
        } else {
            return ServerResponse.createSuccess("查询成功");
        }
    }

    @Override
    public ServerResponse<DriverOrderData> get35TranStatusOrder2() {
        String did = ShiroUtils.getUserId();
        Query<OrderTaking> otQuery = orderTakingService.createQuery();
        otQuery.filter("did", did);
        otQuery.criteria("driverIsAgree").notEqual(2);
        otQuery.order(Sort.descending("updateTime"));
        List<OrderTaking> ots = orderTakingService.findPageList(0, 2, otQuery);
        if (StrUtil.isEmpty(ots) || ots.size() <= 0) return ServerResponse.createSuccess("查询成功");

        OrderTaking ot1;
        OrderTaking ot2 = null;

        ot1 = ots.get(0);
        if (ots.size() == 2) {
            ot2 = ots.get(1);
            if (ot1.getFinishTag() == 2 && ot2.getFinishTag() == 2) ot2 = null;
        }

        Query<Order> oQuery1 = orderService.createQuery();
        oQuery1.filter("oid", ot1.getOid());
        oQuery1.or(
                oQuery1.criteria("tranStatus").equal(3),
                oQuery1.criteria("tranStatus").equal(5)
        );
        Order o1 = orderService.get(oQuery1);
        Order o2 = null;
        if (ot2 != null) {
            Query<Order> oQuery2 = orderService.createQuery();
            oQuery2.filter("oid", ot2.getOid());
            oQuery2.or(
                    oQuery2.criteria("tranStatus").equal(3),
                    oQuery2.criteria("tranStatus").equal(5)
            );
            o2 = orderService.get(oQuery2);
        }

        DriverOrderData driverOrderData = new DriverOrderData();
        if (o1 != null && o2 == null) {
            getResult(o1, driverOrderData, ot1);
        } else if (o1 == null && o2 != null) {
            getResult(o2, driverOrderData, ot2);
        } else if (o1 != null && o2 != null) {
            if (o1.getUpdateTime() >= o2.getUpdateTime()) {
                getResult(o1, driverOrderData, ot1);
            } else if (o1.getUpdateTime() < o2.getUpdateTime()) {
                getResult(o2, driverOrderData, ot2);
            }
        }

        if (StrUtil.isNotEmpty(driverOrderData.getOid())) {
            return ServerResponse.createSuccess("查询成功", driverOrderData);
        } else {
            return ServerResponse.createSuccess("查询成功");
        }
    }

    private void getResult(Order o1, DriverOrderData driverOrderData, OrderTaking ot1) {
        BeanUtils.copyProperties(o1, driverOrderData);
        //checkAppointment(driverOrderData, order, taking);
        driverOrderData.setTradeName(StrUtil.isEmpty(o1.getOutVariety()) ? o1.getInVariety() : o1.getOutVariety());

        driverOrderData.setOrderDate(ot1.getUpdateTime());
        driverOrderData.setCreateDate(ot1.getCreateTime());
        //因为finishTag为1时，司机端 订单会显示完成卸货按钮,司机不点击，则一直显示订单 --- 22年4月19号
        driverOrderData.setFinishTag(ot1.getFinishTag());
        driverOrderData.setDelete(ot1.getDelete());//客商是否撤单
        driverOrderData.setDriverIsAgree(ot1.getDriverIsAgree());

        if (o1.getOutChecking() == 3) {
            driverOrderData.setOutPoundList(new PoundList(o1.getOutSubName(), o1.getOutBillCode(), o1.getCarNum(), o1.getOutVariety(), o1.getOutGrossWeight(), o1.getOutTareWeight(), o1.getInSubName(), o1.getOtherOutTime()));
            driverOrderData.setOutCoalTicket(o1.getCoalTicket());
        }
        if (o1.getInChecking() == 3) {
            driverOrderData.setInPoundList(new PoundList(o1.getOutSubName(), o1.getInBillCode(), o1.getCarNum(), o1.getInVariety(), o1.getInGrossWeight(), o1.getInTareWeight(), o1.getInSubName(), o1.getOtherInTime()));
            driverOrderData.setInCoalTicket(o1.getCoalTicket());
        }
    }

    @Override
    public ServerResponse<DriverOrderData> get35TranStatusOrder3() {
        String did = ShiroUtils.getUserId();
        Query<Order> query = orderService.createQuery();
        query.or(
                query.criteria("tranStatus").equal(3),
                query.criteria("tranStatus").equal(5)
        );
        query.filter("did", did);
        query.order(Sort.descending("updateTime"));

        //pipeline.limit(1);
        List<Order> orders = orderService.list(query);
        if (orders.size() <= 0) return ServerResponse.createSuccess("查询成功");

        Order order = orders.get(0);
        OrderTaking taking = orderTakingService.get("oid", order.getOid());
        DriverOrderData driverOrderData = new DriverOrderData();
        BeanUtils.copyProperties(order, driverOrderData);
        checkAppointment(driverOrderData, order, taking);
        driverOrderData.setTradeName(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());
        driverOrderData.setOrderDate(taking.getUpdateTime());
        driverOrderData.setCreateDate(taking.getCreateTime());
        //因为finishTag为1时，司机端 订单会显示完成卸货按钮,司机不点击，则一直显示订单 --- 22年4月19号
        driverOrderData.setFinishTag(taking.getFinishTag());
        driverOrderData.setDelete(taking.getDelete());//客商是否撤单
        driverOrderData.setDriverIsAgree(taking.getDriverIsAgree());
        if (order.getOutChecking() == 3) {
            driverOrderData.setOutPoundList(new PoundList(order.getOutSubName(), order.getOutBillCode(), order.getCarNum(), order.getOutVariety(), order.getOutGrossWeight(), order.getOutTareWeight(), order.getInSubName(), order.getOtherOutTime(), order.getOutBizContractName()));
            driverOrderData.setOutCoalTicket(order.getCoalTicket());
        }
        if (order.getInChecking() == 3) {
            driverOrderData.setInPoundList(new PoundList(order.getOutSubName(), order.getInBillCode(), order.getCarNum(), order.getInVariety(), order.getInGrossWeight(), order.getInTareWeight(), order.getInSubName(), order.getOtherInTime(), order.getInBizContractName()));
            driverOrderData.setInCoalTicket(order.getCoalTicket());
        }

        return ServerResponse.createSuccess("查询成功", driverOrderData);
    }

    @Override
    public ServerResponse<Map<String, String>> orderRequestPayWithProfitSharing(String gid, String oid, String dvrGoodsId, String openid, String carNum, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        String did = ShiroUtils.getUserId();

        //判断司机是否有未完成订单
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        if (driverInfo.getHaveOrder() == 1) return ServerResponse.createError("司机有未完成的订单");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //if (driverInfoService.checkCarTareWeight(did, carNum)) return ServerResponse.createError("请先完善车辆皮重信息");
        if (StrUtil.isNotEmpty(dvrGoodsId)) {
            WXsDvrGoods wXsDvrGoods = wXsDvrGoodsService.get("dvrGoodsId", dvrGoodsId);
            if (wXsDvrGoods == null) return ServerResponse.createError("订单链接不存在！");
            if (wXsDvrGoods.getUsedNum() >= wXsDvrGoods.getPartNum()) return ServerResponse.createError("车数已用完");
            gid = wXsDvrGoods.getGid();
        }

        Goods goods;
        Order order;
        if (StrUtil.isNotEmpty(gid)) {
            goods = goodsService.get("gid", gid);
            Query<Order> oQuery = orderService.createQuery();
            oQuery.filter("gid", gid);
            oQuery.filter("delete", false);
            oQuery.filter("tranStatus", 0);
            oQuery.filter("locked", false);
            List<Order> orders = orderService.list(oQuery); //货运信息下可接单 的订单列表
            if (orders.size() <= 0) return ServerResponse.createError("货运信息下暂无可用订单");
            order = orders.get(0);  //同货运批次下订单 需支付金额相同，可取第一个值为代表计算接单金额。
        } else if (StrUtil.isNotEmpty(oid)) {
            order = orderService.get("oid", oid);
            goods = goodsService.get("gid", order.getGid());
        } else {
            return ServerResponse.createError("参数错误，不能全空");
        }
        if (goods == null || order == null) return ServerResponse.createError("参数错误，货运信息不存在");

        if (goods.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        //todo: 判断各方代付账户余额
        Map<String, Object> resultMap = checkPayOnBehalfAccount(goods, order);
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");

        //修改订单状态为锁定，等待支付完成
        Query<Order> upOrderQuery = orderService.createQuery();
        upOrderQuery.criteria("oid").equal(order.getOid());
        upOrderQuery.criteria("updateTime").equal(order.getUpdateTime());
        UpdateOperations<Order> orderOperations = orderService.createUpdateOperations();
        orderOperations.set("locked", true);
        UpdateResults a = orderService.update(upOrderQuery, orderOperations);
        if (!a.getUpdatedExisting()) return ServerResponse.createError("系统忙,请重试");
        /*
         * 需要处理的问题：
         * 1。司机请求支付后，放弃支付
         * 2。请求支付的司机数量大于货运信息的车数
         * 3。同一个司机反复请求支付
         * */
        Query<WxPrepaid> query2 = wxPrepaidService.createQuery();
        query2.criteria("goodsId").equal(goods.getGid());
        query2.criteria("pay").equal(false);
        query2.criteria("dvrId").equal(did);
        WxPrepaid dvrWxPerpaid = wxPrepaidService.get(query2);
        if (dvrWxPerpaid != null) {
            ServerResponse<Map<String, String>> response = wxPrepaidService.weChatRequestPay2(openid, dvrWxPerpaid);

            //司机接单失败，订单解锁
            if (response.getStatus() != 0 || response == null) {
                Query<Order> orderUnLockQuery = orderService.createQuery();
                orderUnLockQuery.criteria("oid").equal(order.getOid());
                UpdateOperations<Order> orderUnLockOperations = orderService.createUpdateOperations();
                orderUnLockOperations.set("locked", false);
                orderService.update(orderUnLockQuery, orderUnLockOperations);
            }

            return response;
        } else {
            //添加预支付信息
            WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
            prepaid.setOverdue(false);
            prepaid.setBody("订单付款");
            prepaid.setTotalFee(searchOrderFee(goods, order, fee));
            List<String> billCodes = new ArrayList<>();
            if (StrUtil.isNotEmpty(order.getOutBillCode())) billCodes.add(order.getOutBillCode());
            if (StrUtil.isNotEmpty(order.getInBillCode())) billCodes.add(order.getInBillCode());
            prepaid.setBillCodes(billCodes);
            List<String> openids = new ArrayList<>();
            List<String> merchantIds = new ArrayList<>();
            if (StrUtil.isNotEmpty(goods.getFee5OutType()) && goods.getFee5OutType() == 0) {
                merchantIds.add(goods.getFee5OutId());
            } else if (StrUtil.isNotEmpty(goods.getFee5OutType()) && goods.getFee5OutType() == 1) {
                openids.add(goods.getFee5OutId());
            }
            if (StrUtil.isNotEmpty(goods.getFee5InType()) && goods.getFee5InType() == 0) {
                merchantIds.add(goods.getFee5InId());
            } else if (StrUtil.isNotEmpty(goods.getFee5InType()) && goods.getFee5InType() == 1) {
                openids.add(goods.getFee5InId());
            }
            /*if (StrUtil.isNotEmpty(goods.getFee5OutId())) openids.add(goods.getFee5OutId());
            if (StrUtil.isNotEmpty(goods.getFee5InId())) openids.add(goods.getFee5InId());*/
            prepaid.setOpenids(openids);
            prepaid.setGoodsId(goods.getGid());
            prepaid.setOrderId(order.getOid());
            prepaid.setDvrId(did);
            prepaid.setDvrCarNum(carNum);
            prepaid.setDvrMobile(driverInfo.getMobile());
            prepaid.setDvrLon(longitude);
            prepaid.setDvrLat(latitude);
            if (StrUtil.isNotEmpty(fee)) prepaid.setCusUnorderedFee(fee);

            prepaid.setAttach("fee5");  //包含分账支付的自定义附加信息
//            return wxPrepaidService.weChatRequestPay2(openid, prepaid);

            ServerResponse<Map<String, String>> response = wxPrepaidService.weChatRequestPay2(openid, prepaid);

            //司机接单失败，订单解锁
            if (response.getStatus() != 0 || response == null) {
                Query<Order> orderUnLockQuery = orderService.createQuery();
                orderUnLockQuery.criteria("oid").equal(order.getOid());
                UpdateOperations<Order> orderUnLockOperations = orderService.createUpdateOperations();
                orderUnLockOperations.set("locked", false);
                orderService.update(orderUnLockQuery, orderUnLockOperations);
            }

            return response;
        }

    }

    //检查要求代付账户余额
    private Map<String, Object> checkPayOnBehalfAccount(Goods goods, Order order) {
        Map<String, Object> resultMap = new HashMap<>();

        BigDecimal num0 = new BigDecimal(goods.getOrderNum0());

        int cusPayFee = 0;  //客商支付的费用
        //1.平台收取信息费 - 平台账户、代付单位账户、代付客商账户、司机账户
        if (order.getOutFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getOutUnitPayId())) {
                //判断单位余额
                BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getOutUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getOutFee0()).multiply(num0)) < 0) {
                    resultMap.put("msg1", "发货端余额不足");
                    return resultMap;
                }
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                cusPayFee = cusPayFee + order.getOutFee0();
            }
        }
        if (order.getInFee0() > 0) {
            if (StrUtil.isNotEmpty(goods.getInUnitPayId())) {
                //判断单位余额
                BigDecimal unitAvailableFee = sysUnitAccountService.getAvailableFee(goods.getInUnitPayId());
                if (unitAvailableFee.compareTo(new BigDecimal(order.getInFee0()).multiply(num0)) < 0) {
                    resultMap.put("msg2", "收货端余额不足");
                    return null;
                }
            } else if (StrUtil.isNotEmpty(goods.getPayCid())) {
                cusPayFee = cusPayFee + order.getInFee0();
            }
        }
        //2.单位代扣费 - 单位账户、代付账户、司机账户
        if (order.getOutFee1() > 0 && StrUtil.isNotEmpty(goods.getPayCid()))
            cusPayFee = cusPayFee + order.getOutFee1();
        if (order.getOutFee2() > 0 && StrUtil.isNotEmpty(goods.getPayCid()))
            cusPayFee = cusPayFee + order.getOutFee2();
        if (order.getInFee1() > 0 && StrUtil.isNotEmpty(goods.getPayCid()))
            cusPayFee = cusPayFee + order.getInFee1();
        if (order.getInFee2() > 0 && StrUtil.isNotEmpty(goods.getPayCid()))
            cusPayFee = cusPayFee + order.getInFee2();

        //6.判断客商账户余额
        BigDecimal cusAvailableFee = customerAccountService.getAvailableFee(goods.getPayCid());
        if (cusPayFee > 0 && cusAvailableFee.compareTo(new BigDecimal(cusPayFee).multiply(num0)) < 0) {
            resultMap.put("msg4", "客商余额不足");
            return resultMap;
        }
        return resultMap;
    }

    @Override
    public void unLockOrder(String oid) {
        //支付完成，修改订单状态为解锁
        Query<Order> upOrderQuery = orderService.createQuery();
        upOrderQuery.criteria("oid").equal(oid);
        UpdateOperations<Order> orderOperations = orderService.createUpdateOperations();
        orderOperations.set("locked", false);
        orderService.update(upOrderQuery, orderOperations);
    }

    @Override
    public ServerResponse<Order> wxLinkOrder(String carNum, String dvrGoodsId, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //todo:验证 司机
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");
        if (StrUtil.isEmpty(carNum) && StrUtil.isEmpty(driverInfo.getCarNum()))
            return ServerResponse.createError("接单车牌号不能为空");
        if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        WXsDvrGoods wXsDvrGoods = wXsDvrGoodsService.get("dvrGoodsId", dvrGoodsId);
        if (wXsDvrGoods == null) return ServerResponse.createError("订单链接不存在！");
        if (wXsDvrGoods.getUsedNum() >= wXsDvrGoods.getPartNum()) return ServerResponse.createError("车数已用完");
        String gid = wXsDvrGoods.getGid();

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("订单链接运单号错误！");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = orderService.createQuery();
        query.filter("gid", gid);
        query.filter("tranStatus", 0);
        Order order = orderService.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
//        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)

        // 判断订单是否超过可接单时间
        int hour = queryOrderHour(order);
        if (new Date().getTime() - order.getUpdateTime() > hour * 60 * 60 * 1000)
            return ServerResponse.createError("超过" + hour + "小时，订单过期");

        //TODO:2.判断司机是否存在司机池中，无则添加
        //友商优先使用订单
        //暂时屏蔽司机池添加司机
        /*String cid = StrUtil.isNotEmpty(g.getShareCid()) ? g.getShareCid() : g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee4().toString() : fee;
        } else {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee3().toString() : fee;
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = newOrderFee2(did, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        // 单位设置delivery时，请求企业端，必须企业端返回成功，则可成功接单
        Map<String, Object> deliveryMap = delivery(sysUnits, order, driverInfo, car, carNum);
        boolean su = (boolean) deliveryMap.get("su");
        String deliveryMsg = (String) deliveryMap.get("msg");
        if (!su) return ServerResponse.createError(deliveryMsg);

        //TODO:3.接单
        String oid = order.getOid();
//        int updFeeResult = this.updateFee3(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        int updFeeResult = this.updateFee4(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        } else if (updFeeResult == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (updFeeResult == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (updFeeResult == -7) {
            return ServerResponse.createError("客商余额不足");
        }

        //todo：修改WXsDveGoods数据userNum字段（使用车数）
        Query<WXsDvrGoods> wXsDvrGoodsQuery = wXsDvrGoodsService.createQuery();
        wXsDvrGoodsQuery.criteria("dvrGoodsId").equal(dvrGoodsId);
        UpdateOperations<WXsDvrGoods> wXsDvrGoodsUpdateOperations = wXsDvrGoodsService.createUpdateOperations();
        wXsDvrGoodsUpdateOperations.set("usedNum", wXsDvrGoods.getUsedNum() + 1);
        wXsDvrGoodsService.update(wXsDvrGoodsQuery, wXsDvrGoodsUpdateOperations);

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);
//        return ServerResponse.createSuccess("接单成功", result);

        if (StrUtil.isNotEmpty(deliveryMsg)) {
            return ServerResponse.createSuccess(deliveryMsg, result);
        } else {
            return ServerResponse.createSuccess("操作成功", result);
        }
    }

    @Override
    public ServerResponse<Order> wxLinkOrder2(String carNum, String dvrGoodsId, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //todo:验证 司机
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");
        if (StrUtil.isEmpty(carNum) && StrUtil.isEmpty(driverInfo.getCarNum()))
            return ServerResponse.createError("接单车牌号不能为空");
        if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();

//        if (driverInfoService.checkCarTareWeight(did, carNum)) return ServerResponse.createError("请先完善车辆皮重信息");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        WXsDvrGoods wXsDvrGoods = wXsDvrGoodsService.get("dvrGoodsId", dvrGoodsId);
        if (wXsDvrGoods == null) return ServerResponse.createError("订单链接不存在！");
        if (wXsDvrGoods.getUsedNum() >= wXsDvrGoods.getPartNum()) return ServerResponse.createError("车数已用完");
        String gid = wXsDvrGoods.getGid();

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("订单链接运单号错误！");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = orderService.createQuery();
        query.filter("gid", gid);
        query.filter("tranStatus", 0);
        Order order = orderService.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
//        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)

        // 判断订单是否超过可接单时间
        int hour = queryOrderHour(order);
        if (new Date().getTime() - order.getUpdateTime() > hour * 60 * 60 * 1000)
            return ServerResponse.createError("超过" + hour + "小时，订单过期");

        //TODO:2.判断司机是否存在司机池中，无则添加
        //友商优先使用订单
        //暂时屏蔽司机池添加司机
        /*String cid = StrUtil.isNotEmpty(g.getShareCid()) ? g.getShareCid() : g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee4().toString() : fee;
        } else {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee3().toString() : fee;
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = newOrderFee2(did, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        // 单位设置delivery时，请求企业端，必须企业端返回成功，则可成功接单
        Map<String, Object> deliveryMap = delivery(sysUnits, order, driverInfo, car, carNum);
        boolean su = (boolean) deliveryMap.get("su");
        String deliveryMsg = (String) deliveryMap.get("msg");
        if (!su) return ServerResponse.createError(deliveryMsg);

        //TODO:3.接单
        String oid = order.getOid();
//        int updFeeResult = this.updateFee3(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        int updFeeResult = this.updateFee4(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        } else if (updFeeResult == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (updFeeResult == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (updFeeResult == -7) {
            return ServerResponse.createError("客商余额不足");
        }

        //todo：修改WXsDveGoods数据userNum字段（使用车数）
        Query<WXsDvrGoods> wXsDvrGoodsQuery = wXsDvrGoodsService.createQuery();
        wXsDvrGoodsQuery.criteria("dvrGoodsId").equal(dvrGoodsId);
        UpdateOperations<WXsDvrGoods> wXsDvrGoodsUpdateOperations = wXsDvrGoodsService.createUpdateOperations();
        wXsDvrGoodsUpdateOperations.set("usedNum", wXsDvrGoods.getUsedNum() + 1);
        wXsDvrGoodsService.update(wXsDvrGoodsQuery, wXsDvrGoodsUpdateOperations);

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);
//        return ServerResponse.createSuccess("接单成功", result);

        if (StrUtil.isNotEmpty(deliveryMsg)) {
            return ServerResponse.createSuccess(deliveryMsg, result);
        } else {
            return ServerResponse.createSuccess("操作成功", result);
        }
    }


    @Override
    public ServerResponse<List<CompanyUnit>> searchSubUnit(String unitName) {
        Query<SysUnit> query = sysUnitService.createQuery();
        if (StrUtil.isNotEmpty(unitName)) query.criteria("name").contains(unitName);
        query.criteria("pCode").notEqual(null);
        List<SysUnit> sysUnits = sysUnitService.list(query);

        List<CompanyUnit> resultList = new ArrayList<>();
        for (SysUnit sysUnit : sysUnits) {
            CompanyUnit cu = new CompanyUnit();
            cu.setUnitCode(sysUnit.getCode());
            cu.setUnitName(sysUnit.getName());
            cu.setQuarantineInfo(sysUnit.getQuarantineInfo());
            resultList.add(cu);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<String> addQuarantineInfoBefore(String defaultDownUnit, String healthCodePho, String
            travelCardPho, String nucleicAcidPho, String vaccinationPho, String touchPro, String temperaturePro, String
                                                                  temperature) {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);

        //图片识别占资源，加锁，避免同一订单图片发生短时多次识别
        if (!lockedService.orderQuarantineLock(did, 80)) return ServerResponse.createError("提交频繁！");

        //检查 防疫申报有效期validityTime内 是否重复添加
        SysUnit sysUnit = sysUnitService.get("code", defaultDownUnit);
        Date validityTime = IdUnit.weeHours2(null, -sysUnit.getValidityTime());
        Query<QuarantineInfo> query = quarantineInfoService.createQuery();
        query.filter("did", did);
        query.criteria("type").equal(0);
        query.filter("defaultDownUnit", defaultDownUnit);
        query.criteria("createTime").greaterThanOrEq(validityTime);
        query.order(Sort.descending("createTime"));
        List<QuarantineInfo> quarantineInfos = quarantineInfoService.list(query);
        if (quarantineInfos.size() > 0 && (StrUtil.isEmpty(quarantineInfos.get(0).getAudit()) || quarantineInfos.get(0).getAudit() == 0))
            return ServerResponse.createError("已提交申报，请等待审核");
        if (quarantineInfos.size() > 0 && (quarantineInfos.get(0).getAudit() == 1 || quarantineInfos.get(0).getAudit() == 3))
            return ServerResponse.createError("重复申报");

        //添加未审核的防疫申报信息
        QuarantineInfo result = new QuarantineInfo();
        result.setId(Utils.getUUID());
        result.setHealthCodePho(healthCodePho);
        result.setTravelCardPho(travelCardPho);
        result.setVaccinationPho(vaccinationPho);
        result.setNucleicAcidPho(nucleicAcidPho);
        result.setTouchPro(touchPro);
        result.setTemperaturePro(temperaturePro);
        result.setTemperature(temperature);
        result.setDid(driverInfo.getObjectId().toHexString());
        result.setDriverInfo(driverInfo);
        result.setDefaultDownUnit(defaultDownUnit);
        //result.setOid(order.getOid());
        result.setAudit(0);
        result.setType(0);
        quarantineInfoService.save(result);

        return ServerResponse.createSuccess("防疫申报成功");
    }

    @Override
    public ServerResponse<String> getHistoricalTrack(String oid) {
        Order order = orderService.get("oid", oid);
        if ((StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutUnitCode().equals("**********")) || (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInUnitCode().equals("**********"))) {
            Date endTimeDate = null;
            if (order.getMold() == 0) {
                if (StrUtil.isNotEmpty(order.getTime3())) endTimeDate = order.getTime3();
            } else {
                if (StrUtil.isNotEmpty(order.getTime5())) endTimeDate = order.getTime5();
            }
            String carNum = order.getCarNum();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            String startTime;
            if (StrUtil.isEmpty(order.getTime1())) {
                startTime = sdf.format(order.getCreateTime());
            } else {
                startTime = sdf.format(order.getTime1());
            }
            if (StrUtil.isEmpty(endTimeDate)) endTimeDate = new Date(order.getUpdateTime());
            if (endTimeDate.equals(startTime)) endTimeDate = IdUnit.weeHours1(order.getUpdateTime(), "00:00:00", 1);
            String endTime = sdf.format(endTimeDate);
            String jsonStr = contractService.getHistoricalTrack(carNum, startTime, endTime);
            return ServerResponse.createSuccess(jsonStr);
        } else {
            return ServerResponse.createError("服务暂未开启，敬请期待");
        }
    }

    @Override
    public ServerResponse<Map<String, String>> getPhotoByOid(String oid) {
        Map<String, String> result = new HashMap<>();
        result.put("pho1", photoFileService.signUrl("guobang1"));
        result.put("pho2", photoFileService.signUrl("guobang4"));
        return ServerResponse.createSuccess(result);
    }

    @Override
    public ServerResponse<String> getHotLineByOid(String oid) {
        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");

        // 确定当前二级单位编号
        String subCode;
        String subName;
        int mold = order.getMold();
        if (mold == 0) {
            subCode = order.getOutDefaultDownUnit();
            subName = order.getOutSubName();
        } else if (mold == 1) {
            subCode = order.getInDefaultDownUnit();
            subName = order.getInSubName();
        } else {
            int outChecking = order.getOutChecking();
            if (outChecking != 3) {
                subCode = order.getOutDefaultDownUnit();
                subName = order.getOutSubName();
            } else {
                subCode = order.getInDefaultDownUnit();
                subName = order.getInSubName();
            }
        }

        // 确定当前时间 HH:mm:ss
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String time = sdf.format(new Date());

        // 查询热线电话
        Query<HotLine> query = hotLineService.createQuery();
        query.criteria("subCode").equal(subCode);
        query.and(
                query.criteria("online").lessThanOrEq(time),
                query.criteria("offline").greaterThan(time)
        );
        HotLine hotLine = hotLineService.get(query);
        if (hotLine != null) {
            String mobile = hotLine.getMobile();
            return ServerResponse.createSuccess("查询成功", mobile);
        } else {
            return ServerResponse.createError(subName + "在本时段暂无热线服务");
        }
    }

    @Override
    public ServerResponse<Map<String, Object>> checkInNetWeight(String oid) {
        Map<String, Object> result = new HashMap<>();
        List<Integer> checkList = new ArrayList<>();

        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("参数错误");
        if (order.getMold() != 1) {
            checkList.add(0);
            checkList.add(0);
            checkList.add(0);
            checkList.add(0);
            result.put("checkList", checkList);
            result.put("inNetWeight", 0.0);
            result.put("loadPound", "");
            result.put("loadTime", null);
            result.put("loadPoundPho", "");
            return ServerResponse.createSuccess(result);
        }

        double inNetWeight = 0;
        String loadPound = "";
        Long loadTime = null;
        String loadPoundPho = "";

        SysUnit inSubUnit = sysUnitService.get("code", order.getInDefaultDownUnit());

        //1.判断是否需要司机录入或修改 对方净重值
        if (StrUtil.isEmpty(inSubUnit.getInNeedNetWeight()) || inSubUnit.getInNeedNetWeight() == 0 || inSubUnit.getInNeedNetWeight() == 1) {
            checkList.add(0);
        } else if (StrUtil.isEmpty(order.getInNetWeight())) {
            if (inSubUnit.getInNeedNetWeight() == 2) {
                checkList.add(1);
            } else {
                checkList.add(0);
            }
        } else {
            inNetWeight = order.getInNetWeight();
            if (inSubUnit.getInNeedNetWeight() == 2) {
                checkList.add(2);
            } else {
                checkList.add(0);
            }
        }
        //2.判断是否需要司机录入或修改 装货单号
        if (StrUtil.isEmpty(inSubUnit.getInNeedLoadPound()) || inSubUnit.getInNeedLoadPound() == 0 || inSubUnit.getInNeedLoadPound() == 1) {
            checkList.add(0);
        } else if (StrUtil.isEmpty(order.getLoadPound())) {
            if (inSubUnit.getInNeedLoadPound() == 2) {
                checkList.add(1);
            } else {
                checkList.add(0);
            }
        } else {
            loadPound = order.getLoadPound();
            if (inSubUnit.getInNeedLoadPound() == 2) {
                checkList.add(2);
            } else {
                checkList.add(0);
            }
        }

        //3.判断是否需要司机录入或修改 装货时间
        if (StrUtil.isEmpty(inSubUnit.getInNeedLoadTime()) || inSubUnit.getInNeedLoadTime() == 0 || inSubUnit.getInNeedLoadTime() == 1) {
            checkList.add(0);
        } else if (StrUtil.isEmpty(order.getLoadTime())) {
            if (inSubUnit.getInNeedLoadTime() == 2) {
                checkList.add(1);
            } else {
                checkList.add(0);
            }
        } else {
            loadTime = order.getLoadTime();
            if (inSubUnit.getInNeedLoadTime() == 2) {
                checkList.add(2);
            } else {
                checkList.add(0);
            }
        }

        //4.判断是否需要司机上传或修改 装货单照片
        if (StrUtil.isEmpty(inSubUnit.getInLoadPoundPho()) || inSubUnit.getInLoadPoundPho() == 0 || inSubUnit.getInLoadPoundPho() == 1) {
            checkList.add(0);
        } else if (StrUtil.isEmpty(order.getLoadPoundPho())) {
            if (inSubUnit.getInLoadPoundPho() == 2) {
                checkList.add(1);
            } else {
                checkList.add(0);
            }
        } else {
            loadPoundPho = photoFileService.publicReadUrl(order.getLoadPoundPho());
            if (inSubUnit.getInLoadPoundPho() == 2) {
                checkList.add(2);
            } else {
                checkList.add(0);
            }
        }

        result.put("checkList", checkList);
        result.put("inNetWeight", inNetWeight);
        result.put("loadPound", loadPound);
        result.put("loadTime", loadTime);
        result.put("loadPoundPho", loadPoundPho);
        return ServerResponse.createSuccess(result);
    }

    @Override
    public ServerResponse<String> updateInNetWeight(String oid, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime) {
        Order order = orderService.get("oid", oid);
        if (order.getMold() != 1) return ServerResponse.createError("非收货业务，不需要录入");

        if (order.getInChecking() == 0) {
            UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
            if (StrUtil.isNotEmpty(inNetWeight)) {
                updateOperations.set("inNetWeight", inNetWeight);
                updateOperations.set("inNetWeightPerson", "DU");
            }
            if (StrUtil.isNotEmpty(loadPound)) {
                updateOperations.set("loadPound", loadPound);
                updateOperations.set("loadPoundPerson", "DU");
            }
            if (StrUtil.isNotEmpty(loadTime)) {
                updateOperations.set("loadTime", loadTime);
                updateOperations.set("loadTimePerson", "DU");
            }
            if (StrUtil.isNotEmpty(loadPoundPho)) {
                updateOperations.set("loadPoundPho", loadPoundPho);
                updateOperations.set("loadPoundPhoPerson", "DU");
            }

            Query<Order> query = orderService.createQuery();
            query.filter("oid", order.getOid());
            query.filter("inChecking", 0);
            query.filter("updateTime", order.getUpdateTime());
            UpdateResults result = orderService.update(query, updateOperations);
            if (result != null && result.getUpdatedCount() > 0) {
                deliveryInNetWeight(order.getInUnitCode(), order.getInBillCode(), inNetWeight, loadPound, loadPoundPho, loadTime);
                return ServerResponse.createSuccess("订单修改成功");
            } else {
                return ServerResponse.createError("订单修改失败");
            }
        } else {
            return ServerResponse.createError("车辆已入场，修改失败");
        }
    }

    private void deliveryInNetWeight(String inUnitCode, String inBillCode, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime) {
        SysUnit inUnit = sysUnitService.get("code", inUnitCode);
        if (StrUtil.isNotEmpty(inUnit.getDelivery()) && inUnit.getDelivery() == 1) {
            Map<String, Object> map = new HashMap<>();
            map.put("billCode", inBillCode);

            if (StrUtil.isNotEmpty(inNetWeight)) map.put("inNetWeight", inNetWeight);
            if (StrUtil.isNotEmpty(loadPoundPho)) {
                String phoUrl = photoFileService.publicReadUrl(loadPoundPho);
                map.put("inNetWeightPho", phoUrl);
            }
            if (StrUtil.isNotEmpty(loadPound)) map.put("loadPound", loadPound);
            if (StrUtil.isNotEmpty(loadTime)) map.put("loadTime", loadTime);

            companyOrderService.queryCompanyDeliveryPound(inUnit.getIp(), map);
        }
    }

    @Override
    public int queryOrderHour(Order order) {
        int hour = 72;
        if (StrUtil.isNotEmpty(order.getOutUnitCode())) {
            SysUnit outUnit = sysUnitService.get("code", order.getOutUnitCode());
            if (StrUtil.isNotEmpty(outUnit.getDvrOrderHour())) hour = outUnit.getDvrOrderHour();
        }
        if (StrUtil.isNotEmpty(order.getInUnitCode()) && !order.getInUnitCode().equals(order.getOutUnitCode())) {
            SysUnit inUnit = sysUnitService.get("code", order.getInUnitCode());
            if (StrUtil.isNotEmpty(inUnit.getDvrOrderHour()) && hour > inUnit.getDvrOrderHour())
                hour = inUnit.getDvrOrderHour();
        }
        return hour;
    }

    @Override
    public ServerResponse<HandlingCostPojo> searchCostPay(String oid, String bizContractCode) {
        HandlingCostPojo costPojo = new HandlingCostPojo();

        Order order = orderService.get("oid", oid);
        if (bizContractCode.equals(order.getOutBizContractCode())) {
            costPojo.setOutSubName(order.getOutSubName());
            costPojo.setOutDefaultDownUnit(order.getOutDefaultDownUnit());
            costPojo.setOutBizContractCode(order.getOutBizContractCode());
            costPojo.setVehicleName(StrUtil.isNotEmpty(order.getVehicleNameOutAPP()) ? order.getVehicleNameOutAPP() : order.getVehicleNameOutDvr());
            costPojo.setVehicleCode(StrUtil.isNotEmpty(order.getVehicleCodeOutAPP()) ? order.getVehicleCodeOutAPP() : order.getVehicleCodeOutDvr());

            int loadCostTem = 0;
            int totalFeeTem = 0;
            if (StrUtil.isNotEmpty(order.getVehicleNameOutDvr())) {
                loadCostTem = order.getHandlingCostOutDvr();
                totalFeeTem = order.getHandlingCostOutDvr();
                if (StrUtil.isNotEmpty(order.getHandlingCostOutDvrSupplementary()))
                    totalFeeTem = totalFeeTem + order.getHandlingCostOutDvrSupplementary();
            }
            if (StrUtil.isNotEmpty(order.getVehicleCodeOutAPP())) loadCostTem = order.getHandlingCostOutAPP();
            String loadCostStr = String.format("%03d", loadCostTem);
            String loadCostStr1 = loadCostStr.substring(0, loadCostStr.length() - 2);
            String loadCostStr2 = loadCostStr.substring(loadCostStr.length() - 2);
            Double loadCost = Double.valueOf(loadCostStr1 + "." + loadCostStr2);
            costPojo.setLoadCost(loadCost);

            String totalFeeStr = String.format("%03d", totalFeeTem);
            String totalFeetStr1 = totalFeeStr.substring(0, totalFeeStr.length() - 2);
            String totalFeeStr2 = totalFeeStr.substring(totalFeeStr.length() - 2);
            Double totalFee = Double.valueOf(totalFeetStr1 + "." + totalFeeStr2);
            costPojo.setTotalFee(totalFee);

            costPojo.setPayMsg(order.getPay1() || order.getPay2() ? "支付成功" : "未支付");

            if (order.getPay1() || order.getPay2()) {
                Map<String, List<String>> map = wxPrepaidCostService.searchPayId(oid, bizContractCode);
                costPojo.setOutTradeNo(map.get("outTradeNoList"));
                costPojo.setTransactionId(map.get("transactionIdList"));
            }

            if (loadCost > totalFee) {
                costPojo.setSupplementary(true);
                int supplementaryFeeTem = loadCostTem - totalFeeTem;
                String supplementaryFeeStr = String.format("%03d", supplementaryFeeTem);
                String supplementaryFeeStr1 = supplementaryFeeStr.substring(0, supplementaryFeeStr.length() - 2);
                String supplementaryFeeStr2 = supplementaryFeeStr.substring(supplementaryFeeStr.length() - 2);
                Double supplementaryFee = Double.valueOf(supplementaryFeeStr1 + "." + supplementaryFeeStr2);
                costPojo.setSupplementaryFee(supplementaryFee);
            } else {
                costPojo.setSupplementary(false);
                costPojo.setSupplementaryFee(0.0);
            }
        } else {
            costPojo.setInSubName(order.getInSubName());
            costPojo.setInDefaultDownUnit(order.getInDefaultDownUnit());
            costPojo.setInBizContractCode(order.getInBizContractCode());
            costPojo.setVehicleName(StrUtil.isNotEmpty(order.getVehicleNameInAPP()) ? order.getVehicleNameInAPP() : order.getVehicleNameInDvr());
            costPojo.setVehicleCode(StrUtil.isNotEmpty(order.getVehicleCodeInAPP()) ? order.getVehicleCodeInAPP() : order.getVehicleCodeInDvr());

            int loadCostTem = 0;
            int totalFeeTem = 0;
            if (StrUtil.isNotEmpty(order.getVehicleNameInDvr())) {
                loadCostTem = order.getHandlingCostInDvr();
                totalFeeTem = order.getHandlingCostInDvr();
                if (StrUtil.isNotEmpty(order.getHandlingCostInDvrSupplementary()))
                    totalFeeTem = totalFeeTem + order.getHandlingCostInDvrSupplementary();
            }
            if (StrUtil.isNotEmpty(order.getVehicleNameInAPP())) loadCostTem = order.getHandlingCostInAPP();
            String loadCostStr = String.format("%03d", loadCostTem);
            String loadCostStr1 = loadCostStr.substring(0, loadCostStr.length() - 2);
            String loadCostStr2 = loadCostStr.substring(loadCostStr.length() - 2);
            Double loadCost = Double.valueOf(loadCostStr1 + "." + loadCostStr2);
            costPojo.setLoadCost(loadCost);

            String totalFeeStr = String.format("%03d", totalFeeTem);
            String totalFeetStr1 = totalFeeStr.substring(0, totalFeeStr.length() - 2);
            String totalFeeStr2 = totalFeeStr.substring(totalFeeStr.length() - 2);
            Double totalFee = Double.valueOf(totalFeetStr1 + "." + totalFeeStr2);
            costPojo.setTotalFee(totalFee);

            costPojo.setPayMsg(order.getPay3() || order.getPay4() ? "支付成功" : "未支付");

            if (order.getPay3() || order.getPay4()) {
                Map<String, List<String>> map = wxPrepaidCostService.searchPayId(oid, bizContractCode);
                costPojo.setOutTradeNo(map.get("outTradeNoList"));
                costPojo.setTransactionId(map.get("transactionIdList"));
            }

            if (loadCost > totalFee) {
                costPojo.setSupplementary(true);
                int supplementaryFeeTem = loadCostTem - totalFeeTem;
                String supplementaryFeeStr = String.format("%03d", supplementaryFeeTem);
                String supplementaryFeeStr1 = supplementaryFeeStr.substring(0, supplementaryFeeStr.length() - 2);
                String supplementaryFeeStr2 = supplementaryFeeStr.substring(supplementaryFeeStr.length() - 2);
                Double supplementaryFee = Double.valueOf(supplementaryFeeStr1 + "." + supplementaryFeeStr2);
                costPojo.setSupplementaryFee(supplementaryFee);
            } else {
                costPojo.setSupplementary(false);
                costPojo.setSupplementaryFee(0.0);
            }
        }

        return ServerResponse.createSuccess(costPojo);
    }
}
