package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.manage.pojo.ImageResult;
import com.erdos.coal.park.api.manage.service.IImgVerifyTokenService;
import com.erdos.coal.park.api.manage.service.ISMSLimitService;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.web.sys.service.ISysUserService;
import com.erdos.coal.utils.ImgUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by LIGX on 2018/12/27.
 * 短信接口
 */
//"短信控制器接口列表"
@RestController
@RequestMapping("/api/manage")
public class SmsController extends BaseController {
    @Resource
    private ISMSService smsService;
    @Resource
    private ISMSLimitService smsLimitService;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private IImgVerifyTokenService iImgVerifyTokenService;

    //短信验证码, 在注册之前的短信接口
    @InvokeLog(description = "发送短信验证码接口") //日志
    @PostMapping(value = "/sms")
    public ServerResponse<String> smsHandler(
            @RequestParam String mobile    //"手机号码"
    ) throws GlobalException {
        // if (mobile.equals("15357039213")) return ServerResponse.createSuccess("发送成功");

//        int total = smsLimitService.smsTotalByMobile(mobile);
//        if ("18704983577".equals(mobile) || "15049598533".equals(mobile) || "15804890092".equals(mobile)) { //史芳瑞、贾晓雪、越美丽
        /*if (sysUserService.get("loginName", mobile) != null) {  //web用户短信限制放开到20条。
            // if (total >= 20) return ServerResponse.createError("超出短信限额");
        } else {
            if (total >= 30) return ServerResponse.createError("超出短信限额");
        }
        smsService.sendSMS(mobile);

        smsLimitService.addSmsLimit(mobile);*/
        return ServerResponse.createSuccess("接口停用");
    }

    @InvokeLog(description = "发送短信验证码接口") //日志
    @PostMapping(value = "/sms2")
    public ServerResponse<String> sms2Handler(
            @RequestParam String mobile,    //"手机号码"
            @RequestParam String imgCode    //"图片滑块验证成功授权码"
    ) throws GlobalException {
        // if (mobile.equals("15357039213")) return ServerResponse.createSuccess("发送成功");
        if (iImgVerifyTokenService.checkImaVerifyToken(imgCode)) {
            smsService.sendSMS(mobile);
//            ServerResponse response = smsService.sendSMS(mobile);
//            if (response.getStatus() != 0) return ServerResponse.createError("发送失败,1小时后重试或联系管理员");
            return ServerResponse.createSuccess("发送成功");
        } else {
            return ServerResponse.createError("验证失败！");
        }
    }

    //拼图验证 - 获取拼图
    @InvokeLog(description = "获取拼图") //日志
    @PostMapping(value = "/get_img")
    public ServerResponse<ImageResult> getImgHandler() throws GlobalException {
        try {
            ImageResult imageResult = new ImgUtil().imageResult(); // 生成图片
            int xPosCache = imageResult.getXpos();
            String id = iImgVerifyTokenService.saveXpos(xPosCache);
            imageResult.setXpos(0); //清空x值
            imageResult.setId(id);
            return ServerResponse.createSuccess(imageResult);

        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createError("拼图获取失败");
        }
    }

    //拼图验证 - 验证拼图
    @InvokeLog(description = "验证拼图") //日志
    @PostMapping(value = "/check_img")
    public ServerResponse<String> checkImgHandler(
            @RequestParam String id,
            @RequestParam int moveX
    ) throws GlobalException {
        try {
            if (iImgVerifyTokenService.checkXpos(id, moveX)) {
                // 验证正确 , 返回临时授权码
                String verifyToken = iImgVerifyTokenService.addImgVerifyToken();
                return ServerResponse.createSuccess("验证成功", verifyToken);
            } else {
                return ServerResponse.createError("验证失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createError("验证失败");
        }
    }
}