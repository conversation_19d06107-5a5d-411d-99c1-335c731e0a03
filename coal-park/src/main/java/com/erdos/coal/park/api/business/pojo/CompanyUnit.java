package com.erdos.coal.park.api.business.pojo;

import com.erdos.coal.park.web.sys.pojo.CheckBoxData;

import java.io.Serializable;
import java.util.List;

public class CompanyUnit implements Serializable {
    private String unitCode;    //一级单位编号
    private String unitName;    //一级单位名称
    private String tradeName;   //一级单位经营商品统称

    private List<CheckBoxData> quarantineInfo;  //二级单位防疫申报信息

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public List<CheckBoxData> getQuarantineInfo() {
        return quarantineInfo;
    }

    public void setQuarantineInfo(List<CheckBoxData> quarantineInfo) {
        this.quarantineInfo = quarantineInfo;
    }
}
