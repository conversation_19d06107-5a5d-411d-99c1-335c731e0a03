package com.erdos.coal.park.api.driver.pojo;

import java.io.Serializable;
import java.util.List;

public class HandlingCostPojo implements Serializable {
    private String outSubName;
    private String outDefaultDownUnit;
    private String outBizContractCode;

    private String inSubName;
    private String inDefaultDownUnit;
    private String inBizContractCode;

    private String vehicleName;
    private String vehicleCode;
    private Double loadCost;
    private Double totalFee;
    private String payMsg;
    private List<String> outTradeNo;
    private List<String> transactionId;
    private boolean supplementary;
    private Double supplementaryFee;

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Double getLoadCost() {
        return loadCost;
    }

    public void setLoadCost(Double loadCost) {
        this.loadCost = loadCost;
    }

    public Double getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Double totalFee) {
        this.totalFee = totalFee;
    }

    public String getPayMsg() {
        return payMsg;
    }

    public void setPayMsg(String payMsg) {
        this.payMsg = payMsg;
    }

    public List<String> getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(List<String> outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public List<String> getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(List<String> transactionId) {
        this.transactionId = transactionId;
    }

    public boolean isSupplementary() {
        return supplementary;
    }

    public void setSupplementary(boolean supplementary) {
        this.supplementary = supplementary;
    }

    public Double getSupplementaryFee() {
        return supplementaryFee;
    }

    public void setSupplementaryFee(Double supplementaryFee) {
        this.supplementaryFee = supplementaryFee;
    }
}
