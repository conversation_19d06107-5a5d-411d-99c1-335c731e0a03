package com.erdos.coal.park.web.sys.pojo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class UnitData {
    private String objectId;
    private String id;
    private String name;
    private String code;
    private String ip;
    private String variety; //品种
    private double standardDeduct = 0;  //标准扣费（元）
    private double standardDeductIn = 0;  //标准扣费（元）
    private Integer wechatTimes = 0;    //微信小程序次数
    private double wechatDeduct = 0;   //微信小程序超过次数扣费标准（元）
    private Integer carsTime = 0;   //单日单车次数
    private double carDeduct = 0;  //单日单车降费（元）
    private Integer share = 0;          //是否允许分享给微信小程序  0-允许，1-不允许
    private String loginName;       //用户名
    private String password;        //密码
    private String pCode;   //上级编码
    private Integer isReplacePay = 0;   //是否代付 未设置或0-否，1-是
    private Date createTime;
    private long updateTime;
    private BigDecimal availableFee;    //单位账户余额(元)
    private Integer isAppointment = 0;  //车辆是否预约    0-否，1—是

    private Integer isPunchClock = 0; //车辆进场是否需要签到 0-否，1-是
    private Integer isQuarantine = 0; //车辆进场是否需要防疫申报 0-否，1-是
    private Integer quarantineType = 0; //车辆进场防疫申报方式 0-司机主动上传照片审核，1-厂区终端识别司机身份证上传防疫信息
    private List<CheckBoxData> quarantineInfo;    //进场车辆防疫申报内容 健康码照片、行程卡照片、核酸检测照片、疫苗接种照片、体温值、体温照片
    private String regionId;            //所属防疫区域编号
    private Integer isGrabNumber = 0;   //车辆是否抢号
    private Integer quaVerify = 0;   //防疫内容是否需要审核
    private Integer validityTime;   //防疫申报有效期（小时）

    private String gid; //地理围栏编号
    private String geoCenter;   //地理围栏中心
    private String geoRadius;   //地理围栏半径
    private Boolean startUsing; //是否启用

    private List<SubUnitGeo> subUnitGeoList;    //二级单位围栏改为多个

    private Double fUnitFee;           //一级单位要求代扣的金额（分）
    private Double fUnitFeeIn;           //一级单位要求代扣的金额（分）
    private Double sUnitFee;           //二级单位要求代扣的金额（分）
    private Double sUnitFeeIn;           //二级单位要求代扣的金额（分）
    private Integer isDelOrderBackFee;  //订单废除，是否退还费用 0-退费，1-不退费
    private Integer isForbidCusFee;     //是否禁止客商收费 0-不禁止，1-禁止
    private Double thirdPartyLedger;   //三方分账
    private String thirdPartyAccount;   //三方账号(个人微信openid)
    private String thirdPartyAccount2;   //三方账号(商户ID)
    private String thirdPartyName;      //三方账号名称
    private String thirdPartyPhone;      //三方账号微信手机号

    private String smsCode; //设置三方账户需要短信验证码

    private List<List<String>> subUnitExcavateArea;         //lng+,+lat
    private List<String> subUnitExitPoint;            //出口标记
    private List<String> subUnitEntryPoint;           //入口标记
    private List<String> subUnitEmptyPoundPoint;      //空磅标记
    private List<String> subUnitHeavyPoundPoint;      //重磅标记
    private List<String> subUnitLoadTruckPoint;       //装车点标记
    private List<String> subUnitUnloadTruckPoint;     //卸车点标记
    private String subUnitAuthCode;             //二级单位授权码

    //优惠退费类型
    private Integer preferentialRefundType;         //0-按次返还，当车数达到短盘车数，超过收费车数的订单扣费退还司机账户
    //1-按单价返还，当车数达到短盘车数，按优惠单价收费，多收费用退还司机账户
    //2-按次按单价返还，当车数达到短盘车数，收费车数按优惠单价收费，多收费用 和 超过收费车数的扣费退还司机账户
    //二级单位定时计划任务字段
    private Integer duanPanNum;                     //短盘车数
    private Integer shouFeiNum;                     //收费车数
    private Double preferentialPFPrice;              //平台优惠单价(元)
    private Double preferentialTPPrice;              //三方优惠单价(元)

    //企业推送车牌号接口，二级单位设置语音叫号服务
    private Integer isCall;     // 0或空-不电话语音叫号，1-电话语音叫号

    // 二级单位配置智慧能源授权码
    private String smartEnergyAccessCode;

    private Integer inNeedNetWeight;        // 二级单位配置，采购业务是否需要录入净重 - 0或空 不需要、1需要客商录入、2需要司机录入净重值
    private Integer inNeedLoadPound;        // 二级单位配置，采购业务是否需要录入装货单号 - 0或空 不需要、1需要客商录入、2需要司机录入单号
    private Integer inNeedLoadTime;         // 二级单位配置，采购业务是够需要录入装货时间 - 0或空 不需要、1需要客商录入、2需要司机录入装货时间
    private Integer inLoadPoundPho;           // 二级单位配置，采购业务是否需要上传装货单照片 - 0或空 不需要、1需要客商上传、2需要司机上传

    // 企业部署 菜单添加"司机接单是否回调"设置
    private Integer delivery;   // 0或空 不需要、1需要
    // 企业部署 菜单添加"客商下单量"配置 和 "司机接单时限"配置 ；若未配置，则默认100车 和 72小时 限制；最高可设置300车
    private Integer goodsTotal;     // 空 默认100车
    private Integer dvrOrderHour;   // 空 默认72小时

    // 一级单位设置是否代收装卸费，以及收费商户号和key
    private Integer handlingCost;   //0-否 1-是
    private String mchId;
    private String key;

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public Integer getWechatTimes() {
        return wechatTimes;
    }

    public void setWechatTimes(Integer wechatTimes) {
        this.wechatTimes = wechatTimes;
    }

    public Integer getCarsTime() {
        return carsTime;
    }

    public void setCarsTime(Integer carsTime) {
        this.carsTime = carsTime;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getpCode() {
        return pCode;
    }

    public void setpCode(String pCode) {
        this.pCode = pCode;
    }

    public Integer getIsReplacePay() {
        return isReplacePay;
    }

    public void setIsReplacePay(Integer isReplacePay) {
        this.isReplacePay = isReplacePay;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getAvailableFee() {
        return availableFee;
    }

    public void setAvailableFee(BigDecimal availableFee) {
        this.availableFee = availableFee;
    }

    public double getStandardDeduct() {
        return standardDeduct;
    }

    public void setStandardDeduct(double standardDeduct) {
        this.standardDeduct = standardDeduct;
    }

    public double getStandardDeductIn() {
        return standardDeductIn;
    }

    public void setStandardDeductIn(double standardDeductIn) {
        this.standardDeductIn = standardDeductIn;
    }

    public double getWechatDeduct() {
        return wechatDeduct;
    }

    public void setWechatDeduct(double wechatDeduct) {
        this.wechatDeduct = wechatDeduct;
    }

    public double getCarDeduct() {
        return carDeduct;
    }

    public void setCarDeduct(double carDeduct) {
        this.carDeduct = carDeduct;
    }

    public Integer getIsAppointment() {
        return isAppointment;
    }

    public void setIsAppointment(Integer isAppointment) {
        this.isAppointment = isAppointment;
    }

    public Integer getIsPunchClock() {
        return isPunchClock;
    }

    public void setIsPunchClock(Integer isPunchClock) {
        this.isPunchClock = isPunchClock;
    }

    public Integer getIsQuarantine() {
        return isQuarantine;
    }

    public void setIsQuarantine(Integer isQuarantine) {
        this.isQuarantine = isQuarantine;
    }

    public Integer getQuarantineType() {
        return quarantineType;
    }

    public void setQuarantineType(Integer quarantineType) {
        this.quarantineType = quarantineType;
    }

    public List<CheckBoxData> getQuarantineInfo() {
        return quarantineInfo;
    }

    public void setQuarantineInfo(List<CheckBoxData> quarantineInfo) {
        this.quarantineInfo = quarantineInfo;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public Integer getIsGrabNumber() {
        return isGrabNumber;
    }

    public void setIsGrabNumber(Integer isGrabNumber) {
        this.isGrabNumber = isGrabNumber;
    }

    public Integer getQuaVerify() {
        return quaVerify;
    }

    public void setQuaVerify(Integer quaVerify) {
        this.quaVerify = quaVerify;
    }

    public Integer getValidityTime() {
        return validityTime;
    }

    public void setValidityTime(Integer validityTime) {
        this.validityTime = validityTime;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getGeoCenter() {
        return geoCenter;
    }

    public void setGeoCenter(String geoCenter) {
        this.geoCenter = geoCenter;
    }

    public String getGeoRadius() {
        return geoRadius;
    }

    public void setGeoRadius(String geoRadius) {
        this.geoRadius = geoRadius;
    }

    public Boolean getStartUsing() {
        return startUsing;
    }

    public void setStartUsing(Boolean startUsing) {
        this.startUsing = startUsing;
    }

    public List<SubUnitGeo> getSubUnitGeoList() {
        return subUnitGeoList;
    }

    public void setSubUnitGeoList(List<SubUnitGeo> subUnitGeoList) {
        this.subUnitGeoList = subUnitGeoList;
    }

    public Double getfUnitFee() {
        return fUnitFee;
    }

    public void setfUnitFee(Double fUnitFee) {
        this.fUnitFee = fUnitFee;
    }

    public Double getfUnitFeeIn() {
        return fUnitFeeIn;
    }

    public void setfUnitFeeIn(Double fUnitFeeIn) {
        this.fUnitFeeIn = fUnitFeeIn;
    }

    public Double getsUnitFee() {
        return sUnitFee;
    }

    public void setsUnitFee(Double sUnitFee) {
        this.sUnitFee = sUnitFee;
    }

    public Double getsUnitFeeIn() {
        return sUnitFeeIn;
    }

    public void setsUnitFeeIn(Double sUnitFeeIn) {
        this.sUnitFeeIn = sUnitFeeIn;
    }

    public Integer getIsDelOrderBackFee() {
        return isDelOrderBackFee;
    }

    public void setIsDelOrderBackFee(Integer isDelOrderBackFee) {
        this.isDelOrderBackFee = isDelOrderBackFee;
    }

    public Integer getIsForbidCusFee() {
        return isForbidCusFee;
    }

    public void setIsForbidCusFee(Integer isForbidCusFee) {
        this.isForbidCusFee = isForbidCusFee;
    }

    public Double getThirdPartyLedger() {
        return thirdPartyLedger;
    }

    public void setThirdPartyLedger(Double thirdPartyLedger) {
        this.thirdPartyLedger = thirdPartyLedger;
    }

    public String getThirdPartyAccount() {
        return thirdPartyAccount;
    }

    public void setThirdPartyAccount(String thirdPartyAccount) {
        this.thirdPartyAccount = thirdPartyAccount;
    }

    public String getThirdPartyAccount2() {
        return thirdPartyAccount2;
    }

    public void setThirdPartyAccount2(String thirdPartyAccount2) {
        this.thirdPartyAccount2 = thirdPartyAccount2;
    }

    public String getThirdPartyName() {
        return thirdPartyName;
    }

    public void setThirdPartyName(String thirdPartyName) {
        this.thirdPartyName = thirdPartyName;
    }

    public String getThirdPartyPhone() {
        return thirdPartyPhone;
    }

    public void setThirdPartyPhone(String thirdPartyPhone) {
        this.thirdPartyPhone = thirdPartyPhone;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public List<List<String>> getSubUnitExcavateArea() {
        return subUnitExcavateArea;
    }

    public void setSubUnitExcavateArea(List<List<String>> subUnitExcavateArea) {
        this.subUnitExcavateArea = subUnitExcavateArea;
    }

    public List<String> getSubUnitExitPoint() {
        return subUnitExitPoint;
    }

    public void setSubUnitExitPoint(List<String> subUnitExitPoint) {
        this.subUnitExitPoint = subUnitExitPoint;
    }

    public List<String> getSubUnitEntryPoint() {
        return subUnitEntryPoint;
    }

    public void setSubUnitEntryPoint(List<String> subUnitEntryPoint) {
        this.subUnitEntryPoint = subUnitEntryPoint;
    }

    public List<String> getSubUnitEmptyPoundPoint() {
        return subUnitEmptyPoundPoint;
    }

    public void setSubUnitEmptyPoundPoint(List<String> subUnitEmptyPoundPoint) {
        this.subUnitEmptyPoundPoint = subUnitEmptyPoundPoint;
    }

    public List<String> getSubUnitHeavyPoundPoint() {
        return subUnitHeavyPoundPoint;
    }

    public void setSubUnitHeavyPoundPoint(List<String> subUnitHeavyPoundPoint) {
        this.subUnitHeavyPoundPoint = subUnitHeavyPoundPoint;
    }

    public List<String> getSubUnitLoadTruckPoint() {
        return subUnitLoadTruckPoint;
    }

    public void setSubUnitLoadTruckPoint(List<String> subUnitLoadTruckPoint) {
        this.subUnitLoadTruckPoint = subUnitLoadTruckPoint;
    }

    public List<String> getSubUnitUnloadTruckPoint() {
        return subUnitUnloadTruckPoint;
    }

    public void setSubUnitUnloadTruckPoint(List<String> subUnitUnloadTruckPoint) {
        this.subUnitUnloadTruckPoint = subUnitUnloadTruckPoint;
    }

    public String getSubUnitAuthCode() {
        return subUnitAuthCode;
    }

    public void setSubUnitAuthCode(String subUnitAuthCode) {
        this.subUnitAuthCode = subUnitAuthCode;
    }

    public Integer getPreferentialRefundType() {
        return preferentialRefundType;
    }

    public void setPreferentialRefundType(Integer preferentialRefundType) {
        this.preferentialRefundType = preferentialRefundType;
    }

    public Integer getDuanPanNum() {
        return duanPanNum;
    }

    public void setDuanPanNum(Integer duanPanNum) {
        this.duanPanNum = duanPanNum;
    }

    public Integer getShouFeiNum() {
        return shouFeiNum;
    }

    public void setShouFeiNum(Integer shouFeiNum) {
        this.shouFeiNum = shouFeiNum;
    }

    public Double getPreferentialPFPrice() {
        return preferentialPFPrice;
    }

    public void setPreferentialPFPrice(Double preferentialPFPrice) {
        this.preferentialPFPrice = preferentialPFPrice;
    }

    public Double getPreferentialTPPrice() {
        return preferentialTPPrice;
    }

    public void setPreferentialTPPrice(Double preferentialTPPrice) {
        this.preferentialTPPrice = preferentialTPPrice;
    }

    public Integer getIsCall() {
        return isCall;
    }

    public void setIsCall(Integer isCall) {
        this.isCall = isCall;
    }

    public String getSmartEnergyAccessCode() {
        return smartEnergyAccessCode;
    }

    public void setSmartEnergyAccessCode(String smartEnergyAccessCode) {
        this.smartEnergyAccessCode = smartEnergyAccessCode;
    }

    public Integer getInNeedNetWeight() {
        return inNeedNetWeight;
    }

    public void setInNeedNetWeight(Integer inNeedNetWeight) {
        this.inNeedNetWeight = inNeedNetWeight;
    }

    public Integer getInNeedLoadPound() {
        return inNeedLoadPound;
    }

    public void setInNeedLoadPound(Integer inNeedLoadPound) {
        this.inNeedLoadPound = inNeedLoadPound;
    }

    public Integer getInNeedLoadTime() {
        return inNeedLoadTime;
    }

    public void setInNeedLoadTime(Integer inNeedLoadTime) {
        this.inNeedLoadTime = inNeedLoadTime;
    }

    public Integer getInLoadPoundPho() {
        return inLoadPoundPho;
    }

    public void setInLoadPoundPho(Integer inLoadPoundPho) {
        this.inLoadPoundPho = inLoadPoundPho;
    }

    public Integer getDelivery() {
        return delivery;
    }

    public void setDelivery(Integer delivery) {
        this.delivery = delivery;
    }

    public Integer getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(Integer goodsTotal) {
        this.goodsTotal = goodsTotal;
    }

    public Integer getDvrOrderHour() {
        return dvrOrderHour;
    }

    public void setDvrOrderHour(Integer dvrOrderHour) {
        this.dvrOrderHour = dvrOrderHour;
    }

    public Integer getHandlingCost() {
        return handlingCost;
    }

    public void setHandlingCost(Integer handlingCost) {
        this.handlingCost = handlingCost;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
