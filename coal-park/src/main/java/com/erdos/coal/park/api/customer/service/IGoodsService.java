package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.WXsDvrGoods;
import com.erdos.coal.park.api.customer.pojo.GoodsInUnitData;
import com.erdos.coal.park.api.customer.pojo.GoodsVerbInfoData;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import org.apache.el.parser.BooleanNode;

import java.util.List;
import java.util.Map;

public interface IGoodsService extends IBaseMongoService<Goods> {
    //添加货运信息接口 1.1
    //ServerResponse addGoods(String cid,Map<String, Object> map);
    ServerResponse<Object> addGoods(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);
    ServerResponse<Object> addGoods2(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);
    ServerResponse<Object> addGoods3(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);
    ServerResponse<Object> addGoods4(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);
    ServerResponse<Object> addGoods5(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);
    ServerResponse<Object> addGoods6(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);
    ServerResponse<Object> addGoods7(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay, String loadPound, Long loadTime);

    //查询货运信息接口
    //ServerResponse<List<GoodsInfoData>> goodsListData(Long goodsTime, String pointName, Integer type);
    ServerResponse<EGridResult> goodsListData(Long goodsTime, String pointName, Integer type, Integer page, Integer rows);

    //查询货运信息接口-2
    ServerResponse<EGridResult> goodsListData2(Long goodsTime, String pointName, Integer type, Integer page, Integer rows);
    ServerResponse<EGridResult> goodsListData3(Long goodsTime, String pointName, Integer type, Integer page, Integer rows);
    ServerResponse<EGridResult> goodsListData4(Long goodsTime, String pointName, Integer type, Integer page, Integer rows);

    //删除货运信息 接口
    ServerResponse<String> delGoods(String gid, String[] oids);
    ServerResponse<String> delGoods2(String gid, String[] oids);
    ServerResponse<String> delGoods3(String gid, String[] oids);
    ServerResponse<String> delGoods4(String gid, String[] oids);
    ServerResponse<String> delGoods5(String gid, String[] oids);

    //批量发布货运信息接口
    ServerResponse<Object> addGoodsPL(Goods goods, String longitude, String latitude, String groupNo, Integer isPay);

    //货运信息修改-1
    ServerResponse<List<String>> updateGoods(String gid, String outVariety, String inVariety);
    ServerResponse<List<String>> updateGoods2(String gid, String outVariety, String inVariety);

    //货运信息修改运往地
    ServerResponse<List<String>> updateGoodsPlace(String gid, String spell, String place);
    ServerResponse<List<String>> updateGoodsPlace2(String gid, String spell, String place);

    //添加要分享到微信小程序的货运信息
    ServerResponse<Object> addWeChatGoods(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay);

    //分享货运信息给好友客商
    ServerResponse<String> goodsToFriend(String gid, String cid, String[] oids, Integer total);
    ServerResponse<String> goodsToFriend2(String gid, String cid, String[] oids, Integer total);

    //从好友客商回收分享的货运信息
    ServerResponse<String> regainFriendGoods(String gid, String[] oids);
    ServerResponse<String> regainFriendGoods2(String gid, String[] oids);

    //退回好友客商分享的货运信息
    ServerResponse<String> backFriendGoods(String gid, String[] oids);
    ServerResponse<String> backFriendGoods2(String gid, String[] oids);

    //查询 货运信息下包含的单位信息
    SysUnit[] searchSysUnitInGoods(Goods goods);

    //客商查询最近下单记录，由发填写的货单位信息查询验证收货单位信息
    ServerResponse<GoodsInUnitData> searchGoodsByOutUnit(String outUnitCode, String outBizContractCode, String outVariety, String outDefaultDownUnit, String outDefaultArea);
    ServerResponse<GoodsInUnitData> searchGoodsByOutUnit2(String outUnitCode, String outBizContractCode, String outVariety, String outDefaultDownUnit, String outDefaultArea);

    //客商查询最近下单记录，由发填写的货单位信息查询验证收货单位信息
    ServerResponse<GoodsVerbInfoData> searchVerbInformation(Goods goods);

    Map<String, Object> sysUnitTotalFees2(Goods goods);
    Map<String, Object> sysUnitTotalFees3(Goods goods);

    //暂停或恢复接单
    ServerResponse<List<String>> updateGoodsStatus(String gid, Integer status);
    ServerResponse<List<String>> updateGoodsStatus2(String gid, Integer status);

    //客（友）商指定车数分享给微信司机接单
    ServerResponse<WXsDvrGoods> shareToWXsDvrWithTotal(String gid, Integer total);

    ServerResponse<List<WXsDvrGoods>> getShareToWXsDvrGoodsList(String gid);

    // 查询二级单位是否设置需要录入对方净重
    ServerResponse<List<Boolean>> checkInSubUnitNeedNetWeight(String subCode);
    // 按货运信息编号，批量修改inChecking为0的订单 对方净重
    ServerResponse<List<String>> updateGoodsInNetWeight(String gid, Double inNetWeight);

    //****************************付费接单了的司机短信服务,异步发送；（对接阿里服务）*********************
    void sendSms_ali(Order order, DriverInfo driverInfo, Integer type);

    //****************************付费接单了的司机短信服务,异步发送；（对接华为服务）*********************
    // void sendSms_HuaWei(Order order, DriverInfo driverInfo, Integer type);
     void sendCall_HuaWei(String oid, String carNum, String subName, DriverInfo driverInfo);

     // 查询一级单位客商下单车数限制
    String queryUnitGoodsTotal(String outUnitCode, String inUnitCode, Integer total);

}
