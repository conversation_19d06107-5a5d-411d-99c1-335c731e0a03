package com.erdos.coal.park.api.manage.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.pojo.SysAppData;
import com.erdos.coal.park.web.sys.entity.SysApp;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface IAppManagerService extends IBaseMongoService<SysApp> {
    ServerResponse<SysAppData> getInfo(String cusOrDri);

    /**
     * 二维码扫码下载app
     * 客商：http://IP:端口/api/manage/app/download_app?url=${coal.upload-path}/customer.apk
     * 司机：http://IP:端口/api/manage/app/download_app?url=${coal.upload-path}/driver.apk
     */
    void downloadApp(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
