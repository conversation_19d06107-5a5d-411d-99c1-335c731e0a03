package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.baidu.service.AipOcrService;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.business.service.IContractService;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.dao.IWechatDriverInfoDao;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.pojo.CarData;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.api.manage.pojo.WeChatData;
import com.erdos.coal.park.api.manage.service.ICarInfoService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.service.ISysSwitchService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("wechatDriverInfoService")
public class WechatDriverInfoServiceImpl extends BaseMongoServiceImpl<WechatDriverInfo, IWechatDriverInfoDao> implements IWechatDriverInfoService {

    @Resource
    private ISysSwitchService sysSwitchService;
    @Resource
    private ICarInfoService carInfoService;
    @Resource
    private IHttpAPIService httpAPIService;
    @Resource
    private ICarDao carDao;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    private IContractService contractService;

    @Override
    public ServerResponse<AccessToken> wechatUserLogin(String code) {
        Map<String, String> map = getJscode2sessionInfo(code);
        String openid = map.get("openid");
        String sessionKey = map.get("sessionKey");

        WechatDriverInfo wechatDriverInfo = this.get("openid", openid);
        if (wechatDriverInfo == null) {
            wechatDriverInfo = new WechatDriverInfo();
            wechatDriverInfo.setOpenid(openid);
            wechatDriverInfo.setSessionKey(sessionKey);
            //Map<String, String> photoMap = sysSwitchService.getPhoto(SysConstants.UserType.UT_WECHAT.toString()); // 查询设置账户是否可用
            //if (photoMap.get("check").equals("0")) {
            if (sysSwitchService.isCheck(SysConstants.UserType.UT_WECHAT.toString())) {
                wechatDriverInfo.setState(0);
            } else {
                wechatDriverInfo.setState(4);
            }
            wechatDriverInfo = this.save(wechatDriverInfo);
        }

        //shiroUserSecurity.putShiroUserToCache(wechatDriverInfo.getObjectId().toHexString(), openid, openid.substring(7), UserType.WECHAT.toString());

        String token = JwtUtil.sign(UserType.WECHAT, openid, openid.substring(7));
        return ServerResponse.createSuccess("授权成功", new AccessToken(token));
    }

    @Override
    public ServerResponse<WeChatData> wechatUserAuth(String code) {
        Map<String, String> map = getJscode2sessionInfo(code);
        String openid = map.get("openid");
        String unionid = map.get("unionid");
        WeChatData weChatData = new WeChatData(openid, unionid);
        return ServerResponse.createSuccess("认证成功", weChatData);
    }

    private Map<String, String> getJscode2sessionInfo(String code) {
        String requestUrl = WXPayConstants.sessionHost;
        requestUrl += "?appid=" + WXPayConstants.wechat_appid + "&secret=" + WXPayConstants.wechat_secret + "&js_code=" + code + "&grant_type=" + WXPayConstants.wechat_grant_type;
        JSONObject object = null;
        try {
            object = JSON.parseObject(httpAPIService.doGet(requestUrl));
        } catch (Exception e) {
            e.printStackTrace();
        }
        assert object != null;
        String openid = object.getString("openid");
        String sessionKey = object.getString("session_key");
        String unionid = object.getString("unionid");

        if (openid == null || sessionKey == null)
            throw new GlobalException(ServerResponse.createError(object.getString("errmsg")));

        Map<String, String> map = new HashMap<>();
        map.put("openid", openid);
        map.put("sessionKey", sessionKey);
        map.put("unionid", unionid);
        return map;
    }

    @Override
    public ServerResponse<String> uploadPho(MultipartFile uploadPho, String type) {
        String did = ShiroUtils.getUserId();

        //图片上传oss
        String filename = uploadPho.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf("."));
        String key = did + type + suffix;
        String path = null;
        try {
            path = photoFileService.putObject(key, uploadPho.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 图片上传数据库和本地文件
        // String path = photoFileService.uploadPhoto0(did + type, uploadPho);

        //图片上传fastDFS
        // String path = photoFileService.uploadPhoto(uploadPho);

        if (StrUtil.isNotEmpty(path)) {
            return ServerResponse.createSuccess("图片上传成功", path);
        } else {
            return ServerResponse.createError("图片上传失败");
        }
    }

    @Override
    public ServerResponse<String> uploadPhoPublicRead(MultipartFile uploadPho, String type) {
        String did = ShiroUtils.getUserId();

        //图片上传oss
        String filename = uploadPho.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf("."));
//        String key = did + type + suffix;
//        String key = did + type + Utils.getUUID();
        String key = Utils.getUUID() + type + suffix;
        String path = null;
        try {
            path = photoFileService.putObjectPublicRead(key, uploadPho.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (StrUtil.isNotEmpty(path)) {
            return ServerResponse.createSuccess("图片上传成功", path);
        } else {
            return ServerResponse.createError("图片上传失败");
        }
    }

    @Override
    public ServerResponse<String> updateDriverInfo2(String driverPho, String driverPho2, String driIdentityPhoBef, String driIdentityPhoBack, String driverCarPho, String roadQCPho, String bankCardPho) {
        /*
         *小程序中实际是司机app注册登录的用户
         *小程序图片只能单张上传
         * */
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        assert did != null;
        UpdateOperations<DriverInfo> updateOperations = driverInfoService.createUpdateOperations();
        String name = "";
        if (driIdentityPhoBef != null) {
            //1.识别照片信息
            try {
                /*String idCard = coalConfig.uploadPath + File.separator + driIdentityPhoBef;
                Map<String, String> aipOcrMap = AipOcrService.getIdCardInfo(idCard, "front");*/

                byte[] bytes = photoFileService.getBytes(driIdentityPhoBef);
                Map<String, String> aipOcrMap = AipOcrService.getIdCardInfo(bytes, "front");

                name = aipOcrMap.get("name");
                String idNumber = aipOcrMap.get("idNumber");
                if (StrUtil.isEmpty(name) || StrUtil.isEmpty(idNumber))
                    return ServerResponse.createError("身份证照片识别出错！");

                DriverInfo idCardCheck = driverInfoService.get("identity", idNumber);
//                if (idCardCheck != null) return ServerResponse.createError("用户已经实名认证");
                if (idCardCheck != null && !did.equals(idCardCheck.getObjectId().toHexString())) return ServerResponse.createError("身份证已被其他用户实名认证");

                updateOperations.set("name", name);
                updateOperations.set("identity", idNumber);
                updateOperations.set("address", aipOcrMap.get("address"));
                updateOperations.set("birthday", aipOcrMap.get("birthday"));
            } catch (Exception e) {
                updateOperations.set("name", "");
                updateOperations.set("identity", "");
                logger.warn(e.getMessage());
                //return ServerResponse.createError("缺少身份证照片，请联系管理员！");
            }
        }
        if (driverPho != null) { //驾驶证照片正面
            //1.识别照片
            try {
                byte[] bytes = photoFileService.getBytes(driverPho);
                Map<String, String> aipOcrMap = AipOcrService.getDrivingLicense(bytes);
                String driverPhoName = aipOcrMap.get("name");
                String validTo = aipOcrMap.get("validTo");
                String validToCQ = aipOcrMap.get("validToCQ");

                if (StrUtil.isEmpty(driverPhoName) || (StrUtil.isEmpty(validTo) && StrUtil.isEmpty(validToCQ)))
                    return ServerResponse.createError("驾驶证照片识别出错！");

                if (!"长期".equals(validToCQ)) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                    String nowStr = simpleDateFormat.format(new Date());
                    if (nowStr.compareTo(validTo) > 0) return ServerResponse.createError("驾驶证过期");
                }

                if (!driverPhoName.equals(name) && driIdentityPhoBef != null)
                    return ServerResponse.createError("驾驶证和身份证姓名不一致");
            } catch (Exception e) {
                return ServerResponse.createError("驾驶证照片识别失败！");
            }
        }
        //2.保存图片到数据库
        /*Map<String, String> photoMap = new HashMap<>();
        if (driverPho != null) photoMap.put("driverPho", driverPho);  //驾驶证照片正页
        if (driverPho2 != null) photoMap.put("driverPho2", driverPho2);  //驾驶证照片副页
        if (driIdentityPhoBef != null) photoMap.put("driIdentityPhoBef", driIdentityPhoBef);  //司机身份证正面
        if (driIdentityPhoBack != null) photoMap.put("driIdentityPhoBack", driIdentityPhoBack);  //司机身份证正面
        if (roadQCPho != null) photoMap.put("roadQCPho", roadQCPho);  //道路从业资格证照片
        if (bankCardPho != null) photoMap.put("bankCardPho", bankCardPho);  //银行卡照片
        Map<String, String> pathMap = photoFileService.uploadPhotosByDB(did, "t_driver_info", photoMap);*/

        //3.保存微信司机信息到微信信息表
        /*if (StrUtil.isNotEmpty(pathMap.get("driverPhoPath")))
            updateOperations.set("driverPho", pathMap.get("driverPhoPath"));
        if (StrUtil.isNotEmpty(pathMap.get("driverPho2Path")))
            updateOperations.set("driverPho2", pathMap.get("driverPho2Path"));
        if (StrUtil.isNotEmpty(pathMap.get("driIdentityPhoBefPath")))
            updateOperations.set("driIdentityPhoBef", pathMap.get("driIdentityPhoBefPath"));
        if (StrUtil.isNotEmpty(pathMap.get("driIdentityPhoBackPath")))
            updateOperations.set("driIdentityPhoBack", pathMap.get("driIdentityPhoBackPath"));
        if (StrUtil.isNotEmpty(pathMap.get("roadQCPhoPath")))
            updateOperations.set("roadQCPho", pathMap.get("roadQCPhoPath"));
        if (StrUtil.isNotEmpty(pathMap.get("bankCardPhoPath")))
            updateOperations.set("bankCardPho", pathMap.get("bankCardPhoPath"));*/
        if (StrUtil.isNotEmpty(driverPho)) updateOperations.set("driverPho", driverPho);
        if (StrUtil.isNotEmpty(driverPho2)) updateOperations.set("driverPho2", driverPho2);
        if (StrUtil.isNotEmpty(driIdentityPhoBef)) updateOperations.set("driIdentityPhoBef", driIdentityPhoBef);
        if (StrUtil.isNotEmpty(driIdentityPhoBack)) updateOperations.set("driIdentityPhoBack", driIdentityPhoBack);
        if (StrUtil.isNotEmpty(roadQCPho)) updateOperations.set("roadQCPho", roadQCPho);
        if (StrUtil.isNotEmpty(bankCardPho)) updateOperations.set("bankCardPho", bankCardPho);
        if (driverInfo.getState() == 3) updateOperations.set("state", 0);//审核未通过的信息，在重新上传新信息后置未未审核。
        if (driverInfo.getState() == -1) updateOperations.set("state", 0);//新上传的用户完善信息后，状态改为0
        driverInfoService.update(driverInfoService.createQuery().filter("_id", new ObjectId(did)), updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<String> saveDvrCar(String carNum, String carInfoId, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack, String driverCarPho) {
        String weChatId = ShiroUtils.getUserId();
        assert weChatId != null;

        //校验账号是否有关联app账号。若有app账号，则直接使用app账号关联的车辆。
        //2.图片保存
        Car existCar = carDao.get("carNum", carNum);
        if (existCar == null || existCar.getVerify() == 2) {//车辆的图片 若之前已经上传并通过了审核，则直接忽略新上传的 而 采用之前的图片
            Map<String, String> photoMap = new HashMap<>();
            if (drivingPho1 != null) photoMap.put("drivingPho1", drivingPho1);  //行驶证照片第一页
            if (drivingPho2 != null) photoMap.put("drivingPho2", drivingPho2);  //行驶证照片第二页
            if (drivingPho3 != null) photoMap.put("drivingPho3", drivingPho3);  //行驶证照片第三页
            if (roadTCPho != null) photoMap.put("roadTCPho", roadTCPho);        //驾驶证照片
            if (carIdentityPhoBef != null) photoMap.put("carIdentityPhoBef", carIdentityPhoBef);    //驾驶证照片id
            if (carIdentityPhoBack != null) photoMap.put("carIdentityPhoBack", carIdentityPhoBack); //驾驶证照片id
            if (driverCarPho != null) photoMap.put("driverCarPho", driverCarPho); //司机和车的合影照片id
            Map<String, String> pathMap = photoFileService.uploadPhotosByDB(carNum, "t_car", photoMap);

            if (existCar == null) existCar = new Car();
            existCar.setVerify(0);
            existCar.setCarNum(carNum);
            CarInfo carInfo = carInfoService.get("id", carInfoId);
            existCar.setCarInfo(carInfo);

            if (StrUtil.isNotEmpty(pathMap.get("drivingPho1Path")))
                existCar.setDrivingPho1(pathMap.get("drivingPho1Path"));
            if (StrUtil.isNotEmpty(pathMap.get("drivingPho2Path")))
                existCar.setDrivingPho2(pathMap.get("drivingPho2Path"));
            if (StrUtil.isNotEmpty(pathMap.get("drivingPho3Path")))
                existCar.setDrivingPho3(pathMap.get("drivingPho3Path"));
            if (StrUtil.isNotEmpty(pathMap.get("roadTCPhoPath"))) existCar.setRoadTCPho(pathMap.get("roadTCPhoPath"));
            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBefPath")))
                existCar.setCarIdentityPhoBef(pathMap.get("carIdentityPhoBefPath"));
            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBackPath")))
                existCar.setCarIdentityPhoBack(pathMap.get("carIdentityPhoBackPath"));
            if (StrUtil.isNotEmpty(existCar.getUpdateTime())) existCar.setUpdateTime(new Date().getTime());
            existCar = carDao.save(existCar);
        }

        //3.司机 关联 车辆信息
        WechatDriverInfo wechatDriverInfo = this.getByPK(weChatId);
        String driverId;
        String mobile = null;
        if (StrUtil.isEmpty(wechatDriverInfo.getDid())) {
            driverId = weChatId;
        } else {
            driverId = wechatDriverInfo.getDid();
            mobile = driverInfoService.getByPK(driverId).getMobile();
        }
        Query<DriverToCar> driverToCarQuery = driverToCarDao.createQuery();
        driverToCarQuery.filter("carId", existCar.getObjectId().toHexString());
        driverToCarQuery.filter("driverId", driverId);
        DriverToCar existDriverToCar = driverToCarDao.get(driverToCarQuery);
        if (existDriverToCar != null && existDriverToCar.getDelete() == 2)
            return ServerResponse.createError("车辆存在争议，请联系管理员");

        if (existDriverToCar == null) existDriverToCar = new DriverToCar();
        existDriverToCar.setDriverId(driverId);
        existDriverToCar.setCarId(existCar.getObjectId().toHexString());
        existDriverToCar.setCarNum(carNum);
        if (StrUtil.isNotEmpty(mobile)) existDriverToCar.setMobile(mobile);

        String driverCarPhoStr = null;
        if (driverCarPho != null)
            driverCarPhoStr = photoFileService.uploadPhotoByDB(weChatId + carNum, "t_driver_to_car", driverCarPho);

        if (StrUtil.isNotEmpty(driverCarPhoStr)) existDriverToCar.setDriverCarPho(driverCarPhoStr);
        if (StrUtil.isEmpty(existDriverToCar.getDelete()) || existDriverToCar.getDelete() != 2)
            existDriverToCar.setDelete(0);
        if (StrUtil.isNotEmpty(existDriverToCar.getUpdateTime())) existDriverToCar.setUpdateTime(new Date().getTime());
        driverToCarDao.save(existDriverToCar);

        return ServerResponse.createSuccess("车辆信息添加并关联成功");
    }

    private Car saveCar(Car car, String carNum, String carInfoId, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack) {
        car.setId(Utils.getUUID());
        car.setVerify(0);
        car.setCarNum(carNum);
        CarInfo carInfo = carInfoService.get("id", carInfoId);
        car.setCarInfo(carInfo);

        //保存图片路径oss
        if (StrUtil.isNotEmpty(drivingPho1)) car.setDrivingPho1(drivingPho1);
        if (StrUtil.isNotEmpty(drivingPho2)) car.setDrivingPho2(drivingPho2);
        if (StrUtil.isNotEmpty(drivingPho3)) car.setDrivingPho3(drivingPho3);
        if (StrUtil.isNotEmpty(roadTCPho)) car.setRoadTCPho(roadTCPho);
        if (StrUtil.isNotEmpty(carIdentityPhoBef)) car.setCarIdentityPhoBef(carIdentityPhoBef);
        if (StrUtil.isNotEmpty(carIdentityPhoBack)) car.setCarIdentityPhoBack(carIdentityPhoBack);
        if (StrUtil.isNotEmpty(car.getUpdateTime())) car.setUpdateTime(new Date().getTime());
        return carDao.save(car);
    }

    @Override
    public ServerResponse<String> saveDvrCar2(String carNum, String carInfoId, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack, String driverCarPho) {
        //todo:1.根据开关设置，判断车辆是否开启GPS
        SysSwitch sysSwitch = sysSwitchService.get("code", 2);//查询车辆开关设置
        if (StrUtil.isNotEmpty(sysSwitch.getNeedGPS()) && sysSwitch.getNeedGPS() == 1) {
            Map<String, Object> map = contractService.checkTruckExistV2(carNum);
            if (StrUtil.isNotEmpty(map.get("data")) && !map.get("data").equals("1001"))
                return ServerResponse.createError(map.get("msg").toString());
        }

        /*
         *小程序中实际是司机app注册登录的用户
         *小程序图片只能单张上传
         * */
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverInfo driverInfo = driverInfoService.getByPK(did);

        //识别行驶证车牌号，比对除汉字 后的号码是否一致
        String engineNo = null;
        String owner = null;
        String vin = null;
        if (StrUtil.isNotEmpty(drivingPho1)) {
            try {
                byte[] bytes = photoFileService.getBytes(drivingPho1);
                Map<String, String> aipOcrMap = AipOcrService.getVehicleLicense(bytes);
                String plateNo = aipOcrMap.get("carNum");
                if (StrUtil.isNotEmpty(plateNo) && !plateNo.substring(1).equals(carNum.substring(1)))
                    return ServerResponse.createError("行驶证车号不一致！");
                engineNo = aipOcrMap.get("engineNo");
                owner = aipOcrMap.get("owner");
                vin = aipOcrMap.get("VIN");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //校验是否存在司机车辆信息，否则新增保存
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", did);
        dctQuery.filter("carNum", carNum);
//        dctQuery.filter("delete nin", 1);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar != null) {
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车牌：" + carNum + "被限制使用，请联系管理员");

            Car existCar = carDao.getByPK(driverToCar.getCarId());
            if (existCar == null) {
                existCar = new Car();
                if (StrUtil.isNotEmpty(engineNo)) existCar.setEngineNo(engineNo);
                if (StrUtil.isNotEmpty(owner)) existCar.setOwner(owner);
                if (StrUtil.isNotEmpty(vin)) existCar.setVin(vin);
                existCar = saveCar(existCar, carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack);
            } else if (existCar.getVerify() == 4) {
                return ServerResponse.createError("车辆：" + existCar.getCarNum() + "被停用，关联失败");
            } else if (existCar.getVerify() != 1 || !carNum.equals(existCar.getCarNum())) {    //车辆信息已经存在，且未通过审核，则修改车牌号信息
                Query<Car> carQuery = carDao.createQuery().filter("_id", existCar.getObjectId());
                UpdateOperations<Car> upCarProAndNum = carDao.createUpdateOperations().set("carNum", carNum);
                if (StrUtil.isNotEmpty(drivingPho1)) upCarProAndNum.set("drivingPho1", drivingPho1);
                if (StrUtil.isNotEmpty(drivingPho2)) upCarProAndNum.set("drivingPho2", drivingPho2);
                if (StrUtil.isNotEmpty(drivingPho3)) upCarProAndNum.set("drivingPho3", drivingPho1);
                if (StrUtil.isNotEmpty(roadTCPho)) upCarProAndNum.set("roadTCPho", roadTCPho);
                if (StrUtil.isNotEmpty(carIdentityPhoBef)) upCarProAndNum.set("carIdentityPhoBef", carIdentityPhoBef);
                if (StrUtil.isNotEmpty(carIdentityPhoBack))
                    upCarProAndNum.set("carIdentityPhoBack", carIdentityPhoBack);
                if (StrUtil.isNotEmpty(engineNo)) upCarProAndNum.set("engineNo", engineNo);
                if (StrUtil.isNotEmpty(owner)) upCarProAndNum.set("owner", owner);
                if (StrUtil.isNotEmpty(vin)) upCarProAndNum.set("vin", vin);
                if (existCar.getVerify() == 2) upCarProAndNum.set("verify", 0); //若车辆审核未通过，则在司机重新上传车辆信息后状态改为未审核。
                carDao.update(carQuery, upCarProAndNum);
            }

            {
                Query<DriverToCar> query = driverToCarDao.createQuery().filter("_id", driverToCar.getObjectId());
                UpdateOperations<DriverToCar> update = driverToCarDao.createUpdateOperations();
                if (driverToCar.getDelete() == 1) update.set("delete", 0); //若司机对车辆已经取消关联的，则重新关联上
                if (!driverToCar.getCarId().equals(existCar.getObjectId().toHexString()))
                    update.set("carId", existCar.getObjectId().toHexString());
                if (driverCarPho != null) update.set("driverCarPho", driverCarPho);
                if (driverToCar.getDelete() == 1 || !driverToCar.getCarId().equals(existCar.getObjectId().toHexString()) || driverCarPho != null)
                    driverToCarDao.update(query, update);
            }
        } else {
            Car car = new Car();
            if (StrUtil.isNotEmpty(engineNo)) car.setEngineNo(engineNo);
            if (StrUtil.isNotEmpty(owner)) car.setOwner(owner);
            if (StrUtil.isNotEmpty(vin)) car.setVin(vin);
            car = saveCar(car, carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack);

            driverToCar = new DriverToCar();
            driverToCar.setDriverId(did);
            driverToCar.setMobile(driverInfo.getMobile());
            driverToCar.setName(driverInfo.getName());
            driverToCar.setCarId(car.getObjectId().toHexString());
            driverToCar.setCarNum(carNum);
            if (driverCarPho != null) driverToCar.setDriverCarPho(driverCarPho);
            driverToCar.setDelete(0);
            driverToCarDao.save(driverToCar);
        }

//        Car existCar;
//        if (driverToCar == null) {
//            existCar = new Car();
//        } else {
//            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车牌：" + carNum + "被限制使用，请联系管理员");
//            existCar = carDao.getByPK(driverToCar.getCarId());
//            if (existCar.getVerify() == 4) return ServerResponse.createError("车辆：" + carNum + "被停用，关联失败");
//        }
//        if (driverToCar == null || existCar.getVerify() != 1) {//审核通过的车辆，则不需要上传新的车辆信息
//            /*Map<String, String> photoMap = new HashMap<>();
//            if (drivingPho1 != null) photoMap.put("drivingPho1", drivingPho1);  //行驶证照片第一页
//            if (drivingPho2 != null) photoMap.put("drivingPho2", drivingPho2);  //行驶证照片第二页
//            if (drivingPho3 != null) photoMap.put("drivingPho3", drivingPho3);  //行驶证照片第三页
//            if (roadTCPho != null) photoMap.put("roadTCPho", roadTCPho);        //驾驶证照片
//            if (carIdentityPhoBef != null) photoMap.put("carIdentityPhoBef", carIdentityPhoBef);    //驾驶证照片id
//            if (carIdentityPhoBack != null) photoMap.put("carIdentityPhoBack", carIdentityPhoBack); //驾驶证照片id
//            if (driverCarPho != null) photoMap.put("driverCarPho", driverCarPho); //司机和车的合影照片id
//            Map<String, String> pathMap = photoFileService.uploadPhotosByDB(carNum, "t_car", photoMap);*/
//
//            existCar.setId(Utils.getUUID());
//            existCar.setVerify(0);
//            existCar.setCarNum(carNum);
//            CarInfo carInfo = carInfoService.get("id", carInfoId);
//            existCar.setCarInfo(carInfo);
//
//            /*if (StrUtil.isNotEmpty(pathMap.get("drivingPho1Path")))
//                existCar.setDrivingPho1(pathMap.get("drivingPho1Path"));
//            if (StrUtil.isNotEmpty(pathMap.get("drivingPho2Path")))
//                existCar.setDrivingPho2(pathMap.get("drivingPho2Path"));
//            if (StrUtil.isNotEmpty(pathMap.get("drivingPho3Path")))
//                existCar.setDrivingPho3(pathMap.get("drivingPho3Path"));
//            if (StrUtil.isNotEmpty(pathMap.get("roadTCPhoPath"))) existCar.setRoadTCPho(pathMap.get("roadTCPhoPath"));
//            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBefPath")))
//                existCar.setCarIdentityPhoBef(pathMap.get("carIdentityPhoBefPath"));
//            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBackPath")))
//                existCar.setCarIdentityPhoBack(pathMap.get("carIdentityPhoBackPath"));*/
//            if (StrUtil.isNotEmpty(drivingPho1)) existCar.setDrivingPho1(drivingPho1);
//            if (StrUtil.isNotEmpty(drivingPho2)) existCar.setDrivingPho2(drivingPho2);
//            if (StrUtil.isNotEmpty(drivingPho3)) existCar.setDrivingPho3(drivingPho3);
//            if (StrUtil.isNotEmpty(roadTCPho)) existCar.setRoadTCPho(roadTCPho);
//            if (StrUtil.isNotEmpty(carIdentityPhoBef)) existCar.setCarIdentityPhoBef(carIdentityPhoBef);
//            if (StrUtil.isNotEmpty(carIdentityPhoBack)) existCar.setCarIdentityPhoBack(carIdentityPhoBack);
//            if (StrUtil.isNotEmpty(existCar.getUpdateTime())) existCar.setUpdateTime(new Date().getTime());
//            existCar = carDao.save(existCar);
//        }
//        //3.司机 关联 车辆信息
//        if (driverToCar == null) driverToCar = new DriverToCar();
//        driverToCar.setDriverId(did);
//        driverToCar.setCarId(existCar.getObjectId().toHexString());
//        driverToCar.setCarNum(carNum);
//        driverToCar.setMobile(driverInfo.getMobile());
//        driverToCar.setName(driverInfo.getName());
//        /*String driverCarPhoStr = null;
//        if (driverCarPho != null)
//            driverCarPhoStr = photoFileService.uploadPhotoByDB(did + carNum, "t_driver_to_car", driverCarPho);
//        if (StrUtil.isNotEmpty(driverCarPhoStr)) driverToCar.setDriverCarPho(driverCarPhoStr);*/
//        if (driverCarPho != null) driverToCar.setDriverCarPho(driverCarPho);
//        if (StrUtil.isEmpty(driverToCar.getDelete()) || driverToCar.getDelete() != 2)
//            driverToCar.setDelete(0);
//        if (StrUtil.isNotEmpty(driverToCar.getUpdateTime())) driverToCar.setUpdateTime(new Date().getTime());
//        driverToCarDao.save(driverToCar);

        //4.司机第一次关联车辆作为默认车辆

        if (StrUtil.isEmpty(driverInfo.getCarNum())) {
            Query<DriverInfo> dQuery = driverInfoService.createQuery();
            dQuery.filter("_id", new ObjectId(did));
            UpdateOperations<DriverInfo> dUpdateOP = driverInfoService.createUpdateOperations();
            dUpdateOP.set("carNum", carNum);
            driverInfoService.update(dQuery, dUpdateOP);
        }

        return ServerResponse.createSuccess("车辆信息添加并关联成功");
    }

    @Override
    public ServerResponse<String> saveDvrCar3(String carNum, String carInfoId, Double tareWeight, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack, String driverCarPho) {
        //todo:1.根据开关设置，判断车辆是否开启GPS
        SysSwitch sysSwitch = sysSwitchService.get("code", 2);//查询车辆开关设置
        if (StrUtil.isNotEmpty(sysSwitch.getNeedGPS()) && sysSwitch.getNeedGPS() == 1) {
            Map<String, Object> map = contractService.checkTruckExistV2(carNum);
            if (StrUtil.isNotEmpty(map.get("data")) && !map.get("data").equals("1001"))
                return ServerResponse.createError(map.get("msg").toString());
        }

        /*
         *小程序中实际是司机app注册登录的用户
         *小程序图片只能单张上传
         * */
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverInfo driverInfo = driverInfoService.getByPK(did);

        //识别行驶证车牌号，比对除汉字 后的号码是否一致
        String engineNo = null;
        String owner = null;
        String vin = null;
        if (StrUtil.isNotEmpty(drivingPho1)) {
            try {
                byte[] bytes = photoFileService.getBytes(drivingPho1);
                Map<String, String> aipOcrMap = AipOcrService.getVehicleLicense(bytes);
                String plateNo = aipOcrMap.get("carNum");
                if (StrUtil.isNotEmpty(plateNo) && !plateNo.substring(1).equals(carNum.substring(1)))
                    return ServerResponse.createError("行驶证车号不一致！");
                engineNo = aipOcrMap.get("engineNo");
                owner = aipOcrMap.get("owner");
                vin = aipOcrMap.get("VIN");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //校验是否存在司机车辆信息，否则新增保存
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", did);
        dctQuery.filter("carNum", carNum);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar != null) {
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车牌：" + carNum + "被限制使用，请联系管理员");

            Car existCar = carDao.getByPK(driverToCar.getCarId());
            if (existCar == null) {
                existCar = new Car();
                if (StrUtil.isNotEmpty(engineNo)) existCar.setEngineNo(engineNo);
                if (StrUtil.isNotEmpty(owner)) existCar.setOwner(owner);
                if (StrUtil.isNotEmpty(vin)) existCar.setVin(vin);
                if (StrUtil.isNotEmpty(tareWeight)) existCar.setTareWeight(tareWeight);
                existCar = saveCar(existCar, carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack);
            } else if (existCar.getVerify() == 4) {
                return ServerResponse.createError("车辆：" + existCar.getCarNum() + "被停用，关联失败");
            } else if (existCar.getVerify() != 1 || !carNum.equals(existCar.getCarNum())) {    //车辆信息已经存在，且未通过审核，则修改车牌号信息
                Query<Car> carQuery = carDao.createQuery().filter("_id", existCar.getObjectId());
                UpdateOperations<Car> upCarProAndNum = carDao.createUpdateOperations().set("carNum", carNum);
                if (StrUtil.isNotEmpty(drivingPho1)) upCarProAndNum.set("drivingPho1", drivingPho1);
                if (StrUtil.isNotEmpty(drivingPho2)) upCarProAndNum.set("drivingPho2", drivingPho2);
                if (StrUtil.isNotEmpty(drivingPho3)) upCarProAndNum.set("drivingPho3", drivingPho1);
                if (StrUtil.isNotEmpty(roadTCPho)) upCarProAndNum.set("roadTCPho", roadTCPho);
                if (StrUtil.isNotEmpty(carIdentityPhoBef)) upCarProAndNum.set("carIdentityPhoBef", carIdentityPhoBef);
                if (StrUtil.isNotEmpty(carIdentityPhoBack))
                    upCarProAndNum.set("carIdentityPhoBack", carIdentityPhoBack);
                if (StrUtil.isNotEmpty(engineNo)) upCarProAndNum.set("engineNo", engineNo);
                if (StrUtil.isNotEmpty(owner)) upCarProAndNum.set("owner", owner);
                if (StrUtil.isNotEmpty(vin)) upCarProAndNum.set("vin", vin);
                if (StrUtil.isNotEmpty(tareWeight)) upCarProAndNum.set("tareWeight", tareWeight);
                if (existCar.getVerify() == 2) upCarProAndNum.set("verify", 0); //若车辆审核未通过，则在司机重新上传车辆信息后状态改为未审核。
                carDao.update(carQuery, upCarProAndNum);
            }

            {
                Query<DriverToCar> query = driverToCarDao.createQuery().filter("_id", driverToCar.getObjectId());
                UpdateOperations<DriverToCar> update = driverToCarDao.createUpdateOperations();
                if (driverToCar.getDelete() == 1) update.set("delete", 0); //若司机对车辆已经取消关联的，则重新关联上
                if (!driverToCar.getCarId().equals(existCar.getObjectId().toHexString()))
                    update.set("carId", existCar.getObjectId().toHexString());
                if (driverCarPho != null) update.set("driverCarPho", driverCarPho);
                if (carNum.equals(driverToCar.getCarNum())) update.set("carNum", carNum);
                if (driverToCar.getDelete() == 1 || !driverToCar.getCarId().equals(existCar.getObjectId().toHexString()) || driverCarPho != null)
                    driverToCarDao.update(query, update);
            }
        } else {
            Car car = new Car();
            if (StrUtil.isNotEmpty(engineNo)) car.setEngineNo(engineNo);
            if (StrUtil.isNotEmpty(owner)) car.setOwner(owner);
            if (StrUtil.isNotEmpty(vin)) car.setVin(vin);
            if (StrUtil.isNotEmpty(tareWeight)) car.setTareWeight(tareWeight);
            car = saveCar(car, carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack);

            driverToCar = new DriverToCar();
            driverToCar.setDriverId(did);
            driverToCar.setMobile(driverInfo.getMobile());
            driverToCar.setName(driverInfo.getName());
            driverToCar.setCarId(car.getObjectId().toHexString());
            driverToCar.setCarNum(carNum);
            if (driverCarPho != null) driverToCar.setDriverCarPho(driverCarPho);
            driverToCar.setDelete(0);
            driverToCarDao.save(driverToCar);
        }

//        Car existCar;
//        if (driverToCar == null) {
//            existCar = new Car();
//        } else {
//            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车牌：" + carNum + "被限制使用，请联系管理员");
//            existCar = carDao.getByPK(driverToCar.getCarId());
//            if (existCar.getVerify() == 4) return ServerResponse.createError("车辆：" + carNum + "被停用，关联失败");
//        }
//        if (driverToCar == null || existCar.getVerify() != 1) {//审核通过的车辆，则不需要上传新的车辆信息
//            /*Map<String, String> photoMap = new HashMap<>();
//            if (drivingPho1 != null) photoMap.put("drivingPho1", drivingPho1);  //行驶证照片第一页
//            if (drivingPho2 != null) photoMap.put("drivingPho2", drivingPho2);  //行驶证照片第二页
//            if (drivingPho3 != null) photoMap.put("drivingPho3", drivingPho3);  //行驶证照片第三页
//            if (roadTCPho != null) photoMap.put("roadTCPho", roadTCPho);        //驾驶证照片
//            if (carIdentityPhoBef != null) photoMap.put("carIdentityPhoBef", carIdentityPhoBef);    //驾驶证照片id
//            if (carIdentityPhoBack != null) photoMap.put("carIdentityPhoBack", carIdentityPhoBack); //驾驶证照片id
//            if (driverCarPho != null) photoMap.put("driverCarPho", driverCarPho); //司机和车的合影照片id
//            Map<String, String> pathMap = photoFileService.uploadPhotosByDB(carNum, "t_car", photoMap);*/
//
//            existCar.setId(Utils.getUUID());
//            existCar.setVerify(0);
//            existCar.setCarNum(carNum);
//            CarInfo carInfo = carInfoService.get("id", carInfoId);
//            existCar.setCarInfo(carInfo);
//
//            /*if (StrUtil.isNotEmpty(pathMap.get("drivingPho1Path")))
//                existCar.setDrivingPho1(pathMap.get("drivingPho1Path"));
//            if (StrUtil.isNotEmpty(pathMap.get("drivingPho2Path")))
//                existCar.setDrivingPho2(pathMap.get("drivingPho2Path"));
//            if (StrUtil.isNotEmpty(pathMap.get("drivingPho3Path")))
//                existCar.setDrivingPho3(pathMap.get("drivingPho3Path"));
//            if (StrUtil.isNotEmpty(pathMap.get("roadTCPhoPath"))) existCar.setRoadTCPho(pathMap.get("roadTCPhoPath"));
//            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBefPath")))
//                existCar.setCarIdentityPhoBef(pathMap.get("carIdentityPhoBefPath"));
//            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBackPath")))
//                existCar.setCarIdentityPhoBack(pathMap.get("carIdentityPhoBackPath"));*/
//            if (StrUtil.isNotEmpty(drivingPho1)) existCar.setDrivingPho1(drivingPho1);
//            if (StrUtil.isNotEmpty(drivingPho2)) existCar.setDrivingPho2(drivingPho2);
//            if (StrUtil.isNotEmpty(drivingPho3)) existCar.setDrivingPho3(drivingPho3);
//            if (StrUtil.isNotEmpty(roadTCPho)) existCar.setRoadTCPho(roadTCPho);
//            if (StrUtil.isNotEmpty(carIdentityPhoBef)) existCar.setCarIdentityPhoBef(carIdentityPhoBef);
//            if (StrUtil.isNotEmpty(carIdentityPhoBack)) existCar.setCarIdentityPhoBack(carIdentityPhoBack);
//            if (StrUtil.isNotEmpty(existCar.getUpdateTime())) existCar.setUpdateTime(new Date().getTime());
//            existCar = carDao.save(existCar);
//        }
//        //3.司机 关联 车辆信息
//        if (driverToCar == null) driverToCar = new DriverToCar();
//        driverToCar.setDriverId(did);
//        driverToCar.setCarId(existCar.getObjectId().toHexString());
//        driverToCar.setCarNum(carNum);
//        driverToCar.setMobile(driverInfo.getMobile());
//        driverToCar.setName(driverInfo.getName());
//        /*String driverCarPhoStr = null;
//        if (driverCarPho != null)
//            driverCarPhoStr = photoFileService.uploadPhotoByDB(did + carNum, "t_driver_to_car", driverCarPho);
//        if (StrUtil.isNotEmpty(driverCarPhoStr)) driverToCar.setDriverCarPho(driverCarPhoStr);*/
//        if (driverCarPho != null) driverToCar.setDriverCarPho(driverCarPho);
//        if (StrUtil.isEmpty(driverToCar.getDelete()) || driverToCar.getDelete() != 2)
//            driverToCar.setDelete(0);
//        if (StrUtil.isNotEmpty(driverToCar.getUpdateTime())) driverToCar.setUpdateTime(new Date().getTime());
//        driverToCarDao.save(driverToCar);

        //4.司机第一次关联车辆作为默认车辆

        if (StrUtil.isEmpty(driverInfo.getCarNum())) {
            Query<DriverInfo> dQuery = driverInfoService.createQuery();
            dQuery.filter("_id", new ObjectId(did));
            UpdateOperations<DriverInfo> dUpdateOP = driverInfoService.createUpdateOperations();
            dUpdateOP.set("carNum", carNum);
            driverInfoService.update(dQuery, dUpdateOP);
        }

        return ServerResponse.createSuccess("车辆信息添加并关联成功");
    }

    @Override
    public WechatDriverInfo findUserByName(String name) {
        return this.get("openid", name);
    }

    @Override
    public List<String> findPermissions(String name) {
        //返回当前用户权限列表
        List<String> permissions = new ArrayList<>();
        permissions.add("sys:menu:add");
        permissions.add("sys:menu:edit");
        permissions.add("sys:menu:delete");
        return permissions;
    }
}
