package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.dao.ICustomerUserDao;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.web.app.pojo.CustomerAccountData;
import com.erdos.coal.park.web.app.service.IWebCustomerService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("webCustomerService")
public class WebCustomerServiceImpl extends BaseMongoServiceImpl<CustomerUser, ICustomerUserDao> implements IWebCustomerService {

    @Resource
    private HttpServletRequest request;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IUnitInfoService unitInfoService;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        Query<CustomerUser> query = this.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("name"))) query.filter("name", request.getParameter("name"));
        if (!StrUtil.isEmpty(request.getParameter("mobile"))) query.filter("mobile", request.getParameter("mobile"));
        if (!StrUtil.isEmpty(request.getParameter("identity")))
            query.filter("identity", request.getParameter("identity"));

        String unitName = request.getParameter("company");
        if (StrUtil.isNotEmpty(unitName)) {
            List<ObjectId> cids = new ArrayList<>();
            Query<SysUnit> sysUnitQuery = sysUnitService.createQuery();
            sysUnitQuery.criteria("name").equal(unitName);

            List<SysUnit> units = sysUnitService.list(sysUnitQuery);
            for (SysUnit unit : units) {
                UnitInfo unitInfo = unitInfoService.get("unitCode", unit.getCode());
                if (unitInfo == null) continue;
                cids.add(new ObjectId(unitInfo.getCid()));
            }
            query.criteria("_id").in(cids);
        }

        /*if (!StrUtil.isEmpty(request.getParameter("company"))) {
            query.and(
                    query.criteria("company").contains(request.getParameter("company"))
            );
        }*/

        EGridResult eGridResult = this.findPage(page, rows, query);

        return eGridResult;
    }

    private boolean checkCUser(CustomerUser user) {
        if (user == null) return false;

        if (user.getName() == null) return false;
        if (user.getMobile() == null) return false;
        if (user.getObjectId() == null) return false;
        if (user.getUpdateTime() == null) return false;

        return true;
    }

    @Override
    public ServerResponse<String> editUserState(CustomerUser cUser) {

        if (checkCUser(cUser)) {
            Query<CustomerUser> query = this.createQuery();
            query.filter("objectId", cUser.getObjectId());
            query.filter("updateTime", cUser.getUpdateTime());
            UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
            updateOperations.set("state", cUser.getState());
            updateOperations.set("updateTime", new Date().getTime());

            this.update(query, updateOperations);

            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public EGridResult loadAccountGrid(Integer page, Integer rows) {

        Query<CustomerUser> query = this.createQuery();

        if (!StrUtil.isEmpty(request.getParameter("mobile")))
            query.filter("mobile", request.getParameter("mobile"));


        EGridResult<CustomerUser> eGrid = this.findPage(page, rows, query);
        List<CustomerUser> list = eGrid.getRows();
        List<CustomerAccountData> resList = new ArrayList<>();
        for (CustomerUser user : list) {
            CustomerAccountData cad = new CustomerAccountData();
            cad.setId(user.getObjectId().toString());
            cad.setName(user.getName());
            cad.setMobile(user.getMobile());
            BigDecimal availableFee = customerAccountService.getAvailableFee(user.getObjectId().toString()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);//转换为元
            cad.setTotalFee(availableFee);

            resList.add(cad);
        }
        EGridResult eGridResult = new EGridResult();
        eGridResult.setRows(resList);
        eGridResult.setTotal(eGrid.getTotal());

        return eGridResult;
    }

    @Override
    public EGridResult accEdit(Integer page, Integer rows) {
        String id = request.getParameter("cid");
        Query<CustomerAccount> query = customerAccountService.createQuery().filter("cid", id);
        query.order(Sort.descending("createTime"));
//        List<CustomerAccount> accountList = customerAccountService.list(query);
        EGridResult<CustomerAccount> eGridResult = customerAccountService.findPage(page, rows, query);
        List<CustomerAccount> accountList = eGridResult.getRows();
//        List<CustomerAccount> accounts = new ArrayList<>();
        for (CustomerAccount account : accountList) {

            account.setTotalFee(account.getTotalFee().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));

//            accounts.add(account);
        }
        eGridResult.setRows(accountList);
        return eGridResult;

        /*int size = accounts.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(accounts.size(), accounts.subList(fromIndex, toIndex));*/
    }

    @Override
    public BigDecimal getAvailableFee() {
        String cid = request.getParameter("cid");
        return customerAccountService.getAvailableFee(cid);
    }

    @Override
    public ServerResponse<String> toThirdParty(CustomerUser cUser) {

        Query<CustomerUser> query = this.createQuery();
        query.filter("objectId", cUser.getObjectId());
        query.filter("updateTime", cUser.getUpdateTime());

        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        updateOperations.set("thirdPartyUser", !cUser.isThirdPartyUser());
        updateOperations.set("updateTime", new Date().getTime());

        this.update(query, updateOperations);

        return ServerResponse.createSuccess();
    }

    @Override
    public ServerResponse<List<CustomerUser>> searchByMobile() {
        String mobile = request.getParameter("mobile");
        Query<CustomerUser> query = this.createQuery();
        if (StrUtil.isNotEmpty(mobile)) query.field("mobile").contains(mobile);
        query.criteria("thirdPartyUser").equal(true);

        List<CustomerUser> list = this.list(query);

        CustomerUser nullUser = new CustomerUser();
        nullUser.setMobile("置空");
        nullUser.setPhoneId("置空");
        list.add(nullUser);
        return ServerResponse.createSuccess(list);
    }
}
