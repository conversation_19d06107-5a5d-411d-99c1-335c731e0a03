package com.erdos.coal.park.api.driver.pojo;

import java.io.Serializable;

//电子煤票
public class CoalTicket implements Serializable {
    /*private String year;            //年
    private String month;           //月
    private String day;             //日
    private String ticketNo;        //煤票编号
    private String companyName;     //企业名称
    private String carNum;          //车牌号
    private String variety;         //煤炭种类
    private String netWeight;       //净重
    private String productName;     //煤炭品种
    private String price;           //单价（元/吨）
    private String taxTotalPrice;   //含税总价（元）
    private String transportPlace;  //运往地点*/

    private String ticketNo;//ticketNo");       //煤票编号
    private String ticketType;//ticketType");   //煤票类型
    private String outSubName;//outSubName");   //煤票发货单位
    private String inSubName;//inSubName");     //煤票收货单位
    private String area;//area");               //煤票运往地区
    private String place;//place");             //煤票运往地点
    private String oPlace;//oPlace");           //煤票运往关联地点
    private String coalType;//coalType");       //煤票煤炭种类
    private String productName;//productName"); //煤票煤炭品种
    private String variety;//variety");         //煤票内部品种
    private String carNum;//carNum");           //煤票车号
    private String grossWeight;//grossWeight"); //煤票毛重
    private String tareWeight;//tareWeight");   //煤票皮重
    private String netWeight;//netWeight");     //煤票净重
    private String price;//price");             //煤票单价
    private String heatValue;//heatValue");     //煤票热值
    private String purpose;//purpose");         //煤票用途
    private String cleanWarehouse;//cleanWarehouse");//煤票清库
    private String supplyGua;//supplyGua");     //煤票保供
    private String ticketSign;//ticketSign");   //煤票签名
    private String barCode;//barCode");         //煤票条形码图（暂不处理）
    private String dimBarCode;//dimBarCode");   //煤票二维码图（暂不处理）
    private String billingTime;//billingTime"); //煤票开票时间
    private String billingClerk;//billingClerk");//煤票开票员

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getTicketType() {
        return ticketType;
    }

    public void setTicketType(String ticketType) {
        this.ticketType = ticketType;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getoPlace() {
        return oPlace;
    }

    public void setoPlace(String oPlace) {
        this.oPlace = oPlace;
    }

    public String getCoalType() {
        return coalType;
    }

    public void setCoalType(String coalType) {
        this.coalType = coalType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getTareWeight() {
        return tareWeight;
    }

    public void setTareWeight(String tareWeight) {
        this.tareWeight = tareWeight;
    }

    public String getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(String netWeight) {
        this.netWeight = netWeight;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getHeatValue() {
        return heatValue;
    }

    public void setHeatValue(String heatValue) {
        this.heatValue = heatValue;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getCleanWarehouse() {
        return cleanWarehouse;
    }

    public void setCleanWarehouse(String cleanWarehouse) {
        this.cleanWarehouse = cleanWarehouse;
    }

    public String getSupplyGua() {
        return supplyGua;
    }

    public void setSupplyGua(String supplyGua) {
        this.supplyGua = supplyGua;
    }

    public String getTicketSign() {
        return ticketSign;
    }

    public void setTicketSign(String ticketSign) {
        this.ticketSign = ticketSign;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getDimBarCode() {
        return dimBarCode;
    }

    public void setDimBarCode(String dimBarCode) {
        this.dimBarCode = dimBarCode;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }

    public String getBillingClerk() {
        return billingClerk;
    }

    public void setBillingClerk(String billingClerk) {
        this.billingClerk = billingClerk;
    }

    /*
    public CoalTicket() {
    }

    public CoalTicket(String year, String month, String day, String ticketNo, String companyName, String carNum, String variety, String netWeight, String productName, String price, String taxTotalPrice, String transportPlace) {
        this.year = year;
        this.month = month;
        this.day = day;
        this.ticketNo = ticketNo;
        this.companyName = companyName;
        this.carNum = carNum;
        this.variety = variety;
        this.netWeight = netWeight;
        this.productName = productName;
        this.price = price;
        this.taxTotalPrice = taxTotalPrice;
        this.transportPlace = transportPlace;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(String netWeight) {
        this.netWeight = netWeight;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getTaxTotalPrice() {
        return taxTotalPrice;
    }

    public void setTaxTotalPrice(String taxTotalPrice) {
        this.taxTotalPrice = taxTotalPrice;
    }

    public String getTransportPlace() {
        return transportPlace;
    }

    public void setTransportPlace(String transportPlace) {
        this.transportPlace = transportPlace;
    }*/
}
