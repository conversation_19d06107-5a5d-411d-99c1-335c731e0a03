package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.pojo.OrderInfoData;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.IOrderLogisticsDao;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import com.erdos.coal.park.api.driver.service.IOrderLogisticsService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.geo.GeoJson;
import dev.morphia.geo.LineString;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.Document;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("orderLogisticsService")
public class OrderLogisticsServiceImpl extends BaseMongoServiceImpl<OrderLogistics, IOrderLogisticsDao> implements IOrderLogisticsService {
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderTakingService orderTakingService;

    @Override
    public ServerResponse<OrderLogistics> updateLog(String oid, String longitude, String latitude, Integer finishTag) {
        //TODO1:先根据订单号,运输中，查询物流信息是否存在
        Query<OrderLogistics> query = this.createQuery();
        query.filter("oid", oid);
        query.filter("finishTag", 1);
        OrderLogistics orderLogisticsNew = this.get(query);

        if (!StrUtil.isEmpty(orderLogisticsNew)) {
            //TODO2:存在，更新经度、纬度、更新时间
            UpdateOperations<OrderLogistics> updateOperations = this.createUpdateOperations();

            if (!StrUtil.isEmpty(longitude) && !StrUtil.isEmpty(latitude)) {
                if (!StrUtil.isNumericalValue(longitude) && (Double.valueOf(longitude) > 180 || Double.valueOf(longitude) < -180)) {
                    return ServerResponse.createError("经度输入不正确！");
                }
                if (!StrUtil.isNumericalValue(latitude) && (Double.valueOf(latitude) > 90 || Double.valueOf(latitude) < -90)) {
                    return ServerResponse.createError("纬度输入不正确！");
                }
                if (!longitude.equals(orderLogisticsNew.getLongitude()) || !latitude.equals(orderLogisticsNew.getLatitude())) {
                    updateOperations.set("longitude", longitude);
                    updateOperations.set("latitude", latitude);

                    List<Point> b;
                    Point newPoint = GeoJson.point(Double.valueOf(latitude), Double.valueOf(longitude));
                    try {
                        LineString a = (LineString) orderLogisticsNew.getGeometry();
                        b = a.getCoordinates();
                        b.add(newPoint);
                    } catch (Exception e) {
                        Point oldPoint = (Point) orderLogisticsNew.getGeometry();
                        b = new ArrayList<>();
                        b.add(oldPoint);
                        b.add(newPoint);
                    }
                    LineString lineString = GeoJson.lineString(b.toArray(new Point[0]));
                    updateOperations.set("geometry", lineString);
                }

                if (!StrUtil.isEmpty(finishTag)) updateOperations.set("finishTag", finishTag);

                //因电子磅单和电子煤票展示需求，要求司机未点击完成卸货按钮前，一直展示订单
                if (StrUtil.isNotEmpty(finishTag) && finishTag == 2) {
                    UpdateOperations<OrderTaking> otUp = orderTakingService.createUpdateOperations();
                    otUp.set("finishButton", 1);
                    orderTakingService.update(orderTakingService.createQuery().filter("oid", oid), otUp);
                }

                OrderLogistics orderLogisticsResult = this.findAndModify(this.createQuery().filter("_id", orderLogisticsNew.getObjectId()), updateOperations);
                return ServerResponse.createSuccess("保存成功", orderLogisticsResult);
            }
            return ServerResponse.createError("经纬度参数不能为空");
        } else {
            return ServerResponse.createError("该订单不存在或者已完成！");
        }

    }

    public ServerResponse<OrderLogistics> getByOid(String oid) {

        //根据订单号查询订单物流信息
        OrderLogistics orderLogistics = this.get("oid", oid);
        List lastCoo = orderLogistics.getGeometry().getCoordinates();
//        if (lastCoo.size() > 1) {
        if ("LineString".equals(orderLogistics.getGeometry().convert().getType().getTypeName())) {
            Point p = (Point) lastCoo.get(lastCoo.size() - 1);
            orderLogistics.setGeometry(p);
        }
        //返回查询结果
        return ServerResponse.createSuccess("查询成功", orderLogistics);
    }

    @Override
    //public ServerResponse<List<OrderInfoData>> logisticsOrderList() {
    public ServerResponse<EGridResult> logisticsOrderList(Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("cid", cid);
        query.filter("finishTag", 1);   //订单完成标识（0：下单成功；1：运输途中；2：订单完成）
        query.filter("driverIsAgree", 1);   //司机接受订单
        //List<OrderTaking> orderTakingList = orderTakingService.list(query);
        EGridResult<OrderTaking> eGridResult = orderTakingService.findPage(page, rows, query);
        List<OrderTaking> orderTakingList = eGridResult.getRows();
        Long total = eGridResult.getTotal();
        List<OrderInfoData> resultData = new ArrayList<>();
        for (OrderTaking ot : orderTakingList) {
            Order order = orderService.get("oid", ot.getOid());
            if (order == null || StrUtil.isEmpty(order.getBeginPoint())) continue;  //面议和手工下单没有 起点和终点
            OrderInfoData infoData = new OrderInfoData();   //包装 要返回的数据对象
            infoData.setCarNum(ot.getCarNum());
            infoData.setOid(ot.getOid());
            infoData.setUpdateTime(ot.getUpdateTime());
            infoData.setBeginPoint(order.getBeginPoint());
            infoData.setEndPoint(order.getEndPoint());
            infoData.setTradeName(order.getTradeName());
            infoData.setPrice(order.getPrice());
            infoData.setDistance(order.getDistance());
            infoData.setTolls(order.getTolls());
            infoData.setOutVariety(order.getOutVariety());
            infoData.setInVariety(order.getInVariety());
            infoData.setMold(order.getMold());

            resultData.add(infoData);
        }

        //return ServerResponse.createSuccess("查询成功", resultData);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    @Override
    public ServerResponse<EGridResult> logisticsOrderList2(Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        Query<Order> query = orderService.createQuery();
        query.criteria("cid").equal(cid);
        query.and(
                query.criteria("tranStatus").greaterThanOrEq(1),
                query.criteria("tranStatus").lessThan(5)
        );

        EGridResult<Order> eGridResult = orderService.findPage(page, rows, query);
        List<Order> orderList = eGridResult.getRows();
        Long total = eGridResult.getTotal();
        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orderList) {
            if (order == null || StrUtil.isEmpty(order.getBeginPoint())) continue;  //面议和手工下单没有 起点和终点
            OrderInfoData infoData = new OrderInfoData();   //包装 要返回的数据对象
            infoData.setUpdateTime(order.getUpdateTime());
            infoData.setTradeName(order.getTradeName());
            infoData.setBeginPoint(order.getBeginPoint());
            infoData.setEndPoint(order.getEndPoint());
            infoData.setOid(order.getOid());
            infoData.setCarNum(order.getCarNum());
            infoData.setMold(order.getMold());
            infoData.setPrice(order.getPrice());
            infoData.setDistance(order.getDistance());
            infoData.setTolls(order.getTolls());
            infoData.setOutVariety(order.getOutVariety());
            infoData.setInVariety(order.getInVariety());

            resultData.add(infoData);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    @Override
    public Document createOLDoc(String oid, Integer finishTag, String longitude, String latitude, Date time) {
        OrderLogistics ol = new OrderLogistics();
        ol.setOid(oid);
        ol.setFinishTag(finishTag);
        ol.setLongitude(longitude);      //经度
        ol.setLatitude(latitude);       //纬度
        ol.setUpdateTime(time.getTime());
        Document olDoc = Document.parse(JSONObject.toJSONString(ol));
        Point point = GeoJson.point(Double.valueOf(latitude), Double.valueOf(longitude));
        Document geometry = new Document();
        geometry.append("coordinates", point.getCoordinates());
        geometry.append("type", "Point");
        olDoc.append("geometry", geometry);
        olDoc.append("createTime", time);
        return olDoc;
    }
}
