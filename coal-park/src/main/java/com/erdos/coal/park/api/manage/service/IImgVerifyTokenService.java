package com.erdos.coal.park.api.manage.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.park.api.manage.entity.ImgVerifyToken;

public interface IImgVerifyTokenService extends IBaseMongoService<ImgVerifyToken> {

    // 生成拼图验证，保存xpos
    String saveXpos(int xpos);

    // 校验xpos
    boolean checkXpos(String id, int moveX);

    // 验证码验证成功后，生成临时令牌
    String addImgVerifyToken();

    // 验证临时令牌并删除
    boolean checkImaVerifyToken(String verifyToken);
}
