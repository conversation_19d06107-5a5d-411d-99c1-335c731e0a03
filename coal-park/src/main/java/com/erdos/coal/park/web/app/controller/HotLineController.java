package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.entity.HotLine;
import com.erdos.coal.park.web.app.service.IHotLineService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/hotline")
public class HotLineController extends BaseController {
    @Resource
    private IHotLineService hotLineService;

    // 二级单位 热线电话列表（增删改）
    @RequestMapping("/list")
    public ServerResponse<EGridResult> hotLineListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(hotLineService.loadHotLineGrid(page, rows));
    }

    @PostMapping("/del")
    public ServerResponse<String> deleteHotLineHandler(@RequestBody HotLine hotLine) throws GlobalException {
        return hotLineService.deleteHotLine(hotLine);
    }

    @PostMapping("/add")
    public ServerResponse<String> addHotLineHandler(@RequestBody HotLine hotLine) throws GlobalException {
        return hotLineService.addHotLine(hotLine);
    }

    @PostMapping("/edit")
    public ServerResponse<String> editHotLineHandler(@RequestBody HotLine hotLine) throws GlobalException {
        return hotLineService.editHotLine(hotLine);
    }
}
