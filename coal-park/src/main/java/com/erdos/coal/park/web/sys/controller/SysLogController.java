package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.service.ISysLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;

@RestController
@RequestMapping("/web/sys/log")
public class SysLogController extends BaseController {

    @Resource
    private ISysLogService sysLogService;

    @PostMapping("list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException, ParseException {
        return ServerResponse.createSuccess(sysLogService.loadGrid(page, rows));
    }
}
