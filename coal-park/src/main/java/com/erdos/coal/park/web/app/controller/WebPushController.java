package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.service.IWebPushService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/message/push")
public class WebPushController extends BaseController {

    @Resource
    private IWebPushService webPushService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webPushService.loadGrid(page, rows));
    }

    @PostMapping("/cus_msg_list")
    public ServerResponse<EGridResult> cusMessageListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webPushService.loadCusMessageGrid(page, rows));
    }

    @PostMapping("/dvr_msg_list")
    public ServerResponse<EGridResult> dvrMessageListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webPushService.loadDvrMessageGrid(page, rows));
    }
}

