package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

public class RefundData  implements Serializable {
    private String oid;     //订单号
    private String carNum;  //车牌号

    private String tradeName;                       //商品名称
    private String beginPoint;                      //起点
    private String endPoint;                        //终点

    private BigDecimal balanceFees = new BigDecimal(0);     //司机付款金额（分）
    private String driverPayerId;   //司机id

    private Integer isWeChat;    //0-给app司机下单的货运信息；1-给小程序司机下单的货运信息
    private String refundTime;  //退款成功时间
    private String errMsg;      //退款失败原因|退款状态
    private String refundRecv;  //退款入账账户

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public BigDecimal getBalanceFees() {
        return balanceFees;
    }

    public void setBalanceFees(BigDecimal balanceFees) {
        this.balanceFees = balanceFees;
    }

    public String getDriverPayerId() {
        return driverPayerId;
    }

    public void setDriverPayerId(String driverPayerId) {
        this.driverPayerId = driverPayerId;
    }

    public Integer getIsWeChat() {
        return isWeChat;
    }

    public void setIsWeChat(Integer isWeChat) {
        this.isWeChat = isWeChat;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getRefundRecv() {
        return refundRecv;
    }

    public void setRefundRecv(String refundRecv) {
        this.refundRecv = refundRecv;
    }
}
