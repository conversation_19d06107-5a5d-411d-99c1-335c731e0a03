package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.IAppraiseDao;
import com.erdos.coal.park.api.customer.entity.Appraise;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IAppraiseService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

@Service("appraiseService")
public class AppraiseServiceImpl extends BaseMongoServiceImpl<Appraise, IAppraiseDao> implements IAppraiseService {
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private ICustomerUserService customerUserService;

    //评价订单查询
    @Override
    //public ServerResponse<List<Appraise>> appraiseList(Integer type) {
    public ServerResponse<EGridResult> appraiseList(Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        List<Appraise> appraiseList = new ArrayList<>();
        if (type == 0) {//未评价
            //List<OrderTaking> orderTakings = orderTakingService.searchByUpdateTime(cid, 2, null, null, null);//客商已完成的订单
            EGridResult<OrderTaking> eGridResult = orderTakingService.searchByUpdateTime(cid, 2, null, null, null, page, rows);
            List<OrderTaking> orderTakings = eGridResult.getRows();//客商已完成的订单
            Long total = eGridResult.getTotal();
            for (OrderTaking ot : orderTakings) {
                Appraise appraise = this.get("oid", ot.getOid());
                if (ObjectUtil.isNull(appraise)) {
                    Order order = orderService.get("oid", ot.getOid());
//                    DriverInfo dInfo = driverInfoService.get("carNum", ot.getCarNum());
                    DriverInfo dInfo = driverInfoService.getByPK(ot.getDid());
                    appraise = createAppraise(order, ot, dInfo, 0);
                    appraise.setDriverPro(fileInfoDao.readFile(dInfo.getDriverCarPho(), "t_driver_info"));

                    appraiseList.add(appraise);
                } else if (appraise.getType() == 0) {
                    appraiseList.add(appraise);
                }
            }
            return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, appraiseList));
        } else {
            Query<Appraise> query = this.createQuery();
            query.filter("cid", cid);
            query.filter("type", type);
            query.order(Sort.descending("createTime"));
            //appraiseList = this.list(query);
            return ServerResponse.createSuccess("查询成功", findPage(page, rows, query));
        }

        //return ServerResponse.createSuccess("查询成功", appraiseList);
    }

    @Override
    public ServerResponse<EGridResult> appraiseList2(Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        List<Appraise> appraiseList = new ArrayList<>();
        if (type == 0) {//未评价
            Query<Order> query = orderService.createQuery();
            query.criteria("cid").equal(cid);
            query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(new Date().getTime(), "00:00:00", -10));  //现支持查询10天内的订单。
            query.criteria("tranStatus").equal(5);
            query.order(Sort.descending("createTime"));
            EGridResult<Order> eGridResult = orderService.findPage(page, rows, query);
            List<Order> orders = eGridResult.getRows();//客商已完成的订单
            Long total = eGridResult.getTotal();
            for (Order o : orders) {
                Appraise appraise = this.get("oid", o.getOid());
                if (ObjectUtil.isNull(appraise)) {
                    DriverInfo dInfo = driverInfoService.getByPK(o.getDid());
                    appraise = createAppraise(o, dInfo, 0);
                    appraise.setDriverPro(fileInfoDao.readFile(dInfo.getDriverCarPho(), "t_driver_info"));

                    appraiseList.add(appraise);
                } else if (appraise.getType() == 0) {
                    appraiseList.add(appraise);
                }
            }
            return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, appraiseList));
        } else {
            Query<Appraise> query = this.createQuery();
            query.filter("cid", cid);
            query.filter("type", type);
            query.order(Sort.descending("createTime"));
            return ServerResponse.createSuccess("查询成功", findPage(page, rows, query));
        }
    }

    //提交订单评价
    @Override
    public ServerResponse<String> putAppraise(String oid, Integer starsNum, boolean complain, String reasons) {
        String cid = ShiroUtils.getUserId();
        Pattern pattern = Pattern.compile("[0-5]*");
        if (!pattern.matcher(String.valueOf(starsNum)).matches())
            return ServerResponse.createError("星级参数有误");

        Appraise appraise = this.get("oid", oid);
        if (appraise != null && appraise.getType() > 0)
            return ServerResponse.createError("订单已经评价");

        if (appraise == null) {
            OrderTaking ot = orderTakingService.get("oid", oid);
            Order order = orderService.get("oid", oid);
            DriverInfo dInfo = driverInfoService.get("carNum", ot.getCarNum());
            appraise = createAppraise(order, ot, dInfo, 1);
            appraise.setCid(cid);
            appraise.setCustomerUser(customerUserService.getByPK(cid));
            appraise.setDriverPro(fileInfoDao.readFile(dInfo.getDriverCarPho(), "t_driver_info"));
            appraise.setComplain(false);
            appraise.setStarsNum(starsNum);
            if (complain) {
                appraise.setType(2);
                appraise.setComplain(true);
                appraise.setReasons(reasons);
            }
            this.save(appraise);
        } else {
            UpdateOperations<Appraise> updateOperations = this.createUpdateOperations();
            if (complain) { //有投诉，修改评价记录为投诉
                updateOperations.set("type", 2);
                updateOperations.set("complain", true);
                updateOperations.set("reasons", reasons);
            } else {        //无投诉，修改评价记录星级
                updateOperations.set("type", 1);
                updateOperations.set("complain", false);
                updateOperations.set("starsNum", starsNum);
            }
            this.update(this.createQuery().filter("_id", appraise.getObjectId()), updateOperations);
        }
        return ServerResponse.createSuccess("完成订单" + oid + "的评价");
    }

    @Override
    public String addAppraise(Order order, DriverInfo dInfo, OrderTaking orderTaking, CustomerUser customerUser) {
        Appraise appraise = this.get("oid", order.getOid());
        if (appraise != null)
            return "订单待评价记录已存在";

        appraise = createAppraise(order, orderTaking, dInfo, 0);

        appraise.setCid(orderTaking.getCid());
        appraise.setDriverPro(fileInfoDao.readFile(dInfo.getDriverCarPho(), "t_driver_info"));
        appraise.setCustomerUser(customerUser);

        this.save(appraise);
        return "订单评价记录添加成功";
    }

    private Appraise createAppraise(Order order, OrderTaking orderTaking, DriverInfo driverInfo, Integer type) {
        Appraise appraise = new Appraise();
        appraise.setOid(order.getOid());
        appraise.setStartPoint(order.getBeginPoint());
        appraise.setEndPoint(order.getEndPoint());
        appraise.setLoadTime(orderTaking.getLoadTime() == null ? orderTaking.getCreateTime().getTime() : orderTaking.getLoadTime());
        appraise.setUnloadTime(orderTaking.getUpdateTime());
        appraise.setDriverName(driverInfo.getName());
        appraise.setCarNum(order.getCarNum());
        appraise.setVariety(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());
        appraise.setGid(order.getGid());
        appraise.setType(type);
        return appraise;
    }

    private Appraise createAppraise(Order order, DriverInfo driverInfo, Integer type) {
        Appraise appraise = new Appraise();
        appraise.setOid(order.getOid());
        appraise.setStartPoint(order.getBeginPoint());
        appraise.setEndPoint(order.getEndPoint());
        switch (order.getMold()) {
            case 0:
                if (StrUtil.isNotEmpty(order.getTime2())) appraise.setLoadTime(order.getTime2().getTime());
                if (StrUtil.isNotEmpty(order.getTime3())) appraise.setUnloadTime(order.getTime3().getTime());
                break;
            case 1:
                if (StrUtil.isNotEmpty(order.getTime1())) appraise.setLoadTime(order.getTime1().getTime());
                if (StrUtil.isNotEmpty(order.getTime5())) appraise.setUnloadTime(order.getTime5().getTime());
                break;
            case 2:
                if (StrUtil.isNotEmpty(order.getTime2())) appraise.setLoadTime(order.getTime2().getTime());
                if (StrUtil.isNotEmpty(order.getTime5())) appraise.setUnloadTime(order.getTime5().getTime());
                break;
        }
        appraise.setDriverName(driverInfo.getName());
        appraise.setCarNum(order.getCarNum());
        appraise.setVariety(StrUtil.isEmpty(order.getOutVariety()) ? order.getInVariety() : order.getOutVariety());
        appraise.setGid(order.getGid());
        appraise.setType(type);
        return appraise;
    }
}
