package com.erdos.coal.park.web.app.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.DateUtils;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.RefundPre;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IRefundPreService;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WxPrepaid;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidService;
import com.erdos.coal.park.api.driver.service.IWxResultService;
import com.erdos.coal.park.web.app.pojo.AccountData;
import com.erdos.coal.park.web.app.pojo.OrderDetailData;
import com.erdos.coal.park.web.app.pojo.OrderReport4;
import com.erdos.coal.park.web.app.pojo.ThirdPartyAccountData;
import com.erdos.coal.park.web.app.service.IWebAccountService;
import com.erdos.coal.park.web.sys.dao.ISysUnitAccountDao;
import com.erdos.coal.park.web.sys.entity.*;
import com.erdos.coal.park.web.sys.service.IPlatFormAccountService;
import com.erdos.coal.park.web.sys.service.IPreferentialRefundRecordService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.park.web.sys.service.IThirdPartyAccountService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.aggregation.Projection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.bson.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static dev.morphia.aggregation.Group.*;
import static dev.morphia.aggregation.Projection.projection;

@Service("webAccountService")
public class WebAccountServiceImpl extends BaseMongoServiceImpl<SysUnitAccount, ISysUnitAccountDao> implements IWebAccountService {
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IPlatFormAccountService platFormAccountService;
    @Resource
    private HttpServletRequest request;
    @Resource
    private IOrderService orderService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private IWxPrepaidService prepaidService;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private MongoClient client;
    @Resource
    private IRefundPreService refundPreService;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;
    @Resource
    private IPreferentialRefundRecordService preferentialRefundRecordService;

    @Override
    public ServerResponse<EGridResult> report(Integer page, Integer rows) {
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        String reportType = request.getParameter("type");

        List<AccountData> result = new ArrayList<>();
        if (reportType.equals("0")) {   // 平台入账
            result = ptReport(startDate, endDate);
        } else if (reportType.equals("1")) {    // 客商代扣
            String mobile = request.getParameter("mobile");
            Integer[] type = {6, 9};             //  6.平台代客商 代扣 司机的服务费（司机接单时需要支付给客商的钱）9.客商退回司机信息费
            result = customerReport(type, startDate, endDate, mobile);
        } else if (reportType.equals("2") || reportType.equals("3")) {  // 2:一级单位 3:二级单位代扣
            result = unitReport(reportType, startDate, endDate);
        }
        int size = result.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        EGridResult gridResult = new EGridResult(result.size(), result.subList(fromIndex, toIndex));
        return ServerResponse.createSuccess(gridResult);
    }

    @Override
    public ServerResponse<EGridResult> detail(Integer page, Integer rows) {
        String businessType = request.getParameter("businessType");
        String detailType = request.getParameter("detailType");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");

        List<AccountData> result = new ArrayList<>();
        if (businessType.equals("0")) {
            result = customerDetail(startDate, endDate, detailType);
        } else if (businessType.equals("1") || businessType.equals("2")) {
            result = unitDetail(startDate, endDate, businessType, detailType);
        }

        int size = result.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        EGridResult gridResult = new EGridResult(result.size(), result.subList(fromIndex, toIndex));
        return ServerResponse.createSuccess(gridResult);
    }

    // 单位代扣费用 报表
    private List<AccountData> unitReport(String reportType, String startDate, String endDate) {
        List<String> uidList = new ArrayList<>();
        Query<SysUnit> unitQuery = sysUnitService.createQuery();
        if (reportType.equals("2")) {   // 一级单位
            unitQuery.filter("pCode", null);
        } else if (reportType.equals("3")) {    // 二级单位
            unitQuery.filter("pCode != ", null);
        }
        List<SysUnit> unitList = sysUnitService.list(unitQuery);
        for (SysUnit sysUnit : unitList)
            uidList.add(sysUnit.getObjectId().toString());

        Query<SysUnitAccount> query = this.createQuery().filter("uid in ", uidList);
        if (StrUtil.isNotEmpty(startDate)) {
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        }
        if (StrUtil.isNotEmpty(endDate)) {
            query.filter("createTime <= ", IdUnit.weeHours(endDate, 1));
        }
        Integer[] type = {1, 5};    // 1：单位代扣（客商/司机付款）5：退款（客商或司机付单位费用）
        query.filter("type in ", type);
        query.order(Sort.descending("createTime"));
        List<SysUnitAccount> accountList = this.list(query);

        List<AccountData> result = unitAccGroup(accountList);

        List<AccountData> result1 = new ArrayList<>();
        for (AccountData data : result) {
            String uid = data.getId();
            SysUnit sysUnit = sysUnitService.getByPK(uid);
            if (ObjectUtil.isNotNull(sysUnit)) {
                data.setName(sysUnit.getName());
                result1.add(data);
            }
        }

        return result1;
    }

    // 平台入账所有订单相关的信息费 报表
    private List<AccountData> ptReport(String startDate, String endDate) {
        Query<PlatFormAccount> query = platFormAccountService.createQuery();
        if (StrUtil.isNotEmpty(startDate)) {
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        }
        if (StrUtil.isNotEmpty(endDate)) {
            query.filter("createTime <= ", IdUnit.weeHours(endDate, 1));
        }
        Integer[] type = {5, 6, 7, 8, 9, 10, 11, 12, 13};    // 跟订单相关的
        query.filter("type in ", type);
        query.order(Sort.descending("createTime"));
        List<PlatFormAccount> accountList = platFormAccountService.list(query);
        List<AccountData> result = ptAccGroup(accountList);

        return result;
    }

    // 客商账户 报表
    private List<AccountData> customerReport(Integer[] type, String startDate, String endDate, String mobile) {
        List<String> cidList = new ArrayList<>();
        Query<CustomerUser> userQuery = customerUserService.createQuery();
        if (StrUtil.isNotEmpty(mobile))
            userQuery.filter("mobile", mobile);

        List<CustomerUser> userList = customerUserService.list(userQuery);
        for (CustomerUser user : userList)
            cidList.add(user.getObjectId().toString());

        Query<CustomerAccount> query = customerAccountService.createQuery().filter("cid in ", cidList);
        if (StrUtil.isNotEmpty(startDate)) {
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        }
        if (StrUtil.isNotEmpty(endDate)) {
            query.filter("createTime <= ", IdUnit.weeHours(endDate, 1));
        }

        if (StrUtil.isNotEmpty(type)) query.filter("type in ", type);
        query.order(Sort.descending("createTime"));
        List<CustomerAccount> customerAccountList = customerAccountService.list(query);
        List<AccountData> result = customerAccGroup(customerAccountList);

        List<AccountData> result1 = new ArrayList<>();
        for (AccountData data : result) {
            String id = data.getId();
            CustomerUser user = customerUserService.getByPK(id);
            if (ObjectUtil.isNotNull(user)) {
                data.setMobile(user.getMobile());
                data.setName(user.getName());
                result1.add(data);
            }
        }

        return result1;
    }

    // 单位代扣、代付费用 明细
    private List<AccountData> unitDetail(String startDate, String endDate, String businessType, String detailType) {

        List<String> uidList = new ArrayList<>();
        Query<SysUnit> unitQuery = sysUnitService.createQuery();
        if (businessType.equals("1")) { // 一级单位
            unitQuery.filter("pCode", null);
        } else if (businessType.equals("2")) {  // 二级单位
            unitQuery.filter("pCode != ", null);
        }
        List<SysUnit> unitList = sysUnitService.list(unitQuery);
        for (SysUnit sysUnit : unitList)
            uidList.add(sysUnit.getObjectId().toString());

        Query<SysUnitAccount> query = this.createQuery().filter("uid in ", uidList);
        if (StrUtil.isNotEmpty(startDate)) {
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        }
        if (StrUtil.isNotEmpty(endDate)) {
            query.filter("createTime <= ", IdUnit.weeHours(endDate, 1));
        }
        if (StrUtil.isNotEmpty(detailType) && detailType.equals("0")) { // 单位代扣（钱增多）
            Integer[] type = {1, 5};    // 1：单位代收（客商/司机付款）5：退款（客商或司机付单位费用）
            query.filter("type in ", type);
        } else if (StrUtil.isNotEmpty(detailType) && detailType.equals("1")) {  // 单位代付（钱减少）
            Integer[] type = {0, 2, 3}; // 0：单位代付 2：退差价（代付费用>司机接单费用） 3：撤单退款
            query.filter("type in ", type);
        } else {
            Integer[] type = {1, 5, 0, 2, 3};
            query.filter("type in ", type);
        }
        query.order(Sort.descending("createTime"));
        List<SysUnitAccount> accountList = this.list(query);

        List<AccountData> result1 = new ArrayList<>();
        for (SysUnitAccount account : accountList) {
            String uid = account.getUid();
            SysUnit sysUnit = sysUnitService.getByPK(uid);

            if (ObjectUtil.isNotNull(sysUnit)) {
                AccountData data = new AccountData();
                data.setId(uid);
                data.setTotalFee(account.getTotalFee().abs().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)); // 转换为元

                DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String str = format.format(account.getCreateTime());
                data.setCreateTime(str);
                data.setOid(account.getOid());
                data.setName(sysUnit.getName());
                if (account.getType() == 0) {
                    data.setType(0);
                } else if (account.getType() == 2) {
                    data.setType(1);
                } else if (account.getType() == 3) {
                    data.setType(2);
                } else if (account.getType() == 1) {
                    data.setType(3);
                } else if (account.getType() == 5) {
                    data.setType(4);
                }
                result1.add(data);
            }
        }

        return result1;
    }

    // 客商代扣、代付费用 明细
    private List<AccountData> customerDetail(String startDate, String endDate, String detailType) {
        Query<CustomerAccount> query = customerAccountService.createQuery();
        if (StrUtil.isNotEmpty(startDate)) {
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        }
        if (StrUtil.isNotEmpty(endDate)) {
            query.filter("createTime <= ", IdUnit.weeHours(endDate, 1));
        }
        if (StrUtil.isNotEmpty(detailType) && detailType.equals("0")) { // 代扣（钱增多）
            Integer[] type = {6, 9};    // 6.平台代客商 代扣 司机的服务费（司机接单时需要支付给客商的钱） 9.客商退回司机信息费
            query.filter("type in ", type);
        } else if (StrUtil.isNotEmpty(detailType) && detailType.equals("1")) {  // 代付（钱减少）
            Integer[] type = {3, 4, 5};    // 3.客商账户代付货运信息费扣款 4.客商多代付的订单退款 5.客商已代付的订单退款
            query.filter("type in ", type);
        } else {
            Integer[] type = {3, 4, 5, 6, 9};
            query.filter("type in ", type);
        }
        query.order(Sort.descending("createTime"));
        List<CustomerAccount> customerAccountList = customerAccountService.list(query);

        List<AccountData> result = new ArrayList<>();
        for (CustomerAccount account : customerAccountList) {

            String cid = account.getCid();
            if (StrUtil.isEmpty(cid)) continue;
            CustomerUser user = customerUserService.getByPK(cid);
            if (ObjectUtil.isNotNull(user)) {
                AccountData data = new AccountData();
                data.setMobile(user.getMobile());
                data.setName(user.getName());
                data.setTotalFee(account.getTotalFee().abs().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)); // 转换为元

                DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String str = format.format(account.getCreateTime());
                data.setCreateTime(str);

                data.setOid(account.getOid());
                if (account.getType() == 3) {
                    data.setType(0);
                } else if (account.getType() == 4) {
                    data.setType(1);
                } else if (account.getType() == 5) {
                    data.setType(2);
                } else if (account.getType() == 6) {
                    data.setType(3);
                } else if (account.getType() == 9) {
                    data.setType(4);
                }

                result.add(data);
            }
        }

        return result;
    }

    // 单位按uid和createTime分组
    private List<AccountData> unitAccGroup(List<SysUnitAccount> accList) {
        List<AccountData> result = accList.stream()
                .collect(Collectors.groupingBy(SysUnitAccount::uidAndCreateTime, LinkedHashMap::new, Collectors.toList()))
                .entrySet()
                .stream()
                .map(e -> {
                    AccountData resultSet = new AccountData();
                    BigDecimal sum = e.getValue().stream()
                            .map(SysUnitAccount::getTotalFee)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    String[] temp = e.getKey().split("_");
                    String uid = temp[0];
                    String date = temp[1];
                    resultSet.setId(uid);
                    resultSet.setCreateTime(date);
                    resultSet.setTotalFee(sum.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
                    return resultSet;
                }).collect(Collectors.toList());

        return result;
    }

    // 客商按cid和createTime分组
    private List<AccountData> customerAccGroup(List<CustomerAccount> accList) {
        List<AccountData> result = accList.stream()
                .collect(Collectors.groupingBy(CustomerAccount::cidAndCreateTime, LinkedHashMap::new, Collectors.toList()))
                .entrySet()
                .stream()
                .map(e -> {
                    AccountData resultSet = new AccountData();
                    BigDecimal sum = e.getValue().stream()
                            .map(CustomerAccount::getTotalFee)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    String[] temp = e.getKey().split("_");
                    String cid = temp[0];
                    String date = temp[1];
                    resultSet.setId(cid);
                    resultSet.setTotalFee(sum.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
                    resultSet.setCreateTime(date);
                    return resultSet;
                }).collect(Collectors.toList());

        return result;
    }

    // 平台按createTime分组
    private List<AccountData> ptAccGroup(List<PlatFormAccount> accList) {
        List<AccountData> result = accList.stream()
                .collect(Collectors.groupingBy(PlatFormAccount::createDate, LinkedHashMap::new, Collectors.toList()))
                .entrySet()
                .stream()
                .map(e -> {
                    AccountData resultSet = new AccountData();
                    BigDecimal sum = e.getValue().stream()
                            .map(PlatFormAccount::getTotalFee)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    resultSet.setTotalFee(sum.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
                    resultSet.setCreateTime(e.getKey());
                    return resultSet;
                }).collect(Collectors.toList());

        return result;
    }

    @Override
    public ServerResponse<EGridResult> orderDetail(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        Date startDate;
        if (StrUtil.isNotEmpty(startTime)) {
            startDate = IdUnit.weeHours(startTime, 0);
        } else {
            startDate = IdUnit.weeHours1(null, "00:00:00", 0);
        }
        Date endDate;
        if (StrUtil.isNotEmpty(endTime)) {
            endDate = IdUnit.weeHours(endTime, 1);
        } else {
            endDate = IdUnit.weeHours1(null, "00:00:00", 1);
        }

        Query<Order> query = orderService.createQuery();
        query.criteria("time1").greaterThanOrEq(startDate.getTime());
        query.criteria("time1").lessThan(endDate.getTime());

        String cusMobile = request.getParameter("cusMobile");
        if (StrUtil.isNotEmpty(cusMobile)) {
            CustomerUser cUser = customerUserService.get("mobile", cusMobile);
            if (cUser == null) return ServerResponse.createError("手机号客商不存在");
            query.criteria("cid").equal(cUser.getObjectId().toHexString());
        }

        String dvrMobile = request.getParameter("dvrMobile");
        if (StrUtil.isNotEmpty(dvrMobile)) {
            DriverInfo driverInfo = driverInfoService.get("mobile", dvrMobile);
            query.criteria("did").equal(driverInfo.getObjectId().toHexString());
        }

        String unitName = request.getParameter("unitName");
        if (StrUtil.isNotEmpty(unitName)) {
            query.or(
                    query.criteria("outUnitName").contains(unitName),
                    query.criteria("inUnitName").contains(unitName)
            );
        }

        EGridResult<Order> result = orderService.findPage(page, rows, query);

        List<OrderDetailData> list = new ArrayList<>();
        for (Order o : result.getRows()) {
            OrderDetailData orderDetailData = new OrderDetailData();
            BeanUtils.copyProperties(o, orderDetailData);
            if (StrUtil.isNotEmpty(o.getOutFee0()) && o.getOutFee0() > 0)
                orderDetailData.setOutFee0(o.getOutFee0() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee0()) && o.getInFee0() > 0)
                orderDetailData.setInFee0(o.getInFee0() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee1()) && o.getOutFee1() > 0)
                orderDetailData.setOutFee1(o.getOutFee1() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee2()) && o.getOutFee2() > 0)
                orderDetailData.setOutFee2(o.getOutFee2() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee1()) && o.getInFee1() > 0)
                orderDetailData.setInFee1(o.getInFee1() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee2()) && o.getInFee2() > 0)
                orderDetailData.setInFee2(o.getInFee2() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee3()) && o.getOutFee3() > 0)
                orderDetailData.setOutFee3(o.getOutFee3() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee4()) && o.getOutFee4() > 0)
                orderDetailData.setOutFee4(o.getOutFee4() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee5()) && o.getOutFee5() > 0)
                orderDetailData.setOutFee5(o.getOutFee5() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee5()) && o.getInFee5() > 0)
                orderDetailData.setInFee5(o.getInFee5() / 100.0);

            list.add(orderDetailData);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult(result.getTotal(), list));
    }

    @Override
    public ServerResponse<EGridResult> report2(Integer page, Integer rows) {
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        String reportType = request.getParameter("type");
        String mobile = request.getParameter("mobile");

        EGridResult gridResult = null;

        if (StrUtil.isEmpty(reportType) || reportType.equals("0")) {                   //客商账户
            gridResult = customerReport2(startDate, endDate, mobile, page, rows);
        } else if (reportType.equals("1")) {            //司机账户
            gridResult = driverReport2(startDate, endDate, mobile, page, rows);
        } else if (reportType.equals("2") || reportType.equals("3") || reportType.equals("4")) {            //平台微信支付记录
            gridResult = wxResultReport2(page, rows, startDate, endDate, reportType);
        }

        return ServerResponse.createSuccess(gridResult);
    }

    //客商账户transactionId错漏记的补上
    private void transactionId() {
        Query<CustomerAccount> query = customerAccountService.createQuery();
        query.criteria("transactionId").equal(null);
        query.criteria("outTradeNo").notEqual(null);
        List<CustomerAccount> accountList = customerAccountService.list(query);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> customerAccountCollection = customerAccountService.getCollection();
        for (CustomerAccount account : accountList) {
            WxResult result = wxResultService.get("outTradeNo", account.getOutTradeNo());
            if (result == null) continue;
            try {
                clientSession.startTransaction();
                Map<String, Object> param = new HashMap<>();
                Map<String, Object> updMap = new HashMap<>();
                param.put("transactionId", result.getTransactionId());
                updMap.put("$set", param);
                customerAccountCollection.updateOne(clientSession, new Document("outTradeNo", account.getOutTradeNo()), Document.parse(JSONObject.toJSONString(updMap)));
                clientSession.commitTransaction();
            } catch (Exception e) {
                clientSession.abortTransaction();
            }
        }
    }

    // 客商账户 报表
    private EGridResult customerReport2(String startDate, String endDate, String mobile, Integer page, Integer rows) {
        transactionId();

        Query<CustomerAccount> query = customerAccountService.createQuery();

        if (StrUtil.isNotEmpty(mobile)) {
            List<String> cidList = new ArrayList<>();
            Query<CustomerUser> userQuery = customerUserService.createQuery();
            userQuery.filter("mobile", mobile);
            List<CustomerUser> userList = customerUserService.list(userQuery);
            for (CustomerUser user : userList)
                cidList.add(user.getObjectId().toString());

            query.criteria("cid").in(cidList);
        }
        if (StrUtil.isNotEmpty(startDate))
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        if (StrUtil.isNotEmpty(endDate))
            query.filter("createTime < ", IdUnit.weeHours(endDate, 1));

        query.order(Sort.descending("updateTime"));
        EGridResult result = customerAccountService.findPage(page, rows, query);
        List<CustomerAccount> customerAccountList = result.getRows();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //格式化规则
        List<AccountData> result1 = new ArrayList<>();
        for (CustomerAccount account : customerAccountList) {
            AccountData data = new AccountData();
            data.setId(account.getCid());
            CustomerUser user = customerUserService.getByPK(account.getCid());
            if (ObjectUtil.isNotNull(user)) {
                data.setMobile(user.getMobile());
                data.setName(user.getName());
            }
            data.setTotalFee(account.getTotalFee().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            data.setCreateTime(sdf.format(account.getCreateTime()));
            data.setOid(account.getOid());
            data.setCusType(account.getType());

            result1.add(data);
        }
        result.setRows(result1);

        return result;
    }

    // 司机账户 报表
    private EGridResult driverReport2(String startDate, String endDate, String mobile, Integer page, Integer rows) {
        Query<DriverAccount> query = driverAccountService.createQuery();

        if (StrUtil.isNotEmpty(mobile)) {
            List<String> didList = new ArrayList<>();

            Query<DriverInfo> userQuery = driverInfoService.createQuery();
            userQuery.filter("mobile", mobile);

            List<DriverInfo> userList = driverInfoService.list(userQuery);
            for (DriverInfo user : userList)
                didList.add(user.getObjectId().toString());

            query.criteria("did").in(didList);
        }
        if (StrUtil.isNotEmpty(startDate)) query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        if (StrUtil.isNotEmpty(endDate)) query.filter("createTime < ", IdUnit.weeHours(endDate, 1));

//        query.order(Sort.descending("updateTime"));
        EGridResult eGridResult = driverAccountService.findPage(page, rows, query);
        List<DriverAccount> dvrAccountList = eGridResult.getRows();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //格式化规则
        List<AccountData> result1 = new ArrayList<>();
        for (DriverAccount account : dvrAccountList) {
            AccountData data = new AccountData();
            data.setId(account.getDid());
            DriverInfo user = driverInfoService.getByPK(account.getDid());
            if (ObjectUtil.isNotNull(user)) {
                data.setMobile(user.getMobile());
                data.setName(user.getName());
            }
            data.setTotalFee(account.getTotalFee().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            data.setCreateTime(sdf.format(account.getCreateTime()));
            data.setOid(account.getOid());
            data.setDvrType(account.getType());
            result1.add(data);
        }
        eGridResult.setRows(result1);
        return eGridResult;
    }

    // 平台微信支付 报表
    private EGridResult wxResultReport2(Integer page, Integer rows, String startDate, String endDate, String reportType) {
        /*Query<WxPrepaid> prepaidQuery = prepaidService.createQuery();
        prepaidQuery.criteria("pay").equal(true);
        if (StrUtil.isNotEmpty(mobile)) {
            List<String> cidList = new ArrayList<>();
            Query<CustomerUser> cusUserQuery = customerUserService.createQuery();
            cusUserQuery.filter("mobile", mobile);
            List<CustomerUser> cusUserList = customerUserService.list(cusUserQuery);
            for (CustomerUser user : cusUserList)
                cidList.add(user.getObjectId().toString());

            List<String> didList = new ArrayList<>();
            Query<DriverInfo> dvrUserQuery = driverInfoService.createQuery();
            dvrUserQuery.filter("mobile", mobile);
            List<DriverInfo> dvrUserList = driverInfoService.list(dvrUserQuery);
            for (DriverInfo user : dvrUserList)
                cidList.add(user.getObjectId().toString());

            prepaidQuery.or(
                    prepaidQuery.criteria("cid").in(cidList),
                    prepaidQuery.criteria("did").in(didList)
            );
        }
        List<WxPrepaid> prepaids = prepaidService.list(prepaidQuery);
        List<String> transactionIds = new ArrayList<>();
        for (WxPrepaid prepaid : prepaids) {
            transactionIds.add(prepaid.getTransactionId());
        }*/

        Query<WxResult> query = wxResultService.createQuery();

        if (reportType.equals("3")) {                               //充值 等用户向平台支付
            query.criteria("bankType").notEqual(null);
            query.criteria("paymentNo").equal(null);
        } else if (reportType.equals("4")) {                        //提现
            query.criteria("bankType").equal(null);
        }

        if (StrUtil.isNotEmpty(startDate)) query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        if (StrUtil.isNotEmpty(endDate)) query.filter("createTime < ", IdUnit.weeHours(endDate, 1));
        query.order(Sort.descending("updateTime"));
        EGridResult result = wxResultService.findPage(page, rows, query);
        List<WxResult> wxResultList = result.getRows();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //格式化规则
        List<AccountData> result1 = new ArrayList<>();
        for (WxResult wxResult : wxResultList) {
            AccountData data = new AccountData();
            data.setTotalFee(new BigDecimal(wxResult.getTotalFee()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            data.setCreateTime(sdf.format(wxResult.getCreateTime()));
            data.setOid(wxResult.getOrderId());
            data.setTransactionId(wxResult.getTransactionId());
            data.setOutTradeNo(wxResult.getOutTradeNo());
            data.setOutRefundNo(wxResult.getOutRefundNo());
            data.setPaymentNo(wxResult.getPaymentNo());
            data.setOutOrderNo(wxResult.getOutOrderNo());
            data.setResultCode(wxResult.getResultCode());
            data.setErrCodeDes(wxResult.getErrCodeDes());

            WxPrepaid wxPrepaid = prepaidService.get("outTradeNo", wxResult.getOutTradeNo());
            if (wxPrepaid == null) {
                Order order = orderService.get("oid", wxResult.getOutOrderNo());

                if (order == null) {
                    ThirdPartyAccount thirdPartyAccount = thirdPartyAccountService.get("outTradeNo", wxResult.getOutTradeNo());
                    data.setWxResultType(9);
                    data.setName(thirdPartyAccount.getName());
                    data.setTotalFee(thirdPartyAccount.getTotalFee().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                    result1.add(data);
                    continue;
                }

                String outBillCode = order.getOutBillCode();
                String inBillCode = order.getInBillCode();
                if (StrUtil.isNotEmpty(outBillCode) && order.getOutFee5() > 0) {
                    wxPrepaid = prepaidService.get("outOrderNo", outBillCode);
                    AccountData dataOut = new AccountData();
                    dataOut.setName(wxPrepaid.getName());
                    dataOut.setWxResultType(8);
                    dataOut.setErrCodeDes(wxResult.getErrCodeDes());
                    dataOut.setResultCode(wxResult.getResultCode());
                    dataOut.setOutOrderNo(wxPrepaid.getOutOrderNo());
                    dataOut.setTransactionId(wxResult.getTransactionId());
                    dataOut.setOid(wxResult.getOutOrderNo());
                    dataOut.setCreateTime(sdf.format(wxPrepaid.getCreateTime()));
                    dataOut.setTotalFee(new BigDecimal(wxPrepaid.getAmount()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                    result1.add(dataOut);
                }
                if (StrUtil.isNotEmpty(inBillCode) && order.getInFee5() > 0) {
                    wxPrepaid = prepaidService.get("outOrderNo", inBillCode);
                    AccountData dataIn = new AccountData();
                    dataIn.setTotalFee(new BigDecimal(wxPrepaid.getAmount()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                    dataIn.setCreateTime(sdf.format(wxPrepaid.getCreateTime()));
                    dataIn.setOid(wxResult.getOutOrderNo());
                    dataIn.setTransactionId(wxResult.getTransactionId());
                    dataIn.setOutOrderNo(wxPrepaid.getOutOrderNo());
                    dataIn.setResultCode(wxResult.getResultCode());
                    dataIn.setErrCodeDes(wxResult.getErrCodeDes());
                    dataIn.setWxResultType(8);
                    dataIn.setName(wxPrepaid.getName());
                    result1.add(dataIn);
                }
                continue;
            }
            switch (wxPrepaid.getType()) {
                case 0:                     //充值
                    if (StrUtil.isNotEmpty(wxPrepaid.getCid())) {
                        if (StrUtil.isEmpty(wxPrepaid.getCdid())) {
                            data.setWxResultType(0);
                        } else {
                            data.setWxResultType(1);
                        }
                        CustomerUser user0 = customerUserService.getByPK(wxPrepaid.getCid());
                        data.setMobile(user0.getMobile());
                        data.setName(user0.getName());
                    } else if (StrUtil.isNotEmpty(wxPrepaid.getDid())) {
                        data.setWxResultType(2);
                        DriverInfo user0 = driverInfoService.getByPK(wxPrepaid.getDid());
                        data.setMobile(user0.getMobile());
                        data.setName(user0.getName());
                    }
                    break;
                case 1:                     //订单付款
                    data.setWxResultType(3);
                    DriverInfo user1 = driverInfoService.getByPK(wxPrepaid.getDvrId());
                    data.setMobile(user1.getMobile());
                    data.setName(user1.getName());
                    break;
                case 2:                     //退款
                    data.setWxResultType(4);
                    DriverInfo user2 = driverInfoService.getByPK(wxPrepaid.getDid());
                    data.setMobile(user2.getMobile());
                    data.setName(user2.getName());
                    break;
                case 3:                     //提现
                    if (data.getTotalFee().compareTo(new BigDecimal("0")) == 0)
                        data.setTotalFee(new BigDecimal(wxPrepaid.getTotalFee()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                    RefundPre refundPre = refundPreService.get("transferNo", wxPrepaid.getOutTradeNo());
                    if (refundPre != null && StrUtil.isNotEmpty(refundPre.getCustomerUser())) {
                        data.setWxResultType(5);
                        CustomerUser user3 = refundPre.getCustomerUser();
                        data.setMobile(user3.getMobile());
                        data.setName(user3.getName());
                        if (refundPre.getRemark().equals("三方账户余额提现到微信零钱")) data.setWxResultType(7);
                    } else if (refundPre != null && StrUtil.isNotEmpty(refundPre.getDriverInfo())) {
                        data.setWxResultType(6);
                        DriverInfo user3 = refundPre.getDriverInfo();
                        data.setMobile(user3.getMobile());
                        data.setName(user3.getName());
                    }
                    break;
                default:
                    break;
            }

            result1.add(data);
        }
        result.setRows(result1);

        return result;
    }

    @Override
    public ServerResponse<EGridResult> report3(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        Date startDate;
        if (StrUtil.isNotEmpty(startTime)) {
            startDate = IdUnit.weeHours(startTime, 0);
        } else {
            startDate = IdUnit.weeHours1(null, "00:00:00", 0);
        }
        Date endDate;
        if (StrUtil.isNotEmpty(endTime)) {
            endDate = IdUnit.weeHours(endTime, 1);
        } else {
            endDate = IdUnit.weeHours1(null, "00:00:00", 1);
        }

        Query<Order> query = orderService.createQuery();
        query.criteria("time1").greaterThanOrEq(startDate.getTime());
        query.criteria("time1").lessThan(endDate.getTime());

        String cusMobile = request.getParameter("cusMobile");
        if (StrUtil.isNotEmpty(cusMobile)) {
            CustomerUser cUser = customerUserService.get("mobile", cusMobile);
            if (cUser == null) return ServerResponse.createError("手机号客商不存在");
            query.criteria("cid").equal(cUser.getObjectId().toHexString());
        }

        String cusName = request.getParameter("cusName");
        if (StrUtil.isNotEmpty(cusName)) {
            CustomerUser cUser = customerUserService.get("name", cusName);
            query.criteria("cid").equal(cUser.getObjectId().toHexString());
        }

        String unitName = request.getParameter("unitName");
        if (StrUtil.isNotEmpty(unitName)) {
            query.or(
                    query.criteria("outUnitName").contains(unitName),
                    query.criteria("inUnitName").contains(unitName)
            );
        }

        String type = request.getParameter("type");
        if (StrUtil.isEmpty(type) || type.equals("0")) {
            query.or(
                    query.criteria("outFee0").greaterThan(0),
                    query.criteria("inFee0").greaterThan(0),
                    query.criteria("outFee1").greaterThan(0),
                    query.criteria("inFee1").greaterThan(0),
                    query.criteria("outFee2").greaterThan(0),
                    query.criteria("inFee2").greaterThan(0),
                    query.criteria("outFee3").greaterThan(0),
                    query.criteria("inFee3").greaterThan(0),
                    query.criteria("outFee4").greaterThan(0),
                    query.criteria("inFee4").greaterThan(0),
                    query.criteria("outFee5").greaterThan(0),
                    query.criteria("inFee5").greaterThan(0),
                    query.criteria("outFee6").greaterThan(0),
                    query.criteria("inFee6").greaterThan(0),
                    query.criteria("outFee7").greaterThan(0),
                    query.criteria("inFee7").greaterThan(0)
            );
        } else if (type.equals("1")) {
            query.or(
                    query.criteria("outFee1").greaterThan(0),
                    query.criteria("inFee1").greaterThan(0),
                    query.criteria("outFee2").greaterThan(0),
                    query.criteria("inFee2").greaterThan(0)
            );
        } else if (type.equals("2")) {
            query.or(
                    query.criteria("outFee5").greaterThan(0),
                    query.criteria("inFee5").greaterThan(0)
            );
        }

        EGridResult<Order> result = orderService.findPage(page, rows, query);

        List<OrderDetailData> list = new ArrayList<>();
        for (Order o : result.getRows()) {
            OrderDetailData orderDetailData = new OrderDetailData();
            BeanUtils.copyProperties(o, orderDetailData);
            if (StrUtil.isNotEmpty(o.getOutFee0()) && o.getOutFee0() > 0)
                orderDetailData.setOutFee0(o.getOutFee0() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee0()) && o.getInFee0() > 0)
                orderDetailData.setInFee0(o.getInFee0() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee1()) && o.getOutFee1() > 0)
                orderDetailData.setOutFee1(o.getOutFee1() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee2()) && o.getOutFee2() > 0)
                orderDetailData.setOutFee2(o.getOutFee2() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee1()) && o.getInFee1() > 0)
                orderDetailData.setInFee1(o.getInFee1() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee2()) && o.getInFee2() > 0)
                orderDetailData.setInFee2(o.getInFee2() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee3()) && o.getOutFee3() > 0)
                orderDetailData.setOutFee3(o.getOutFee3() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee4()) && o.getOutFee4() > 0)
                orderDetailData.setOutFee4(o.getOutFee4() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee5()) && o.getOutFee5() > 0)
                orderDetailData.setOutFee5(o.getOutFee5() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee5()) && o.getInFee5() > 0)
                orderDetailData.setInFee5(o.getInFee5() / 100.0);

            list.add(orderDetailData);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult(result.getTotal(), list));
    }

    @Override
    public ServerResponse<EGridResult> statisticsThirdPartyAccount(Integer page, Integer rows) {
        List<ThirdPartyAccountData> result = new ArrayList<>();

        String unitCode = request.getParameter("unitCode");
        String name = request.getParameter("cusName");
        String date = request.getParameter("date");
        String statisticsType = request.getParameter("type");

        if (StrUtil.isEmpty(date))
            return ServerResponse.createSuccess("请选择一天日期", new EGridResult(result.size(), result));
        if (StrUtil.isEmpty(statisticsType))
            return ServerResponse.createSuccess("请选择类型", new EGridResult(result.size(), result));

        Query<ThirdPartyAccount> query = thirdPartyAccountService.createQuery();
        query.criteria("type").equal(0);
        if (statisticsType.equals("0")) {
            if (StrUtil.isNotEmpty(unitCode)) query.criteria("unitCode").equal(unitCode);
        } else {
            if (StrUtil.isNotEmpty(name)) query.criteria("name").equal(name);
        }
        query.and(
                query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours(date, 0)),
                query.criteria("createTime").lessThan(IdUnit.weeHours(date, 1))
        );

        AggregationPipeline pipeline = thirdPartyAccountService.createAggregation();
        pipeline.match(query);

        if (statisticsType.equals("0")) {
            pipeline.group(
                    id(grouping("unitCode")),
                    grouping("unitCode", first("unitCode")),
                    grouping("id", first("id")),
                    grouping("name", first("name")),
                    grouping("counts", push("unitCode")),
                    grouping("totalFee", sum("totalFee")),
                    grouping("createTime", first("createTime"))
            );
        } else {
            pipeline.group(
                    id(grouping("id"), grouping("unitCode")),
                    grouping("unitCode", first("unitCode")),
                    grouping("id", first("id")),
                    grouping("name", first("name")),
                    grouping("counts", push("id")),
                    grouping("totalFee", sum("totalFee")),
                    grouping("createTime", first("createTime"))
            );
        }

        pipeline.lookup("sys_unit", "unitCode", "code", "sysUnits");
        pipeline.lookup("t_customer", "id", "phoneId", "cusUser");

        Iterator<ThirdPartyAccountData> iterator = pipeline.aggregate(ThirdPartyAccountData.class);

        while (iterator.hasNext()) {
            ThirdPartyAccountData account = iterator.next();
            account.setTotalFee(account.getTotalFee().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            result.add(account);
        }

        return ServerResponse.createSuccess(new EGridResult(result.size(), result));
    }

    @Override
    public ServerResponse<EGridResult> report4(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");       //2023-03-11
        String endTime = request.getParameter("endTime");           //2023-03-11
        Date startDate;
        if (StrUtil.isNotEmpty(startTime)) {
            startDate = IdUnit.weeHours(startTime, 0);
        } else {
            startDate = IdUnit.weeHours1(null, "00:00:00", 0);
        }
        Date endDate;
        if (StrUtil.isNotEmpty(endTime)) {
            endDate = IdUnit.weeHours(endTime, 1);
        } else {
            endDate = IdUnit.weeHours1(null, "00:00:00", 1);
        }

        String sUnitName = request.getParameter("sUnitName");
        String type = request.getParameter("type");
        String mold = request.getParameter("mold");
        List<OrderReport4> result = new ArrayList<>();
        if ("1".equals(mold) && "0".equals(type)) {
            result = report4_0(sUnitName, startDate, endDate);
        } else if ("1".equals(mold) && "1".equals(type)) {
            result = report4_1(sUnitName, startDate, endDate);
        } else if ("0".equals(mold) && "0".equals(type)) {
            result = report4_2(sUnitName, startDate, endDate);
        } else if ("0".equals(mold) && "1".equals(type)) {
            result = report4_3(sUnitName, startDate, endDate);
        }

        return ServerResponse.createSuccess(new EGridResult(result.size(), result));
    }

    // report4统计收货模式全部订单（已接单和已完成）
    // report4统计一级单位全部订单（已接单和已完成）
    private List<OrderReport4> report4_0(String unitName, Date startDate, Date endDate) {
        Query<Order> query = orderService.createQuery();
        if (StrUtil.isNotEmpty(unitName)) {
            query.or(
                    query.criteria("outUnitName").contains(unitName),
                    query.criteria("inUnitName").contains(unitName)
            );
        }
        query.and(
                query.criteria("time1").greaterThanOrEq(startDate.getTime()),
                query.criteria("time1").lessThan(endDate.getTime())
        );
        query.or(
                query.criteria("outFee0").greaterThan(0),
                query.criteria("inFee0").greaterThan(0),
                query.criteria("outFee5").greaterThan(0),
                query.criteria("inFee5").greaterThan(0)
        );

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("outUnitCode"),
                projection("inUnitCode"),
                projection("outUnitName"),
                projection("inUnitName"),
                projection("outFee0"),
                projection("inFee0"),
                projection("outFee5"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time1"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("outUnitCode"), grouping("inUnitCode"), grouping("callStartTime")),
                grouping("outUnitCode", first("outUnitCode")),
                grouping("inUnitCode", first("inUnitCode")),
                grouping("outUnitName", first("outUnitName")),
                grouping("inUnitName", first("inUnitName")),
                grouping("counts", push("outUnitCode")),
                grouping("time1", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("inFee0", sum("inFee0")),
                grouping("outFee5", sum("outFee5")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline.sort(Sort.ascending("time1"), Sort.ascending("outUnitCode"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);

        List<OrderReport4> list = new ArrayList<>();
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            list.add(orderReport4);
        }
        return list;
        /*Query<Order> query = orderService.createQuery();
         query.criteria("mold").notEqual(0);
        if (StrUtil.isNotEmpty(subName)) query.criteria("inSubName").contains(subName);
        query.and(
                query.criteria("time1").greaterThanOrEq(startDate.getTime()),
                query.criteria("time1").lessThan(endDate.getTime())
        );
        query.and(
                query.criteria("tranStatus").greaterThan(1),
                query.criteria("tranStatus").lessThanOrEq(5)
        );

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("inDefaultDownUnit"),
                projection("inSubName"),
                projection("inFee0"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time1"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("inDefaultDownUnit"), grouping("callStartTime")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("inSubName", first("inSubName")),
                grouping("counts", push("inDefaultDownUnit")),
                grouping("time1", first("callStartTime")),
                grouping("inFee0", sum("inFee0")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline.sort(Sort.ascending("time1"),Sort.ascending("inDefaultDownUnit"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);

        List<OrderReport4> list = new ArrayList<>();
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            list.add(orderReport4);
        }
        return list;*/
    }

    // report4统计收货模式已完成订单
    // report4统计一级单位已完成订单
    private List<OrderReport4> report4_1(String unitName, Date startDate, Date endDate) {
        List<OrderReport4> list = new ArrayList<>();

        Query<Order> query = orderService.createQuery();
        query.criteria("mold").equal(0);
        if (StrUtil.isNotEmpty(unitName)) {
            query.or(
                    query.criteria("outUnitName").contains(unitName),
                    query.criteria("inUnitName").contains(unitName)
            );
        }
        query.and(
                query.criteria("time3").greaterThanOrEq(startDate.getTime()),
                query.criteria("time3").lessThan(endDate.getTime())
        );
        query.or(
                query.criteria("outFee0").greaterThan(0),
                query.criteria("inFee0").greaterThan(0),
                query.criteria("outFee5").greaterThan(0),
                query.criteria("inFee5").greaterThan(0)
        );

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("outUnitCode"),
                projection("inUnitCode"),
                projection("outUnitName"),
                projection("inUnitName"),
                projection("outFee0"),
                projection("inFee0"),
                projection("outFee5"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time3"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("outUnitCode"), grouping("inUnitCode"), grouping("callStartTime")),
                grouping("outUnitCode", first("outUnitCode")),
                grouping("inUnitCode", first("inUnitCode")),
                grouping("outUnitName", first("outUnitName")),
                grouping("inUnitName", first("inUnitName")),
                grouping("counts", push("outUnitCode")),
                grouping("time3", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("inFee0", sum("inFee0")),
                grouping("outFee5", sum("outFee5")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline.sort(Sort.ascending("time3"), Sort.ascending("outUnitCode"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            list.add(orderReport4);
        }

        Query<Order> query2 = orderService.createQuery();
        query2.criteria("mold").notEqual(0);
        if (StrUtil.isNotEmpty(unitName)) {
            query2.or(
                    query2.criteria("outUnitName").contains(unitName),
                    query2.criteria("inUnitName").contains(unitName)
            );
        }
        query2.and(
                query2.criteria("time5").greaterThanOrEq(startDate.getTime()),
                query2.criteria("time5").lessThan(endDate.getTime())
        );
        query2.or(
                query2.criteria("outFee0").greaterThan(0),
                query2.criteria("inFee0").greaterThan(0),
                query2.criteria("outFee5").greaterThan(0),
                query2.criteria("inFee5").greaterThan(0)
        );

        AggregationPipeline pipeline2 = orderService.createAggregation();
        pipeline2.match(query2);

        pipeline2.project(
                projection("outUnitCode"),
                projection("inUnitCode"),
                projection("outUnitName"),
                projection("inUnitName"),
                projection("outFee0"),
                projection("inFee0"),
                projection("outFee5"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time5"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline2.group(
                id(grouping("outUnitCode"), grouping("inUnitCode"), grouping("callStartTime")),
                grouping("outUnitCode", first("outUnitCode")),
                grouping("inUnitCode", first("inUnitCode")),
                grouping("outUnitName", first("outUnitName")),
                grouping("inUnitName", first("inUnitName")),
                grouping("counts", push("outUnitCode")),
                grouping("time5", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("inFee0", sum("inFee0")),
                grouping("outFee5", sum("outFee5")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline2.sort(Sort.ascending("time5"), Sort.ascending("outUnitCode"));

        Iterator<OrderReport4> iterator2 = pipeline2.aggregate(OrderReport4.class);
        while (iterator2.hasNext()) {
            OrderReport4 orderReport4 = iterator2.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            list.add(orderReport4);
        }
        return list;

        /*Query<Order> query = orderService.createQuery();
        query.criteria("mold").notEqual(0);
        if (StrUtil.isNotEmpty(subName)) query.criteria("inSubName").contains(subName);
        query.and(
                query.criteria("time5").greaterThanOrEq(startDate.getTime()),
                query.criteria("time5").lessThan(endDate.getTime())
        );
        query.criteria("tranStatus").equal(5);

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("inDefaultDownUnit"),
                projection("inSubName"),
                projection("inFee0"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time5"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("inDefaultDownUnit"), grouping("callStartTime")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("inSubName", first("inSubName")),
                grouping("counts", push("inDefaultDownUnit")),
                grouping("time5", first("callStartTime")),
                grouping("inFee0", sum("inFee0")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline.sort(Sort.ascending("time5"), Sort.ascending("inDefaultDownUnit"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);

        List<OrderReport4> list = new ArrayList<>();
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            list.add(orderReport4);
        }
        return list;*/
    }

    // report4统计发货模式全部订单（已接单和已完成）
    // report4统计二级单位全部订单（已接单和已完成）
    private List<OrderReport4> report4_2(String subName, Date startDate, Date endDate) {
        Query<Order> query = orderService.createQuery();
        query.or(
                query.criteria("outSubName").contains(subName),
                query.criteria("inSubName").contains(subName)
        );
        query.and(
                query.criteria("time1").greaterThanOrEq(startDate.getTime()),
                query.criteria("time1").lessThan(endDate.getTime())
        );
        query.or(
                query.criteria("outFee0").greaterThan(0),
                query.criteria("inFee0").greaterThan(0),
                query.criteria("outFee5").greaterThan(0),
                query.criteria("inFee5").greaterThan(0)
        );

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("outDefaultDownUnit"),
                projection("inDefaultDownUnit"),
                projection("outSubName"),
                projection("inSubName"),
                projection("outFee0"),
                projection("inFee0"),
                projection("outFee5"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time1"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("outDefaultDownUnit"), grouping("inDefaultDownUnit"), grouping("callStartTime")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("inSubName", first("inSubName")),
                grouping("counts", push("outDefaultDownUnit")),
                grouping("time1", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("inFee0", sum("inFee0")),
                grouping("outFee5", sum("outFee5")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline.sort(Sort.ascending("time1"), Sort.ascending("outDefaultDownUnit"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);

        List<OrderReport4> list = new ArrayList<>();
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            list.add(orderReport4);
        }
        return list;
        /*Query<Order> query = orderService.createQuery();
         query.criteria("mold").notEqual(1);
        if (StrUtil.isNotEmpty(subName)) query.criteria("outSubName").contains(subName);
        query.and(
                query.criteria("time1").greaterThanOrEq(startDate.getTime()),
                query.criteria("time1").lessThan(endDate.getTime())
        );
        query.and(
                query.criteria("tranStatus").greaterThan(1),
                query.criteria("tranStatus").lessThanOrEq(5)
        );

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("outDefaultDownUnit"),
                projection("outSubName"),
                projection("outFee0"),
                projection("outFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time1"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("outDefaultDownUnit"), grouping("callStartTime")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("counts", push("outDefaultDownUnit")),
                grouping("time1", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("outFee5", sum("outFee5"))
        );

        pipeline.sort(Sort.ascending("time1"),Sort.ascending("outDefaultDownUnit"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);

        List<OrderReport4> list = new ArrayList<>();
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setOutFee0((new BigDecimal(orderReport4.getOutFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            orderReport4.setOutFee5((new BigDecimal(orderReport4.getOutFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            list.add(orderReport4);
        }
        return list;*/
    }

    // report4统计发货模式已完成订单
    private List<OrderReport4> report4_3(String subName, Date startDate, Date endDate) {
        List<OrderReport4> list = new ArrayList<>();

        Query<Order> query = orderService.createQuery();
        query.criteria("mold").equal(0);
        query.or(
                query.criteria("outSubName").contains(subName),
                query.criteria("inSubName").contains(subName)
        );
        query.and(
                query.criteria("time3").greaterThanOrEq(startDate.getTime()),
                query.criteria("time3").lessThan(endDate.getTime())
        );
        query.or(
                query.criteria("outFee0").greaterThan(0),
                query.criteria("inFee0").greaterThan(0),
                query.criteria("outFee5").greaterThan(0),
                query.criteria("inFee5").greaterThan(0)
        );

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("outDefaultDownUnit"),
                projection("inDefaultDownUnit"),
                projection("outSubName"),
                projection("inSubName"),
                projection("outFee0"),
                projection("inFee0"),
                projection("outFee5"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time3"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("outDefaultDownUnit"), grouping("inDefaultDownUnit"), grouping("callStartTime")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("inSubName", first("inSubName")),
                grouping("counts", push("outDefaultDownUnit")),
                grouping("time3", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("inFee0", sum("inFee0")),
                grouping("outFee5", sum("outFee5")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline.sort(Sort.ascending("time3"), Sort.ascending("outDefaultDownUnit"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            list.add(orderReport4);
        }

        Query<Order> query2 = orderService.createQuery();
        query2.criteria("mold").notEqual(0);
        query2.or(
                query2.criteria("outSubName").contains(subName),
                query2.criteria("inSubName").contains(subName)
        );
        query2.and(
                query2.criteria("time5").greaterThanOrEq(startDate.getTime()),
                query2.criteria("time5").lessThan(endDate.getTime())
        );
        query2.or(
                query2.criteria("outFee0").greaterThan(0),
                query2.criteria("inFee0").greaterThan(0),
                query2.criteria("outFee5").greaterThan(0),
                query2.criteria("inFee5").greaterThan(0)
        );

        AggregationPipeline pipeline2 = orderService.createAggregation();
        pipeline2.match(query2);

        pipeline2.project(
                projection("outDefaultDownUnit"),
                projection("inDefaultDownUnit"),
                projection("outSubName"),
                projection("inSubName"),
                projection("outFee0"),
                projection("inFee0"),
                projection("outFee5"),
                projection("inFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time5"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline2.group(
                id(grouping("outDefaultDownUnit"), grouping("inDefaultDownUnit"), grouping("callStartTime")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("inSubName", first("inSubName")),
                grouping("counts", push("outDefaultDownUnit")),
                grouping("time5", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("inFee0", sum("inFee0")),
                grouping("outFee5", sum("outFee5")),
                grouping("inFee5", sum("inFee5"))
        );

        pipeline2.sort(Sort.ascending("time5"), Sort.ascending("outDefaultDownUnit"));

        Iterator<OrderReport4> iterator2 = pipeline2.aggregate(OrderReport4.class);
        while (iterator2.hasNext()) {
            OrderReport4 orderReport4 = iterator2.next();
            orderReport4.setInFee0((new BigDecimal(orderReport4.getInFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            orderReport4.setInFee5((new BigDecimal(orderReport4.getInFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            list.add(orderReport4);
        }
        return list;
        /*Query<Order> query = orderService.createQuery();
        query.criteria("mold").notEqual(1);
        if (StrUtil.isNotEmpty(subName)) query.criteria("outSubName").contains(subName);
        query.and(
                query.criteria("time3").greaterThanOrEq(startDate.getTime()),
                query.criteria("time3").lessThan(endDate.getTime())
        );
        query.criteria("tranStatus").greaterThan(5);

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        pipeline.project(
                projection("outDefaultDownUnit"),
                projection("outSubName"),
                projection("outFee0"),
                projection("outFee5"),
                projection("callStartTime",     //临时借用String类型的callStartTime字段存取数据
                        // 截取日期
                        Projection.expression("$dateToString",
                                new BasicDBObject("format", "%Y-%m-%d").append("date", new BasicDBObject("$toDate", "$time3"))
                                        .append("timezone", "Asia/Shanghai"))
                )
        );

        pipeline.group(
                id(grouping("outDefaultDownUnit"), grouping("callStartTime")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("counts", push("outDefaultDownUnit")),
                grouping("time3", first("callStartTime")),
                grouping("outFee0", sum("outFee0")),
                grouping("outFee5", sum("outFee5"))
        );

        pipeline.sort(Sort.ascending("time3"), Sort.ascending("outDefaultDownUnit"));

        Iterator<OrderReport4> iterator = pipeline.aggregate(OrderReport4.class);

        List<OrderReport4> list = new ArrayList<>();
        while (iterator.hasNext()) {
            OrderReport4 orderReport4 = iterator.next();
            orderReport4.setOutFee0((new BigDecimal(orderReport4.getOutFee0()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            orderReport4.setOutFee5((new BigDecimal(orderReport4.getOutFee5()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).intValue()));
            list.add(orderReport4);
        }
        return list;*/
    }

    @Override
    public ServerResponse<EGridResult> preferentialRefundRecordList(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");       //2023-03-11
        String endTime = request.getParameter("endTime");           //2023-03-11
        if (StrUtil.isEmpty(startTime)) startTime = DateUtils.format(IdUnit.weeHours1(null, "00:00:00", 0));
        if (StrUtil.isEmpty(endTime)) endTime = DateUtils.format(IdUnit.weeHours1(null, "00:00:00", 1));

        String sUnitName = request.getParameter("sUnitName");

        Query<PreferentialRefundRecord> query = preferentialRefundRecordService.createQuery();
        query.criteria("dateStr").greaterThanOrEq(startTime);
        query.criteria("dateStr").lessThan(endTime);
        if (StrUtil.isNotEmpty(sUnitName)) query.criteria("subName").contains(sUnitName);
        EGridResult<PreferentialRefundRecord> result = preferentialRefundRecordService.findPage(page, rows, query);

        List<PreferentialRefundRecord> list = result.getRows();
        List<PreferentialRefundRecord> resultList = new ArrayList<>();
        for (PreferentialRefundRecord record : list) {
            PreferentialRefundRecord resultRecord = new PreferentialRefundRecord();
            BeanUtils.copyProperties(record, resultRecord);
            BigDecimal totalFee = record.getTotalFee().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);//转换为元
            resultRecord.setTotalFee(totalFee);
            resultList.add(resultRecord);
        }
        EGridResult eGridResult = new EGridResult();
        eGridResult.setRows(resultList);
        eGridResult.setTotal(result.getTotal());

        return ServerResponse.createSuccess(eGridResult);
    }
}
