package com.erdos.coal.park.api.manage.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.entity.OwnerAppeal;
import org.springframework.web.multipart.MultipartFile;

public interface IOwnerAppealService extends IBaseMongoService<OwnerAppeal> {
    ServerResponse<OwnerAppeal> saveAppeal(MultipartFile cardBef, MultipartFile cardBack, MultipartFile ownerCard, MultipartFile license, String mobile, String identity, String carNum, String reason, Integer appOrWx);
}
