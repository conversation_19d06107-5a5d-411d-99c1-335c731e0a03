package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

@Entity(value = "sys_role_menu", noClassnameStored = true)
@Indexes(@Index(fields = {@Field(value = "rid"), @Field(value = "mid")}))
public class SysRoleMenu extends BaseMongoInfo {

    private String rid;//角色id
    private String mid;//菜单id
    private String[] attr;//菜单属性

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String[] getAttr() {
        return attr;
    }

    public void setAttr(String[] attr) {
        this.attr = attr;
    }
}
