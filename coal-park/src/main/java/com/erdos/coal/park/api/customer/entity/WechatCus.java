package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_wechat_cus", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("openid")})
})
public class WechatCus extends BaseMongoInfo {
    private String cid; //关联客商编号
    //@Indexed(options = @IndexOptions(name = "openid", background = true))
    private String openid;  //微信号 用户唯一标识
    private String sessionKey;  //会话密钥
    private Integer times = 0;  //使用次数

    private String mobile; //手机号
    private String name; //客商真实姓名
    private String sex; //客商性别
    private String job; //客商职务
    private String company; //客商所属公司
    private String identity; //身份证号
    private Integer state;//用户狀態    0-未认证，1-正常,2-停用

    private String fee;//设置默认收取司机的金额(元)
    private String barPwd;//交易密码

    private String abilityTag; //黑白名单标识 0:黑名单 1：白名单
    private String phoneId;//手机序列号
    private String deviceId;//阿里云推送 设备号
    private String nickname;//微信昵称
    private String wxName;  //微信账户真实姓名

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getAbilityTag() {
        return abilityTag;
    }

    public void setAbilityTag(String abilityTag) {
        this.abilityTag = abilityTag;
    }

    public String getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(String phoneId) {
        this.phoneId = phoneId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getBarPwd() {
        return barPwd;
    }

    public void setBarPwd(String barPwd) {
        this.barPwd = barPwd;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getWxName() {
        return wxName;
    }

    public void setWxName(String wxName) {
        this.wxName = wxName;
    }
}
