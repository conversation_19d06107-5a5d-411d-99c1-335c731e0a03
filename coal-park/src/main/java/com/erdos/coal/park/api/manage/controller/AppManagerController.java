package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.manage.pojo.SysAppData;
import com.erdos.coal.park.api.manage.service.IAppManagerService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

//"app版本更新接口列表"
@RestController
@RequestMapping("/api/manage/app")
public class AppManagerController extends BaseController {
    @Resource
    private IAppManagerService appService;

    @InvokeLog(description = "更新app版本 接口") //日志
    @PostMapping(value = "/update_app")
    public ServerResponse<SysAppData> updateAppHandler(
            @RequestParam(value = "cusOrDri") String cusOrDri  //"客商或司机APP 0-客商 1-司机"
    ) throws GlobalException {
        return appService.getInfo(cusOrDri);
    }

    @InvokeLog(description = "扫码下载app 接口")  // 日志
    @GetMapping(value = "/download_app")
    public void downloadHandler() throws GlobalException, IOException {
        appService.downloadApp(request, response);
    }
}
