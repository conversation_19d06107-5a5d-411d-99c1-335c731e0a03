package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

@Entity(value = "t_cus_message",noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("cid")}),
        @Index(fields = {@Field("type")})
})
public class CusMessage extends BaseMongoInfo {
    private String cid;
    private String oid;

    private String title;  //标题
    private String body;    //内容
    private String did;     //司机id
    private String didMobile;     //司机手机号
    private String carNum;     //订单车牌号
    private Boolean read;   //是否已读 true已读，false未读
    private Integer type;   //0：司机主动撤单，1：司机拒绝订单

    //可能的微信消息推送返回结果字段
    private String errCode;
    private String errMsg;
    private String msGid;

    public CusMessage() {
    }

    public CusMessage(String cid, String oid, String title, String body, String did, String didMobile, String carNum, Boolean read, Integer type) {
        this.cid = cid;
        this.oid = oid;
        this.title = title;
        this.body = body;
        this.did = did;
        this.didMobile = didMobile;
        this.carNum = carNum;
        this.read = read;
        this.type = type;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getDidMobile() {
        return didMobile;
    }

    public void setDidMobile(String didMobile) {
        this.didMobile = didMobile;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Boolean getRead() {
        return read;
    }

    public void setRead(Boolean read) {
        this.read = read;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
