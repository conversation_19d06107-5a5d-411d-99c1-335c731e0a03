package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.IDriverGroupPrequelDao;
import com.erdos.coal.park.api.customer.entity.DriverGroup;
import com.erdos.coal.park.api.customer.entity.DriverGroupPrequel;
import com.erdos.coal.park.api.customer.service.IDriverGroupPrequelService;
import com.erdos.coal.park.api.customer.service.IDriverGroupService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service("driverGroupPrequelService")
public class DriverGroupPrequelServiceImpl extends BaseMongoServiceImpl<DriverGroupPrequel, IDriverGroupPrequelDao> implements IDriverGroupPrequelService {

    @Resource
    private IPushInfoService pushInfoService;
    @Lazy
    @Resource
    private IDriverGroupService driverGroupService;
    @Lazy
    @Resource
    private IDriverInfoService driverInfoService;

    @Override
    public void saveAndSend(List<DriverGroupPrequel> prequelList, String groupNo, String groupName) {
        for (DriverGroupPrequel prequel : prequelList) {
            DriverInfo driverInfo = prequel.getDriverInfo();

            String thing4 = "邀请您加入<" + groupName + ">司机组";
            //pushInfoService.weChatSend13662(driverInfo.getPhoneId(), prequel.getcName(), thing4, prequel.getDid(), groupNo);
        }
        this.save(prequelList);
    }

    @Override
    public void sendDelInformation(List<DriverGroupPrequel> prequelList, String groupNo, String groupName) {
        for (DriverGroupPrequel prequel : prequelList) {
            DriverInfo driverInfo = prequel.getDriverInfo();

            String thing4 = "您已被移出<" + groupName + ">司机组";
            pushInfoService.weChatSend13662(driverInfo.getPhoneId(), prequel.getcName(), thing4, prequel.getDid(), groupNo);
        }
    }

    @Override
    public ServerResponse<String> saveDvrGroupInvite(String groupNo, Integer manner) {
        String did = ShiroUtils.getUserId();
        Query<DriverGroupPrequel> query = this.createQuery();
        query.criteria("groupNo").equal(groupNo);
        query.criteria("did").equal(did);
        query.criteria("delete").equal(false);
        DriverGroupPrequel prequel = this.get(query);

        if (prequel == null) return ServerResponse.createError("消息已处理");

        if (manner == 0) {
            DriverInfo driverInfo = driverInfoService.getByPK(did);
            DriverGroup group = driverGroupService.getByPK(groupNo);
            String[] dvrId = group.getDvrId();
            List<DriverInfo> driverInfos = group.getDriverInfos();

            UpdateOperations<DriverGroup> updateOperations = driverGroupService.createUpdateOperations();
            driverInfos.add(driverInfo);
            updateOperations.set("driverInfos", driverInfos);

            String[] newDvrId;
            if (dvrId == null) {
                newDvrId = new String[]{did};
            } else {
                List<String> dvrIds = new ArrayList<>(dvrId.length);
                Collections.addAll(dvrIds, dvrId);
                dvrIds.add(did);
                newDvrId = dvrIds.toArray(new String[dvrIds.size()]);
            }
            updateOperations.set("dvrId", newDvrId);
            driverGroupService.update(driverGroupService.createQuery().filter("_id", new ObjectId(groupNo)), updateOperations);

            this.delete(query);
        } else if (manner == 1) {
            UpdateOperations<DriverGroupPrequel> updateOperations = this.createUpdateOperations();
            updateOperations.set("delete", true);
            this.update(query, updateOperations);
        }

        return ServerResponse.createSuccess("成功");
    }
}
