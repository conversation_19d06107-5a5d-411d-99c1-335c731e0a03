package com.erdos.coal.park.web.app.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.core.base.mongo.entity.GeoPoint;
import dev.morphia.annotations.*;
import dev.morphia.geo.Geometry;

/**
 * 新建集合（用于保存扫码设备的信息）
 * 包含如下字段
 * 设备编码
 * 设备名称
 * 设备标识（设备二维码内包含的内容）
 * 一级单位
 * 二级单位
 * 地理位置（经纬度）
 * 设备状态
 * <p>
 * 在平台后台管理web端 “基础设置” 下增加一个二级菜单 “设备管理”
 * 页面包含新增修改删除按钮
 * 数据展示上面集合里的信息
 * <p>
 * 司机扫码后分为以下步骤
 * 1 司机扫码后建立连接 二维码里包含设备信息 参数里包含订单编号
 * 2 根据设备信息查询是否在设备集合里注册 未注册返回错误
 * 3 根据设备信息在连接列表里查询 设备端的PC连接是否已建立 未建立返回错误
 * 4 根据司机的订单编号查询企业订单编号返回给设备端的PC连接（业务逻辑参考https://heijinyun.net/api/bus/receive_car_msg/search_order）
 * 5 设备端的PC操作完成调用服务器接口 然后通过司机端连接推送操作结果给司机
 * 6 司机端和设备PC端断开连接
 * 2024-4-11 增加二维码字段 qrCode, 并在保存时同步为 deviceId.
 * 脚本执行:
 * // 复制 deviceId 到新的列 qrCode 中
 * db.t_device_info.find().forEach(function(item) {
 *     db.t_device_info.update({
 *         "_id": item._id
 *     }, {
 *         "$set": {
 *             "qrCode": item.deviceId
 *         }
 *     }, false, true)
 * });
 *
 */
@Entity(value = "t_device_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("deviceId")}, options = @IndexOptions(unique = true))
})
public class DeviceInfo extends BaseMongoInfo {

    // 设备标识（设备二维码内包含的内容）
    private String deviceId;
    // 2024-4-11 增加二维码字段,并在保存时同步为 deviceId.
    private String qrCode;
    // 设备编码
    private String deviceCode;
    // 设备名称
    private String deviceName;
    // 一级单位
    private String unitCode;
    private String unitName;
    // 二级单位
    private String subCode = "";
    private String subName;

    // 地理位置（经纬度）
    private String longitude;   //经度
    private String latitude;    //纬度
    //@Indexed(value = IndexDirection.GEO2DSPHERE, name = "_geometry", background = true)
    private Geometry geometry;
    //点（Point）,线（LineString）,多边形（Polygon）,多点（MultiPoint）,多线（MultiLineString）,多个多边形（MultiPolygon）,几何集合（GeometryCollection）
    // 设备状态
    private Integer status;   //状态（0：未注册1：已注册）

    private GeoPoint point;

    //2022-10-10 增加是否检查距离, 0: 不检查；1：检查
    private Integer checkDistance = 1;

    public String getDeviceCode() {
        return deviceCode;
    }

    public DeviceInfo setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
        return this;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public DeviceInfo setDeviceName(String deviceName) {
        this.deviceName = deviceName;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public DeviceInfo setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getQrCode() {
        return qrCode;
    }

    public DeviceInfo setQrCode(String qrCode) {
        this.qrCode = qrCode;
        return this;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public DeviceInfo setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        return this;
    }

    public String getUnitName() {
        return unitName;
    }

    public DeviceInfo setUnitName(String unitName) {
        this.unitName = unitName;
        return this;
    }

    public String getSubCode() {
        return subCode;
    }

    public DeviceInfo setSubCode(String subCode) {
        this.subCode = subCode;
        return this;
    }

    public String getSubName() {
        return subName;
    }

    public DeviceInfo setSubName(String subName) {
        this.subName = subName;
        return this;
    }

    public String getLongitude() {
        return longitude;
    }

    public DeviceInfo setLongitude(String longitude) {
        this.longitude = longitude;
        return this;
    }

    public String getLatitude() {
        return latitude;
    }

    public DeviceInfo setLatitude(String latitude) {
        this.latitude = latitude;
        return this;
    }

    public Geometry getGeometry() {
        return geometry;
    }

    public DeviceInfo setGeometry(Geometry geometry) {
        this.geometry = geometry;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public DeviceInfo setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public GeoPoint getPoint() {
        return point;
    }

    public DeviceInfo setPoint(GeoPoint point) {
        this.point = point;
        return this;
    }

    public Integer getCheckDistance() {
        return checkDistance;
    }

    public void setCheckDistance(Integer checkDistance) {
        this.checkDistance = checkDistance;
    }
}
