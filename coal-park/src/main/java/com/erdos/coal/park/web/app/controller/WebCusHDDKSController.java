package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.entity.CusHDDKS;
import com.erdos.coal.park.web.app.entity.DistrictCode;
import com.erdos.coal.park.web.app.service.ICusHDDKSService;
import com.erdos.coal.park.web.app.service.IDistrictCodeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/cus_hdd_ks")
public class WebCusHDDKSController extends BaseController {
    @Resource
    private ICusHDDKSService cusHDDKSService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return cusHDDKSService.loadGrid(page, rows);
    }

    @PostMapping("/add")
    public ServerResponse addKSHandler(@RequestBody CusHDDKS cusHDDKS) throws GlobalException {
        return cusHDDKSService.addCusHDDKS(cusHDDKS);
    }

    @PostMapping("/edit")
    public ServerResponse editKSHandler(@RequestBody CusHDDKS cusHDDKS) throws GlobalException {
        return cusHDDKSService.editCusHDDKS(cusHDDKS);
    }

    @PostMapping("/del")
    public ServerResponse delKSHandler(@RequestBody CusHDDKS cusHDDKS) throws GlobalException {
        return cusHDDKSService.delCusHDDKS(cusHDDKS);
    }
}
