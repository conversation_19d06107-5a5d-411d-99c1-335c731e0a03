package com.erdos.coal.park.api.business.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.entity.TradePrice;
import com.erdos.coal.park.api.business.entity.TradeUnit;
import com.erdos.coal.park.api.business.pojo.VehicleTypeCost;
import com.erdos.coal.park.api.business.service.*;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

//"企业推送绑定客商信息到平台"
@RestController
@RequestMapping("/api/bus/unit_info")
public class UnitInfoController extends BaseController {
    @Resource
    private IUnitInfoService unitInfoService;
    @Resource
    private ISMSService ismsService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private ITradeUnitService tradeUnitService;
    @Resource
    private ITradeContractService tradeContractService;
    @Resource
    private ITradePriceService tradePriceService;
    @Resource
    private ITradeContractWhiteListService tradeContractWhiteListService;

    @InvokeLog(description = "企业绑定客商 接口") //日志
    @PostMapping(value = "/bound_customer")
    public ServerResponse<String> boundCustomerHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String uName = (String) data.get("uName");
        String telCode = (String) data.get("telCode");
        String unitCode = (String) data.get("unitCode");
        String userCode = (String) data.get("userCode");
        String smsCode = (String) data.get("smsCode");
        if (StrUtil.isEmpty(uName) || StrUtil.isEmpty(telCode) || StrUtil.isEmpty(unitCode) || StrUtil.isEmpty(userCode)) {
            return ServerResponse.createError("参数错误");
        }
        if (StrUtil.isEmpty(smsCode))
            return ServerResponse.createError("短信验证码不能为空！");
        SMS sms = ismsService.get("mobile", telCode);
        if (StrUtil.isEmpty(sms) || !sms.getCode().equals(smsCode))
            return ServerResponse.createError("短信验证码错误！");

        return unitInfoService.boundCustomer(uName, telCode, unitCode, userCode);
        /*String str = "";
        try {
            str = URLDecoder.decode(data.toJSONString(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return ServerResponse.createSuccess("卡洛斯的反馈");*/
    }

    @InvokeLog(description = "企业绑定客商 接口") //日志
    @PostMapping(value = "/bound_customer2")
    public ServerResponse<String> boundCustomer2Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String telCode = (String) data.get("telCode");
        String unitCode = (String) data.get("unitCode");
        String userCode = (String) data.get("userCode");
        String tempCode = (String) data.get("tempCode");
        if (StrUtil.isEmpty(telCode) || StrUtil.isEmpty(unitCode) || StrUtil.isEmpty(userCode)) {
            return ServerResponse.createError("参数错误");
        }
        if (StrUtil.isEmpty(tempCode) || !"9325da1920a14ca6b076f4487af10793".equals(tempCode))
            return ServerResponse.createError("验证码错误");

        return unitInfoService.boundCustomer(null, telCode, unitCode, userCode);
    }

    @InvokeLog(description = "企业废除绑定客商 接口") //日志
    @PostMapping(value = "/cancel_bound")
    public ServerResponse<String> cancelBoundHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String userCode = (String) data.get("userCode");
        String telCode = (String) data.get("telCode");
        //String smsCode = (String) data.get("smsCode");
        if (StrUtil.isEmpty(userCode))// || StrUtil.isEmpty(smsCode))
            return ServerResponse.createError("参数错误");

       /* SMS sms = ismsService.get("mobile", telCode);
        if (StrUtil.isEmpty(sms) || !sms.getCode().equals(smsCode))
            return ServerResponse.createError("短信验证码错误！");*/

        return unitInfoService.cancelBound(userCode);
    }

    @InvokeLog(description = "企业添加二级单位 接口") //日志
    @PostMapping(value = "/add_second_unit")
    public ServerResponse<String> addSecondUnitHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String pCode = (String) data.get("pCode");
        String subUnitList = (String) data.get("subUnitList");
        if (StrUtil.isEmpty(pCode) || StrUtil.isEmpty(subUnitList))
            return ServerResponse.createError("参数错误");

        return unitInfoService.addSecondUnit(pCode, subUnitList);
    }

    @InvokeLog(description = "企业修改二级单位 接口") //日志
    @PostMapping(value = "/edit_second_unit")
    public ServerResponse<String> editSecondUnitHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String code = (String) data.get("code");
        String name = (String) data.get("name");
        if (StrUtil.isEmpty(code) || StrUtil.isEmpty(name))
            return ServerResponse.createError("参数错误");

        return unitInfoService.editSecondUnit(code, name);
    }

    @InvokeLog(description = "企业删除二级单位 接口") //日志
    @PostMapping(value = "/del_second_unit")
    public ServerResponse<String> delSecondUnitHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String code = (String) data.get("code");
        if (StrUtil.isEmpty(code))
            return ServerResponse.createError("参数错误");

        return unitInfoService.delSecondUnit(code);
    }

    @InvokeLog(description = "企业推送trade数据 接口") //日志
    @PostMapping(value = "/trade")
    public ServerResponse<List<Integer>> tradeHandler(
            @RequestBody JSONArray data
    ) throws GlobalException {
        SysUnit sysUnit = null;
        // 遍历数据，按tableType和operateType 进行数据分类
        // 1.新增
        List<Integer> addIds1 = new ArrayList<>();
        List<Integer> addIds2 = new ArrayList<>();
        List<Integer> addIds3 = new ArrayList<>();
        List<TradeUnit> addTradeUnitList = new ArrayList<>();
        List<TradeContract> addTradeContractList = new ArrayList<>();
        List<TradePrice> addTradePriceList = new ArrayList<>();
        // 2.修改
        Map<Integer, TradePrice> editTradePriceMap = new HashMap<>();
        // 3.删除
        List<Integer> delIds1 = new ArrayList<>();
        List<Integer> delIds2 = new ArrayList<>();
        List<Integer> delIds3 = new ArrayList<>();
        List<String> delTradeUnitCodes = new ArrayList<>();
        List<String> delTradeContractCodes = new ArrayList<>();
        List<String> delTradePriceCodes = new ArrayList<>();

        Integer tempId3 = null;
        TradePrice tempTradePrice = null;
        boolean edit = false;
        for (int i = 0; i < data.size(); i++) {
            JSONObject object = data.getJSONObject(i);
            int id = object.getInteger("id");
            int tableType = object.getInteger("tableType");
            int operateType = object.getInteger("operateType");

            if (StrUtil.isEmpty(sysUnit))
                sysUnit = sysUnitService.get("code", Pattern.compile(object.getString("unitCode") + "$"));

            if (tableType == 1) {
                String bizUnitCode = object.getString("bizUnitCode");
                TradeUnit tradeUnit = JSON.toJavaObject(object, TradeUnit.class);
                if (operateType == 0) {
                    addIds1.add(id);
                    addTradeUnitList.add(tradeUnit);
                } else if (operateType == 2) {
                    delIds1.add(id);
                    delTradeUnitCodes.add(bizUnitCode);
                }
            } else if (tableType == 2) {
                String bizContractCode = object.getString("bizContractCode");
                TradeContract tradeContract = JSON.toJavaObject(object, TradeContract.class);
                if (operateType == 0) {
                    JSONObject costP = object.getJSONObject("vehicleTypeCost");
                    if (costP != null) {
                        JSONArray costC = costP.getJSONArray("vehicleTypeCost");
                        if (costC != null) {
                            List<VehicleTypeCost> vehicleTypeCostList = costC.toJavaList(VehicleTypeCost.class);
                            tradeContract.setVehicleTypeCostList(vehicleTypeCostList);
                        }
                    }

                    addIds2.add(id);
                    addTradeContractList.add(tradeContract);
                } else if (operateType == 2) {
                    delIds2.add(id);
                    delTradeContractCodes.add(bizContractCode);
                }
            } else if (tableType == 3) {
                String selfCode = object.getString("selfCode");
                if (null != tempTradePrice && selfCode.equals(tempTradePrice.getSelfCode())) {
                    edit = true;
                } else if (null != tempTradePrice && !selfCode.equals(tempTradePrice.getSelfCode())) {
                    delIds3.add(tempId3);
                    delTradePriceCodes.add(tempTradePrice.getSelfCode());
                    tempId3 = null;
                    tempTradePrice = null;
                }
                TradePrice tradePrice = JSON.toJavaObject(object, TradePrice.class);
                if (operateType == 0) {
                    if (edit) {
                        editTradePriceMap.put(id, tradePrice);
                        edit = false;
                    } else {
                        addIds3.add(id);
                        if (1 == tradePrice.getBizType()) {
                            tradePrice.setFee(sysUnit.getStandardDeduct());
                        } else {
                            tradePrice.setFee(sysUnit.getStandardDeductIn());
                        }
                        addTradePriceList.add(tradePrice);
                    }
                } else if (operateType == 2) {
                    tempId3 = id;
                    tempTradePrice = tradePrice;
                }
            }
        }
        if (null != tempTradePrice) {
            delIds3.add(tempId3);
            delTradePriceCodes.add(tempTradePrice.getSelfCode());
            tempId3 = null;
            tempTradePrice = null;
        }

        List<Integer> successIds = new ArrayList<>();
        if (delTradeUnitCodes.size() > 0) {
            boolean d = tradeUnitService.del(delTradeUnitCodes);
            if (d) successIds.addAll(delIds1);
        }
        if (delTradeContractCodes.size() > 0) {
            boolean d = tradeContractService.del(delTradeContractCodes);
            if (d) successIds.addAll(delIds2);
        }
        if (delTradePriceCodes.size() > 0) {
            boolean d = tradePriceService.del(delTradePriceCodes);
            if (d) successIds.addAll(delIds3);
        }
        if (addTradeUnitList.size() > 0) {
            boolean a = tradeUnitService.add(addTradeUnitList);
            if (a) successIds.addAll(addIds1);
        }
        if (addTradeContractList.size() > 0) {
            boolean a = tradeContractService.add(addTradeContractList);
            if (a) successIds.addAll(addIds2);
        }
        if (addTradePriceList.size() > 0) {
            boolean a = tradePriceService.add(addTradePriceList);
            if (a) successIds.addAll(addIds3);
        }
        if (editTradePriceMap.size() > 0) {
            List<Integer> editIds = tradePriceService.edit(editTradePriceMap);
            successIds.addAll(editIds);
        }

        tradeContractWhiteListService.sendTradeSms(addTradePriceList, editTradePriceMap);
        return ServerResponse.createSuccess("success", successIds);
    }
}
