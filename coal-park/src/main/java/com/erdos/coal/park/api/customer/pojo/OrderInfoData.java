package com.erdos.coal.park.api.customer.pojo;

import com.erdos.coal.utils.StrUtil;

import java.io.Serializable;

public class OrderInfoData implements Serializable {

    private Long updateTime;     //时间
    private String tradeName;      //商品名称
    private String beginPoint;     //起点
    private String endPoint;        //终点

    private String oid;     //单号
    private String carNum;     //车牌号
    private Double price; //单价
    private Double distance;//总里程
    private Double tolls;//预估过路费

    private String outVariety;//发货单位产品名称
    private String inVariety;//收货单位产品名称
    private Integer mold;   //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2
    private boolean delete;//订单废除状态 true废除，false未废除

    private String ysStatus;    //运输状态（-1:无司机接单，0：司机接单但未拉货；1：运输途中；2：订单完成；3：订单已废除; 4:司机拒绝订单）
    private Integer tranStatus; //订单状态：0-未接单，1-已接单，2-发货入场，3-运输中，4-收货入场，5-订单完成，6-订单废除,7-司机拒绝订单
    private String outUnitCode;                     //发货单位编码
    private String outUnitName;                     //发货单位名称
    private String inUnitCode;                      //收货单位编码
    private String inUnitName;                      //收货单位名称
    private String outBizContractCode;             //发货单位业务合同编码
    private String outBizContractName;             //发货单位业务合同名称
    private String inBizContractCode;              //收货单位业务合同编码
    private String inBizContractName;              //收货单位业务合同名称
    private String outDefaultDownUnit;             //发货单位 默认二级单位编码
    private String outSubName;                      //发货单位 默认二级单位名称
    private String inDefaultDownUnit;              //收货单位 默认二级单位编码
    private String inSubName;                       //收货单位 默认二级单位名称
    private String outDefaultArea;                 //发货单位 默认场区编码
    private String outAreaName;                    //发货单位 默认场区名称
    private String inDefaultArea;                  //收货单位 默认场区编码
    private String inAreaName;                     //收货单位 默认场区名称

    private Boolean[] appType;
    //App端需要处理的order类型：index 0-是否可修改，1-是否可废除，2-是否可分享给微信司机用户，3-是否来自友商分享，
    // 4-是否可分享给友商，5-是否可退回，
    private Integer share;//0-订单未分享给好友客商, 1-订单分享给好友客商

    //发货 或 收货 单位 checking到3-检票时，会有毛重和皮重参数要记录到订单中
    private String outGrossWeight;
    private String outTareWeight;
    private String inGrossWeight;
    private String inTareWeight;
    private Integer isTransport;   //订单是否可运输：0-可运输，1-禁止运输

    private String outBillCode;     //发货企业票号
    private String inBillCode;      //收货企业票号

    private Double inNetWeight;     // 采购业务需要录入对方的净重
    private String loadPound;           // 采购业务需要录入对方的装货单号
    private String loadPoundPho;           // 采购业务需要上传对方的装货单照片
    private Long loadTime;              // 采购业务需要录入对方的装货时间

    // 2024-12-28 急加
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;

    public Integer getIsTransport() {
        return isTransport;
    }

    public void setIsTransport(Integer isTransport) {
        this.isTransport = StrUtil.isNotEmpty(isTransport) ? isTransport : 0;
    }

    public String getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(String outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public String getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(String outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public String getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(String inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public String getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(String inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    public Boolean[] getAppType() {
        return appType;
    }

    public void setAppType(Boolean[] appType) {
        this.appType = appType;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public boolean isDelete() {
        return delete;
    }

    public boolean getDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }

    public String getYsStatus() {
        return ysStatus;
    }

    public void setYsStatus(String ysStatus) {
        this.ysStatus = ysStatus;
    }

    public Integer getTranStatus() {
        return tranStatus;
    }

    public void setTranStatus(Integer tranStatus) {
        this.tranStatus = tranStatus;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getOutDefaultArea() {
        return outDefaultArea;
    }

    public void setOutDefaultArea(String outDefaultArea) {
        this.outDefaultArea = outDefaultArea;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getInDefaultArea() {
        return inDefaultArea;
    }

    public void setInDefaultArea(String inDefaultArea) {
        this.inDefaultArea = inDefaultArea;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getOutBillCode() {
        return outBillCode;
    }

    public void setOutBillCode(String outBillCode) {
        this.outBillCode = outBillCode;
    }

    public String getInBillCode() {
        return inBillCode;
    }

    public void setInBillCode(String inBillCode) {
        this.inBillCode = inBillCode;
    }

    public Double getInNetWeight() {
        return inNetWeight;
    }

    public void setInNetWeight(Double inNetWeight) {
        this.inNetWeight = inNetWeight;
    }

    public String getLoadPound() {
        return loadPound;
    }

    public void setLoadPound(String loadPound) {
        this.loadPound = loadPound;
    }

    public String getLoadPoundPho() {
        return loadPoundPho;
    }

    public void setLoadPoundPho(String loadPoundPho) {
        this.loadPoundPho = loadPoundPho;
    }

    public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }
}
