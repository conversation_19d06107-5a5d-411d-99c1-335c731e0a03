package com.erdos.coal.park.api.business.pojo;

import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.driver.entity.DriverInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class SynOrder2 implements Serializable {
    private String objId;

    private List<DriverInfo> driverInfo;
    private String dvrName;
    private String dvrMobile;
    private List<CustomerUser> customerUser;
    private String cusName;
    private String cusMobile;

    private String oid;     //订单号
    private String carNum;  //车牌号
    private String outUnitCode;                     //发货单位编码
    private String outUnitName;                     //发货单位名称
    private String outBizContractCode;             //发货单位业务合同编码
    private String outBizContractName;             //发货单位业务合同名称
    private String outSubName;                      //发货单位 默认二级单位名称
    private String outAreaName;                    //发货单位 默认场区名称
    private String outBillCode;     //发货企业票号
    private String outGrossWeight;
    private String outTareWeight;
    private Date otherOutTime;      //发货单位，checking 为 '检票' 时，记录的时间
    private String outTradingUnitName;      //发货交易单位名称
    private Double outWeight;       //发货入场重量
    private String inUnitCode;                     //收货单位编码
    private String inUnitName;                     //收货单位名称
    private String inBizContractCode;             //收货单位业务合同编码
    private String inBizContractName;             //收货单位业务合同名称
    private String inSubName;                      //收货单位 默认二级单位名称
    private String inAreaName;                    //收货单位 默认场区名称
    private String inBillCode;     //收货企业票号
    private String inGrossWeight;
    private String inTareWeight;
    private Date otherInTime;      //收货单位，checking 为 '检票' 时，记录的时间
    private String inTradingUnitName;      //收货交易单位名称
    private Double inWeight;       //收货入场重量

    private String did;
    private String outTradingUnit;      //发货交易单位编号
    private String inTradingUnit;      //收货交易单位编号
    private String cid;

    private Date time1;         //接单时间
    private Date time2;         //过空时间  就是checking=1时的时间
    private Date time3;         //离场时间  就是checking=3时的时间
    private Date time4;         //离场时间  就是checking=3时的时间
    private Date time5;         //离场时间  就是checking=3时的时间

    private String outVariety;  //品种
    private String inVariety;  //品种

    private Integer mold;                            //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2

    private Date createTime;
    private Long updateTime;

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public String getDvrName() {
        return driverInfo.get(0).getName();
    }

    public void setDvrName(String dvrName) {
        this.dvrName = dvrName;
    }

    public String getDvrMobile() {
        return driverInfo.get(0).getMobile();
    }

    public void setDvrMobile(String dvrMobile) {
        this.dvrMobile = dvrMobile;
    }

    public String getCusName() {
        return customerUser.get(0).getName();
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusMobile() {
        return customerUser.get(0).getMobile();
    }

    public void setCusMobile(String cusMobile) {
        this.cusMobile = cusMobile;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getOutBillCode() {
        return outBillCode;
    }

    public void setOutBillCode(String outBillCode) {
        this.outBillCode = outBillCode;
    }

    public String getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(String outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public String getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(String outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public Date getOtherOutTime() {
        return otherOutTime;
    }

    public void setOtherOutTime(Date otherOutTime) {
        this.otherOutTime = otherOutTime;
    }

    public String getOutTradingUnitName() {
        return outTradingUnitName;
    }

    public void setOutTradingUnitName(String outTradingUnitName) {
        this.outTradingUnitName = outTradingUnitName;
    }

    public Double getOutWeight() {
        return outWeight;
    }

    public void setOutWeight(Double outWeight) {
        this.outWeight = outWeight;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getOutTradingUnit() {
        return outTradingUnit;
    }

    public void setOutTradingUnit(String outTradingUnit) {
        this.outTradingUnit = outTradingUnit;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public void setDriverInfo(List<DriverInfo> driverInfo) {
        this.driverInfo = driverInfo;
    }

    public void setCustomerUser(List<CustomerUser> customerUser) {
        this.customerUser = customerUser;
    }

    public Date getTime1() {
        return time1;
    }

    public void setTime1(Date time1) {
        this.time1 = time1;
    }

    public Date getTime2() {
        return time2;
    }

    public void setTime2(Date time2) {
        this.time2 = time2;
    }

    public Date getTime3() {
        return time3;
    }

    public void setTime3(Date time3) {
        this.time3 = time3;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public String getInBillCode() {
        return inBillCode;
    }

    public void setInBillCode(String inBillCode) {
        this.inBillCode = inBillCode;
    }

    public String getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(String inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public String getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(String inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    public Date getOtherInTime() {
        return otherInTime;
    }

    public void setOtherInTime(Date otherInTime) {
        this.otherInTime = otherInTime;
    }

    public String getInTradingUnitName() {
        return inTradingUnitName;
    }

    public void setInTradingUnitName(String inTradingUnitName) {
        this.inTradingUnitName = inTradingUnitName;
    }

    public Double getInWeight() {
        return inWeight;
    }

    public void setInWeight(Double inWeight) {
        this.inWeight = inWeight;
    }

    public String getInTradingUnit() {
        return inTradingUnit;
    }

    public void setInTradingUnit(String inTradingUnit) {
        this.inTradingUnit = inTradingUnit;
    }

    public Date getTime4() {
        return time4;
    }

    public void setTime4(Date time4) {
        this.time4 = time4;
    }

    public Date getTime5() {
        return time5;
    }

    public void setTime5(Date time5) {
        this.time5 = time5;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
