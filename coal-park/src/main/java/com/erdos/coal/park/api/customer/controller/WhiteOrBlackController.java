package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP司机黑白名单管理接口列表"
@RestController
@RequestMapping("/api/cus/whiteOrBlack")
public class WhiteOrBlackController extends BaseController {
    @Resource
    private IDriverPoolService driverPoolService;

    @InvokeLog(description = "查询司机 接口", printReturn = false) //日志
    @PostMapping(value = "/driver_list")
    public ServerResponse<EGridResult> driverListHandler(
            @RequestParam(value = "carNum", required = false) String carNum,   //"车牌号/姓名"
            @RequestParam(value = "whiteOrBlack", required = false) Integer whiteOrBlack,  //"黑白名单 0-黑名单,1-白名单"
            @RequestParam(value = "page", required = false) Integer page,      //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows       //"每页多少条"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
        //return driverPoolService.driverPoolListData(carNum, whiteOrBlack, page, rows);
    }

    @InvokeLog(description = "黑白名单添加 接口") //日志
    @PostMapping(value = "/whiteOrBlack_add")
    public ServerResponse<String> whiteOrBlackAddHandler(
            @RequestParam(value = "ids") String[] ids,                  //"司机池id"
            @RequestParam(value = "whiteOrBlack") Integer whiteOrBlack  //"黑白名单 0-黑名单,1-白名单"
    ) throws GlobalException {
        return driverPoolService.whiteOrBlackAdd(ids, whiteOrBlack);
    }

    @InvokeLog(description = "黑白名单移除 接口") //日志
    @PostMapping(value = "/whiteOrBlack_del")
    public ServerResponse<String> whiteOrBlackDelHandler(
            @RequestParam(value = "ids") String[] ids   //"司机池id"
    ) throws GlobalException {
        return driverPoolService.whiteOrBlackAdd(ids, null);
    }
}
