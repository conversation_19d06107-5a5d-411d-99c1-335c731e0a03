package com.erdos.coal.park.api.driver.pojo;

import com.erdos.coal.park.web.sys.pojo.CheckBoxData;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class DriverOrderData implements Serializable {

    private String oid;             //单号
    private String carNum;          //车牌号
    private Double price;           //单价
    private String tradeName;      //商品名称
    private String beginPoint;     //起点
    private String endPoint;        //终点
    private Double distance;          //总里程
    private Double tolls;           //预估过路费
    private Long orderDate;       //完成日期
    private Date createDate;        //接单时间
    private Integer finishTag;      //订单完成标识(0-下单成功,1-运输途中,2-订单完成)
    private Integer mold;          //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2
    private Integer delete;       //订单撤销标记
    private Integer check;    //订单撤销退款审核

    private Integer driverIsAgree;  //司机是否接受订单 - 0-司机还没有接受；1-司机接受订单；2-司机拒绝订单
    /* private String outBillCode;//出货企业订单编号
     private String inBillCode;  //收货企业订单编号*/

    private Integer appointment;   //是否显示预约按钮 //0-不显示预约，1-未预约，2-预约成功
    private String aSubCode;//需要预约的二级单位编号
    private String aSubName;//需要预约的二级单位名称
    private Long aStartTime;//预约的起始时间
    private Long aEndTime;//预约的结束时间

    //发货 或 收货 单位 checking到3-检票时，会有毛重和皮重参数要记录到订单中
    private String outGrossWeight;
    private String outTareWeight;
    private String inGrossWeight;
    private String inTareWeight;

    private Integer isPunchClock = 0; //车辆进场是否需要签到 0-不显示签到，1-未签到，2-签到成功
    private Integer isQuarantine = 0; //车辆进场是否需要防疫申报 0-不显示防疫申报，1-未提交防疫申报，2-已提交防疫申报
    private List<CheckBoxData> quarantineInfo;    //进场车辆防疫申报内容 健康码照片、行程卡照片、核酸检测照片、疫苗接种照片、体温值、体温照片
    private Integer isGrabNumber = 0;   //车辆是否抢号 0-不显示抢号，1-未抢号，2-已抢号

    private Integer isTransport;   //订单是否可运输：0-可运输，1-禁止运输

    private String callStartTime;   //企业叫号开始时间
    private String callEndTime;     //企业叫号结束时间（过期）

    private PoundList outPoundList;     //出库电子磅单
    private PoundList inPoundList;      //入库电子磅单
    private CoalTicket outCoalTicket;   //出库电子煤票
    private CoalTicket inCoalTicket;    //入库电子煤票
    private Integer tranStatus; //订单状态：0-未接单，1-已接单，2-发货入场，3-运输中，4-收货入场，5-订单完成，6-订单废除,7-司机拒绝订单

    private String outSubName;                      //发货单位 默认二级单位名称
    private String inSubName;                       //收货单位 默认二级单位名称
    private String outTradingUnitName;      //发货交易单位名称
    private String inTradingUnitName;       //收货交易单位名称

    private String outCusDesc;       //发货单位客商备注
    private String inCusDesc;       //收货单位客商备注

    private String cipcherText;     // 智慧能源-提煤单加密串

    private Integer[] isCost;     //判断订单是否需要支付装卸费 0-不需要，1-需要支付，2-需要补交;数组【0】发货单位，【1】收货单位
    private String outBizContractCode;
    private String inBizContractCode;

    // 2024-12-28 急加
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;

    private boolean isCancel;   // 司机历史订单，标记是否为作废订单（true-作废，false-未作废）

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public Long getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Long orderDate) {
        this.orderDate = orderDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getFinishTag() {
        return finishTag;
    }

    public void setFinishTag(Integer finishTag) {
        this.finishTag = finishTag;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    public Integer getCheck() {
        return check;
    }

    public void setCheck(Integer check) {
        this.check = check;
    }

    public Integer getDriverIsAgree() {
        return driverIsAgree;
    }

    public void setDriverIsAgree(Integer driverIsAgree) {
        this.driverIsAgree = driverIsAgree;
    }

    public Integer getAppointment() {
        return appointment;
    }

    public void setAppointment(Integer appointment) {
        this.appointment = appointment;
    }

    public String getaSubName() {
        return aSubName;
    }

    public void setaSubName(String aSubName) {
        this.aSubName = aSubName;
    }

    public String getaSubCode() {
        return aSubCode;
    }

    public void setaSubCode(String aSubCode) {
        this.aSubCode = aSubCode;
    }

    public Long getaStartTime() {
        return aStartTime;
    }

    public void setaStartTime(Long aStartTime) {
        this.aStartTime = aStartTime;
    }

    public Long getaEndTime() {
        return aEndTime;
    }

    public void setaEndTime(Long aEndTime) {
        this.aEndTime = aEndTime;
    }

    public Integer getIsPunchClock() {
        return isPunchClock;
    }

    public void setIsPunchClock(Integer isPunchClock) {
        this.isPunchClock = isPunchClock;
    }

    public Integer getIsQuarantine() {
        return isQuarantine;
    }

    public void setIsQuarantine(Integer isQuarantine) {
        this.isQuarantine = isQuarantine;
    }

    public List<CheckBoxData> getQuarantineInfo() {
        return quarantineInfo;
    }

    public void setQuarantineInfo(List<CheckBoxData> quarantineInfo) {
        this.quarantineInfo = quarantineInfo;
    }

    public Integer getIsGrabNumber() {
        return isGrabNumber;
    }

    public void setIsGrabNumber(Integer isGrabNumber) {
        this.isGrabNumber = isGrabNumber;
    }

    public String getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(String outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public String getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(String outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public String getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(String inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public String getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(String inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    public Integer getIsTransport() {
        return isTransport;
    }

    public void setIsTransport(Integer isTransport) {
        this.isTransport = isTransport;
    }

    public String getCallStartTime() {
        return callStartTime;
    }

    public void setCallStartTime(String callStartTime) {
        this.callStartTime = callStartTime;
    }

    public String getCallEndTime() {
        return callEndTime;
    }

    public void setCallEndTime(String callEndTime) {
        this.callEndTime = callEndTime;
    }

    public PoundList getOutPoundList() {
        return outPoundList;
    }

    public void setOutPoundList(PoundList outPoundList) {
        this.outPoundList = outPoundList;
    }

    public PoundList getInPoundList() {
        return inPoundList;
    }

    public void setInPoundList(PoundList inPoundList) {
        this.inPoundList = inPoundList;
    }

    public CoalTicket getOutCoalTicket() {
        return outCoalTicket;
    }

    public void setOutCoalTicket(CoalTicket outCoalTicket) {
        this.outCoalTicket = outCoalTicket;
    }

    public CoalTicket getInCoalTicket() {
        return inCoalTicket;
    }

    public void setInCoalTicket(CoalTicket inCoalTicket) {
        this.inCoalTicket = inCoalTicket;
    }

    public Integer getTranStatus() {
        return tranStatus;
    }

    public void setTranStatus(Integer tranStatus) {
        this.tranStatus = tranStatus;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getOutTradingUnitName() {
        return outTradingUnitName;
    }

    public void setOutTradingUnitName(String outTradingUnitName) {
        this.outTradingUnitName = outTradingUnitName;
    }

    public String getInTradingUnitName() {
        return inTradingUnitName;
    }

    public void setInTradingUnitName(String inTradingUnitName) {
        this.inTradingUnitName = inTradingUnitName;
    }

    public String getOutCusDesc() {
        return outCusDesc;
    }

    public void setOutCusDesc(String outCusDesc) {
        this.outCusDesc = outCusDesc;
    }

    public String getInCusDesc() {
        return inCusDesc;
    }

    public void setInCusDesc(String inCusDesc) {
        this.inCusDesc = inCusDesc;
    }

    public String getCipcherText() {
        return cipcherText;
    }

    public void setCipcherText(String cipcherText) {
        this.cipcherText = cipcherText;
    }

    public Integer[] getIsCost() {
        return isCost;
    }

    public void setIsCost(Integer[] isCost) {
        this.isCost = isCost;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public boolean getIsCancel() {
        return isCancel;
    }

    public void setIsCancel(boolean isCancel) {
        this.isCancel = isCancel;
    }
}

