package com.erdos.coal.park.api.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.business.dao.IBusGoodsDao;
import com.erdos.coal.park.api.business.entity.BusGoods;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.service.IBusGoodsService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.pojo.OrderInfoData;
import com.erdos.coal.park.api.customer.service.*;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IOrderRecordService;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.*;
import com.erdos.coal.utils.StrUtil;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.UpdateResult;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.mongodb.client.model.Filters.in;

@Service("busGoodsService")
public class BusGoodsServiceImpl extends BaseMongoServiceImpl<BusGoods, IBusGoodsDao> implements IBusGoodsService {

    @Resource
    private IUnitInfoService unitInfoService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private ICarDao carDao;
    @Resource
    private MongoClient client;
    @Resource
    private IOrderRecordService orderRecordService;
    @Resource
    private IGoodsService goodsService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private ISysSwitchService sysSwitchService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private ISysUnitAccountService sysUnitAccountService;
    @Resource
    private IPlatFormAccountService platFormAccountService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    //校验order的billCode，避免同一unitCode下出现min重复导致的billCode重复
    private String checkBillCode(String unitCode, Integer min, Integer billType) {
        String billCode = unitCode + billType + String.format("%09d", min);

        Query<Order> query = orderService.createQuery();
        if (billType == 5) {
            query.filter("inBillCode", billCode);
        } else if (billType == 6) {
            query.filter("outBillCode", billCode);
        }
        List<Order> list = query.find().toList();
        if (list.size() > 0) {
            logger.warn("下单执行校验方法checkBillCode时，" + billCode + "在order表中有重复了");
            return "票号错误";
        }
        return null;
    }

    private String checkDvrCarNum(DriverInfo driverInfo, String carNum) {
        //0.根据车牌查询司机信息,若司机信息不存在，则下单失败
        if (driverInfo == null) return "指定的车牌号司机不存在";
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return "指定的车牌号司机未通过审核";

        //1.判断车辆是否通过审核
        Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
        dtcQuery.filter("carNum", carNum);
        dtcQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
        if (driverToCar == null) return "指定的车号，司机还未关联！";
        if (driverToCar.getDelete() == 1) return "司机已删除车辆";
        if (driverToCar.getDelete() == 2) return "车号存在争议，平台已限制司机使用";
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return "车号参数错误,车辆不存在";
        if (car.getVerify() == 0 || car.getVerify() == 2) return "车辆审核不可用，请更换指定司机及车牌号";
        if (car.getVerify() == 4) return "车辆停用";

        //2.判断司机是否有未完成订单
        if (driverInfo.getHaveOrder() > 0) return "司机：" + driverInfo.getMobile() + "有未完成订单,下单失败";
        return null;
    }

    @Override
    @Transactional
    public ServerResponse<String> busPushGoods(String userCode, Integer min, Integer max, String carNum, String dvrMobile, Integer carType, BusGoods goods, String transportPlace) {
        //检查客商是否存在
        UnitInfo unitInfo = unitInfoService.get("userCode", userCode);
        if (unitInfo == null) return ServerResponse.createError("编号：" + userCode + "客商不存在");
        CustomerUser cUser = customerUserService.getByPK(unitInfo.getCid());
        Integer cusFee = new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue();
        //检查车牌号和司机是否可用
        String msg = "";
        checkBillCode(unitInfo.getUnitCode(), min, goods.getBillType());
        DriverInfo driverInfo = driverInfoService.get("mobile", dvrMobile);
        if (StrUtil.isNotEmpty(carNum)) msg = checkDvrCarNum(driverInfo, carNum);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //补充货运信息
        SysUnit unit = sysUnitService.get("code", unitInfo.getUnitCode());
        goods.setCid(unitInfo.getCid());
        goods.setBusGid(Utils.getUUID());
        goods.setUnitCode(unit.getCode());
        goods.setUnitName(unit.getName());
        goods.setTradeName(unit.getVariety());
        Goods g = new Goods();
        g.setFees3(cusFee);    //设置客商收费
        g.setTotal(max - min + 1);
        if (goods.getBillType() == 6) {
            goods.setMold(0);
            g.setOutUnitCode(unit.getCode());
            g.setOutDefaultDownUnit(goods.getTradesubcode());
        } else if (goods.getBillType() == 5) {
            goods.setMold(1);
            g.setInUnitCode(unit.getCode());
            g.setInDefaultDownUnit(goods.getTradesubcode());
        }

        //查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees2(g);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //事务添加订单
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> busGoodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            busGoodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = g.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setBusGid(goods.getBusGid());
                o.setOid(oid);
                o.setStatus(0);
                o.setTradeName(goods.getTradeName());
                o.setMold(goods.getMold());
                o.setIsWeChat(0);
                o.setShare(0);
                //5，采购-入库。6，销售-出库
                if (goods.getBillType() == 6) {
                    o.setOutMinMax(String.valueOf(min + i));
                    o.setOutBillCode(g.getOutUnitCode() + "6" + String.format("%09d", (min + i)));
                    o.setOutChecking(0);
                    o.setOutUnitCode(unit.getCode());
                    o.setOutUnitName(unit.getName());
                    o.setOutBizContractCode(goods.getBizcontractcode());
                    o.setOutBizContractName(goods.getBizcontractname());
                    o.setOutVariety(goods.getProductname());
                    o.setOutDefaultDownUnit(goods.getTradesubcode());
                    o.setOutSubName(goods.getSubname());
                    o.setOutDefaultArea(goods.getDefaultarea());
                    o.setOutAreaName(goods.getAreaname());
                } else if (goods.getBillType() == 5) {
                    o.setInMinMax(String.valueOf(min + i));
                    o.setInBillCode(g.getInUnitCode() + "5" + String.format("%09d", (min + i)));
                    o.setInChecking(0);
                    o.setInUnitCode(unit.getCode());
                    o.setInUnitName(unit.getName());
                    o.setInBizContractCode(goods.getBizcontractcode());
                    o.setInBizContractName(goods.getBizcontractname());
                    o.setInVariety(goods.getProductname());
                    o.setInDefaultDownUnit(goods.getTradesubcode());
                    o.setInSubName(goods.getSubname());
                    o.setInDefaultArea(goods.getDefaultarea());
                    o.setInAreaName(goods.getAreaname());
                }
                o.setDelete(false);
                //o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setCid(goods.getCid());
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(carNum)) {
                    o.setCarNum(carNum);
                    o.setDid(driverInfo.getObjectId().toHexString());
                }

                addOrderFees2(g, o);

                if (StrUtil.isNotEmpty(transportPlace)) o.setPlace(transportPlace);
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("busPushGoods" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("下单成功");
    }

    @Override
    @Transactional
    public ServerResponse<String> busPushGoods2(String userCode, Integer min, Integer max, String carNum, String dvrMobile, Integer carType, BusGoods goods, String transportPlace) {
        //检查客商是否存在
        UnitInfo unitInfo = unitInfoService.get("userCode", userCode);
        if (unitInfo == null) return ServerResponse.createError("编号：" + userCode + "客商不存在");
        CustomerUser cUser = customerUserService.getByPK(unitInfo.getCid());
        Integer cusFee = new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue();
        //检查车牌号和司机是否可用
        String msg = "";
        checkBillCode(unitInfo.getUnitCode(), min, goods.getBillType());
        DriverInfo driverInfo = driverInfoService.get("mobile", dvrMobile);
        if (StrUtil.isNotEmpty(carNum)) msg = checkDvrCarNum(driverInfo, carNum);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //补充货运信息
        SysUnit unit = sysUnitService.get("code", unitInfo.getUnitCode());
        goods.setCid(unitInfo.getCid());
        String busGid = Utils.getUUID();
        goods.setBusGid(busGid);
        goods.setUnitCode(unit.getCode());
        goods.setUnitName(unit.getName());
        goods.setTradeName(unit.getVariety());
        Goods g = new Goods();
        g.setFees3(cusFee);    //设置客商收费
        g.setTotal(max - min + 1);
        if (goods.getBillType() == 6) {
            goods.setMold(0);
            g.setOutUnitCode(unit.getCode());
            g.setOutDefaultDownUnit(goods.getTradesubcode());
            g.setOutVarietyCode(goods.getOutVarietyCode());
        } else if (goods.getBillType() == 5) {
            goods.setMold(1);
            g.setInUnitCode(unit.getCode());
            g.setInDefaultDownUnit(goods.getTradesubcode());
        }

        //查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees2(g);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //事务添加订单
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> busGoodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            busGoodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = g.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setBusGid(goods.getBusGid());
                o.setOid(oid);
                o.setStatus(0);
                o.setTradeName(goods.getTradeName());
                o.setMold(goods.getMold());
                o.setIsWeChat(0);
                o.setShare(0);
                //5，采购-入库。6，销售-出库
                if (goods.getBillType() == 6) {
                    o.setOutMinMax(String.valueOf(min + i));
                    o.setOutBillCode(g.getOutUnitCode() + "6" + String.format("%09d", (min + i)));
                    o.setOutChecking(0);
                    o.setOutUnitCode(unit.getCode());
                    o.setOutUnitName(unit.getName());
                    o.setOutBizContractCode(goods.getBizcontractcode());
                    o.setOutBizContractName(goods.getBizcontractname());
                    o.setOutVariety(goods.getProductname());
                    o.setOutDefaultDownUnit(goods.getTradesubcode());
                    o.setOutSubName(goods.getSubname());
                    o.setOutDefaultArea(goods.getDefaultarea());
                    o.setOutAreaName(goods.getAreaname());
                    o.setOutTradingUnit(goods.getTradingUnit());
                    o.setOutTradingUnitName(goods.getTradingUnitName());
                    o.setOutVarietyCode(g.getOutVarietyCode());
                } else if (goods.getBillType() == 5) {
                    o.setInMinMax(String.valueOf(min + i));
                    o.setInBillCode(g.getInUnitCode() + "5" + String.format("%09d", (min + i)));
                    o.setInChecking(0);
                    o.setInUnitCode(unit.getCode());
                    o.setInUnitName(unit.getName());
                    o.setInBizContractCode(goods.getBizcontractcode());
                    o.setInBizContractName(goods.getBizcontractname());
                    o.setInVariety(goods.getProductname());
                    o.setInDefaultDownUnit(goods.getTradesubcode());
                    o.setInSubName(goods.getSubname());
                    o.setInDefaultArea(goods.getDefaultarea());
                    o.setInAreaName(goods.getAreaname());
                    o.setInTradingUnit(goods.getTradingUnit());
                    o.setInTradingUnitName(goods.getTradingUnitName());
                }
                o.setDelete(false);
                //o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setCid(goods.getCid());
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(carNum)) {
                    o.setCarNum(carNum);
                    o.setDid(driverInfo.getObjectId().toHexString());
                }

                if (StrUtil.isNotEmpty(goods.getRemark1())) o.setRemark1(goods.getRemark1());
                if (StrUtil.isNotEmpty(goods.getRemark2())) o.setRemark2(goods.getRemark2());
                if (StrUtil.isNotEmpty(goods.getRemark3())) o.setRemark3(goods.getRemark3());
                if (StrUtil.isNotEmpty(goods.getRemark4())) o.setRemark4(goods.getRemark4());

                addOrderFees2(g, o);

                if (StrUtil.isNotEmpty(transportPlace)) o.setPlace(transportPlace);
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("busPushGoods2" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("下单成功", busGid);
    }

    @Override
    @Transactional
    public ServerResponse<String> busPushGoods3(String userCode, Integer min, Integer max, String carNum, String dvrMobile, Integer carType, BusGoods goods, String transportPlace) {
        //检查客商是否存在
        UnitInfo unitInfo = unitInfoService.get("userCode", userCode);
        if (unitInfo == null) return ServerResponse.createError("编号：" + userCode + "客商不存在");
        CustomerUser cUser = customerUserService.getByPK(unitInfo.getCid());
        Integer cusFee = new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue();
        //检查车牌号和司机是否可用
        String msg = "";
        checkBillCode(unitInfo.getUnitCode(), min, goods.getBillType());
        DriverInfo driverInfo = driverInfoService.get("mobile", dvrMobile);
        if (StrUtil.isNotEmpty(carNum)) msg = checkDvrCarNum(driverInfo, carNum);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //补充货运信息
        SysUnit unit = sysUnitService.get("code", unitInfo.getUnitCode());
        goods.setCid(unitInfo.getCid());
        String busGid = Utils.getUUID();
        goods.setBusGid(busGid);
        goods.setUnitCode(unit.getCode());
        goods.setUnitName(unit.getName());
        goods.setTradeName(unit.getVariety());
        Goods g = new Goods();
        g.setFees3(cusFee);    //设置客商收费
        g.setTotal(max - min + 1);
        if (goods.getBillType() == 6) {
            goods.setMold(0);
            g.setOutUnitCode(unit.getCode());
            g.setOutDefaultDownUnit(goods.getTradesubcode());
            g.setOutVarietyCode(goods.getOutVarietyCode());
        } else if (goods.getBillType() == 5) {
            goods.setMold(1);
            g.setInUnitCode(unit.getCode());
            g.setInDefaultDownUnit(goods.getTradesubcode());
        }

        //查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees3(g);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //事务添加订单
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> busGoodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            busGoodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = g.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setBusGid(goods.getBusGid());
                o.setOid(oid);
                o.setStatus(0);
                o.setTradeName(goods.getTradeName());
                o.setMold(goods.getMold());
                o.setIsWeChat(0);
                o.setShare(0);
                //5，采购-入库。6，销售-出库
                if (goods.getBillType() == 6) {
                    o.setOutMinMax(String.valueOf(min + i));
                    o.setOutBillCode(g.getOutUnitCode() + "6" + String.format("%09d", (min + i)));
                    o.setOutChecking(0);
                    o.setOutUnitCode(unit.getCode());
                    o.setOutUnitName(unit.getName());
                    o.setOutBizContractCode(goods.getBizcontractcode());
                    o.setOutBizContractName(goods.getBizcontractname());
                    o.setOutVariety(goods.getProductname());
                    o.setOutDefaultDownUnit(goods.getTradesubcode());
                    o.setOutSubName(goods.getSubname());
                    o.setOutDefaultArea(goods.getDefaultarea());
                    o.setOutAreaName(goods.getAreaname());
                    o.setOutTradingUnit(goods.getTradingUnit());
                    o.setOutTradingUnitName(goods.getTradingUnitName());
                    o.setOutVarietyCode(g.getOutVarietyCode());
                } else if (goods.getBillType() == 5) {
                    o.setInMinMax(String.valueOf(min + i));
                    o.setInBillCode(g.getInUnitCode() + "5" + String.format("%09d", (min + i)));
                    o.setInChecking(0);
                    o.setInUnitCode(unit.getCode());
                    o.setInUnitName(unit.getName());
                    o.setInBizContractCode(goods.getBizcontractcode());
                    o.setInBizContractName(goods.getBizcontractname());
                    o.setInVariety(goods.getProductname());
                    o.setInDefaultDownUnit(goods.getTradesubcode());
                    o.setInSubName(goods.getSubname());
                    o.setInDefaultArea(goods.getDefaultarea());
                    o.setInAreaName(goods.getAreaname());
                    o.setInTradingUnit(goods.getTradingUnit());
                    o.setInTradingUnitName(goods.getTradingUnitName());
                }
                o.setDelete(false);
                //o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setCid(goods.getCid());
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(carNum)) {
                    o.setCarNum(carNum);
                    o.setDid(driverInfo.getObjectId().toHexString());
                }

                if (StrUtil.isNotEmpty(goods.getRemark1())) o.setRemark1(goods.getRemark1());
                if (StrUtil.isNotEmpty(goods.getRemark2())) o.setRemark2(goods.getRemark2());
                if (StrUtil.isNotEmpty(goods.getRemark3())) o.setRemark3(goods.getRemark3());
                if (StrUtil.isNotEmpty(goods.getRemark4())) o.setRemark4(goods.getRemark4());

                addOrderFees2(g, o);

                if (StrUtil.isNotEmpty(transportPlace)) o.setPlace(transportPlace);
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("busPushGoods3" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("下单成功", busGid);
    }

    //订单order，在企业或客商代付的情况下，添加费用等信息(计费)
    private void addOrderFees2(Goods goods, Order order) {
        //订单计费，计为0的表示不收费
        order.setOutFee0(goods.getFeesOut());
        order.setInFee0(goods.getFeesIn());
        order.setOutFee1(goods.getFees1Out());
        order.setInFee1(goods.getFees1In());
        order.setOutFee2(goods.getFees2Out());
        order.setInFee2(goods.getFees2In());
        //客商收费，分成两个字段，实际存的值相同，即司机付费时只付一个就行。
        order.setOutFee3(goods.getFees3());
        order.setInFee3(0);
        //友商和客商只能有一人收费
        order.setOutFee4(0);
        order.setInFee4(0);
        order.setOutFee5(StrUtil.isNotEmpty(goods.getFee5Out()) ? goods.getFee5Out() : 0);    //第三方费用
        order.setInFee5(StrUtil.isNotEmpty(goods.getFee5In()) ? goods.getFee5In() : 0);
        order.setOutFee6(0);    //司机服务费
        order.setInFee6(0);
        order.setOutFee7(0);    //司机特权费
        order.setInFee7(0);
        order.setOutCount6(0);  //司机服务次数
        order.setInCount6(0);
        order.setOutCount7(0);  //司机特权次数
        order.setInCount7(0);
    }

    private List<String> billCodeList(String startCode, String endCode, String billCodeList) {
        List<String> lists;
        if (StrUtil.isNotEmpty(billCodeList)) {
            billCodeList = billCodeList.replace("[", "");
            billCodeList = billCodeList.replace("]", "");
            billCodeList = billCodeList.replace(" ", "");
            String[] billCodes = billCodeList.split(",");
            lists = new ArrayList<>(Arrays.asList(billCodes));
        } else {
            lists = new ArrayList<>();
            String startPrefix = startCode.substring(0, startCode.length() - 9);
            int min = Integer.parseInt(startCode.substring(startCode.length() - 9));
            int max = Integer.parseInt(endCode.substring(endCode.length() - 9));
            for (int i = min; i <= max; i++) {
                lists.add(startPrefix + String.format("%09d", i));
            }
        }

        return lists;
    }

    @Override
    public ServerResponse<List<String>> updateBusGoods(String startCode, String endCode, Integer billType, String variety, String billCodeList, String transportPlace) {
        List<String> billCodes = billCodeList(startCode, endCode, billCodeList);

        Query<Order> oQuery = orderService.createQuery();
        oQuery.criteria("mold").notEqual(2);    //企业单只有单发货 或 单收货模式
        if (billType == 5) {
            oQuery.filter("inChecking", 0);
            oQuery.filter("inBillCode in", billCodes.toArray());
        } else if (billType == 6) {
            oQuery.filter("outChecking", 0);
            oQuery.filter("outBillCode in", billCodes.toArray());
        }
        oQuery.filter("delete", false);
        List<Order> orders = orderService.list(oQuery);
        if (orders == null || orders.size() <= 0) return ServerResponse.createError("订单不存在");

        //将要修改的订单设置为不可用状态，防止接单
        orderService.update(oQuery, orderService.createUpdateOperations().set("status", 1)); //要修改的订单置为不可用状态

        //todo:开始修改
        UpdateOperations<Order> oUpdateOperations = orderService.createUpdateOperations();
        if (billType == 6) oUpdateOperations.set("outVariety", variety);
        if (billType == 5) oUpdateOperations.set("inVariety", variety);
        if (StrUtil.isNotEmpty(transportPlace)) oUpdateOperations.set("place", transportPlace);
        oUpdateOperations.set("status", 0);
        orderService.update(oQuery, oUpdateOperations);

        //todo:货运信息的variety 保持和 第一条order的variety 相同
        oQuery.order(Sort.ascending("gid"), Sort.ascending("createTime"));
        List<Order> afterUpOrders = orderService.list(oQuery);
        String gid = "";
        String busGid = "";
        for (Order o : afterUpOrders) {
            if (StrUtil.isEmpty(busGid)) busGid = o.getBusGid();

            if (StrUtil.isEmpty(o.getGid()) || gid.equals(o.getGid())) continue;
            gid = o.getGid();

            UpdateOperations<Goods> gUpdateOperations = goodsService.createUpdateOperations();
            gUpdateOperations.set("outVariety", o.getOutVariety() == null ? "" : o.getOutVariety());
            gUpdateOperations.set("inVariety", o.getInVariety() == null ? "" : o.getInVariety());
            goodsService.update(goodsService.createQuery().filter("gid", gid), gUpdateOperations);
        }

        //修改企业单productname
        Query<BusGoods> busQuery = this.createQuery();
        busQuery.criteria("busGid").equal(busGid);
        UpdateOperations<BusGoods> busUpdate = this.createUpdateOperations();
        busUpdate.set("productname", variety);
        this.update(busQuery, busUpdate);

        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<List<String>> delBusGoods(String startCode, String endCode, Integer billType, String billCodeList) {

        List<String> billCodes = billCodeList(startCode, endCode, billCodeList);
        Query<Order> oQuery = orderService.createQuery();
        if (billType == 5) {
            oQuery.filter("inBillCode in", billCodes.toArray());
            oQuery.filter("inChecking", 0);
        } else if (billType == 6) {
            oQuery.filter("outBillCode in", billCodes.toArray());
            oQuery.filter("outChecking", 0);
        }
        oQuery.criteria("tranStatus").lessThanOrEq(1);
        oQuery.order(Sort.ascending("busGid"), Sort.ascending("gid"));
        List<Order> orders = orderService.list(oQuery);
        if (orders == null || orders.size() <= 0) return ServerResponse.createSuccess("删除成功0");

        String tempGid = "";
        String tempBusGid = "";
        int delNum = 0;
        int orderNum0 = 0;
        int orderNum1 = 0;
        int busDelNum = 0;       //企业单废除的单数
        int busDelNum1 = 0;      //企业单未拆分作废单数
        List<String> pushOids = new ArrayList<>();
        List<String> carNums = new ArrayList<>();  //需要司机统一撤单的 司机的车牌号
        List<String> delOids = new ArrayList<>();
        List<String> delBillCodes = new ArrayList<>();
        List<Order> delOrders = new ArrayList<>();  //司机接单了，可能需要退费的订单
        List<ObjectId> dvrIds = new ArrayList<>();    //接单了的司机编号集合
        Map<String, String> gMap = new HashMap<>();  //已经拆分出的货运信息，要修改删除车数、未接单车数、接单车数
        Map<String, String> busGoodsDelMap = new HashMap<>();  //作废的企业单编号 和 作废的车数
        for (Order o : orders) {
            if (StrUtil.isEmpty(tempBusGid)) tempBusGid = o.getBusGid();
            if (!tempBusGid.equals(o.getBusGid())) {
                busGoodsDelMap.put(tempBusGid, busDelNum + "," + busDelNum1);
                busDelNum = 0;
                busDelNum1 = 0;
                tempBusGid = o.getBusGid();
            }
            busDelNum++;
            if (StrUtil.isEmpty(o.getGid())) busDelNum1++;

            if (StrUtil.isEmpty(o.getGid())) {       //客商还未拆分的订单可直接删除
                delOids.add(o.getOid());
                if (o.getMold() == 0) {
                    delBillCodes.add(o.getOutBillCode());
                } else if (o.getMold() == 1) {
                    delBillCodes.add(o.getInBillCode());
                }
                continue;
            }

            delOids.add(o.getOid());
            if (o.getMold() == 0) {
                delBillCodes.add(o.getOutBillCode());
            } else if (o.getMold() == 1) {
                delBillCodes.add(o.getInBillCode());
            }
            if (StrUtil.isEmpty(tempGid)) tempGid = o.getGid();
            if (o.getTranStatus() == 0) {       //司机还未接单的订单可直接删除
                if (!tempGid.equals(o.getGid())) {
                    gMap.put(tempGid, delNum + "," + orderNum0 + "," + orderNum1);
                    tempGid = o.getGid();
                    delNum = 0;
                    orderNum0 = 0;
                    orderNum1 = 0;
                }
                delNum++;
                orderNum0++;
            } else if (o.getTranStatus() == 1) {            //司机接单了，还未入场的，删除订单,退费,修改司机接单状态，并推送消息给司机
                //司机接过的订单，不在运输/未进场 的订单，加入到推送订单编号集合中。
                pushOids.add(o.getOid());
                carNums.add(o.getCarNum());
                delOrders.add(o);
                dvrIds.add(new ObjectId(o.getDid()));

                if (!tempGid.equals(o.getGid())) {
                    gMap.put(tempGid, delNum + "," + orderNum0 + "," + orderNum1);
                    tempGid = o.getGid();
                    delNum = 0;
                    orderNum0 = 0;
                    orderNum1 = 0;
                }
                delNum++;
                orderNum1++;
            }
        }

        if (StrUtil.isNotEmpty(tempBusGid)) busGoodsDelMap.put(tempBusGid, busDelNum + "," + busDelNum1);
        if (StrUtil.isNotEmpty(tempGid)) gMap.put(tempGid, delNum + "," + orderNum0 + "," + orderNum1);

        //废除订单
        if (delOids.size() > 0) {

            SysUnit sysUnit1 = null;
            SysUnit sysUnit2 = null;
            if (billType == 5) {
                sysUnit1 = sysUnitService.get("code", orders.get(0).getInUnitCode());
                sysUnit2 = sysUnitService.get("code", orders.get(0).getInDefaultDownUnit());
            } else if (billType == 6) {
                sysUnit1 = sysUnitService.get("code", orders.get(0).getOutUnitCode());
                sysUnit2 = sysUnitService.get("code", orders.get(0).getOutDefaultDownUnit());
            }
            Query<SysSwitch> query = sysSwitchService.createQuery();
            query.filter("code", 3); //订单
            SysSwitch sysSwitch = sysSwitchService.get(query);
            boolean pfIsRefund = sysSwitch == null || StrUtil.isEmpty(sysSwitch.getIsRefund()) || sysSwitch.getIsRefund() == 0;
            boolean sysIsRefund = true; //true-表示撤单退款
            if (StrUtil.isNotEmpty(sysUnit1) && StrUtil.isNotEmpty(sysUnit1.getIsDelOrderBackFee()) && sysUnit1.getIsDelOrderBackFee() == 1)
                sysIsRefund = false;    //false-表示撤单不退款

            boolean isUpOrder = delOrder(delOids, delOrders, pfIsRefund, pfIsRefund, sysUnit1, sysUnit2, sysIsRefund, sysIsRefund, dvrIds, gMap, busGoodsDelMap);

            if (!isUpOrder) return ServerResponse.createError("稍后重试");
        }
        //推送订单已废除的消息给司机
        if (pushOids.size() > 0) orderRecordService.deleteOrder(pushOids);

        return ServerResponse.createSuccess("货运信息下未运输的订单废除成功,共废除" + delNum + "车,其中" + pushOids.size() + "车需要司机同意废除,司机们的车牌号为：" + carNums, delBillCodes);
    }

    /**
     * 企业撤单
     * 1.客商未拆分的订单直接删除，修改order delete。
     * 2.客商拆分但司机未接单直接删除，修改order delete。
     * 3.客商拆分且司机接单，删除同时退回费用。
     * 4.修改goods各车数
     * 5.driverInfo接单字段
     */
    @Transactional
    public boolean delOrder(List<String> delOids, List<Order> delOrders, boolean pfIsRefundOut, boolean pfIsRefundIn, SysUnit sysUnit1, SysUnit sysUnit2, boolean sysIsRefundOut, boolean sysIsRefundIn, List<ObjectId> dvrIds, Map<String, String> gMap, Map<String, String> busGoodsMap) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();
        MongoCollection<Document> busGoodsCollection = this.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();
            //修改要废除的order delete为true
            Map<String, Object> oMap = new HashMap<>();
            oMap.put("updateTime", time.getTime());
            oMap.put("delete", true);
            oMap.put("delType", 0); //客商撤单
            oMap.put("isCheck", 3); // 无需审核
            oMap.put("tranStatus", 6);
            oMap.put("time6", time);//订单作废时间
            Map<String, Object> oUpMap = new HashMap<>();
            oUpMap.put("$set", oMap);
            UpdateResult upResult = orderCollection.updateMany(clientSession, in("oid", delOids), Document.parse(JSONObject.toJSONString(oUpMap)));
            if (upResult.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return false;
            }
            //删除orderTaking
            otCollection.deleteMany(clientSession, in("oid", delOids));

            //订单退款不需要审核，平台会配置撤单是否退费、收发货单位会配置撤单是否退费，
            List<Document> dvrAccountDocs = new ArrayList<>();
            List<Document> cusAccountDocs = new ArrayList<>();
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            List<Document> pfAccountDocs = new ArrayList<>();
            List<Document> thirdPartyAccountDocs = new ArrayList<>();
            for (Order order : delOrders) {
                //存在三方分账的情况时，不退费
                if ((StrUtil.isNotEmpty(order.getOutFee5()) && order.getOutFee5() > 0) || (StrUtil.isNotEmpty(order.getInFee5()) && order.getInFee5() > 0))
                    continue;

                int dvrFee = 0; //订单中需要退还司机的总金额
                //平台信息费 退回，同时平台账户金额核减
                if (pfIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer1())) { //平台收取发货单位的信息费，符合条件则退回
                    String outPayer1 = order.getOutPayer1();
                    int outFee0 = order.getOutFee0();
                    switch (order.getOutPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer1, new BigDecimal(outFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 12, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + outFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 13, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(outPayer1, null, 3, order.getOid(), outFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                if (pfIsRefundIn && StrUtil.isNotEmpty(order.getInPayer1())) { //平台收取收货单位的信息费，符合条件则退回
                    String inPayer1 = order.getInPayer1();
                    int inFee0 = order.getInFee0();
                    switch (order.getInPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer1, new BigDecimal(inFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + inFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(inPayer1, null, 3, order.getOid(), inFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                //单位收取的代扣费 退回，同时单位账户金额核减
                if (sysIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer2())) {   //发货单位收取的代扣费，符合条件则退回
                    String outPayer2 = order.getOutPayer2();
                    int outFee1 = order.getOutFee1();
                    int outFee2 = order.getOutFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer2, new BigDecimal(outFee1 + outFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + outFee1 + outFee2;
                            break;
                        default:
                            break;
                    }

                    if (outFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnit1.getObjectId().toHexString(), null, 5, order.getOid(), -outFee1, time));
                    if (outFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnit2.getObjectId().toHexString(), null, 5, order.getOid(), -outFee2, time));
                }
                if (sysIsRefundIn && StrUtil.isNotEmpty(order.getInPayer2())) {   //收货单位收取的代扣费，符合条件则退回
                    String inPayer2 = order.getInPayer2();
                    int inFee1 = order.getInFee1();
                    int inFee2 = order.getInFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer2, new BigDecimal(inFee1 + inFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + inFee1 + inFee2;
                            break;
                        default:
                            break;
                    }

                    if (inFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnit1.getObjectId().toHexString(), null, 5, order.getOid(), -inFee1, time));
                    if (inFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnit2.getObjectId().toHexString(), null, 5, order.getOid(), -inFee2, time));
                }
                //客商或友商收取的信息费和第三方费用 退回司机,同时客商账户和第三方账户金额核减
                if (StrUtil.isNotEmpty(order.getOutPayer3()) || StrUtil.isNotEmpty(order.getInPayer3())) {
                    int cusFee = order.getOutFee3() + order.getOutFee4();
                    int outFee5 = order.getOutFee5();
                    int inFee5 = order.getInFee5();

                    dvrFee = dvrFee + cusFee + outFee5 + inFee5;
                    if (cusFee > 0) {
                        String cid = order.getShare() == 2 ? order.getShareCid() : order.getCid();
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(-cusFee), 9, order.getOid(), time));
                    }
//                    if (outFee5 > 0)
//                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(sysUnit1.getThirdPartyAccount(), null, -outFee5, 1, order.getOid(), time));
//                    if (inFee5 > 0)
//                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(sysUnit1.getThirdPartyAccount(), null, -inFee5, 1, order.getOid(), time));
                }
                if (dvrFee > 0)
                    dvrAccountDocs.add(driverAccountService.createDriAccountDoc(order.getDid(), null, new BigDecimal(dvrFee), 3, order.getOid(), time));

            }
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
            if (thirdPartyAccountDocs.size() > 0) thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);

            for (String key : gMap.keySet()) {
                if (StrUtil.isEmpty(key)) continue;
                String numStr = gMap.get(key);
                String[] nums = numStr.split(",");
                Integer delNum = Integer.valueOf(nums[0]);
                Integer orderNum0 = Integer.valueOf(nums[1]);
                Integer orderNum1 = Integer.valueOf(nums[2]);

                //修改货运信息
                Map<String, Object> param = new HashMap<>();
                param.put("delNum", delNum);
                param.put("orderNum0", -orderNum0);
                param.put("orderNum1", -orderNum1);
                Map<String, Object> updMap = new HashMap<>();
                updMap.put("$inc", param);
                gCollection.updateOne(clientSession, new Document("gid", key), Document.parse(JSONObject.toJSONString(updMap)));
            }
            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", paramMap);
            driverCollection.updateMany(clientSession, in("_id", dvrIds), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            //修改企业单数量
            for (String key : busGoodsMap.keySet()) {
                if (StrUtil.isEmpty(key)) continue;
                String numStr = busGoodsMap.get(key);
                String[] nums = numStr.split(",");
                Integer busDelNum = Integer.valueOf(nums[0]);
                Integer busDelNum1 = Integer.valueOf(nums[1]);
                Map<String, Object> param = new HashMap<>();
                param.put("delNum", busDelNum);         //作废单数累加
                param.put("availableNum", -busDelNum1); //可用单数累减
                Map<String, Object> updMap = new HashMap<>();
                updMap.put("$inc", param);
                busGoodsCollection.updateOne(clientSession, new Document("busGid", key), Document.parse(JSONObject.toJSONString(updMap)));
            }

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("busGoodsServiceImpl_delOrder" + e.getMessage());
            clientSession.abortTransaction();
            return false;
        } finally {
            clientSession.close();
        }
        return true;
    }

    @Override
    public ServerResponse<EGridResult> searchBusGoods(String unitCode, Integer mold, String busGid, Integer type, Integer rows, Integer page) {
        String cid = ShiroUtils.getUserId();
        Query<BusGoods> query = this.createQuery();
        query.filter("cid", cid);
        if (StrUtil.isNotEmpty(unitCode)) query.filter("unitCode", unitCode);
        if (StrUtil.isNotEmpty(mold)) query.filter("mold", mold);
        if (StrUtil.isNotEmpty(busGid)) query.filter("busGid", busGid);
        query.order(Sort.descending("objectId"));
        EGridResult<BusGoods> eGridResult = this.findPage(page, rows, query);

        return ServerResponse.createSuccess("查询成功", eGridResult);
    }

    @Override
    public ServerResponse<EGridResult> searchBusGoods2(String unitCode, Integer mold, String busGid, Integer type, Integer rows, Integer page) {
        String cid = ShiroUtils.getUserId();
        Query<BusGoods> query = this.createQuery();
        query.filter("cid", cid);

        if (StrUtil.isNotEmpty(type) && type == 0) {
            query.criteria("availableNum").greaterThan(0);
        } else if (StrUtil.isNotEmpty(type) && type == 1) {
            query.criteria("availableNum").equal(0);
        }
        if (StrUtil.isNotEmpty(unitCode)) query.filter("unitCode", unitCode);
        if (StrUtil.isNotEmpty(mold)) query.filter("mold", mold);
        if (StrUtil.isNotEmpty(busGid)) query.filter("busGid", busGid);
        query.order(Sort.descending("objectId"));
        EGridResult<BusGoods> eGridResult = this.findPage(page, rows, query);

        return ServerResponse.createSuccess("查询成功", eGridResult);
    }

    @Override
    public ServerResponse<Object> useBusGoods(String busGid, Goods goods, String cusDesc) {
        BusGoods busGoods = this.get("busGid", busGid);
        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        if (busGoods.getAvailableNum() < goods.getTotal()) return ServerResponse.createError("可用车数不足");

        //todo:若指定司机车号，则需要校验司机和车辆信息
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(goods.getCarNum())) {
            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", goods.getCarNum());
            dtcQuery.filter("driverId", goods.getDriverId());
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(goods.getDriverId());
            if (driverInfo == null) return ServerResponse.createError("指定的车牌号司机不存在");
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
                return ServerResponse.createError("指定的车牌号司机未通过审核");

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
            /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
            dpQuery.filter("cid", cid);
            dpQuery.filter("did", driverInfo.getObjectId().toString());
            DriverPool dp = driverPoolService.get(dpQuery);
            if (dp == null) {
                dp = new DriverPool();
                dp.setCid(cid);
                dp.setDid(driverInfo.getObjectId().toString());
                dp.setCustomerUser(cUser);
                dp.setDriverInfo(driverInfo);
                driverPoolService.save(dp);
            } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                return ServerResponse.createError("指定车牌号司机在黑名单中");
            }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        if (busGoods.getMold() == 0) {
            goods.setOutUnitName(busGoods.getUnitName());
            goods.setOutUnitCode(busGoods.getUnitCode());
            goods.setOutBizContractName(busGoods.getBizcontractname());
            goods.setOutBizContractCode(busGoods.getBizcontractcode());
            goods.setOutVariety(busGoods.getProductname());
            goods.setOutSubName(busGoods.getSubname());
            goods.setOutDefaultDownUnit(busGoods.getTradesubcode());
            goods.setOutAreaName(busGoods.getAreaname());
            goods.setOutDefaultArea(busGoods.getDefaultarea());
            goods.setOutMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setOutMax(busGoods.getEndNum());
            goods.setOutTradingUnit(busGoods.getTradingUnit());
            goods.setOutTradingUnitName(busGoods.getTradingUnitName());
            goods.setOutCusDesc(cusDesc);
        } else {
            goods.setInUnitName(busGoods.getUnitName());
            goods.setInUnitCode(busGoods.getUnitCode());
            goods.setInBizContractName(busGoods.getBizcontractname());
            goods.setInBizContractCode(busGoods.getBizcontractcode());
            goods.setInVariety(busGoods.getProductname());
            goods.setInSubName(busGoods.getSubname());
            goods.setInDefaultDownUnit(busGoods.getTradesubcode());
            goods.setInAreaName(busGoods.getAreaname());
            goods.setInDefaultArea(busGoods.getDefaultarea());
            goods.setInMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setInMax(busGoods.getEndNum());
            goods.setInTradingUnit(busGoods.getTradingUnit());
            goods.setInTradingUnitName(busGoods.getTradingUnitName());
            goods.setInCusDesc(cusDesc);
        }
        goods.setTradeName(busGoods.getTradeName());
        goods.setMold(busGoods.getMold());

        return addGoods3(goods, goods.getCarNum(), driverInfo, goods.getIsPay(), busGid);
    }

    public ServerResponse<Object> addGoods3(Goods goods, String designCarNum, DriverInfo driverInfo, Integer isPay, String busGid) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());
        goods.setStatus(0);

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees2(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder3(designCarNum, goods, 1, driverInfo, busGid);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder3(designCarNum, goods, 0, driverInfo, busGid);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Transactional
    Goods addGoodsAndOrder3(String designCarNum, Goods goods, Integer isHand, DriverInfo driverInfo, String busGid) {
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = goodsService.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> busGoodsCollection = this.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            //TODO:0.修改企业单使用单数
            Map<String, Object> busGoodsUpMap = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            busGoodsUpMap.put("useNum", goods.getTotal());
            busGoodsUpMap.put("availableNum", -goods.getTotal());
            upMap.put("$inc", busGoodsUpMap);
            busGoodsCollection.updateOne(clientSession, new Document("busGid", busGid), Document.parse(JSONObject.toJSONString(upMap)));

            //TODO:1.新增Goods
            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:2.根据车数 total 修改相应数量的订单Order
            int total = goods.getTotal();
            List<String> oids = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            //查询企业单下可用的订单
            Query<Order> oQuery = orderService.createQuery();
            oQuery.criteria("busGid").equal(busGid);
            oQuery.criteria("gid").equal(null);         //未拆分
            oQuery.criteria("delete").equal(false);     //未废除
            oQuery.limit(total);
            oQuery.order(Sort.ascending("objectId"));
            List<Order> orders = orderService.list(oQuery);   //企业单下可用的订单集合
            if (orders.size() < total) {
                clientSession.abortTransaction();
                return null;
            }
            for (Order o : orders) {
                oids.add(o.getOid());

                paramMap.put("gid", goods.getGid());
                paramMap.put("beginPoint", goods.getBeginPoint());
                paramMap.put("endPoint", goods.getEndPoint());
                paramMap.put("weight", goods.getWeight());
                paramMap.put("price", goods.getPrice());
                paramMap.put("distance", goods.getDistance());
                paramMap.put("tolls", goods.getTolls());
                paramMap.put("isHand", isHand);
                paramMap.put("updateTime", time.getTime());
                if (StrUtil.isNotEmpty(goods.getSpell())) paramMap.put("spell", goods.getSpell());
                if (StrUtil.isNotEmpty(goods.getPlace())) paramMap.put("place", goods.getPlace());
                paramMap.put("pType", goods.getpType());

                if (StrUtil.isNotEmpty(goods.getOutCusDesc())) paramMap.put("outCusDesc", goods.getOutCusDesc());
                if (StrUtil.isNotEmpty(goods.getInCusDesc())) paramMap.put("inCusDesc", goods.getInCusDesc());

                if (StrUtil.isNotEmpty(designCarNum)) {//指定车牌号，生成接单信息
                    paramMap.put("tranStatus", 1);
                    paramMap.put("carNum", designCarNum);
                    paramMap.put("did", goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(o.getOid(), goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
            }
            //订单计费，计为0的表示不收费
            //客商收费，分成两个字段，实际存的值相同，即司机付费时只付一个就行。
            paramMap.put("outFee3", goods.getFees3());
            Map<String, Object> orderUpMap = new HashMap<>();
            orderUpMap.put("$set", paramMap);
            orderCollection.updateMany(clientSession, in("oid", oids.toArray()), Document.parse(JSONObject.toJSONString(orderUpMap)));

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder3_busGoods" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<Object> useBusGoods2(String busGid, Goods goods, String cusDesc) {
        //校验往货业务
        if (StrUtil.isNotEmpty(goods.getMold2()) && goods.getMold2() == 1) {
            if (StrUtil.isEmpty(goods.getBeginDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getBeginPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
            if (StrUtil.isEmpty(goods.getEndDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getEndPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
        }

        BusGoods busGoods = this.get("busGid", busGid);
        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        if (busGoods.getAvailableNum() < goods.getTotal()) return ServerResponse.createError("可用车数不足");

        //todo:若指定司机车号，则需要校验司机和车辆信息
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(goods.getCarNum())) {
            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", goods.getCarNum());
            dtcQuery.filter("driverId", goods.getDriverId());
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(goods.getDriverId());
            if (driverInfo == null) return ServerResponse.createError("指定的车牌号司机不存在");
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
                return ServerResponse.createError("指定的车牌号司机未通过审核");

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
            /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
            dpQuery.filter("cid", cid);
            dpQuery.filter("did", driverInfo.getObjectId().toString());
            DriverPool dp = driverPoolService.get(dpQuery);
            if (dp == null) {
                dp = new DriverPool();
                dp.setCid(cid);
                dp.setDid(driverInfo.getObjectId().toString());
                dp.setCustomerUser(cUser);
                dp.setDriverInfo(driverInfo);
                driverPoolService.save(dp);
            } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                return ServerResponse.createError("指定车牌号司机在黑名单中");
            }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        if (busGoods.getMold() == 0) {
            goods.setOutUnitName(busGoods.getUnitName());
            goods.setOutUnitCode(busGoods.getUnitCode());
            goods.setOutBizContractName(busGoods.getBizcontractname());
            goods.setOutBizContractCode(busGoods.getBizcontractcode());
            goods.setOutVariety(busGoods.getProductname());
            goods.setOutSubName(busGoods.getSubname());
            goods.setOutDefaultDownUnit(busGoods.getTradesubcode());
            goods.setOutAreaName(busGoods.getAreaname());
            goods.setOutDefaultArea(busGoods.getDefaultarea());
            goods.setOutMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setOutMax(busGoods.getEndNum());
            goods.setOutTradingUnit(busGoods.getTradingUnit());
            goods.setOutTradingUnitName(busGoods.getTradingUnitName());
            goods.setOutCusDesc(cusDesc);
        } else {
            goods.setInUnitName(busGoods.getUnitName());
            goods.setInUnitCode(busGoods.getUnitCode());
            goods.setInBizContractName(busGoods.getBizcontractname());
            goods.setInBizContractCode(busGoods.getBizcontractcode());
            goods.setInVariety(busGoods.getProductname());
            goods.setInSubName(busGoods.getSubname());
            goods.setInDefaultDownUnit(busGoods.getTradesubcode());
            goods.setInAreaName(busGoods.getAreaname());
            goods.setInDefaultArea(busGoods.getDefaultarea());
            goods.setInMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setInMax(busGoods.getEndNum());
            goods.setInTradingUnit(busGoods.getTradingUnit());
            goods.setInTradingUnitName(busGoods.getTradingUnitName());
            goods.setInCusDesc(cusDesc);
        }
        goods.setTradeName(busGoods.getTradeName());
        goods.setMold(busGoods.getMold());
        goods.setOutVarietyCode(busGoods.getOutVarietyCode());

        return addGoods4(goods, goods.getCarNum(), driverInfo, goods.getIsPay(), busGid);
    }

    public ServerResponse<Object> addGoods4(Goods goods, String designCarNum, DriverInfo driverInfo, Integer isPay, String busGid) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());
        goods.setStatus(0);

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees2(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder4(designCarNum, goods, 1, driverInfo, busGid);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder4(designCarNum, goods, 0, driverInfo, busGid);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Override
    public ServerResponse<Object> useBusGoods3(String busGid, Goods goods, String cusDesc) {
        //校验往货业务
        if (StrUtil.isNotEmpty(goods.getMold2()) && goods.getMold2() == 1) {
            if (StrUtil.isEmpty(goods.getBeginDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getBeginPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
            if (StrUtil.isEmpty(goods.getEndDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getEndPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
        }

        BusGoods busGoods = this.get("busGid", busGid);

        //校验车数是否超过限制
        String msg = goodsService.queryUnitGoodsTotal(busGoods.getUnitCode(), null, goods.getTotal());
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        if (busGoods.getAvailableNum() < goods.getTotal()) return ServerResponse.createError("可用车数不足");

        //  鄂绒集团 - 下单车数不能超过10车
        // if ("0010011427".equals(busGoods.getUnitCode()) && goods.getTotal()>10) return ServerResponse.createError("车数不能超过10车");

        //todo:若指定司机车号，则需要校验司机和车辆信息
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(goods.getCarNum())) {
            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", goods.getCarNum());
            dtcQuery.filter("driverId", goods.getDriverId());
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(goods.getDriverId());
            if (driverInfo == null) return ServerResponse.createError("指定的车牌号司机不存在");
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
                return ServerResponse.createError("指定的车牌号司机未通过审核");

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
            /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
            dpQuery.filter("cid", cid);
            dpQuery.filter("did", driverInfo.getObjectId().toString());
            DriverPool dp = driverPoolService.get(dpQuery);
            if (dp == null) {
                dp = new DriverPool();
                dp.setCid(cid);
                dp.setDid(driverInfo.getObjectId().toString());
                dp.setCustomerUser(cUser);
                dp.setDriverInfo(driverInfo);
                driverPoolService.save(dp);
            } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                return ServerResponse.createError("指定车牌号司机在黑名单中");
            }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        if (busGoods.getMold() == 0) {
            goods.setOutUnitName(busGoods.getUnitName());
            goods.setOutUnitCode(busGoods.getUnitCode());
            goods.setOutBizContractName(busGoods.getBizcontractname());
            goods.setOutBizContractCode(busGoods.getBizcontractcode());
            goods.setOutVariety(busGoods.getProductname());
            goods.setOutSubName(busGoods.getSubname());
            goods.setOutDefaultDownUnit(busGoods.getTradesubcode());
            goods.setOutAreaName(busGoods.getAreaname());
            goods.setOutDefaultArea(busGoods.getDefaultarea());
            goods.setOutMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setOutMax(busGoods.getEndNum());
            goods.setOutTradingUnit(busGoods.getTradingUnit());
            goods.setOutTradingUnitName(busGoods.getTradingUnitName());
            goods.setOutCusDesc(cusDesc);
        } else {
            goods.setInUnitName(busGoods.getUnitName());
            goods.setInUnitCode(busGoods.getUnitCode());
            goods.setInBizContractName(busGoods.getBizcontractname());
            goods.setInBizContractCode(busGoods.getBizcontractcode());
            goods.setInVariety(busGoods.getProductname());
            goods.setInSubName(busGoods.getSubname());
            goods.setInDefaultDownUnit(busGoods.getTradesubcode());
            goods.setInAreaName(busGoods.getAreaname());
            goods.setInDefaultArea(busGoods.getDefaultarea());
            goods.setInMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setInMax(busGoods.getEndNum());
            goods.setInTradingUnit(busGoods.getTradingUnit());
            goods.setInTradingUnitName(busGoods.getTradingUnitName());
            goods.setInCusDesc(cusDesc);
        }
        goods.setTradeName(busGoods.getTradeName());
        goods.setMold(busGoods.getMold());
        goods.setOutVarietyCode(busGoods.getOutVarietyCode());

        if (StrUtil.isNotEmpty(busGoods.getRemark1())) goods.setRemark1(busGoods.getRemark1());
        if (StrUtil.isNotEmpty(busGoods.getRemark2())) goods.setRemark2(busGoods.getRemark2());
        if (StrUtil.isNotEmpty(busGoods.getRemark3())) goods.setRemark3(busGoods.getRemark3());
        if (StrUtil.isNotEmpty(busGoods.getRemark4())) goods.setRemark4(busGoods.getRemark4());

        return addGoods5(goods, goods.getCarNum(), driverInfo, goods.getIsPay(), busGid);
    }


    public ServerResponse<Object> addGoods5(Goods goods, String designCarNum, DriverInfo driverInfo, Integer isPay, String busGid) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());
        goods.setStatus(0);

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees3(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder4(designCarNum, goods, 1, driverInfo, busGid);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder4(designCarNum, goods, 0, driverInfo, busGid);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Transactional
    Goods addGoodsAndOrder4(String designCarNum, Goods goods, Integer isHand, DriverInfo driverInfo, String busGid) {
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = goodsService.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> busGoodsCollection = this.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            //TODO:0.修改企业单使用单数
            Map<String, Object> busGoodsUpMap = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            busGoodsUpMap.put("useNum", goods.getTotal());
            busGoodsUpMap.put("availableNum", -goods.getTotal());
            upMap.put("$inc", busGoodsUpMap);
            busGoodsCollection.updateOne(clientSession, new Document("busGid", busGid), Document.parse(JSONObject.toJSONString(upMap)));

            //TODO:1.新增Goods
            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:2.根据车数 total 修改相应数量的订单Order
            int total = goods.getTotal();
            List<String> oids = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            //查询企业单下可用的订单
            Query<Order> oQuery = orderService.createQuery();
            oQuery.criteria("busGid").equal(busGid);
            oQuery.criteria("gid").equal(null);         //未拆分
            oQuery.criteria("delete").equal(false);     //未废除
            oQuery.limit(total);
            oQuery.order(Sort.ascending("objectId"));
            List<Order> orders = orderService.list(oQuery);   //企业单下可用的订单集合
            if (orders.size() < total) {
                clientSession.abortTransaction();
                return null;
            }
            for (Order o : orders) {
                oids.add(o.getOid());

                paramMap.put("gid", goods.getGid());
                paramMap.put("beginPoint", goods.getBeginPoint());
                paramMap.put("endPoint", goods.getEndPoint());
                paramMap.put("weight", goods.getWeight());
                paramMap.put("price", goods.getPrice());
                paramMap.put("distance", goods.getDistance());
                paramMap.put("tolls", goods.getTolls());
                paramMap.put("isHand", isHand);
                paramMap.put("updateTime", time.getTime());
                if (StrUtil.isNotEmpty(goods.getSpell())) paramMap.put("spell", goods.getSpell());
                if (StrUtil.isNotEmpty(goods.getPlace())) paramMap.put("place", goods.getPlace());
                paramMap.put("pType", goods.getpType());

                if (StrUtil.isNotEmpty(goods.getOutCusDesc())) paramMap.put("outCusDesc", goods.getOutCusDesc());
                if (StrUtil.isNotEmpty(goods.getInCusDesc())) paramMap.put("inCusDesc", goods.getInCusDesc());

                if (StrUtil.isNotEmpty(goods.getMold2())) paramMap.put("mold2", goods.getMold2());
                if (StrUtil.isNotEmpty(goods.getBeginDistrictCode()))
                    paramMap.put("beginDistrictCode", goods.getBeginDistrictCode());
                if (StrUtil.isNotEmpty(goods.getEndDistrictCode()))
                    paramMap.put("endDistrictCode", goods.getEndDistrictCode());

                if (StrUtil.isNotEmpty(designCarNum)) {//指定车牌号，生成接单信息
                    paramMap.put("tranStatus", 1);
                    paramMap.put("carNum", designCarNum);
                    paramMap.put("did", goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(o.getOid(), goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
            }
            //订单计费，计为0的表示不收费
            //客商收费，分成两个字段，实际存的值相同，即司机付费时只付一个就行。
            paramMap.put("outFee0", goods.getFeesOut());
            paramMap.put("inFee0", goods.getFeesIn());
            paramMap.put("outFee1", goods.getFees1Out());
            paramMap.put("inFee1", goods.getFees1In());
            paramMap.put("outFee2", goods.getFees2Out());
            paramMap.put("inFee2", goods.getFees2In());
            paramMap.put("outFee3", goods.getFees3());
            paramMap.put("outFee5", StrUtil.isNotEmpty(goods.getFee5Out()) ? goods.getFee5Out() : 0);
            paramMap.put("inFee5", StrUtil.isNotEmpty(goods.getFee5In()) ? goods.getFee5In() : 0);
            Map<String, Object> orderUpMap = new HashMap<>();
            orderUpMap.put("$set", paramMap);
            orderCollection.updateMany(clientSession, in("oid", oids.toArray()), Document.parse(JSONObject.toJSONString(orderUpMap)));

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder4_busGoods" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<Object> useBusGoods4(String busGid, Goods goods, String cusDesc, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime) {
        //校验往货业务
        if (StrUtil.isNotEmpty(goods.getMold2()) && goods.getMold2() == 1) {
            if (StrUtil.isEmpty(goods.getBeginDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getBeginPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
            if (StrUtil.isEmpty(goods.getEndDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getEndPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
        }

        BusGoods busGoods = this.get("busGid", busGid);

        //校验车数是否超过限制
        String msg = goodsService.queryUnitGoodsTotal(busGoods.getUnitCode(), null, goods.getTotal());
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        if (busGoods.getAvailableNum() < goods.getTotal()) return ServerResponse.createError("可用车数不足");

        //  鄂绒集团 - 下单车数不能超过10车
        // if ("0010011427".equals(busGoods.getUnitCode()) && goods.getTotal()>10) return ServerResponse.createError("车数不能超过10车");

        if (busGoods.getMold() == 1 && StrUtil.isNotEmpty(inNetWeight) && !"null".equals(inNetWeight))
            goods.setInNetWeightOne(inNetWeight);
        if (goods.getTotal() == 1 && StrUtil.isNotEmpty(loadPound) && !"null".equals(loadPound))
            goods.setLoadPound(loadPound);
        if (goods.getTotal() == 1 && StrUtil.isNotEmpty(loadPoundPho) && !"null".equals(loadPoundPho))
            goods.setLoadPoundPho(loadPoundPho);
        if (goods.getTotal() == 1 && StrUtil.isNotEmpty(loadTime) && !"null".equals(loadTime))
            goods.setLoadTime(loadTime);

        //todo:若指定司机车号，则需要校验司机和车辆信息
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(goods.getCarNum())) {
            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", goods.getCarNum());
            dtcQuery.filter("driverId", goods.getDriverId());
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(goods.getDriverId());
            if (driverInfo == null) return ServerResponse.createError("指定的车牌号司机不存在");
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
                return ServerResponse.createError("指定的车牌号司机未通过审核");

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
            /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
            dpQuery.filter("cid", cid);
            dpQuery.filter("did", driverInfo.getObjectId().toString());
            DriverPool dp = driverPoolService.get(dpQuery);
            if (dp == null) {
                dp = new DriverPool();
                dp.setCid(cid);
                dp.setDid(driverInfo.getObjectId().toString());
                dp.setCustomerUser(cUser);
                dp.setDriverInfo(driverInfo);
                driverPoolService.save(dp);
            } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                return ServerResponse.createError("指定车牌号司机在黑名单中");
            }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        if (busGoods.getMold() == 0) {
            goods.setOutUnitName(busGoods.getUnitName());
            goods.setOutUnitCode(busGoods.getUnitCode());
            goods.setOutBizContractName(busGoods.getBizcontractname());
            goods.setOutBizContractCode(busGoods.getBizcontractcode());
            goods.setOutVariety(busGoods.getProductname());
            goods.setOutSubName(busGoods.getSubname());
            goods.setOutDefaultDownUnit(busGoods.getTradesubcode());
            goods.setOutAreaName(busGoods.getAreaname());
            goods.setOutDefaultArea(busGoods.getDefaultarea());
            goods.setOutMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setOutMax(busGoods.getEndNum());
            goods.setOutTradingUnit(busGoods.getTradingUnit());
            goods.setOutTradingUnitName(busGoods.getTradingUnitName());
            goods.setOutCusDesc(cusDesc);
        } else {
            goods.setInUnitName(busGoods.getUnitName());
            goods.setInUnitCode(busGoods.getUnitCode());
            goods.setInBizContractName(busGoods.getBizcontractname());
            goods.setInBizContractCode(busGoods.getBizcontractcode());
            goods.setInVariety(busGoods.getProductname());
            goods.setInSubName(busGoods.getSubname());
            goods.setInDefaultDownUnit(busGoods.getTradesubcode());
            goods.setInAreaName(busGoods.getAreaname());
            goods.setInDefaultArea(busGoods.getDefaultarea());
            goods.setInMin(busGoods.getEndNum() - busGoods.getAvailableNum() + 1);
            goods.setInMax(busGoods.getEndNum());
            goods.setInTradingUnit(busGoods.getTradingUnit());
            goods.setInTradingUnitName(busGoods.getTradingUnitName());
            goods.setInCusDesc(cusDesc);
        }
        goods.setTradeName(busGoods.getTradeName());
        goods.setMold(busGoods.getMold());
        goods.setOutVarietyCode(busGoods.getOutVarietyCode());

        if (StrUtil.isNotEmpty(busGoods.getRemark1())) goods.setRemark1(busGoods.getRemark1());
        if (StrUtil.isNotEmpty(busGoods.getRemark2())) goods.setRemark2(busGoods.getRemark2());
        if (StrUtil.isNotEmpty(busGoods.getRemark3())) goods.setRemark3(busGoods.getRemark3());
        if (StrUtil.isNotEmpty(busGoods.getRemark4())) goods.setRemark4(busGoods.getRemark4());

        return addGoods6(goods, goods.getCarNum(), driverInfo, goods.getIsPay(), busGid, loadPound, loadPoundPho, loadTime);
    }

    public ServerResponse<Object> addGoods6(Goods goods, String designCarNum, DriverInfo driverInfo, Integer isPay, String busGid, String loadPound, String loadPoundPho, Long loadTime) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());
        goods.setStatus(0);

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees3(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //判断收货业务是否需要录入对方净重
        if (goods.getMold() == 1) {
            SysUnit[] sysUnits = (SysUnit[]) payMap.get("sysUnits");
            SysUnit inSubUnit = sysUnits[3];
            if (StrUtil.isNotEmpty(inSubUnit.getInNeedNetWeight()) && inSubUnit.getInNeedNetWeight() == 1 && StrUtil.isEmpty(goods.getInNetWeightOne()))
                return ServerResponse.createError("需要录入对方净重");
        }

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder5(designCarNum, goods, 1, driverInfo, busGid, loadPound, loadPoundPho, loadTime);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            } else {
                String mes = cUser.getMobile() + "发布的面议单";
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder5(designCarNum, goods, 0, driverInfo, busGid, loadPound, loadPoundPho, loadTime);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Transactional
    Goods addGoodsAndOrder5(String designCarNum, Goods goods, Integer isHand, DriverInfo driverInfo, String busGid, String loadPound, String loadPoundPho, Long loadTime) {
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = goodsService.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> busGoodsCollection = this.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            //TODO:0.修改企业单使用单数
            Map<String, Object> busGoodsUpMap = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            busGoodsUpMap.put("useNum", goods.getTotal());
            busGoodsUpMap.put("availableNum", -goods.getTotal());
            upMap.put("$inc", busGoodsUpMap);
            busGoodsCollection.updateOne(clientSession, new Document("busGid", busGid), Document.parse(JSONObject.toJSONString(upMap)));

            //TODO:1.新增Goods
            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:2.根据车数 total 修改相应数量的订单Order
            int total = goods.getTotal();
            List<String> oids = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            //查询企业单下可用的订单
            Query<Order> oQuery = orderService.createQuery();
            oQuery.criteria("busGid").equal(busGid);
            oQuery.criteria("gid").equal(null);         //未拆分
            oQuery.criteria("delete").equal(false);     //未废除
            oQuery.limit(total);
            oQuery.order(Sort.ascending("objectId"));
            List<Order> orders = orderService.list(oQuery);   //企业单下可用的订单集合
            if (orders.size() < total) {
                clientSession.abortTransaction();
                return null;
            }
            for (Order o : orders) {
                oids.add(o.getOid());

                if (StrUtil.isNotEmpty(designCarNum)) {//指定车牌号，生成接单信息
                    paramMap.put("tranStatus", 1);
                    paramMap.put("carNum", designCarNum);
                    paramMap.put("did", goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(o.getOid(), goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
            }

            paramMap.put("gid", goods.getGid());
            paramMap.put("beginPoint", goods.getBeginPoint());
            paramMap.put("endPoint", goods.getEndPoint());
            paramMap.put("weight", goods.getWeight());
            paramMap.put("price", goods.getPrice());
            paramMap.put("distance", goods.getDistance());
            paramMap.put("tolls", goods.getTolls());
            paramMap.put("isHand", isHand);
            paramMap.put("updateTime", time.getTime());
            if (StrUtil.isNotEmpty(goods.getSpell())) paramMap.put("spell", goods.getSpell());
            if (StrUtil.isNotEmpty(goods.getPlace())) paramMap.put("place", goods.getPlace());
            paramMap.put("pType", goods.getpType());

            if (StrUtil.isNotEmpty(goods.getOutCusDesc())) paramMap.put("outCusDesc", goods.getOutCusDesc());
            if (StrUtil.isNotEmpty(goods.getInCusDesc())) paramMap.put("inCusDesc", goods.getInCusDesc());

            if (StrUtil.isNotEmpty(goods.getMold2())) paramMap.put("mold2", goods.getMold2());
            if (StrUtil.isNotEmpty(goods.getBeginDistrictCode()))
                paramMap.put("beginDistrictCode", goods.getBeginDistrictCode());
            if (StrUtil.isNotEmpty(goods.getEndDistrictCode()))
                paramMap.put("endDistrictCode", goods.getEndDistrictCode());

            //订单计费，计为0的表示不收费
            //客商收费，分成两个字段，实际存的值相同，即司机付费时只付一个就行。
            paramMap.put("outFee0", goods.getFeesOut());
            paramMap.put("inFee0", goods.getFeesIn());
            paramMap.put("outFee1", goods.getFees1Out());
            paramMap.put("inFee1", goods.getFees1In());
            paramMap.put("outFee2", goods.getFees2Out());
            paramMap.put("inFee2", goods.getFees2In());
            paramMap.put("outFee3", goods.getFees3());
            paramMap.put("outFee5", StrUtil.isNotEmpty(goods.getFee5Out()) ? goods.getFee5Out() : 0);
            paramMap.put("inFee5", StrUtil.isNotEmpty(goods.getFee5In()) ? goods.getFee5In() : 0);

            if (StrUtil.isNotEmpty(goods.getInNetWeightOne())) {
                paramMap.put("inNetWeight", goods.getInNetWeightOne());
                paramMap.put("inNetWeightPerson", "CU");
            }
            if (StrUtil.isNotEmpty(loadPound)) {
                paramMap.put("loadPound", loadPound);
                paramMap.put("loadPoundPerson", "CU");
            }
            if (StrUtil.isNotEmpty(loadTime)) {
                paramMap.put("loadTime", loadTime);
                paramMap.put("loadTimePerson", "CU");
            }
            if (StrUtil.isNotEmpty(loadPoundPho)) {
                paramMap.put("loadPoundPho", loadPoundPho);
                paramMap.put("loadPoundPhoPerson", "CU");
            }
            Map<String, Object> orderUpMap = new HashMap<>();
            orderUpMap.put("$set", paramMap);
            orderCollection.updateMany(clientSession, in("oid", oids.toArray()), Document.parse(JSONObject.toJSONString(orderUpMap)));

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder5_busGoods" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<EGridResult> getBusGoods(String busGid, Integer type, Integer page, Integer rows) {
        Query<Order> query = orderService.createQuery();
        query.filter("busGid", busGid);

        if (StrUtil.isNotEmpty(type)) {
            switch (type) {
                case 0:
                    query.criteria("tranStatus").equal(0);
                    break;
                case 1:
                    query.criteria("tranStatus").greaterThan(0);
                    break;
                case 2:
                    query.criteria("tranStatus").equal(5);
                    break;
                default:
                    break;
            }
        }

        query.order(Sort.descending("createTime"));
        EGridResult<Order> eGridResult = orderService.findPage(page, rows, query);
        List<Order> orders = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orders) {
            //TODO:处理要返回的数据包装到OrderInfoData对象中
            OrderInfoData result = new OrderInfoData();
            BeanUtils.copyProperties(order, result);
            resultData.add(result);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    @Override
    public ServerResponse<String> busAddGoods(Integer min, Integer max, BusGoods goods) {
        //检查客商是否存在
        CustomerUser cUser = customerUserService.getByPK(goods.getCid());
        Integer cusFee = new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue();
        //检查车牌号和司机是否可用
        String msg = "";
        checkBillCode(goods.getUnitCode(), min, goods.getBillType());

        DriverInfo driverInfo = null;
        if (StrUtil.isNotEmpty(goods.getDvrMobile())) {
            driverInfo = driverInfoService.get("mobile", goods.getDvrMobile());
            msg = checkDvrCarNum(driverInfo, goods.getCarNum());
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);
        }

        Goods g = new Goods();
        g.setFees3(cusFee);    //设置客商收费
        g.setTotal(max - min + 1);
        if (goods.getBillType() == 6) {
            g.setOutUnitCode(goods.getUnitCode());
            g.setOutDefaultDownUnit(goods.getTradesubcode());
            g.setOutVarietyCode(goods.getOutVarietyCode());
        } else if (goods.getBillType() == 5) {
            g.setInUnitCode(goods.getUnitCode());
            g.setInDefaultDownUnit(goods.getTradesubcode());
        }

        //查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = goodsService.sysUnitTotalFees3(g);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        Order oldOrder = orderService.get("busGid", goods.getBusGid());
        String transportPlace = oldOrder.getPlace();

        //事务添加订单
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> busGoodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();

            Map<String, Object> params = new HashMap<>();
            params.put("availableNum", (max - min + 1));
            params.put("totalNum", (max - min + 1));
            Map<String, Object > updMap = new HashMap<>();
            updMap.put("$inc", params);
            busGoodsCollection.updateOne(clientSession, new Document("busGid", goods.getBusGid()), Document.parse(JSONObject.toJSONString(updMap))) ;//gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = g.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setBusGid(goods.getBusGid());
                o.setOid(oid);
                o.setStatus(0);
                o.setTradeName(goods.getTradeName());
                o.setMold(goods.getMold());
                o.setIsWeChat(0);
                o.setShare(0);
                //5，采购-入库。6，销售-出库
                if (goods.getBillType() == 6) {
                    o.setOutMinMax(String.valueOf(min + i));
                    o.setOutBillCode(g.getOutUnitCode() + "6" + String.format("%09d", (min + i)));
                    o.setOutChecking(0);
                    o.setOutUnitCode(goods.getUnitCode());
                    o.setOutUnitName(goods.getUnitName());
                    o.setOutBizContractCode(goods.getBizcontractcode());
                    o.setOutBizContractName(goods.getBizcontractname());
                    o.setOutVariety(goods.getProductname());
                    o.setOutDefaultDownUnit(goods.getTradesubcode());
                    o.setOutSubName(goods.getSubname());
                    o.setOutDefaultArea(goods.getDefaultarea());
                    o.setOutAreaName(goods.getAreaname());
                    o.setOutTradingUnit(goods.getTradingUnit());
                    o.setOutTradingUnitName(goods.getTradingUnitName());
                    o.setOutVarietyCode(g.getOutVarietyCode());
                } else if (goods.getBillType() == 5) {
                    o.setInMinMax(String.valueOf(min + i));
                    o.setInBillCode(g.getInUnitCode() + "5" + String.format("%09d", (min + i)));
                    o.setInChecking(0);
                    o.setInUnitCode(goods.getUnitCode());
                    o.setInUnitName(goods.getUnitName());
                    o.setInBizContractCode(goods.getBizcontractcode());
                    o.setInBizContractName(goods.getBizcontractname());
                    o.setInVariety(goods.getProductname());
                    o.setInDefaultDownUnit(goods.getTradesubcode());
                    o.setInSubName(goods.getSubname());
                    o.setInDefaultArea(goods.getDefaultarea());
                    o.setInAreaName(goods.getAreaname());
                    o.setInTradingUnit(goods.getTradingUnit());
                    o.setInTradingUnitName(goods.getTradingUnitName());
                }
                o.setDelete(false);
                //o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setCid(goods.getCid());
                o.setTranStatus(0);
                if (null != driverInfo) {
                    o.setCarNum(goods.getCarNum());
                    o.setDid(driverInfo.getObjectId().toHexString());
                }

                if (StrUtil.isNotEmpty(goods.getRemark1())) o.setRemark1(goods.getRemark1());
                if (StrUtil.isNotEmpty(goods.getRemark2())) o.setRemark2(goods.getRemark2());
                if (StrUtil.isNotEmpty(goods.getRemark3())) o.setRemark3(goods.getRemark3());
                if (StrUtil.isNotEmpty(goods.getRemark4())) o.setRemark4(goods.getRemark4());

                addOrderFees2(g, o);

                if (StrUtil.isNotEmpty(transportPlace)) o.setPlace(transportPlace);
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("busPushGoods2" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("下单成功");
    }
}
