package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ISysUnitAccountService extends IBaseMongoService<SysUnitAccount> {
    // 查询账户余额
    BigDecimal getAvailableFee(String uid);

    // 充值
    ServerResponse<String> addUnitAccount(UnitData data);

    // 账户详情
    ServerResponse<EGridResult> detailsAccount(Integer page, Integer rows);

    // ServerResponse账户余额
    ServerResponse<BigDecimal> responseAvailableFee();

    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
    void createSysUnitAccountDocs(List<Document> sysUnitAccountDocs, String[] uids, String[] userIds, int[] types, String oid, int[] fees, Date time);

    Document createSysUnitAccountDocs(String uid, String userId, int type, String oid, int fee, Date time);
}
