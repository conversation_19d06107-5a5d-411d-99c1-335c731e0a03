package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.UserDefinedAddress;

public interface IUserDefinedAddressService extends IBaseMongoService<UserDefinedAddress> {

    //添加自定义地址 接口
    ServerResponse<Object> addUserDefinedAddress(String addName, String fullName, Double[] location, String province, String city, String district);

    //删除自定义地址 接口
    ServerResponse<String> delUserDefinedAddress(String caId);

    //查询自定义地址 接口
    //ServerResponse<List<UserDefinedAddress>> searchUserDefinedAddress(String unitCode, String addName);
    ServerResponse<EGridResult> searchUserDefinedAddress(String unitCode, String addName, Integer page, Integer rows);

    //微信小程序个人给客商（手机号）添加自定义地址
    ServerResponse<String> addUserDefinedAddressWithMobile(String mobile, String addName, String fullName, Double[] location);
}
