package com.erdos.coal.park.api.manage.service;

import com.erdos.coal.core.base.mongo.IBaseMongoDAO;
import com.erdos.coal.park.api.manage.entity.Locked;

import java.util.concurrent.CompletableFuture;

public interface ILockedService extends IBaseMongoDAO<Locked> {

    //并发及异步测试
    CompletableFuture<String> test1(String code, int tid);

    boolean getLock(String code, int type, int threadID);

    void unLock(String code, int type);

    boolean getLock(String code, int type);

    boolean orderQuarantineLock(String oid, int type);

    void orderQuarantineUnLock(String oid, int type);
}
