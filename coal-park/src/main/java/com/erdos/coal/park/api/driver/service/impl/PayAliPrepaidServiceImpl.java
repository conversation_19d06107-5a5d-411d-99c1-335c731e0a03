package com.erdos.coal.park.api.driver.service.impl;

import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.driver.dao.IPayAliPrepaidDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.PayAliPrepaid;
import com.erdos.coal.park.api.driver.service.IPayAliPrepaidService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.transaction.alipay.bean.AlipayConfig;
import com.erdos.coal.transaction.alipay.service.AliPayService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("payAliPrepaidService")
public class PayAliPrepaidServiceImpl extends BaseMongoServiceImpl<PayAliPrepaid, IPayAliPrepaidDao> implements IPayAliPrepaidService {

    @Resource
    private ILockedService lockedService;
    @Resource
    private AliPayService aliPayService;
    @Resource
    private AlipayConfig alipayConfig;
    @Resource
    private DriverInfoServiceImpl driverInfoService;

    @Override
    public ServerResponse<String> createAliOrder(String body, String subject, String totalAmount) {
        String did = ShiroUtils.getUserId();
        DriverInfo dUser = driverInfoService.getByPK(did);

        //判断订单金额不能为空.
        if (StrUtil.isEmpty(totalAmount) || Double.valueOf(totalAmount) - 0.0 == 0.0)
            return ServerResponse.createError("订单金额不能为空");

        if (StrUtil.isNotEmpty(dUser.getState()) && (dUser.getState() != 1 && dUser.getState() != 4))
            return ServerResponse.createError("司机账户不可用！");

        PayAliPrepaid aliPrepaid = new PayAliPrepaid();
        aliPrepaid.setAppId(alipayConfig.APP_ID_DRIVER);

        //type=1-司机给自己充值
        //cusOrDri=1 表示的是司机黑金宝app
        return createAliOrder(did, body, subject, totalAmount, did, 1, aliPrepaid, 1);
    }

    @Override
    public ServerResponse<String> createAliOrder(String masterId, String body, String subject, String totalAmount, String guestId, Integer type, PayAliPrepaid aliPrepaid, Integer cusOrDri) {
        //用户重复提交加锁
        boolean lock = lockedService.getLock(masterId, SysConstants.LockType.ALI_PAY_PREPAID.getType());
        if (!lock) {
            return ServerResponse.createError("ERROR");
        } else {
            try {

                //测试的时候，totalAmount设置为0.01
                //totalAmount = "0.01";

                String outTradeNo = IdUnit.createId("ali");//商户订单号,支付宝要求限制最大长度64
                AlipayTradeAppPayResponse response = aliPayService.preOrder(body, subject, outTradeNo, totalAmount, cusOrDri);
                //预下单失败处理
                if (response == null) {
                    return ServerResponse.createError("FAIL");
                }

                //保存预下单信息
                aliPrepaid.setMasterId(masterId);
                aliPrepaid.setGuestId(guestId);
                aliPrepaid.setSubject(subject);
                aliPrepaid.setOutTradeNo(outTradeNo);
                aliPrepaid.setTotalAmount(totalAmount);
                aliPrepaid.setProductCode(alipayConfig.PRODUCT_CODE);
                aliPrepaid.setBody(body);
                aliPrepaid.setTimeoutExpress(alipayConfig.TIME_OUT_EXPRESS);
                aliPrepaid.setSellerId(alipayConfig.SELLER_ID);
                aliPrepaid.setPay(false);
                aliPrepaid.setType(type);
                this.save(aliPrepaid);
                return ServerResponse.createSuccess("SUCCESS", response.getBody());

            } finally {
                lockedService.unLock(masterId, SysConstants.LockType.ALI_PAY_PREPAID.getType());
            }
        }
    }
}
