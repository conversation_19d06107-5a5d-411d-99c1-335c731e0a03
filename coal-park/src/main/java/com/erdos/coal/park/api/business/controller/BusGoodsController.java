package com.erdos.coal.park.api.business.controller;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.entity.BusGoods;
import com.erdos.coal.park.api.business.service.IBusGoodsService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/bus/goods")
public class BusGoodsController extends BaseController {
    @Resource
    private IBusGoodsService busGoodsService;

    @InvokeLog(description = "企业系统代客商线下下单 接口")   //日志
    @PostMapping(value = "/biz_push_goods")
    public ServerResponse<String> bizPushGoodsHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        return ServerResponse.createError("接口停用！");

        /*if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String userCode = data.getString("customerusercode");
        Integer startNum = data.getInteger("startnum");
        Integer endNum = data.getInteger("endnum");
        String carNum = data.getString("carnum");
        String dvrMobile = data.getString("dvrmobile");
        Integer carType = data.getInteger("cartype");

        if (StrUtil.isNotEmpty(carNum) && endNum - startNum + 1 > 1) return ServerResponse.createError("指派车牌号只能下一单");
        if (StrUtil.isNotEmpty(carNum) && StrUtil.isEmpty(dvrMobile))
            return ServerResponse.createError("指派车牌号时缺少司机手机号");

        BusGoods goods = new BusGoods();
        goods.setUserCode(userCode);
        goods.setBillType(data.getInteger("billtype"));
        goods.setStartNum(startNum);
        goods.setEndNum(endNum);
        goods.setBizunitname(data.getString("bizunitname"));
        goods.setBizcontractcode(data.getString("bizcontractcode"));
        goods.setBizcontractname(data.getString("bizcontractname"));
        goods.setProductname(data.getString("productname"));
        goods.setTradesubcode(data.getString("tradesubcode"));
        goods.setSubname(data.getString("subname"));
        goods.setDefaultarea(data.getString("defaultarea"));
        goods.setAreaname(data.getString("areaname"));
        if (StrUtil.isNotEmpty(carNum)) goods.setCarNum(carNum);
        if (StrUtil.isNotEmpty(dvrMobile)) goods.setDvrMobile(dvrMobile);
        if (StrUtil.isNotEmpty(carType)) goods.setCarType(carType);
        goods.setDelNum(0);
        goods.setUseNum(0);
        goods.setAvailableNum(endNum - startNum + 1);
        goods.setTotalNum(endNum - startNum + 1);

        String transportPlace = data.getString("transportplace");
        return busGoodsService.busPushGoods(userCode, startNum, endNum, carNum, dvrMobile, carType, goods, transportPlace);*/
    }

    @InvokeLog(description = "企业系统代客商线下下单2 接口")   //日志
    @PostMapping(value = "/biz_push_goods2")
    public ServerResponse<String> bizPushGoods2Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String userCode = data.getString("customerusercode");
        Integer startNum = data.getInteger("startnum");
        Integer endNum = data.getInteger("endnum");
        String carNum = data.getString("carnum");
        String dvrMobile = data.getString("dvrmobile");
        Integer carType = data.getInteger("cartype");

        String tradingUnit = data.getString("bizunitcode");
        String tradingUnitName = data.getString("bizunitname");

        Integer billtype = data.getInteger("billtype");

        if (StrUtil.isNotEmpty(carNum) && endNum - startNum + 1 > 1) return ServerResponse.createError("指派车牌号只能下一单");
        if (StrUtil.isNotEmpty(carNum) && StrUtil.isEmpty(dvrMobile))
            return ServerResponse.createError("指派车牌号时缺少司机手机号");

        BusGoods goods = new BusGoods();
        goods.setUserCode(userCode);
        goods.setBillType(billtype);
        goods.setStartNum(startNum);
        goods.setEndNum(endNum);
        goods.setBizunitname(data.getString("bizunitname"));
        goods.setBizcontractcode(data.getString("bizcontractcode"));
        goods.setBizcontractname(data.getString("bizcontractname"));
        goods.setProductname(data.getString("productname"));
        goods.setTradesubcode(data.getString("tradesubcode"));
        goods.setSubname(data.getString("subname"));
        goods.setDefaultarea(data.getString("defaultarea"));
        goods.setAreaname(data.getString("areaname"));
        if (StrUtil.isNotEmpty(carNum)) goods.setCarNum(carNum);
        if (StrUtil.isNotEmpty(dvrMobile)) goods.setDvrMobile(dvrMobile);
        if (StrUtil.isNotEmpty(carType)) goods.setCarType(carType);
        goods.setDelNum(0);
        goods.setUseNum(0);
        goods.setAvailableNum(endNum - startNum + 1);
        goods.setTotalNum(endNum - startNum + 1);

        goods.setTradingUnit(tradingUnit);
        goods.setTradingUnitName(tradingUnitName);

        if (data.getString("remark1") != null) goods.setRemark1(data.getString("remark1"));
        if (data.getString("remark2") != null) goods.setRemark2(data.getString("remark2"));
        if (data.getString("remark3") != null) goods.setRemark3(data.getString("remark3"));
        if (data.getString("remark4") != null) goods.setRemark4(data.getString("remark4"));

        if (billtype == 6) goods.setOutVarietyCode(data.getString("varietyCode"));

        String transportPlace = data.getString("transportplace");
        return busGoodsService.busPushGoods2(userCode, startNum, endNum, carNum, dvrMobile, carType, goods, transportPlace);
    }

    @InvokeLog(description = "企业系统代客商线下下单2 接口")   //日志
    @PostMapping(value = "/biz_push_goods3")
    public ServerResponse<String> bizPushGoods3Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String userCode = data.getString("customerusercode");
        Integer startNum = data.getInteger("startnum");
        Integer endNum = data.getInteger("endnum");
        String carNum = data.getString("carnum");
        String dvrMobile = data.getString("dvrmobile");
        Integer carType = data.getInteger("cartype");

        String tradingUnit = data.getString("bizunitcode");
        String tradingUnitName = data.getString("bizunitname");

        Integer billtype = data.getInteger("billtype");

        if (StrUtil.isNotEmpty(carNum) && endNum - startNum + 1 > 1) return ServerResponse.createError("指派车牌号只能下一单");
        if (StrUtil.isNotEmpty(carNum) && StrUtil.isEmpty(dvrMobile))
            return ServerResponse.createError("指派车牌号时缺少司机手机号");

        BusGoods goods = new BusGoods();
        goods.setUserCode(userCode);
        goods.setBillType(billtype);
        goods.setStartNum(startNum);
        goods.setEndNum(endNum);
        goods.setBizunitname(data.getString("bizunitname"));
        goods.setBizcontractcode(data.getString("bizcontractcode"));
        goods.setBizcontractname(data.getString("bizcontractname"));
        goods.setProductname(data.getString("productname"));
        goods.setTradesubcode(data.getString("tradesubcode"));
        goods.setSubname(data.getString("subname"));
        goods.setDefaultarea(data.getString("defaultarea"));
        goods.setAreaname(data.getString("areaname"));
        if (StrUtil.isNotEmpty(carNum)) goods.setCarNum(carNum);
        if (StrUtil.isNotEmpty(dvrMobile)) goods.setDvrMobile(dvrMobile);
        if (StrUtil.isNotEmpty(carType)) goods.setCarType(carType);
        goods.setDelNum(0);
        goods.setUseNum(0);
        goods.setAvailableNum(endNum - startNum + 1);
        goods.setTotalNum(endNum - startNum + 1);

        goods.setTradingUnit(tradingUnit);
        goods.setTradingUnitName(tradingUnitName);

        if (data.getString("remark1") != null) goods.setRemark1(data.getString("remark1"));
        if (data.getString("remark2") != null) goods.setRemark2(data.getString("remark2"));
        if (data.getString("remark3") != null) goods.setRemark3(data.getString("remark3"));
        if (data.getString("remark4") != null) goods.setRemark4(data.getString("remark4"));

        if (billtype == 6) goods.setOutVarietyCode(data.getString("varietyCode"));

        String transportPlace = data.getString("transportplace");
        return busGoodsService.busPushGoods3(userCode, startNum, endNum, carNum, dvrMobile, carType, goods, transportPlace);
    }

    @InvokeLog(description = "企业系统修改线下下单 接口")   //日志
    @PostMapping(value = "/biz_update_variety")
    public ServerResponse<List<String>> bizUpdateVarietyHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String billCodeList = data.getString("billcodelist");

        Integer billType = data.getInteger("billtype");
        String startCode = data.getString("startCode");
        String endCode = data.getString("endCode");

        String variety = data.getString("productname");
        String transportPlace = data.getString("transportplace");

        if (StrUtil.isEmpty(variety)) return ServerResponse.createError("要修改的商品名称参数不能为空");

        return busGoodsService.updateBusGoods(startCode, endCode, billType, variety, billCodeList, transportPlace);
    }

    @InvokeLog(description = "企业系统删除线下下单 接口")   //日志
    @PostMapping(value = "/biz_delete_variety")
    public ServerResponse<List<String>> bizDeleteVarietyHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        Integer billType = data.getInteger("billtype");
        String startCode = data.getString("startCode");
        String endCode = data.getString("endCode");

        String billCodeList = data.getString("billcodelist");
        return busGoodsService.delBusGoods(startCode, endCode, billType, billCodeList);
    }

    @InvokeLog(description = "企业系统补充线下单 接口")   //日志
    @PostMapping(value = "/biz_add_goods")
    public ServerResponse<String> bizAddGoodsHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String busGid = data.getString("busGid");
        BusGoods goods = busGoodsService.get("busGid", busGid);
        if (null == goods) return ServerResponse.createError("busGid错误，订单不存在");

        Integer startNum = data.getInteger("startnum");
        Integer endNum = data.getInteger("endnum");

//        goods.setStartNum(startNum);
//        goods.setEndNum(endNum);
        return busGoodsService.busAddGoods(startNum, endNum, goods);
    }
}
