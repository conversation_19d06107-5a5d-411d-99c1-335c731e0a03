package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.service.IWechatOrderRecordService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

//"司机小程序订单管理接口列表"
@RestController
@RequestMapping("/api/dvr/wechat/order")
public class WechatOrderRecordController extends BaseController {

    @Resource
    private IWechatOrderRecordService wechatOrderRecordService;

    @InvokeLog(description = "小程序扫码或分享接单  接口")//日志
    @PostMapping(value = "/batch")
    public ServerResponse<Map<String, String>> batchHandler(
            @RequestParam(value = "carNum") String carNum,         //"车牌号"
            @RequestParam(value = "carInfoId") String carInfoId,   //"车型"
            @RequestParam(value = "gid") String gid,                           //"货物编号"
            @RequestParam(value = "oids", required = false) String[] oids,     //"订单编号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "fee", required = false) String fee          //"友商收取金额"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
        /*if (StrUtil.isNotEmpty(fee)) fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        return wechatOrderRecordService.batch(carNum, carInfoId, gid, oids, longitude, latitude, fee);*/
    }

    @InvokeLog(description = "订单由微信直接付款结果查询（订单为指定车号）  接口", printReturn = false)  //日志
    @PostMapping(value = "/searchDispath")
    public ServerResponse<Map<String, String>> searchDispathHandler(
            @RequestParam(value = "transactionId", required = false) String transactionId,     //"订单号 微信订单号"
            @RequestParam(value = "outTradeNo", required = false) String outTradeNo,           //"商户订单号"
            @RequestParam(value = "longitude", required = false) String longitude,             //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,               //"纬度"
            @RequestParam(value = "oid") String oid                                            //"司机接单订单号"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
        //return wechatOrderRecordService.search(transactionId, outTradeNo, longitude, latitude, null, oid);
    }

    @InvokeLog(description = "订单由微信直接付款结果查询（订单为扫码下单或批量下单）  接口", printReturn = false)  //日志
    @PostMapping(value = "/searchBatch")
    public ServerResponse<Map<String, String>> searchBatchHandler(
            @RequestParam(value = "transactionId", required = false) String transactionId,     //"订单号 微信订单号"
            @RequestParam(value = "outTradeNo", required = false) String outTradeNo,           //"商户订单号"
            @RequestParam(value = "longitude", required = false) String longitude,             //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,               //"纬度"
            @RequestParam(value = "gid") String gid                                            //
    ) throws GlobalException {
        //真微信小程序司机
        //return wechatOrderRecordService.search(transactionId, outTradeNo, longitude, latitude, gid, null);
        //假微信小程序司机，实际是app司机
        //return wechatOrderRecordService.search2(transactionId, outTradeNo, longitude, latitude, gid, null);
        return ServerResponse.createError("接口停用");
    }

    @InvokeLog(description = "微信小程序司机查询订单 接口", printReturn = false)//日志
    @PostMapping(value = "/query_order")
    public ServerResponse<List<DriverOrderData>> queryOrderHandler(
            @RequestParam(value = "finishTag") int[] finishTag,                    //"订单完成标识(0-下单成功,1-运输途中,2-订单完成)"
            @RequestParam(value = "finishTime", required = false) Long finishTime  //"完成时间"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用，功能接口请调用 /api/dvr/order/order_query");
        //return wechatOrderRecordService.queryOrder(finishTag, finishTime);
    }

    @InvokeLog(description = "司机（包含小程序司机）同意或主动撤销订单 接口")//日志
    @PostMapping(value = "/save_order_delete")
    public ServerResponse<String> saveOrderDeleteHandler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用，和/api/dvr/push/save_delete_order接口功能重复");
        //return orderRecordService.saveDeleteOrder(oid);
    }

    //微信小程序查询订单信息的支付金额---------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "小程序查询货物信息的支付金额  接口", printReturn = false)//日志
    @PostMapping(value = "/search_goods")
    public ServerResponse<GoodsInfoData> searchGoodsHandler(
            @RequestParam(value = "gid") String gid    //"货物编号"
    ) throws GlobalException {
        return wechatOrderRecordService.searchGoods(gid);
    }
}
