package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.business.pojo.VehicleTypeCost;
import com.erdos.coal.park.api.business.service.ITradeContractService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.WXsDvrGoods;
import com.erdos.coal.park.api.customer.pojo.DriverInfoData;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.pojo.HandlingCostPojo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IOrderDimensionService;
import com.erdos.coal.park.api.driver.service.IOrderRecordService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.utils.NumFmtUtil;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

//"司机APP订单管理接口列表"
@RestController
@RequestMapping("/api/dvr/order")
public class OrderRecordController extends BaseController {

    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderRecordService orderRecordService;
    @Resource
    private IOrderDimensionService orderDimensionService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private ITradeContractService tradeContractService;

    @InvokeLog(description = "指定车牌号-接单或拒绝 接口", printReturn = false) //日志
    @PostMapping(value = "/design_carNum5")
    public ServerResponse<Order> designCarNum5Handler(
            @RequestParam(value = "driverIsAgree") int driverIsAgree,  //"司机是否接受订单(0-司机还没有接受；1-司机接受订单；2-司机拒绝订单)"
            @RequestParam(value = "oid") String oid,                   //"订单号"
            @RequestParam(value = "carNum", required = false) String carNum,       //"车牌号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude    //"纬度"
    ) throws GlobalException {
        return orderRecordService.designCarNum5(driverIsAgree, oid, carNum, longitude, latitude);
    }

    @InvokeLog(description = "扫码接单-扫码 接口", printReturn = false) //日志
    @PostMapping(value = "/sweep_code5")
    public ServerResponse<Order> sweepCode5Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "fee", required = false) String fee,             //"友商收取金额"
            @RequestParam(value = "carNum", required = false) String carNum        //"车牌号"
    ) throws GlobalException {
        String id = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(id);
        if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();

//        if (driverInfoService.checkCarTareWeight(id, carNum)) return ServerResponse.createError("请先完善车辆皮重信息");

        String mobile = driverInfo.getMobile();
        if (StrUtil.isNotEmpty(fee) && NumFmtUtil.isNumeric(fee)) {
            fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        } else {
            fee = null;
        }
        return orderService.sweepCode4(mobile, carNum, gid, longitude, latitude, fee);
    }

    @InvokeLog(description = "指定车牌号-接单或拒绝 接口", printReturn = false) //日志
    @PostMapping(value = "/design_carNum4")
    public ServerResponse<Order> designCarNum6Handler(
            @RequestParam(value = "driverIsAgree") int driverIsAgree,  //"司机是否接受订单(0-司机还没有接受；1-司机接受订单；2-司机拒绝订单)"
            @RequestParam(value = "oid") String oid,                   //"订单号"
            @RequestParam(value = "carNum", required = false) String carNum,       //"车牌号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude    //"纬度"
    ) throws GlobalException {
        return orderRecordService.designCarNum6(driverIsAgree, oid, carNum, longitude, latitude);
    }

    @InvokeLog(description = "扫码接单-扫码 接口", printReturn = false) //日志
    @PostMapping(value = "/sweep_code4")
    public ServerResponse<Order> sweepCode6Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "fee", required = false) String fee,             //"友商收取金额"
            @RequestParam(value = "carNum", required = false) String carNum        //"车牌号"
    ) throws GlobalException {
        String id = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(id);
        if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();
        String mobile = driverInfo.getMobile();
        if (StrUtil.isNotEmpty(fee) && NumFmtUtil.isNumeric(fee)) {
            fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        } else {
            fee = null;
        }
        return orderService.sweepCode6(mobile, carNum, gid, longitude, latitude, fee);
    }

    @InvokeLog(description = "扫码接单-二维码 接口") //日志
    @PostMapping(value = "/two_dimension_code")
    public ServerResponse<DriverInfoData> twoDimensionCodeHandler(
            @RequestParam(value = "carNum", required = false) String carNum    //"车牌号"
    ) throws GlobalException {
        return orderRecordService.twoDimensionCode(carNum);
    }

    @InvokeLog(description = "订单信息查询 分页 接口", printReturn = false) //日志
    @PostMapping(value = "/order_query2")
    public ServerResponse<EGridResult> orderQuery2Handler(
            @RequestParam(value = "finishTag") int[] finishTag,        //"订单完成标识(0-下单成功,1-运输途中,2-订单完成)"
            @RequestParam(value = "finishTime", required = false) Long finishTime,//"完成时间"
            @RequestParam(value = "point", required = false) String point,        //"终点"
            @RequestParam(value = "tradePwd", required = false) String tradePwd,  //"交易密码"
            @RequestParam(value = "page") Integer page,                //"第几页"
            @RequestParam(value = "rows") Integer rows                 //"每页多少条"
    ) throws GlobalException {
//        return orderRecordService.orderQuery2(finishTag, finishTime, point, tradePwd, page, rows);
//        return orderRecordService.orderQuery3(finishTag, finishTime, point, tradePwd, page, rows);
        return orderRecordService.orderQuery4(finishTag, finishTime, point, tradePwd, page, rows);
    }

    @InvokeLog(description = "找货 接口", printReturn = false) //日志
    @PostMapping(value = "/lookGoods")
    public ServerResponse<List<GoodsInfoData>> lookGoodsHandler(
            @RequestParam(value = "point", required = false) String point  //"目的地查询"
    ) throws GlobalException {
        return ServerResponse.createError("暂停服务!");
//        return orderRecordService.lookGoods(point);
    }

    @InvokeLog(description = "找货 接口", printReturn = false) //日志
    @PostMapping(value = "/lookGoods2")
    public ServerResponse<List<GoodsInfoData>> lookGoods2Handler(
            @RequestParam(value = "point", required = false) String point  //"目的地查询"
    ) throws GlobalException {
        return orderRecordService.lookGoods2(point);
    }

    @InvokeLog(description = "抢单 接口") //日志
    @PostMapping(value = "/grab")
    public ServerResponse<String> grabHandler(
            @RequestParam(value = "gid") String gid,                               //"货物编号"
            @RequestParam(value = "carNum", required = false) String carNum,       //"车牌号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude    //"纬度"
    ) throws GlobalException {
        return orderRecordService.grab(gid, carNum, longitude, latitude);
    }

    @InvokeLog(description = "判断交易密码是否设置 接口")
    @PostMapping(value = "isTradePwd")
    public ServerResponse<String> isTradePwdHandler() throws GlobalException {
        return orderRecordService.isTradePwd();
    }

    @InvokeLog(description = "验证交易密码 接口")
    @PostMapping(value = "checkTradePwd")
    public ServerResponse<String> checkTradePwdHandler(
            @RequestParam(value = "pwd") String pwd    //"交易密码"
    ) throws GlobalException {
        return orderRecordService.checkTradePwd(pwd);
    }

    @InvokeLog(description = "出示订单二维码 接口", printReturn = false) //日志
    @PostMapping(value = "/order_dimension", produces = "application/json")
    public ServerResponse<String> OrderDimensionHandler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return orderDimensionService.getNewOrderDimension(oid);
    }

    @InvokeLog(description = "查询司机app或小程序接单应付金额 接口") //日志
    @PostMapping(value = "/search_fee2")
    public ServerResponse<Double> searchFee2Handler(
            @RequestParam(value = "oid", required = false) String oid, //"订单编号"
            @RequestParam(value = "gid", required = false) String gid, //"货运信息编号"
            @RequestParam(value = "fee", required = false) String fee  //"友商收取金额"
    ) throws GlobalException {
        if (StrUtil.isNotEmpty(fee) && NumFmtUtil.isNumeric(fee)) {
            fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        } else {
            fee = null;
        }
        return orderRecordService.searchFee(oid, gid, fee);
    }

    @InvokeLog(description = "查询司机app或小程序接单应付金额 接口") //日志
    @PostMapping(value = "/search_fee3")
    public ServerResponse<Map<String, Object>> searchFee3Handler(
            @RequestParam(value = "oid", required = false) String oid, //"订单编号"
            @RequestParam(value = "gid", required = false) String gid, //"货运信息编号"
            @RequestParam(value = "fee", required = false) String fee  //"友商收取金额"
    ) throws GlobalException {  // 接口未启用（本接口查询结果会指引前端使用平台余额或微信直接支付）。
        if (StrUtil.isNotEmpty(fee) && NumFmtUtil.isNumeric(fee)) {
            fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        } else {
            fee = null;
        }
        return orderRecordService.searchFee2(oid, gid, fee);
    }

    @InvokeLog(description = "二级单位预约提交 接口") //日志
    @PostMapping(value = "/appointment")
    public ServerResponse<String> submitAppointmentHandler(
            @RequestParam(value = "oid") String oid,           //"订单编号"
            @RequestParam(value = "startTime") Long startTime, //"预约的起始时间"
            @RequestParam(value = "endTime") Long endTime      //"预约的结束时间"
    ) throws GlobalException {
        return orderRecordService.submitAppointment(oid, startTime, endTime);
    }

    @InvokeLog(description = "查询预约排队 接口") //日志
    @PostMapping(value = "/search_queue")
    public ServerResponse<Object> searchQueueHandler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return orderRecordService.searchQueue(oid);
    }

    @InvokeLog(description = "修改订单车牌号 接口") //日志
    @PostMapping(value = "/update_order_carnum")
    public ServerResponse<String> updateOrderCarNumHandler(
            @RequestParam(value = "oid") String oid,       //"订单编号"
            @RequestParam(value = "carNum") String carNum  //"车牌号"
    ) throws GlobalException {
        return orderRecordService.updateOrderCarNum(oid, carNum);
    }

    @InvokeLog(description = "二级单位司机签到 接口", printReturn = false)   //日志
    @PostMapping(value = "/punch_clock")
    public ServerResponse<String> punchClockHandler(
            @RequestParam(value = "oid") String oid,              //订单编号
            @RequestParam(value = "imei", required = false) String imei,              //"设备唯一标识符"
            @RequestParam(value = "longitude") String longitude,    //"经度"
            @RequestParam(value = "latitude") String latitude       //"纬度"
    ) throws GlobalException {  // 接口调用了百度地图api鹰眼轨迹地理围栏服务，接口未使用。
        return orderRecordService.punchClock(oid, imei, longitude, latitude);
    }

    @InvokeLog(description = "二级单位司机签到 接口", printReturn = false)   //日志
    @PostMapping(value = "/punch_clock2")
    public ServerResponse<String> punchClock2Handler(
            @RequestParam(value = "oid") String oid,              //订单编号
            @RequestParam(value = "imei", required = false) String imei,              //"设备唯一标识符"
            @RequestParam(value = "longitude") String longitude,    //"经度"
            @RequestParam(value = "latitude") String latitude       //"纬度"
    ) throws GlobalException {
        return orderRecordService.punchClock2(oid, imei, longitude, latitude);
    }

    @InvokeLog(description = "防疫申报图片上传 接口") //日志
    @PostMapping(value = "/upload_quarantine_pho")
    public ServerResponse<String> uploadQuarantinePhoHandler(
            @RequestParam(value = "uploadPho") MultipartFile uploadPho, //"上传照片"
            @RequestParam(value = "type") String type                   //"照片类型--健康码照片（核酸检测 和 疫苗接种）:healthCodePho,行程卡照片:travelCardPho,体温照片:temperaturePro
    ) throws GlobalException {
        return orderRecordService.uploadQuarantinePho(uploadPho, type);
    }

    @InvokeLog(description = "司机提交防疫申报 接口") //日志
    @PostMapping(value = "/add_quarantine")
    public ServerResponse<String> addQuarantineHandler(
            @RequestParam(value = "oid") String oid,    //"订单编号"
            @RequestParam(value = "healthCodePho", required = false) String healthCodePho,    //"健康码照片 （核酸检测 和 疫苗接种）"
            @RequestParam(value = "nucleicAcidPho", required = false) String nucleicAcidPho,    //"核酸检测照片"
            @RequestParam(value = "vaccinationPho", required = false) String vaccinationPho,    //"疫苗接种照片"
            @RequestParam(value = "travelCardPho", required = false) String travelCardPho,    //"行程卡照片"
            @RequestParam(value = "touchPro", required = false) String touchPro,    //"是否同行或密接照片"
            @RequestParam(value = "temperaturePro", required = false) String temperaturePro,    //"体温照片"
            @RequestParam(value = "temperature", required = false) String temperature    //"体温数值"
    ) throws GlobalException {
//        return orderRecordService.addQuarantineInfo(oid, healthCodePho, travelCardPho, temperaturePro, temperature);
        return orderRecordService.addQuarantineInfo2(oid, healthCodePho, travelCardPho, nucleicAcidPho, vaccinationPho, touchPro, temperaturePro, temperature);
    }

    @InvokeLog(description = "司机提交抢号 接口") //日志
    @PostMapping(value = "/grab_number")
    public ServerResponse<String> grabNumberHandler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return orderRecordService.grabNumber(oid);
    }

    @InvokeLog(description = "司机查询tranStatus3，5订单 接口") //日志
    @PostMapping(value = "/get_35tran_status")
    public ServerResponse<DriverOrderData> get35TranStatusHandler() throws GlobalException {
//        return orderRecordService.get35TranStatusOrder();
//        return orderRecordService.get35TranStatusOrder2();
        return orderRecordService.get35TranStatusOrder3();
    }

    @InvokeLog(description = "微信请求生成支付订单-司机接单付款 接口") //日志
    @PostMapping(value = "/pay_order")
    public ServerResponse<Map<String, String>> payOrderHandler(
            @RequestParam(value = "gid", required = false) String gid,
            @RequestParam(value = "oid", required = false) String oid,
            @RequestParam(value = "dvrGoodsId", required = false) String dvrGoodsId,
            @RequestParam(value = "fee", required = false) String fee,             //"友商收取金额"
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "carNum", required = false) String carNum        //"车牌号"
    ) throws GlobalException {
        /*if (StrUtil.isEmpty(openid)) openid = driverInfoService.getByPK(ShiroUtils.getUserId()).getPhoneId();

        return orderRecordService.orderRequestPayWithProfitSharing(gid, oid, dvrGoodsId, openid, carNum, longitude, latitude, fee);*/
        return ServerResponse.createError("接口未启用");
    }

    @InvokeLog(description = "司机微信链接接单 接口", printReturn = false) //日志
    @PostMapping(value = "/wx_link_order")
    public ServerResponse<Order> wxLinkOrderHandler(
            @RequestParam(value = "dvrGoodsId") String dvrGoodsId,       //"货运信息编号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "fee", required = false) String fee,             //"友商收取金额"
            @RequestParam(value = "carNum", required = false) String carNum        //"车牌号"
    ) throws GlobalException {
        /*if (StrUtil.isNotEmpty(fee) && NumFmtUtil.isNumeric(fee)) {
            fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        } else {
            fee = null;
        }
        return orderRecordService.wxLinkOrder(carNum, dvrGoodsId, longitude, latitude, fee);*/
        return ServerResponse.createError("接口停用");
    }

    @InvokeLog(description = "司机微信链接接单 接口", printReturn = false) //日志
    @PostMapping(value = "/wx_link_order2")
    public ServerResponse<Order> wxLinkOrder2Handler(
            @RequestParam(value = "dvrGoodsId") String dvrGoodsId,       //"货运信息编号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "fee", required = false) String fee,             //"友商收取金额"
            @RequestParam(value = "carNum", required = false) String carNum        //"车牌号"
    ) throws GlobalException {
        if (StrUtil.isNotEmpty(fee) && NumFmtUtil.isNumeric(fee)) {
            fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        } else {
            fee = null;
        }
        return orderRecordService.wxLinkOrder2(carNum, dvrGoodsId, longitude, latitude, fee);
    }

    @InvokeLog(description = "二级单位模糊查询 接口") //日志
    @PostMapping(value = "/search_sub_unit")
    public ServerResponse<List<CompanyUnit>> searchSubUnitHandler(
            @RequestParam(value = "unitName", required = false) String unitName       //"二级单位关键字"
    ) throws GlobalException {
        return orderRecordService.searchSubUnit(unitName);
    }

    @InvokeLog(description = "司机接单前提交防疫申报 接口") //日志
    @PostMapping(value = "/add_quarantine_before")
    public ServerResponse<String> addQuarantineBeforeHandler(
            @RequestParam(value = "defaultDownUnit") String defaultDownUnit,    //"二级单位编号"
            @RequestParam(value = "healthCodePho", required = false) String healthCodePho,    //"健康码照片 （核酸检测 和 疫苗接种）"
            @RequestParam(value = "nucleicAcidPho", required = false) String nucleicAcidPho,    //"核酸检测照片"
            @RequestParam(value = "vaccinationPho", required = false) String vaccinationPho,    //"疫苗接种照片"
            @RequestParam(value = "travelCardPho", required = false) String travelCardPho,    //"行程卡照片"
            @RequestParam(value = "touchPro", required = false) String touchPro,    //"是否同行或密接照片"
            @RequestParam(value = "temperaturePro", required = false) String temperaturePro,    //"体温照片"
            @RequestParam(value = "temperature", required = false) String temperature    //"体温数值"
    ) throws GlobalException {
        return orderRecordService.addQuarantineInfoBefore(defaultDownUnit, healthCodePho, travelCardPho, nucleicAcidPho, vaccinationPho, touchPro, temperaturePro, temperature);
    }

    @InvokeLog(description = "查询订单车辆轨迹 接口") //日志
    @PostMapping(value = "/get_historical_track")
    public ServerResponse<String> getHistoricalTrackHandler(
            @RequestParam(value = "oid") String oid       //"订单编号"
    ) throws GlobalException {
        return orderRecordService.getHistoricalTrack(oid);
    }
    @InvokeLog(description = "二级单位模糊查询 接口") //日志
    @PostMapping(value = "/get_photo_by_oid")
    public ServerResponse<Map<String, String>> getPhotoByOidHandler(
            @RequestParam(value = "oid") String oid       //"订单编号"
    ) throws GlobalException {
        return orderRecordService.getPhotoByOid(oid);
    }

    @InvokeLog(description = "按订单号查询二级单位求助热线 接口") //日志
    @PostMapping(value = "/get_hotline_by_oid")
    public ServerResponse<String> getHotLineByOidHandler(
            @RequestParam(value = "oid") String oid       //"订单编号"
    ) throws GlobalException {
        return orderRecordService.getHotLineByOid(oid);
    }

    @InvokeLog(description = "司机查询订单是否需要录入对方净重 接口") //日志
    @PostMapping(value = "/check_in_net_weight")
    public ServerResponse<Map<String, Object>> checkInNetWeightHandler(
            @RequestParam(value = "oid") String oid       //"订单编号"
    ) throws GlobalException {
        return orderRecordService.checkInNetWeight(oid);
    }

    @InvokeLog(description = "司机录入对方净重 接口") //日志
    @PostMapping(value = "/update_in_net_weight")
    public ServerResponse<String> updateOrderInNetWeightHandler(
            @RequestParam(value = "oid") String oid,       //"订单编号"
            @RequestParam(value = "inNetWeight", required = false) Double inNetWeight,       //"对方净重"
//            @RequestParam(value = "inNetWeightPho", required = false) String inNetWeightPho,       //"煤单照片"
            @RequestParam(value = "loadPound", required = false) String loadPound,      //"收货业务对方装货单号"
            @RequestParam(value = "loadPoundPho", required = false) String loadPoundPho,       //"煤单照片"
            @RequestParam(value = "loadTime", required = false) Long loadTime      //"收货业务对方装货时间"
    ) throws GlobalException {
        return orderRecordService.updateInNetWeight(oid, inNetWeight, loadPound, loadPoundPho, loadTime);
    }

    @InvokeLog(description = "查询车型和装卸费列表 接口") //日志
    @PostMapping(value = "/cost_list")
    public ServerResponse<List<VehicleTypeCost>> costListHandler(
            @RequestParam(value = "oid") String oid,                            //"订单编号"
            @RequestParam(value = "bizContractCode") String bizContractCode     //合同编号
    ) throws GlobalException {
        TradeContract tradeContract = tradeContractService.get("bizContractCode", bizContractCode);
        if (tradeContract != null && tradeContract.getVehicleTypeCostList() != null) {
            return ServerResponse.createSuccess(tradeContract.getVehicleTypeCostList());
        } else {
            return ServerResponse.createSuccess(new ArrayList<>());
        }
    }

    @InvokeLog(description = "查询订单装卸费信息支付结果 接口") //日志
    @PostMapping(value = "/search_cost_pay")
    public ServerResponse<HandlingCostPojo> searchCostPayHandler(
            @RequestParam(value = "oid") String oid,                            //"订单编号"
            @RequestParam(value = "bizContractCode") String bizContractCode     //合同编号
    ) throws GlobalException {
        return orderRecordService.searchCostPay(oid, bizContractCode);
    }
}
