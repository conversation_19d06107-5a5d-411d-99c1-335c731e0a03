package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.service.IWebDriverPoolService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/customer/driverPool")
public class WebDriverPoolController extends BaseController {

    @Resource
    private IWebDriverPoolService webDriverPoolService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> driPoolListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webDriverPoolService.driPoolList(page, rows));
    }
}
