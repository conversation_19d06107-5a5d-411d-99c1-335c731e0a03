package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.manage.service.ICarInfoService;
import com.erdos.coal.park.web.app.entity.CarInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//"字典表查询列表"
@RestController
@RequestMapping("/api/manage/dic")
public class CarInfoController extends BaseController {

    @Resource
    private ICarInfoService carInfoService;

    //@InvokeLog(description = "车型查询 接口") //日志
    @PostMapping(value = "/car_info")
    public ServerResponse<List<CarInfo>> updateA<PERSON><PERSON><PERSON><PERSON>() throws GlobalException {
        return ServerResponse.createSuccess(carInfoService.list());
    }

}
