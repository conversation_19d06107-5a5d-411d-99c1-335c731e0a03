package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.IOrderRecordDao;
import com.erdos.coal.park.api.driver.entity.*;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.service.*;
import com.erdos.coal.park.api.manage.service.ICarInfoService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;
import com.erdos.coal.park.web.sys.service.IPlatFormAccountService;
import com.erdos.coal.park.web.sys.service.ISysUnitAccountService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayDriverConfig;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.transaction.wxpay.service.WXPayWechatConfig;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.UpdateResult;
import dev.morphia.geo.GeoJson;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("wechatOrderRecordService")
public class WechatOrderRecordServiceImpl extends BaseMongoServiceImpl<OrderRecord, IOrderRecordDao> implements IWechatOrderRecordService {

    @Resource
    private IOrderService orderService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IOrderLogisticsService orderLogisticsService;
    @Resource
    private IGoodsService goodsService;
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private IWxPrepaidService wxPrepaidService;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private WXPayWechatConfig config;
    @Resource
    private WXPayDriverConfig config2;
    @Resource
    private ILockedService lockedService;
    @Resource
    private IOrderRecordService orderRecordService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private ISysUnitAccountService unitAccountService;
    @Resource
    private IPlatFormAccountService platFormAccountService;
    @Resource
    private ICarInfoService carInfoService;
    @Resource
    private IDriverInfoService driverInfoService;

    @Resource
    private MongoClient client;

    @Override
//    public ServerResponse<Map<String, String>> batch(String carNum, String gid, String[] oids, String longitude, String latitude, String fee) {
    public ServerResponse<Map<String, String>> batch(String carNum, String carInfoId, String gid, String[] oids, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(carNum)) return ServerResponse.createError("车号不能为空！");

        String userId = ShiroUtils.getUserId();
        //真的微信小程序司机
        //ServerResponse response = isOrderTaking(userId, carNum, gid, oids, longitude, latitude);
        //假的微信小程序司机，实际是app司机
        ServerResponse response = isOrderTaking2(userId, carNum, gid, oids, longitude, latitude);
        if (response.getStatus() == 0) { //司机可以接单
            //真的微信小程序司机
            //return batchOrderTaking(carNum, carInfoId, userId, gid, oids, longitude, latitude, fee); //统一下单
            //假的微信小程序司机，实际是app司机
            return batchOrderTaking2(carNum, userId, gid, oids, longitude, latitude, fee); //统一下单
        } else {
            return response;   //司机不能接单的错误信息
        }
    }

    @Override
    public ServerResponse<Map<String, String>> search(String transactionId, String outTradeNo, String longitude, String latitude, String gid, String oid) {

        Map<String, String> map = new HashMap<>();
        WxPrepaid wxPrepaid = null;

        if (StrUtil.isEmpty(transactionId)) {
            if (StrUtil.isEmpty(outTradeNo)) {
                return ServerResponse.createError("微信订单号/商户订单号不能为空");
            } else {
                map.put("out_trade_no", outTradeNo);
                wxPrepaid = wxPrepaidService.get(wxPrepaidService.createQuery().filter("outTradeNo", outTradeNo));
            }
        } else {
            map.put("transaction_id", transactionId);
        }

        String userId = ShiroUtils.getUserId();

        if (wxPrepaid == null)
            return ServerResponse.createError("请输入商户订单号");
        Map<String, String> mapResult = payResult(map, longitude, latitude, userId, gid, oid, wxPrepaid.getShareFee() == null ? "" : wxPrepaid.getShareFee().toString());//加锁
        if (ObjectUtil.isNull(mapResult)) {
            throw new GlobalException(ResponseCode.PAY_QUERY_FAIL);
        }
        return ServerResponse.createSuccess("查询成功", mapResult);
    }

    @Override
    public ServerResponse<Map<String, String>> search2(String transactionId, String outTradeNo, String longitude, String latitude, String gid, String oid) {
        /*司机若想接单，必须先设置使用的车牌号 */
        String userId = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(userId);
        if (StrUtil.isEmpty(driverInfo.getCarNum())) return ServerResponse.createError("请先设置车牌号，再接单");

        Map<String, String> map = new HashMap<>();
        WxPrepaid wxPrepaid = null;

        if (StrUtil.isEmpty(transactionId)) {
            if (StrUtil.isEmpty(outTradeNo)) {
                return ServerResponse.createError("微信订单号/商户订单号不能为空");
            } else {
                map.put("out_trade_no", outTradeNo);
                wxPrepaid = wxPrepaidService.get(wxPrepaidService.createQuery().filter("outTradeNo", outTradeNo));
            }
        } else {
            map.put("transaction_id", transactionId);
        }


        if (wxPrepaid == null)
            return ServerResponse.createError("请输入商户订单号");
        Map<String, String> mapResult = payResult2(map, longitude, latitude, userId, gid, oid, wxPrepaid.getShareFee() == null ? "" : wxPrepaid.getShareFee().toString());//加锁
        if (ObjectUtil.isNull(mapResult)) {
            throw new GlobalException(ResponseCode.PAY_QUERY_FAIL);
        }
        return ServerResponse.createSuccess("查询成功", mapResult);
    }

    @Override
    @Transactional
    public WxResult payResult(Map<String, String> map) {

        WxResult wxResult = wxResultService.get("transactionId", map.get("transaction_id"));

        if (ObjectUtil.isNull(wxResult)) {
            wxResult = wxResultService.get("outTradeNo", map.get("out_trade_no"));

            if (ObjectUtil.isNull(wxResult)) {

                WxPrepaid wxPrepaid = wxPrepaidService.get("outTradeNo", map.get("out_trade_no"));
                //Map<String, String> mapResult = payResult(map, null, null, wxPrepaid.getCdid(), null, wxPrepaid.getOid(), wxPrepaid.getShareFee() == null ? "" : wxPrepaid.getShareFee().toString());//加锁
                Map<String, String> mapResult = payResult2(map, null, null, wxPrepaid.getCdid(), null, wxPrepaid.getOid(), wxPrepaid.getShareFee() == null ? "" : wxPrepaid.getShareFee().toString());//加锁
                if (mapResult == null) {
                    return null;
                } else {
                    wxResult = wxResultService.get("outTradeNo", map.get("out_trade_no"));
                }
            }
        }
        return wxResult;
    }

    @Override
    public ServerResponse<List<DriverOrderData>> queryOrder(int[] finishTag, Long finishTime) {
        String objectId = ShiroUtils.getUserId();
        List<DriverOrderData> drList = new ArrayList<>();

        //TODO:1.小程序查询当前司机的所有订单信息
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("did", objectId);
        query.filter("driverIsAgree != ", 2);//未拒绝的订单
        query.filter("finishTag in ", finishTag);

        Long startTime = finishTime != null ? IdUnit.weeHours(finishTime, "00:00:00", 0) : null;
        Long endTime = finishTime != null ? IdUnit.weeHours(finishTime, "00:00:00", 1) : null;
        if (startTime != null && startTime == -1) {
            return ServerResponse.createError("时间参数格式不符合秒或毫秒级时间戳长度");
        } else if (startTime != null) {
            query.filter("updateTime >= ", startTime);
        }
        if (endTime != null && endTime == -1) {
            return ServerResponse.createError("时间参数格式不符合秒或毫秒级时间戳长度");
        } else if (endTime != null) {
            query.filter("updateTime <= ", endTime);
        }
        query.order(Sort.descending("updateTime"));

        List<OrderTaking> orderTakingList = query.find().toList();

        for (OrderTaking taking : orderTakingList) {
            Order order = orderService.get("oid", taking.getOid());
            DriverOrderData driverOrderData = new DriverOrderData();
            driverOrderData.setBeginPoint(order.getBeginPoint());
            driverOrderData.setPrice(order.getPrice());
            driverOrderData.setEndPoint(order.getEndPoint());
            driverOrderData.setTradeName(order.getTradeName());
            driverOrderData.setDistance(order.getDistance());
            driverOrderData.setTolls(order.getTolls());
            //driverOrderData.setCarNum(wechatDriverInfo.getCarNum());
            driverOrderData.setCarNum(order.getCarNum());
            driverOrderData.setOid(taking.getOid());
            driverOrderData.setOrderDate(taking.getUpdateTime());
            driverOrderData.setCreateDate(taking.getCreateTime());
            driverOrderData.setFinishTag(taking.getFinishTag());
            driverOrderData.setMold(order.getMold());
            driverOrderData.setDelete(taking.getDelete());//客商是否撤单
            driverOrderData.setCheck(order.getIsCheck());//订单撤销退款是否审核
            drList.add(driverOrderData);
        }
        return ServerResponse.createSuccess("查询成功", drList);
    }

    @Override
    public ServerResponse<GoodsInfoData> searchGoods(String gid) {
        String userId = ShiroUtils.getUserId();
        Query<Order> query = orderService.createQuery();
        query.filter("gid", gid);
        //query.filter("isWeChat", 1);
        List<Order> orders = orderService.list(query);// 货运信息下全部订单集合
//        Goods g = goodsService.get(query);
        if (orders.size() == 0)
            return ServerResponse.createError("货运信息下无订单");

        Query<OrderTaking> takingQuery = orderTakingService.createQuery().filter("gid", gid).filter("did", userId);
        OrderTaking taking = orderTakingService.get(takingQuery);

        //TODO:处理要返回的数据包装到GoodsInfoData对象中
        Goods g = goodsService.get("gid", gid);
        GoodsInfoData result = new GoodsInfoData();
        BeanUtils.copyProperties(g, result);
        if (ObjectUtil.isNotNull(taking))
            result.setDriverIsAgree(taking.getDriverIsAgree());
//        result.setFees(new BigDecimal(orderRecordService.searchFee(gid, null)));
        result.setFees(new BigDecimal(0));

        return ServerResponse.createSuccess("查询成功", result);
    }

    //加锁    查询支付结果，保存订单相关信息，并返回
    @Transactional
    Map<String, String> payResult(Map<String, String> map, String longitude, String latitude, String userId, String gid, String oid, String shareFee) {
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.getByPK(userId);

        boolean lock = lockedService.getLock(wechatDriverInfo.getOpenid(), SysConstants.LockType.WX_PAY_RESULT.getType());//加锁
        if (!lock) {
            return null;
        } else {
            try {

                ClientSession clientSession = client.startSession();
                WxResult wxResult = wxResultService.get("transactionId", map.get("transaction_id"));

                MongoCollection<Document> orderCollection = orderService.getCollection();
                MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
                MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
                MongoCollection<Document> wechatDriverCollection = wechatDriverInfoService.getCollection();
                MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
                MongoCollection<Document> unitAccountCollection = unitAccountService.getCollection();
                MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
                Date date = new Date();
                Map<String, Object> updMap = new HashMap<>();
                Map<String, Object> params = new HashMap<>();

                if (oid == null) {
                    Query<Order> query = orderService.createQuery();
                    query.filter("gid", gid);
                    query.filter("locked", true);
                    Order order = orderService.get(query);//查询货运信息下一条锁定订单，如果付款失败，释放该订单
                    if (order == null)
                        return null;
                    oid = order.getOid();
                }

                if (ObjectUtil.isNull(wxResult)) {
                    wxResult = wxResultService.get("outTradeNo", map.get("out_trade_no"));

                    if (ObjectUtil.isNull(wxResult)) {
                        try {
                            WXPay wxpay = new WXPay(config, wxPayConstants.notifyUrl, true, false);
                            map = wxpay.orderQuery(map);
                        } catch (Exception e) {
                            e.printStackTrace();
                            unLock(oid);
                            unOrderLoc(oid);    //删除物流信息
                            return null;
                        }
                        //如trade_state不为 SUCCESS，则只返回out_trade_no（必传）和attach（选传）。
                        if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS") && map.get("trade_state").equals("SUCCESS")) {

                            WxPrepaid wxPrepaid = wxPrepaidService.get("outTradeNo", map.get("out_trade_no"));
                            if (ObjectUtil.isNull(wxPrepaid)) {
                                unLock(oid);
                                unOrderLoc(oid);    //删除物流信息
                                return null;
                            }

                            MongoCollection<Document> resultCollection = wxResultService.getCollection();
                            MongoCollection<Document> prepaidCollection = wxPrepaidService.getCollection();
                            try {
                                clientSession.startTransaction();
                                int cashFee = Utils.parseInt(map.get("cash_fee"), 0);
                                wxResult = new WxResult();
                                wxResult.setReturnCode(map.get("return_code"));
                                wxResult.setBankType(map.get("bank_type"));
                                wxResult.setCashFee(cashFee);
                                wxResult.setFeeType(map.get("fee_type"));
                                wxResult.setIsSubscribe(map.get("is_subscribe"));
                                wxResult.setNonceStr(map.get("nonce_str"));
                                wxResult.setOpenid(map.get("openid"));
                                wxResult.setOutTradeNo(map.get("out_trade_no"));
                                wxResult.setResultCode(map.get("result_code"));
                                wxResult.setSign(map.get("sign"));
                                wxResult.setTimeEnd(map.get("time_end"));
                                Integer l = Utils.parseInt(map.get("total_fee"), 0);
                                wxResult.setTotalFee(l);
                                wxResult.setTradeType(map.get("trade_type"));
                                wxResult.setTransactionId(map.get("transaction_id"));
                                wxResult.setUpdateTime(date.getTime());

                                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                                document.append("createTime", date);
                                resultCollection.insertOne(clientSession, document);//保存支付成功结果

                                Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
                                params.clear();
                                updMap.clear();
                                params.put("pay", true);
                                params.put("updateTime", date.getTime());
                                updMap.put("$set", params);
                                prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(updMap)));//更新预支付表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

                            } catch (Exception e) {
                                clientSession.abortTransaction();
                                unLock(oid);
                                unOrderLoc(oid);    //删除物流信息
                                return null;
                            }
                        } else {    //支付失败
                            clientSession.abortTransaction();
                            unOrderLoc(oid);    //删除物流信息
                            unLock(oid);
                            return null;
                        }

                        if (StrUtil.isEmpty(gid))//回调时没有gid
                            gid = orderService.get("oid", oid).getGid();

                        Goods goods = goodsService.get("gid", gid);
                        Order order = orderService.get("oid", oid);

                        //TODO 1.修改order
                        Document filter = new Document("oid", oid);
                        filter.append("locked", true);
                        filter.append("updateTime", order.getUpdateTime());

                        int[] tempFee = orderRecordService.getPTFee(goods, userId, SysConstants.UserType.UT_WECHAT.toString());
                        int ptFees = tempFee[0] + tempFee[1];//司机接单应付平台费用

                        // 司机付款金额分别存入一/二 级收/发 单位和客商/友商账户
                        Map<String, Object> orderConMap = new HashMap<>();
                        int cusFee = 0;

                        if (goods.getFees1In() > 0 && order.getFees1In() == 0) {
                            orderConMap.put("fees1In", goods.getFees1In());
                            orderConMap.put("payerId1In", userId);

                            String inUnitCode = order.getInUnitCode();
                            SysUnit sysUnit = sysUnitService.get("code", inUnitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees1In()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }
                        if (goods.getFees1Out() > 0 && order.getFees1Out() == 0) {
                            orderConMap.put("fees1Out", goods.getFees1Out());
                            orderConMap.put("payerId1Out", userId);

                            String unitCode = order.getOutUnitCode();
                            SysUnit sysUnit = sysUnitService.get("code", unitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees1Out()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }
                        if (goods.getFees2In() > 0 && order.getFees2In() == 0) {
                            orderConMap.put("fees2In", goods.getFees2In());
                            orderConMap.put("payerId2In", userId);

                            String unitCode = order.getInDefaultDownUnit();
                            SysUnit sysUnit = sysUnitService.get("code", unitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees2In()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }
                        if (goods.getFees2Out() > 0 && order.getFees2Out() == 0) {
                            orderConMap.put("fees2Out", goods.getFees2Out());
                            orderConMap.put("payerId2Out", userId);

                            String unitCode = order.getOutDefaultDownUnit();
                            SysUnit sysUnit = sysUnitService.get("code", unitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees2Out()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }

                        if (StrUtil.isNotEmpty(shareFee)) {
                            cusFee = Integer.parseInt(shareFee);
                        } else {
                            cusFee = goods.getFees3();
                        }
                        if (cusFee > 0) {
                            orderConMap.put("shareFees", cusFee);
                            orderConMap.put("driverPayerId", userId);

                            cusAccountCollection.insertOne(clientSession, customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(cusFee), 9, oid, date));
                        }

                        int balanceFees = ptFees - order.getFeesOut() - order.getFeesIn() - order.getFees();//客商或单位代付平台金额不足，司机补
                        if (balanceFees > 0) {
                            orderConMap.put("balanceFees", balanceFees);
                            orderConMap.put("driverPayerId", userId);

                            //司机有支付平台的钱，则平台账户添加数据
                            pfAccountCollection.insertOne(clientSession, platFormAccountService.createPlatFormAccountDoc(userId, new BigDecimal(balanceFees), 8, oid, date));
                        }

                        updMap.clear();
                        orderConMap.put("carNum", wechatDriverInfo.getCarNum());
                        orderConMap.put("updateTime", date.getTime());
                        orderConMap.put("transactionId", wxResult.getTransactionId());//微信订单号
                        orderConMap.put("outTradeNo", wxResult.getOutTradeNo());//商户订单号
                        orderConMap.put("locked", false);
                        updMap.put("$set", orderConMap);
                        UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(updMap)));
                        if (a.getModifiedCount() <= 0) {
                            return null;
                        }

                        //TODO 2.添加orderTaking ,生成接单信息
                        DriverInfo driverInfo = new DriverInfo();
                        driverInfo.setObjectId(wechatDriverInfo.getObjectId());
                        driverInfo.setCarNum(wechatDriverInfo.getCarNum());
                        int finishTag = goods.getMold() == 1 ? 1 : 0;
                        Document otDoc = orderTakingService.createOTDoc(oid, goods, driverInfo, finishTag, order.getIsHand(), 1, date);
                        orderTakingCollection.insertOne(clientSession, otDoc);

                        if (goods.getMold() == 1 && StrUtil.isNotEmpty(latitude) && StrUtil.isNotEmpty(longitude)) {        //收货物流模式
                            //先把库里原有的该订单物流信息删除再添加订单物流信息
                            unOrderLoc(oid);
                            OrderLogistics ol = new OrderLogistics();
                            ol.setOid(oid);
                            ol.setFinishTag(1);
                            ol.setLatitude(latitude);       //纬度
                            ol.setLongitude(longitude);      //经度
                            ol.setUpdateTime(date.getTime());
                            Document olDoc = Document.parse(JSONObject.toJSONString(ol));
                            Point point = GeoJson.point(Double.valueOf(latitude), Double.valueOf(longitude));
                            Document geometry = new Document();
                            geometry.append("type", "Point");
                            geometry.append("coordinates", point.getCoordinates());
                            olDoc.append("geometry", geometry);
                            olDoc.append("createTime", date);
                            orderLogisticsCollection.insertOne(clientSession, olDoc);//保存物流信息
                        }

                        //TODO 3.微信小程序次数+1
                        Document doc = new Document("openid", wechatDriverInfo.getOpenid());
                        doc.append("updateTime", wechatDriverInfo.getUpdateTime());
                        updMap.clear();
                        params.clear();
                        params.put("carNum", wechatDriverInfo.getCarNum());
                        params.put("updateTime", date.getTime());
                        params.put("times", wechatDriverInfo.getTimes() + 1);//次数+1
                        updMap.put("$set", params);
                        wechatDriverCollection.updateOne(clientSession, doc, Document.parse(JSONObject.toJSONString(updMap)));

                        clientSession.commitTransaction();
                    }
                }

                Map<String, String> result = new HashMap<>();
                result.put("oid", oid);
                result.put("carNum", wechatDriverInfo.getCarNum());
                result.put("gid", gid);

                return result;

            } finally {

                lockedService.unLock(wechatDriverInfo.getOpenid(), SysConstants.LockType.WX_PAY_RESULT.getType());//解锁
            }
        }
    }

    //加锁    查询支付结果，保存订单相关信息，并返回
    @Transactional
    Map<String, String> payResult2(Map<String, String> map, String longitude, String latitude, String userId, String gid, String oid, String shareFee) {
        boolean lock = lockedService.getLock(userId, SysConstants.LockType.WX_PAY_RESULT.getType());//加锁
        if (!lock) {
            return null;
        } else {
            WxPrepaid wxPrepaid = wxPrepaidService.get("transactionId", map.get("transaction_id"));
            if (ObjectUtil.isNull(wxPrepaid)) {
                unLock(oid);
                unOrderLoc(oid);    //删除物流信息
                return null;
            }

            DriverInfo driverInfo = driverInfoService.getByPK(wxPrepaid.getDriverId());
            driverInfo.setCarNum(wxPrepaid.getCarNum());

            try {
                ClientSession clientSession = client.startSession();
                WxResult wxResult = wxResultService.get("transactionId", map.get("transaction_id"));

                MongoCollection<Document> orderCollection = orderService.getCollection();
                MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
                MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
                MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
                MongoCollection<Document> unitAccountCollection = unitAccountService.getCollection();
                MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
                Date date = new Date();
                Map<String, Object> updMap = new HashMap<>();
                Map<String, Object> params = new HashMap<>();

                if (oid == null) {
                    Query<Order> query = orderService.createQuery();
                    query.filter("gid", gid);
                    query.filter("locked", true);
                    Order order = orderService.get(query);//查询货运信息下一条锁定订单，如果付款失败，释放该订单
                    if (order == null)
                        return null;
                    oid = order.getOid();
                }


                if (ObjectUtil.isNull(wxResult)) {
                    wxResult = wxResultService.get("outTradeNo", map.get("out_trade_no"));

                    if (ObjectUtil.isNull(wxResult)) {
                        try {
                            WXPay wxpay = new WXPay(config2, wxPayConstants.notifyUrl, true, false);
                            map = wxpay.orderQuery(map);
                        } catch (Exception e) {
                            e.printStackTrace();
                            unLock(oid);
                            unOrderLoc(oid);    //删除物流信息
                            return null;
                        }
                        //如trade_state不为 SUCCESS，则只返回out_trade_no（必传）和attach（选传）。
                        if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS") && map.get("trade_state").equals("SUCCESS")) {

                            MongoCollection<Document> resultCollection = wxResultService.getCollection();
                            MongoCollection<Document> prepaidCollection = wxPrepaidService.getCollection();
                            try {
                                clientSession.startTransaction();
                                int cashFee = Utils.parseInt(map.get("cash_fee"), 0);
                                wxResult = new WxResult();
                                wxResult.setReturnCode(map.get("return_code"));
                                wxResult.setBankType(map.get("bank_type"));
                                wxResult.setCashFee(cashFee);
                                wxResult.setFeeType(map.get("fee_type"));
                                wxResult.setIsSubscribe(map.get("is_subscribe"));
                                wxResult.setNonceStr(map.get("nonce_str"));
                                wxResult.setOpenid(map.get("openid"));
                                wxResult.setOutTradeNo(map.get("out_trade_no"));
                                wxResult.setResultCode(map.get("result_code"));
                                wxResult.setSign(map.get("sign"));
                                wxResult.setTimeEnd(map.get("time_end"));
                                Integer l = Utils.parseInt(map.get("total_fee"), 0);
                                wxResult.setTotalFee(l);
                                wxResult.setTradeType(map.get("trade_type"));
                                wxResult.setTransactionId(map.get("transaction_id"));
                                wxResult.setUpdateTime(date.getTime());

                                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                                document.append("createTime", date);
                                resultCollection.insertOne(clientSession, document);//保存支付成功结果

                                Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
                                params.clear();
                                updMap.clear();
                                params.put("pay", true);
                                params.put("updateTime", date.getTime());
                                updMap.put("$set", params);
                                prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(updMap)));//更新预支付表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

                            } catch (Exception e) {
                                clientSession.abortTransaction();
                                unLock(oid);
                                unOrderLoc(oid);    //删除物流信息
                                return null;
                            }
                        } else {    //支付失败
                            clientSession.abortTransaction();
                            unOrderLoc(oid);    //删除物流信息
                            unLock(oid);
                            return null;
                        }

                        if (StrUtil.isEmpty(gid))//回调时没有gid
                            gid = orderService.get("oid", oid).getGid();

                        Goods goods = goodsService.get("gid", gid);
                        Order order = orderService.get("oid", oid);

                        //TODO 1.修改order
                        Document filter = new Document("oid", oid);
                        filter.append("locked", true);
                        filter.append("updateTime", order.getUpdateTime());

                        int[] tempFee = orderRecordService.getPTFee(goods, userId, SysConstants.UserType.UT_WECHAT.toString());
                        int ptFees = tempFee[0] + tempFee[1];//司机接单应付平台费用

                        // 司机付款金额分别存入一/二 级收/发 单位和客商/友商账户
                        Map<String, Object> orderConMap = new HashMap<>();
                        int cusFee;

                        if (goods.getFees1In() > 0 && order.getFees1In() == 0) {
                            orderConMap.put("fees1In", goods.getFees1In());
                            orderConMap.put("payerId1In", userId);

                            String inUnitCode = order.getInUnitCode();
                            SysUnit sysUnit = sysUnitService.get("code", inUnitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees1In()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }
                        if (goods.getFees1Out() > 0 && order.getFees1Out() == 0) {
                            orderConMap.put("fees1Out", goods.getFees1Out());
                            orderConMap.put("payerId1Out", userId);

                            String unitCode = order.getOutUnitCode();
                            SysUnit sysUnit = sysUnitService.get("code", unitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees1Out()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }
                        if (goods.getFees2In() > 0 && order.getFees2In() == 0) {
                            orderConMap.put("fees2In", goods.getFees2In());
                            orderConMap.put("payerId2In", userId);

                            String unitCode = order.getInDefaultDownUnit();
                            SysUnit sysUnit = sysUnitService.get("code", unitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees2In()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }
                        if (goods.getFees2Out() > 0 && order.getFees2Out() == 0) {
                            orderConMap.put("fees2Out", goods.getFees2Out());
                            orderConMap.put("payerId2Out", userId);

                            String unitCode = order.getOutDefaultDownUnit();
                            SysUnit sysUnit = sysUnitService.get("code", unitCode);
                            SysUnitAccount sysUnitAccount = new SysUnitAccount();
                            sysUnitAccount.setUid(sysUnit.getObjectId().toString());
                            sysUnitAccount.setUserId(userId);
                            sysUnitAccount.setOid(oid);
                            sysUnitAccount.setType(1);
                            sysUnitAccount.setTotalFee(new BigDecimal(goods.getFees2Out()));
                            sysUnitAccount.setUpdateTime(date.getTime());
                            Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
                            uAccount.append("createTime", date);
                            unitAccountCollection.insertOne(clientSession, uAccount);
                        }

                        if (StrUtil.isNotEmpty(shareFee)) {
                            cusFee = Integer.parseInt(shareFee);
                        } else {
                            cusFee = goods.getFees3();
                        }
                        if (cusFee > 0) {
                            orderConMap.put("shareFees", cusFee);
                            orderConMap.put("driverPayerId", userId);

                            cusAccountCollection.insertOne(clientSession, customerAccountService.createCusAccountDoc(goods.getCid(), new BigDecimal(cusFee), 9, oid, date));
                        }

                        int balanceFees = ptFees - order.getFeesOut() - order.getFeesIn() - order.getFees();//客商或单位代付平台金额不足，司机补
                        if (balanceFees > 0) {
                            orderConMap.put("balanceFees", balanceFees);
                            orderConMap.put("driverPayerId", userId);

                            //司机有支付平台的钱，则平台账户添加数据
                            pfAccountCollection.insertOne(clientSession, platFormAccountService.createPlatFormAccountDoc(userId, new BigDecimal(balanceFees), 8, oid, date));
                        }

                        updMap.clear();
                        orderConMap.put("carNum", driverInfo.getCarNum());
                        orderConMap.put("updateTime", date.getTime());
                        orderConMap.put("transactionId", wxResult.getTransactionId());//微信订单号
                        orderConMap.put("outTradeNo", wxResult.getOutTradeNo());//商户订单号
                        orderConMap.put("locked", false);
                        updMap.put("$set", orderConMap);
                        UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(updMap)));
                        if (a.getModifiedCount() <= 0) {
                            return null;
                        }

                        //TODO 2.添加orderTaking ,生成接单信息
                        int finishTag = goods.getMold() == 1 ? 1 : 0;
                        Document otDoc = orderTakingService.createOTDoc(oid, goods, driverInfo, finishTag, order.getIsHand(), 1, date);
                        orderTakingCollection.insertOne(clientSession, otDoc);

                        if (goods.getMold() == 1 && StrUtil.isNotEmpty(latitude) && StrUtil.isNotEmpty(longitude)) {        //收货物流模式
                            //先把库里原有的该订单物流信息删除再添加订单物流信息
                            unOrderLoc(oid);
                            OrderLogistics ol = new OrderLogistics();
                            ol.setOid(oid);
                            ol.setFinishTag(1);
                            ol.setLatitude(latitude);       //纬度
                            ol.setLongitude(longitude);      //经度
                            ol.setUpdateTime(date.getTime());
                            Document olDoc = Document.parse(JSONObject.toJSONString(ol));
                            Point point = GeoJson.point(Double.valueOf(latitude), Double.valueOf(longitude));
                            Document geometry = new Document();
                            geometry.append("type", "Point");
                            geometry.append("coordinates", point.getCoordinates());
                            olDoc.append("geometry", geometry);
                            olDoc.append("createTime", date);
                            orderLogisticsCollection.insertOne(clientSession, olDoc);//保存物流信息
                        }

                        clientSession.commitTransaction();
                    }
                }

                Map<String, String> result = new HashMap<>();
                result.put("oid", oid);
                result.put("carNum", driverInfo.getCarNum());
                result.put("gid", gid);

                return result;

            } finally {

                lockedService.unLock(userId, SysConstants.LockType.WX_PAY_RESULT.getType());//解锁
            }
        }
    }

    // 司机是否可以接单
    private ServerResponse<Map<String, String>> isOrderTaking(String userId, String carNum, String gid, String[] oids, String longitude, String latitude) {
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.getByPK(userId);
        if (wechatDriverInfo == null)
            return ServerResponse.createError("司机不存在");

        if (StrUtil.isNotEmpty(wechatDriverInfo.getState()) && (wechatDriverInfo.getState() != 1 && wechatDriverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        Query<WechatDriverInfo> dvrQuery = wechatDriverInfoService.createQuery();
        dvrQuery.filter("carNum", carNum);
        dvrQuery.or(
                dvrQuery.criteria("drivingPho1").contains("drivingPho1"),
                dvrQuery.criteria("carIdentityPhoBef").contains("carIdentityPhoBef")
        );
        dvrQuery.filter("state != ", 2);
        List<WechatDriverInfo> list = dvrQuery.find().toList();
        if (list.size() > 0 && !list.get(0).getOpenid().equals(wechatDriverInfo.getOpenid()))
            return ServerResponse.createError("该车牌号已绑定微信号");

        //todo:检查司机是否有 未完成的订单
        if (orderTakingService.searchById(userId, 2).size() > 0)
            return ServerResponse.createError("司机有未完成订单，不能接单");

        //TODO:1.查询当前货运信息下或订单号中可接单 订单信息
        List<Order> orders;
        Query<Order> query = orderService.createQuery();
        if (StrUtil.isNotEmpty(oids) && oids.length > 0) {
            query.filter("oid in ", oids);
            query.filter("isWeChat", 1);
            orders = query.find().toList();
        } else {
            query.filter("gid", gid);
            query.filter("isWeChat", 1);
            orders = orderService.list(query);// 货运信息下全部订单集合
        }
        if (orders.size() <= 0) return ServerResponse.createError("货运信息无订单");

        Goods g = goodsService.get("gid", gid); // 同一批次下货运模式是相同的
        Integer mold = g.getMold();//物流模式
        //当mold为1时，即收货物流模式，经纬度参数不能为空
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude)) {
            return ServerResponse.createError("允许获取当前位置信息才可以接单");
        }
        return ServerResponse.createSuccess("司机可接单");
    }

    // 司机是否可以接单2
    private ServerResponse<Map<String, String>> isOrderTaking2(String userId, String carNum, String gid, String[] oids, String longitude, String latitude) {
        DriverInfo driverInfo = driverInfoService.getByPK(userId);
        if (driverInfo == null)
            return ServerResponse.createError("司机不存在");

        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo:检查司机是否有 未完成的订单
        if (orderTakingService.searchById(userId, 2).size() > 0)
            return ServerResponse.createError("司机有未完成订单，不能接单");

        //TODO:1.查询当前货运信息下或订单号中可接单 订单信息
        List<Order> orders;
        Query<Order> query = orderService.createQuery();
        if (StrUtil.isNotEmpty(oids) && oids.length > 0) {
            query.filter("oid in ", oids);
            query.filter("isWeChat", 1);
            orders = query.find().toList();
        } else {
            query.filter("gid", gid);
            query.filter("isWeChat", 1);
            orders = orderService.list(query);// 货运信息下全部订单集合
        }
        if (orders.size() <= 0) return ServerResponse.createError("货运信息无订单");

        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude)) {
            return ServerResponse.createError("允许获取当前位置信息才可以接单");
        }
        return ServerResponse.createSuccess("司机可接单");
    }

    private ServerResponse batchOrderTaking(String carNum, String carInfoId, String userId, String gid, String[] oids, String longitude, String latitude, String fee) {

        UpdateOperations<WechatDriverInfo> updateOperations = wechatDriverInfoService.createUpdateOperations().set("carNum", carNum);
        updateOperations.set("carInfoId", carInfoId);
        CarInfo carInfo = carInfoService.get("id", carInfoId);
        if (ObjectUtil.isNotNull(carInfo)) {
            updateOperations.set("carType", carInfo.getCarType());
            updateOperations.set("axlesNumber", carInfo.getAxlesNumber());
            updateOperations.set("capacity", carInfo.getCapacity());
        }
        wechatDriverInfoService.update(wechatDriverInfoService.createQuery().filter("_id", new ObjectId(userId)), updateOperations);

        Goods g = goodsService.get("gid", gid);
        Date time = new Date();
        //TODO:1.查询当前货运信息下所有的 未被接单且未删除,未锁定的一条订单
        Query<Order> orderQuery = orderService.createQuery();
        orderQuery.filter("gid", gid);
        orderQuery.filter("carNum", null);
        orderQuery.filter("delete", false);
        orderQuery.filter("status", 0);//订单状态0 为可用状态 才可以接单
        orderQuery.filter("locked", false);//订单未锁定
        orderQuery.filter("isWeChat", 1);
        if (StrUtil.isNotEmpty(oids) && oids.length > 0)
            orderQuery.filter("oid in ", oids);//选中订单分享
        Order order = orderService.get(orderQuery);
        if (ObjectUtil.isNull(order))
            return ServerResponse.createError("没有可接订单");
        Integer totalFee = orderRecordService.totalFee(g, order, userId, SysConstants.UserType.UT_WECHAT.toString(), fee);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();

        if (totalFee == 0) {  //司机付款金额为0，客商已代付

            //下订单  /* 添加updateTime作为要修改条件,解决并发操作过程数据脏读问题 */
            MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();

            try {
                clientSession.startTransaction();
                //1.修改order
                Document filter = new Document("oid", order.getOid());
                filter.append("updateTime", order.getUpdateTime());
                Map<String, Object> map = new HashMap<>();
                Map<String, Object> upMap = new HashMap<>();
                map.put("carNum", carNum);
                map.put("updateTime", time.getTime());
                upMap.put("$set", map);
                UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(upMap)));
                if (a.getModifiedCount() <= 0) {
                    return ServerResponse.createError("接单失败！");
                }

                //2.添加orderTaking ,生成接单信息
                DriverInfo driverInfo = new DriverInfo();
                driverInfo.setObjectId(new ObjectId(userId));
                driverInfo.setCarNum(carNum);
                Document otDoc = orderTakingService.createOTDoc(order.getOid(), g, driverInfo, 1, 0, 1, time);
                orderTakingCollection.insertOne(clientSession, otDoc);

                //3.添加订单物流信息
                Document olDoc = orderLogisticsService.createOLDoc(order.getOid(), 1, longitude, latitude, time);
                orderLogisticsCollection.insertOne(clientSession, olDoc);//保存物流信息

                clientSession.commitTransaction();
            } catch (Exception e) {
                clientSession.abortTransaction();
                return ServerResponse.createError("接单失败");
            }
            //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
            Order result = new Order();
            result.setOid(order.getOid());
            result.setCarNum(carNum);
            result.setGid(gid);

            return ServerResponse.createSuccess(result);
        } else {    // 收发货单位或客商代付金额不足或未代付，先锁定该订单，然后司机付款，付款完成后释放该订单
            orderQuery = orderService.createQuery();
            orderQuery.filter("gid", gid);
            orderQuery.filter("carNum", null);
            orderQuery.filter("delete", false);
            orderQuery.filter("status", 0);//订单状态0 为可用状态 才可以接单
            orderQuery.filter("locked", false);//订单未锁定
            orderQuery.filter("isWeChat", 1);
            if (StrUtil.isNotEmpty(oids) && oids.length > 0)
                orderQuery.filter("oid in ", oids);//选中订单分享
            List<Order> orderList = orderService.list(orderQuery);

            String oid = "";
            int i = 0;
            while (i < orderList.size()) {
                Order orderOne = orderList.get(i);
                oid = orderOne.getOid();
                try {
                    clientSession.startTransaction();
                    //1.修改order
                    Document filter = new Document("oid", oid);
                    filter.append("updateTime", orderOne.getUpdateTime());
                    filter.append("locked", false);

                    Map<String, Object> coMap = new HashMap<>();
                    Map<String, Object> upMap = new HashMap<>();
                    coMap.put("carNum", carNum);
                    coMap.put("updateTime", time.getTime());
                    coMap.put("locked", true);
                    upMap.put("$set", coMap);
                    UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(upMap)));
                    if (a.getModifiedCount() > 0) {
                        //添加订单物流信息
                        Document olDoc = orderLogisticsService.createOLDoc(order.getOid(), 1, longitude, latitude, time);
                        orderLogisticsCollection.insertOne(clientSession, olDoc);//保存物流信息

                        clientSession.commitTransaction();
                        break;

                    } else {    //未得到锁
                        i++;
                        continue;
                    }

                } catch (Exception e) {
                    clientSession.abortTransaction();
                    return ServerResponse.createError("网络繁忙，请稍后重试");
                }
            }
            if (i >= orderList.size()) {
                return ServerResponse.createError("网络繁忙，请稍后重试");
            }

            return this.unifiedOrder(userId, totalFee, "小程序-扫码或分享下单", oid, fee);
        }
    }

    private ServerResponse batchOrderTaking2(String carNum, String userId, String gid, String[] oids, String longitude, String latitude, String fee) {
        Goods g = goodsService.get("gid", gid);
        //TODO:1.查询当前货运信息下所有的 未被接单且未删除,未锁定的一条订单
        Query<Order> orderQuery = orderService.createQuery();
        orderQuery.filter("gid", gid);
        orderQuery.filter("carNum", null);
        orderQuery.filter("delete", false);
        orderQuery.filter("status", 0);//订单状态0 为可用状态 才可以接单
        orderQuery.filter("locked", false);//订单未锁定
        orderQuery.filter("isWeChat", 1);
        if (StrUtil.isNotEmpty(oids) && oids.length > 0) orderQuery.filter("oid in ", oids);//选中订单分享
        Order order = orderService.get(orderQuery);
        if (ObjectUtil.isNull(order)) return ServerResponse.createError("没有可接订单");
        Integer totalFee = orderRecordService.totalFee(g, order, userId, SysConstants.UserType.UT_WECHAT.toString(), fee);

        Date time = new Date();
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();

        if (totalFee == 0) {  //司机付款金额为0，客商已代付
            //下订单  /* 添加updateTime作为要修改条件,解决并发操作过程数据脏读问题 */
            try {
                clientSession.startTransaction();

                //1.修改order
                Document filter = new Document("oid", order.getOid());
                filter.append("updateTime", order.getUpdateTime());
                Map<String, Object> map = new HashMap<>();
                Map<String, Object> upMap = new HashMap<>();
                map.put("carNum", carNum);
                map.put("updateTime", time.getTime());
                upMap.put("$set", map);
                UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(upMap)));
                if (a.getModifiedCount() <= 0) {
                    return ServerResponse.createError("接单失败！");
                }

                //2.添加orderTaking ,生成接单信息
                DriverInfo driverInfo = new DriverInfo();
                driverInfo.setObjectId(new ObjectId(userId));
                driverInfo.setCarNum(carNum);
                //Document otDoc = orderTakingService.createOTDoc(order.getOid(), g, driverInfo, 1, 0, 1, time);
                Document otDoc = orderTakingService.createOTDoc(order.getOid(), g, driverInfo, 0, 0, 1, time);
                orderTakingCollection.insertOne(clientSession, otDoc);

                //3.添加订单物流信息
                Document olDoc = orderLogisticsService.createOLDoc(order.getOid(), 1, longitude, latitude, time);
                orderLogisticsCollection.insertOne(clientSession, olDoc);//保存物流信息

                clientSession.commitTransaction();
            } catch (Exception e) {
                clientSession.abortTransaction();
                return ServerResponse.createError("接单失败");
            }
            //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
            Order result = new Order();
            result.setOid(order.getOid());
            result.setCarNum(carNum);
            result.setGid(gid);

            return ServerResponse.createSuccess(result);
        } else {    // 收发货单位或客商代付金额不足或未代付，先锁定该订单，然后司机付款，付款完成后释放该订单
            orderQuery = orderService.createQuery();
            orderQuery.filter("gid", gid);
            orderQuery.filter("carNum", null);
            orderQuery.filter("delete", false);
            orderQuery.filter("status", 0);//订单状态0 为可用状态 才可以接单
            orderQuery.filter("locked", false);//订单未锁定
            orderQuery.filter("isWeChat", 1);
            if (StrUtil.isNotEmpty(oids) && oids.length > 0)
                orderQuery.filter("oid in ", oids);//选中订单分享
            List<Order> orderList = orderService.list(orderQuery);

            String oid = "";
            int i = 0;
            while (i < orderList.size()) {
                Order orderOne = orderList.get(i);
                oid = orderOne.getOid();
                try {
                    clientSession.startTransaction();
                    //1.修改order
                    Document filter = new Document("oid", oid);
                    filter.append("updateTime", orderOne.getUpdateTime());
                    filter.append("locked", false);

                    Map<String, Object> coMap = new HashMap<>();
                    Map<String, Object> upMap = new HashMap<>();
                    coMap.put("carNum", carNum);
                    coMap.put("updateTime", time.getTime());
                    coMap.put("locked", true);
                    upMap.put("$set", coMap);
                    UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(upMap)));
                    if (a.getModifiedCount() > 0) {
                        //添加订单物流信息
                        Document olDoc = orderLogisticsService.createOLDoc(order.getOid(), 1, longitude, latitude, time);
                        orderLogisticsCollection.insertOne(clientSession, olDoc);//保存物流信息

                        clientSession.commitTransaction();
                        break;

                    } else {    //未得到锁
                        i++;
                        continue;
                    }

                } catch (Exception e) {
                    clientSession.abortTransaction();
                    return ServerResponse.createError("网络繁忙，请稍后重试");
                }
            }
            if (i >= orderList.size()) {
                return ServerResponse.createError("网络繁忙，请稍后重试");
            }

            //真的微信小程序司机
            //return this.unifiedOrder(userId, totalFee, "小程序-扫码或分享下单", oid, fee);
            //假的微信小程序司机，实际是app司机
            return this.unifiedOrder2(userId, totalFee, "小程序-扫码或分享下单", oid, fee, carNum);
        }
    }

    //统一下单  totalFee单位为分
    private ServerResponse<Map<String, String>> unifiedOrder(String userId, Integer totalFee, String body, String oid, String fee) {
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.getByPK(userId);

        boolean lock = lockedService.getLock(wechatDriverInfo.getOpenid(), SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) { //未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {

                Map<String, String> map = new HashMap<>();

                Map<String, String> reMap = new HashMap<>();

                long timeStart = System.currentTimeMillis();
                String timestamp = String.format("%010d", timeStart / 1000);

                String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

                map.put("total_fee", String.valueOf(totalFee));
//        map.put("total_fee", "1");//测试金额设置为1分钱
                map.put("trade_type", "JSAPI");//小程序
                try {
                    InetAddress address = InetAddress.getLocalHost();
                    map.put("spbill_create_ip", address.getHostAddress());
                    map.put("out_trade_no", out_trade_no);
                    map.put("body", body);
                    map.put("openid", wechatDriverInfo.getOpenid());
                    map.put("attach", "routine");//微信小程序
                    WXPay wxpay = new WXPay(config, wxPayConstants.notifyUrl, true, false);
                    map = wxpay.unifiedOrder(map);//统一下单

                    String return_code = map.get("return_code");//状态

                    if (return_code.equals("FAIL") || out_trade_no == null) {
                        unLock(oid);    //释放订单
                        unOrderLoc(oid);    //删除物流信息
                        return ServerResponse.createError("统一下单失败", map);
                    }

                    // 业务逻辑处理 ****************************
                    WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
                    prepaid.setType(2);//订单付款
                    prepaid.setBody(body);
                    prepaid.setTotalFee(totalFee);
//            prepaid.setTotalFee(1);//测试金额设置为1分钱
                    prepaid.setSpbillCreateIp(address.getHostAddress());
                    String result_code = map.get("result_code");
                    if (result_code.equals("SUCCESS")) {
                        prepaid.setTradeType("JSAPI");
                        prepaid.setOutTradeNo(out_trade_no);
                        prepaid.setPrepayId(map.get("prepay_id"));

                    }
                    prepaid.setNonceStr(map.get("nonce_str"));
                    prepaid.setSign(map.get("sign"));
                    prepaid.setAppid(map.get("appid"));
                    prepaid.setMchId(map.get("mch_id"));
                    prepaid.setCdid(userId);
                    prepaid.setOid(oid);    //小程序支付的订单号
                    if (StrUtil.isNotEmpty(fee))
                        prepaid.setShareFee(Integer.parseInt(fee) * 100);
                    prepaid.setAttach("routine");
                    wxPrepaidService.save(prepaid);

                    //生成二次签名
                    reMap.put("timeStamp", timestamp);//以秒为单位的10位数字
                    reMap.put("appId", config.getAppID());
                    reMap.put("package", "prepay_id=" + map.get("prepay_id"));
                    reMap.put("nonceStr", map.get("nonce_str"));
                    reMap.put("signType", "MD5");
                    reMap.put("paySign", WXPayUtil.generateSignature(reMap, config.getKey(), WXPayConstants.SignType.MD5));
                    reMap.put("out_trade_no", out_trade_no);
                    reMap.put("oid", oid);

                } catch (Exception e) {
                    e.printStackTrace();
                    unLock(oid);    //释放订单
                    unOrderLoc(oid);    //删除物流信息
                    return ServerResponse.createError("统一下单失败");
                }


                return ServerResponse.createSuccess("统一下单成功", reMap);

            } finally {
                lockedService.unLock(wechatDriverInfo.getOpenid(), SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁
            }
        }

    }

    private ServerResponse<Map<String, String>> unifiedOrder2(String userId, Integer totalFee, String body, String oid, String fee, String carNum) {
        DriverInfo driverInfo = driverInfoService.getByPK(userId);

        boolean lock = lockedService.getLock(userId, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) { //未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {
                Map<String, String> map = new HashMap<>();
                Map<String, String> reMap = new HashMap<>();

                long timeStart = System.currentTimeMillis();
                String timestamp = String.format("%010d", timeStart / 1000);
                String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);

                map.put("total_fee", String.valueOf(totalFee));
                map.put("trade_type", "JSAPI");//小程序
                try {
                    InetAddress address = InetAddress.getLocalHost();
                    map.put("spbill_create_ip", address.getHostAddress());
                    map.put("out_trade_no", out_trade_no);
                    map.put("body", body);
                    map.put("openid", driverInfo.getOpenid());
                    map.put("attach", "routine");//微信小程序
                    WXPay wxpay = new WXPay(config, wxPayConstants.notifyUrl, true, false);
                    map = wxpay.unifiedOrder(map);//统一下单

                    String return_code = map.get("return_code");//状态

                    if (return_code.equals("FAIL") || out_trade_no == null) {
                        unLock(oid);    //释放订单
                        unOrderLoc(oid);    //删除物流信息
                        return ServerResponse.createError("统一下单失败", map);
                    }

                    // 业务逻辑处理 ****************************
                    WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
                    prepaid.setType(2);//订单付款
                    prepaid.setBody(body);
                    prepaid.setTotalFee(totalFee);
                    prepaid.setSpbillCreateIp(address.getHostAddress());
                    String result_code = map.get("result_code");
                    if (result_code.equals("SUCCESS")) {
                        prepaid.setTradeType("JSAPI");
                        prepaid.setOutTradeNo(out_trade_no);
                        prepaid.setPrepayId(map.get("prepay_id"));

                    }
                    prepaid.setNonceStr(map.get("nonce_str"));
                    prepaid.setSign(map.get("sign"));
                    prepaid.setAppid(map.get("appid"));
                    prepaid.setMchId(map.get("mch_id"));
                    prepaid.setCdid(userId);
                    prepaid.setOid(oid);    //小程序支付的订单号
                    if (StrUtil.isNotEmpty(fee)) prepaid.setShareFee(Integer.parseInt(fee) * 100);
                    if (StrUtil.isNotEmpty(carNum)) prepaid.setCarNum(carNum);
                    prepaid.setDriverId(userId);
                    prepaid.setAttach("routine");
                    wxPrepaidService.save(prepaid);

                    //生成二次签名
                    reMap.put("timeStamp", timestamp);//以秒为单位的10位数字
                    reMap.put("appId", config.getAppID());
                    reMap.put("package", "prepay_id=" + map.get("prepay_id"));
                    reMap.put("nonceStr", map.get("nonce_str"));
                    reMap.put("signType", "MD5");
                    reMap.put("paySign", WXPayUtil.generateSignature(reMap, config.getKey(), WXPayConstants.SignType.MD5));
                    reMap.put("out_trade_no", out_trade_no);
                    reMap.put("oid", oid);

                } catch (Exception e) {
                    e.printStackTrace();
                    unLock(oid);    //释放订单
                    unOrderLoc(oid);    //删除物流信息
                    return ServerResponse.createError("统一下单失败");
                }

                return ServerResponse.createSuccess("统一下单成功", reMap);
            } finally {
                lockedService.unLock(userId, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁
            }
        }
    }

    //释放order
    private void unLock(String oid) {
        Order order = orderService.get("oid", oid);
        Query<Order> query = orderService.createQuery();
        query.filter("oid", oid);
        query.filter("updateTime", order.getUpdateTime());
        query.filter("locked", true);
        UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
        updateOperations.set("updateTime", new Date().getTime());
        updateOperations.set("locked", false);
        updateOperations.set("carNum", null);
        orderService.update(query, updateOperations);//释放订单锁
    }

    //删除orderLogistics
    private void unOrderLoc(String oid) {
        orderLogisticsService.delete("oid", oid);
    }

}
