package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.driver.entity.WxPrepaidCost;
import com.erdos.coal.park.api.driver.entity.WxResultCost;

import java.util.List;
import java.util.Map;

public interface IWxPrepaidCostService extends IBaseMongoService<WxPrepaidCost> {

    // 平台代替单位发起支付请求（代收装卸费）
    ServerResponse<Map<String, String>> weChatRequestPay(String did, String openid, String oid, int totalFee, Integer type, String vehicleCode, String bizContractCode);

    // 微信支付完成，微信服务 支付通知，平台业务处理
    String weChatPayResult(Map<String, String> map);

    //
    Integer[] orderCost(Order order);

    // 查询支付订单编号
    Map<String, List<String>> searchPayId(String oid, String bizContractCode);
}
