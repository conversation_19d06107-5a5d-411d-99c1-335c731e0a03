package com.erdos.coal.park.api.customer.pojo;

import java.io.Serializable;

public class GoodsInUnitData implements Serializable {
    private String inUnitName;     //收货一级单位名称
    private String inUnitCode;     //收货一级单位编号
    private String inBizContractName;     //收货合同名称
    private String inBizContractCode;     //收货合同编号
    private String inVariety;           //收货单位产品名称
    private String inSubName;     //收货二级单位名称
    private String inDefaultDownUnit;     //收货二级单位编号
    private String inAreaName;     //收货场区名称
    private String inDefaultArea;     //收货场区编号
    private Integer inMin;          //收货单位最小号
    private Integer inMax;          //收货单位最大号
    private boolean disable;        //票号是否大于零

    private String inTradingUnit;       //收货交易单位编号
    private String inTradingUnitName;       //收货交易单位名称

    public String getInTradingUnit() {
        return inTradingUnit;
    }

    public void setInTradingUnit(String inTradingUnit) {
        this.inTradingUnit = inTradingUnit;
    }

    public String getInTradingUnitName() {
        return inTradingUnitName;
    }

    public void setInTradingUnitName(String inTradingUnitName) {
        this.inTradingUnitName = inTradingUnitName;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public String getInDefaultArea() {
        return inDefaultArea;
    }

    public void setInDefaultArea(String inDefaultArea) {
        this.inDefaultArea = inDefaultArea;
    }

    public Integer getInMin() {
        return inMin;
    }

    public void setInMin(Integer inMin) {
        this.inMin = inMin;
    }

    public Integer getInMax() {
        return inMax;
    }

    public void setInMax(Integer inMax) {
        this.inMax = inMax;
    }

    public boolean isDisable() {
        return disable;
    }

    public void setDisable(boolean disable) {
        this.disable = disable;
    }

    public GoodsInUnitData() {
        this.disable = false;
    }


    public GoodsInUnitData(String inUnitName, String inUnitCode, String inBizContractName, String inBizContractCode, String inVariety, String inSubName, String inDefaultDownUnit, String inAreaName, String inDefaultArea, Integer inMin, Integer inMax) {
        this.inUnitName = inUnitName;
        this.inUnitCode = inUnitCode;
        this.inBizContractName = inBizContractName;
        this.inBizContractCode = inBizContractCode;
        this.inVariety = inVariety;
        this.inSubName = inSubName;
        this.inDefaultDownUnit = inDefaultDownUnit;
        this.inAreaName = inAreaName;
        this.inDefaultArea = inDefaultArea;
        this.inMin = inMin;
        this.inMax = inMax;
        this.disable = true;
    }
}
