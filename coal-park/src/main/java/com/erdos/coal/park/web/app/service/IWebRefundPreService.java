package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.RefundPre;
import com.erdos.coal.park.web.app.pojo.RefundPreData;

import java.util.Map;

public interface IWebRefundPreService extends IBaseMongoService<RefundPre> {
    //企业付款到零钱申请列表
    EGridResult refundPreLoadGrid(Integer page, Integer rows);

    //企业付款到零钱申请 审核
    ServerResponse<String> saveCheck(RefundPreData data);

    //企业付款到零钱 重试
    ServerResponse<Map<String, String>> retry();

    //企业付款到零钱 errCodeDes为<该笔付款正在处理中，请稍后查询付款结果>时，进行复查
    ServerResponse<Map<String, String>> review(String transferNo);
}
