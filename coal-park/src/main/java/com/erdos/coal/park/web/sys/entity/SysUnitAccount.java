package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Entity(value = "sys_unit_Account", noClassnameStored = true)
public class SysUnitAccount extends BaseMongoInfo {
    private String uid;     //sysUnit的 ObjectId
    private String userId;    //扣款人（单位）或付款人（客商或司机）ObjectId
    private BigDecimal totalFee = new BigDecimal("0");   //金额(分)
    /**
     * 类型
     * 0：扣款（单位代付平台费用）
     * 1：单位代收（客商/司机付款）
     * 2：退差价（代付费用>司机接单费用）
     * 3：退款（单位付平台费用——即totalFee>0 客商废除订单、司机拒绝、司机主动废除）
     * 5：退款（客商或司机付单位费用——即totalFee<0 客商废除订单、司机拒绝、司机主动废除）
     * 4：充值
     */
    private Integer type;   //类型 0：扣款（单位代付平台费用） 1：单位代收（客商/司机付款） 2：退差价（代付费用>司机接单费用） 3：退款（客商废除订单）
    private String oid;     //扣款订单号

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String uidAndCreateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //格式化规则
        Date date = getCreateTime();//获得你要处理的时间 Date型
        String strDate = sdf.format(date); //格式化成yyyy-MM-dd格式的时间字符串

        return getUid() + "_" + strDate;
    }
}
