package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.map.service.MapService;
import com.erdos.coal.park.api.customer.dao.IGoodsDao;
import com.erdos.coal.park.api.customer.dao.IOrderDao;
import com.erdos.coal.park.api.customer.dao.IUserDefinedAddressDao;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.UserDefinedAddress;
import com.erdos.coal.park.api.driver.dao.IOrderLogisticsDao;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import com.erdos.coal.park.web.app.pojo.LogisticsData;
import com.erdos.coal.park.web.app.service.IWebLogisticsService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("webLogisticsService")
public class WebLogisticsServiceImpl extends BaseMongoServiceImpl<OrderLogistics, IOrderLogisticsDao> implements IWebLogisticsService {

    @Resource
    private HttpServletRequest request;
    @Resource
    private IOrderDao orderDao;
    @Resource
    private IGoodsDao goodsDao;
    @Resource
    private IUserDefinedAddressDao userDefinedAddressDao;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        Query<OrderLogistics> query = this.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("oid")))
            query.filter("oid", request.getParameter("oid"));

        EGridResult<OrderLogistics> result = this.findPage(page, rows, query);
        List<OrderLogistics> logList = result.getRows();
//        List<OrderLogistics> logList = this.list(query);

        List<LogisticsData> ldList = new ArrayList<>();
        for (OrderLogistics logistics : logList) {
            LogisticsData ld = new LogisticsData();
            String oid = logistics.getOid();
            Order order = orderDao.get("oid", oid);//根据订单号查询订单信息取出车牌号
            ld.setOid(oid);
            ld.setCarNum(order.getCarNum());
            ld.setFinishTag(logistics.getFinishTag());
            if (!StrUtil.isEmpty(logistics.getLongitude()) && !StrUtil.isEmpty(logistics.getLatitude())) {
//                ld.setPlace(MapService.getLocationAddrGaode(logistics.getLongitude() + "," + logistics.getLatitude()));
                ld.setPlace(MapService.getLocationAddrBaiDu(logistics.getLongitude() + "," + logistics.getLatitude()));
            }
            ldList.add(ld);
        }
        return new EGridResult(result.getTotal(), ldList);
        /*int size = ldList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        EGridResult eGridResult = new EGridResult(ldList.size(), ldList.subList(fromIndex, toIndex));
        return eGridResult;
        */

    }

    public List getPointList() {
        String oid = request.getParameter("oid");
        OrderLogistics ls = this.get("oid", oid);
        List list = ls.getGeometry().getCoordinates();
        return list;
    }

    public Map<String, Object> getMap() {
        Map<String, Object> data = new HashMap<>();

        String oid = request.getParameter("oid");
        Order order = orderDao.get("oid", oid);
        Goods goods = goodsDao.get("gid", order.getGid());
        if (ObjectUtil.isNotEmpty(goods)) {
            String title = goods.getEndPoint();
            String cid = goods.getCid();
            Query<UserDefinedAddress> query1 = userDefinedAddressDao.createQuery();

            //TODO: 1.先查询是不是客商自定义地址，
            query1.filter("addName", title);//用户自定义名称
            query1.filter("cid", cid);//客商id
            UserDefinedAddress address = userDefinedAddressDao.get(query1);
            if (ObjectUtil.isNotEmpty(address)) {
                Point point = (Point) address.getGeometry();
                if (ObjectUtil.isNotEmpty(point)) {
                    data.put("title", title);
//                    double[] d = CoordinateTranService.gps84_To_Gcj02(point.getLatitude(), point.getLongitude());
                    data.put("lat", point.getLatitude());
                    data.put("lng", point.getLongitude());
                }
            } else {
                //TODO: 2.如果不是再查询是不是后台维护定义的地址
                Query<UserDefinedAddress> query = userDefinedAddressDao.createQuery();
                query.filter("addName", title);
                query.or(
                        query.criteria("cid").equal(null),
                        query.criteria("cid").equal("")
                );
                List<UserDefinedAddress> listAddress = query.find().toList();
                if (listAddress.size() > 0) {
                    Point point = (Point) listAddress.get(0).getGeometry();
                    if (ObjectUtil.isNotEmpty(point)) {
                        data.put("title", title);
//                        double[] d = CoordinateTranService.gps84_To_Gcj02(point.getLatitude(), point.getLongitude());
                        data.put("lat", point.getLatitude());
                        data.put("lng", point.getLongitude());
                    }
                }
            }
        }
        data.put("oid", oid);
        return data;
    }
}
