package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.baidu.service.AipOcrService;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverInfoDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.driver.entity.*;
import com.erdos.coal.park.api.driver.pojo.CarData;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IDriverOpenidService;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.entity.UserTokenRecord;
import com.erdos.coal.park.api.manage.service.ICarInfoService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.api.manage.service.IUserTokenRecordService;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.sys.service.ISysSwitchService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("driverInfoService")
public class DriverInfoServiceImpl extends BaseMongoServiceImpl<DriverInfo, IDriverInfoDao> implements IDriverInfoService {
    @Resource
    private ISMSService smsService;
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private ISysSwitchService sysSwitchService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;
    @Resource
    private ICarInfoService carInfoService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private ICarDao carDao;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    private IUserTokenRecordService userTokenRecordService;
    @Resource
    private IDriverOpenidService driverOpenidService;

    private Map<String, Object> regLogCheck(String phoneId, String mobile, String code, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        if (StrUtil.isEmpty(phoneId) || StrUtil.isEmpty(deviceId)) {
            map.put("msg", "请先同意微信授权");
            return map;
        }

        //TODO: 2, 按手机号查询
        DriverInfo driverInfo = this.get("mobile", mobile);
        if (driverInfo == null) driverInfo = new DriverInfo();

        if (!phoneId.equals(driverInfo.getPhoneId())) {
            //TODO: 1, 验证码校验
            SMS sms = smsService.get("mobile", mobile);
            if (sms == null || !sms.getCode().equals(code)) {
                map.put("msg", "验证码错误");
                return map;
            }
        }

        map.put("driverInfo", driverInfo);
        return map;
    }

    @Override
    public ServerResponse<AccessToken> driverUserReg(String phoneId, String mobile, String code, String deviceId) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        Map<String, Object> checkMap = regLogCheck(phoneId, mobile, code, deviceId);
        String checkMsg = (String) checkMap.get("msg");
        if (StringUtils.isNotEmpty(checkMsg)) return ServerResponse.createError(checkMsg);
        DriverInfo driverInfo = (DriverInfo) checkMap.get("driverInfo");
        if (StrUtil.isNotEmpty(driverInfo.getMobile())) return ServerResponse.createError("手机号已经注册");

        driverInfo = new DriverInfo();
        driverInfo.setMobile(mobile);
        driverInfo.setPhoneId(phoneId);
        driverInfo.setDeviceId(deviceId);

        /*Map<String, String> photoMap = sysSwitchService.getPhoto(SysConstants.UserType.UT_DRIVER.toString()); // 查询设置账户是否可用
        if (photoMap.get("check").equals("0")) {*/
        if (sysSwitchService.isCheck(SysConstants.UserType.UT_DRIVER.toString())) {
            driverInfo.setState(0);
        } else {
            driverInfo.setState(4);
        }

        DriverInfo result = this.save(driverInfo);
        //shiroUserSecurity.putShiroUserToCache(result.getObjectId().toHexString(), mobile, mobile.substring(7), UserType.DU.toString());

        String token = JwtUtil.sign(UserType.DU, mobile, mobile.substring(7));
        return ServerResponse.createSuccess("注册成功", new AccessToken(token));

    }

    @Override
    public ServerResponse<AccessToken> driverUserReg2(String phoneId, String mobile, String code, String deviceId) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        Map<String, Object> checkMap = regLogCheck(phoneId, mobile, code, deviceId);
        String checkMsg = (String) checkMap.get("msg");
        if (StringUtils.isNotEmpty(checkMsg)) return ServerResponse.createError(checkMsg);
        DriverInfo driverInfo = (DriverInfo) checkMap.get("driverInfo");
        if (StrUtil.isNotEmpty(driverInfo.getMobile())) return ServerResponse.createError("手机号已经注册");

        driverInfo = new DriverInfo();
        driverInfo.setMobile(mobile);
        driverInfo.setPhoneId(phoneId);
        driverInfo.setDeviceId(deviceId);
        driverInfo.setInitialOpenid(phoneId);

        if (sysSwitchService.isCheck(SysConstants.UserType.UT_DRIVER.toString())) {
//            driverInfo.setState(0);
            driverInfo.setState(-1);        //新用户注册还未完善个人信息标记状态为-1
        } else {
            driverInfo.setState(4);
        }

        DriverInfo result = this.save(driverInfo);

        String token = JwtUtil.sign(UserType.DU, mobile, mobile.substring(7));

        //添加用户token记录
        UserTokenRecord tokenRecord = new UserTokenRecord();
        tokenRecord.setUserId(result.getObjectId().toHexString());
        tokenRecord.setUserType("DU");
        tokenRecord.setToken(token);
        userTokenRecordService.save(tokenRecord);

        return ServerResponse.createSuccess("注册成功", new AccessToken(token));

    }

    @Override
    public ServerResponse<AccessToken> driverUserLogin(String phoneId, String mobile, String code, String deviceId) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        Map<String, Object> checkMap = regLogCheck(phoneId, mobile, code, deviceId);
        String checkMsg = (String) checkMap.get("msg");
        if (StringUtils.isNotEmpty(checkMsg)) return ServerResponse.createError(checkMsg);
        DriverInfo driverInfo = (DriverInfo) checkMap.get("driverInfo");

        if (StrUtil.isEmpty(driverInfo.getMobile())) return ServerResponse.createError("用户名错误");

        Query<DriverInfo> query = this.createQuery().filter("mobile", mobile);
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();
        if (driverInfo.getPhoneId() == null || !driverInfo.getPhoneId().equals(phoneId))
            updateOperations.set("phoneId", phoneId);
        if (driverInfo.getDeviceId() == null || !driverInfo.getDeviceId().equals(deviceId))
            updateOperations.set("deviceId", deviceId);//修改用户 设备id
        this.update(query, updateOperations);

        //shiroUserSecurity.putShiroUserToCache(driverInfo.getObjectId().toHexString(), mobile, mobile.substring(7), UserType.DU.toString());

        String token = JwtUtil.sign(UserType.DU, mobile, mobile.substring(7));
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }

    @Override
    public ServerResponse<AccessToken> driverUserLogin2(String phoneId, String mobile, String code, String deviceId) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        Map<String, Object> checkMap = regLogCheck(phoneId, mobile, code, deviceId);
        String checkMsg = (String) checkMap.get("msg");
        if (StringUtils.isNotEmpty(checkMsg)) return ServerResponse.createError(checkMsg);
        DriverInfo driverInfo = (DriverInfo) checkMap.get("driverInfo");

        if (StrUtil.isEmpty(driverInfo.getMobile())) return ServerResponse.createError("用户名错误");
        if (driverInfo.getState() == 2) return ServerResponse.createError("账户不可用");
        String initialOpenid = driverInfo.getPhoneId();

        Query<DriverInfo> query = this.createQuery().filter("mobile", mobile);
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();
        if (driverInfo.getPhoneId() == null || !driverInfo.getPhoneId().equals(phoneId)) {
            updateOperations.set("phoneId", phoneId);
            //限制用户三天才可以更换openid
            Query<DriverOpenid> openidQuery = driverOpenidService.createQuery();
            openidQuery.criteria("did").equal(driverInfo.getObjectId().toHexString());
            openidQuery.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(null, -3));
            /*Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.HOUR_OF_DAY, -1);
            Date d = calendar.getTime();
            openidQuery.criteria("createTime").greaterThanOrEq(d);*/
            List<DriverOpenid> openids = driverOpenidService.list(openidQuery);
            if (openids.size() > 0) {
                // 添加 解除时间 提示
                DriverOpenid openid = openids.get(openids.size() - 1);
                Date createTime = openid.getCreateTime();
                long hoursTS = (new Date().getTime() - createTime.getTime()) / (1000 * 60 * 60);
                return ServerResponse.createError("频繁更换微信，登录失败。在约" + (72 - hoursTS) + "小时后可更换");
            }
            //添加用户openid使用记录
            DriverOpenid driverOpenid = new DriverOpenid();
            driverOpenid.setOpenid(phoneId);
            driverOpenid.setDid(driverInfo.getObjectId().toHexString());
            driverOpenidService.save(driverOpenid);
        }
        if (driverInfo.getDeviceId() == null || !driverInfo.getDeviceId().equals(deviceId))
            updateOperations.set("deviceId", deviceId);//修改用户 设备id
        this.update(query, updateOperations);

        String token = JwtUtil.sign(UserType.DU, mobile, mobile.substring(7));

        //更新用户token记录
        Query<UserTokenRecord> tokenRecordQuery = userTokenRecordService.createQuery();
        tokenRecordQuery.criteria("userId").equal(driverInfo.getObjectId().toHexString());
        tokenRecordQuery.criteria("userType").equal("DU");
        UserTokenRecord tokenRecord = userTokenRecordService.get(tokenRecordQuery);
        if (tokenRecord == null) {
            tokenRecord = new UserTokenRecord();
            tokenRecord.setUserId(driverInfo.getObjectId().toHexString());
            tokenRecord.setUserType("DU");
            tokenRecord.setToken(token);
            userTokenRecordService.save(tokenRecord);
        } else {
            UpdateOperations<UserTokenRecord> updateTokenRecord = userTokenRecordService.createUpdateOperations();
            updateTokenRecord.set("token", token);
            userTokenRecordService.update(userTokenRecordService.createQuery().filter("userId", driverInfo.getObjectId().toHexString()), updateTokenRecord);
        }
        if (StrUtil.isEmpty(driverInfo.getInitialOpenid()))
            this.update(this.createQuery().filter("_id", driverInfo.getObjectId()), this.createUpdateOperations().set("initialOpenid", initialOpenid));
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }

    @Override
    public ServerResponse<Boolean> checkPoneId(String mobile, String phoneId) {

        DriverInfo driverInfo = this.get("mobile", mobile);
        if (driverInfo == null) driverInfo = new DriverInfo();

        if (!phoneId.equals(driverInfo.getPhoneId())) {
            return ServerResponse.createSuccess("检查成功",true);
        } else {
            return ServerResponse.createSuccess("检查成功",false);
        }
    }

    @Override
//    public ServerResponse<String> updateDvrInfo(String name, String identity, MultipartFile drivingPho, MultipartFile driverPho, MultipartFile carIdentityPhoBef, MultipartFile carIdentityPhoBack, MultipartFile driIdentityPhoBef, MultipartFile driIdentityPhoBack, MultipartFile driverCarPho) {
    public ServerResponse<String> updateDvrInfo(MultipartFile driverPho, MultipartFile driverPho2, MultipartFile driIdentityPhoBef, MultipartFile driIdentityPhoBack, MultipartFile driverCarPho, MultipartFile roadQCPho, MultipartFile bankCardPho) {
        String did = ShiroUtils.getUserId();
        assert did != null;
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();

        // 1.查询开关，判断需要上传的照片数量
        // Map<String, String> photoMap = sysSwitchService.getPhoto(null);
        // if (photoMap.get("number").equals("3") || photoMap.get("number").equals("8")) {

        // 2.识别身份证图片，获取姓名和身份证号码信息
        try {
            Map<String, String> aipOcrMap = AipOcrService.getIdCardInfo(driIdentityPhoBef, "front");
            String name = aipOcrMap.get("name");
            String idNumber = aipOcrMap.get("idNumber");
            if (StrUtil.isEmpty(name) || StrUtil.isEmpty(idNumber))
                return ServerResponse.createError("身份证照片识别出错！");
            updateOperations.set("identity", idNumber);
            updateOperations.set("name", name);

        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createError("身份证照片不能为空！");
        }
        // }

        // 3.上传图片
        Map<String, MultipartFile> photoMap = new HashMap<>();
        if (driverPho != null) photoMap.put("driverPho", driverPho);  //驾驶证照片正页
        if (driverPho2 != null) photoMap.put("driverPho2", driverPho2);  //驾驶证照片副页
        photoMap.put("driIdentityPhoBef", driIdentityPhoBef);  //司机身份证正面
        if (driIdentityPhoBack != null) photoMap.put("driIdentityPhoBack", driIdentityPhoBack);  //司机身份证正面
        if (roadQCPho != null) photoMap.put("roadQCPho", roadQCPho);  //道路从业资格证照片
        if (bankCardPho != null) photoMap.put("bankCardPho", bankCardPho);  //银行卡照片
        Map<String, String> pathMap = photoFileService.uploadPhotosByUid(did, "t_driver_info", photoMap);
        //Map<String, String> pathMap = photoFileService.uploadPhotos(photoMap);

        // 4.保存图片到司机信息表
        if (StrUtil.isNotEmpty(pathMap.get("driverPhoPath")))
            updateOperations.set("driverPho", pathMap.get("driverPhoPath"));
        if (StrUtil.isNotEmpty(pathMap.get("driverPho2Path")))
            updateOperations.set("driverPho2", pathMap.get("driverPho2Path"));
        updateOperations.set("driIdentityPhoBef", pathMap.get("driIdentityPhoBefPath"));
        if (StrUtil.isNotEmpty(pathMap.get("driIdentityPhoBackPath")))
            updateOperations.set("driIdentityPhoBack", pathMap.get("driIdentityPhoBackPath"));
        if (StrUtil.isNotEmpty(pathMap.get("roadQCPhoPath")))
            updateOperations.set("roadQCPho", pathMap.get("roadQCPhoPath"));
        if (StrUtil.isNotEmpty(pathMap.get("bankCardPhoPath")))
            updateOperations.set("bankCardPho", pathMap.get("bankCardPhoPath"));
        Query<DriverInfo> query = this.createQuery().filter("_id", new ObjectId(did));
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<DriverInfo> selectOne() {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = this.getByPK(did);
        String drivingPho1 = driverInfo.getDrivingPho1();
        String drivingPho2 = driverInfo.getDrivingPho2();
        String driverPho = driverInfo.getDriverPho();
        String driverCarPho = driverInfo.getDriverCarPho();
        String carIdentityPhoBef = driverInfo.getCarIdentityPhoBef();//车主身份证正面
        String carIdentityPhoBack = driverInfo.getCarIdentityPhoBack();//车主身份证背面
        String driIdentityPhoBef = driverInfo.getDriIdentityPhoBef();//司机身份证正面
        String driIdentityPhoBack = driverInfo.getDriIdentityPhoBack();//司机身份证背面

        if (!StrUtil.isEmpty(drivingPho1)) {
            drivingPho1 = fileInfoDao.readFile(drivingPho1, "t_driver_info");
            driverInfo.setDrivingPho1(drivingPho1);
        }
        if (!StrUtil.isEmpty(drivingPho2)) {
            drivingPho2 = fileInfoDao.readFile(drivingPho2, "t_driver_info");
            driverInfo.setDrivingPho2(drivingPho2);
        }
        if (!StrUtil.isEmpty(driverPho)) {
            driverPho = fileInfoDao.readFile(driverPho, "t_driver_info");
            driverInfo.setDriverPho(driverPho);
        }
        if (!StrUtil.isEmpty(driverCarPho)) {
            driverCarPho = fileInfoDao.readFile(driverCarPho, "t_driver_info");
            driverInfo.setDriverCarPho(driverCarPho);
        }
        if (!StrUtil.isEmpty(carIdentityPhoBef)) {
            carIdentityPhoBef = fileInfoDao.readFile(carIdentityPhoBef, "t_driver_info");
            driverInfo.setCarIdentityPhoBef(carIdentityPhoBef);
        }
        if (!StrUtil.isEmpty(carIdentityPhoBack)) {
            carIdentityPhoBack = fileInfoDao.readFile(carIdentityPhoBack, "t_driver_info");
            driverInfo.setCarIdentityPhoBack(carIdentityPhoBack);
        }
        if (!StrUtil.isEmpty(driIdentityPhoBef)) {
            driIdentityPhoBef = fileInfoDao.readFile(driIdentityPhoBef, "t_driver_info");
            driverInfo.setDriIdentityPhoBef(driIdentityPhoBef);
        }
        if (!StrUtil.isEmpty(driIdentityPhoBack)) {
            driIdentityPhoBack = fileInfoDao.readFile(driIdentityPhoBack, "t_driver_info");
            driverInfo.setDriIdentityPhoBack(driIdentityPhoBack);
        }
        if (StrUtil.isEmpty(driverInfo.getName())) {
            driverInfo.setName(" ");
        }
        driverInfo.setTradePwd(null);
        return ServerResponse.createSuccess("查询成功", driverInfo);
    }

    //司机更改手机号
    @Override
    public ServerResponse<String> updateMobile(String mobile, String code) {
        //TODO: 1, 验证码校验
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null) {
            return ServerResponse.createError("请先获取验证码");
        }
        if (!sms.getCode().equals(code)) {
            return ServerResponse.createError("验证码错误");
        }
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = this.getByPK(did);
        //TODO: 2, 手机号合法性校验(测试放开)
        if (!StrUtil.isPhone(mobile)) {
            return ServerResponse.createError("手机号码不正确");
        }

        //TODO: 3, 判断手机号是否已使用
        DriverInfo driverInfo1 = this.get("mobile", mobile);

        if (driverInfo1 == null) {   //未使用
            Query<DriverInfo> query = this.createQuery().filter("mobile", driverInfo.getMobile());
            UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations().set("mobile", mobile);
            this.update(query, updateOperations);

            return ServerResponse.createSuccess("修改成功");
        } else {
            return ServerResponse.createError("该手机号已使用");
        }
    }

    @Override
    public ServerResponse<String> tradePwd(String identity, String pwd, String confirmPwd) {
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverInfo driverInfo = this.getByPK(did);
        Map<String, String> flag = getIdentity().getData();
        if (StrUtil.isNotEmpty(flag) && flag.get("flag").equals("1")) {//需要输入身份证号码的，需要和库里比对
            if (StrUtil.isEmpty(identity)) {
                //return ServerResponse.createError("请输入车主身份证号");
                return ServerResponse.createError("请输入司机身份证号");
            } else {
                if (driverInfo == null || StrUtil.isEmpty(driverInfo.getIdentity()))
                    return ServerResponse.createError("司机暂未完善身份证信息或稍后重试");
                if (!identity.equals(driverInfo.getIdentity().substring(driverInfo.getIdentity().length() - 4))) //对比身份证号后4位
                    return ServerResponse.createError("请输入正确的身份证号");
            }
        }
        if (!pwd.equals(confirmPwd)) {
            return ServerResponse.createError("两次输入的密码不一致");
        }
        if (!StrUtil.isSixNum(confirmPwd)) {
            return ServerResponse.createError("请输入6位数字的交易密码");
        }

//        String password = "123456";
//        String salt = new SecureRandomNumberGenerator().nextBytes().toString();
//        int times = 2;  // 加密次数：2*/
//        String alogrithmName = "md5";   // 加密算法
//        String encodePassword = new SimpleHash(alogrithmName, password, salt, times).toString();
//        String encodePassword = new SimpleHash(alogrithmName, password).toString();

//        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
//        String str = passwordEncoder.encode(confirmPwd);

        String str = Utils.md5(confirmPwd);

        Query<DriverInfo> query = this.createQuery().filter("_id", new ObjectId(did));
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();
        updateOperations.set("tradePwd", str);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("交易密码设置成功");
    }

    @Override
    public DriverInfo findUserByName(String name) {
        return this.get("mobile", name);
    }

    @Override
    public List<String> findPermissions(String name) {
        //返回当前用户权限列表
        List<String> permissions = new ArrayList<>();
        permissions.add("sys:menu:add");
        permissions.add("sys:menu:edit");
        permissions.add("sys:menu:delete");
        return permissions;
    }

    @Override
    public ServerResponse<Map<String, String>> getSwitch() {
        // 后面升级用户上传驾驶证，这里临时设置旧用户不参与。
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = this.getByPK(did);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String createTimeStr = simpleDateFormat.format(driverInfo.getCreateTime());
        if ("20250415".compareTo(createTimeStr) > 0){   // 老用户保持原来的仅上传身份证
            Map<String, String> oldPhoMap = new HashMap<>();
            oldPhoMap.put("number", "1");
            oldPhoMap.put("value", "driIdentityPhoBef");
            return ServerResponse.createSuccess("查询成功", oldPhoMap);
        }

        Map<String, String> photoMap = sysSwitchService.getDvrPhoto(0);

        //不用上传照片或者无需审核的设置审核状态为已通过
        /*以下功能已经在 /coal-park/src/main/java/com/erdos/coal/park/web/sys/service/impl/SysSwitchServiceImpl.java 文件的addSwitch方法完成，一经开关设置，用户状态已统一修改
        if (photoMap.get("number").equals("0") || photoMap.get("check").equals("1")) {
            String did = ShiroUtils.getUserId();
            assert did != null;
            String type = ShiroUtils.getUserType();
            assert type != null;
            List<Integer> state = new ArrayList<>();
            state.add(1);
            state.add(2);

            if (type.equals(SysConstants.UserType.UT_DRIVER.toString())) {//app,driverInfo
                Query<DriverInfo> query = this.createQuery();
                query.filter("_id", new ObjectId(did));
                query.filter("state nin ", state);//状态为“审核通过”或“冻结”不用修改，其他的都可以改为“可用”

                UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();
                updateOperations.set("state", 4);
                this.update(query, updateOperations);
            } else if (type.equals(SysConstants.UserType.UT_WECHAT.toString())) {//小程序，wechatDriverInfo
                Query<WechatDriverInfo> query = wechatDriverInfoService.createQuery();
                query.filter("_id", new ObjectId(did));
                query.filter("state nin ", state);//状态为“审核通过”或“冻结”不用修改，其他的都可以改为“可用”

                UpdateOperations<WechatDriverInfo> updateOperations = wechatDriverInfoService.createUpdateOperations();
                updateOperations.set("state", 4);
                wechatDriverInfoService.update(query, updateOperations);
            }
        }*/
        return ServerResponse.createSuccess("查询成功", photoMap);
    }

    /**
     * state = 1 审核通过的交易密码必须输入身份证号
     * state = 0 或state = 3  未审核
     * 1.未审核不可用,需先完善用户信息
     * 2.审核未通过,需先通过审核再设置交易密码
     * state = 4 无需上传身份证号
     * state = 2 冻结
     * 1.有身份证号
     * 2.没有身份证号
     */
    @Override
    public ServerResponse<Map<String, String>> getIdentity() {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = this.getByPK(did);
        Map<String, String> map = new HashMap<>();

        Integer state = driverInfo.getState();
        if (state == 1) {
            map.put("flag", "1");
            //return ServerResponse.createSuccess("请输入车主身份证号", map);
            return ServerResponse.createSuccess("请输入司机身份证号", map);
        } else if (state == 0) {
            if (StrUtil.isEmpty(driverInfo.getIdentity())) {
                map.put("flag", "2");
                return ServerResponse.createSuccess("请先完善用户信息", map);
            } else {
                map.put("flag", "3");
                return ServerResponse.createSuccess("等待审核通过后再设置交易密码", map);
            }
        } else if (state == 3) {
            map.put("flag", "3");
            return ServerResponse.createSuccess("审核未通过", map);
        } else if (state == 2) {
            if (StrUtil.isEmpty(driverInfo.getIdentity())) {
                map.put("flag", "4");
                //return ServerResponse.createSuccess("无需输入车主身份证号", map);
                return ServerResponse.createSuccess("无需输入司机身份证号", map);
            } else {
                map.put("flag", "1");
                //return ServerResponse.createSuccess("请输入车主身份证号", map);
                return ServerResponse.createSuccess("请输入司机身份证号", map);
            }
        } else if (state == 4) {
            map.put("flag", "4");
            //return ServerResponse.createSuccess("无需输入车主身份证号", map);
            return ServerResponse.createSuccess("无需输入司机身份证号", map);
        }
        return ServerResponse.createError();
    }

    @Override
    public ServerResponse<Map<String, String>> getCarSwitch() {
        Map<String, String> photoMap = sysSwitchService.getCarPhoto(0);
        return ServerResponse.createSuccess("查询成功", photoMap);
    }

    @Override
    public ServerResponse<String> saveDriverCar(String carNum, String carInfoId, MultipartFile drivingPho1, MultipartFile drivingPho2, MultipartFile drivingPho3, MultipartFile roadTCPho, MultipartFile carIdentityPhoBef, MultipartFile carIdentityPhoBack, MultipartFile driverCarPho) {
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverInfo driverInfo = this.getByPK(did);

        // 0.查询后台 需要上传对照片数量
        // Map<String, String> photoMap = sysSwitchService.getPhoto(null);
        // if (photoMap.get("number").equals("3") || photoMap.get("number").equals("8")) {
        /*if (drivingPho1 == null || carIdentityPhoBef == null || drivingPho2 == null)
                    return ServerResponse.createError("请上传照片");*/

        Car existCar;
        //2.图片保存
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", did);
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete nin", 1);
        DriverToCar existDriverToCar = driverToCarDao.get(dctQuery);
        if (existDriverToCar == null) {
            existCar = new Car();
        } else {
            if (existDriverToCar.getDelete() == 2) return ServerResponse.createError("车牌：" + carNum + "被限制使用，请联系管理员");
            existCar = carDao.getByPK(existDriverToCar.getCarId());
            if (existCar.getVerify() == 4) return ServerResponse.createError("车辆：" + carNum + "被停用，关联失败");
        }
        if (existDriverToCar == null || existCar.getVerify() != 1) {//审核通过的车辆，则不需要上传新的车辆信息
            Map<String, MultipartFile> photoMap = new HashMap<>();
            if (drivingPho1 != null) photoMap.put("drivingPho1", drivingPho1);  //行驶证照片第一页
            if (drivingPho2 != null) photoMap.put("drivingPho2", drivingPho2);  //行驶证照片第二页
            if (drivingPho3 != null) photoMap.put("drivingPho3", drivingPho3);  //行驶证照片第三页
            if (roadTCPho != null) photoMap.put("roadTCPho", roadTCPho);        //驾驶证照片
            if (carIdentityPhoBef != null) photoMap.put("carIdentityPhoBef", carIdentityPhoBef);    //车主身份证照片正面
            if (carIdentityPhoBack != null) photoMap.put("carIdentityPhoBack", carIdentityPhoBack); //车主身份证照片反面
            if (driverCarPho != null) photoMap.put("driverCarPho", driverCarPho); //司机和车的合影照片id
            Map<String, String> pathMap = photoFileService.uploadPhotosByUid(did + carNum, "t_car", photoMap);
            //Map<String, String> pathMap = photoFileService.uploadPhotos(photoMap);

            existCar.setVerify(0);
            existCar.setId(Utils.getUUID());
            existCar.setCarNum(carNum);
            CarInfo carInfo = carInfoService.get("id", carInfoId);
            existCar.setCarInfo(carInfo);
            if (StrUtil.isNotEmpty(pathMap.get("drivingPho1Path")))
                existCar.setDrivingPho1(pathMap.get("drivingPho1Path"));
            if (StrUtil.isNotEmpty(pathMap.get("drivingPho2Path")))
                existCar.setDrivingPho2(pathMap.get("drivingPho2Path"));
            if (StrUtil.isNotEmpty(pathMap.get("drivingPho3Path")))
                existCar.setDrivingPho3(pathMap.get("drivingPho3Path"));
            if (StrUtil.isNotEmpty(pathMap.get("roadTCPhoPath"))) existCar.setRoadTCPho(pathMap.get("roadTCPhoPath"));
            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBefPath")))
                existCar.setCarIdentityPhoBef(pathMap.get("carIdentityPhoBefPath"));
            if (StrUtil.isNotEmpty(pathMap.get("carIdentityPhoBackPath")))
                existCar.setCarIdentityPhoBack(pathMap.get("carIdentityPhoBackPath"));
            if (StrUtil.isNotEmpty(existCar.getUpdateTime())) existCar.setUpdateTime(new Date().getTime());
            existCar = carDao.save(existCar);
        }

        //3.司机 关联 车辆信息
        String driverCarPhoStr = null;
        if (driverCarPho != null)
            driverCarPhoStr = photoFileService.uploadPhotoByUid(did + carNum, "t_driver_to_car", driverCarPho);
        //driverCarPhoStr = photoFileService.uploadPhoto(driverCarPho);

        if (existDriverToCar == null) existDriverToCar = new DriverToCar();
        existDriverToCar.setCarId(existCar.getObjectId().toHexString());
        existDriverToCar.setDriverId(did);
        existDriverToCar.setCarNum(carNum);
        existDriverToCar.setMobile(driverInfo.getMobile());
        existDriverToCar.setName(driverInfo.getName());
        if (StrUtil.isEmpty(existDriverToCar.getDelete()) || existDriverToCar.getDelete() != 2)
            existDriverToCar.setDelete(0);
        if (StrUtil.isNotEmpty(driverCarPhoStr)) existDriverToCar.setDriverCarPho(driverCarPhoStr);
        if (StrUtil.isNotEmpty(existDriverToCar.getUpdateTime())) existDriverToCar.setUpdateTime(new Date().getTime());
        driverToCarDao.save(existDriverToCar);

        return ServerResponse.createSuccess("车辆信息添加并关联成功");
    }

    @Override
    public ServerResponse<List<CarData>> getDriverCars() {
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverInfo driverInfo = this.getByPK(did);

        //查询司机是否关联了微信小程序账号
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("did", did);
        Query<DriverToCar> driverToCarQuery = driverToCarDao.createQuery();
        if (wechatDriverInfo != null) {
            driverToCarQuery.or(
                    driverToCarQuery.criteria("driverId").equal(did),
                    driverToCarQuery.criteria("driverId").equal(wechatDriverInfo.getObjectId().toHexString())
            );
        } else {
            driverToCarQuery.filter("driverId", did);
        }
        driverToCarQuery.filter("delete nin", 1);
        List<DriverToCar> driverToCars = driverToCarDao.list(driverToCarQuery);

        List<CarData> resultList = new ArrayList<>();
        if (driverToCars.size() <= 0) return ServerResponse.createSuccess("暂无关联车辆", resultList);

        for (DriverToCar driverToCar : driverToCars) {
            Car car = carDao.getByPK(driverToCar.getCarId());

            CarData carData = new CarData();
            BeanUtils.copyProperties(car, carData);
//            carData.setId(car.getObjectId().toHexString());
            carData.setId(driverToCar.getObjectId().toHexString());
            if (StrUtil.isNotEmpty(driverInfo.getCarNum()) && car.getCarNum().equals(driverInfo.getCarNum()))
                carData.setUsing(true);
            carData.setDelete(driverToCar.getDelete());

            // 判断车辆是否已经录入皮重信息
            if (StrUtil.isEmpty(car.getTareWeight()) || 0.0 == car.getTareWeight()) {
                carData.setHasTareWeight(false);
            } else {
                carData.setHasTareWeight(true);
            }
            resultList.add(carData);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<String> cancelToCar(String id) {
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverToCar exitDriverToCar = driverToCarDao.getByPK(id);
        if (exitDriverToCar == null) return ServerResponse.createError("错误删除");

        Query<DriverToCar> driverToCarQuery = driverToCarDao.createQuery().filter("_id", new ObjectId(id));
        UpdateOperations<DriverToCar> updateOperations = driverToCarDao.createUpdateOperations();
        updateOperations.set("delete", 1);
        driverToCarDao.update(driverToCarQuery, updateOperations);

        //司机取消关联车辆 后 若字段carNum（正在使用车辆号牌）为该车牌号，则须删除carNum字段信息
        DriverInfo driverInfo = this.getByPK(did);
        Car car = carDao.getByPK(exitDriverToCar.getCarId());
        if (car.getCarNum().equals(driverInfo.getCarNum())) {
            Query<DriverInfo> query = this.createQuery().filter("_id", new ObjectId(did));
            UpdateOperations<DriverInfo> driverInfoUpdateOperations = this.createUpdateOperations();
            driverInfoUpdateOperations.set("carNum", "");
            this.update(query, driverInfoUpdateOperations);
            //司机账号是否关联了小程序账号
            WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("did", did);
            if (wechatDriverInfo != null && StrUtil.isNotEmpty(wechatDriverInfo.getCarNum()) && car.getCarNum().equals(wechatDriverInfo.getCarNum())) {
                wechatDriverInfo.setCarNum(null);
                wechatDriverInfo.setUpdateTime(new Date().getTime());
                wechatDriverInfoService.save(wechatDriverInfo);
            }
        }

        return ServerResponse.createSuccess("车辆取消关联成功");
    }

    @Override
    public ServerResponse<String> choseCar(String carNum) {
        String did = ShiroUtils.getUserId();
        assert did != null;

        //2.判断司机关联的车号 是否可用
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", did);
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete nin", 1);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        if (driverToCar.getDelete() == 2) return ServerResponse.createError("车辆：" + carNum + "违反平台规则，被限制使用");
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车号错误");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //3.修改司机信息当前使用车号carNum字段
        Query<DriverInfo> query = this.createQuery().filter("_id", new ObjectId(did));
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations().set("carNum", carNum);
        if (StrUtil.isNotEmpty(car.getDrivingPho1())) updateOperations.set("drivingPho1", car.getDrivingPho1());
        if (StrUtil.isNotEmpty(car.getDrivingPho2())) updateOperations.set("drivingPho2", car.getDrivingPho2());
        if (StrUtil.isNotEmpty(car.getDrivingPho3())) updateOperations.set("drivingPho3", car.getDrivingPho3());
        if (StrUtil.isNotEmpty(car.getCarIdentityPhoBef()))
            updateOperations.set("carIdentityPhoBef", car.getCarIdentityPhoBef());
        if (StrUtil.isNotEmpty(car.getCarIdentityPhoBack()))
            updateOperations.set("carIdentityPhoBack", car.getCarIdentityPhoBack());
        if (StrUtil.isNotEmpty(driverToCar.getDriverCarPho()))
            updateOperations.set("driverCarPho", driverToCar.getDriverCarPho());
        updateOperations.set("carInfoId", car.getCarInfo().getId());
        updateOperations.set("carType", car.getCarInfo().getCarType());
        updateOperations.set("capacity", car.getCarInfo().getCapacity());
        updateOperations.set("axlesNumber", car.getCarInfo().getAxlesNumber());
        this.update(query, updateOperations);
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("did", did);
        if (wechatDriverInfo != null && (StrUtil.isEmpty(wechatDriverInfo.getCarNum()) || !car.getCarNum().equals(wechatDriverInfo.getCarNum()))) {
            wechatDriverInfo.setCarNum(carNum);
            wechatDriverInfo.setDrivingPho1(car.getDrivingPho1());
            wechatDriverInfo.setDrivingPho2(car.getDrivingPho2());
            wechatDriverInfo.setDrivingPho3(car.getDrivingPho3());
            wechatDriverInfo.setCarIdentityPhoBef(car.getCarIdentityPhoBef());
            wechatDriverInfo.setCarIdentityPhoBack(car.getCarIdentityPhoBack());
            wechatDriverInfo.setDriverCarPho(driverToCar.getDriverCarPho());
            wechatDriverInfo.setUpdateTime(new Date().getTime());
            wechatDriverInfo.setCarInfoId(car.getCarInfo().getId());
            wechatDriverInfo.setCarType(car.getCarInfo().getCarType());
            wechatDriverInfo.setCapacity(car.getCarInfo().getCapacity());
            wechatDriverInfo.setAxlesNumber(car.getCarInfo().getAxlesNumber());
            wechatDriverInfoService.save(wechatDriverInfo);
        }

        return ServerResponse.createSuccess("选择成功");
    }

    @Override
    public ServerResponse<String> choseCar2(String carNum) {
        String did = ShiroUtils.getUserId();
        assert did != null;

        //2.判断司机关联的车号 是否可用
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", did);
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete nin", 1);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        if (driverToCar.getDelete() == 2) return ServerResponse.createError("车辆：" + carNum + "违反平台规则，被限制使用");
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车号错误");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");
        if (StrUtil.isEmpty(car.getTareWeight())) return ServerResponse.createError("请先完善车辆"+carNum+"的皮重信息");

        //3.修改司机信息当前使用车号carNum字段
        Query<DriverInfo> query = this.createQuery().filter("_id", new ObjectId(did));
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations().set("carNum", carNum);
        if (StrUtil.isNotEmpty(car.getDrivingPho1())) updateOperations.set("drivingPho1", car.getDrivingPho1());
        if (StrUtil.isNotEmpty(car.getDrivingPho2())) updateOperations.set("drivingPho2", car.getDrivingPho2());
        if (StrUtil.isNotEmpty(car.getDrivingPho3())) updateOperations.set("drivingPho3", car.getDrivingPho3());
        if (StrUtil.isNotEmpty(car.getCarIdentityPhoBef()))
            updateOperations.set("carIdentityPhoBef", car.getCarIdentityPhoBef());
        if (StrUtil.isNotEmpty(car.getCarIdentityPhoBack()))
            updateOperations.set("carIdentityPhoBack", car.getCarIdentityPhoBack());
        if (StrUtil.isNotEmpty(driverToCar.getDriverCarPho()))
            updateOperations.set("driverCarPho", driverToCar.getDriverCarPho());
        updateOperations.set("carInfoId", car.getCarInfo().getId());
        updateOperations.set("carType", car.getCarInfo().getCarType());
        updateOperations.set("capacity", car.getCarInfo().getCapacity());
        updateOperations.set("axlesNumber", car.getCarInfo().getAxlesNumber());
        this.update(query, updateOperations);
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("did", did);
        if (wechatDriverInfo != null && (StrUtil.isEmpty(wechatDriverInfo.getCarNum()) || !car.getCarNum().equals(wechatDriverInfo.getCarNum()))) {
            wechatDriverInfo.setCarNum(carNum);
            wechatDriverInfo.setDrivingPho1(car.getDrivingPho1());
            wechatDriverInfo.setDrivingPho2(car.getDrivingPho2());
            wechatDriverInfo.setDrivingPho3(car.getDrivingPho3());
            wechatDriverInfo.setCarIdentityPhoBef(car.getCarIdentityPhoBef());
            wechatDriverInfo.setCarIdentityPhoBack(car.getCarIdentityPhoBack());
            wechatDriverInfo.setDriverCarPho(driverToCar.getDriverCarPho());
            wechatDriverInfo.setUpdateTime(new Date().getTime());
            wechatDriverInfo.setCarInfoId(car.getCarInfo().getId());
            wechatDriverInfo.setCarType(car.getCarInfo().getCarType());
            wechatDriverInfo.setCapacity(car.getCarInfo().getCapacity());
            wechatDriverInfo.setAxlesNumber(car.getCarInfo().getAxlesNumber());
            wechatDriverInfoService.save(wechatDriverInfo);
        }

        return ServerResponse.createSuccess("选择成功");
    }

    @Override
    public ServerResponse<String> cleanCarNum() {
        Long nowTime = new Date().getTime();
        String did = ShiroUtils.getUserId();
        assert did != null;
        DriverInfo driverInfo = this.getByPK(did);

        driverInfo.setCarNum(null);
        driverInfo.setUpdateTime(nowTime);
        this.save(driverInfo);

        //查询是否关联微信小程序
        WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get("did", did);
        if (wechatDriverInfo != null) {
            wechatDriverInfo.setCarNum(null);
            wechatDriverInfo.setUpdateTime(nowTime);
            wechatDriverInfoService.save(wechatDriverInfo);
        }

        return ServerResponse.createSuccess("设置成功");
    }

    @Override
    public void updateHaveOrder(String did, Integer haveOrder) {
        UpdateOperations<DriverInfo> up = this.createUpdateOperations();
        up.set("haveOrder", haveOrder);
        this.update(this.createQuery().filter("objectId", new ObjectId(did)), up);
    }

    @Override
    public void updateHaveOrder(List<String> dids, Integer haveOrder) {
        List<ObjectId> didObjectS = new ArrayList<>();
        for (String did : dids) {
            didObjectS.add(new ObjectId(did));
        }
        UpdateOperations<DriverInfo> up = this.createUpdateOperations();
        up.set("haveOrder", haveOrder);
        this.update(this.createQuery().filter("objectId in", didObjectS.toArray()), up);
    }

    private DriverInfo zytRegLogCheck(String zytOpenid, String mobile, String code) {
        if (StrUtil.isEmpty(zytOpenid)) throw new GlobalException(1, "小程序openid不能为空");

        //TODO: 2, 按手机号查询
        DriverInfo driverInfo = this.get("mobile", mobile);
        if (driverInfo == null) driverInfo = new DriverInfo();

        if (!zytOpenid.equals(driverInfo.getZytOpenid())) {
            //TODO: 1, 验证码校验
            SMS sms = smsService.get("mobile", mobile);
            if (sms == null || !sms.getCode().equals(code))
                throw new GlobalException(1, "验证码错误");
        }

        return driverInfo;
    }

    @Override
    public ServerResponse<String> zytDriverReg(String zytOpenid, String mobile, String code) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        DriverInfo driverInfo = zytRegLogCheck(zytOpenid, mobile, code);
        if (StrUtil.isNotEmpty(driverInfo.getMobile())) return ServerResponse.createError("手机号已经注册，请登录");

        driverInfo = new DriverInfo();
        driverInfo.setMobile(mobile);
        driverInfo.setZytOpenid(zytOpenid);
        driverInfo.setZytInitialOpenid(zytOpenid);

        if (sysSwitchService.isCheck(SysConstants.UserType.UT_DRIVER.toString())) {
            driverInfo.setState(-1);        //新用户注册还未完善个人信息标记状态为-1
        } else {
            driverInfo.setState(4);
        }

        this.save(driverInfo);

        return ServerResponse.createSuccess("注册成功");
    }

    @Override
    public ServerResponse<DriverInfo> zytDriverLogin(String zytOpenid, String mobile, String code) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        DriverInfo driverInfo = zytRegLogCheck(zytOpenid, mobile, code);
        if (StrUtil.isEmpty(driverInfo.getMobile())) return ServerResponse.createError("用户名错误");
        if (driverInfo.getState() == 2) return ServerResponse.createError("账户不可用");

        // 更新用户的智运通司机小程序的openid
        if (driverInfo.getZytOpenid() == null || !driverInfo.getZytOpenid().equals(zytOpenid)) {
            Query<DriverInfo> query = this.createQuery().filter("mobile", mobile);
            UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();
            updateOperations.set("zytOpenid", zytOpenid);
            if (driverInfo.getZytInitialOpenid() == null) updateOperations.set("zytInitialOpenid", zytOpenid);
            this.update(query, updateOperations);
        }

        DriverInfo resultDvr = new DriverInfo();
        resultDvr.setObjectId(driverInfo.getObjectId());
        resultDvr.setIdentity(driverInfo.getIdentity());
        resultDvr.setCarNum(driverInfo.getCarNum());
        return ServerResponse.createSuccess("登录成功", resultDvr);
    }

    @Override
    public ServerResponse<AccessToken> zytGetDvrToken(String zytOpenid, String mobile) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        DriverInfo driverInfo = zytRegLogCheck(zytOpenid, mobile, null);
        if (StrUtil.isEmpty(driverInfo.getMobile())) return ServerResponse.createError("用户名错误");
        if (driverInfo.getState() == 2) return ServerResponse.createError("账户不可用");

        String token = JwtUtil.sign(UserType.DU, mobile, mobile.substring(7));

        //更新用户token记录
        Query<UserTokenRecord> tokenRecordQuery = userTokenRecordService.createQuery();
        tokenRecordQuery.criteria("userId").equal(driverInfo.getObjectId().toHexString() + "zyt");
        tokenRecordQuery.criteria("userType").equal("DU");
        UserTokenRecord tokenRecord = userTokenRecordService.get(tokenRecordQuery);
        if (tokenRecord == null) {
            tokenRecord = new UserTokenRecord();
            tokenRecord.setUserId(driverInfo.getObjectId().toHexString() + "zyt");
            tokenRecord.setUserType("DU");
            tokenRecord.setToken(token);
            userTokenRecordService.save(tokenRecord);
        } else {
            UpdateOperations<UserTokenRecord> updateTokenRecord = userTokenRecordService.createUpdateOperations();
            updateTokenRecord.set("token", token);
            userTokenRecordService.update(userTokenRecordService.createQuery().filter("userId", driverInfo.getObjectId().toHexString() + "zyt"), updateTokenRecord);
        }

        return ServerResponse.createSuccess("获取token成功", new AccessToken(token));
    }

    @Override
    public ServerResponse<List<String>> zytSearchDvrCar() {
        String did = ShiroUtils.getUserId();

        Query<DriverToCar> driverToCarQuery = driverToCarDao.createQuery();
        driverToCarQuery.filter("driverId", did);
        driverToCarQuery.filter("delete nin", 1);
        List<DriverToCar> driverToCars = driverToCarDao.list(driverToCarQuery);

        List<ObjectId> carIds = new ArrayList<>();
        for (DriverToCar driverToCar : driverToCars) {
            carIds.add(new ObjectId(driverToCar.getCarId()));
        }
        Query<Car> carQuery = carDao.createQuery();
        carQuery.field("_id").in(carIds);
        carQuery.or(
                carQuery.criteria("verify").equal(1),
                carQuery.criteria("verify").equal(3)
        );
        List<Car> cars = carDao.list(carQuery);

        List<String> resultList = new ArrayList<>();
        for (Car car : cars) {
            resultList.add(car.getCarNum());
        }
        return ServerResponse.createSuccess(resultList);
    }

    @Override
    public ServerResponse<String> updateTareWeight(String dvrToCarId, Double tareWeight) {
        DriverToCar exitDriverToCar = driverToCarDao.getByPK(dvrToCarId);
        if (exitDriverToCar == null) return ServerResponse.createError("错误修改");
        if (StrUtil.isEmpty(tareWeight)) tareWeight = 0.0;

        //修改车辆皮重信息
        Query<Car> query = carDao.createQuery();
        query.criteria("_id").equal(new ObjectId(exitDriverToCar.getCarId()));
        UpdateOperations<Car> updateOperation = carDao.createUpdateOperations();
        updateOperation.set("tareWeight", tareWeight);
        carDao.updateOne(query, updateOperation);
        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public boolean checkCarTareWeight(String did, String carNum) {
        if (StrUtil.isEmpty(carNum)) {
            DriverInfo driverInfo = this.getByPK(did);
            carNum = driverInfo.getCarNum();
        }

        Query<DriverToCar> toCarQuery = driverToCarDao.createQuery();
        toCarQuery.criteria("driverId").equal(did);
        toCarQuery.criteria("delete").equal(0);
        List<DriverToCar> driverToCars = driverToCarDao.list(toCarQuery);

        List<ObjectId> carIds = new ArrayList<>();
        for (DriverToCar toCar: driverToCars){
            carIds.add(new ObjectId(toCar.getCarId()));
        }

        Query<Car> carQuery = carDao.createQuery();
        carQuery.criteria("_id").in(carIds);
        carQuery.criteria("carNum").equal(carNum);
        carQuery.or(
                carQuery.criteria("verify").equal(1),
                carQuery.criteria("verify").equal(3)
        );
        Car car = carDao.get(carQuery);
        return StrUtil.isEmpty(car.getTareWeight());
    }
}
