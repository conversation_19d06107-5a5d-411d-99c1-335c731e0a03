package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;
import org.omg.CORBA.PRIVATE_MEMBER;

@Entity(value = "t_customer", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("mobile")}, options = @IndexOptions(unique = true))
})
public class CustomerUser extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "_idx_mobile", unique = true, background = true))
    private String mobile; //手机号
    private String name; //客商真实姓名
    private String password; //登录密码
    private String sex; //客商性别
    private String job; //客商职务
    private String company; //客商所属公司
    private String identity; //身份证号
    private String weixin; //微信
    private String qq;//QQ
    private String address;//住址
    private Integer state;//用户狀態    0-未认证，1-正常,2-停用
    private String abilityTag; //黑白名单标识 0:黑名单 1：白名单
    private String phoneId;//手机序列号
    private String deviceId;//阿里云推送 设备号
    private String fee;//设置默认收取司机的金额(元)

    private String barPwd;//交易密码

    private String openid;  //微信号
    private String nickname;//微信昵称
    private String wxName;  //微信账户真实姓名

    private String payeeType;//支付宝账户类型。可取值：
    // 1、ALIPAY_USERID：支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成。
    // 2、ALIPAY_LOGONID：支付宝登录号，支持邮箱和手机号格式。
    private String payeeAccount;//支付宝账户名称。

    private String initialOpenid;   //用户初始注册时的微信openid

    private boolean thirdPartyUser;   //是否三方用户 false-否，true-是

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getWeixin() {
        return weixin;
    }

    public void setWeixin(String weixin) {
        this.weixin = weixin;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getAbilityTag() {
        return abilityTag;
    }

    public void setAbilityTag(String abilityTag) {
        this.abilityTag = abilityTag;
    }

    public String getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(String phoneId) {
        this.phoneId = phoneId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getBarPwd() {
        return barPwd;
    }

    public void setBarPwd(String barPwd) {
        this.barPwd = barPwd;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getWxName() {
        return wxName;
    }

    public void setWxName(String wxName) {
        this.wxName = wxName;
    }

    public String getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(String payeeType) {
        this.payeeType = payeeType;
    }

    public String getPayeeAccount() {
        return payeeAccount;
    }

    public void setPayeeAccount(String payeeAccount) {
        this.payeeAccount = payeeAccount;
    }

    public String getInitialOpenid() {
        return initialOpenid;
    }

    public void setInitialOpenid(String initialOpenid) {
        this.initialOpenid = initialOpenid;
    }

    public boolean isThirdPartyUser() {
        return thirdPartyUser;
    }

    public void setThirdPartyUser(boolean thirdPartyUser) {
        this.thirdPartyUser = thirdPartyUser;
    }
}
