package com.erdos.coal.park.api.driver.dao.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.park.api.driver.dao.IDriverSignInDao;
import com.erdos.coal.park.api.driver.entity.DriverSignIn;
import org.springframework.stereotype.Repository;

@Repository("driverSignInDao")
public class DriverSignInDaoImpl extends BaseMongoDAOImpl<DriverSignIn> implements IDriverSignInDao {
}
