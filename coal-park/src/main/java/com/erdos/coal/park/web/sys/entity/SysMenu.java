package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.List;

@Entity(value = "sys_menu", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")}, options = @IndexOptions(unique = true))
})
public class SysMenu extends BaseMongoInfo {

    //@Indexed(options = @IndexOptions(name = "_idx_menu_id", unique = true, background = true))
    private String id;

    @Property("pId")
    private String _parentId; //父节点id

    private String text;

    private String state;//open/closed

    private String url;

    private String icon;

    private Integer type = 1; //类型: 系统0/业务1

    private Integer order = 0; //排序号

    private Integer enable = 1; //启用

    //private Map<String, Object> attributes = new HashMap<String, Object>(); // 添加到节点的自定义属性

    private List<SysMenu> children; //子节点数据

    public SysMenu() {
    }

    public SysMenu(String id, String text, Integer type, String pid) {
        this.id = id;
        this.text = text;
        this.type = type;
        this._parentId = pid;
    }

    public SysMenu(String id, String text, Integer type, String pid, Integer order) {
        this.id = id;
        this.text = text;
        this.type = type;
        this._parentId = pid;
        this.order = order;
    }

    public SysMenu(String id, String text, Integer type, String pid, String url) {
        this.id = id;
        this.text = text;
        this.type = type;

        this._parentId = pid;
        this.url = url;
    }

    public SysMenu(String id, String text, Integer type, String pid, String url, Integer order) {
        this.id = id;
        this.text = text;
        this.type = type;

        this._parentId = pid;
        this.url = url;
        this.order = order;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void set_parentId(String _parentId) {
        this._parentId = _parentId;
    }

    public String get_parentId() {
        return _parentId;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getState() {
        return (state == null || state.equals("0")) ? "closed" : "open";
    }

    public void setState(String state) {
        this.state = state.equals("0") ? "closed" : "open";
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

//    public Map<String, Object> getAttributes() {
//        return attributes;
//    }
//
//    public void setAttributes(Map<String, Object> attributes) {
//        this.attributes = attributes;
//    }

    public List<SysMenu> getChildren() {
        return children;
    }

    public void setChildren(List<SysMenu> children) {
        this.children = children;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }
}
