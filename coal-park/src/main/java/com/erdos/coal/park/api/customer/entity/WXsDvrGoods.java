package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;


@Entity(value = "t_wxs_dvr_goods", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("dvrGoodsId")}, options = @IndexOptions(unique = true)),
        @Index(fields = {@Field("gid")})
})
public class WXsDvrGoods extends BaseMongoInfo {
    private String dvrGoodsId;  //

    private String gid;

    private Integer mold;
    private String beginPoint;
    private String endPoint;
    private Integer total;
    private Double price;
    private String outVariety;
    private String inVariety;
    private String outBizContractName;      //发货单位业务合同名称
    private String inBizContractName;       //收货单位业务合同名称

    private Integer partNum;    //分享给司机们的批次车数
    private Integer usedNum;    //已有接单的司机数量

    @Reference(value = "customerUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser;

    @Reference(value = "shareCusUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser shareCusUser;

    public String getDvrGoodsId() {
        return dvrGoodsId;
    }

    public void setDvrGoodsId(String dvrGoodsId) {
        this.dvrGoodsId = dvrGoodsId;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public Integer getPartNum() {
        return partNum;
    }

    public void setPartNum(Integer partNum) {
        this.partNum = partNum;
    }

    public Integer getUsedNum() {
        return usedNum;
    }

    public void setUsedNum(Integer usedNum) {
        this.usedNum = usedNum;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }

    public CustomerUser getShareCusUser() {
        return shareCusUser;
    }

    public void setShareCusUser(CustomerUser shareCusUser) {
        this.shareCusUser = shareCusUser;
    }
}
