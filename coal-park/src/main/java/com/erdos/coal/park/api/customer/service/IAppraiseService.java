package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Appraise;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.driver.entity.DriverInfo;

import java.util.List;

public interface IAppraiseService extends IBaseMongoService<Appraise> {

    //查询订单评价列表
    //ServerResponse<List<Appraise>> appraiseList(Integer type);
    ServerResponse<EGridResult> appraiseList(Integer type, Integer page, Integer rows);
    ServerResponse<EGridResult> appraiseList2(Integer type, Integer page, Integer rows);

    //提交订单评价
    ServerResponse<String> putAppraise(String oid, Integer starsNum, boolean complain, String reasons);

    //添加已完成的订单 的待评价记录
    String addAppraise(Order order, DriverInfo dInfo, OrderTaking orderTaking, CustomerUser customerUser);
}
