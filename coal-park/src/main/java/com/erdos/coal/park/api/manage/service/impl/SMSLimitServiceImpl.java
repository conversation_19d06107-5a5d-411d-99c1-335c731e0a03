package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.manage.dao.ISMSLimitDao;
import com.erdos.coal.park.api.manage.entity.SMSLimit;
import com.erdos.coal.park.api.manage.service.ISMSLimitService;
import com.erdos.coal.utils.IdUnit;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("smsLimitService")
public class SMSLimitServiceImpl extends BaseMongoServiceImpl<SMSLimit, ISMSLimitDao> implements ISMSLimitService {
    @Override
    public int smsTotalByMobile(String mobile) {
        Query<SMSLimit> query = this.createQuery();
        query.criteria("mobile").equal(mobile);
        query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(System.currentTimeMillis(), -3));

        List<SMSLimit> smsLimit = this.list(query);
        return smsLimit.size();
    }

    @Override
    public void addSmsLimit(String mobile) {
        SMSLimit smsLimit = new SMSLimit();
        smsLimit.setMobile(mobile);
        this.save(smsLimit);
    }
}
