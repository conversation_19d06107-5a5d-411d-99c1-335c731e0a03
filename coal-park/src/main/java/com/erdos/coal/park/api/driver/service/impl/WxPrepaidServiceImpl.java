package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.push.model.v20160801.QueryUniqueDeviceStatRequest;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.park.api.driver.dao.IWxPrepaidDao;
import com.erdos.coal.park.api.driver.dao.IWxResultDao;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WxPrepaid;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.pojo.Receiver;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IOrderRecordService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.sys.entity.SeparateAccounts;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISeparateAccountsService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.*;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.BsonDocument;
import org.bson.Document;
import org.hibernate.validator.constraints.pl.REGON;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("wxPrepaidService")
public class WxPrepaidServiceImpl extends BaseMongoServiceImpl<WxPrepaid, IWxPrepaidDao> implements IWxPrepaidService {
    @Resource
    private IWxResultDao wxResultDao;
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private ILockedService lockedService;
    @Resource
    private WXPayDriverConfig driverConfig;
    @Resource
    private WXPayCustomerConfig customerConfig;
    @Resource
    private WXPayWechatConfig wxPayWechatConfig;
    @Resource
    private WXPayWechatCusConfig wxPayWechatCusConfig;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Lazy
    @Resource
    private IGoodsService goodsService;
    @Resource
    private MongoClient client;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Lazy
    @Resource
    private IOrderRecordService orderRecordService;
    @Lazy
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private ISeparateAccountsService separateAccountsService;
    @Resource
    private WXPayV3 wxPayV3;

    @Override
    public ServerResponse<WxResult> wxQueryPay(String transactionId, String outTradeNo, String cdid, String driOrCus) {

        Map<String, String> map = new HashMap<>();
        if (StrUtil.isEmpty(transactionId)) {
            if (StrUtil.isEmpty(outTradeNo)) {
                return ServerResponse.createError("微信订单号/商户订单号不能为空");
            } else {
                map.put("out_trade_no", outTradeNo);
            }
        } else {
            map.put("transaction_id", transactionId);
        }

        WxResult wxResult = payResult(map, 1, cdid, driOrCus);//加锁
        if (ObjectUtil.isNull(wxResult)) {
            throw new GlobalException(ResponseCode.PAY_QUERY_FAIL);
        }
        return ServerResponse.createSuccess("查询成功", wxResult);
    }

    //加锁
    @Transactional
    public WxResult payResult(Map<String, String> map, Integer flag, String cdid, String driOrCus) {

        cdid = (cdid == null || cdid.equals("") ? map.get("attach") : cdid);
        boolean lock = lockedService.getLock(cdid, SysConstants.LockType.WX_PAY_RESULT.getType());//加锁
        if (!lock) {
            return null;
        } else {
            try {

                WxResult wxResult = null;
                if (StrUtil.isNotEmpty(map.get("transaction_id"))) {
                    wxResult = wxResultDao.get("transactionId", map.get("transaction_id"));
                }

                if (ObjectUtil.isNull(wxResult)) {
                    wxResult = wxResultDao.get("outTradeNo", map.get("out_trade_no"));

                    if (ObjectUtil.isNull(wxResult)) {
                        if (flag == 1) {    //判断是查询订单还是回调
                            try {
                                WXPay wxpay = new WXPay(driverConfig, wxPayConstants.notifyUrl, true, false);
                                if (driOrCus.equals("customer")) {
                                    wxpay = new WXPay(customerConfig, wxPayConstants.notifyUrl, true, false);
                                }

                                map = wxpay.orderQuery(map);
                            } catch (Exception e) {
                                e.printStackTrace();
                                return null;
                            }
                        }

                        if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS")) {

                            WxPrepaid wxPrepaid = this.get("outTradeNo", map.get("out_trade_no"));
                            if (ObjectUtil.isNull(wxPrepaid)) {
                                return null;
                            }

                            MongoCollection<Document> resultCollection = wxResultDao.getCollection();
                            MongoCollection<Document> prepaidCollection = this.getCollection();
                            MongoCollection<Document> accountCollection = driverAccountService.getCollection();
                            ClientSession clientSession = client.startSession();
                            try {
                                clientSession.startTransaction();

                                wxResult = new WxResult();
                                wxResult.setReturnCode(map.get("return_code"));
                                wxResult.setBankType(map.get("bank_type"));
                                wxResult.setCashFee(Integer.parseInt(map.get("cash_fee")));
                                wxResult.setFeeType(map.get("fee_type"));
                                wxResult.setIsSubscribe(map.get("is_subscribe"));
                                wxResult.setNonceStr(map.get("nonce_str"));
                                wxResult.setOpenid(map.get("openid"));
                                wxResult.setOutTradeNo(map.get("out_trade_no"));
                                wxResult.setResultCode(map.get("result_code"));
                                wxResult.setSign(map.get("sign"));
                                wxResult.setTimeEnd(map.get("time_end"));
                                Integer l = Utils.parseInt(map.get("total_fee"), 0);
                                wxResult.setTotalFee(l);
                                wxResult.setTradeType(map.get("trade_type"));
                                wxResult.setTransactionId(map.get("transaction_id"));
                                wxResult.setUpdateTime(new Date().getTime());

                                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                                document.append("createTime", new Date());

                                resultCollection.insertOne(clientSession, document);//保存支付成功结果

                                Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
                                Map<String, Object> updMap = new HashMap<>();
                                Map<String, Object> params = new HashMap<>();
                                updMap.put("pay", true);
                                updMap.put("updateTime", new Date().getTime());
                                params.put("$set", updMap);
                                prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(params)));//更新预支付表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

                                //司机账户
                                DriverAccount driverAccount = new DriverAccount();
                                driverAccount.setDid(wxPrepaid.getDid());
                                driverAccount.setCdid(wxPrepaid.getCdid());
                                driverAccount.setType(0);
                                driverAccount.setOutTradeNo(map.get("out_trade_no"));
                                driverAccount.setTransactionId(map.get("transaction_id"));
                                driverAccount.setUpdateTime(new Date().getTime());

                                Document accountDoc = Document.parse(JSONObject.toJSONString(driverAccount));
                                accountDoc.append("createTime", new Date());
                                accountDoc.append("totalFee", BigDecimal.valueOf(l));//微信 单位为分
                                accountCollection.insertOne(clientSession, accountDoc);

                                clientSession.commitTransaction();
                            } catch (Exception e) {
                                clientSession.abortTransaction();
                                return null;
                            } finally {
                                clientSession.close();
                            }
                        }
                    }
                }

                return wxResult;

            } finally {
                lockedService.unLock(cdid, SysConstants.LockType.WX_PAY_RESULT.getType());//解锁
            }
        }
    }

    @Override
    public ServerResponse<Map<String, String>> wxRequestPay(String body, Integer totalFee, String did, String driOrCus) {

        String driverId;
        if (driOrCus.equals("customer")) {
            DriverPool dp = driverPoolService.getByPK(did); //参数中的did实际是从客商司机池中查询所得，所以did是司机池的编号
            driverId = dp.getDid();
        } else {
            driverId = did;
        }
        DriverInfo driverInfo = driverInfoService.getByPK(driverId); //客商和司机都不可以给未审核的司机充值
        if (null == driverInfo || (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))) {
            return ServerResponse.createError("请检查司机是否存在或司机未通过审核，不能充值");
        }

        String cdid = ShiroUtils.getUserId();

        boolean lock = lockedService.getLock(cdid, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) {//未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {

                Map<String, String> map = new HashMap<>();

                Map<String, String> reMap = new HashMap<>();

                long timeStart = System.currentTimeMillis();
                String timestamp = String.format("%010d", timeStart / 1000);

                String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

                map.put("total_fee", String.valueOf(totalFee * 100));
                //map.put("total_fee", "1");//测试金额设置为1分钱
                map.put("trade_type", "APP");

                try {
                    InetAddress address = InetAddress.getLocalHost();
                    map.put("spbill_create_ip", address.getHostAddress());
                    map.put("out_trade_no", out_trade_no);
                    map.put("body", body);
                    map.put("time_start", dateFormat.format(timeStart));
                    map.put("attach", cdid);

                    //判断是司机app还是客商app
                    WXPay wxpay = new WXPay(driverConfig, wxPayConstants.notifyUrl, true, false);
                    if (driOrCus.equals("customer")) {
                        wxpay = new WXPay(customerConfig, wxPayConstants.notifyUrl, true, false);
                    }
                    map = wxpay.unifiedOrder(map);//统一下单

                    String return_code = map.get("return_code");//状态

                    if (return_code.equals("FAIL") || out_trade_no == null) {
                        return ServerResponse.createError("统一下单失败", map);
                    }

                    // 业务逻辑处理 ****************************
                    WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
                    prepaid.setBody(body);
                    prepaid.setType(1);//账户充值
                    prepaid.setTotalFee(totalFee * 100);
//                    prepaid.setTotalFee(1);//测试金额设置为1分钱
                    prepaid.setSpbillCreateIp(address.getHostAddress());
                    String result_code = map.get("result_code");
                    if (result_code.equals("SUCCESS")) {
                        prepaid.setTradeType("APP");
                        prepaid.setOutTradeNo(out_trade_no);
                        prepaid.setPrepayId(map.get("prepay_id"));

                    }
                    prepaid.setNonceStr(map.get("nonce_str"));
                    prepaid.setSign(map.get("sign"));
                    prepaid.setAppid(map.get("appid"));
                    prepaid.setMchId(map.get("mch_id"));
                    prepaid.setDid(did);
                    prepaid.setCdid(cdid);
                    prepaid.setAttach(cdid);
                    this.save(prepaid);

                    //app端需要生成二次签名
                    reMap.put("timestamp", timestamp);//app端需要以秒为单位的10位数字

                    if (driOrCus.equals("customer")) {
                        reMap.put("appid", customerConfig.getAppID());
                    } else {
                        reMap.put("appid", driverConfig.getAppID());
                    }
                    reMap.put("partnerid", driverConfig.getMchID());//司机和客商的商户号和密钥使用的是同一个
                    reMap.put("prepayid", map.get("prepay_id"));
                    reMap.put("package", "Sign=WXPay");
                    reMap.put("noncestr", map.get("nonce_str"));
                    reMap.put("sign", WXPayUtil.generateSignature(reMap, driverConfig.getKey(), WXPayConstants.SignType.MD5));
                    reMap.put("out_trade_no", out_trade_no);//订单号返回给app端方便查询支付是否成功

                } catch (Exception e) {
                    e.printStackTrace();
                    return ServerResponse.createError("统一下单失败");
                }

                return ServerResponse.createSuccess("统一下单成功", reMap);
            } finally {
                lockedService.unLock(cdid, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁

            }
        }
    }

    @Override
    public ServerResponse<Map<String, String>> wxRequestPay3(String body, Integer totalFee) {
        String cid = ShiroUtils.getUserId();

        boolean lock = lockedService.getLock(cid, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) {//未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {
                if (StrUtil.isEmpty(totalFee) || totalFee == 0)
                    return ServerResponse.createError("支付金额不能为空");

                WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
                prepaid.setCid(cid);
                ServerResponse<Map<String, String>> response = createWXOrder(body, totalFee, cid, prepaid);

                return response;

            } finally {
                lockedService.unLock(cid, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁

            }
        }
    }

    private ServerResponse<Map<String, String>> createWXOrder(String body, Integer totalFee, String cdid, WxPrepaid prepaid) {
        long timeStart = System.currentTimeMillis();
        String timestamp = String.format("%010d", timeStart / 1000);
        String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

        Map<String, String> map = new HashMap<>();
        map.put("total_fee", String.valueOf(totalFee * 100));
//        map.put("total_fee", "1");//测试金额设置为1分钱
        map.put("trade_type", "APP");

        Map<String, String> reMap = new HashMap<>();
        try {
            InetAddress address = InetAddress.getLocalHost();
            map.put("spbill_create_ip", address.getHostAddress());
            map.put("out_trade_no", out_trade_no);
            map.put("body", body);
            map.put("time_start", dateFormat.format(timeStart));
            //map.put("attach", cdid);
            map.put("attach", "Semicolon," + cdid);
            WXPay wxpay = new WXPay(customerConfig, wxPayConstants.notifyUrl, true, false);
            map = wxpay.unifiedOrder(map);//统一下单

            String return_code = map.get("return_code");//状态

            if (return_code.equals("FAIL") || out_trade_no == null) {
                return ServerResponse.createError("统一下单失败", map);
            }

            // 业务逻辑处理 ****************************
            if (map.get("result_code").equals("SUCCESS")) {
                prepaid.setTradeType("APP");
                prepaid.setOutTradeNo(out_trade_no);
                prepaid.setPrepayId(map.get("prepay_id"));
            }
            prepaid.setBody(body);
            prepaid.setType(2);//订单付款
            prepaid.setTotalFee(totalFee * 100);
//            prepaid.setTotalFee(1);//测试金额设置为1分钱
            prepaid.setSpbillCreateIp(address.getHostAddress());
            prepaid.setNonceStr(map.get("nonce_str"));
            prepaid.setSign(map.get("sign"));
            prepaid.setAppid(map.get("appid"));
            prepaid.setMchId(map.get("mch_id"));
            //prepaid.setDid(did);
            prepaid.setCdid(cdid);
            //prepaid.setAttach(cdid);
            prepaid.setAttach("Semicolon," + cdid);
            this.save(prepaid);

            //app端需要生成二次签名
            reMap.put("timestamp", timestamp);//app端需要以秒为单位的10位数字
            reMap.put("appid", customerConfig.getAppID());
            reMap.put("partnerid", customerConfig.getMchID());
            reMap.put("prepayid", map.get("prepay_id"));
            reMap.put("package", "Sign=WXPay");
            reMap.put("noncestr", map.get("nonce_str"));
            reMap.put("sign", WXPayUtil.generateSignature(reMap, customerConfig.getKey(), WXPayConstants.SignType.MD5));
            reMap.put("out_trade_no", out_trade_no);

        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createError("统一下单失败");
        }
        return ServerResponse.createSuccess("统一下单成功", reMap);
    }

    @Override
    public ServerResponse<WxResult> wxQueryPay2(String transactionId, String outTradeNo) {
        String cid = ShiroUtils.getUserId();

        Map<String, String> map = new HashMap<>();
        if (StrUtil.isNotEmpty(transactionId)) {
            map.put("transaction_id", transactionId);
        } else {
            if (StrUtil.isNotEmpty(outTradeNo)) {
                map.put("out_trade_no", outTradeNo);
            } else {
                return ServerResponse.createError("微信订单号/商户订单号不能为空");
            }
        }

        WxResult wxResult = payResult2(map, 1, cid);//加锁
        if (ObjectUtil.isNull(wxResult)) {
            return new ServerResponse<>(8, "查询失败，请稍后重试");
        }
        return ServerResponse.createSuccess("查询成功", wxResult);
    }

    @Override
    public WxResult payResult2(Map<String, String> map, Integer flag, String cdid) {
        boolean lock = lockedService.getLock(cdid, SysConstants.LockType.WX_PAY_RESULT.getType());//加锁
        if (!lock) {  //没得到锁
            return null;
        } else {
            try {

                WxResult wxResult = wxResultDao.get("transactionId", map.get("transaction_id"));
                if (ObjectUtil.isNull(wxResult)) {
                    wxResult = wxResultDao.get("outTradeNo", map.get("out_trade_no"));
                    if (ObjectUtil.isNull(wxResult)) {
                        try {
                            if (flag == 1) {    //判断是查询订单还是回调
                                WXPay wxpay = new WXPay(customerConfig, wxPayConstants.notifyUrl, true, false);
                                map = wxpay.orderQuery(map);
                            }

                            WxPrepaid prepaid = this.get("outTradeNo", map.get("out_trade_no"));
                            if (prepaid != null || StrUtil.isNotEmpty(map.get("total_fee")) || Integer.valueOf(map.get("total_fee")) - prepaid.getTotalFee() == 0)
                                wxResult = payResult3(map, prepaid);
                        } catch (Exception e) {
                            e.printStackTrace();
                            return null;
                        }
                    }
                }
                return wxResult;

            } finally {
                lockedService.unLock(cdid, SysConstants.LockType.WX_PAY_RESULT.getType());//解锁
            }

        }
    }

    //加锁
    @Transactional
    public WxResult payResult3(Map<String, String> map, WxPrepaid prepaid) {
        WxResult wxResult = null;

        if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS")) {
            MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
            MongoCollection<Document> resultCollection = wxResultDao.getCollection();
            MongoCollection<Document> prepaidCollection = this.getCollection();
            ClientSession clientSession = client.startSession();
            Date date = new Date();

            try {
                clientSession.startTransaction();

                Document cAccountDoc = customerAccountService.createCusAccountDoc(prepaid.getCid(), new BigDecimal(prepaid.getTotalFee()), 1, prepaid.getOutTradeNo(), prepaid.getTransactionId(), date);
                cAccountCollection.insertOne(clientSession, cAccountDoc);

                wxResult = new WxResult();
                wxResult.setReturnCode(map.get("return_code"));
                wxResult.setBankType(map.get("bank_type"));
                wxResult.setCashFee(Integer.parseInt(map.get("cash_fee")));
                wxResult.setFeeType(map.get("fee_type"));
                wxResult.setIsSubscribe(map.get("is_subscribe"));
                wxResult.setNonceStr(map.get("nonce_str"));
                wxResult.setOpenid(map.get("openid"));
                wxResult.setOutTradeNo(map.get("out_trade_no"));
                wxResult.setResultCode(map.get("result_code"));
                wxResult.setSign(map.get("sign"));
                wxResult.setTimeEnd(map.get("time_end"));
                Integer l = Utils.parseInt(map.get("total_fee"), 0);
                wxResult.setTotalFee(l);
                wxResult.setTradeType(map.get("trade_type"));
                wxResult.setTransactionId(map.get("transaction_id"));
                wxResult.setUpdateTime(new Date().getTime());

                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                document.append("createTime", new Date());

                resultCollection.insertOne(clientSession, document);//保存支付成功结果

                Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
                Map<String, Object> updMap = new HashMap<>();
                Map<String, Object> params = new HashMap<>();
                updMap.put("pay", true);
                updMap.put("updateTime", new Date().getTime());
                params.put("$set", updMap);
                prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(params)));//更新预支付表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

                clientSession.commitTransaction();
            } catch (Exception e) {
                clientSession.abortTransaction();
                return null;
            } finally {
                clientSession.close();
            }
        }
        return wxResult;
    }

    //根据当前用户类型，获取WXPay支付对象
    private WXPay getWxPayByUserType() throws Exception {
        WXPay wxpay;
        switch (Objects.requireNonNull(ShiroUtils.getUserType())) {
            case "CU":
                wxpay = new WXPay(wxPayWechatCusConfig, wxPayConstants.notifyUrl, true, false);
                //wxpay = new WXPay(customerConfig,wxPayConstants.notifyUrl, true, false);
                break;
            case "DU":
                wxpay = new WXPay(wxPayWechatConfig, wxPayConstants.notifyUrl, true, false);
                //wxpay = new WXPay(driverConfig, wxPayConstants.notifyUrl, true, false);
                break;
                /*case "WECHATCU":
                    wxpay = new WXPay(wxPayWechatCusConfig, wxPayConstants.notifyUrl, true, false);
                    break;
                case "WECHAT":
                    wxpay = new WXPay(wxPayWechatConfig, wxPayConstants.notifyUrl, true, false);
                    break;*/
            default:
                return null;// ServerResponse.createError("参数错误");
        }
        return wxpay;
    }

    @Override
    public ServerResponse<WxResult> weChatQueryPay(String transactionId, String outTradeNo) {
        Map<String, String> map = new HashMap<>();

        try {
            //1.订单查询参数不可全为空
            if (StrUtil.isNotEmpty(transactionId)) {
                map.put("transaction_id", transactionId);
            } else if (StrUtil.isNotEmpty(outTradeNo)) {
                map.put("out_trade_no", outTradeNo);
            } else {
                return ServerResponse.createError("微信订单号/商户订单号不能为空");
            }

            //2.平台业务处理支付结果
            WxResult wxResult = weChatPayResult(ShiroUtils.getUserId(), map, 1);//加锁
            if (ObjectUtil.isNull(wxResult))
                throw new GlobalException(ResponseCode.PAY_QUERY_FAIL);

            return ServerResponse.createSuccess("查询成功", wxResult);
        } catch (Exception e) {
            return ServerResponse.createError("WXPay错误！");
        }
    }

    @Override
    public WxResult weChatPayResult(String userId, Map<String, String> map, Integer flag) {
        boolean lock = lockedService.getLock(userId, SysConstants.LockType.WX_PAY_RESULT.getType());//加锁
        if (!lock) {  //没得到锁
            return null;
        } else {
            try {
                WxResult wxResult = wxResultDao.get("transactionId", map.get("transaction_id"));
                if (ObjectUtil.isNull(wxResult)) {
                    wxResult = wxResultDao.get("outTradeNo", map.get("out_trade_no"));
                    if (ObjectUtil.isNull(wxResult)) {  //平台支付结果信息为空时，处理支付结果；否则直接返回
                        try {
                            if (flag == 1) {    //判断是查询订单还是回调,1 订单查询
                                //获取WXPay对象
                                WXPay wxpay = getWxPayByUserType();
                                map = wxpay.orderQuery(map);
                            }

                            if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS")) {
                                //判断微信支付系统推送支付结果信息和预支付信息金额是否一致
                                WxPrepaid prepaid = this.get("outTradeNo", map.get("out_trade_no"));
                                if (prepaid != null || StrUtil.isNotEmpty(map.get("total_fee")) || Integer.valueOf(map.get("total_fee")) - prepaid.getTotalFee() == 0)
                                    wxResult = payResult(map, prepaid);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return null;
                        }
                    }
                }
                return wxResult;

            } finally {
                lockedService.unLock(userId, SysConstants.LockType.WX_PAY_RESULT.getType());//解锁
            }

        }
    }

    @Override
    public WxResult weChatPayResult3(String userId, Map<String, String> map, Integer flag) {
        boolean lock = lockedService.getLock(userId, SysConstants.LockType.WX_PAY_RESULT.getType());//加锁
        if (!lock) {  //没得到锁
            return null;
        } else {
            try {
                WxResult wxResult = wxResultDao.get("transactionId", map.get("transaction_id"));
                if (ObjectUtil.isNull(wxResult)) {
                    wxResult = wxResultDao.get("outTradeNo", map.get("out_trade_no"));
                    if (ObjectUtil.isNull(wxResult)) {  //平台支付结果信息为空时，处理支付结果；否则直接返回
                        try {
                            if (flag == 1) {    //判断是查询订单还是回调,1 订单查询
                                //获取WXPay对象
                                WXPay wxpay = getWxPayByUserType();
                                map = wxpay.orderQuery(map);
                            }

                            if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS")) {
                                //判断微信支付系统推送支付结果信息和预支付信息金额是否一致
                                WxPrepaid prepaid = this.get("outTradeNo", map.get("out_trade_no"));
                                if (prepaid != null || StrUtil.isNotEmpty(map.get("total_fee")) || Integer.valueOf(map.get("total_fee")) - prepaid.getTotalFee() == 0)
                                    wxResult = payResult(map, prepaid);

                                // 判断分账接收方本自然月接收分账金额是否达到18.2W，达到则停止分账
                                /*String account1 = "oC-ha5M4IbWjIhhGyztFPnad1biU";
                                String accountName1 = "";
                                String account2 = "";
                                String accountName2 = "";
                                Integer totalAccounts = separateAccountsService.sumSeparateAccountsWithMonth(account1);  // 自然月已分账总金额
                                String fenzhangStr = String.valueOf(prepaid.getTotalFee() / 100 * 0.3);              // 元
                                String fenzhangStrZhengshu = fenzhangStr.substring(0, fenzhangStr.indexOf("."));    // 整数部分
                                Integer fenzhangyuan = Integer.valueOf(fenzhangStrZhengshu);                        // 元
                                Integer fenzhang = fenzhangyuan * 100;                                              // 分
                                if ((totalAccounts + fenzhangyuan) < 182000) {
                                    //订单分配成功,执行异步分账
                                    toThirdParty3(prepaid, wxResult, account1, account2, fenzhang, 0, accountName1, accountName2);
                                }*/
                                beforeMinSeparate();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return null;
                        }
                    }
                }
                return wxResult;

            } finally {
                lockedService.unLock(userId, SysConstants.LockType.WX_PAY_RESULT.getType());//解锁
            }

        }
    }

    //微信支付结果信息 添加 到WxResult对象
    private WxResult saveWxResult(Map<String, String> map) {
        WxResult wxResult = new WxResult();
        wxResult.setReturnCode(map.get("return_code"));
        wxResult.setBankType(map.get("bank_type"));
        wxResult.setCashFee(Integer.parseInt(map.get("cash_fee")));
        wxResult.setFeeType(map.get("fee_type"));
        wxResult.setIsSubscribe(map.get("is_subscribe"));
        wxResult.setNonceStr(map.get("nonce_str"));
        wxResult.setOpenid(map.get("openid"));
        wxResult.setOutTradeNo(map.get("out_trade_no"));
        wxResult.setResultCode(map.get("result_code"));
        wxResult.setSign(map.get("sign"));
        wxResult.setTimeEnd(map.get("time_end"));
        Integer l = Utils.parseInt(map.get("total_fee"), 0);
        wxResult.setTotalFee(l);
        wxResult.setTradeType(map.get("trade_type"));
        wxResult.setTransactionId(map.get("transaction_id"));
        wxResult.setUpdateTime(new Date().getTime());
        return wxResult;
    }

    /**
     * 处理微信订单支付成功信息
     */
    //加锁
    @Transactional
    public WxResult payResult(Map<String, String> map, WxPrepaid prepaid) {
        WxResult wxResult = saveWxResult(map);

        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> resultCollection = wxResultDao.getCollection();
        MongoCollection<Document> prepaidCollection = this.getCollection();
        ClientSession clientSession = client.startSession();
        Date date = new Date();

        try {
            clientSession.startTransaction();
            //1.微信订单支付结果保存到数据库中
            Document document = Document.parse(JSONObject.toJSONString(wxResult));
            document.append("createTime", new Date());
            resultCollection.insertOne(clientSession, document);

            //2.预支付信息中客商编号cid不为空，则微信支付订单为 客商为 客商自己 充值
            if (StrUtil.isNotEmpty(prepaid.getCid())) {
                Document cAccountDoc = customerAccountService.createCusAccountDoc(prepaid.getCid(), new BigDecimal(prepaid.getTotalFee()), 1, prepaid.getOutTradeNo(), map.get("transaction_id"), date);
                cAccountCollection.insertOne(clientSession, cAccountDoc);
            }
            //3.预支付信息中司机编号did不为空，则微信支付订单为 客商或司机为 司机 充值
            if (StrUtil.isNotEmpty(prepaid.getDid())) {
                Document accountDoc = driverAccountService.createDriAccountDoc(prepaid.getDid(), prepaid.getCdid(), BigDecimal.valueOf(Utils.parseInt(map.get("total_fee"), 0)), 0, map.get("out_trade_no"), map.get("transaction_id"), date);
                dAccountCollection.insertOne(clientSession, accountDoc);
            }

            //更新预支付表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );
            Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("pay", true);
            updMap.put("updateTime", new Date().getTime());
            Map<String, Object> params = new HashMap<>();
            params.put("$set", updMap);
            prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(params)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return wxResult;
    }

    /**
     * 处理微信包含分账订单支付成功信息
     */
    @Override
    public WxResult weChatPayResult2(Map<String, String> map, Integer flag) {
        try {
            WxResult wxResult = wxResultDao.get("transactionId", map.get("transaction_id"));
            if (ObjectUtil.isNull(wxResult)) {
                wxResult = wxResultDao.get("outTradeNo", map.get("out_trade_no"));
                if (ObjectUtil.isNull(wxResult)) {  //平台支付结果信息为空时，处理支付结果；否则直接返回
                    try {
                        if (flag == 1) {    //判断是查询订单还是回调,1 订单查询
                            //获取WXPay对象
                            WXPay wxpay = getWxPayByUserType();
                            map = wxpay.orderQuery(map);
                        }

                        Query<WxPrepaid> query = this.createQuery();
                        query.criteria("outTradeNo").equal(map.get("out_trade_no"));
                        query.criteria("type").equal(1);
                        WxPrepaid prepaid = this.get(query);

                        String orderErrorMsg = "支付失败";
                        if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS")) {
                            //判断微信支付系统推送支付结果信息和预支付信息金额是否一致
                            if (prepaid != null || StrUtil.isNotEmpty(map.get("total_fee")) || Integer.valueOf(map.get("total_fee")) - prepaid.getTotalFee() == 0) {
                                wxResult = payResult2(map);
                                int result = orderRecordService.distributionOrder(prepaid);
                                if (result == -2) {
                                    orderErrorMsg = "接受订单失败，请重试";
                                } else if (result == -5) {
                                    orderErrorMsg = "发货端余额不足";
                                } else if (result == -6) {
                                    orderErrorMsg = "收货端余额不足";
                                } else if (result == -7) {
                                    orderErrorMsg = "客商余额不足";
                                } else {
                                    orderErrorMsg = null;

                                    // 判断分账接收方本自然月接收分账金额是否达到16.2W，达到则停止分账
                                    SysUnit sysUnit = sysUnitService.get("code", "**********");     // 分账接收方只定为一个，暂时将接收方账号配置在准泰单位下thirdPartyAccount
                                    String account = sysUnit.getThirdPartyAccount();
                                    Integer totalAccounts = separateAccountsService.sumSeparateAccountsWithMonth(account);  // 自然月已分账总金额
                                    BigDecimal totalFee = new BigDecimal(prepaid.getTotalFee());        //金额单位分
                                    BigDecimal separateFee = totalFee.divide(new BigDecimal(100)).multiply(new BigDecimal(0.3)).setScale(0, RoundingMode.DOWN); //金额单位元
                                    if ((totalAccounts + separateFee.intValue()) < 162000) {
                                        //订单分配成功,执行异步分账
                                        // toThirdParty(prepaid, wxResult);
                                        //toThirdParty2(prepaid, wxResult, account, separateFee.multiply(new BigDecimal(100)).intValue(), sysUnit.getThirdPartyName());
                                    }
                                }
                            }
                        }
                        //订单分配失败，则退款
                        if (StrUtil.isNotEmpty(orderErrorMsg)) refundToDvr(prepaid, orderErrorMsg);
                        //解锁订单
                        orderRecordService.unLockOrder(prepaid.getOrderId());
                    } catch (Exception e) {
                        e.printStackTrace();
                        return null;
                    }
                }
            }
            return wxResult;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 处理微信订单支付成功信息
     * 1.保存支付结果
     * 2.修改预支付信息
     */
    //加锁
    @Transactional
    public WxResult payResult2(Map<String, String> map) {
        WxResult wxResult = saveWxResult(map);

        MongoCollection<Document> resultCollection = wxResultDao.getCollection();
        MongoCollection<Document> prepaidCollection = this.getCollection();
        ClientSession clientSession = client.startSession();

        try {
            clientSession.startTransaction();
            //1.微信订单支付结果保存到数据库中
            Document document = Document.parse(JSONObject.toJSONString(wxResult));
            document.append("createTime", new Date());
            resultCollection.insertOne(clientSession, document);

            //更新预支付表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );
            Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("updateTime", new Date().getTime());
            updMap.put("pay", true);
            Map<String, Object> params = new HashMap<>();
            params.put("$set", updMap);
            prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(params)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return wxResult;
    }

    /**
     * 包含分账的订单，司机支付完成并成功分配到订单，则请求微信分账
     */
    @Async("busTaskExecutor")
    public void toThirdParty(WxPrepaid prepaid, WxResult result) throws Exception {
        Map<String, String> certMap = wxPayV3.getCertificates();

        String transactionId = result.getTransactionId();

        List<String> billCodes = prepaid.getBillCodes();
        JSONArray receiverArray = new JSONArray();
        List<WxPrepaid> prepaids = new ArrayList<>();
        for (String billCode : billCodes) {
            String code = billCode.substring(0, 10);
            SysUnit sysUnit = sysUnitService.get("code", code);
            String accountType = "PERSONAL_OPENID";
            String account = sysUnit.getThirdPartyAccount();
            if (StrUtil.isNotEmpty(sysUnit.getThirdPartyAccount2())) {
                accountType = "MERCHANT_ID";
                account = sysUnit.getThirdPartyAccount2();
            }

            String outOrderNo = billCode;
            //查询校验是否已经完成分账
            Query<WxPrepaid> prepaidQuery = this.createQuery();
            prepaidQuery.criteria("type").equal(4);
            prepaidQuery.filter("transactionId", transactionId);
            prepaidQuery.filter("outOrderNo", outOrderNo);
            prepaidQuery.filter("pay", true);
            //根据“分账单号，商户订单号”判断订单是否多次重复提交
            WxPrepaid refundPrepaid = this.get(prepaidQuery);
            if (refundPrepaid == null) {//"该订单已经分账，则不处理
                //添加预分账请求记录
                WxPrepaid wxPrepaid = new WxPrepaid();
                wxPrepaid.setAppid(wxPayWechatConfig.getAppID());
                wxPrepaid.setMchId(wxPayWechatConfig.getMchID());
                wxPrepaid.setType(4);//分账
                wxPrepaid.setTransactionId(transactionId);
                wxPrepaid.setOutOrderNo(outOrderNo);//商户分账单号
                wxPrepaid.setAccountType(accountType);         //分账接收方类型
                wxPrepaid.setAccount(account);             //分账接收方账号
                wxPrepaid.setName(sysUnit.getThirdPartyName());                //分账接收方姓名
                wxPrepaid.setAmount(sysUnit.getThirdPartyLedger());              //分账金额
                wxPrepaid.setDescription("分给商户" + sysUnit.getThirdPartyName());         //分账原因
                wxPrepaid.setUnfreezeUnsplit(true);
                wxPrepaid.setPay(false);            //微信分账返回成功后改为true
                prepaids.add(wxPrepaid);

                JSONObject receiver = new JSONObject();
                receiver.put("type", accountType);
                receiver.put("account", account);

                if ("MERCHANT_ID".equals(accountType)) {
                    receiver.put("name", sysUnit.getThirdPartyName());
                } else {
                    receiver.put("name", wxPayV3.rsaEncryptOAEP(sysUnit.getThirdPartyName(), certMap));
                }
                receiver.put("amount", sysUnit.getThirdPartyLedger());
                receiver.put("description", outOrderNo + "分给商户" + sysUnit.getThirdPartyName());
                receiverArray.add(receiver);
            }
        }
        if (receiverArray.size() > 0) {
            //请求微信分账
            JSONObject param = new JSONObject();
            param.put("appid", wxPayWechatCusConfig.getAppID());
            param.put("transaction_id", transactionId);
            param.put("out_order_no", prepaid.getOrderId());
            param.put("receivers", receiverArray);
            param.put("unfreeze_unsplit", true);
            String strParam = param.toJSONString();

            String url = wxPayConstants.PROFITSHARING_URL_SUFFIX;

            String responseEntity = wxPayV3.execute("POST", url, strParam, certMap.get("serialNo"));

            JSONObject jsonObject = JSON.parseObject(responseEntity);
            if (StrUtil.isNotEmpty(jsonObject.getString("message"))) {
                logger.info(responseEntity);
                logger.info("===============================================.addProfitSharing========================================");
            }

            //处理微信分账返回结果
            //支付结果保存
            WxResult wxResult = new WxResult();
            wxResult.setTransactionId(jsonObject.getString("transaction_id"));
            wxResult.setOutOrderNo(jsonObject.getString("out_order_no"));
            wxResult.setOrderId(jsonObject.getString("order_id"));

            JSONArray array = jsonObject.getJSONArray("receivers");
            List<Receiver> list = new ArrayList<>();
            for (Object o : array) {
                JSONObject object = (JSONObject) o;
                String description = object.getString("description");
                Receiver receiver = new Receiver();
                receiver.setAmount(object.getInteger("amount"));
                receiver.setDescription(description);
                receiver.setType(object.getString("type"));
                receiver.setAccount(object.getString("account"));
                String resultDec = object.getString("result");
                if ("SUCCESS".equals(resultDec)) {
                    //预支付表
                    for (WxPrepaid pre : prepaids) {
                        String billCode = description.substring(0, 20);
                        if (pre.getOutOrderNo().equals(billCode)) pre.setPay(true);
                    }
                }
                receiver.setFailReason(object.getString("fail_reason"));
                receiver.setCreateTime(object.getString("create_time"));
                receiver.setFinishTime(object.getString("finish_time"));
                list.add(receiver);
            }
            wxResult.setReceivers(list);
            wxResultDao.save(wxResult);

            //司机账户表添加数据，司机直接微信付款接单成功记录
            saveDvrAccountType8(prepaid, wxResult);
            this.save(prepaids);
        }
    }


    /**
     * 包含分账的订单，司机支付完成并成功分配到订单，则请求微信分账
     */
    @Async("busTaskExecutor")
    public void toThirdParty2(WxPrepaid prepaid, WxResult result, String account, Integer amount, String name) throws Exception {
        Map<String, String> certMap = wxPayV3.getCertificates();

        String transactionId = result.getTransactionId();

        List<String> billCodes = prepaid.getBillCodes();
        JSONArray receiverArray = new JSONArray();
        List<WxPrepaid> prepaids = new ArrayList<>();
        String billCode = billCodes.get(0);
        String accountType = "PERSONAL_OPENID";

        String outOrderNo = billCode;
        //查询校验是否已经完成分账
        Query<WxPrepaid> prepaidQuery = this.createQuery();
        prepaidQuery.criteria("type").equal(4);
        prepaidQuery.filter("transactionId", transactionId);
        prepaidQuery.filter("outOrderNo", outOrderNo);
        prepaidQuery.filter("pay", true);
        //根据“分账单号，商户订单号”判断订单是否多次重复提交
        WxPrepaid refundPrepaid = this.get(prepaidQuery);
        if (refundPrepaid == null) {//"若订单已经分账，则不处理
            //添加预分账请求记录
            WxPrepaid wxPrepaid = new WxPrepaid();
            wxPrepaid.setAppid(wxPayWechatConfig.getAppID());
            wxPrepaid.setMchId(wxPayWechatConfig.getMchID());
            wxPrepaid.setType(4);//分账
            wxPrepaid.setTransactionId(transactionId);
            wxPrepaid.setOutOrderNo(outOrderNo);//商户分账单号
            wxPrepaid.setAccountType(accountType);         //分账接收方类型
            wxPrepaid.setAccount(account);             //分账接收方账号
            wxPrepaid.setName(name);                //分账接收方姓名
            wxPrepaid.setAmount(amount);              //分账金额
            wxPrepaid.setDescription("司机接单支付金额分账");         //分账原因
            wxPrepaid.setUnfreezeUnsplit(true);
            wxPrepaid.setPay(false);            //微信分账返回成功后改为true
            prepaids.add(wxPrepaid);

            JSONObject receiver = new JSONObject();
            receiver.put("type", accountType);
            receiver.put("account", account);
            receiver.put("name", wxPayV3.rsaEncryptOAEP(name, certMap));
            receiver.put("amount", amount);
            receiver.put("description", outOrderNo + "订单收款分给商户" + name);
            receiverArray.add(receiver);
        }
        if (receiverArray.size() > 0) {
            //请求微信分账
            JSONObject param = new JSONObject();
            param.put("appid", wxPayWechatCusConfig.getAppID());
            param.put("transaction_id", transactionId);
            param.put("out_order_no", prepaid.getOrderId());
            param.put("receivers", receiverArray);
            param.put("unfreeze_unsplit", true);
            String strParam = param.toJSONString();

            String url = wxPayConstants.PROFITSHARING_URL_SUFFIX;

            String responseEntity = wxPayV3.execute("POST", url, strParam, certMap.get("serialNo"));

            JSONObject jsonObject = JSON.parseObject(responseEntity);
            if (StrUtil.isNotEmpty(jsonObject.getString("message"))) {
                logger.info(responseEntity);
                logger.info("===============================================.addProfitSharing========================================");
            }

            //处理微信分账返回结果
            //支付结果保存
            WxResult wxResult = new WxResult();
            wxResult.setTransactionId(jsonObject.getString("transaction_id"));
            wxResult.setOutOrderNo(jsonObject.getString("out_order_no"));
            wxResult.setOrderId(jsonObject.getString("order_id"));

            JSONArray array = jsonObject.getJSONArray("receivers");
            List<Receiver> list = new ArrayList<>();
            for (Object o : array) {
                JSONObject object = (JSONObject) o;
                String description = object.getString("description");
                Receiver receiver = new Receiver();
                receiver.setAmount(object.getInteger("amount"));
                receiver.setDescription(description);
                receiver.setType(object.getString("type"));
                receiver.setAccount(object.getString("account"));
                String resultDec = object.getString("result");
                if ("SUCCESS".equals(resultDec)) {
                    //预支付表
                    for (WxPrepaid pre : prepaids) {
                        if (pre.getOutOrderNo().equals(description.substring(0, 20))) pre.setPay(true);
                    }
                }
                receiver.setFailReason(object.getString("fail_reason"));
                receiver.setCreateTime(object.getString("create_time"));
                receiver.setFinishTime(object.getString("finish_time"));
                list.add(receiver);
            }
            wxResult.setReceivers(list);
            wxResultDao.save(wxResult);

            //司机账户表添加数据，司机直接微信付款接单成功记录
            saveDvrAccountType8(prepaid, wxResult);
            this.save(prepaids);
        }
    }


    public void beforeMinSeparate() {
        Query<WxPrepaid> query = this.createQuery();
        query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(null, "00:00:00", -10));
        query.criteria("pay").equal(true);
        query.criteria("profitSharing").equal("Y");
        query.or(
                query.criteria("separate").equal(false),
                query.criteria("separate").equal(null)
        );
        query.criteria("updateTime").lessThanOrEq(IdUnit.weeHours3(null, -1).getTime());
        query.criteria("totalFee").greaterThanOrEq(3);    // 分账金额须大于0，那么最小整数总金额须是3
        query.limit(20);
        List<WxPrepaid> listFirst = this.list(query);

        List<WxPrepaid> list = new ArrayList<>();
        for (WxPrepaid wxPrepaid: listFirst) {
            Query<WxPrepaid> upQuery = this.createQuery();
            upQuery.criteria("_id").equal(wxPrepaid.getObjectId());
            upQuery.criteria("updateTime").equal(wxPrepaid.getUpdateTime());
            UpdateOperations<WxPrepaid> updateOperations = this.createUpdateOperations();
            updateOperations.set("frequencyLimitedLocked", 1);
            UpdateResults resa = this.update(upQuery, updateOperations);
            if (resa.getUpdatedCount() > 0 && list.size() <= 3) list.add(wxPrepaid);

            if (list.size() >=3 ) break;
        }

//        String account1 = "oC-ha5M4IbWjIhhGyztFPnad1biU";
        String account1 = "oC-ha5Mm9xzNr1aRWwzoSYU-17rY";
        String accountName1 = "";
//        String account2 = "oC-ha5E3lnB_jOVQDYKU_P2mhwBU";
        String account2 = "oC-ha5PF2YCNQ7aYpJwWHKpqeXmU";
        String accountName2 = "";

        // 判断分账接收方本自然月接收分账金额是否达到18.2W，达到则停止分账
        // 判断分账接收方本自然月接收分账金额是否达到19.2W，达到则停止分账
        Integer totalAccounts = separateAccountsService.sumSeparateAccountsWithMonth(account1);  // 自然月已分账总金额

        Integer totalAccounts2 = separateAccountsService.sumSeparateAccountsWithMonth(account2);  // 自然月已分账总金额

        List<WxPrepaid> fenZhangList = new ArrayList<>();
        List<WxPrepaid> unfeezeList = new ArrayList<>();
        for (WxPrepaid prepaid : list) {

            Double ff1 = prepaid.getTotalFee() * 0.3;           //分
            String ff2 = String.valueOf(ff1);
            String ff3 = ff2.substring(0, ff2.indexOf("."));    //取小数点前面部分
            Integer fenzhang = Integer.valueOf(ff3);            //分

            if (fenzhang == 0) continue;

//            int t = prepaid.getCreateTime().compareTo(new Date(1732982400000L));    // 2024年12月1号0点0分0秒 后开始分账。
//            if (t>=0 && (totalAccounts + fenzhang) <= ********) {
            if ((totalAccounts + fenzhang) <= ********) {
                fenZhangList.add(prepaid);
                totalAccounts = totalAccounts + fenzhang;
            } else if ((totalAccounts2 + fenzhang) <= ********) {
                fenZhangList.add(prepaid);
                totalAccounts2 = totalAccounts2 + fenzhang;
//                account1 = "oC-ha5E3lnB_jOVQDYKU_P2mhwBU";  //第一账户达到额度，则像第二账户分账
                account1 = "oC-ha5PF2YCNQ7aYpJwWHKpqeXmU";  //第一账户达到额度，则像第二账户分账
            } else {
                unfeezeList.add(prepaid);
            }
        }

        for (WxPrepaid prepaid : fenZhangList) {
            WxResult wxResult = wxResultDao.get("outTradeNo", prepaid.getOutTradeNo());

            Double ff1 = prepaid.getTotalFee() * 0.3;           //分
            String ff2 = String.valueOf(ff1);
            String ff3 = ff2.substring(0, ff2.indexOf("."));    //取小数点前面部分
            Integer fenzhang = Integer.valueOf(ff3);            //分
            //订单分配成功,执行异步分账
            try {
                toThirdParty3(prepaid, wxResult, account1, account2, fenzhang, 0, accountName1, accountName2);
            } catch (Exception e) {
                logger.error("beforeMinSeparate 异常：" + e.getMessage());
            }
        }

        for (WxPrepaid prepaid: unfeezeList){
            WxResult wxResult = wxResultDao.get("outTradeNo", prepaid.getOutTradeNo());
            String transactionId = wxResult.getTransactionId();

            //执行异步分账解冻
            try {
                unfreeze(prepaid, transactionId);
            } catch (Exception e) {
                logger.error("beforeMinSeparate 解冻异常：" + e.getMessage());
            }
        }
    }

    /**
     * 包含分账的订单，司机支付完成并成功分配到订单，则请求微信分账
     */
    @Async("busTaskExecutor")
    public void toThirdParty3(WxPrepaid prepaid, WxResult result, String account1, String account2, Integer amount1, Integer amount2, String name1, String name2) throws Exception {
        // Map<String, String> certMap = wxPayV3.getCertificates();

        String accountType = "PERSONAL_OPENID";
        String transactionId = result.getTransactionId();
        String outOrderNo = prepaid.getOutOrderNo();

        JSONArray receiverArray = new JSONArray();
        List<WxPrepaid> prepaids = new ArrayList<>();

        //查询校验是否已经完成分账
        Query<WxResult> resultQuery = wxResultDao.createQuery();
        resultQuery.filter("outOrderNo", outOrderNo);
        //根据“分账单号，商户订单号”判断订单是否多次重复提交
        WxResult refundResult = wxResultDao.get(resultQuery);
        if (refundResult == null) {//"若订单已经分账，则不处理
            //添加预分账请求记录
            WxPrepaid wxPrepaid1 = new WxPrepaid();
            wxPrepaid1.setAppid(wxPayWechatCusConfig.getAppID());
            wxPrepaid1.setMchId(wxPayWechatCusConfig.getMchID());
            wxPrepaid1.setType(5);//分账
            wxPrepaid1.setTransactionId(transactionId);
            wxPrepaid1.setOutOrderNo(outOrderNo);//商户分账单号
            wxPrepaid1.setAccountType(accountType);         //分账接收方类型
            wxPrepaid1.setAccount(account1);             //分账接收方账号
            // wxPrepaid1.setName(name1);                //分账接收方姓名
            wxPrepaid1.setAmount(amount1);              //分账金额
            wxPrepaid1.setDescription("司机账户充值金额分账");         //分账原因
            wxPrepaid1.setUnfreezeUnsplit(true);
            wxPrepaid1.setPay(false);            //微信分账返回成功后改为true
            prepaids.add(wxPrepaid1);

            JSONObject receiver1 = new JSONObject();
            receiver1.put("type", accountType);
            receiver1.put("account", account1);
            // receiver1.put("name", wxPayV3.rsaEncryptOAEP(name1, certMap));
            receiver1.put("amount", amount1);
            receiver1.put("description", "分账到" + name1);
            receiverArray.add(receiver1);
        }
        if (receiverArray.size() > 0) {
            //请求微信分账
            JSONObject param = new JSONObject();
            param.put("appid", wxPayWechatCusConfig.getAppID());
            param.put("transaction_id", transactionId);
            param.put("out_order_no", outOrderNo);
            param.put("receivers", receiverArray);
            param.put("unfreeze_unsplit", true);
            String strParam = param.toJSONString();


            String url = "https://api.mch.weixin.qq.com" + wxPayConstants.PROFITSHARING_URL_SUFFIX;

//            String responseEntity = wxPayV3.execute("POST", url, strParam, certMap.get("serialNo"));
            String wechatpaySerial = "PUB_KEY_ID_0115401723212024103100389200000123";
            String responseEntity = wxPayV3.execute("POST", url, strParam, wechatpaySerial);

            JSONObject jsonObject = JSON.parseObject(responseEntity);
            if (StrUtil.isNotEmpty(jsonObject.getString("message"))) {
                logger.error(responseEntity);
                logger.error("===============================================.addProfitSharing3========================================");
            }

            //处理微信分账返回结果
            WxResult wxResult = new WxResult();
            wxResult.setTransactionId(jsonObject.getString("transaction_id"));
            wxResult.setOutOrderNo(jsonObject.getString("out_order_no"));
            wxResult.setOrderId(jsonObject.getString("order_id"));

            JSONArray array = jsonObject.getJSONArray("receivers");
            List<Receiver> list = new ArrayList<>();
            List<SeparateAccounts> separateAccountsList = new ArrayList<>();
            for (Object o : array) {
                JSONObject object = (JSONObject) o;
                String account = object.getString("account");
                Integer amount = object.getInteger("amount");
                String detailId = object.getString("detail_id");
                String resultStr = object.getString("result");
                Receiver receiver = new Receiver();
                receiver.setDescription(object.getString("description"));
                receiver.setAmount(amount);
                receiver.setType(object.getString("type"));
                receiver.setAccount(account);
                receiver.setResult(resultStr);
                if ("CLOSED".equals(resultStr)) receiver.setFailReason(object.getString("fail_reason"));
                receiver.setCreateTime(object.getString("create_time"));
                receiver.setFinishTime(object.getString("finish_time"));
                receiver.setDetailId(detailId);
                list.add(receiver);

                if (account.equals(account1)) {
                    SeparateAccounts separateAccounts = new SeparateAccounts();
                    separateAccounts.setDid(prepaid.getDid());
                    separateAccounts.setOutTradeNo(prepaid.getOutTradeNo());
                    separateAccounts.setTransactionId(transactionId);
                    separateAccounts.setOutOrderNo(outOrderNo);
                    separateAccounts.setAccountType(accountType);
                    separateAccounts.setAccount(account);
                    separateAccounts.setAmount(amount);
                    separateAccounts.setDetailId(detailId);
                    separateAccounts.setResult(resultStr);
                    if ("CLOSED".equals(resultStr)) separateAccounts.setFailReason(object.getString("fail_reason"));
                    separateAccountsList.add(separateAccounts);
                }
            }

            //更新司机充值预支付表
            Query<WxPrepaid> updateQuery = this.createQuery().filter("outTradeNo", prepaid.getOutTradeNo());
            UpdateOperations<WxPrepaid> updateOperations = this.createUpdateOperations();
            updateOperations.set("separate", true);
            this.update(updateQuery, updateOperations);

            // 保存分账返回结果
            wxResult.setReceivers(list);
            wxResultDao.save(wxResult);
            //添加分账记录
            separateAccountsService.save(separateAccountsList);
            // 保存预分账请求
            this.save(prepaids);
        }
    }

    private void saveDvrAccountType8(WxPrepaid prepaid, WxResult wxResult) {
        DriverAccount account = new DriverAccount();
        account.setDid(prepaid.getDvrId());
        account.setTotalFee(new BigDecimal(prepaid.getTotalFee()));
        account.setType(8);
        account.setOid(prepaid.getOrderId());
        account.setOutTradeNo(wxResult.getOutOrderNo());
        account.setTransactionId(wxResult.getTransactionId());
        driverAccountService.save(account);
    }

    /**
     * 包含分账的订单，超过分账额度，则不分账，直接解冻
     */
    @Async("busTaskExecutor")
    public void unfreeze(WxPrepaid prepaid, String transactionId) throws Exception {

        String outOrderNo = prepaid.getOutOrderNo();

        //查询校验是否已经完成分账
        Query<WxResult> resultQuery = wxResultDao.createQuery();
        resultQuery.filter("outOrderNo", outOrderNo);
        //根据“分账单号，商户订单号”判断订单是否多次重复提交
        WxResult refundResult = wxResultDao.get(resultQuery);

        if (refundResult == null) {
            //请求微信分账解冻
            JSONObject param = new JSONObject();
            param.put("transaction_id", transactionId);
            param.put("out_order_no", outOrderNo);
            param.put("description", "解冻全部剩余资金");
            String strParam = param.toJSONString();

            String url = "https://api.mch.weixin.qq.com" + wxPayConstants.PROFITSHARING_UNFREEZE_URL_SUFFIX;

            String wechatpaySerial = "PUB_KEY_ID_0115401723212024103100389200000123";
            String responseEntity = wxPayV3.execute("POST", url, strParam, wechatpaySerial);

            JSONObject jsonObject = JSON.parseObject(responseEntity);
            if (StrUtil.isNotEmpty(jsonObject.getString("message"))) {
                logger.error(responseEntity);
                logger.error("===============================================.addProfitSharing3（unfreeze）========================================");
            }

            //处理微信分账返回结果
            WxResult wxResult = new WxResult();
            wxResult.setTransactionId(jsonObject.getString("transaction_id"));
            wxResult.setOutOrderNo(jsonObject.getString("out_order_no"));
            wxResult.setOrderId(jsonObject.getString("order_id"));

            JSONArray array = jsonObject.getJSONArray("receivers");
            List<Receiver> list = new ArrayList<>();
            for (Object o : array) {
                JSONObject object = (JSONObject) o;
                String account = object.getString("account");
                Integer amount = object.getInteger("amount");
                String detailId = object.getString("detail_id");
                String resultStr = object.getString("result");
                Receiver receiver = new Receiver();
                receiver.setDescription(object.getString("description"));
                receiver.setAmount(amount);
                receiver.setType(object.getString("type"));
                receiver.setAccount(account);
                receiver.setResult(resultStr);
                if ("CLOSED".equals(resultStr)) receiver.setFailReason(object.getString("fail_reason"));
                receiver.setCreateTime(object.getString("create_time"));
                receiver.setFinishTime(object.getString("finish_time"));
                receiver.setDetailId(detailId);
                list.add(receiver);
            }

            //更新司机充值预支付表
            Query<WxPrepaid> updateQuery = this.createQuery().filter("outTradeNo", prepaid.getOutTradeNo());
            UpdateOperations<WxPrepaid> updateOperations = this.createUpdateOperations();
            updateOperations.set("separate", true);
            this.update(updateQuery, updateOperations);

            // 保存分账返回结果
            wxResult.setReceivers(list);
            wxResultDao.save(wxResult);
        }
    }

    /**
     * 包含分账的订单，司机支付完成但未分配到订单，则退款给司机
     */
    private void refundToDvr(WxPrepaid prepaid, String orderErrorMsg) throws Exception {
        String outTradeNo = prepaid.getOutTradeNo();

        //查询校验是否已经完成退款
        Query<WxPrepaid> prepaidQuery = this.createQuery();
        prepaidQuery.criteria("type").equal(2);
        prepaidQuery.filter("outTradeNo", outTradeNo);
        prepaidQuery.filter("outRefundNo", prepaid.getOrderId());
        prepaidQuery.filter("pay", true);
        //根据“退款单号，微信订单号，商户订单号”判断订单是否多次重复提交
        WxPrepaid refundPrepaid = this.get(prepaidQuery);
        if (refundPrepaid == null) {//"该订单已经退款,则不处理
            //添加预退款请求记录
            WxPrepaid wxPrepaid = new WxPrepaid();
            wxPrepaid.setAppid(wxPayWechatConfig.getAppID());
            wxPrepaid.setMchId(wxPayWechatConfig.getMchID());
            wxPrepaid.setType(2);//退款
            wxPrepaid.setDid(prepaid.getDvrId());
            wxPrepaid.setOutTradeNo(outTradeNo);
            wxPrepaid.setOutRefundNo(prepaid.getOrderId());//商户退款单号
            wxPrepaid.setTotalFee(prepaid.getTotalFee());//订单金额
            wxPrepaid.setRefundFee(prepaid.getTotalFee());//退款金额
            wxPrepaid.setRefundDesc(orderErrorMsg + ",订单分配失败退款");//退款原因
            wxPrepaid.setPay(false);    //微信退款返回成功后改为true

            //请求微信退款
            WXPay wxpay = new WXPay(wxPayWechatConfig, wxPayConstants.notifyUrl, true, false);
            Map<String, String> map = new HashMap<>();
            map.put("out_trade_no", outTradeNo);
            map.put("out_refund_no", prepaid.getOrderId());//商户退款单号
            map.put("total_fee", String.valueOf(prepaid.getTotalFee()));//订单金额
            map.put("refund_fee", String.valueOf(prepaid.getTotalFee()));//退款金额
            map.put("refund_desc", "订单分配失败退款");
            Map<String, String> resultMap = wxpay.refund(map);
            logger.info("===============================================wxPrepaidService.addProfitSharing========================================");
            logger.info(resultMap.toString());
            //处理微信退款返回结果
            String returnCode = resultMap.get("return_code");
            if (returnCode.equals("SUCCESS")) {
                String resultCode = resultMap.get("result_code");
                if (resultCode.equals("SUCCESS")) {
                    //预支付表
                    wxPrepaid.setNonceStr(resultMap.get("nonce_str"));
                    wxPrepaid.setSign(resultMap.get("sign"));
                    wxPrepaid.setPay(true);
                    wxPrepaid.setTransactionId(resultMap.get("transaction_id"));

                    //支付结果保存
                    WxResult wxResult = new WxResult();
                    wxResult.setResultCode(resultCode);
                    wxResult.setReturnCode(returnCode);
                    wxResult.setOutTradeNo(resultMap.get("out_trade_no"));
                    wxResult.setTransactionId(resultMap.get("transaction_id"));
                    wxResult.setSign(resultMap.get("sign"));
                    wxResult.setNonceStr(resultMap.get("nonce_str"));
                    wxResult.setOutRefundNo(resultMap.get("out_refund_no"));//商户退款单号
                    wxResult.setRefundId(resultMap.get("refund_id"));//微信退款单号
                    wxResult.setRefundFee(Utils.parseInt(resultMap.get("refund_fee"), 0));//退款总金额
                    wxResult.setSettlementRefundFee(Utils.parseInt(resultMap.get("settlement_refund_fee"), 0));//应结退款金额
                    wxResult.setTotalFee(Utils.parseInt(resultMap.get("total_fee"), 0));
                    wxResult.setSettlementTotalFee(Utils.parseInt(resultMap.get("settlement_total_fee"), 0));//应结订单金额
                    wxResult.setFeeType(resultMap.get("fee_type"));
                    wxResult.setCashFee(Utils.parseInt(resultMap.get("cash_fee"), 0));
                    wxResult.setCashFeeType(resultMap.get("cash_fee_type"));
                    wxResult.setCashRefundFee(Utils.parseInt(resultMap.get("cash_refund_fee"), 0));
                    wxResultDao.save(wxResult);
//                            return ServerResponse.createSuccess("审核通过，零钱支付的退款20分钟内到账，银行卡支付的退款3个工作日");
                }
//                return ServerResponse.createError(resultMap.get("err_code_des"));
            }
            this.save(wxPrepaid);
//            return ServerResponse.createError(resultMap.get("return_msg"));
        }
    }

    /**
     * openid:小程序支付时必填参数
     * body: 支付商品描述
     * totalFee:支付金额
     * payeeid:被支付的人或商品id
     * useType:使用类型：0客商自己充值，1客商给司机充值 或 司机给自己充值，2客商代付货运信息费，3司机接单支付费用
     * shareFee:客商或友商要求收取的费用，来自客商发布货运信息前自己设置的收费金额 或 司机接单时客商（友商）手动输入的收费金额
     * carNum:车牌号，当司机接单时，司机的车牌号
     * driverId:车牌号对应的司机id（车号和司机之间是多对多的关系，所以需要指明司机id）
     * tradeType: 请求微信支付平台创建订单类型 APP-平台app调起微信支付，JSAPI-平台小程序调起微信支付
     * attach: 附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用,自定义为下单时用户ID
     */
    @Override
    public ServerResponse<Map<String, String>> weChatRequestPay(String openid, String body, int totalFee, String payeeid, Integer useType, Integer shareFee, String carNum, String driverId) {
        String payerid = ShiroUtils.getUserId();

        boolean lock = lockedService.getLock(payerid, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) {//未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {
                if (StrUtil.isEmpty(totalFee) || totalFee == 0) return ServerResponse.createError("支付金额不能为空");
                String tradeType;   //APP-平台app调起微信支付，JSAPI-平台小程序调起微信支付
                if (StrUtil.isNotEmpty(openid)) {
                    tradeType = "JSAPI";
                } else {
                    tradeType = "APP";
                }

                //1.平台向微信支付系统请求下单，创建订单
                long timeStart = System.currentTimeMillis();
                String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
                Map<String, String> unifiedOrderResMap = createWEChatOrder(out_trade_no, timeStart, body, openid, totalFee, tradeType);
                if (StrUtil.isEmpty(unifiedOrderResMap)) return ServerResponse.createError("统一下单失败");
                if (unifiedOrderResMap.get("return_code").equals("FAIL"))
                    return ServerResponse.createError(unifiedOrderResMap.get("return_msg"));
                if (StrUtil.isNotEmpty(unifiedOrderResMap.get("err_code")))
                    return ServerResponse.createError(unifiedOrderResMap.get("err_code_des"));

                //2.微信后台系统成功创建订单，平台添加预支付信息，等待用户支付结果
                WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
                prepaid.setBody(body);
                prepaid.setTotalFee(totalFee);
                prepaid.setCdid(payerid);
                savePrepaid(useType, payeeid, prepaid, out_trade_no, unifiedOrderResMap, tradeType, shareFee, carNum, driverId);

                //3.对微信支付系统成功创建对订单信息生成二次签名，返回前端调起支付界面
                return signWechatOrder(out_trade_no, timeStart, unifiedOrderResMap, tradeType);
            } finally {
                lockedService.unLock(payerid, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁

            }
        }
    }

    //平台向微信支付系统请求下单，创建订单
    private Map<String, String> createWEChatOrder(String out_trade_no, long timeStart, String body, String openid, Integer totalFee, String tradeType) {
        try {
            WXPay wxpay = getWxPayByUserType();

            Map<String, String> map = new HashMap<>();
            map.put("total_fee", String.valueOf(totalFee));
            map.put("trade_type", tradeType);//  "APP" ,"JSAPI"小程序
            map.put("spbill_create_ip", InetAddress.getLocalHost().getHostAddress());
            map.put("out_trade_no", out_trade_no);
            map.put("body", body);
            if (tradeType.equals("JSAPI")) {
                map.put("openid", openid);
            } else if (tradeType.equals("APP")) {
                map.put("time_start", new SimpleDateFormat("yyyyMMddHHmmss").format(timeStart));
            } else {
                return null;
            }
            map.put("attach", ShiroUtils.getUserId());
            return wxpay.unifiedOrder(map);//统一下单
        } catch (Exception ignored) {
            return null;//ServerResponse.createError("统一下单失败");
        }
    }

    //微信支付系统成功创建订单，平台添加预支付信息，等待用户支付结果
    private void savePrepaid(Integer useType, String payeeid, WxPrepaid prepaid, String out_trade_no, Map<String, String> unifiedOrderResMap, String tradeType, Integer shareFee, String carNum, String driverId) {
        //useType 使用类型：0客商自己充值，1客商给司机充值 或 司机给自己充值，2客商代付货运信息费，3司机接单支付费用
        switch (useType) {
            case 0:
                prepaid.setCid(payeeid);
                prepaid.setType(0);
                break;
            case 1:
                prepaid.setDid(payeeid);
                prepaid.setType(0);
                break;
            case 2:
                prepaid.setGid(payeeid);
                prepaid.setNumber(goodsService.get("gid", payeeid).getTotal());
                prepaid.setType(1);
                break;
            case 3:
                prepaid.setOid(payeeid);
                prepaid.setShareFee(shareFee);  //客商或友商要求收取的费用
                prepaid.setCarNum(carNum);
                prepaid.setDriverId(driverId);
                prepaid.setType(1);
                break;
            default:
                break;
        }
        // 业务逻辑处理 ****************************
        //if (map.get("result_code").equals("SUCCESS")) {
        prepaid.setTradeType(tradeType);
        prepaid.setOutTradeNo(out_trade_no);
        prepaid.setPrepayId(unifiedOrderResMap.get("prepay_id"));
        //}
        try {
            prepaid.setSpbillCreateIp(InetAddress.getLocalHost().getHostAddress());
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        prepaid.setSign(unifiedOrderResMap.get("sign"));
        prepaid.setNonceStr(unifiedOrderResMap.get("nonce_str"));
        prepaid.setAppid(unifiedOrderResMap.get("appid"));
        prepaid.setMchId(unifiedOrderResMap.get("mch_id"));
        prepaid.setAttach(ShiroUtils.getUserId());
        this.save(prepaid);
    }

    /**
     * openid:小程序支付时必填参数
     * body: 支付商品描述
     * totalFee:支付金额
     * payeeid:被支付的人或商品id
     * useType:使用类型：0客商自己充值，1客商给司机充值 或 司机给自己充值，2客商代付货运信息费，3司机接单支付费用
     * shareFee:客商或友商要求收取的费用，来自客商发布货运信息前自己设置的收费金额 或 司机接单时客商（友商）手动输入的收费金额
     * carNum:车牌号，当司机接单时，司机的车牌号
     * driverId:车牌号对应的司机id（车号和司机之间是多对多的关系，所以需要指明司机id）
     * tradeType: 请求微信支付平台创建订单类型 APP-平台app调起微信支付，JSAPI-平台小程序调起微信支付
     * attach: 附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用,自定义为下单时用户ID
     */
    @Override
    public ServerResponse<Map<String, String>> weChatRequestPay3(String openid, String body, int totalFee, String payeeid, Integer useType, Integer shareFee, String carNum, String driverId) {
        String payerid = ShiroUtils.getUserId();

        String profitSharing = "Y";
        /*if (totalFee > 3) {
            // 判断分账接收方本自然月接收分账金额是否达到18.2W，达到则停止分账
            //Integer totalAccounts = separateAccountsService.sumSeparateAccountsWithMonth("oC-ha5M4IbWjIhhGyztFPnad1biU");  // 自然月已分账总金额
            Integer totalAccounts = separateAccountsService.sumSeparateAccountsWithMonth("011001");  // 自然月已分账总金额
            Double ff1 = totalFee * 0.3;                        //分
            String ff2 = String.valueOf(ff1);
            String ff3 = ff2.substring(0, ff2.indexOf("."));    //取小数点前面部分
            Integer fenzhang = Integer.valueOf(ff3);            //分

            if ((totalAccounts + fenzhang) > ********) profitSharing = "N";
        } else {
            profitSharing = "N";
        }*/
        if (totalFee<=3) profitSharing = "N";

        boolean lock = lockedService.getLock(payerid, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) {//未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {
                if (StrUtil.isEmpty(totalFee) || totalFee == 0) return ServerResponse.createError("支付金额不能为空");
                String tradeType;   //APP-平台app调起微信支付，JSAPI-平台小程序调起微信支付
                if (StrUtil.isNotEmpty(openid)) {
                    tradeType = "JSAPI";
                } else {
                    tradeType = "APP";
                }

                //1.平台向微信支付系统请求下单，创建订单
                long timeStart = System.currentTimeMillis();
                String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
                Map<String, String> unifiedOrderResMap = createWEChatOrder3(out_trade_no, timeStart, body, openid, totalFee, tradeType, profitSharing);
                if (StrUtil.isEmpty(unifiedOrderResMap)) return ServerResponse.createError("统一下单失败");
                if (unifiedOrderResMap.get("return_code").equals("FAIL"))
                    return ServerResponse.createError(unifiedOrderResMap.get("return_msg"));
                if (StrUtil.isNotEmpty(unifiedOrderResMap.get("err_code")))
                    return ServerResponse.createError(unifiedOrderResMap.get("err_code_des"));

                //2.微信后台系统成功创建订单，平台添加预支付信息，等待用户支付结果
                WxPrepaid prepaid = new WxPrepaid();//生成预支付信息
                prepaid.setBody(body);
                prepaid.setTotalFee(totalFee);
                prepaid.setCdid(payerid);
                prepaid.setOutOrderNo(Utils.getUUID());  // 提前设定支付到账后分账商户唯一单号
                savePrepaid3(useType, payeeid, prepaid, out_trade_no, unifiedOrderResMap, tradeType, shareFee, carNum, driverId, profitSharing);

                //3.对微信支付系统成功创建对订单信息生成二次签名，返回前端调起支付界面
                return signWechatOrder(out_trade_no, timeStart, unifiedOrderResMap, tradeType);
            } finally {
                lockedService.unLock(payerid, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁

            }
        }
    }

    //平台向微信支付系统请求下单，创建订单
    private Map<String, String> createWEChatOrder3(String out_trade_no, long timeStart, String body, String openid, Integer totalFee, String tradeType, String profitSharing) {
        try {
            WXPay wxpay = getWxPayByUserType();

            Map<String, String> map = new HashMap<>();
            map.put("total_fee", String.valueOf(totalFee));
            map.put("trade_type", tradeType);//  "APP" ,"JSAPI"小程序
            map.put("spbill_create_ip", InetAddress.getLocalHost().getHostAddress());
            map.put("out_trade_no", out_trade_no);
            map.put("body", body);
            if (tradeType.equals("JSAPI")) {
                map.put("openid", openid);
            } else if (tradeType.equals("APP")) {
                map.put("time_start", new SimpleDateFormat("yyyyMMddHHmmss").format(timeStart));
            } else {
                return null;
            }
            map.put("attach", "separate");
            map.put("profit_sharing", profitSharing);  //是否指定分账
            return wxpay.unifiedOrder(map);//统一下单
        } catch (Exception ignored) {
            return null;//ServerResponse.createError("统一下单失败");
        }
    }

    //微信支付系统成功创建订单，平台添加预支付信息，等待用户支付结果
    private void savePrepaid3(Integer useType, String payeeid, WxPrepaid prepaid, String out_trade_no, Map<String, String> unifiedOrderResMap, String tradeType, Integer shareFee, String carNum, String driverId, String profitSharing) {
        //useType 使用类型：0客商自己充值，1客商给司机充值 或 司机给自己充值，2客商代付货运信息费，3司机接单支付费用
        switch (useType) {
            case 0:
                prepaid.setCid(payeeid);
                prepaid.setType(0);
                break;
            case 1:
                prepaid.setDid(payeeid);
                prepaid.setType(0);
                break;
            case 2:
                prepaid.setGid(payeeid);
                prepaid.setNumber(goodsService.get("gid", payeeid).getTotal());
                prepaid.setType(1);
                break;
            case 3:
                prepaid.setOid(payeeid);
                prepaid.setShareFee(shareFee);  //客商或友商要求收取的费用
                prepaid.setCarNum(carNum);
                prepaid.setDriverId(driverId);
                prepaid.setType(1);
                break;
            default:
                break;
        }
        // 业务逻辑处理 ****************************
        //if (map.get("result_code").equals("SUCCESS")) {
        prepaid.setTradeType(tradeType);
        prepaid.setOutTradeNo(out_trade_no);
        prepaid.setPrepayId(unifiedOrderResMap.get("prepay_id"));
        //}
        try {
            prepaid.setSpbillCreateIp(InetAddress.getLocalHost().getHostAddress());
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        prepaid.setSign(unifiedOrderResMap.get("sign"));
        prepaid.setNonceStr(unifiedOrderResMap.get("nonce_str"));
        prepaid.setAppid(unifiedOrderResMap.get("appid"));
        prepaid.setMchId(unifiedOrderResMap.get("mch_id"));
        prepaid.setAttach(ShiroUtils.getUserId());
        prepaid.setProfitSharing(profitSharing);
        this.save(prepaid);
    }


    @Override
    public ServerResponse<Map<String, String>> weChatRequestPay2(String openid, WxPrepaid wxPrepaid) {
        String payerid = wxPrepaid.getDvrId();

        boolean lock = lockedService.getLock(payerid, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) {//未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {
                int totalFee = wxPrepaid.getTotalFee();
                if (StrUtil.isEmpty(totalFee) || totalFee == 0) return ServerResponse.createError("支付金额不能为空");
                String tradeType;   //APP-平台app调起微信支付，JSAPI-平台小程序调起微信支付
                if (StrUtil.isNotEmpty(openid)) {
                    tradeType = "JSAPI";
                } else {
                    tradeType = "APP";
                }

                Map<String, String> unifiedOrderResMap;
                String out_trade_no;
                long timeStart = System.currentTimeMillis();
                //1.平台向微信支付系统请求下单，创建订单
                if (StrUtil.isNotEmpty(wxPrepaid.getOutTradeNo())) {
                    out_trade_no = wxPrepaid.getOutTradeNo();
                    unifiedOrderResMap = createWEChatOrder2(out_trade_no, timeStart, openid, wxPrepaid, tradeType);
                } else {
                    out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
                    unifiedOrderResMap = createWEChatOrder2(out_trade_no, timeStart, openid, wxPrepaid, tradeType);
                }
                if (StrUtil.isEmpty(unifiedOrderResMap)) return ServerResponse.createError("统一下单失败");
                if (unifiedOrderResMap.get("return_code").equals("FAIL"))
                    return ServerResponse.createError(unifiedOrderResMap.get("return_msg"));
                if (StrUtil.isNotEmpty(unifiedOrderResMap.get("err_code")))
                    return ServerResponse.createError(unifiedOrderResMap.get("err_code_des"));

                if (StrUtil.isEmpty(wxPrepaid.getOutTradeNo())) {
                    //2.微信后台系统成功创建订单，平台添加预支付信息，等待用户支付结果
                    savePrepaid2(wxPrepaid, out_trade_no, unifiedOrderResMap, tradeType);
                }

                //3.对微信支付系统成功创建对订单信息生成二次签名，返回前端调起支付界面
                return signWechatOrder(out_trade_no, timeStart, unifiedOrderResMap, tradeType);
            } finally {
                lockedService.unLock(payerid, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁

            }
        }
    }

    //平台向微信支付系统请求下单，创建订单
    private Map<String, String> createWEChatOrder2(String out_trade_no, long timeStart, String openid, WxPrepaid prepaid, String tradeType) {
        try {
            WXPay wxpay = getWxPayByUserType();

            Map<String, String> map = new HashMap<>();
            map.put("total_fee", String.valueOf(prepaid.getTotalFee()));
            map.put("trade_type", tradeType);//  "APP" ,"JSAPI"小程序
            map.put("out_trade_no", out_trade_no);
            map.put("body", prepaid.getBody());
            map.put("spbill_create_ip", InetAddress.getLocalHost().getHostAddress());
            if (tradeType.equals("JSAPI")) {
                map.put("openid", openid);
            } else if (tradeType.equals("APP")) {
                map.put("time_start", new SimpleDateFormat("yyyyMMddHHmmss").format(timeStart));
            } else {
                return null;
            }
            map.put("attach", prepaid.getAttach());
            map.put("profit_sharing", "Y");  //是否指定分账
            return wxpay.unifiedOrder(map);//统一下单
        } catch (Exception ignored) {
            return null;
        }
    }

    //微信支付系统成功创建订单，平台添加预支付信息，等待用户支付结果
    private void savePrepaid2(WxPrepaid prepaid, String out_trade_no, Map<String, String> unifiedOrderResMap, String tradeType) {
        prepaid.setType(1); //订单付款
        // 业务逻辑处理 ****************************
        prepaid.setPrepayId(unifiedOrderResMap.get("prepay_id"));
        prepaid.setTradeType(tradeType);
        prepaid.setOutTradeNo(out_trade_no);
        try {
            prepaid.setSpbillCreateIp(InetAddress.getLocalHost().getHostAddress());
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        prepaid.setAppid(unifiedOrderResMap.get("appid"));
        prepaid.setMchId(unifiedOrderResMap.get("mch_id"));
        prepaid.setNonceStr(unifiedOrderResMap.get("nonce_str"));
        prepaid.setSign(unifiedOrderResMap.get("sign"));
//        prepaid.setAttach(ShiroUtils.getUserId());

        this.save(prepaid);
    }

    //对微信支付系统成功创建对订单，生成签名
    private ServerResponse<Map<String, String>> signWechatOrder(String out_trade_no, long timeStart, Map<String, String> unifiedOrderResMap, String tradeType) {
        String appid;
        String key;
        switch (Objects.requireNonNull(ShiroUtils.getUserType())) {
            case "CU":
                appid = wxPayWechatCusConfig.getAppID();
                key = wxPayWechatCusConfig.getKey();
                /*appid = customerConfig.getAppID();
                key = customerConfig.getKey();*/
                break;
            case "DU":
                appid = wxPayWechatConfig.getAppID();
                key = wxPayWechatConfig.getKey();
                /*appid = driverConfig.getAppID();
                key = driverConfig.getKey();*/
                break;
            /*case "WECHATCUS":
                appid = wxPayWechatCusConfig.getAppID();
                key = wxPayWechatCusConfig.getKey();
                break;
            case "WECHAT":
                appid = wxPayWechatConfig.getAppID();
                key = wxPayWechatConfig.getKey();
                break;*/
            default:
                return ServerResponse.createError("参数错误");
        }
        //生成二次签名
        Map<String, String> reMap = new HashMap<>();
        try {
            reMap.put("timeStamp", String.format("%010d", timeStart / 1000));//以秒为单位的10位数字
            reMap.put("nonceStr", unifiedOrderResMap.get("nonce_str"));
            if (tradeType.equals("JSAPI")) {
                reMap.put("appId", appid);
                reMap.put("package", "prepay_id=" + unifiedOrderResMap.get("prepay_id"));
                reMap.put("signType", "MD5");
                reMap.put("paySign", WXPayUtil.generateSignature(reMap, key, WXPayConstants.SignType.MD5));
            } else if (tradeType.equals("APP")) {
                reMap.put("appid", appid);
                reMap.put("partnerid", wxPayConstants.mch_id);
                reMap.put("prepayid", unifiedOrderResMap.get("prepay_id"));
                reMap.put("package", "Sign=WXPay");
                reMap.put("sign", WXPayUtil.generateSignature(reMap, key, WXPayConstants.SignType.MD5));
                reMap.put("out_trade_no", out_trade_no);
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        reMap.put("out_trade_no", out_trade_no);
        return ServerResponse.createSuccess("统一下单成功", reMap);
    }

}
