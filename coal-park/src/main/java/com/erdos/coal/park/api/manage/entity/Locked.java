package com.erdos.coal.park.api.manage.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.Date;

@Entity(value = "t_locked", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("userId")}, options = @IndexOptions(unique = true)),
        @Index(fields = {@Field("lockTime")}, options = @IndexOptions(expireAfterSeconds = 600))
})
public class Locked extends BaseMongoInfo {

    //@Indexed(options = @IndexOptions(name = "_idx_locked_user_id", background = true))
    private String userId;

    //@Indexed(options = @IndexOptions(name = "_idx_locked_time", expireAfterSeconds = 600))
    private Date lockTime; //发送时间

    private Integer type; //类型

    private String test;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getLockTime() {
        return lockTime;
    }

    public void setLockTime(Date lockTime) {
        this.lockTime = lockTime;
    }

    public String getTest() {
        return test;
    }

    public void setTest(String test) {
        this.test = test;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
