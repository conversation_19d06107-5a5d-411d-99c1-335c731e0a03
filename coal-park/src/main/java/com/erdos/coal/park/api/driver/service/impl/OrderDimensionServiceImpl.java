package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.IOrderDimensionDao;
import com.erdos.coal.park.api.driver.entity.OrderDimension;
import com.erdos.coal.park.api.driver.service.IOrderDimensionService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("orderDimensionService")
public class OrderDimensionServiceImpl extends BaseMongoServiceImpl<OrderDimension, IOrderDimensionDao> implements IOrderDimensionService {
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IGoodsService goodsService;

    @Override
    public ServerResponse<String> getNewOrderDimension(String oid) {
        String did = ShiroUtils.getUserId();
        //todo:判断oid是否正确
        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("did", did);
        query.filter("oid", oid);
        OrderTaking orderTaking = orderTakingService.get(query);
        if (orderTaking == null)
            return ServerResponse.createError("订单号错误");
        //todo:校验订单费用是否都支付了
        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");
        String msg = checkDriverIsPay(order);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //删除订单的二维码数据，表示二维码过期不可用
        this.delete("oid", oid);
        //添加新的订单二维码
        String newDimension = Utils.md5(oid + System.currentTimeMillis());
        OrderDimension dimension = new OrderDimension();
        dimension.setOid(oid);
        dimension.setMd5Oid(newDimension);
        dimension.setMakeTime(new Date());
        save(dimension);

        return ServerResponse.createSuccess("生成新二维码需要的字符串成功", newDimension);
    }

    private String checkDriverIsPay(Order order) {
        //校验平台信息费是否支付
        Goods goods = goodsService.get("gid", order.getGid());
        if (goods.getFeesOut() + goods.getFeesIn() > 0 && order.getFees() + order.getFeesOut() + order.getFeesIn() + order.getBalanceFees() <= 0)
            return "订单未支付";
        //校验单位代扣费是否支付
        if (goods.getFees1Out() > order.getFees1Out()) return "订单未支付";
        if (goods.getFees1In() > order.getFees1In()) return "订单未支付";
        if (goods.getFees2Out() > order.getFees2Out()) return "订单未支付";
        if (goods.getFees2In() > order.getFees2In()) return "订单未支付";
        //司机支付给客商或者友商的钱，客商或者友商是可以在出示二维码给司机扫码接单时手动输入金额的，
        //所以，该项金额的比较不能得出司机实际是否支付了订单的结论，则该项目不校验
        return null;
    }
}
