package com.erdos.coal.park.api.business.pojo;

import org.bson.types.ObjectId;

import java.io.Serializable;

public class SynCustomer implements Serializable {
    private ObjectId objectId;

    private String objId;
    private String mobile; //手机号
    private String name; //客商真实姓名
    private Integer state;//用户狀態    0-未认证，1-正常,2-停用
    private String phoneId;//手机序列号
    private String initialOpenid;   //用户初始注册时的微信openid
    private String fee;//设置默认收取司机的金额(元)
    private boolean thirdPartyUser;   //是否三方用户 false-否，true-是

    public void setObjectId(ObjectId objectId) {
        this.objectId = objectId;
    }

    public String getObjId() {
        return objectId.toHexString();
    }

    public void setObjId(String objId) {
        this.objId = objectId.toHexString();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(String phoneId) {
        this.phoneId = phoneId;
    }

    public String getInitialOpenid() {
        return initialOpenid;
    }

    public void setInitialOpenid(String initialOpenid) {
        this.initialOpenid = initialOpenid;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public boolean isThirdPartyUser() {
        return thirdPartyUser;
    }

    public void setThirdPartyUser(boolean thirdPartyUser) {
        this.thirdPartyUser = thirdPartyUser;
    }
}
