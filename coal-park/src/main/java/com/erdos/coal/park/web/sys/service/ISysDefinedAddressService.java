package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.UserDefinedAddress;
import com.erdos.coal.park.web.sys.pojo.DefinedAddressData;

public interface ISysDefinedAddressService extends IBaseMongoService<UserDefinedAddress> {

    EGridResult loadGrid(Integer page, Integer rows);

    //保存单条记录
    DefinedAddressData getAddress();

    ServerResponse addAddress(DefinedAddressData address);

    ServerResponse editAddress(DefinedAddressData address);

    ServerResponse delAddress(DefinedAddressData address);
}
