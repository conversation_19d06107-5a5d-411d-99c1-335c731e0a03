package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.pojo.OrderInfoData;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import org.bson.Document;

import java.util.Date;
import java.util.List;

public interface IOrderLogisticsService extends IBaseMongoService<OrderLogistics> {

    //维护订单物流信息
    ServerResponse<OrderLogistics> updateLog(String oid, String longitude, String latitude, Integer finishTag);

    //根据订单号查询订单物流信息
    ServerResponse<OrderLogistics> getByOid(String oid);

    //查询客商下物流订单，即运输途中的订单（手工下单的，面议的，没有出发点、终点的订单不显示）
    //ServerResponse<List<OrderInfoData>> logisticsOrderList();
    ServerResponse<EGridResult> logisticsOrderList(Integer page, Integer rows);
    ServerResponse<EGridResult> logisticsOrderList2(Integer page, Integer rows);

    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
    Document createOLDoc(String oid, Integer finishTag, String longitude, String latitude, Date time);

}
