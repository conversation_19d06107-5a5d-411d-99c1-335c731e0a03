package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import com.erdos.coal.park.web.app.service.IWebLogisticsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/web/app/logistics")
public class WebLogisticsController extends BaseController {

    @Resource
    private IWebLogisticsService logisticsService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(logisticsService.loadGrid(page, rows));
    }

    @PostMapping("/listPoint")
    public ServerResponse<List> listPointHandler() throws GlobalException {
        return ServerResponse.createSuccess(logisticsService.getPointList());
    }

    @PostMapping("/getMap")
    public ServerResponse<OrderLogistics> getMapHandler() throws GlobalException {
        return ServerResponse.createSuccess(logisticsService.get("oid", request.getParameter("oid")));

    }

    @PostMapping("/endPoint")
    public ServerResponse<Map> endPointHandler() {
        return ServerResponse.createSuccess(logisticsService.getMap());
    }
}
