package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_app_trade_price", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("bizContractCode")}),
        @Index(fields = {@Field("selfCode")})
})
public class TradePrice extends BaseMongoInfo {
    private boolean allowLimit;      //额度限定
    private String bizContractCode; //业务合同编号
    private Integer bizType;        //业务类型 出库:1   入库:0
    private boolean checkContainDeduct;  //验收包含扣除
    private boolean checkPayLimit;       //验收结算限制
    private boolean checkPayLimitWithHold;//验收结算限制预扣
    private String defaultArea;     //默认场区
    private String defaultDownUnit; //限定二级单位
    private Double internalPrice;   //内部单价
    private Double limitAllow;      //限定额度
    private Double limitWeight;     //限定计量
    private Double makeupInternalPrice; //内部补差单价
    private Double makeupPayPrice;  //结算补差单价
    private Double makeupPublicPrice;   //外部补差单价
    private boolean onceMeter;           //单次计量
    private Double payPrice;        //结算单价
    private Double publicPrice;     //外部单价
    private boolean reOnceMeter;         //反向计量
    private String selfCode;            //自身代码
    private Integer status;              //状态
    private String subCode;         //二级单位编码
    private Double tradeAmount;         //交易额
    private Double tradeCount;          //交易量
    private String unitCode;        //单位编码
    private boolean useOppositePrice;    //使用对方单价
    private String variety;         //品种
    private boolean weightLimit;     //计量限定

    private Integer fee;                //平台收费（分）-- 自定义

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizContractCode() {
        return bizContractCode;
    }

    public void setBizContractCode(String bizContractCode) {
        this.bizContractCode = bizContractCode;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getDefaultDownUnit() {
        return defaultDownUnit;
    }

    public void setDefaultDownUnit(String defaultDownUnit) {
        this.defaultDownUnit = defaultDownUnit;
    }

    public Double getLimitAllow() {
        return limitAllow;
    }

    public void setLimitAllow(Double limitAllow) {
        this.limitAllow = limitAllow;
    }

    public boolean isAllowLimit() {
        return allowLimit;
    }

    public boolean getAllowLimit() {
        return allowLimit;
    }

    public void setAllowLimit(boolean allowLimit) {
        this.allowLimit = allowLimit;
    }

    public String getDefaultArea() {
        return defaultArea;
    }

    public void setDefaultArea(String defaultArea) {
        this.defaultArea = defaultArea;
    }

    public Double getLimitWeight() {
        return limitWeight;
    }

    public void setLimitWeight(Double limitWeight) {
        this.limitWeight = limitWeight;
    }

    public boolean isWeightLimit() {
        return weightLimit;
    }

    public boolean getWeightLimit() {
        return weightLimit;
    }

    public void setWeightLimit(boolean weightLimit) {
        this.weightLimit = weightLimit;
    }

    public Double getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(Double payPrice) {
        this.payPrice = payPrice;
    }

    public Double getInternalPrice() {
        return internalPrice;
    }

    public void setInternalPrice(Double internalPrice) {
        this.internalPrice = internalPrice;
    }

    public Double getPublicPrice() {
        return publicPrice;
    }

    public void setPublicPrice(Double publicPrice) {
        this.publicPrice = publicPrice;
    }

    public Double getMakeupPayPrice() {
        return makeupPayPrice;
    }

    public void setMakeupPayPrice(Double makeupPayPrice) {
        this.makeupPayPrice = makeupPayPrice;
    }

    public Double getMakeupInternalPrice() {
        return makeupInternalPrice;
    }

    public void setMakeupInternalPrice(Double makeupInternalPrice) {
        this.makeupInternalPrice = makeupInternalPrice;
    }

    public Double getMakeupPublicPrice() {
        return makeupPublicPrice;
    }

    public void setMakeupPublicPrice(Double makeupPublicPrice) {
        this.makeupPublicPrice = makeupPublicPrice;
    }

    public boolean isCheckPayLimit() {
        return checkPayLimit;
    }

    public boolean getCheckPayLimit() {
        return checkPayLimit;
    }

    public void setCheckPayLimit(boolean checkPayLimit) {
        this.checkPayLimit = checkPayLimit;
    }

    public boolean isCheckPayLimitWithHold() {
        return checkPayLimitWithHold;
    }

    public boolean getCheckPayLimitWithHold() {
        return checkPayLimitWithHold;
    }

    public void setCheckPayLimitWithHold(boolean checkPayLimitWithHold) {
        this.checkPayLimitWithHold = checkPayLimitWithHold;
    }

    public boolean isCheckContainDeduct() {
        return checkContainDeduct;
    }

    public boolean getCheckContainDeduct() {
        return checkContainDeduct;
    }

    public void setCheckContainDeduct(boolean checkContainDeduct) {
        this.checkContainDeduct = checkContainDeduct;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Double getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(Double tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public Double getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Double tradeCount) {
        this.tradeCount = tradeCount;
    }

    public boolean isOnceMeter() {
        return onceMeter;
    }

    public boolean getOnceMeter() {
        return onceMeter;
    }

    public void setOnceMeter(boolean onceMeter) {
        this.onceMeter = onceMeter;
    }

    public boolean isReOnceMeter() {
        return reOnceMeter;
    }

    public boolean getReOnceMeter() {
        return reOnceMeter;
    }

    public void setReOnceMeter(boolean reOnceMeter) {
        this.reOnceMeter = reOnceMeter;
    }

    public boolean isUseOppositePrice() {
        return useOppositePrice;
    }

    public boolean getUseOppositePrice() {
        return useOppositePrice;
    }

    public void setUseOppositePrice(boolean useOppositePrice) {
        this.useOppositePrice = useOppositePrice;
    }

    public String getSelfCode() {
        return selfCode;
    }

    public void setSelfCode(String selfCode) {
        this.selfCode = selfCode;
    }

    public Integer getFee() {
        return fee;
    }

    public void setFee(Integer fee) {
        this.fee = fee;
    }
}
