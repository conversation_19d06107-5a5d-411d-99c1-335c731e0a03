package com.erdos.coal.park.api.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.business.dao.IUnitInfoDao;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service(value = "unitInfoService")
public class UnitInfoServiceImpl extends BaseMongoServiceImpl<UnitInfo, IUnitInfoDao> implements IUnitInfoService {
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private ISysUnitService sysUnitService;

    @Override
    public ServerResponse<String> boundCustomer(String uName, String telCode, String unitCode, String userCode) {
        //TODO:1.姓名、出生年月和手机号 验证 平台是否存在该客商
        CustomerUser customerUser = customerUserService.get("mobile", telCode);
        if (customerUser == null)
            return ServerResponse.createError("用户未在平台注册，绑定失败");

        //todo:判断认证的企业是否在平台存在
        SysUnit sysUnit = sysUnitService.get("code", Pattern.compile(unitCode + "$"));
        if (sysUnit == null)
            return ServerResponse.createError("用户所在企业未在平台维护，请联系管理员");

        //TODO:2.判断用户是否已经绑定过了
        UnitInfo unitInfo = this.get("userCode", userCode);
        if (unitInfo != null)
//            return ServerResponse.createError("用户已经绑定过，绑定失败");
            return ServerResponse.createSuccess("用户已经绑定过，绑定失败");

        // 同一个手机号在同一个一级单位下认证过则重复
        Query<UnitInfo> onlyOneQuery = this.createQuery();
        onlyOneQuery.criteria("cid").equal(customerUser.getObjectId().toHexString());
        onlyOneQuery.criteria("unitCode").equal(sysUnit.getCode());
        UnitInfo onlyOneUnitInfo = this.get(onlyOneQuery);
        if (onlyOneUnitInfo != null) return ServerResponse.createError("用户["+telCode+"]已经在单位["+unitCode+"]使用usercode为<"+onlyOneUnitInfo.getUserCode()+">认证绑定");

        //TODO:3.客商认证后，状态默认改为正常可用
        Query<CustomerUser> query = customerUserService.createQuery();
        query.filter("_id", customerUser.getObjectId());
        query.filter("updateTime", customerUser.getUpdateTime());
        UpdateOperations<CustomerUser> updateOperations = customerUserService.createUpdateOperations();
        updateOperations.set("state", 1);
        CustomerUser result = customerUserService.findAndModify(query, updateOperations);
        if (result == null) {
            return ServerResponse.createError("系统繁忙，请稍后重试");
        }

        //TODO:4，添加客商绑定信息
        unitInfo = new UnitInfo();
        unitInfo.setCid(customerUser.getObjectId().toString());
        unitInfo.setUnitCode(sysUnit.getCode());
        unitInfo.setUserCode(userCode);
        unitInfo.setCustomerUser(customerUser);
        this.save(unitInfo);

        return ServerResponse.createSuccess("绑定成功");
    }

    @Override
    public ServerResponse<String> cancelBound(String userCode) {
        UnitInfo unitInfo = this.get("userCode", userCode);
        if (unitInfo != null && unitInfo.getCustomerUser() != null) {
            //客商账号state状态置为不可用
            Query<UnitInfo> uQuery = this.createQuery();
            uQuery.filter("cid", unitInfo.getCid());
            List<UnitInfo> list = uQuery.find().toList();
            if (list.size() == 1) {
                Query<CustomerUser> query = customerUserService.createQuery();
                query.filter("_id", unitInfo.getCustomerUser().getObjectId());
                query.filter("updateTime", unitInfo.getCustomerUser().getUpdateTime());
                UpdateOperations<CustomerUser> updateOperations = customerUserService.createUpdateOperations();
                updateOperations.set("state", 0);
                CustomerUser result = customerUserService.findAndModify(query, updateOperations);
                if (result == null) {
                    return ServerResponse.createError("系统繁忙，请稍后重试");
                }
            }

            this.delete(unitInfo.getObjectId());
        }
        if (unitInfo != null) {
            this.delete(unitInfo.getObjectId());
        }
        return ServerResponse.createSuccess("废除成功");
    }

    @Override
    public ServerResponse<String> addSecondUnit(String pCode, String subUnitList) {
        SysUnit sysUnit = sysUnitService.get("code", Pattern.compile(pCode + "$"));
        if (sysUnit == null)
            return ServerResponse.createError("该单位的一级单位未在平台注册");

        List<Map> listStr = JSON.parseArray(subUnitList, Map.class);

        List<SysUnit> list = new ArrayList<>();
        for (Map<String, String> map : listStr) {
            SysUnit secUnit = new SysUnit();
            String code = map.get("code");
            String name = map.get("name");

            SysUnit sysUnit1 = sysUnitService.get("code", code);
            if (sysUnit1 == null) { //不存在的新增，存在的跳过
                secUnit.setId(Utils.getUUID());
                secUnit.setpCode(sysUnit.getCode());
                secUnit.setCode(code);
                secUnit.setName(name);
                list.add(secUnit);
            }
        }
        sysUnitService.save(list);

        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> editSecondUnit(String code, String name) {
        SysUnit sysUnit = sysUnitService.get("code", code);
        if (sysUnit == null)
            return ServerResponse.createSuccess("该二级单位未在平台添加");

        Query<SysUnit> query = sysUnitService.createQuery();
        query.filter("code", code);
        UpdateOperations<SysUnit> updateOperations = sysUnitService.createUpdateOperations();
        updateOperations.set("name", name);
        sysUnitService.update(query, updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<String> delSecondUnit(String code) {
        SysUnit sysUnit = sysUnitService.get("code", code);
        if (sysUnit == null)
            return ServerResponse.createError("该二级单位未在平台添加");

        sysUnitService.delete("code", code);
        return ServerResponse.createSuccess("删除成功");
    }

    @Override
    public ServerResponse<AccessToken> bizUserLogin(String username, String password) {
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String mdpw = passwordEncoder.encode(password);*/
        if (StrUtil.isEmpty(password)) return ServerResponse.createError("密码错误");
        String mdpw = Utils.md5(password);

        //TODO：1, 业务系统用户账号验证
        Query<SysUnit> query = sysUnitService.createQuery();
        query.criteria("loginName").equal(username);
        query.criteria("password").equal(mdpw);
        SysUnit sysUnit = sysUnitService.get(query);
//        SysUnit sysUnit = sysUnitService.get("loginName", username);
//        if (sysUnit == null || !mdpw.equals(sysUnit.getPassword()))
        if (sysUnit == null) return ServerResponse.createError("用户名或密码错误");

        //shiroUserSecurity.putShiroUserToCache(sysUnit.getId(), username, mdpw, UserType.BIZ.toString());

        String token = JwtUtil.sign(UserType.BIZ, username, sysUnit.getPassword());
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }
}
