package com.erdos.coal.park.api.business.controller;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.pojo.*;
import com.erdos.coal.park.api.business.service.ISynOrderService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

//"同步平台的订单到 企业系统,供平台的定时任务"
@RestController
@RequestMapping("/api/bus/syn_order")
public class SynOrderController extends BaseController {
    @Resource
    private ISynOrderService synOrderService;

    @InvokeLog(description = "同步平台的废除订单到企业系统 接口", printReturn = false) //日志
    @PostMapping(value = "/cancel")
    public ServerResponse<List<OrderAggregate>> synOrderCancelHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
//        return ServerResponse.createError("接口停用");

//        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        /*if (e.equals("e"))
            throw new GlobalException("-1", "globalException: e is null");
        return ServerResponse.createSuccess("api main");*/
// userCode, unitCode, bizType, 票号集
        String unitCode = (String) data.get("unitCode");
        return synOrderService.cancelOrderList(unitCode);//ServerResponse.createSuccess("查询成功","{}");
    }

    @InvokeLog(description = "查询平台发货物流订单 接口", printReturn = false) //日志
    @PostMapping(value = "/search")
    public ServerResponse<List<SynOrder>> searchOrderHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String objId = data.getString("objId");
        Integer rows = data.getInteger("rows");

        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
//        return synOrderService.searchOrderList(objId, startTime, endTime, rows);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "查询平台发货物流订单 接口", printReturn = false) //日志
    @PostMapping(value = "/search2")
    public ServerResponse<List<SynOrder2>> searchOrder2Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String objId = data.getString("objId");
        Integer rows = data.getInteger("rows");

        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
//        return synOrderService.searchOrderList2(objId, startTime, endTime, rows);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "查询平台客商信息 接口", printReturn = false) //日志
    @PostMapping(value = "/search_customer")
    public ServerResponse<List<SynCustomer>> searchCustomerUserHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String objId = data.getString("objId");
        Integer rows = data.getInteger("rows");

        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
//        return synOrderService.searchCustomerUserList(objId, startTime, endTime, rows);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "查询平台司机信息 接口", printReturn = false) //日志
    @PostMapping(value = "/search_driver")
    public ServerResponse<List<SynDriverInfo>> searchDriverInfoHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String objId = data.getString("objId");
        Integer rows = data.getInteger("rows");

        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
//        return synOrderService.searchDriverInoList(objId, startTime, endTime, rows);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "查询平台车辆信息 接口", printReturn = false) //日志
    @PostMapping(value = "/search_car")
    public ServerResponse<List<SynCar>> searchCarHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String objId = data.getString("objId");
        Integer rows = data.getInteger("rows");

        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
//        return synOrderService.searchCarList(objId, startTime, endTime, rows);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "查询平台单位信息 接口", printReturn = false) //日志
    @PostMapping(value = "/search_unit")
    public ServerResponse<List<SynSysUnit>> searchSysUnitHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String objId = data.getString("objId");
        Integer rows = data.getInteger("rows");

        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
//        return synOrderService.searchSysUnitList(objId, startTime, endTime, rows);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "billCode查询车辆轨迹 接口", printReturn = false) //日志
    @PostMapping(value = "/get_historical_track")
    public ServerResponse<String> getHistoricalTrackHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String billCode = data.getString("billCode");
        Integer bizType = data.getInteger("bizType");

//        return synOrderService.getHistoricalTrack(billCode, bizType);
        return ServerResponse.createError("接口关闭");
    }

    @InvokeLog(description = "billCode查询车辆过磅图片 接口", printReturn = false) //日志
    @PostMapping(value = "/get_photo")
    public ServerResponse<Map<String, String>> getPhotoHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        String billCode = data.getString("billCode");
        Integer bizType = data.getInteger("bizType");

//        return synOrderService.getPhotoByBillCode(billCode, bizType);
        return ServerResponse.createError("接口关闭");
    }
}
