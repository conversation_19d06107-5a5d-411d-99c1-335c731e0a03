package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.RefundPre;
import com.erdos.coal.park.web.app.pojo.RefundPreData;

import java.util.List;
import java.util.Map;

public interface IRefundPreService extends IBaseMongoService<RefundPre> {
    //查询微信昵称
    ServerResponse<String> getWxNickname();

    //获取微信昵称，openid
    ServerResponse<String> bindWxOpenid(String code, String wxName);

    //app 企业付款到零钱申请
    ServerResponse<String> wxRefundApply(double amount);

    //小程序 企业付款到零钱申请
    ServerResponse<String> wxRefundApply(double amount, String openid, String name);

    //管理员审核通过企业付款到零钱
    Map<String, String> wxRefundPass(String transferNo, RefundPre refundPre) throws Exception;

    //查询企业付款到零钱申请进度
    ServerResponse<List<RefundPreData>> queryRefund();

    //客商或者司机账户绑定支付宝前，拼接授权url
    ServerResponse<String> PreBindingAliPay(Integer cusOrDri);

    //客商或者司机账户绑定支付宝，auth_code获取用户信息
    ServerResponse<String> BindingAliPay(String appId, String authCode);

    //客商或司机提出支付宝提现申请
    ServerResponse<String> RefundAliPayPre(String aliPayLoginId, String name, String amount);

    //支付宝提现方式，审核通过的情况
    Map<String, String> aliPayRefundPass(String transferNo);
}
