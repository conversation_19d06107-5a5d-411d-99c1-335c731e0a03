package com.erdos.coal.park.api.driver.entity;


import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_wechat_driver_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("openid")})
})
public class WechatDriverInfo extends BaseMongoInfo {
    private String did;   //关联司机app账号

    private String carNum;  //车牌号
    //@Indexed(options = @IndexOptions(name = "openid", background = true))
    private String openid;  //微信号 用户唯一标识
    private String sessionKey;  //会话密钥
    private Integer times = 0;  //使用次数

    private String drivingPho1;      //行驶证照片第一页
    private String drivingPho2;     //行驶证照片第二页
    private String drivingPho3;     //行驶证照片第三页
    private String driverPho;       //驾驶证照片
    private String driverPho2;       //驾驶证照片(副页）
    private String carIdentityPhoBef;     //车主身份证照片正面
    private String carIdentityPhoBack;     //车主身份证照片背面
    private String driIdentityPhoBef;       //司机身份证照片正面
    private String driIdentityPhoBack;      //司机身份证照片背面
    private String bankCardPho;    //银行卡照片
    private String roadQCPho;    //道路从业资格证照片
    private String driverCarPho;    //司机和车的合影照片

    private String name;    //姓名
    private String identity;   //身份证号

    private Integer state = 0;        //用戶状态 0：未审核不可用 1：正常 2：停用 3:审核未通过 4：未审核但可用

    private String carInfoId;   // 码表 车型id
    private String carType; //车型
    private Double capacity = 0.0;    //载重（kg） 识别行驶证照片
    private String axlesNumber; //车轴数

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getDrivingPho1() {
        return drivingPho1;
    }

    public void setDrivingPho1(String drivingPho1) {
        this.drivingPho1 = drivingPho1;
    }

    public String getDrivingPho2() {
        return drivingPho2;
    }

    public void setDrivingPho2(String drivingPho2) {
        this.drivingPho2 = drivingPho2;
    }

    public String getDrivingPho3() {
        return drivingPho3;
    }

    public void setDrivingPho3(String drivingPho3) {
        this.drivingPho3 = drivingPho3;
    }

    public String getDriverPho() {
        return driverPho;
    }

    public void setDriverPho(String driverPho) {
        this.driverPho = driverPho;
    }

    public String getCarIdentityPhoBef() {
        return carIdentityPhoBef;
    }

    public void setCarIdentityPhoBef(String carIdentityPhoBef) {
        this.carIdentityPhoBef = carIdentityPhoBef;
    }

    public String getCarIdentityPhoBack() {
        return carIdentityPhoBack;
    }

    public void setCarIdentityPhoBack(String carIdentityPhoBack) {
        this.carIdentityPhoBack = carIdentityPhoBack;
    }

    public String getDriIdentityPhoBef() {
        return driIdentityPhoBef;
    }

    public void setDriIdentityPhoBef(String driIdentityPhoBef) {
        this.driIdentityPhoBef = driIdentityPhoBef;
    }

    public String getDriIdentityPhoBack() {
        return driIdentityPhoBack;
    }

    public void setDriIdentityPhoBack(String driIdentityPhoBack) {
        this.driIdentityPhoBack = driIdentityPhoBack;
    }

    public String getDriverCarPho() {
        return driverCarPho;
    }

    public void setDriverCarPho(String driverCarPho) {
        this.driverCarPho = driverCarPho;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getCarInfoId() {
        return carInfoId;
    }

    public void setCarInfoId(String carInfoId) {
        this.carInfoId = carInfoId;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getAxlesNumber() {
        return axlesNumber;
    }

    public void setAxlesNumber(String axlesNumber) {
        this.axlesNumber = axlesNumber;
    }

    public String getDriverPho2() {
        return driverPho2;
    }

    public void setDriverPho2(String driverPho2) {
        this.driverPho2 = driverPho2;
    }

    public String getBankCardPho() {
        return bankCardPho;
    }

    public void setBankCardPho(String bankCardPho) {
        this.bankCardPho = bankCardPho;
    }

    public String getRoadQCPho() {
        return roadQCPho;
    }

    public void setRoadQCPho(String roadQCPho) {
        this.roadQCPho = roadQCPho;
    }
}
