package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.customer.entity.DriverGroup;

public interface IWebDriverGroupService extends IBaseMongoService<DriverGroup> {

    //司机组列表
    EGridResult group(Integer page, Integer rows);

    //客商司机组中司机列表
    EGridResult driver(Integer page, Integer rows);
}
