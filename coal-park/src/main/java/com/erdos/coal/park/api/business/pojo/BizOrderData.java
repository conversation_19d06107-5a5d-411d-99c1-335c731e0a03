package com.erdos.coal.park.api.business.pojo;

import java.io.Serializable;
import java.util.Date;

public class BizOrderData implements Serializable {
    private String billCode;   //订单属于企业系统的编号
    private Integer checking;   //订单的检票状态
    private String carNum;      //车牌号
    private Double capacity;    //载重kg 识别行驶证照片
    private String axlesNumber; //车轴数
    private String carType;     //车型
    private String carTypeName; //车型名称

    private Double otherGross;
    private Double otherTare;
//    private Date otherOutTime;  //发货单位，checking 为 '检票' 时，记录的时间
//    private Date otherInTime;   //收货单位，checking 为 '一检' 时，记录的时间
    private String otherInTime;   //收货单位，checking 为 '一检' 时，记录的时间    yyyy-MM-dd HH:mm:ss
    private String otherOutTime;  //发货单位，checking 为 '检票' 时，记录的时间    yyyy-MM-dd HH:mm:ss

    private String isQua;       //是否申报防疫信息，0-未申报，1-申报
    private String quaResult;   //防疫信息审核结果，0-未通过，1-通过

    private String spell = "";   //运往地编码
    private String place = "";   //运往地名称

    private String oid;     //平台订单号

    private String dvrName;     //司机姓名
    private String dvrMobile;   //司机电话
    private String dvrIdentity; //司机身份证号码

    private String cipcherText; // 智慧能源-提煤单加密串

    private Double tareWeight;  // 司机接单车辆皮重

    private Double inNetWeight;         // 采购业务需要录入对方的净重
    private String inNetWeightPho;      // 采购业务需要录入对方的净重（司机录入时，需上传照片）
    private String loadPound;           // 采购业务需要录入对方的装货单号
    private String loadTime;              // 采购业务需要录入对方的装货时间 yyyy-MM-dd HH:mm:ss

    private String needPayCost;    //司机需要支付装卸费
    private String needAddPayCost; //司机需要补充支付装卸费
    private String vehicleCode;    //司机选择的车型

    public BizOrderData() {
    }

    public BizOrderData(String billCode, Integer checking, String carNum, Double capacity, String axlesNumber, String carType) {
        this.billCode = billCode;
        this.checking = checking;
        this.carNum = carNum;
        this.capacity = capacity;
        this.axlesNumber = axlesNumber;
        this.carType = carType;
    }

//    public BizOrderData(String billCode, Integer checking, String carNum, Double capacity, String axlesNumber, String carType, Double otherGross, Double otherTare, Date otherOutTime, Date otherInTime) {
    public BizOrderData(String billCode, Integer checking, String carNum, Double capacity, String axlesNumber, String carType, Double otherGross, Double otherTare, String otherOutTime, String otherInTime) {
        this.billCode = billCode;
        this.checking = checking;
        this.carNum = carNum;
        this.capacity = capacity;
        this.axlesNumber = axlesNumber;
        this.carType = carType;
        this.otherGross = otherGross;
        this.otherTare = otherTare;
        this.otherOutTime = otherOutTime;
        this.otherInTime = otherInTime;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public Integer getChecking() {
        return checking;
    }

    public void setChecking(Integer checking) {
        this.checking = checking;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getAxlesNumber() {
        return axlesNumber;
    }

    public void setAxlesNumber(String axlesNumber) {
        this.axlesNumber = axlesNumber;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }

    public Double getOtherGross() {
        return otherGross;
    }

    public void setOtherGross(Double otherGross) {
        this.otherGross = otherGross;
    }

    public Double getOtherTare() {
        return otherTare;
    }

    public void setOtherTare(Double otherTare) {
        this.otherTare = otherTare;
    }

    /*public Date getOtherOutTime() {
        return otherOutTime;
    }

    public void setOtherOutTime(Date otherOutTime) {
        this.otherOutTime = otherOutTime;
    }

    public Date getOtherInTime() {
        return otherInTime;
    }

    public void setOtherInTime(Date otherInTime) {
        this.otherInTime = otherInTime;
    }*/

    public String getOtherInTime() {
        return otherInTime;
    }

    public void setOtherInTime(String otherInTime) {
        this.otherInTime = otherInTime;
    }

    public String getOtherOutTime() {
        return otherOutTime;
    }

    public void setOtherOutTime(String otherOutTime) {
        this.otherOutTime = otherOutTime;
    }

    public String getIsQua() {
        return isQua;
    }

    public void setIsQua(String isQua) {
        this.isQua = isQua;
    }

    public String getQuaResult() {
        return quaResult;
    }

    public void setQuaResult(String quaResult) {
        this.quaResult = quaResult;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getDvrName() {
        return dvrName;
    }

    public void setDvrName(String dvrName) {
        this.dvrName = dvrName;
    }

    public String getDvrMobile() {
        return dvrMobile;
    }

    public void setDvrMobile(String dvrMobile) {
        this.dvrMobile = dvrMobile;
    }

    public String getDvrIdentity() {
        return dvrIdentity;
    }

    public void setDvrIdentity(String dvrIdentity) {
        this.dvrIdentity = dvrIdentity;
    }

    public String getCipcherText() {
        return cipcherText;
    }

    public void setCipcherText(String cipcherText) {
        this.cipcherText = cipcherText;
    }

    public Double getTareWeight() {
        return tareWeight;
    }

    public void setTareWeight(Double tareWeight) {
        this.tareWeight = tareWeight;
    }

    public Double getInNetWeight() {
        return inNetWeight;
    }

    public void setInNetWeight(Double inNetWeight) {
        this.inNetWeight = inNetWeight;
    }

    public String getInNetWeightPho() {
        return inNetWeightPho;
    }

    public void setInNetWeightPho(String inNetWeightPho) {
        this.inNetWeightPho = inNetWeightPho;
    }

    public String getLoadPound() {
        return loadPound;
    }

    public void setLoadPound(String loadPound) {
        this.loadPound = loadPound;
    }

    /*public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }*/

    public String getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(String loadTime) {
        this.loadTime = loadTime;
    }

    public String getNeedPayCost() {
        return needPayCost;
    }

    public void setNeedPayCost(String needPayCost) {
        this.needPayCost = needPayCost;
    }

    public String getNeedAddPayCost() {
        return needAddPayCost;
    }

    public void setNeedAddPayCost(String needAddPayCost) {
        this.needAddPayCost = needAddPayCost;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }
}
