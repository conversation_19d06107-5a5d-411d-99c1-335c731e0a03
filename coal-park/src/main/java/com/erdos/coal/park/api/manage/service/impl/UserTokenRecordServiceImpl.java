package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.manage.dao.IUserTokenRecordDao;
import com.erdos.coal.park.api.manage.entity.UserTokenRecord;
import com.erdos.coal.park.api.manage.service.IUserTokenRecordService;
import org.springframework.stereotype.Service;

@Service("userTokenRecordService")
public class UserTokenRecordServiceImpl extends BaseMongoServiceImpl<UserTokenRecord, IUserTokenRecordDao> implements IUserTokenRecordService {
}
