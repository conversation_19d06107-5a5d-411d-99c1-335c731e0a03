package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IWechatCusService;
import com.erdos.coal.park.api.manage.pojo.WeChatData;
import com.erdos.coal.park.api.manage.service.IImgVerifyTokenService;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by LIGX on 2018/12/27.
 * 客商管理接口
 */
//"1.客商管理接口列表"
@RestController
@RequestMapping("/api/manage/cus")
public class ManageController extends BaseController {
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IWechatCusService wechatCusService;
    @Resource
    private IImgVerifyTokenService iImgVerifyTokenService;

    //注册 -------------------------------------------------------------------------------------------------------------
    //在注册之前应该有短信接口
    @InvokeLog(description = "客商注册接口") //日志
    @PostMapping(value = "/reg2")
    public ServerResponse<AccessToken> reg2Handler(
            @RequestParam(value = "phoneId") String phoneId,   //"手机序列号"
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "password") String password, //"密码"
            @RequestParam(value = "code") String code          //"短信验证码"
    ) throws GlobalException {
        return customerUserService.customReg2(phoneId, mobile, password, code);
    }

    //登录 -------------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "客商登录接口") //日志
    @PostMapping(value = "/login2")
    public ServerResponse<AccessToken> login2Handler(
            @RequestParam(value = "phoneId") String phoneId,            //"手机序列号"
            @RequestParam(value = "username") String username,          //"用户名/手机号"
            @RequestParam(value = "password") String password,          //"密码"
//            @ApiParam(name = "deviceId", required = true, value = "设备id") @RequestParam(value = "deviceId") String deviceId,
            @RequestParam(value = "code", required = false) String code    //"更换登录设备时需要的短信验证码"
    ) throws GlobalException {
        return customerUserService.customLogin2(phoneId, username, password, code);
    }

    @InvokeLog(description = "客商登录接口") //日志
    @PostMapping(value = "/login3")
    public ServerResponse<AccessToken> login3Handler(
            @RequestParam(value = "phoneId") String phoneId,            //"手机序列号"
            @RequestParam(value = "username") String username,          //"用户名/手机号"
            @RequestParam(value = "password") String password,          //"密码"
            @RequestParam(value = "code", required = false) String code,    //"更换登录设备时需要的短信验证码"
            @RequestParam(value = "imgCode", required = false) String imgCode    //"图片滑块验证成功授权码"
    ) throws GlobalException {
        if ((StrUtil.isEmpty(code) && iImgVerifyTokenService.checkImaVerifyToken(imgCode))||(StrUtil.isNotEmpty(code))) {
            return customerUserService.customLogin2(phoneId, username, password, code);
        } else {
            return ServerResponse.createError("验证失败！");
        }
    }

    @InvokeLog(description = "校验客商是否更换微信接口") //日志
    @PostMapping(value = "/check_phoneId")
    public ServerResponse<Boolean> checkPhoneIdHandler(
            @RequestParam(value = "phoneId") String phoneId,   //"手机序列号"
            @RequestParam(value = "mobile") String mobile     //"手机号码"
    ) throws GlobalException {
        return customerUserService.checkPoneId(mobile, phoneId);
    }

    //短信验证码重置密码
    @InvokeLog(description = "客商短信验证码重置密码接口") //日志
    @PostMapping(value = "/resetPwd")
    public ServerResponse<AccessToken> resetPwdHandler(
            @RequestParam(value = "newPwd") String newPwd,         //"新密码"
            @RequestParam(value = "confirmPwd") String confirmPwd, //"确认密码"
            @RequestParam(value = "code") String code,             //"短信验证码"
            @RequestParam(value = "username") String username      //"用户名/手机号"
    ) throws GlobalException {
        /*ServerResponse<CustomerUser> res = customerUserService.resetPwd(username, code, newPwd, confirmPwd);
        if (res.getStatus().equals(ResponseCode.SUCCESS.getCode())) {
            CustomerUser user = res.getData();
            return loginManageHandler.customerUserLogin(user.getPhoneId(), username, newPwd, code);
        } else {
            return new ServerResponse<>(res.getStatus(), res.getMsg());
        }*/
        return customerUserService.resetPwd(username, code, newPwd, confirmPwd);
    }


    //微信小程序授权---------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "客商微信小程序授权 接口") //日志
    @PostMapping(value = "/weChatAuth")
    public ServerResponse<WeChatData> weChatAuthHandler(
            @RequestParam(value = "code") String code   //"登录时获取的 code"
    ) throws GlobalException {
        return wechatCusService.wechatUserLogin(code);
    }
}
