package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Entity(value = "sys_user", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")}, options = @IndexOptions(unique = true))
})
public class SysUser extends BaseMongoInfo {

    //@Indexed(options = @IndexOptions(name = "_idx_user_id", unique = true, background = true))
    private String id;

    @Property("pId")
    private String _parentId; //父节点id

    private String name;

    //@NotEmpty :不能为null，且Size>0
    //@NotNull  :不能为null，但可以为empty,没有Size的约束
    //@NotBlank :只用于String,不能为null且trim()之后size>0

    @NotBlank(message = "登录名不能为空")
    private String loginName;//登录名

    @NotEmpty(message = "密码不能为空")
    private String password;

    @Pattern(regexp = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$", message = "手机号格式错误")
    private String phoneNum;

    @Email(message = "邮箱格式错误")
    private String email;

    private String[] roles;

    private String roleId;//角色Id

    private String state;//open/closed

    private Integer type = 1; //0用户组; 1用户

    private Integer order = 0; //排序号

    private Integer enable = 1; //启用

    //@Override
    public String getId() {
        return id;
    }

    //@Override
    public void setId(String id) {
        this.id = id;
    }

    public String get_parentId() {
        return _parentId;
    }

    public void set_parentId(String _parentId) {
        this._parentId = _parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String[] getRoles() {
        return roles;
    }

    public void setRoles(String[] roles) {
        this.roles = roles;
    }
}