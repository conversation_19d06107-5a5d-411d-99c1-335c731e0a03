package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.api.manage.dao.IOwnerAppealDao;
import com.erdos.coal.park.api.manage.entity.OwnerAppeal;
import com.erdos.coal.park.api.manage.service.IOwnerAppealService;
import com.erdos.coal.utils.ObjectUtil;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("ownerAppealService")
public class OwnerAppealServiceImpl extends BaseMongoServiceImpl<OwnerAppeal, IOwnerAppealDao> implements IOwnerAppealService {
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;

    @Override
    public ServerResponse<OwnerAppeal> saveAppeal(MultipartFile cardBef, MultipartFile cardBack, MultipartFile ownerCard, MultipartFile license, String mobile, String identity, String carNum, String reason, Integer appOrWx) {

        //TODO: 1, 手机号合法性校验(测试放开)
//        if (!SysCheck.isPhone(mobile)) {
//            return ServerResponse.createError("手机号码不正确");
//        }

        if (appOrWx == 0) {
            Query<DriverInfo> query = driverInfoService.createQuery();
            query.filter("identity", identity);
            query.filter("carNum", carNum);
            DriverInfo driverInfo = driverInfoService.get(query);
            if (ObjectUtil.isNull(driverInfo))
                return ServerResponse.createError("请确认身份证号和车牌号输入正确");
        } else if (appOrWx == 1) {
            Query<WechatDriverInfo> query = wechatDriverInfoService.createQuery();
            query.filter("identity", identity);
            query.filter("carNum", carNum);
            WechatDriverInfo wechatDriverInfo = wechatDriverInfoService.get(query);
            if (ObjectUtil.isNull(wechatDriverInfo))
                return ServerResponse.createError("请确认身份证号和车牌号输入正确");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("identity", identity);
        map.put("carNum", carNum);
        OwnerAppeal appeal = this.get(map);
        if (ObjectUtil.isNotNull(appeal)) {
            Long updateTime = appeal.getUpdateTime();
            Query<OwnerAppeal> query = this.createQuery();
            query.filter("identity", identity);
            query.filter("carNum", carNum);
            query.filter("updateTime >= ", updateTime);
            query.filter("updateTime <= ", updateTime + (24 * 60 * 60 * 1000));
            List<OwnerAppeal> list = query.find().toList();
            if (list.size() > 0)
                return ServerResponse.createError("同一天只能申诉一次");
        }

        OwnerAppeal ownerAppeal = new OwnerAppeal();
        String id = Utils.getUUID();
        ownerAppeal.setId(id);

        ownerAppeal.setMobile(mobile);
        ownerAppeal.setReason(reason);
        ownerAppeal.setIdentity(identity);
        ownerAppeal.setCarNum(carNum);

        String suffixName;
        String fileName;
        if (cardBef != null) {
            fileName = cardBef.getOriginalFilename();
            suffixName = fileName.substring(fileName.lastIndexOf("."));
            //车主身份证正面
            fileInfoDao.deleteByUId(id + "cardBef" + suffixName, "t_owner_appeal");
            String carBefStr = fileInfoDao.fileSave(id + "cardBef", cardBef, "t_owner_appeal");
            ownerAppeal.setCardBef(carBefStr);
        }
        if (cardBack != null) {
            fileName = cardBack.getOriginalFilename();
            suffixName = fileName.substring(fileName.lastIndexOf("."));
            //车主身份证反面
            fileInfoDao.deleteByUId(id + "cardBack" + suffixName, "t_owner_appeal");
            String carBackStr = fileInfoDao.fileSave(id + "cardBack", cardBack, "t_owner_appeal");
            ownerAppeal.setCardBack(carBackStr);
        }
        if (ownerCard != null) {
            fileName = ownerCard.getOriginalFilename();
            suffixName = fileName.substring(fileName.lastIndexOf("."));
            fileInfoDao.deleteByUId(id + "ownerCard" + suffixName, "t_owner_appeal");
            String ownerCardStr = fileInfoDao.fileSave(id + "ownerCard", ownerCard, "t_owner_appeal");
            ownerAppeal.setOwnerCard(ownerCardStr);
        }
        if (license != null) {
            fileName = license.getOriginalFilename();
            suffixName = fileName.substring(fileName.lastIndexOf("."));
            fileInfoDao.deleteByUId(id + "license" + suffixName, "t_owner_appeal");
            String licenseStr = fileInfoDao.fileSave(id + "license", license, "t_owner_appeal");
            ownerAppeal.setLicense(licenseStr);
        }
        this.save(ownerAppeal);
        return ServerResponse.createSuccess("保存成功", ownerAppeal);
    }
}
