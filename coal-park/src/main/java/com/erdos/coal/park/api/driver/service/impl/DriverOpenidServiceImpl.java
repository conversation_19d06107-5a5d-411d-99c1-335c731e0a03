package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.driver.dao.IDriverOpenidDao;
import com.erdos.coal.park.api.driver.entity.DriverOpenid;
import com.erdos.coal.park.api.driver.service.IDriverOpenidService;
import org.springframework.stereotype.Service;

@Service("driverOpenidService")
public class DriverOpenidServiceImpl extends BaseMongoServiceImpl<DriverOpenid, IDriverOpenidDao> implements IDriverOpenidService {
}
