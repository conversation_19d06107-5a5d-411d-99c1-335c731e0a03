package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import com.erdos.coal.park.api.driver.service.IOrderLogisticsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单物流位置信息接口
 */
//"司机APP订单物流接口列表"
@RestController
@RequestMapping("/api/dvr/order_logistics")
public class OrderLogisticsController extends BaseController {
    @Resource
    private IOrderLogisticsService orderLogisticsService;

    @InvokeLog(description = "维护订单物流信息 接口", printReturn = false) //日志
    @PostMapping(value = "/add_ord_log")
    public ServerResponse<OrderLogistics> addOrderLogisticsHandler(
            @RequestParam(value = "oid") String oid,                                //"订单号"
            @RequestParam(value = "longitude") String longitude,                    //"经度"
            @RequestParam(value = "latitude") String latitude,                      //"纬度"
            @RequestParam(value = "finishTag", required = false) Integer finishTag  //"订单是否完成 1或不写-运输途中2-完成"
    ) throws GlobalException {
        return orderLogisticsService.updateLog(oid, longitude, latitude, finishTag);
    }

}
