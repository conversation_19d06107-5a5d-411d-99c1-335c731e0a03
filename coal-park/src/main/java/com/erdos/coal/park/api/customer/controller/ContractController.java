package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.business.service.IContractService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//"客商APP查询企业数据接口列表"
@RestController
@RequestMapping("/api/cus/contract")
public class ContractController extends BaseController {
    @Resource
    private IContractService contractService;

    @InvokeLog(description = "查询客商对应企业服务器《客户一级单位》 接口", printReturn = false) //日志
    @PostMapping(value = "/unit_code")
    public ServerResponse<List<CompanyUnit>> unitCodeHandler() throws GlobalException {
        return contractService.getUnitCode();
    }

    @InvokeLog(description = "查询客商对应企业服务器《合同，票号区间》 接口") //日志
    @PostMapping(value = "/biz_contract_code")
    public ServerResponse<Object> bizContractCodeHandler(
            @RequestParam(value = "unitCode") String unitCode,  //"一级单位编号"
            @RequestParam(value = "bizType") Integer bizType    //"出货单位(1)或入货单位(0)"
    ) throws GlobalException {
        return contractService.getBizContractCode(unitCode, bizType);
    }

    @InvokeLog(description = "查询客商对应企业服务器《商品,二级单位，场区 三者的树形结构数据》 接口")//日志
    @PostMapping(value = "/variety_code")
    public ServerResponse<Object> varietyCodeHandler(
            @RequestParam(value = "unitCode") String unitCode,              //"一级单位编号"
            @RequestParam(value = "bizContractCode") String bizContractCode //"业务合同编号"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用！");

        //return contractService.getVarietyCode(unitCode, bizContractCode);
    }

    @InvokeLog(description = "查询客商对应企业服务器《商品,二级单位，场区 三者的树形结构数据》 接口")//日志
    @PostMapping(value = "/variety_code2")
    public ServerResponse<Object> varietyCode2Handler(
            @RequestParam(value = "unitCode") String unitCode,              //"一级单位编号"
            @RequestParam(value = "bizContractCode") String bizContractCode //"业务合同编号"
    ) throws GlobalException {
        return contractService.getVarietyCode2(unitCode, bizContractCode);
    }

    @InvokeLog(description = "平台一级单位查询业务系统二级单位 接口")//日志
    @PostMapping(value = "/get_subunit_list")
    public ServerResponse<Object> getSubUnitListHandler(
            @RequestParam(value = "unitCode") String unitCode               //"一级单位编号"
    ) throws GlobalException {
        return contractService.getSubUnitList(unitCode);
    }

    @InvokeLog(description = "查询可计划抢运列表 接口")//日志
    @PostMapping(value = "/get_rp_list")
    public ServerResponse<Object> getRushPlanListHandler(
            @RequestParam(value = "unitCode") String unitCode,              //"一级单位编号"
            @RequestParam(value = "defaultDownUnit") String defaultDownUnit,//"二级单位编号"
            @RequestParam(value = "bizType") Integer bizType,               //"提货，送货"
            @RequestParam(value = "page", required = false) Integer page    //"第几页(从0开始)"
    ) throws GlobalException {
        return contractService.getRushPlanList(unitCode, defaultDownUnit, bizType, page);
    }

    @InvokeLog(description = "计划抢运 接口")//日志
    @PostMapping(value = "/add_rp")
    public ServerResponse<Object> rushPlanHandler(
            @RequestParam(value = "unitCode") String unitCode,              //"一级单位编号"
            @RequestParam(value = "id") String id,                          //"计划抢运编号"
            @RequestParam(value = "expectCarCount") Integer expectCarCount, //"车数"
            @RequestParam(value = "bizUnitCode") String bizUnitCode,        //"业务单位编码"
            @RequestParam(value = "isTakeAll") Boolean isTakeAll            //"是否接受可抢运数量的抢运"
    ) throws GlobalException {
        return contractService.addRushPlan(unitCode, id, expectCarCount, isTakeAll, bizUnitCode);
    }

    @InvokeLog(description = "计划查询品种 接口")//日志
    @PostMapping(value = "/get_variety_list")
    public ServerResponse<Object> getVarietyListHandler(
            @RequestParam(value = "unitCode") String unitCode          //"一级单位编号"
    ) throws GlobalException {
        return contractService.getVarietyList(unitCode);
    }

    @InvokeLog(description = "计划提交 接口")//日志
    @PostMapping(value = "/save_rd")
    public ServerResponse<Object> RushDetailsHandler(
            @RequestParam(value = "unitCode") String unitCode,             //"一级单位编号"
            @RequestParam(value = "defaultDownUnit") String defaultDownUnit,//"二级单位编号"
            @RequestParam(value = "bizUnitCode") String bizUnitCode,       //"业务单位编码"
            @RequestParam(value = "varietyCode") String varietyCode,       //"物料"
            @RequestParam(value = "bizType") Integer bizType,              //"提货，送货"
            @RequestParam(value = "carCount") Integer carCount,            //"车数"
            @RequestParam(value = "weightCount") Double weightCount,       //"重量"
            @RequestParam(value = "planStartTime") Long planStartTime,     //"计划开始时间"
            @RequestParam(value = "planEndTime") Long planEndTime,         //"计划结束时间"
            @RequestParam(value = "planLeadTime") Integer planLeadTime     //"计划时间"
    ) throws GlobalException {
        return contractService.saveRushDetails(unitCode, defaultDownUnit, bizUnitCode, varietyCode, bizType, carCount, weightCount, planStartTime, planEndTime, planLeadTime);
    }

    @InvokeLog(description = "获取发运计划明细信息 接口")//日志
    @PostMapping(value = "/get_rd_list")
    public ServerResponse<Object> getRushDetailsListHandler(
            @RequestParam(value = "unitCode") String unitCode,              //"一级单位编号"
            @RequestParam(value = "defaultDownUnit") String defaultDownUnit,//"二级单位编号"
            @RequestParam(value = "bizUnitCode") String bizUnitCode,        //"业务单位编码"
            @RequestParam(value = "bizType") Integer bizType,               //"提货，送货"
            @RequestParam(value = "page", required = false) Integer page    //"第几页(从0开始)"
    ) throws GlobalException {
        return contractService.getRushDetailsList(unitCode, defaultDownUnit, bizUnitCode, bizType, page);
    }

    @InvokeLog(description = "获取发运计划明细信息 接口")//日志
    @PostMapping(value = "/get_rdfrp_list")
    public ServerResponse<Object> getRushDetailsFromRushPlanHandler(
            @RequestParam(value = "unitCode") String unitCode,             //"一级单位编号"
            @RequestParam(value = "defaultDownUnit") String defaultDownUnit,//"二级单位编号"
            @RequestParam(value = "bizUnitCode") String bizUnitCode,       //"业务单位编码"
            @RequestParam(value = "bizType") Integer bizType,              //"提货，送货"
            @RequestParam(value = "page", required = false) Integer page   //"第几页(从0开始)"
    ) throws GlobalException {
        return contractService.getRushDetailsFromRushPlan(unitCode, defaultDownUnit, bizUnitCode, bizType, page);
    }

    @InvokeLog(description = "查看合同品种车数毛皮净重损益检票时间，明细 接口")//日志
    @PostMapping(value = "/get_info_from_worked")
    public ServerResponse<Object> getInfoFromWorkedHandler(
            @RequestParam(value = "unitCode") String unitCode,                  //"一级单位编号"
            @RequestParam(value = "subCode", required = false) String subCode,  //"二级单位编号"
            @RequestParam(value = "bizType") Integer bizType,                  //"提货，送货"
            @RequestParam(value = "startTime") Long startTime,                 //"开始时间"
            @RequestParam(value = "endTime") Long endTime,                     //value = "结束时间"
            @RequestParam(value = "page", required = false) Integer page       //"第几页(从0开始)"
    ) throws GlobalException {
        return contractService.getInfoFromWorked(unitCode, subCode, bizType, startTime, endTime, page);
    }

    @InvokeLog(description = "查看合同品种车数毛皮净重损益检票时间，统计 接口")//日志
    @PostMapping(value = "/get_total_info_from_worked")
    public ServerResponse<Object> getTotalInfoFromWorkedHandler(
            @RequestParam(value = "unitCode") String unitCode,                 //"一级单位编号"
            @RequestParam(value = "subCode", required = false) String subCode, //"二级单位编号"
            @RequestParam(value = "bizType") Integer bizType,                  //"提货，送货"
            @RequestParam(value = "startTime") Long startTime,                 //"开始时间"
            @RequestParam(value = "endTime") Long endTime                      //"结束时间"
    ) throws GlobalException {
        return contractService.getTotalInfoFromWorked(unitCode, subCode, bizType, startTime, endTime);
    }

    @InvokeLog(description = "查询业务单位 接口")//日志
    @PostMapping(value = "/biz_unit_code")
    public ServerResponse<Object> getInfoFromWorkedHandler(
            @RequestParam(value = "unitCode") String unitCode,                 //"一级单位编号"
            @RequestParam(value = "bizType") Integer bizType                   //"销售，采购"
    ) throws GlobalException {
        return contractService.getBizUnitCodeList(unitCode, bizType);
    }

    @InvokeLog(description = "查询业务二级单位运往地 接口")//日志
    @PostMapping(value = "/get_place_area_info")
    public ServerResponse<Object> getPlaceAreaInfoHandler(
            @RequestParam(value = "unitCode") String unitCode,                 //"一级单位编号"
            @RequestParam(value = "subCOde") String subCode,                   //"二级单位编号"
            @RequestParam(value = "spell", required = false) String spell                   //"二级单位编号"
    ) throws GlobalException {
        return contractService.getPlaceAreaInfo(unitCode, subCode, spell);
    }
}
