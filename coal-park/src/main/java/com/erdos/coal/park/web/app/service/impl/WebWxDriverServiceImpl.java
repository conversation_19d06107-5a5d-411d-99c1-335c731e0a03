package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.driver.dao.IWechatDriverInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.manage.service.ICarInfoService;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.app.service.IWebWxDriverService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Service("webWxDriverService")
public class WebWxDriverServiceImpl extends BaseMongoServiceImpl<WechatDriverInfo, IWechatDriverInfoDao> implements IWebWxDriverService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private ICarInfoService carInfoService;
    @Resource
    private IDriverToCarDao driverToCarDao;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {

        Query<WechatDriverInfo> query = this.createQuery();

        if (!StrUtil.isEmpty(request.getParameter("carNum"))) {
            query.criteria("carNum").containsIgnoreCase(request.getParameter("carNum")); //like
        }

        query.order(Sort.descending("updateTime"));

        return this.findPage(page, rows, query);
    }

    @Override
    public ServerResponse saveCheck(WechatDriverInfo wechatDriverInfo) {

        Query<WechatDriverInfo> query = this.createQuery();
        query.filter("objectId", wechatDriverInfo.getObjectId());
        query.filter("updateTime", wechatDriverInfo.getUpdateTime());

        UpdateOperations<WechatDriverInfo> updateOperations = this.createUpdateOperations().set("state", wechatDriverInfo.getState());
        String id = wechatDriverInfo.getCarInfoId();
        if (StrUtil.isNotEmpty(id)) {
            updateOperations.set("carInfoId", id);
            CarInfo carInfo = carInfoService.get("id", id);
            if (ObjectUtil.isNotNull(carInfo)) {
                updateOperations.set("axlesNumber", carInfo.getAxlesNumber());
                updateOperations.set("capacity", carInfo.getCapacity());
                updateOperations.set("carType", carInfo.getCarType());
            }
        }
        this.update(query, updateOperations);
        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public ServerResponse editWxDriver() {
        Map<String, Object> data = new HashMap<>();
        WechatDriverInfo wxDriver = this.getByPK(request.getParameter("id"));

        String drivingPho1 = fileInfoDao.readFile(wxDriver.getDrivingPho1(), "t_wechat_driver_info");//行驶证照片
        if (!StrUtil.isEmpty(drivingPho1)) {
            wxDriver.setDrivingPho1("/web" + drivingPho1);
        } else {
            wxDriver.setDrivingPho1("/static/asserts/images/mg3.png");
        }
        String drivingPho2 = fileInfoDao.readFile(wxDriver.getDrivingPho2(), "t_wechat_driver_info");//行驶证照片
        if (!StrUtil.isEmpty(drivingPho2)) {
            wxDriver.setDrivingPho2("/web" + drivingPho2);
        } else {
            wxDriver.setDrivingPho2("/static/asserts/images/mg3.png");
        }
        String driverPho = fileInfoDao.readFile(wxDriver.getDriverPho(), "t_wechat_driver_info");//驾驶证照片
        if (!StrUtil.isEmpty(driverPho)) {
            wxDriver.setDriverPho("/web" + driverPho);
        } else {
            wxDriver.setDriverPho("/static/asserts/images/mg4.png");
        }
        String carIdentityPhoBef = fileInfoDao.readFile(wxDriver.getCarIdentityPhoBef(), "t_wechat_driver_info");//车主身份证照片正面
        if (!StrUtil.isEmpty(carIdentityPhoBef)) {
            wxDriver.setCarIdentityPhoBef("/web" + carIdentityPhoBef);
        } else {
            wxDriver.setCarIdentityPhoBef("/static/asserts/images/mg1.png");
        }

        String carIdentityPhoBack = fileInfoDao.readFile(wxDriver.getCarIdentityPhoBack(), "t_wechat_driver_info");//车主身份证照片背面
        if (!StrUtil.isEmpty(carIdentityPhoBack)) {
            wxDriver.setCarIdentityPhoBack("/web" + carIdentityPhoBack);
        } else {
            wxDriver.setCarIdentityPhoBack("/static/asserts/images/mg2.png");
        }
        String driIdentityPhoBef = fileInfoDao.readFile(wxDriver.getDriIdentityPhoBef(), "t_wechat_driver_info");//司机身份证照片正面
        if (!StrUtil.isEmpty(driIdentityPhoBef)) {
            wxDriver.setDriIdentityPhoBef("/web" + driIdentityPhoBef);
        } else {
            wxDriver.setDriIdentityPhoBef("/static/asserts/images/mg1.png");
        }

        String driIdentityPhoBack = fileInfoDao.readFile(wxDriver.getDriIdentityPhoBack(), "t_wechat_driver_info");//司机身份证照片背面
        if (!StrUtil.isEmpty(driIdentityPhoBack)) {
            wxDriver.setDriIdentityPhoBack("/web" + driIdentityPhoBack);
        } else {
            wxDriver.setDriIdentityPhoBack("/static/asserts/images/mg2.png");
        }

        String driverCarPho = fileInfoDao.readFile(wxDriver.getDriverCarPho(), "t_wechat_driver_info");//司机和车的合影照片
        if (!StrUtil.isEmpty(driverCarPho)) {
            wxDriver.setDriverCarPho("/web" + driverCarPho);
        } else {
            wxDriver.setDriverCarPho("/static/asserts/images/mg5.png");
        }

        data.put("driver", wxDriver);
        return ServerResponse.createSuccess();
    }

    @Override
    public EGridResult loadDvrToCarGrid(Integer page, Integer rows) {
        String openid = request.getParameter("openid");
        String driverId = openid;
        WechatDriverInfo wechatDriverInfo = this.get("openid", openid);
        if (StrUtil.isNotEmpty(wechatDriverInfo.getDid())) driverId = wechatDriverInfo.getDid();
        Query<DriverToCar> query = driverToCarDao.createQuery();
        query.filter("driverId", driverId);
        query.order(Sort.descending("createTime"));
        return driverToCarDao.findPage(page, rows, query);
    }

    @Override
    public ServerResponse<String> updateDvrToCarDel() {
        String openid = request.getParameter("openid");
        String driverId = openid;
        WechatDriverInfo wechatDriverInfo = this.get("openid", openid);
        if (StrUtil.isNotEmpty(wechatDriverInfo.getDid())) driverId = wechatDriverInfo.getDid();

        String carNum = request.getParameter("carNum");
        int delete = Integer.valueOf(request.getParameter("delete"));
        if (delete == 0) {
            delete = 2;
        } else if (delete == 2) {
            delete = 0;
        } else if (delete == 1) return ServerResponse.createError("暂不支持修改车辆的关联状态");


        Query<DriverToCar> query = driverToCarDao.createQuery();
        query.filter("driverId", driverId);
        query.filter("carNum", carNum);
        UpdateOperations<DriverToCar> updateOperations = driverToCarDao.createUpdateOperations();
        updateOperations.set("delete", delete);
        driverToCarDao.update(query, updateOperations);

        return ServerResponse.createSuccess("修改成功");
    }
}
