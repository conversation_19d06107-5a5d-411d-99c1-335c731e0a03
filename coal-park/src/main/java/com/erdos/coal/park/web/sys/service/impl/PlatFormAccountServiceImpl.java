package com.erdos.coal.park.web.sys.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.web.sys.dao.IPlatFormAccountDao;
import com.erdos.coal.park.web.sys.entity.PlatFormAccount;
import com.erdos.coal.park.web.sys.service.IPlatFormAccountService;
import org.bson.Document;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

@Service("platFormAccountService")
public class PlatFormAccountServiceImpl extends BaseMongoServiceImpl<PlatFormAccount, IPlatFormAccountDao> implements IPlatFormAccountService {
    @Override
    public Document createPlatFormAccountDoc(String payerId, BigDecimal fee, Integer type, String oid, Date time) {
        PlatFormAccount platFormAccount = new PlatFormAccount();
        platFormAccount.setPayerId(payerId);
        platFormAccount.setType(type);
        platFormAccount.setOid(oid);
        platFormAccount.setUpdateTime(time.getTime());
        Document platFormAccountDoc = Document.parse(JSONObject.toJSONString(platFormAccount));
        platFormAccountDoc.append("totalFee", fee);
        platFormAccountDoc.append("createTime", time);
        return platFormAccountDoc;
    }

    @Override
    public Document createPlatFormAccountDoc(String payerId, BigDecimal fee, Integer type, Date time, String preferentialRefundNo) {
        PlatFormAccount platFormAccount = new PlatFormAccount();
        platFormAccount.setPayerId(payerId);
        platFormAccount.setType(type);
        platFormAccount.setUpdateTime(time.getTime());
        platFormAccount.setPreferentialRefundNo(preferentialRefundNo);
        Document platFormAccountDoc = Document.parse(JSONObject.toJSONString(platFormAccount));
        platFormAccountDoc.append("totalFee", fee);
        platFormAccountDoc.append("createTime", time);
        return platFormAccountDoc;
    }
}
