package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.web.app.service.IWebWxDriverService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/driver")
public class WebWxDriverController extends BaseController {

    @Resource
    private IWebWxDriverService webWxDriverService;

    @PostMapping("/wx_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webWxDriverService.loadGrid(page, rows));
    }

    @PostMapping("/wx_edit")
    public ServerResponse editHandler() throws GlobalException {
        return webWxDriverService.editWxDriver();
    }

    @PostMapping("/wx_saveCheck")
    public ServerResponse saveCheckHandler(@RequestBody WechatDriverInfo wechatDriverInfo) {
        return webWxDriverService.saveCheck(wechatDriverInfo);
    }

    @PostMapping("/wxdvr_to_car_list")
    public ServerResponse<EGridResult> dvrToCarlistHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webWxDriverService.loadDvrToCarGrid(page, rows));
    }

    @PostMapping("/update_wxdvr_to_car")
    public ServerResponse<String> updateDvrToCarHandler() throws GlobalException {
        return webWxDriverService.updateDvrToCarDel();
    }
}
