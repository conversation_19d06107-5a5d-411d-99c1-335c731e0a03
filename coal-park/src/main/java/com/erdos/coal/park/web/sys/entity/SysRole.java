package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "sys_role", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")}, options = @IndexOptions(unique = true))
})
public class SysRole extends BaseMongoInfo {

    //@Indexed(options = @IndexOptions(name = "_idx_role_id", unique = true, background = true))
    private String id;

    private String name;
    private Integer order = 0; //排序号

    private Integer state; //1:0

    private String[] menus; // 选中状态

    private String[] funcs; // 选中+半选

    //@Override
    public String getId() {
        return id;
    }

    //@Override
    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String[] getMenus() {
        return menus;
    }

    public void setMenus(String[] menus) {
        this.menus = menus;
    }

    public String[] getFuncs() {
        return funcs;
    }

    public void setFuncs(String[] funcs) {
        this.funcs = funcs;
    }
}
