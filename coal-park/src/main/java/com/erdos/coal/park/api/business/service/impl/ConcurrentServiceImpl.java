package com.erdos.coal.park.api.business.service.impl;

import com.erdos.coal.park.api.business.BusAsyncTask;
import com.erdos.coal.park.api.business.service.IConcurrentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.CompletableFuture;

@Service("concurrentService")
public class ConcurrentServiceImpl implements IConcurrentService {

    @Resource
    private BusAsyncTask busAsyncTask;

    @Resource
    private HttpServletRequest request;

    @Override
    public CompletableFuture<String> test1(String s) {
        String user = (String) request.getSession().getAttribute("userCode");
        String v = (String) request.getParameter("e");
        return busAsyncTask.test1(user + "=>" + v);
    }

    @Override
    public CompletableFuture<String> test2(String s) {
        return busAsyncTask.test2(s);
    }

    @Override
    public CompletableFuture<String> test3(String s) {
        return busAsyncTask.test3(s);
    }

    @Override
    public CompletableFuture<String> test4(String s) {
        return busAsyncTask.test4(s);
    }
}
