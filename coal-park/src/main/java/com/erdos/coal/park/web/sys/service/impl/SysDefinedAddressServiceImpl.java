package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.dao.IUserDefinedAddressDao;
import com.erdos.coal.park.api.customer.entity.UserDefinedAddress;
import com.erdos.coal.park.web.sys.pojo.DefinedAddressData;
import com.erdos.coal.park.web.sys.service.ISysDefinedAddressService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.geo.GeoJson;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service("definedAddressService")
public class SysDefinedAddressServiceImpl extends BaseMongoServiceImpl<UserDefinedAddress, IUserDefinedAddressDao> implements ISysDefinedAddressService {
    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        List<UserDefinedAddress> addressList = this.list(this.createQuery().order(Sort.descending("updateTime")));
        List<DefinedAddressData> dataList = new ArrayList<>();
        for (UserDefinedAddress address : addressList) {

            DefinedAddressData data = new DefinedAddressData();
            data.setId(address.getObjectId().toString());
            data.setUnitCode(address.getUnitCode());
            data.setAddName(address.getAddName());
            data.setCid(address.getCid());
            data.setFullName(address.getFullName());
            data.setCreateTime(address.getCreateTime());
            data.setUpdateTime(address.getUpdateTime());
            Point point = (Point) address.getGeometry();
            if (!StrUtil.isEmpty(point)) {
//                double[] d = CoordinateTranService.gps84_To_Gcj02(point.getLatitude(), point.getLongitude());
//                data.setCoordinates(retain6(d[1]) + "," + retain6(d[0]));
                data.setCoordinates(point.getLongitude() + "," + point.getLatitude());
            }

            dataList.add(data);
        }
        int size = dataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
//        this.findPage(page,rows,map)
        EGridResult eGridResult = new EGridResult(dataList.size(), dataList.subList(fromIndex, toIndex));
        return eGridResult;
    }

    @Override
    public DefinedAddressData getAddress() {
        UserDefinedAddress address = this.getByPK(request.getParameter("id"));
        DefinedAddressData data = new DefinedAddressData();
        data.setId(address.getObjectId().toString());
        data.setUnitCode(address.getUnitCode());
        data.setAddName(address.getAddName());
        data.setCid(address.getCid());
        data.setFullName(address.getFullName());
        Point point = (Point) address.getGeometry();
        if (!StrUtil.isEmpty(point)) {
//            double[] d = CoordinateTranService.gps84_To_Gcj02(point.getLatitude(), point.getLongitude());
//            data.setCoordinates(d[1] + "," + d[0]);
            data.setCopyCoordinates(point.getLongitude() + "," + point.getLatitude());
        }
        return data;
    }

    /**
     * 保留小数点后六位
     *
     * @param num
     * @return
     */
    private static double retain6(double num) {
        String result = String.format("%.6f", num);
        return Double.valueOf(result);
    }

    @Override
    public ServerResponse addAddress(DefinedAddressData address) {
        if (checkAddress(address)) {
            String cStr = checkByName(address.getAddName(), address.getFullName());
            if (!StrUtil.isEmpty(cStr)) {
                return ServerResponse.createError(cStr);
            }

            UserDefinedAddress definedAddress = new UserDefinedAddress();
            definedAddress.setAddName(StrUtil.isEmpty(address.getAddName()) ? address.getFullName() : address.getAddName());
            definedAddress.setFullName(address.getFullName());
            definedAddress.setUnitCode(address.getUnitCode());
            String[] s = address.getCoordinates().split(",");
            Point point = GeoJson.point(Double.valueOf(s[1]), Double.valueOf(s[0]));
            definedAddress.setGeometry(point);
            this.save(definedAddress);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse editAddress(DefinedAddressData address) {
        if (checkAddress(address) && address.getId() != null && address.getUpdateTime() != null) {
            String cStr = checkByName(address.getAddName(), address.getFullName());
            if (!StrUtil.isEmpty(cStr)) {
                return ServerResponse.createError(cStr);
            }

            Query<UserDefinedAddress> query = this.createQuery();
            query.filter("_id", new ObjectId(address.getId()));
            query.filter("updateTime", address.getUpdateTime());

            UpdateOperations<UserDefinedAddress> updateOperations = this.createUpdateOperations();
            updateOperations.set("unitCode", address.getUnitCode());
            updateOperations.set("addName", StrUtil.isEmpty(address.getAddName()) ? address.getFullName() : address.getAddName());
            updateOperations.set("fullName", address.getFullName());
            String[] s = address.getCoordinates().split(",");
            Point point = GeoJson.point(Double.valueOf(s[1]), Double.valueOf(s[0]));
            updateOperations.set("geometry", point);
            this.update(query, updateOperations);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse delAddress(DefinedAddressData address) {
        ObjectId objectId = new ObjectId(address.getId());
        UserDefinedAddress has = this.getByPK(objectId);
        if (has != null) {
            this.delete(objectId);
            return ServerResponse.createSuccess();
        }
        return ServerResponse.createError();
    }

    Boolean checkAddress(DefinedAddressData address) {
        if (address == null) return false;
//        if (address.getUnitCode() == null) return false;
        return true;
    }

    //自定义地址 按照名称判断是否重复
    private String checkByName(String addName, String fullName) {
        if (!StrUtil.isEmpty(addName)) {
            UserDefinedAddress uAdd = this.get("addName", addName);
            if (uAdd != null)
                return "自定义名称已存在，请重新命名";
        }
        UserDefinedAddress uFul = this.get("fullName", fullName);
        if (uFul != null)
            return fullName + "已经添加过地址列表";

        return null;
    }
}
