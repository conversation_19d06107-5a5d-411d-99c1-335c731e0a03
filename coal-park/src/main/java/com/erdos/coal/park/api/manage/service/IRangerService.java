package com.erdos.coal.park.api.manage.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.entity.Ranger;

public interface IRangerService extends IBaseMongoService<Ranger> {
    //用户注册 登录
    ServerResponse<AccessToken> regAndLogin(String username, String password, String code);
}
