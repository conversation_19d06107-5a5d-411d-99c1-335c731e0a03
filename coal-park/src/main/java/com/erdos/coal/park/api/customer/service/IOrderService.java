package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.pojo.OrderStatistics;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import org.bson.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public interface IOrderService extends IBaseMongoService<Order> {

    //扫码下单接口
    ServerResponse<Order> sweepCode(String mobile, String carNum, String gid, String longitude, String latitude, String fee);
    ServerResponse<Order> sweepCode2(String mobile, String carNum, String gid, String longitude, String latitude, String fee);
    ServerResponse<Order> sweepCode3(String mobile, String carNum, String gid, String longitude, String latitude, String fee);
    ServerResponse<Order> sweepCode4(String mobile, String carNum, String gid, String longitude, String latitude, String fee);

    ServerResponse<Order> sweepCode6(String mobile, String carNum, String gid, String longitude, String latitude, String fee);

    //二维码下单接口
    ServerResponse<Object> twoDimensionCode(String gid);
    ServerResponse<Object> twoDimensionCode2(String gid);

    //订单查询接口
    //ServerResponse<List<OrderInfoData>> searchOrder(Long orderTime, String endPoint, String[] finishTagArr, Integer isHand);
    ServerResponse<EGridResult> searchOrder(Long orderTime, String endPoint, String[] finishTagArr, Integer isHand, Integer page, Integer rows);
    ServerResponse<EGridResult> searchOrder2(Long orderTime, String endPoint, String[] finishTagArr, Integer isHand, Integer page, Integer rows);

    //查询货运信息下所有订单
    //ServerResponse<List<OrderInfoData>> searchGidOrder(String gid);
    ServerResponse<EGridResult> searchGidOrder(String gid, Integer page, Integer rows);
    ServerResponse<EGridResult> searchGidOrder2(String gid, Integer type, Integer page, Integer rows);
    ServerResponse<EGridResult> searchGidOrder3(String gid, Integer type, Integer page, Integer rows);

    //根据货运信息编号，查询不同类型的订单
    List<Order> searchOrderByGid(String gid, Integer type);

    //订单修改接口
    ServerResponse<List<String>> updateOrders(String gid, String[] oids, String outVariety, String inVariety);
    ServerResponse<List<String>> updateOrders2(String gid, String[] oids, String outVariety, String inVariety,String spell, String place);

    //订单修改运往地接口
    ServerResponse<List<String>> updateOrdersPlace(String gid, String[] oids, String spell, String place);

    //指定车牌号下单
    //ServerResponse<String> appointOrderCarNum(String oid, String carNum);
    ServerResponse<String> appointOrderCarNum(String oid, String carNum, String driverId);
    ServerResponse<String> appointOrderCarNum2(String oid, String carNum, String driverId);

    //手机号或车牌号查询司机集合
    ServerResponse<List<DriverToCar>> searchDvrByCarNumOrMobile(String mobile, String carNum);

    Document createPayDoc(Order order, String payerId, int cusPayeeFee, String carNum, String driverId, Date date, String transactionId, String outTradeNo);

    //订单 禁止或恢复运输
    ServerResponse<EGridResult> updateOrderIsTransport(String[] oids, Integer isTransport);

    //客商订单统计
    ServerResponse<List<OrderStatistics>> statisticsOrder(Long startTime, Long endTime);
    ServerResponse<List<OrderStatistics>> statisticsOrder2(Long startTime, Long endTime);

    ServerResponse<List<String>> updateOrdersInNetWeight(String oid, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime);

    //司机接单时执行异步任务-智慧能源-提煤单加密信息
    void supervisoryElectVoucherContPosition(Order order, DriverInfo driverInfo, String carNum, String accessCode);
    //企业检票时执行异步任务-智慧能源-提煤单状态被动变更
    void coalDeliveryNoteStatusUpdate(Order order, Integer checking);
}
