package com.erdos.coal.park.web.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverInfoDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.*;
import com.erdos.coal.park.api.driver.service.*;
import com.erdos.coal.park.api.manage.service.ICarInfoService;
import com.erdos.coal.park.api.manage.service.impl.PhotoFileServiceImpl;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.app.pojo.DriverAccountData;
import com.erdos.coal.park.web.app.service.IWebDriverService;
import com.erdos.coal.utils.StrUtil;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("webDriverService")
public class WebDriverServiceImpl extends BaseMongoServiceImpl<DriverInfo, IDriverInfoDao> implements IWebDriverService {

    @Resource
    private HttpServletRequest request;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private ICarInfoService carInfoService;
    @Resource
    private IDriverSignInService driverSignInService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private PhotoFileServiceImpl photoFileService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private IQuarantineInfoService quarantineInfoService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private ICarDao carDao;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {

        //自定义查询条件
        //coal-ds-mongo 模块中 com.erdos.coal.test.service.impl.BookServiceImplImpl
        //包含了一些常用自定义查询

        Query<DriverInfo> query = this.createQuery();
        query.criteria("state").notEqual(-1);

        if (StrUtil.isNotEmpty(request.getParameter("carId"))) {
            Car car = carDao.get("id", request.getParameter("carId"));
            DriverToCar dtc = driverToCarDao.get("carId", car.getObjectId().toHexString());
            String did = dtc.getDriverId();
            query.criteria("_id").equal(new ObjectId(did));
        } else {
            if (!StrUtil.isEmpty(request.getParameter("name"))) {
                query.criteria("name").equalIgnoreCase(request.getParameter("name"));
            }
            if (!StrUtil.isEmpty(request.getParameter("mobile"))) {
                query.criteria("mobile").equalIgnoreCase(request.getParameter("mobile"));
            }
            if (!StrUtil.isEmpty(request.getParameter("identity"))) {
                query.criteria("identity").equalIgnoreCase(request.getParameter("identity"));
            }
            if (!StrUtil.isEmpty(request.getParameter("carNum"))) {
                query.criteria("carNum").containsIgnoreCase(request.getParameter("carNum")); //like
            }
        }

        query.order(Sort.ascending("state"), Sort.descending("updateTime"), Sort.descending("createTime"));

        //return this.findPage(page, rows, query);

        EGridResult<DriverInfo> result = this.findPage(page, rows, query);
        signUrlPhoto(result);
        synCarInfo(result.getRows());
        return result;
    }

    //同步司机信息表中到车牌号对应到车辆信息数据
    private void synCarInfo(List<DriverInfo> driverInfos) {
        for (DriverInfo driverInfo : driverInfos) {
            if (StrUtil.isEmpty(driverInfo.getCarNum())) continue;
            if (StrUtil.isNotEmpty(driverInfo.getCarType())) continue;

            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("driverId", driverInfo.getObjectId().toHexString());
            dtcQuery.filter("carNum", driverInfo.getCarNum());
            DriverToCar dtc = driverToCarDao.get(dtcQuery);
            if (dtc == null) continue;
            String carId = dtc.getCarId();
            Car car = carDao.getByPK(carId);
            driverInfo.setCarType(car.getCarInfo().getCarType());
            driverInfo.setCapacity(car.getCarInfo().getCapacity());
            driverInfo.setAxlesNumber(car.getCarInfo().getAxlesNumber());

            MongoCollection<Document> driverInfoCollection = driverInfoService.getCollection();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("carType", car.getCarInfo().getCarType());
            paramMap.put("capacity", car.getCarInfo().getCapacity());
            paramMap.put("axlesNumber", car.getCarInfo().getAxlesNumber());
            Map<String, Object> params = new HashMap<>();
            params.put("$set", paramMap);
            driverInfoCollection.updateOne(new Document("_id", driverInfo.getObjectId()), BsonDocument.parse(JSON.toJSONString(params)));
        }
    }

    /**
     * 先查询本地数据库是否有照片，
     * 有则 将本地地址放入列表对象中
     * 无则 获取oss图片访问URL, 放入列表对象中
     */
    private void signUrlPhoto(EGridResult<DriverInfo> driverInfos) {
        for (DriverInfo driverInfo : driverInfos.getRows()) {
            if (StrUtil.isNotEmpty(driverInfo.getDriverPho())) {
                String filePath = photoFileService.readPhoto0(driverInfo.getDriverPho(), "t_driver_info");
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setDriverPho("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setDriverPho(photoFileService.signUrl(driverInfo.getDriverPho()));
                }
            }
            if (StrUtil.isNotEmpty(driverInfo.getDriverPho2())) {
                String filePath = photoFileService.readPhoto0(driverInfo.getDriverPho2(), "t_driver_info");
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setDriverPho2("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setDriverPho2(photoFileService.signUrl(driverInfo.getDriverPho2()));
                }
            }
            if (StrUtil.isNotEmpty(driverInfo.getDriIdentityPhoBef())) {
                String filePath = photoFileService.readPhoto0(driverInfo.getDriIdentityPhoBef(), "t_driver_info");
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setDriIdentityPhoBef("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setDriIdentityPhoBef(photoFileService.signUrl(driverInfo.getDriIdentityPhoBef()));
                }
            }
            if (StrUtil.isNotEmpty(driverInfo.getDriIdentityPhoBack())) {
                String filePath = photoFileService.readPhoto0(driverInfo.getDriIdentityPhoBack(), "t_driver_info");
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setDriIdentityPhoBack("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setDriIdentityPhoBack(photoFileService.signUrl(driverInfo.getDriIdentityPhoBack()));
                }
            }
            if (StrUtil.isNotEmpty(driverInfo.getRoadQCPho())) {
                String filePath = photoFileService.readPhoto0(driverInfo.getRoadQCPho(), "t_driver_info");
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setRoadQCPho("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setRoadQCPho(photoFileService.signUrl(driverInfo.getRoadQCPho()));
                }
            }
            if (StrUtil.isNotEmpty(driverInfo.getBankCardPho())) {
                String filePath = photoFileService.readPhoto0(driverInfo.getBankCardPho(), "t_driver_info");
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setBankCardPho("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setBankCardPho(photoFileService.signUrl(driverInfo.getBankCardPho()));
                }
            }
        }
    }

    /**
     * 把之前保存在数据库到图片转移到oss
     */
    private void putPhotoToOss(DriverInfo driver) {
        if (StrUtil.isNotEmpty(driver.getDriverPho()) && photoFileService.isOSSObjectNUll(driver.getDriverPho()))  //判断是否需要上传照片到oss
            photoFileService.putObject(driver.getDriverPho(), "t_driver_info");            //驾驶证照片

        if (StrUtil.isNotEmpty(driver.getDriverPho2()) && photoFileService.isOSSObjectNUll(driver.getDriverPho2()))
            photoFileService.putObject(driver.getDriverPho2(), "t_driver_info");            //驾驶证照片副页

        if (StrUtil.isNotEmpty(driver.getDriIdentityPhoBef()) && photoFileService.isOSSObjectNUll(driver.getDriIdentityPhoBef()))
            photoFileService.putObject(driver.getDriIdentityPhoBef(), "t_driver_info");            //司机身份证照片正面

        if (StrUtil.isNotEmpty(driver.getDriIdentityPhoBack()) && photoFileService.isOSSObjectNUll(driver.getDriIdentityPhoBack()))
            photoFileService.putObject(driver.getDriIdentityPhoBack(), "t_driver_info");            //司机身份证照片正面

        if (StrUtil.isNotEmpty(driver.getRoadQCPho()) && photoFileService.isOSSObjectNUll(driver.getRoadQCPho()))
            photoFileService.putObject(driver.getRoadQCPho(), "t_driver_info");            //道路从业资格证照片

        if (StrUtil.isNotEmpty(driver.getBankCardPho()) && photoFileService.isOSSObjectNUll(driver.getBankCardPho()))
            photoFileService.putObject(driver.getBankCardPho(), "t_driver_info");            //银行卡照片
    }

    public ServerResponse editDriver() {
        Map<String, Object> data = new HashMap<>();
        DriverInfo driver = this.getByPK(request.getParameter("id"));

        //将司机保存在本地和mongodb数据库中到照片，上传到oss
        //putPhotoToOss(driver);

        String carInfoId = driver.getCarInfoId();
        if (!StrUtil.isEmpty(carInfoId)) {
            CarInfo carInfo = carInfoService.get("id", carInfoId);
            driver.setCarType(carInfo.getCarType());
            driver.setAxlesNumber(carInfo.getAxlesNumber());
            driver.setCapacity(driver.getCapacity() == 0 ? carInfo.getCapacity() : driver.getCapacity());   //行驶证识别
        }
        data.put("driver", driver);
        return ServerResponse.createSuccess(data);
    }

    @Override
    public ServerResponse saveCheck(DriverInfo driverInfo) {
        DriverInfo oldDi = driverInfoService.getByPK(driverInfo.getObjectId());

        Query<DriverInfo> query = this.createQuery();
        query.filter("objectId", driverInfo.getObjectId());
        query.filter("updateTime", driverInfo.getUpdateTime());
        UpdateOperations<DriverInfo> updateOperations = this.createUpdateOperations();
        updateOperations.set("state", driverInfo.getState());
        String stateStr = StrUtil.isNotEmpty(driverInfo.getStateStr()) ? driverInfo.getStateStr() : " ";
        updateOperations.set("stateStr", stateStr);

        if (StrUtil.isNotEmpty(driverInfo.getName())) updateOperations.set("name", driverInfo.getName());
        if (StrUtil.isNotEmpty(driverInfo.getCarNum())) updateOperations.set("carNum", driverInfo.getCarNum());
        if (StrUtil.isNotEmpty(driverInfo.getIdentity())) updateOperations.set("identity", driverInfo.getIdentity());

        /*String id = driverInfo.getCarInfoId();
        if (StrUtil.isNotEmpty(id)) {
            updateOperations.set("carInfoId", id);
            CarInfo carInfo = carInfoService.get("id", id);
            if (ObjectUtil.isNotNull(carInfo)) {
                updateOperations.set("carType", carInfo.getCarType());
                updateOperations.set("axlesNumber", carInfo.getAxlesNumber());
                updateOperations.set("capacity", carInfo.getCapacity());
            }
        }*/

        this.update(query, updateOperations);

        if (oldDi != null && StrUtil.isNotEmpty(oldDi.getState()) && StrUtil.isNotEmpty(driverInfo.getState()) && driverInfo.getState() != 0 && !oldDi.getState().equals(driverInfo.getState())) {
            //0：未审核不可用 1：通过审核 2：冻结 3:审核未通过 4：未审核但可用
            String body = "账号：" + oldDi.getMobile() + "的用户，等待审核中，暂不可用";
            if (driverInfo.getState() == 1) {
                body = "账号：" + oldDi.getMobile() + "的用户，审核通过，可用";
            } else if (driverInfo.getState() == 2) {
                body = "账号：" + oldDi.getMobile() + "的用户，账号冻结，不可用";
            } else if (driverInfo.getState() == 3) {
                body = "账号：" + oldDi.getMobile() + "的用户，审核未通过，不可用";
            }
            Map<String, String> map = pushInfoService.weChatSendDvr(oldDi.getPhoneId(), oldDi.getMobile(), oldDi.getName(), "账号审核", driverInfo.getStateStr());
            pushInfoService.addPushInfo("司机账号审核", body, driverInfo.getObjectId().toString(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));
        }
        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public EGridResult bwlist() {
        Query<DriverInfo> query = this.createQuery();
        String[] abilityTag = {"0", "1"};
        query.filter("abilityTag in ", abilityTag);
        if (!StrUtil.isEmpty(request.getParameter("name"))) {
            query.and(
                    query.criteria("name").equal(request.getParameter("name"))
            );
        }
        if (!StrUtil.isEmpty(request.getParameter("mobile"))) {
            query.and(
                    query.criteria("mobile").equal(request.getParameter("mobile"))
            );
        }
        if (!StrUtil.isEmpty(request.getParameter("identity"))) {
            query.and(
                    query.criteria("identity").equal(request.getParameter("identity"))
            );
        }
        if (!StrUtil.isEmpty(request.getParameter("carNum"))) {
            query.and(
                    query.criteria("carNum").equal(request.getParameter("carNum"))
            );
        }
        List<DriverInfo> driverList = query.find().toList();
        return new EGridResult(driverList.size(), driverList);
    }

    @Override
    public EGridResult accList(Integer page, Integer rows) {

//        Object[] state = {0, null};
        Query<DriverInfo> query = this.createQuery();
        query.or(
                query.criteria("state").equal(1),
                query.criteria("state").equal(4)
        );

        if (StrUtil.isNotEmpty(request.getParameter("name")))
            query.filter("name", request.getParameter("name"));
        if (StrUtil.isNotEmpty(request.getParameter("mobile")))
            query.filter("mobile", request.getParameter("mobile"));

//        List<DriverInfo> driverList = query.find().toList();
        EGridResult<DriverInfo> result = driverInfoService.findPage(page, rows, query);
        List<DriverInfo> driverList = result.getRows();

        List<DriverAccountData> accountDataList = new ArrayList<>();
        for (DriverInfo driverInfo : driverList) {

            DriverAccountData accountData = new DriverAccountData();
            BigDecimal availableFee = this.getAvailableFee(driverInfo.getObjectId().toString());
            accountData.setAvailableFee(availableFee);
            accountData.setId(driverInfo.getObjectId().toString());
            accountData.setMobile(driverInfo.getMobile());
            accountData.setName(driverInfo.getName());

            accountDataList.add(accountData);
        }

        return new EGridResult(result.getTotal(), accountDataList);
        /*int size = accountDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(accountDataList.size(), accountDataList.subList(fromIndex, toIndex));*/
    }

    @Override
    public EGridResult accEdit(Integer page, Integer rows) {
        String id = request.getParameter("id");
        Map<String, Object> map = new HashMap<>();
        map.put("did", id);
        Query<DriverAccount> query = driverAccountService.createQuery().filter("did", id);
        query.order(Sort.descending("createTime"));
        List<DriverAccount> accountList = driverAccountService.list(query);
        List<DriverAccount> accounts = new ArrayList<>();
        for (DriverAccount account : accountList) {
            DriverAccount account1 = new DriverAccount();
            account1.setTotalFee(account.getTotalFee().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
            account1.setOid(account.getOid());
            account1.setDid(account.getDid());
            account1.setCreateTime(account.getCreateTime());
            account1.setType(account.getType());
            accounts.add(account1);
        }

        int size = accounts.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(accounts.size(), accounts.subList(fromIndex, toIndex));
    }

    @Override
    public EGridResult loadSignInGrid(Integer page, Integer rows) {
        String mobile = request.getParameter("mobile");

        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);

        Query<DriverSignIn> query = driverSignInService.createQuery();
        query.filter("driverId", driverInfo.getObjectId().toHexString());
        query.order(Sort.descending("createTime"));

        return driverSignInService.findPage(page, rows, query);
    }

    @Override
    public EGridResult loadDvrToCarGrid(Integer page, Integer rows) {
        String mobile = request.getParameter("mobile");
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);

        Query<DriverToCar> query = driverToCarDao.createQuery();
        query.filter("driverId", driverInfo.getObjectId().toHexString());
        query.order(Sort.descending("createTime"));
        EGridResult<DriverToCar> result = driverToCarDao.findPage(page, rows, query);
        List<DriverToCar> list = result.getRows();

        for (DriverToCar driverToCar : list) {
            /*String driverCarPho = fileInfoDao.readFile(driverToCar.getDriverCarPho(), "t_driver_to_car");
            driverToCar.setDriverCarPho(driverCarPho);*/
            if (StrUtil.isNotEmpty(driverToCar.getDriverCarPho())) {
                String filePath = photoFileService.readPhoto0(driverToCar.getDriverCarPho(), "t_driver_to_car");

                //图片上传oss
                /*if (photoFileService.isOSSObjectNUll(driverToCar.getDriverCarPho()))
                    photoFileService.putObject(driverToCar.getDriverCarPho(), "t_driver_to_car");*/

                //返回图片url给前端页面
                if (StrUtil.isNotEmpty(filePath)) {
                    driverInfo.setBankCardPho("https://heijinyun.net/web" + filePath);
                } else {
                    driverInfo.setBankCardPho(photoFileService.signUrl(driverToCar.getDriverCarPho()));
                }
            }
        }
        return result;
    }

    @Override
    public ServerResponse<String> updateDvrToCarDel() {
        String mobile = request.getParameter("mobile");
        String carNum = request.getParameter("carNum");
        int delete = Integer.valueOf(request.getParameter("delete"));
        if (delete == 0) {
            delete = 2;
        } else if (delete == 2) {
            delete = 0;
        } else if (delete == 1) return ServerResponse.createError("暂不支持修改车辆的关联状态");

        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);

        Query<DriverToCar> query = driverToCarDao.createQuery();
        query.filter("driverId", driverInfo.getObjectId().toHexString());
        query.filter("carNum", carNum);
        UpdateOperations<DriverToCar> updateOperations = driverToCarDao.createUpdateOperations();
        updateOperations.set("delete", delete);
        driverToCarDao.update(query, updateOperations);

        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public BigDecimal getAvailableFee() {
        String did = request.getParameter("did");
        return getAvailableFee(did);
    }

    private BigDecimal getAvailableFee(String did) {
        Map<String, Object> map = new HashMap<>();
        map.put("did", did);
        BigDecimal availableFee = new BigDecimal("0");

        Query<DriverAccount> query = driverAccountService.createQuery().filter("did", did);
        query.criteria("type").notEqual(8);
        query.order(Sort.descending("createTime"));
        List<DriverAccount> accountList = driverAccountService.list(query);
        for (DriverAccount account : accountList) {
            availableFee = availableFee.add(account.getTotalFee());//单位为分
        }
        availableFee = availableFee.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        return availableFee;
    }

    @Override
    public EGridResult loadGridQuarantine(Integer page, Integer rows) {
        Query<QuarantineInfo> query = quarantineInfoService.createQuery();
        String mobile = request.getParameter("mobile");
        if (StrUtil.isNotEmpty(mobile)) {
            DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
            if (driverInfo == null) {
                query.criteria("did").equal(null);
            } else {
                query.filter("did", driverInfo.getObjectId().toHexString());
            }
        }
        query.order(Sort.descending("createTime"));

        EGridResult<QuarantineInfo> result = quarantineInfoService.findPage(page, rows, query);
        List<QuarantineInfo> list = result.getRows();

        for (QuarantineInfo qua : list) {
            if (StrUtil.isNotEmpty(qua.getHealthCodePho()))
                qua.setHealthCodePho(photoFileService.signUrl(qua.getHealthCodePho()));
            if (StrUtil.isNotEmpty(qua.getTravelCardPho()))
                qua.setTravelCardPho(photoFileService.signUrl(qua.getTravelCardPho()));
            if (StrUtil.isNotEmpty(qua.getNucleicAcidPho()))
                qua.setNucleicAcidPho(photoFileService.signUrl(qua.getNucleicAcidPho()));
            if (StrUtil.isNotEmpty(qua.getVaccinationPho()))
                qua.setVaccinationPho(photoFileService.signUrl(qua.getVaccinationPho()));
            if (StrUtil.isNotEmpty(qua.getTemperaturePro()))
                qua.setTemperaturePro(photoFileService.signUrl(qua.getTemperaturePro()));
        }
        return result;
    }

    @Override
    public ServerResponse saveAudit() {
        int audit = Integer.valueOf(request.getParameter("audit"));
        if (audit == 4 && StrUtil.isEmpty(request.getParameter("auditDes")))
            return ServerResponse.createError("需填写审核不通过原因！");

        Query<QuarantineInfo> query = quarantineInfoService.createQuery();
        query.filter("id", request.getParameter("id"));

        QuarantineInfo quarantineInfo = quarantineInfoService.get(query);
        //人工审核通过后，修改orderTaking防疫申报记录
        if ("3".equals(request.getParameter("audit"))) {
            String defaultDownUnit = quarantineInfo.getDefaultDownUnit();
            Order order = orderService.get("oid", quarantineInfo.getOid());
            int isQuarantine = 0;
            if (defaultDownUnit.equals(order.getOutDefaultDownUnit())) isQuarantine = 1;
            if (defaultDownUnit.equals(order.getInDefaultDownUnit())) isQuarantine = 2;
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
            otUpdateOperations.set("isQuarantine", isQuarantine);
            Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("oid", order.getOid());
            orderTakingService.update(otQuery, otUpdateOperations);
        }

        UpdateOperations<QuarantineInfo> updateOperations = quarantineInfoService.createUpdateOperations();
        updateOperations.set("audit", request.getParameter("audit"));
        updateOperations.set("auditDes", request.getParameter("auditDes"));
        quarantineInfoService.update(query, updateOperations);

        String auditDes = "";
        if (audit == 2) auditDes = quarantineInfo.getMsg();
        if (audit == 3) auditDes = "审核通过";
        if (audit == 4) auditDes = request.getParameter("auditDes");
        DriverInfo driverInfo = quarantineInfo.getDriverInfo();
        //添加推送信息
        if (StrUtil.isNotEmpty(auditDes)) {
            Map<String, String> map = pushInfoService.weChatSendDvr(driverInfo.getPhoneId(), driverInfo.getMobile(), driverInfo.getName(), "防疫申报审核", quarantineInfo.getDefaultDownUnit() + ":" + auditDes);
            pushInfoService.addPushInfo("疫情防控", "司机订单：" + quarantineInfo.getOid() + "防疫申报审核结果(" + quarantineInfo.getDefaultDownUnit() + ")" + auditDes, quarantineInfo.getDid(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));
        }
        return ServerResponse.createSuccess("成功");
    }
}
