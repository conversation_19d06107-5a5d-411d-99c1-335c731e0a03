package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IWxPrepaidService;
import com.erdos.coal.utils.NumFmtUtil;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by LIGX on 2018/12/27.
 * 客商端接口
 */
//"客商微信充值接口"
@RestController
@RequestMapping("/api/cus/deposit")
public class DepositController extends BaseController {
    @Resource
    private IWxPrepaidService wxPrepaidService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private ICustomerUserService customerUserService;

    @InvokeLog(description = "微信请求生成支付订单-客商给司机账户充值 接口") //日志
    @PostMapping(value = "/wx_request_pay")
    public ServerResponse<Map<String, String>> wxRequestPayHandler(
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "body") String body,                          //"商品描述 APP名字-实际商品名称"
            @RequestParam(value = "totalFee") String totalFee,                  //"订单金额（元）"
            @RequestParam(value = "did") String did                             //"司机id"
    ) throws GlobalException {
        //return wxPrepaidService.wxRequestPay(body, totalFee, did, "customer");
        if (NumFmtUtil.isDouble(totalFee)) {
            DriverPool dp = driverPoolService.getByPK(did); //参数中的did实际是从客商司机池中查询所得，所以did是司机池的编号
            if (StrUtil.isEmpty(openid)) openid = customerUserService.getByPK(ShiroUtils.getUserId()).getPhoneId();
            //将接收到的 以元为单位的金额 换算成 分为单位的金额 应为微信统一下单接口金额单位就是 整型数据，且单位是分，
            int fee = new BigDecimal(totalFee).multiply(new BigDecimal(100)).intValue();
            return wxPrepaidService.weChatRequestPay(openid, body, fee, dp.getDid(), 1, null, null, null);
        } else {
            return ServerResponse.createError("totalFee 参数错误！");
        }
    }

    @InvokeLog(description = "微信订单查询  接口")
    @PostMapping(value = "/wx_query_pay")
    public ServerResponse<WxResult> wxQueryPayHandler(
            @RequestParam(value = "transactionId", required = false) String transactionId,      //"订单号 微信订单号"
            @RequestParam(value = "outTradeNo", required = false) String outTradeNo             //"商户订单号"
    ) throws GlobalException {
        /*String cid = ShiroUtils.getUserId();
        return wxPrepaidService.wxQueryPay(transactionId, outTradeNo, cid, "customer");*/
        return wxPrepaidService.weChatQueryPay(transactionId, outTradeNo);

    }

    /*
     * 3-客商给自己充值，微信请求生成支付订单
     * */
    @InvokeLog(description = "微信请求生成支付订单-客商给自己充值 接口") //日志
    @PostMapping(value = "/wx_request_pay3")
    public ServerResponse<Map<String, String>> wxRequestPay3Handler(
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "body") String body,                          //"商品描述 APP名字-实际商品名称"
            @RequestParam(value = "totalFee") String totalFee                   //"订单金额（元）"
    ) throws GlobalException {
        //return wxPrepaidService.wxRequestPay3(body, totalFee);
        if (NumFmtUtil.isDouble(totalFee)) {
            String cid = ShiroUtils.getUserId();
            if (StrUtil.isEmpty(openid)) openid = customerUserService.getByPK(ShiroUtils.getUserId()).getPhoneId();
            //将接收到的 以元为单位的金额 换算成 分为单位的金额 应为微信统一下单接口金额单位就是 整型数据，且单位是分，
            int fee = new BigDecimal(totalFee).multiply(new BigDecimal(100)).intValue();
            return wxPrepaidService.weChatRequestPay(openid, body, fee, cid, 0, null, null, null);
        } else {
            return ServerResponse.createError("totalFee参数错误");
        }
    }

    @InvokeLog(description = "微信订单查询-客商自己充值  接口")
    @PostMapping(value = "/wx_query_pay2")
    public ServerResponse<WxResult> wxQueryPay2Handler(
            @RequestParam(value = "transactionId", required = false) String transactionId,      //"订单号 微信订单号"
            @RequestParam(value = "outTradeNo", required = false) String outTradeNo             //"商户订单号"
    ) throws GlobalException {
        //return wxPrepaidService.wxQueryPay2(transactionId, outTradeNo);
        return wxPrepaidService.weChatQueryPay(transactionId, outTradeNo);
    }
}
