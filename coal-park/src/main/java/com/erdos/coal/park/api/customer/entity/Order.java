package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.customer.pojo.GOrderTaking;
import com.erdos.coal.park.api.driver.pojo.CoalTicket;
import dev.morphia.annotations.*;

import java.util.Date;
import java.util.List;

@Entity(value = "t_order", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("gid")}),
        @Index(fields = {@Field("busGid")}),
        @Index(fields = {@Field("oid")}),
        @Index(fields = {@Field("did")}),
        @Index(fields = {@Field("cid")}),
        @Index(fields = {@Field("outBillCode")}),
        @Index(fields = {@Field("inBillCode")}, options = @IndexOptions(unique = true, partialFilter = "{inBillCode:{$exists:true}}")),
        @Index(fields = {@Field("shareCid")}),
        @Index(fields = {@Field("time1")}),
        @Index(fields = {@Field("time3")}),
        @Index(fields = {@Field("time5")}),
        @Index(fields = {@Field("time6")}),
        @Index(fields = {@Field("outUnitCode")}),
        @Index(fields = {@Field("outDefaultDownUnit")}),
        @Index(fields = {@Field("inUnitCode")}),
        @Index(fields = {@Field("inDefaultDownUnit")}),
        @Index(fields = @Field("createTime"), options = @IndexOptions(name = "order_createTime_1")),
        @Index(fields = {@Field("cipcherText")})
})
public class Order extends BaseMongoInfo {
    private String oid;     //订单号
    private String carNum;  //车牌号

    //@Indexed(options = @IndexOptions(name = "_gid", background = true))
    private String gid; //货运信息编号
    private String busGid; //货运信息编号

    private String outMinMax;   //发货单位 票号区间
    private String inMinMax;    //收货单位 票号区间

    private boolean delete;   //订单废除标记
    private Integer delType;    //订单废除类型 0：客商废除，1：司机主动废除，2：指定车牌号，司机拒绝接单，3：司机同意客商废除订单， 4：企业撤单
    private Integer status;   //订单状态：0可用，1不可用
    private Integer isHand; //是否手工下单0：扫码下单 1：是（手工下单） 2：批量下单（抢单）

    private String tradeName;                       //商品名称
    private String beginPoint;                      //起点
    private String endPoint;                        //终点
    private Double weight;                          //车载重量要求
    private Double price;                           //单价
    private Double distance;                        //总里程
    private Double tolls;                           //预估过路费

    private Integer pType;          //下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单 5-企业下单

    private Integer mold;                            //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2
    private String outUnitCode;                     //发货单位编码
    private String outUnitName;                     //发货单位名称
    private String inUnitCode;                      //收货单位编码
    private String inUnitName;                      //收货单位名称
    private String outBizContractCode;             //发货单位业务合同编码
    private String outBizContractName;             //发货单位业务合同名称
    private String inBizContractCode;              //收货单位业务合同编码
    private String inBizContractName;              //收货单位业务合同名称
    private String outVariety;                      //发货单位产品名称
    private String inVariety;                       //收货单位产品名称
    private String outDefaultDownUnit;             //发货单位 默认二级单位编码
    private String outSubName;                      //发货单位 默认二级单位名称
    private String inDefaultDownUnit;              //收货单位 默认二级单位编码
    private String inSubName;                       //收货单位 默认二级单位名称
    private String outDefaultArea;                 //发货单位 默认场区编码
    private String outAreaName;                    //发货单位 默认场区名称
    private String inDefaultArea;                  //收货单位 默认场区编码
    private String inAreaName;                     //收货单位 默认场区名称

    private Integer fees = 0;    //平台收取的信息费（分）
    private String payerId; //支付信息费人的id

    private Integer feesOut = 0;    //平台收取发货单位的信息费（分）
    private String payerIdOut; //支付信息费人的id
    private Integer feesIn = 0;    //平台收取收货单位的信息费（分）
    private String payerIdIn; //支付信息费人的id
    private Integer fees1Out = 0;    //发货企业一级单位收取到的代扣费（分）
    private String payerId1Out; //支付代扣费（司机或客商）  人的id
    private Integer fees1In = 0;    //收货企业一级单位收取到的代扣费（分）
    private String payerId1In; //支付代扣费  人的id
    private Integer fees2Out = 0;    //发货企业二级单位收取到的代扣费（分）
    private String payerId2Out; //支付代扣费  人的id
    private Integer fees2In = 0;    //收货企业一级单位收取到的代扣费（分）
    private String payerId2In; //支付代扣费  人的id

    private Integer balanceFees = 0;     //客商代付平台金额不足或客商未代付，司机支付平台金额（分）
    private Integer shareFees = 0;     //友商或客商 收取的信息费（分）
    private String driverPayerId;   //司机id
    private boolean locked;     //是否锁定

    private Integer isWeChat;    //0-给app司机下单的货运信息；1-给小程序司机下单的货运信息
    private Integer isCheck;    //订单废除是否审核 0:未审核，1：通过审核，2：未通过，3：无需审核

    private String transactionId;   //微信订单号
    private String outTradeNo;  //商户订单号

    private Integer share;  //0-订单未分享给好友客商, 1-订单分享给好友客商
    private String shareCid;    //订单来自编号为cid的客商分享           2022.04.17 改为订单分享给cid客商
    private String shareGid;    //订单来自编号为gid的客商货单分享                   字段废除
    private String shareOid;    //订单来自编号为oid的客商订单分享                   字段作废

    private String outBillCode;     //发货企业票号
    private String inBillCode;      //收货企业票号
    private Integer outChecking = 0;    //发货企业系统标记订单的检票状态 ：1- 一检，2- 二检，3- 检票
    private Integer inChecking = 0;    //收货企业系统标记订单的检票状态 ：1- 一检，2- 二检，3- 检票

    //发货 或 收货 单位 checking到3-检票时，会有毛重和皮重参数要记录到订单中
    private String outGrossWeight;
    private String outTareWeight;
    private String inGrossWeight;
    private String inTareWeight;
    private Date otherOutTime;      //发货单位，checking 为 '检票' 时，记录的时间
    private Date otherInTime;       //收货单位，checking 为 '一检' 时，记录的时间
    private Double outGrossWeight7;
    private Double outTareWeight7;
    private Double inGrossWeight7;
    private Double inTareWeight7;

    private Integer isTransport;   //订单是否可运输：0-可运输，1-禁止运输

    private String callStartTime;   //企业叫号开始时间
    private String callEndTime;     //企业叫号结束时间（过期）

    private String spell;   //运往地编码
    private String place;   //运往地名称

    private CoalTicket coalTicket;
    /*    private PoundList outPoundList;
        private PoundList inPoundList;*/
    private Date time1;     //司机接单时间
    private Date time2;     //发货入场时间
    private Date time3;     //发货出场时间
    private Date time4;     //收货入场时间
    private Date time5;     //收货出场时间
    private Date time6;     //订单作废时间
    private Date time7;     //司机点击卸货按钮时间
    private String outTradingUnit;      //发货交易单位编号
    private String outTradingUnitName;      //发货交易单位名称
    private String outInspector;        //发货质检员
    private String outBusOrderNo;       //发货业务单号
    private Double outWeight;       //发货入场重量
    private String inTradingUnit;       //收货交易单位编号
    private String inTradingUnitName;       //收货交易单位名称
    private String inInspector;         //收货质检员
    private String inBusOrderNo;        //收货业务单号
    private Double inWeight;        //收货入场重量
    //费用
    private Integer outIsCharge;   //是否收费（0-不收费，1-收费）
    private Integer inIsCharge;   //是否收费（0-不收费，1-收费）
    private Integer outFee0;     //平台收费（分）
    private Integer inFee0;      //平台收费（分）
    private Integer outFee1;     //一级单位收费（分）
    private Integer inFee1;      //一级单位收费（分）
    private Integer outFee2;     //二级单位收费（分）
    private Integer inFee2;      //二级单位收费（分）
    private Integer outFee3;     //客商收费（分）
    private Integer inFee3;      //客商收费（分）
    private Integer outFee4;     //友商收费（分）
    private Integer inFee4;      //友商收费（分）
    private Integer outFee5;     //第三方收费（分）
    private Integer inFee5;      //第三方收费（分）
    private Integer outFee6;     //服务费（分）
    private Integer inFee6;      //服务费（分）
    private Integer outFee7;     //特权费（分）
    private Integer inFee7;      //特权费（分）
    private String outPayer1;        //付款人（平台收费项目）
    private String outPayer2;        //付款人（一二级单位收费项目）
    private String outPayer3;        //付款人（客商或友商 和 服务特权费项目）
    private String outPayerType1;    //付款人类型，CU-客商，DU-司机，SU-单位
    private String outPayerType2;    //付款人类型，CU-客商，DU-司机
    private String inPayer1;         //付款人（平台收费项目）
    private String inPayer2;         //付款人（一二级单位收费项目）
    private String inPayer3;         //付款人（客商或友商 和 服务特权费项目）
    private String inPayerType1;     //付款人类型，CU-客商，DU-司机，SU-单位
    private String inPayerType2;     //付款人类型，CU-客商，DU-司机
    private Integer outCount6;   //服务次数
    private Integer outCount7;   //特权次数
    private Integer inCount6;    //服务次数
    private Integer inCount7;    //特权次数

    private String did;
    private String cid;
    private Integer tranStatus; //订单状态：0-未接单，1-已接单，2-发货入场，3-运输中，4-收货入场，5-订单完成，6-订单废除,7-司机拒绝订单
    /*
     * 下单后，订单为未接单状态，（若指定车号下单，则为已接单）
     * 发货业务 ，outchecking=1，为发货入场；outchecking=3，为订单完成
     * 收货业务 ，inchecking=1，为收货入场； inchecking=3，为订单完成
     * 收发业务 ，outchecking=1，为发货入场；outchecking=3，为运输途中；inchecking=1，为收货入场；inchecking=3为订单完成
     * */


    private String outCusDesc;       //发货单位客商备注
    private String inCusDesc;       //收货单位客商备注

    private Integer mold2;          //是否往货业务，空或0-否，1-是
    private String beginDistrictCode;//起点区划编码
    private String endDistrictCode; //终点区划编码

    private String outVarietyCode;  // 智慧能源-合同备案中的煤种ID
    private String cipcherText;     // 智慧能源-提煤单加密串

    private Double inNetWeight;         // 采购业务需要录入对方的净重
    private String inNetWeightPerson;   //采购业务录入对方净重值的人（客商 或 司机）
    private String inNetWeightPho;      // 采购业务需要录入对方的净重（司机录入时，需上传照片）

    private String loadPound;           // 采购业务需要录入对方的装货单号
    private String loadPoundPerson;     //采购业务录入对方装货单号的人（客商 或 司机）

    private Long loadTime;              // 采购业务需要录入对方的装货时间
    private String loadTimePerson;      //采购业务录入对方装货时间的人（客商 或 司机）

    private String loadPoundPho;        // 采购业务需要上传对方的装货单照片
    private String loadPoundPhoPerson;  //采购业务上传对方的装货单照片的人（客商 或 司机）

    private String vehicleNameOutDvr;       // 发货装卸费车型（司机选择）
    private String vehicleCodeOutDvr;       // 发货装卸费车型（司机选择）
    private Integer handlingCostOutDvr;     // 发货装卸费（司机选择交费）
    private boolean pay1;       //是否支付成功
    private Integer handlingCostOutDvrSupplementary;     // 发货装卸费（司机补费）
    private boolean pay2;       //是否支付成功
    private String vehicleNameInDvr;        // 收货装卸费车型（司机选择）
    private String vehicleCodeInDvr;        // 收货装卸费车型（司机选择）
    private Integer handlingCostInDvr;      // 收货装卸费（司机选择交费）
    private boolean pay3;       //是否支付成功
    private Integer handlingCostInDvrSupplementary;      // 收货装卸费（司机补费）
    private boolean pay4;       //是否支付成功
    private String vehicleNameOutAPP;       // 发货装卸费车型（厂区确认）
    private String vehicleCodeOutAPP;       // 发货装卸费车型（厂区确认）
    private Integer handlingCostOutAPP;     // 发货装卸费（厂区确认）
    private String vehicleNameInAPP;        // 收货装卸费车型（厂区确认）
    private String vehicleCodeInAPP;        // 收货装卸费车型（厂区确认）
    private Integer handlingCostInAPP;      // 收货装卸费（厂区确认）

    // 2024-12-28 急加
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;

    // 2025-06-05 订单离场状态 null或0-未离场，1-离场
    private String outIsExit;
    private String inIsExit;

    public Integer getMold2() {
        return mold2;
    }

    public void setMold2(Integer mold2) {
        this.mold2 = mold2;
    }

    public String getBeginDistrictCode() {
        return beginDistrictCode;
    }

    public void setBeginDistrictCode(String beginDistrictCode) {
        this.beginDistrictCode = beginDistrictCode;
    }

    public String getEndDistrictCode() {
        return endDistrictCode;
    }

    public void setEndDistrictCode(String endDistrictCode) {
        this.endDistrictCode = endDistrictCode;
    }

    public String getOutCusDesc() {
        return outCusDesc;
    }

    public void setOutCusDesc(String outCusDesc) {
        this.outCusDesc = outCusDesc;
    }

    public String getInCusDesc() {
        return inCusDesc;
    }

    public void setInCusDesc(String inCusDesc) {
        this.inCusDesc = inCusDesc;
    }

    private List<GOrderTaking> orderTakings;

    public List<GOrderTaking> getOrderTakings() {
        return orderTakings;
    }

    public void setOrderTakings(List<GOrderTaking> orderTakings) {
        this.orderTakings = orderTakings;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Integer getTranStatus() {
        return tranStatus;
    }

    public void setTranStatus(Integer tranStatus) {
        this.tranStatus = tranStatus;
    }

    public CoalTicket getCoalTicket() {
        return coalTicket;
    }

    public void setCoalTicket(CoalTicket coalTicket) {
        this.coalTicket = coalTicket;
    }

    public String getBusGid() {
        return busGid;
    }

    public void setBusGid(String busGid) {
        this.busGid = busGid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getOutMinMax() {
        return outMinMax;
    }

    public void setOutMinMax(String outMinMax) {
        this.outMinMax = outMinMax;
    }

    public String getInMinMax() {
        return inMinMax;
    }

    public void setInMinMax(String inMinMax) {
        this.inMinMax = inMinMax;
    }

    public boolean isDelete() {
        return delete;
    }

    public boolean getDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }

    public Integer getDelType() {
        return delType;
    }

    public void setDelType(Integer delType) {
        this.delType = delType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsHand() {
        return isHand;
    }

    public void setIsHand(Integer isHand) {
        this.isHand = isHand;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getOutDefaultArea() {
        return outDefaultArea;
    }

    public void setOutDefaultArea(String outDefaultArea) {
        this.outDefaultArea = outDefaultArea;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getInDefaultArea() {
        return inDefaultArea;
    }

    public void setInDefaultArea(String inDefaultArea) {
        this.inDefaultArea = inDefaultArea;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public Integer getFees() {
        return fees;
    }

    public void setFees(Integer fees) {
        this.fees = fees;
    }

    public Integer getFeesOut() {
        return feesOut;
    }

    public void setFeesOut(Integer feesOut) {
        this.feesOut = feesOut;
    }

    public String getPayerIdOut() {
        return payerIdOut;
    }

    public void setPayerIdOut(String payerIdOut) {
        this.payerIdOut = payerIdOut;
    }

    public Integer getFeesIn() {
        return feesIn;
    }

    public void setFeesIn(Integer feesIn) {
        this.feesIn = feesIn;
    }

    public String getPayerIdIn() {
        return payerIdIn;
    }

    public void setPayerIdIn(String payerIdIn) {
        this.payerIdIn = payerIdIn;
    }

    public Integer getFees1Out() {
        return fees1Out;
    }

    public void setFees1Out(Integer fees1Out) {
        this.fees1Out = fees1Out;
    }

    public String getPayerId1Out() {
        return payerId1Out;
    }

    public void setPayerId1Out(String payerId1Out) {
        this.payerId1Out = payerId1Out;
    }

    public Integer getFees1In() {
        return fees1In;
    }

    public void setFees1In(Integer fees1In) {
        this.fees1In = fees1In;
    }

    public String getPayerId1In() {
        return payerId1In;
    }

    public void setPayerId1In(String payerId1In) {
        this.payerId1In = payerId1In;
    }

    public Integer getFees2Out() {
        return fees2Out;
    }

    public void setFees2Out(Integer fees2Out) {
        this.fees2Out = fees2Out;
    }

    public String getPayerId2Out() {
        return payerId2Out;
    }

    public void setPayerId2Out(String payerId2Out) {
        this.payerId2Out = payerId2Out;
    }

    public Integer getFees2In() {
        return fees2In;
    }

    public void setFees2In(Integer fees2In) {
        this.fees2In = fees2In;
    }

    public String getPayerId2In() {
        return payerId2In;
    }

    public void setPayerId2In(String payerId2In) {
        this.payerId2In = payerId2In;
    }

    public Integer getBalanceFees() {
        return balanceFees;
    }

    public void setBalanceFees(Integer balanceFees) {
        this.balanceFees = balanceFees;
    }

    public Integer getShareFees() {
        return shareFees;
    }

    public void setShareFees(Integer shareFees) {
        this.shareFees = shareFees;
    }

    public String getDriverPayerId() {
        return driverPayerId;
    }

    public void setDriverPayerId(String driverPayerId) {
        this.driverPayerId = driverPayerId;
    }

    public Integer getIsWeChat() {
        return isWeChat;
    }

    public void setIsWeChat(Integer isWeChat) {
        this.isWeChat = isWeChat;
    }

    public boolean isLocked() {
        return locked;
    }

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    public Integer getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(Integer isCheck) {
        this.isCheck = isCheck;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getShareCid() {
        return shareCid;
    }

    public void setShareCid(String shareCid) {
        this.shareCid = shareCid;
    }

    public String getShareGid() {
        return shareGid;
    }

    public void setShareGid(String shareGid) {
        this.shareGid = shareGid;
    }

    public String getShareOid() {
        return shareOid;
    }

    public void setShareOid(String shareOid) {
        this.shareOid = shareOid;
    }

    public String getOutBillCode() {
        return outBillCode;
    }

    public void setOutBillCode(String outBillCode) {
        this.outBillCode = outBillCode;
    }

    public String getInBillCode() {
        return inBillCode;
    }

    public void setInBillCode(String inBillCode) {
        this.inBillCode = inBillCode;
    }

    public Integer getOutChecking() {
        return outChecking;
    }

    public void setOutChecking(Integer outChecking) {
        this.outChecking = outChecking;
    }

    public Integer getInChecking() {
        return inChecking;
    }

    public void setInChecking(Integer inChecking) {
        this.inChecking = inChecking;
    }

    public String getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(String outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public String getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(String outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public String getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(String inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public String getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(String inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    public Double getOutGrossWeight7() {
        return outGrossWeight7;
    }

    public void setOutGrossWeight7(Double outGrossWeight7) {
        this.outGrossWeight7 = outGrossWeight7;
    }

    public Double getOutTareWeight7() {
        return outTareWeight7;
    }

    public void setOutTareWeight7(Double outTareWeight7) {
        this.outTareWeight7 = outTareWeight7;
    }

    public Double getInGrossWeight7() {
        return inGrossWeight7;
    }

    public void setInGrossWeight7(Double inGrossWeight7) {
        this.inGrossWeight7 = inGrossWeight7;
    }

    public Double getInTareWeight7() {
        return inTareWeight7;
    }

    public void setInTareWeight7(Double inTareWeight7) {
        this.inTareWeight7 = inTareWeight7;
    }

    public Integer getpType() {
        return pType;
    }

    public void setpType(Integer pType) {
        this.pType = pType;
    }

    public Date getOtherOutTime() {
        return otherOutTime;
    }

    public void setOtherOutTime(Date otherOutTime) {
        this.otherOutTime = otherOutTime;
    }

    public Date getOtherInTime() {
        return otherInTime;
    }

    public void setOtherInTime(Date otherInTime) {
        this.otherInTime = otherInTime;
    }

    public Integer getIsTransport() {
        return isTransport;
    }

    public void setIsTransport(Integer isTransport) {
        this.isTransport = isTransport;
    }

    public String getCallStartTime() {
        return callStartTime;
    }

    public void setCallStartTime(String callStartTime) {
        this.callStartTime = callStartTime;
    }

    public String getCallEndTime() {
        return callEndTime;
    }

    public void setCallEndTime(String callEndTime) {
        this.callEndTime = callEndTime;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public Integer getOutIsCharge() {
        return outIsCharge;
    }

    public void setOutIsCharge(Integer outIsCharge) {
        this.outIsCharge = outIsCharge;
    }

    public Integer getInIsCharge() {
        return inIsCharge;
    }

    public void setInIsCharge(Integer inIsCharge) {
        this.inIsCharge = inIsCharge;
    }

    public Integer getOutFee0() {
        return outFee0;
    }

    public void setOutFee0(Integer outFee0) {
        this.outFee0 = outFee0;
    }

    public Integer getInFee0() {
        return inFee0;
    }

    public void setInFee0(Integer inFee0) {
        this.inFee0 = inFee0;
    }

    public Integer getOutFee1() {
        return outFee1;
    }

    public void setOutFee1(Integer outFee1) {
        this.outFee1 = outFee1;
    }

    public Integer getInFee1() {
        return inFee1;
    }

    public void setInFee1(Integer inFee1) {
        this.inFee1 = inFee1;
    }

    public Integer getOutFee2() {
        return outFee2;
    }

    public void setOutFee2(Integer outFee2) {
        this.outFee2 = outFee2;
    }

    public Integer getInFee2() {
        return inFee2;
    }

    public void setInFee2(Integer inFee2) {
        this.inFee2 = inFee2;
    }

    public Integer getOutFee3() {
        return outFee3;
    }

    public void setOutFee3(Integer outFee3) {
        this.outFee3 = outFee3;
    }

    public Integer getInFee3() {
        return inFee3;
    }

    public void setInFee3(Integer inFee3) {
        this.inFee3 = inFee3;
    }

    public Integer getOutFee4() {
        return outFee4;
    }

    public void setOutFee4(Integer outFee4) {
        this.outFee4 = outFee4;
    }

    public Integer getInFee4() {
        return inFee4;
    }

    public void setInFee4(Integer inFee4) {
        this.inFee4 = inFee4;
    }

    public Integer getOutFee5() {
        return outFee5;
    }

    public void setOutFee5(Integer outFee5) {
        this.outFee5 = outFee5;
    }

    public Integer getInFee5() {
        return inFee5;
    }

    public void setInFee5(Integer inFee5) {
        this.inFee5 = inFee5;
    }

    public Integer getOutFee6() {
        return outFee6;
    }

    public void setOutFee6(Integer outFee6) {
        this.outFee6 = outFee6;
    }

    public Integer getInFee6() {
        return inFee6;
    }

    public void setInFee6(Integer inFee6) {
        this.inFee6 = inFee6;
    }

    public Integer getOutFee7() {
        return outFee7;
    }

    public void setOutFee7(Integer outFee7) {
        this.outFee7 = outFee7;
    }

    public Integer getInFee7() {
        return inFee7;
    }

    public void setInFee7(Integer inFee7) {
        this.inFee7 = inFee7;
    }

    public String getOutPayer1() {
        return outPayer1;
    }

    public void setOutPayer1(String outPayer1) {
        this.outPayer1 = outPayer1;
    }

    public String getOutPayer2() {
        return outPayer2;
    }

    public void setOutPayer2(String outPayer2) {
        this.outPayer2 = outPayer2;
    }

    public String getOutPayerType1() {
        return outPayerType1;
    }

    public void setOutPayerType1(String outPayerType1) {
        this.outPayerType1 = outPayerType1;
    }

    public String getOutPayerType2() {
        return outPayerType2;
    }

    public void setOutPayerType2(String outPayerType2) {
        this.outPayerType2 = outPayerType2;
    }

    public String getInPayer1() {
        return inPayer1;
    }

    public void setInPayer1(String inPayer1) {
        this.inPayer1 = inPayer1;
    }

    public String getInPayer2() {
        return inPayer2;
    }

    public void setInPayer2(String inPayer2) {
        this.inPayer2 = inPayer2;
    }

    public String getInPayerType1() {
        return inPayerType1;
    }

    public void setInPayerType1(String inPayerType1) {
        this.inPayerType1 = inPayerType1;
    }

    public String getInPayerType2() {
        return inPayerType2;
    }

    public void setInPayerType2(String inPayerType2) {
        this.inPayerType2 = inPayerType2;
    }

    public Integer getOutCount6() {
        return outCount6;
    }

    public void setOutCount6(Integer outCount6) {
        this.outCount6 = outCount6;
    }

    public Integer getOutCount7() {
        return outCount7;
    }

    public void setOutCount7(Integer outCount7) {
        this.outCount7 = outCount7;
    }

    public Integer getInCount6() {
        return inCount6;
    }

    public void setInCount6(Integer inCount6) {
        this.inCount6 = inCount6;
    }

    public Integer getInCount7() {
        return inCount7;
    }

    public void setInCount7(Integer inCount7) {
        this.inCount7 = inCount7;
    }

    public String getOutPayer3() {
        return outPayer3;
    }

    public void setOutPayer3(String outPayer3) {
        this.outPayer3 = outPayer3;
    }

    public String getInPayer3() {
        return inPayer3;
    }

    public void setInPayer3(String inPayer3) {
        this.inPayer3 = inPayer3;
    }

    public Date getTime1() {
        return time1;
    }

    public void setTime1(Date time1) {
        this.time1 = time1;
    }

    public Date getTime2() {
        return time2;
    }

    public void setTime2(Date time2) {
        this.time2 = time2;
    }

    public Date getTime3() {
        return time3;
    }

    public void setTime3(Date time3) {
        this.time3 = time3;
    }

    public Date getTime4() {
        return time4;
    }

    public void setTime4(Date time4) {
        this.time4 = time4;
    }

    public Date getTime5() {
        return time5;
    }

    public void setTime5(Date time5) {
        this.time5 = time5;
    }

    public Date getTime6() {
        return time6;
    }

    public void setTime6(Date time6) {
        this.time6 = time6;
    }

    public Date getTime7() {
        return time7;
    }

    public void setTime7(Date time7) {
        this.time7 = time7;
    }

    public String getOutTradingUnit() {
        return outTradingUnit;
    }

    public void setOutTradingUnit(String outTradingUnit) {
        this.outTradingUnit = outTradingUnit;
    }

    public String getOutTradingUnitName() {
        return outTradingUnitName;
    }

    public void setOutTradingUnitName(String outTradingUnitName) {
        this.outTradingUnitName = outTradingUnitName;
    }

    public String getOutInspector() {
        return outInspector;
    }

    public void setOutInspector(String outInspector) {
        this.outInspector = outInspector;
    }

    public String getOutBusOrderNo() {
        return outBusOrderNo;
    }

    public void setOutBusOrderNo(String outBusOrderNo) {
        this.outBusOrderNo = outBusOrderNo;
    }

    public Double getOutWeight() {
        return outWeight;
    }

    public void setOutWeight(Double outWeight) {
        this.outWeight = outWeight;
    }

    public String getInTradingUnit() {
        return inTradingUnit;
    }

    public void setInTradingUnit(String inTradingUnit) {
        this.inTradingUnit = inTradingUnit;
    }

    public String getInTradingUnitName() {
        return inTradingUnitName;
    }

    public void setInTradingUnitName(String inTradingUnitName) {
        this.inTradingUnitName = inTradingUnitName;
    }

    public String getInInspector() {
        return inInspector;
    }

    public void setInInspector(String inInspector) {
        this.inInspector = inInspector;
    }

    public String getInBusOrderNo() {
        return inBusOrderNo;
    }

    public void setInBusOrderNo(String inBusOrderNo) {
        this.inBusOrderNo = inBusOrderNo;
    }

    public Double getInWeight() {
        return inWeight;
    }

    public void setInWeight(Double inWeight) {
        this.inWeight = inWeight;
    }

    public String getOutVarietyCode() {
        return outVarietyCode;
    }

    public void setOutVarietyCode(String outVarietyCode) {
        this.outVarietyCode = outVarietyCode;
    }

    public String getCipcherText() {
        return cipcherText;
    }

    public void setCipcherText(String cipcherText) {
        this.cipcherText = cipcherText;
    }

    public Double getInNetWeight() {
        return inNetWeight;
    }

    public void setInNetWeight(Double inNetWeight) {
        this.inNetWeight = inNetWeight;
    }

    public String getInNetWeightPerson() {
        return inNetWeightPerson;
    }

    public void setInNetWeightPerson(String inNetWeightPerson) {
        this.inNetWeightPerson = inNetWeightPerson;
    }

    public String getInNetWeightPho() {
        return inNetWeightPho;
    }

    public void setInNetWeightPho(String inNetWeightPho) {
        this.inNetWeightPho = inNetWeightPho;
    }

    public String getLoadPound() {
        return loadPound;
    }

    public void setLoadPound(String loadPound) {
        this.loadPound = loadPound;
    }

    public String getLoadPoundPerson() {
        return loadPoundPerson;
    }

    public void setLoadPoundPerson(String loadPoundPerson) {
        this.loadPoundPerson = loadPoundPerson;
    }

    public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }

    public String getLoadTimePerson() {
        return loadTimePerson;
    }

    public void setLoadTimePerson(String loadTimePerson) {
        this.loadTimePerson = loadTimePerson;
    }

    public String getLoadPoundPho() {
        return loadPoundPho;
    }

    public void setLoadPoundPho(String loadPoundPho) {
        this.loadPoundPho = loadPoundPho;
    }

    public String getLoadPoundPhoPerson() {
        return loadPoundPhoPerson;
    }

    public void setLoadPoundPhoPerson(String loadPoundPhoPerson) {
        this.loadPoundPhoPerson = loadPoundPhoPerson;
    }

    public String getVehicleNameOutDvr() {
        return vehicleNameOutDvr;
    }

    public void setVehicleNameOutDvr(String vehicleNameOutDvr) {
        this.vehicleNameOutDvr = vehicleNameOutDvr;
    }

    public Integer getHandlingCostOutDvr() {
        return handlingCostOutDvr;
    }

    public void setHandlingCostOutDvr(Integer handlingCostOutDvr) {
        this.handlingCostOutDvr = handlingCostOutDvr;
    }

    public Integer getHandlingCostOutDvrSupplementary() {
        return handlingCostOutDvrSupplementary;
    }

    public void setHandlingCostOutDvrSupplementary(Integer handlingCostOutDvrSupplementary) {
        this.handlingCostOutDvrSupplementary = handlingCostOutDvrSupplementary;
    }

    public String getVehicleNameInDvr() {
        return vehicleNameInDvr;
    }

    public void setVehicleNameInDvr(String vehicleNameInDvr) {
        this.vehicleNameInDvr = vehicleNameInDvr;
    }

    public Integer getHandlingCostInDvr() {
        return handlingCostInDvr;
    }

    public void setHandlingCostInDvr(Integer handlingCostInDvr) {
        this.handlingCostInDvr = handlingCostInDvr;
    }

    public Integer getHandlingCostInDvrSupplementary() {
        return handlingCostInDvrSupplementary;
    }

    public void setHandlingCostInDvrSupplementary(Integer handlingCostInDvrSupplementary) {
        this.handlingCostInDvrSupplementary = handlingCostInDvrSupplementary;
    }

    public String getVehicleNameOutAPP() {
        return vehicleNameOutAPP;
    }

    public void setVehicleNameOutAPP(String vehicleNameOutAPP) {
        this.vehicleNameOutAPP = vehicleNameOutAPP;
    }

    public Integer getHandlingCostOutAPP() {
        return handlingCostOutAPP;
    }

    public void setHandlingCostOutAPP(Integer handlingCostOutAPP) {
        this.handlingCostOutAPP = handlingCostOutAPP;
    }

    public String getVehicleNameInAPP() {
        return vehicleNameInAPP;
    }

    public void setVehicleNameInAPP(String vehicleNameInAPP) {
        this.vehicleNameInAPP = vehicleNameInAPP;
    }

    public Integer getHandlingCostInAPP() {
        return handlingCostInAPP;
    }

    public void setHandlingCostInAPP(Integer handlingCostInAPP) {
        this.handlingCostInAPP = handlingCostInAPP;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    public boolean isPay1() {
        return pay1;
    }

    public boolean getPay1() {
        return pay1;
    }

    public void setPay1(boolean pay1) {
        this.pay1 = pay1;
    }

    public boolean isPay2() {
        return pay2;
    }

    public boolean getPay2() {
        return pay2;
    }

    public void setPay2(boolean pay2) {
        this.pay2 = pay2;
    }

    public boolean isPay3() {
        return pay3;
    }

    public boolean getPay3() {
        return pay3;
    }

    public void setPay3(boolean pay3) {
        this.pay3 = pay3;
    }

    public boolean isPay4() {
        return pay4;
    }

    public boolean getPay4() {
        return pay4;
    }

    public void setPay4(boolean pay4) {
        this.pay4 = pay4;
    }

    public String getVehicleCodeOutDvr() {
        return vehicleCodeOutDvr;
    }

    public void setVehicleCodeOutDvr(String vehicleCodeOutDvr) {
        this.vehicleCodeOutDvr = vehicleCodeOutDvr;
    }

    public String getVehicleCodeInDvr() {
        return vehicleCodeInDvr;
    }

    public void setVehicleCodeInDvr(String vehicleCodeInDvr) {
        this.vehicleCodeInDvr = vehicleCodeInDvr;
    }

    public String getVehicleCodeOutAPP() {
        return vehicleCodeOutAPP;
    }

    public void setVehicleCodeOutAPP(String vehicleCodeOutAPP) {
        this.vehicleCodeOutAPP = vehicleCodeOutAPP;
    }

    public String getVehicleCodeInAPP() {
        return vehicleCodeInAPP;
    }

    public void setVehicleCodeInAPP(String vehicleCodeInAPP) {
        this.vehicleCodeInAPP = vehicleCodeInAPP;
    }

    public String getOutIsExit() {
        return outIsExit;
    }

    public void setOutIsExit(String outIsExit) {
        this.outIsExit = outIsExit;
    }

    public String getInIsExit() {
        return inIsExit;
    }

    public void setInIsExit(String inIsExit) {
        this.inIsExit = inIsExit;
    }
}
