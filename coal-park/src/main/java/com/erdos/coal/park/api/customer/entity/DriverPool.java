package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_driver_pool", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("cid")})
})
public class DriverPool extends BaseMongoInfo {
    private String cid; //客商编号
    private String did;    //司机编号
    private Integer whiteOrBlack;   //0：黑名单，1：白名单

    @Reference(value = "driverInfoID", lazy = true, idOnly = true, ignoreMissing = true)
    private DriverInfo driverInfo;

    @Reference(value = "customerUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser;

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public Integer getWhiteOrBlack() {
        return whiteOrBlack;
    }

    public void setWhiteOrBlack(Integer whiteOrBlack) {
        this.whiteOrBlack = whiteOrBlack;
    }

    public DriverInfo getDriverInfo() {
        return driverInfo;
    }

    public void setDriverInfo(DriverInfo driverInfo) {
        this.driverInfo = driverInfo;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }
}
