package com.erdos.coal.park.api.manage.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

@Entity(value = "t_owner_appeal", noClassnameStored = true)
public class OwnerAppeal extends BaseMongoInfo {

    private String id;
    private String cardBef;//车主身份证照片正面
    private String cardBack;//车主身份证反面
    private String ownerCard;//车主和身份证合照
    private String license;//行驶证照片
    private String mobile;//手机号
    private Integer check;        //审核是否通过 0：未通过 1：通过
    private String identity;//身份证号
    private String carNum;  //车牌号
    private String reason;  //申诉理由
    private Integer appOrWx;    //申诉 App：0  微信小程序：1

    //@Override
    public String getId() {
        return id;
    }

    //@Override
    public void setId(String id) {
        this.id = id;
    }

    public String getCardBef() {
        return cardBef;
    }

    public void setCardBef(String cardBef) {
        this.cardBef = cardBef;
    }

    public String getCardBack() {
        return cardBack;
    }

    public void setCardBack(String cardBack) {
        this.cardBack = cardBack;
    }

    public String getOwnerCard() {
        return ownerCard;
    }

    public void setOwnerCard(String ownerCard) {
        this.ownerCard = ownerCard;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getCheck() {
        return check;
    }

    public void setCheck(Integer check) {
        this.check = check;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getAppOrWx() {
        return appOrWx;
    }

    public void setAppOrWx(Integer appOrWx) {
        this.appOrWx = appOrWx;
    }
}
