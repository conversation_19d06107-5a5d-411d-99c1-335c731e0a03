package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.app.dao.IWebCarInfoDao;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.app.service.IWebCarInfoService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service("webCarTypeService")
public class WebCarInfoServiceImpl extends BaseMongoServiceImpl<CarInfo, IWebCarInfoDao> implements IWebCarInfoService {

    @Resource
    private HttpServletRequest request;
    @Resource
    private ICarDao carDao;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private IDriverInfoService driverInfoService;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        Query<CarInfo> query = this.createQuery();
        return this.findPage(page, rows, query);
    }

    @Override
    public ServerResponse addCarInfo(CarInfo carInfo) {
        if (checkCarType(carInfo)) {
            carInfo.setId(Utils.getUUID());
            this.save(carInfo);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse editCarInfo(CarInfo carInfo) {
        if (checkCarType(carInfo)) {// condition
            Query<CarInfo> condition = this.createQuery();
            condition.filter("id", carInfo.getId());
            condition.filter("updateTime", carInfo.getUpdateTime());

            //value
            UpdateOperations<CarInfo> value = this.createUpdateOperations();
            value.set("carType", carInfo.getCarType());
            value.set("carTypeNum", carInfo.getCarTypeNum());
            value.set("capacity", carInfo.getCapacity());
            value.set("axlesNumber", carInfo.getAxlesNumber());

            this.update(condition, value);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse delCarInfo(CarInfo carInfo) {
        if (checkCarType(carInfo)) {// condition
            this.delete("id", carInfo.getId());

            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    boolean checkCarType(CarInfo carInfo) {
        if (carInfo == null) return false;

        if (carInfo.getCarType() == null) return false;
        if (carInfo.getCarTypeNum() == null) return false;
        if (carInfo.getCapacity() == null) return false;
        if (carInfo.getAxlesNumber() == null) return false;
        // ...

        return true;
    }

    @Override
    public EGridResult carsLoadGrid(Integer page, Integer rows) {
        Query<Car> query = carDao.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("carNum"))) {
            query.criteria("carNum").containsIgnoreCase(request.getParameter("carNum")); //like
        }
        query.order(Sort.ascending("verify"), Sort.descending("updateTime"), Sort.descending("createTime"));
        /*query.order(Sort.ascending("createTime"));
        query.order(Sort.descending("updateTime"));*/

        EGridResult<Car> result = carDao.findPage(page, rows, query);
        /*List<Car> cars = result.getRows();

        for (Car car : cars) {
            String drivingPho1 = fileInfoDao.readFile(car.getDrivingPho1(), "t_car");//行驶证照片（第一页）
            String drivingPho2 = fileInfoDao.readFile(car.getDrivingPho2(), "t_car");//行驶证照片（第二页）
            String drivingPho3 = fileInfoDao.readFile(car.getDrivingPho3(), "t_car");//行驶证照片（第三页）
            String roadTCPho = fileInfoDao.readFile(car.getRoadTCPho(), "t_car");//车辆运输许可证照片
            String carIdentityPhoBef = fileInfoDao.readFile(car.getCarIdentityPhoBef(), "t_car");//车主身份证照片正面
            String carIdentityPhoBack = fileInfoDao.readFile(car.getCarIdentityPhoBack(), "t_car");//车主身份证照片反面

            car.setDrivingPho1(drivingPho1);
            car.setDrivingPho2(drivingPho2);
            car.setDrivingPho3(drivingPho3);
            car.setRoadTCPho(roadTCPho);
            car.setCarIdentityPhoBef(carIdentityPhoBef);
            car.setCarIdentityPhoBack(carIdentityPhoBack);
        }*/
        if (result != null) {
            List<Car> cars = result.getRows();
            if (cars != null && cars.size() > 0) signUrlPhoto(cars);
        }

        return result;
    }

    /**
     * 先查询本地数据库是否有照片，
     * 有则 将本地地址放入列表对象中
     * 无则 获取oss图片访问URL, 放入列表对象中
     */
    private void signUrlPhoto(List<Car> cars) {
        for (Car car : cars) {
            //putPhotoToOss(car);
            if (StrUtil.isNotEmpty(car.getDrivingPho1())) {
                String filePath = photoFileService.readPhoto0(car.getDrivingPho1(), "t_car");
                if (StrUtil.isNotEmpty(filePath)) {
                    car.setDrivingPho1("https://heijinyun.net/web" + filePath);
                } else {
                    car.setDrivingPho1(photoFileService.signUrl(car.getDrivingPho1()));
                }
            }
            if (StrUtil.isNotEmpty(car.getDrivingPho2())) {
                String filePath = photoFileService.readPhoto0(car.getDrivingPho2(), "t_car");
                if (StrUtil.isNotEmpty(filePath)) {
                    car.setDrivingPho2("https://heijinyun.net/web" + filePath);
                } else {
                    car.setDrivingPho2(photoFileService.signUrl(car.getDrivingPho2()));
                }
            }
            if (StrUtil.isNotEmpty(car.getDrivingPho3())) {
                String filePath = photoFileService.readPhoto0(car.getDrivingPho3(), "t_car");
                if (StrUtil.isNotEmpty(filePath)) {
                    car.setDrivingPho3("https://heijinyun.net/web" + filePath);
                } else {
                    car.setDrivingPho3(photoFileService.signUrl(car.getDrivingPho3()));
                }
            }
            if (StrUtil.isNotEmpty(car.getCarIdentityPhoBef())) {
                String filePath = photoFileService.readPhoto0(car.getCarIdentityPhoBef(), "t_car");
                if (StrUtil.isNotEmpty(filePath)) {
                    car.setCarIdentityPhoBef("https://heijinyun.net/web" + filePath);
                } else {
                    car.setCarIdentityPhoBef(photoFileService.signUrl(car.getCarIdentityPhoBef()));
                }
            }
            if (StrUtil.isNotEmpty(car.getCarIdentityPhoBack())) {
                String filePath = photoFileService.readPhoto0(car.getCarIdentityPhoBack(), "t_car");
                if (StrUtil.isNotEmpty(filePath)) {
                    car.setCarIdentityPhoBack("https://heijinyun.net/web" + filePath);
                } else {
                    car.setCarIdentityPhoBack(photoFileService.signUrl(car.getCarIdentityPhoBack()));
                }
            }
            if (StrUtil.isNotEmpty(car.getRoadTCPho())) {
                String filePath = photoFileService.readPhoto0(car.getRoadTCPho(), "t_car");
                if (StrUtil.isNotEmpty(filePath)) {
                    car.setRoadTCPho("https://heijinyun.net/web" + filePath);
                } else {
                    car.setRoadTCPho(photoFileService.signUrl(car.getRoadTCPho()));
                }
            }
        }
    }

    /**
     * 把之前保存在数据库到图片转移到oss
     */
    private void putPhotoToOss(Car car) {
        if (StrUtil.isNotEmpty(car.getDrivingPho1()) && photoFileService.isOSSObjectNUll(car.getDrivingPho1()))  //判断是否需要上传照片到oss
            photoFileService.putObject(car.getDrivingPho1(), "t_car");            //行驶证照片

        if (StrUtil.isNotEmpty(car.getDrivingPho2()) && photoFileService.isOSSObjectNUll(car.getDrivingPho2()))
            photoFileService.putObject(car.getDrivingPho2(), "t_car");            //行驶证照片副页正面

        if (StrUtil.isNotEmpty(car.getDrivingPho3()) && photoFileService.isOSSObjectNUll(car.getDrivingPho3()))
            photoFileService.putObject(car.getDrivingPho3(), "t_car");            //行驶证照片副页反面

        if (StrUtil.isNotEmpty(car.getCarIdentityPhoBef()) && photoFileService.isOSSObjectNUll(car.getCarIdentityPhoBef()))
            photoFileService.putObject(car.getCarIdentityPhoBef(), "t_car");            //车主身份证照片正面

        if (StrUtil.isNotEmpty(car.getCarIdentityPhoBack()) && photoFileService.isOSSObjectNUll(car.getCarIdentityPhoBack()))
            photoFileService.putObject(car.getCarIdentityPhoBack(), "t_car");            //车主身份证照片反面

        if (StrUtil.isNotEmpty(car.getRoadTCPho()) && photoFileService.isOSSObjectNUll(car.getRoadTCPho()))
            photoFileService.putObject(car.getRoadTCPho(), "t_car");            //车辆运输许可证照片
    }


    @Override
    public ServerResponse verifyCar() {

        Long updateTime = Long.valueOf(request.getParameter("updateTime"));
        Query<Car> condition = carDao.createQuery();
        condition.filter("id", request.getParameter("id"));
        condition.filter("updateTime", updateTime);

        Car car = carDao.get("id", request.getParameter("id"));
        if (car == null) {
            logger.warn("WebCarInfoServiceImpl.java:224" + request.getParameter("id"));
            return ServerResponse.createError("参数错误");
        }

        int verify = Integer.parseInt(request.getParameter("verify"));
        String verifyStr = StrUtil.isNotEmpty(request.getParameter("verifyStr")) ? request.getParameter("verifyStr") : "";
        String carNum = request.getParameter("carNum");
        String carInfoId = request.getParameter("carInfoId");
        //value
        UpdateOperations<Car> value = carDao.createUpdateOperations();
        if (StrUtil.isNotEmpty(verify)) value.set("verify", verify);
        value.set("verifyStr", verifyStr);
        if (StrUtil.isNotEmpty(carNum)) value.set("carNum", carNum);
        if (StrUtil.isNotEmpty(carInfoId)) {
            CarInfo carInfo = this.get("id", carInfoId);
            if (carInfo != null) value.set("carInfo", carInfo);
        }
        carDao.update(condition, value);

        if (StrUtil.isEmpty(verifyStr)) {
            if (verify == 1) verifyStr = "通过";
            if (verify == 2) verifyStr = "未通过";
            if (verify == 4) verifyStr = "停用";
        }
        //添加审核结果到消息列表
        if (StrUtil.isNotEmpty(verify) && verify != car.getVerify() && StrUtil.isNotEmpty(verifyStr)) {
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carId", car.getObjectId().toHexString());
            List<DriverToCar> dtcList = driverToCarDao.list(dtcQuery);
            ///是否审核，0-未审核不可用，1-审核通过，2-审核未通过，3未审核可用，4停用
            List<ObjectId> dids = new ArrayList<>();
            for (DriverToCar dtc : dtcList) {
                dids.add(new ObjectId(dtc.getDriverId()));
            }
            Query<DriverInfo> dQuery = driverInfoService.createQuery();
            dQuery.filter("_id in", dids.toArray());
            List<DriverInfo> driverInfos = driverInfoService.list(dQuery);

            for (DriverInfo di : driverInfos) {
                //微信异步推送
                pushInfoService.weChatSendCar(di.getPhoneId(), di.getMobile(), di.getName(), "车辆审核", verifyStr, carNum, di.getObjectId().toHexString());

                //保存推送信息
                //pushInfoService.addPushInfo("车辆审核", "车辆：" + carNum + "审核结果" + verifyStr, di.getObjectId().toHexString(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));
            }


        }

        return ServerResponse.createSuccess();

    }
}
