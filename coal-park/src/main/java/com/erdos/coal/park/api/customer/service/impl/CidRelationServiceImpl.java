package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.ICidRelationDao;
import com.erdos.coal.park.api.customer.entity.CidRelation;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.pojo.CidRelationData;
import com.erdos.coal.park.api.customer.service.ICidRelationService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.utils.ObjectUtil;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("cidRelationService")
public class CidRelationServiceImpl extends BaseMongoServiceImpl<CidRelation, ICidRelationDao> implements ICidRelationService {
    @Resource
    private ICustomerUserService customerUserService;

    @Override
    public ServerResponse<String> addCidRelation(String mobile) {
        CustomerUser user = customerUserService.get("mobile", mobile);
        if (ObjectUtil.isNull(user))
            return ServerResponse.createError("该手机号未注册");

        String cid = ShiroUtils.getUserId();
        assert cid != null;
        CustomerUser customerUser = customerUserService.getByPK(cid);
        if (cid.equals(user.getObjectId().toString()))
            return ServerResponse.createError("不能添加自己为好友");
        Query<CidRelation> query = this.createQuery();
        query.or(
                query.and(
                        query.criteria("cid1").equal(user.getObjectId().toString()),
                        query.criteria("cid2").equal(customerUser.getObjectId().toString())
                ),
                query.and(
                        query.criteria("cid2").equal(user.getObjectId().toString()),
                        query.criteria("cid1").equal(customerUser.getObjectId().toString())
                )
        );

        List<CidRelation> list = query.find().toList();
        if (list.size() > 0)
            return ServerResponse.createError("已经是好友，无需重新添加");
        CidRelation cidRelation = new CidRelation();
        cidRelation.setCid1(cid);
        cidRelation.setCid2(user.getObjectId().toString());
        cidRelation.setCustomerUser1(customerUser);
        cidRelation.setCustomerUser2(user);
        this.save(cidRelation);
        return ServerResponse.createSuccess("好友添加成功");
    }

    @Override
    public ServerResponse<String> delCidRelation(String id) {
        String cid = ShiroUtils.getUserId();
        Query<CidRelation> query = this.createQuery();
        query.or(
                query.and(
                        query.criteria("cid1").equal(id),
                        query.criteria("cid2").equal(cid)
                ),
                query.and(
                        query.criteria("cid2").equal(id),
                        query.criteria("cid1").equal(cid)
                )
        );
        List<CidRelation> list = this.list(query);
        if (list.size() > 0) {
            Query<CidRelation> delQuery = this.createQuery();
            delQuery.filter("cid1", list.get(0).getCid1());
            delQuery.filter("cid2", list.get(0).getCid2());
            this.delete(delQuery);
        } else {
            return ServerResponse.createError("已经不是好友，无需删除");
        }
        return ServerResponse.createSuccess("好友删除成功");
    }

    @Override
    //public ServerResponse<List<CidRelationData>> listCidRelation() {
    public ServerResponse<EGridResult> listCidRelation(Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        assert cid != null;
        Query<CidRelation> query = this.createQuery();
        query.or(
                query.criteria("cid1").equal(cid),
                query.criteria("cid2").equal(cid)
        );
        //List<CidRelation> list = this.list(query);
        EGridResult<CidRelation> eGridResult = findPage(page, rows, query);
        List<CidRelation> list = findPage(page, rows, query).getRows();
        Long total = eGridResult.getTotal();
        List<CidRelationData> userList = new ArrayList<>();
        for (CidRelation cidRelation : list) {
            CidRelationData cidRelationData = new CidRelationData();
            if (cid.equals(cidRelation.getCid1())) {
                CustomerUser user = cidRelation.getCustomerUser2();
                cidRelationData.setCid(user.getObjectId().toString());
                cidRelationData.setMobile(user.getMobile());
                cidRelationData.setName(user.getName());
                userList.add(cidRelationData);
            } else {
                CustomerUser user = cidRelation.getCustomerUser1();
                cidRelationData.setCid(user.getObjectId().toString());
                cidRelationData.setMobile(user.getMobile());
                cidRelationData.setName(user.getName());
                userList.add(cidRelationData);
            }
        }
        //return ServerResponse.createSuccess("查询成功", userList);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, userList));
    }
}
