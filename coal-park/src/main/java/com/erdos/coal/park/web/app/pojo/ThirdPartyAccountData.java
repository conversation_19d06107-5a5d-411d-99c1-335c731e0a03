package com.erdos.coal.park.web.app.pojo;

import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.web.sys.entity.SysUnit;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ThirdPartyAccountData implements Serializable {
    private String unitCode;        //三方账号的单位编码
    private String unitName;        //三方账号的单位名称
    private String id;              //三方账号 --- 对应客商的phoneId
    private String name;            //三方账号人姓名
    private String mobile;          //三方账号人手机号

    private List<String> counts;
    private Integer total;          //总条数
    private BigDecimal totalFee;    //总金额（分转元）

    private List<SysUnit> sysUnits;
    private List<CustomerUser> cusUser;

    private Date createTime;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
//        return unitName;
        return sysUnits.get(0).getName();
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
//        return mobile;
        return cusUser.get(0).getMobile();
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public List<String> getCounts() {
        return counts;
    }

    public void setCounts(List<String> counts) {
        this.counts = counts;
    }

    public Integer getTotal() {
//        return total;
        return counts.size();
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public List<SysUnit> getSysUnits() {
        return sysUnits;
    }

    public void setSysUnits(List<SysUnit> sysUnits) {
        this.sysUnits = sysUnits;
    }

    public List<CustomerUser> getCusUser() {
        return cusUser;
    }

    public void setCusUser(List<CustomerUser> cusUser) {
        this.cusUser = cusUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
