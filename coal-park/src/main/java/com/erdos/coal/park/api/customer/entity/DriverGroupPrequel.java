package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Reference;

@Entity(value = "t_driver_group_prequel", noClassnameStored = true)
public class DriverGroupPrequel extends BaseMongoInfo {
    //客商按手机号添加司机到司机组，需要司机同意，司机同意或拒绝后，则删除记录
    private String groupNo; //司机组编号
    private String cid;     //客商编号
    private String cName;   //客商姓名
    private String did;     //司机编号
    private boolean delete;  //司机拒绝加入

    @Reference(value = "driverInfoID", lazy = true, idOnly = true, ignoreMissing = true)
    private DriverInfo driverInfo;

    @Reference(value = "customerUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser;

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getcName() {
        return cName;
    }

    public void setcName(String cName) {
        this.cName = cName;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public boolean isDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }

    public DriverInfo getDriverInfo() {
        return driverInfo;
    }

    public void setDriverInfo(DriverInfo driverInfo) {
        this.driverInfo = driverInfo;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }
}
