package com.erdos.coal.park.api.customer.pojo;

import com.erdos.coal.utils.StrUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class GoodsInfoData implements Serializable {
    private String gid;             //货运信息编号
    private String tradeName;      //商品名称
    private String beginPoint;     //起点
    private String endPoint;        //终点
    private Integer total;           //总车数
    private Double weight;          //车载重量要求
    private Date releaseTime;     //发布时间

    private Double price;           //单价
    private Double distance;          //总里程
    private Double tolls;           //预估过路费

    private Integer mold;           //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2
    private String outVariety;      //发货单位产品
    private String inVariety;       //收货单位产品

    private String outUnitName;     //发货一级单位名称
    private String outUnitCode;     //发货一级单位编号
    private String outBizContractName;     //发货合同名称
    private String outBizContractCode;     //发货合同编号
    private String outSubName;     //发货二级单位名称
    private String outDefaultDownUnit;     //发货二级单位编号
    private String outAreaName;     //发货场区名称
    private String outDefaultArea;     //发货场区编号

    private String inUnitName;     //收货一级单位名称
    private String inUnitCode;     //收货一级单位编号
    private String inBizContractName;     //收货合同名称
    private String inBizContractCode;     //收货合同编号
    private String inSubName;     //收货二级单位名称
    private String inDefaultDownUnit;     //收货二级单位编号
    private String inAreaName;     //收货场区名称
    private String inDefaultArea;     //收货场区编号

    private Integer status; //货运信息状态 0-货运信息下订单可接单 1-货运信息下订单暂停接单
    private Boolean[] appType;
    //App端需要处理的goods类型：index 0-是否可修改，1-是否可废除，2-是否可分享给微信司机用户，3-是否来自友商分享，
    // 4-是否可分享给友商，5-是否可退回，

    private BigDecimal fees;   //付款金额
    /*
     * 0-未分享货单给好友客商
     * 1-分享一部分货单给好友客商,还有部分可以分享
     * 2-可以分享的订单已经全部分享给好友客商了
     * */
    private Integer share;
    private String shareType;       //0-客自己的订单、1-分享给友商的订单、2-来自客商分享的订单
    private String cusName;         //客商姓名
    private String shareCusName;    //友商姓名

    private Integer driverIsAgree = 0;  //司机是否接受订单 - 0-司机还没有接受；1-司机接受订单；2-司机拒绝订单

    private int isPay = 0;  //客商是否代付(0-不代付，1-代付)
    private String groupNo;
    private String designCarNum;
    private String designDriverId;
    private String longitude;
    private String latitude;
    private Integer pType;          //下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单

    private Integer isHistory;  //0-货运信息下订单都未接单，1-货运信息下订单有或全部接单，2-货运信息下订单全部完成

    private Integer orderNum0 = 0;  //货运信息下未接单车数
    private Integer orderNum1 = 0;  //货运信息下已接单未完成车数
    private Integer delNum = 0; //废除的车数
    private Integer orderNum3 = 0;  //货运信息下已入场车数
    private Integer orderNum2 = 0;  //货运信息下已接单已完成车数
    private Double outNetWeight;
    private Double inNetWeight;

    private String spell;   //运往地编码
    private String place;   //运往地名称
    private String outTradingUnit;      //发货交易单位编号
    private String outTradingUnitName;      //发货交易单位名称
    private String inTradingUnit;       //收货交易单位编号
    private String inTradingUnitName;       //收货交易单位名称

    private Integer partSurplusNum; //货运信息按车数批次分享给司机微信后剩余的车数

    private Integer mold2;          //是否往货业务，空或0-否，1-是
    private String beginDistrictCode;//起点区划编码
    private String endDistrictCode; //终点区划编码

    private String productID;  // 智慧能源-合同备案中的煤种ID

    private Double inNetWeightOne;     // 采购业务需要录入对方的净重(单车净重)
    private String loadPound;           // 采购业务需要录入装货单号（下单一车时传参）
    private Long loadTime;           // 采购业务需要录入装货时间（下单一车时传参）

    // 2024-12-28 急加
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;

    public String getOutTradingUnit() {
        return outTradingUnit;
    }

    public void setOutTradingUnit(String outTradingUnit) {
        this.outTradingUnit = outTradingUnit;
    }

    public String getOutTradingUnitName() {
        return outTradingUnitName;
    }

    public void setOutTradingUnitName(String outTradingUnitName) {
        this.outTradingUnitName = outTradingUnitName;
    }

    public String getInTradingUnit() {
        return inTradingUnit;
    }

    public void setInTradingUnit(String inTradingUnit) {
        this.inTradingUnit = inTradingUnit;
    }

    public String getInTradingUnitName() {
        return inTradingUnitName;
    }

    public void setInTradingUnitName(String inTradingUnitName) {
        this.inTradingUnitName = inTradingUnitName;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public Integer getOrderNum0() {
        return orderNum0;
    }

    public void setOrderNum0(Integer orderNum0) {
        this.orderNum0 = orderNum0;
    }

    public Integer getOrderNum1() {
        return orderNum1;
    }

    public void setOrderNum1(Integer orderNum1) {
        this.orderNum1 = orderNum1;
    }

    public Integer getDelNum() {
        return delNum;
    }

    public void setDelNum(Integer delNum) {
        this.delNum = delNum;
    }

    public Integer getOrderNum3() {
        return orderNum3;
    }

    public void setOrderNum3(Integer orderNum3) {
        this.orderNum3 = orderNum3;
    }

    public Integer getOrderNum2() {
        return orderNum2;
    }

    public void setOrderNum2(Integer orderNum2) {
        this.orderNum2 = orderNum2;
    }

    public Double getOutNetWeight() {
        return outNetWeight;
    }

    public void setOutNetWeight(Double outNetWeight) {
        this.outNetWeight = outNetWeight;
    }

    public void setOutNetWeight(Double outGrossWeight, Double outTareWeight) {
        Double grossWeight = StrUtil.isNotEmpty(outGrossWeight) ? outGrossWeight : 0.0;
        Double tareWeight = StrUtil.isNotEmpty(outTareWeight) ? outTareWeight : 0.0;
        this.outNetWeight = grossWeight - tareWeight;
    }

    public Double getInNetWeight() {
        return inNetWeight;
    }

    public void setInNetWeight(Double inNetWeight) {
        this.inNetWeight = inNetWeight;
    }

    public void setInNetWeight(Double inGrossWeight, Double inTareWeight) {
        Double grossWeight = StrUtil.isNotEmpty(inGrossWeight) ? inGrossWeight : 0.0;
        Double tareWeight = StrUtil.isNotEmpty(inTareWeight) ? inTareWeight : 0.0;
        this.inNetWeight = grossWeight - tareWeight;
    }

    public BigDecimal getFees() {
        return fees;
    }

    public void setFees(BigDecimal fees) {
        this.fees = fees;
    }

    public Boolean[] getAppType() {
        return appType;
    }

    public void setAppType(Boolean[] appType) {
        this.appType = appType;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getOutDefaultArea() {
        return outDefaultArea;
    }

    public void setOutDefaultArea(String outDefaultArea) {
        this.outDefaultArea = outDefaultArea;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public String getInDefaultArea() {
        return inDefaultArea;
    }

    public void setInDefaultArea(String inDefaultArea) {
        this.inDefaultArea = inDefaultArea;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getShareCusName() {
        return shareCusName;
    }

    public void setShareCusName(String shareCusName) {
        this.shareCusName = shareCusName;
    }

    public Integer getDriverIsAgree() {
        return driverIsAgree;
    }

    public void setDriverIsAgree(Integer driverIsAgree) {
        this.driverIsAgree = driverIsAgree;
    }

    public int getIsPay() {
        return isPay;
    }

    public void setIsPay(int isPay) {
        this.isPay = isPay;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getDesignCarNum() {
        return designCarNum;
    }

    public void setDesignCarNum(String designCarNum) {
        this.designCarNum = designCarNum;
    }

    public String getDesignDriverId() {
        return designDriverId;
    }

    public void setDesignDriverId(String designDriverId) {
        this.designDriverId = designDriverId;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public Integer getpType() {
        return pType;
    }

    public void setpType(Integer pType) {
        this.pType = pType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public Integer getPartSurplusNum() {
        return partSurplusNum;
    }

    public void setPartSurplusNum(Integer partSurplusNum) {
        this.partSurplusNum = partSurplusNum;
    }

    public Integer getMold2() {
        return mold2;
    }

    public void setMold2(Integer mold2) {
        this.mold2 = mold2;
    }

    public String getBeginDistrictCode() {
        return beginDistrictCode;
    }

    public void setBeginDistrictCode(String beginDistrictCode) {
        this.beginDistrictCode = beginDistrictCode;
    }

    public String getEndDistrictCode() {
        return endDistrictCode;
    }

    public void setEndDistrictCode(String endDistrictCode) {
        this.endDistrictCode = endDistrictCode;
    }

    public String getProductID() {
        return productID;
    }

    public void setProductID(String productID) {
        this.productID = productID;
    }

    public Double getInNetWeightOne() {
        return inNetWeightOne;
    }

    public void setInNetWeightOne(Double inNetWeightOne) {
        this.inNetWeightOne = inNetWeightOne;
    }

    public String getLoadPound() {
        return loadPound;
    }

    public void setLoadPound(String loadPound) {
        this.loadPound = loadPound;
    }

    public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }
}
