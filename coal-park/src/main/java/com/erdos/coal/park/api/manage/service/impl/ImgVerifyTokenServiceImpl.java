package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.manage.dao.IImgVerifyTokenDao;
import com.erdos.coal.park.api.manage.entity.ImgVerifyToken;
import com.erdos.coal.park.api.manage.service.IImgVerifyTokenService;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service("imgVerifyTokenService")
public class ImgVerifyTokenServiceImpl extends BaseMongoServiceImpl<ImgVerifyToken, IImgVerifyTokenDao> implements IImgVerifyTokenService {

    @Override
    public String saveXpos(int xpos) {
        String id = Utils.getUUID();
        ImgVerifyToken imgVerifyToken = new ImgVerifyToken();
        imgVerifyToken.setId(id);
        imgVerifyToken.setXpos(xpos);
        imgVerifyToken.setMakeTime(new Date());
        this.save(imgVerifyToken);

        return id;
    }

    @Override
    public boolean checkXpos(String id, int moveX) {
        ImgVerifyToken imgVerifyToken = this.get("id", id);
        if (imgVerifyToken != null) {
            int moveCheckError = 5; // 允许的误差范围设置为2个像素
            int xPoxCache = imgVerifyToken.getXpos();
            if ((moveX < (xPoxCache + moveCheckError)) && (moveX > (xPoxCache - moveCheckError))) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    @Override
    public String addImgVerifyToken() {
        String verifyToken = Utils.getUUID();
        ImgVerifyToken imgVerifyToken = new ImgVerifyToken();
        imgVerifyToken.setVerifyToken(verifyToken);
        imgVerifyToken.setMakeTime(new Date());
        this.save(imgVerifyToken);

        return verifyToken;
    }

    @Override
    public boolean checkImaVerifyToken(String verifyToken) {
        Query<ImgVerifyToken> query = this.createQuery();
        query.criteria("verifyToken").equal(verifyToken);
        ImgVerifyToken imgVerifyToken = this.findAndDelete(query);
        if (imgVerifyToken == null) {
            return false;
        } else {
            return true;
        }
    }
}
