package com.erdos.coal.park.web.app.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

@Entity(value = "t_cus_hdd", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("cid")}),
        @Index(fields = {@Field("mobile")})
})
public class CusHDDKS extends BaseMongoInfo {
    private String id;
    private String cid;
    private String mobile;
    private String name;
    private String hddUrl;
    private String appKey;
    private String appSecret;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHddUrl() {
        return hddUrl;
    }

    public void setHddUrl(String hddUrl) {
        this.hddUrl = hddUrl;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
}
