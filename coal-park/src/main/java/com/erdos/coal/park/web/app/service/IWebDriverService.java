package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.QuarantineInfo;

import java.math.BigDecimal;

public interface IWebDriverService extends IBaseMongoService<DriverInfo> {
    //司机信息列表
    EGridResult loadGrid(Integer page, Integer rows);

    ServerResponse editDriver();

    //保存审核是否通过
    ServerResponse saveCheck(DriverInfo driverInfo);

    //司机信用列表
    EGridResult bwlist();

    //司机账户金额列表
    EGridResult accList(Integer page, Integer rows);

    //司机账户详情
    EGridResult accEdit(Integer page, Integer rows);

    //账户剩余金额
    BigDecimal getAvailableFee();

    //司机签到列表
    EGridResult loadSignInGrid(Integer page, Integer rows);

    //司机关联车辆列表
    EGridResult loadDvrToCarGrid(Integer page, Integer rows);

    //修改司机关联车辆的状态
    ServerResponse<String> updateDvrToCarDel();

    EGridResult loadGridQuarantine(Integer page, Integer rows);
    //保存防疫申报审核是否通过
    ServerResponse saveAudit();
}
