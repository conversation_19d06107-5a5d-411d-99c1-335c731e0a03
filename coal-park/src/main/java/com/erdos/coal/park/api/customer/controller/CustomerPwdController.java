package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/*
客商app修改密码
 */
//"客商APP修改密码接口"
@RestController
@RequestMapping("/api/cus/pwd")
public class CustomerPwdController extends BaseController {
    @Resource
    private ICustomerUserService customerUserService;

    //修改密码
    @InvokeLog(description = "客商修改密码接口") //日志
    @PostMapping(value = "/editPwd")
    public ServerResponse<AccessToken> editPwdHandler(
            @RequestParam(value = "oldPwd") String oldPwd,     //"旧密码"
            @RequestParam(value = "newPwd") String newPwd,     //"新密码"
            @RequestParam(value = "confirmPwd") String confirmPwd//"确认密码"
    ) throws GlobalException {
        //TODO: 不用传当前登录用户(客商)的参数, 通过 getCurrentUser 这个方法取
        return customerUserService.editPwd(oldPwd, newPwd, confirmPwd);

    }

    //判断设置或修改交易密码
    @InvokeLog(description = "客商判断设置或修改交易密码接口") //日志
    @PostMapping(value = "/checkBarPwd")
    public ServerResponse<Integer> checkBarPwdHandler() throws GlobalException {
        return customerUserService.checkBarPwd();
    }

    //设置交易密码
    @InvokeLog(description = "客商设置交易密码接口") //日志
    @PostMapping(value = "/setBarPwd")
    public ServerResponse<String> setBarPwdHandler(
            @RequestParam(value = "barPwd") String barPwd,         //"交易密码"
            @RequestParam(value = "confirmPwd") String confirmPwd  //"确认密码"
    ) throws GlobalException {
        return customerUserService.setBarPwd(barPwd, confirmPwd);
    }

    //旧交易密码修改新交易密码
    @InvokeLog(description = "客商重置交易密码接口") //日志
    @PostMapping(value = "/updateBarPwd")
    public ServerResponse<String> updateBarPwdHandler(
            @RequestParam(value = "oldBarPwd") String oldBarPwd,   //"旧交易密码"
            @RequestParam(value = "newBarPwd") String newBarPwd,   //"新交易密码"
            @RequestParam(value = "confirmPwd") String confirmPwd  //"确认密码"
    ) throws GlobalException {
        return customerUserService.updateBarPwd(oldBarPwd, newBarPwd, confirmPwd);
    }

    //短信验证码修改交易密码
    @InvokeLog(description = "客商短信验证码修改交易密码接口") //日志
    @PostMapping(value = "/smsUpBarPwd")
    public ServerResponse<String> smsUpBarPwdSHandler(
            @RequestParam(value = "smsCode") String smsCode,       //"短信验证码"
            @RequestParam(value = "barPwd") String barPwd,         //"交易密码"
            @RequestParam(value = "confirmPwd") String confirmPwd  //"确认密码"
    ) throws GlobalException {
        return customerUserService.smsUpBarPwd(smsCode, barPwd, confirmPwd);
    }

    //发布货运信息前验证交易密码是否正确
    @InvokeLog(description = "发布货运信息前验证交易密码是否正确 接口") //日志
    @PostMapping(value = "/check_barPwd")
    public ServerResponse<String> checkBarPwdHandler(
            @RequestParam(value = "barPwd") String barPwd      //"输入密码"
    ) throws GlobalException {
        return customerUserService.checkBarPwd(barPwd);
    }

}
