package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysApp;
import org.springframework.web.multipart.MultipartFile;

public interface ISysAppService extends IBaseMongoService<SysApp> {
    EGridResult loadGrid(Integer page, Integer rows);

    ServerResponse saveApp(MultipartFile file);

    ServerResponse deleteApp(SysApp deleted);
}
