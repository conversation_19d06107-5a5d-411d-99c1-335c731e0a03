package com.erdos.coal.park.web.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.dao.IRefundPreDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.RefundPre;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IRefundPreService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWxResultService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.park.web.app.service.IWebRefundPreService;
import com.erdos.coal.park.web.sys.service.IThirdPartyAccountService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.transaction.wxpay.service.WXPayWechatConfig;
import com.erdos.coal.transaction.wxpay.service.WXPayWechatCusConfig;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

@Service("webRefundPreService")
public class WebRefundPreServiceImpl extends BaseMongoServiceImpl<RefundPre, IRefundPreDao> implements IWebRefundPreService {

    @Resource
    private IRefundPreService refundPreService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private MongoClient client;
    @Resource
    private HttpServletRequest request;
    @Resource
    private ILockedService lockedService;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private WXPayWechatCusConfig wxPayWechatCusConfig;
    @Resource
    private WXPayWechatConfig wxPayWechatConfig;
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    @Override
    public EGridResult refundPreLoadGrid(Integer page, Integer rows) {
        Query<RefundPre> query = refundPreService.createQuery();
        String mobile = request.getParameter("mobile");
        if (StrUtil.isNotEmpty(mobile)) {
            CustomerUser cUser = customerUserService.get("mobile", mobile);
            DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
            String cid = cUser != null ? cUser.getObjectId().toHexString() : null;
            String did = driverInfo != null ? driverInfo.getObjectId().toHexString() : null;
            String openid = cUser != null ? cUser.getPhoneId() : null;
            query.or(
                    query.criteria("userId").equal(cid),
                    query.criteria("userId").equal(did),
                    query.criteria("userId").equal(openid)
            );
        }

        if (StrUtil.isNotEmpty(request.getParameter("transferNo")))
            query.filter("transferNo", request.getParameter("transferNo"));
        query.order(Sort.ascending("check"));//, Sort.ascending("createTime"));
//        List<RefundPre> refundPreList = refundPreService.list(query);

        EGridResult<RefundPre> result = refundPreService.findPage(page, rows, query);
        List<RefundPre> refundPreList = result.getRows();

        List<RefundPreData> list = new ArrayList<>();
        for (RefundPre refundPre : refundPreList) {
            RefundPreData refundPreData = new RefundPreData();
            BeanUtils.copyProperties(refundPre, refundPreData);
            if (refundPre.getCustomerUser() != null) {
                refundPreData.setUserType(0);
                refundPreData.setMobile(refundPre.getCustomerUser().getMobile());
            } else {
                refundPreData.setUserType(1);
                refundPreData.setMobile(refundPre.getDriverInfo().getMobile());
            }
            refundPreData.setAmount(refundPre.getAmount().divide(new BigDecimal(100)).doubleValue());//(refundPre.getAmount() * 1.0 / 100);
            list.add(refundPreData);
        }

        return new EGridResult(result.getTotal(), list);
        /*int size = list.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        return new EGridResult(list.size(), list.subList(fromIndex, toIndex));*/
    }

    @Override
    @Transactional
    public ServerResponse<String> saveCheck(RefundPreData data) {
        boolean lock = lockedService.getLock(data.getTransferNo(), SysConstants.LockType.REFUND_PRE.getType());//加锁
        if (!lock) {
            return null;
        } else {
            RefundPre refundPre = this.get("transferNo", data.getTransferNo());
            checkWXResult(data.getTransferNo(), refundPre);
            if (refundPre.getCheck() > 0 && data.getCheck() == 0)
                return ServerResponse.createError("已经审核过的数据，不可重新定义为未审核！");
            if (refundPre.getCheck() == 1 && data.getCheck() == 2 && "SUCCESS".equals(refundPre.getResultCode()))
                return ServerResponse.createError("用户已经成功提现，不可进行审核");

            if (refundPre.getCheck() == 2 && data.getCheck() == 1)
                return ServerResponse.createError("资金已退回用户平台账户，不可再通过审核");

            Map<String, Object> upMap = new HashMap<>();
            if (StrUtil.isNotEmpty(data.getCheck())) upMap.put("check", data.getCheck());
            if (StrUtil.isNotEmpty(data.getReason())) upMap.put("reason", data.getReason());

            ClientSession clientSession = client.startSession();
            try {
                clientSession.startTransaction();
                if (refundPre.getCheck() != 1 && data.getCheck() == 1) { //审核通过，调用支付宝或微信提现接口
                    Map<String, String> map;
                    if (data.getRefundType() == 0) {
                        map = refundPreService.aliPayRefundPass(data.getTransferNo());//支付宝
                    } else {
                        map = refundPreService.wxRefundPass(data.getTransferNo(), refundPre);//微信
                    }
                    if (StrUtil.isNotEmpty(map.get("resultCode"))) upMap.put("resultCode", map.get("resultCode"));
                    if (StrUtil.isNotEmpty(map.get("errCode"))) {
                        upMap.put("errCode", map.get("errCode"));
                    } else {
                        upMap.put("errCode", "");
                    }
                    if (StrUtil.isNotEmpty(map.get("errCodeDes"))) {
                        upMap.put("errCodeDes", map.get("errCodeDes"));
                    } else {
                        upMap.put("errCodeDes", "");
                    }
                } else if (refundPre.getCheck() != 2 && data.getCheck() == 2) {
                    if (data.getUserType() == 0) {
                        Document filter = new Document("outTradeNo", data.getTransferNo());
                        if (data.getRemark().equals("三方账户余额提现到微信零钱")) {
                            MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();
                            thirdPartyCollection.deleteOne(clientSession, filter);
                        } else {
                            MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
                            cusAccountCollection.deleteOne(clientSession, filter);
                        }
                    } else {
                        MongoCollection<Document> driAccountCollection = driverAccountService.getCollection();
                        Document filter = new Document("outTradeNo", data.getTransferNo());
                        driAccountCollection.deleteOne(clientSession, filter);
                    }
                }

                upMap.put("updateTime", new Date().getTime());
                Map<String, Object> parMap = new HashMap<>();
                parMap.put("$set", upMap);
                Document updateQuery = new Document("transferNo", data.getTransferNo());
                updateQuery.append("updateTime", data.getUpdateTime());
                MongoCollection<Document> refundCollection = this.getCollection();
                refundCollection.updateOne(clientSession, updateQuery, Document.parse(JSON.toJSONString(parMap)));
                clientSession.commitTransaction();

                return ServerResponse.createSuccess("成功");
            } catch (Exception e) {
                e.printStackTrace();
                clientSession.abortTransaction();
                return ServerResponse.createError("失败");
            } finally {
                clientSession.close();
                lockedService.unLock(data.getTransferNo(), SysConstants.LockType.REFUND_PRE.getType());
            }
        }
    }

    @Override
    @Transactional
    public ServerResponse<Map<String, String>> retry() {
        String transferNo = request.getParameter("transferNo");
        boolean lock = lockedService.getLock(transferNo, SysConstants.LockType.REFUND_PRE.getType());//加锁
        if (!lock) {
            return null;
        } else {
            Map<String, String> map = new HashMap<>();
            try {
                RefundPre refundPre = this.get("transferNo", transferNo);
                checkWXResult(transferNo, refundPre);
                if ("SUCCESS".equals(refundPre.getResultCode())) return ServerResponse.createError("已经成功提现，不许重试");

                //调用支付宝或微信提现接口
                if (refundPre.getRefundType() == 0) {
                    map = refundPreService.aliPayRefundPass(transferNo);//支付宝
                } else {
                    map = refundPreService.wxRefundPass(transferNo, refundPre);//微信
                }
                Query<RefundPre> query = this.createQuery().filter("transferNo", transferNo);
                UpdateOperations<RefundPre> updateOperations = this.createUpdateOperations();
                if (StrUtil.isNotEmpty(map.get("resultCode")))
                    updateOperations.set("resultCode", map.get("resultCode"));
                if (StrUtil.isNotEmpty(map.get("errCode"))) {
                    updateOperations.set("errCode", map.get("errCode"));
                } else {
                    updateOperations.set("errCode", "");
                }
                if (StrUtil.isNotEmpty(map.get("errCodeDes"))) {
                    updateOperations.set("errCodeDes", map.get("errCodeDes"));
                } else {
                    updateOperations.set("errCodeDes", "");
                }

                this.update(query, updateOperations);

                return ServerResponse.createSuccess(map.get("resultCode"));
            } catch (Exception e) {
                e.printStackTrace();
                return ServerResponse.createError(map.get("errCodeDes"));
            } finally {
                lockedService.unLock(transferNo, SysConstants.LockType.REFUND_PRE.getType());
            }
        }
    }

    private void checkWXResult(String tradeNo, RefundPre refundPre) {
        //更新微信支付结果表里的 支付结果 到 提现审核表中
        WxResult wxResult = wxResultService.get("outTradeNo", tradeNo);
        if (wxResult != null && (StrUtil.isEmpty(refundPre.getResultCode()) || !refundPre.getResultCode().equals(wxResult.getResultCode()))) {
            refundPre.setResultCode(wxResult.getResultCode());
            refundPre.setUpdateTime(new Date().getTime());
            this.save(refundPre);
        }
    }

    @Override
    public ServerResponse<Map<String, String>> review(String transferNo) {
        WxResult result = wxResultService.get("outTradeNo", transferNo);

        Query<RefundPre> query = refundPreService.createQuery();
        query.criteria("transferNo").equal(transferNo);

        UpdateOperations<RefundPre> updateOperations = refundPreService.createUpdateOperations();

        if (result == null || !"SUCCESS".equals(result.getResultCode())) {
            try {
                Map<String, String> map = getTransferInfo(transferNo);
                String return_code = map.get("return_code");
                String return_msg = map.get("return_msg");
                if ("SUCCESS".equals(return_code) && StrUtil.isEmpty(return_msg)) {
                    String result_code = map.get("result_code");
                    if ("SUCCESS".equals(result_code)) {
                        String status = map.get("status");           //SUCCESS:转账成功 FAILED:转账失败 PROCESSING:处理中
                        String reason = map.get("reason");
                        if ("SUCCESS".equals(status)) {
                            updateOperations.set("resultCode", status);
                            updateOperations.set("errCode", "");
                            updateOperations.set("errCodeDes", "");
                        } else {
                            updateOperations.set("errCode", status);
                            updateOperations.set("errCodeDes", reason);
                        }
                        refundPreService.update(query, updateOperations);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            updateOperations.set("resultCode", result.getResultCode());
            if (StrUtil.isNotEmpty(result.getErrCode())) {
                updateOperations.set("errCode", result.getErrCode());
            } else {
                updateOperations.set("errCode", "");
            }
            if (StrUtil.isNotEmpty(result.getErrCodeDes())) {
                updateOperations.set("errCodeDes", result.getErrCodeDes());
            } else {
                updateOperations.set("errCodeDes", "");
            }
            refundPreService.update(query, updateOperations);
        }
        return ServerResponse.createSuccess("查询成功");

    }

    private Map<String, String> getTransferInfo(String transferNo) throws Exception {
        RefundPre refundPre = refundPreService.get("transferNo", transferNo);
        WXPay wxpay = new WXPay(wxPayWechatCusConfig, wxPayConstants.notifyUrl, true, false);
        //1.0 拼凑企业支付需要的参数
        String appid = wxPayWechatCusConfig.getAppID();  //微信公众号的appid
        String mch_id = wxPayWechatCusConfig.getMchID(); //商户号
        if (refundPre.getDriverInfo() != null) {
            wxpay = new WXPay(wxPayWechatConfig, wxPayConstants.notifyUrl, true, false);
            mch_id = wxPayWechatConfig.getMchID();
            appid = wxPayWechatConfig.getAppID();
        }
        String nonce_str = WXPayUtil.generateNonceStr(32); //生成随机数
        //2.0 生成map集合
        Map<String, String> packageParams = new HashMap<>();
        packageParams.put("appid", appid);         //微信公众号的appid
        packageParams.put("mch_id", mch_id);       //商户号
        packageParams.put("nonce_str", nonce_str);  //随机生成后数字，保证安全性
        packageParams.put("partner_trade_no", refundPre.getTransferNo()); //生成商户订单号
        String sign;
        if (refundPre.getDriverInfo() != null) {
            sign = WXPayUtil.generateSignature(packageParams, wxPayWechatConfig.getKey(), WXPayConstants.SignType.MD5);
        } else {
            sign = WXPayUtil.generateSignature(packageParams, wxPayWechatCusConfig.getKey(), WXPayConstants.SignType.MD5);
        }
        packageParams.put("sign", sign);
        return wxpay.gettransferinfo(packageParams);
        /*
        return_code	是	SUCCESS	String(16) SUCCESS/FAIL 此字段是通信标识，非付款标识，付款是否成功需要查看result_code来判断
        return_msg	否	签名失败	String(128) 返回信息，如非空，为错误原因

        以下字段在return_code为SUCCESS的时候有返回
        业务结果	    result_code	    是	SUCCESS	String(16)	SUCCESS/FAIL ，非付款标识，付款是否成功需要查看status字段来判断
        错误代码	    err_code	    否	SYSTEMERROR	String(32)	错误码信息
        错误代码描述	err_code_des	否	系统错误	String(128)	结果信息描述

        以下字段在return_code 和result_code都为SUCCESS的时候有返回
        商户单号	partner_trade_no	是	10000098201411111234567890	String(32)	商户使用查询API填写的单号的原路返回.
        Appid	appid	            是	wxe062425f740d30d8	String(128)	商户号的appid
        商户号	mch_id	            是	10000098	String(32)	微信支付分配的商户号
        付款单号	detail_id	        是	1000000000201503283103439304	String(64)	调用付款API时，微信支付系统内部产生的单号
        转账状态	status	            是	SUCCESS	string(16) SUCCESS:转账成功, FAILED:转账失败, PROCESSING:处理中
        失败原因	reason	            否	余额不足	String(128)	如果失败则有失败原因
        收款用户openid	openid	    是	oxTWIuGaIt6gTKsQRLau2M0yL16E	String(64)	转账的openid
        收款用户姓名	transfer_name	否	马华	String(64)	收款用户姓名
        付款金额	payment_amount	    是	5000	int	付款金额单位为“分”
        转账时间	transfer_time	    是	2015-04-21 20:00:00	String(32)	发起转账的时间
        付款成功时间	payment_time	是	2015-04-21 20:01:00	String(32)	付款成功时间
        付款备注	desc	            是	车险理赔	String(100)	付款备注
        */
    }

}
