package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.driver.entity.OrderPosition;

public interface IOrderPositionService extends IBaseMongoService<OrderPosition> {
    //记录签到时GPS
    void addPositionWherePunchClock(Order order, Integer outOrIn);
    //checking == 1时GPS
    void addPositionWhereChecking1(Order order, Integer outOrIn);
    //checking == 3时GPS
    void addPositionWhereChecking3(Order order, Integer outOrIn);
}
