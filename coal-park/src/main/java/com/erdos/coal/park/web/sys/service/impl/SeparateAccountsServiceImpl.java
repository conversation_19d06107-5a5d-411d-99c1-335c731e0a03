package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.web.sys.dao.ISeparateAccountsDao;
import com.erdos.coal.park.web.sys.entity.SeparateAccounts;
import com.erdos.coal.park.web.sys.pojo.SeparateAccountsData;
import com.erdos.coal.park.web.sys.service.ISeparateAccountsService;
import com.erdos.coal.utils.IdUnit;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.aggregation.Projection;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Iterator;

import static dev.morphia.aggregation.Group.*;

@Service("separateAccountsService")
public class SeparateAccountsServiceImpl extends BaseMongoServiceImpl<SeparateAccounts, ISeparateAccountsDao> implements ISeparateAccountsService {
    @Override
    public Integer sumSeparateAccountsWithMonth(String account) {
        Date startTime = IdUnit.weeHours1(IdUnit.getMonthFirstDay().getTime(), "00:00:00", 0);
        Query<SeparateAccounts> query = this.createQuery();
        query.criteria("createTime").greaterThanOrEq(startTime);
        query.criteria("account").equal(account);

        AggregationPipeline pipeline = this.createAggregation();
        pipeline.match(query);
        pipeline.group(
                id(grouping("outOrderNo")),
                grouping("amount",first("amount")),
                grouping("account",first("account"))
        );
        pipeline.project(
                Projection.projection("account"),
                Projection.projection("amount")
        );
        pipeline.group(
                id(grouping("account")),
                grouping("amount",sum("amount"))
        );

        Iterator<SeparateAccountsData> item = pipeline.aggregate(SeparateAccountsData.class);

        while (item.hasNext()){
            SeparateAccountsData accounts = item.next();
            return accounts.getAmount();
        }

        return 0;
    }
}
