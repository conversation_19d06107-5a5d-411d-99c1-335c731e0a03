package com.erdos.coal.park.api.manage.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_user_token", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("token")}, options = @IndexOptions(unique = true)),
        @Index(fields = {@Field("userId")}),
        @Index(fields = {@Field("userType")})
})
public class UserTokenRecord extends BaseMongoInfo {
    private String userId;
    private String userType;
    private String token;

    //限制用户短期连续提交 或 网络抖动问题
    private Long time1 = 0L;     //客商添加货运信息接口
    private Long time2 = 0L;     //客商废除货运信息接口
    private Long time3 = 0L;     //客商批量添加货运信息接口
    private Long time4 = 0L;     //客商修改货运信息接口
    private Long time5 = 0L;     //客商修改货运信息运往地接口
    private Long time6 = 0L;     //客商分享货运信息给友商接口
    private Long time7 = 0L;     //客商从友商回收货运信息接口
    private Long time8 = 0L;     //友商退回货运信息接口
    private Long time9 = 0L;     //客商修改订单接口
    private Long time10 = 0L;     //客商修改订单运往地接口
    private Long time11 = 0L;     //客商指派订单给司机接口
    private Long time12 = 0L;     //司机接受或拒绝订单接口
    private Long time13 = 0L;     //司机扫码接单接口
    private Long time14 = 0L;     //司机抢单接口
    private Long time15 = 0L;     //司机订单提交预约接口
    private Long time16 = 0L;     //司机修改订单车牌号接口
    private Long time17 = 0L;     //司机订单打卡签到接口
    private Long time18 = 0L;     //司机订单上传防疫照片接口
    private Long time19 = 0L;     //司机提交防疫信息接口
    private Long time20 = 0L;     //司机撤单接口
    private Long time21 = 0L;     //客商添加货运信息接口 新
    private Long time22 = 0L;     //客商废除货运信息接口 新
    private Long time23 = 0L;     //司机撤单接口 新
    private Long time24 = 0L;     //司机接受或拒绝订单接口 新
    private Long time25 = 0L;     //司机扫码接单接口 新
    private Long time26 = 0L;     //客商拆分企业单接口 新
    private Long time27 = 0L;     //企业系统推送订单号-装卸

    private Long time28 = 0L;     //司机同意拒绝订单
    private Long time29 = 0L;     //司机接单

    private Long time30 = 0L;   //用户提现申请接口

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getTime1() {
        return time1;
    }

    public void setTime1(Long time1) {
        this.time1 = time1;
    }

    public Long getTime2() {
        return time2;
    }

    public void setTime2(Long time2) {
        this.time2 = time2;
    }

    public Long getTime3() {
        return time3;
    }

    public void setTime3(Long time3) {
        this.time3 = time3;
    }

    public Long getTime4() {
        return time4;
    }

    public void setTime4(Long time4) {
        this.time4 = time4;
    }

    public Long getTime5() {
        return time5;
    }

    public void setTime5(Long time5) {
        this.time5 = time5;
    }

    public Long getTime6() {
        return time6;
    }

    public void setTime6(Long time6) {
        this.time6 = time6;
    }

    public Long getTime7() {
        return time7;
    }

    public void setTime7(Long time7) {
        this.time7 = time7;
    }

    public Long getTime8() {
        return time8;
    }

    public void setTime8(Long time8) {
        this.time8 = time8;
    }

    public Long getTime9() {
        return time9;
    }

    public void setTime9(Long time9) {
        this.time9 = time9;
    }

    public Long getTime10() {
        return time10;
    }

    public void setTime10(Long time10) {
        this.time10 = time10;
    }

    public Long getTime11() {
        return time11;
    }

    public void setTime11(Long time11) {
        this.time11 = time11;
    }

    public Long getTime12() {
        return time12;
    }

    public void setTime12(Long time12) {
        this.time12 = time12;
    }

    public Long getTime13() {
        return time13;
    }

    public void setTime13(Long time13) {
        this.time13 = time13;
    }

    public Long getTime14() {
        return time14;
    }

    public void setTime14(Long time14) {
        this.time14 = time14;
    }

    public Long getTime15() {
        return time15;
    }

    public void setTime15(Long time15) {
        this.time15 = time15;
    }

    public Long getTime16() {
        return time16;
    }

    public void setTime16(Long time16) {
        this.time16 = time16;
    }

    public Long getTime17() {
        return time17;
    }

    public void setTime17(Long time17) {
        this.time17 = time17;
    }

    public Long getTime18() {
        return time18;
    }

    public void setTime18(Long time18) {
        this.time18 = time18;
    }

    public Long getTime19() {
        return time19;
    }

    public void setTime19(Long time19) {
        this.time19 = time19;
    }

    public Long getTime20() {
        return time20;
    }

    public void setTime20(Long time20) {
        this.time20 = time20;
    }

    public Long getTime21() {
        return time21;
    }

    public void setTime21(Long time21) {
        this.time21 = time21;
    }

    public Long getTime22() {
        return time22;
    }

    public void setTime22(Long time22) {
        this.time22 = time22;
    }

    public Long getTime23() {
        return time23;
    }

    public void setTime23(Long time23) {
        this.time23 = time23;
    }

    public Long getTime24() {
        return time24;
    }

    public void setTime24(Long time24) {
        this.time24 = time24;
    }

    public Long getTime25() {
        return time25;
    }

    public void setTime25(Long time25) {
        this.time25 = time25;
    }

    public Long getTime26() {
        return time26;
    }

    public void setTime26(Long time26) {
        this.time26 = time26;
    }

    public Long getTime27() {
        return time27;
    }

    public void setTime27(Long time27) {
        this.time27 = time27;
    }

    public Long getTime28() {
        return time28;
    }

    public void setTime28(Long time28) {
        this.time28 = time28;
    }

    public Long getTime29() {
        return time29;
    }

    public void setTime29(Long time29) {
        this.time29 = time29;
    }

    public Long getTime30() {
        return time30;
    }

    public void setTime30(Long time30) {
        this.time30 = time30;
    }
}
