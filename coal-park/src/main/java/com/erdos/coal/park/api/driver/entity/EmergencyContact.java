package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

@Entity(value = "t_emergency_contact", noClassnameStored = true)
public class EmergencyContact extends BaseMongoInfo {
    private String did;         //司机id
    private String name;        //应急联系人姓名
    private String mobile;      //应急联系人电话号码

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
