package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.ICustomerAccountDao;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.bson.Document;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service("customerAccountService")
public class CustomerAccountServiceImpl extends BaseMongoServiceImpl<CustomerAccount, ICustomerAccountDao> implements ICustomerAccountService {
    @Override
    public ServerResponse<CustomerAccount> getCustomerAccount() {
        String cid = ShiroUtils.getUserId();

        CustomerAccount result = new CustomerAccount();
        result.setCid(cid);
        //将账户中的金额 计算单位由 分 转化为 元
        result.setTotalFee(getAvailableFee(cid).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));

        return ServerResponse.createSuccess("查询成功", result);
    }

    //计算账户可用总金额
    @Override
    public BigDecimal getAvailableFee(String cid) {
        Query<CustomerAccount> query = this.createQuery();
        query.filter("cid", cid);
        query.order(Sort.ascending("createTime"));
        List<CustomerAccount> accountList = this.list(query);
        BigDecimal availableFee = new BigDecimal("0");
        for (CustomerAccount account : accountList) {
            availableFee = availableFee.add(account.getTotalFee());//单位为分
        }
        return availableFee;
    }

    @Override
    public Document createCusAccountDoc(String cid, BigDecimal fee, Integer type, String oid, Date time) {
        CustomerAccount cAccount = new CustomerAccount();
        cAccount.setCid(cid);
        cAccount.setType(type);
        cAccount.setOid(oid);
        cAccount.setUpdateTime(time.getTime());
        Document cAccountDoc = Document.parse(JSONObject.toJSONString(cAccount));
        cAccountDoc.append("totalFee", fee);
        cAccountDoc.append("createTime", time);
        return cAccountDoc;
    }

    @Override
    public Document createCusAccountDoc(String cid, BigDecimal fee, Integer type, String outTradeNo, String transactionId, Date time) {
        CustomerAccount cAccount = new CustomerAccount();
        cAccount.setCid(cid);
        cAccount.setType(type);
        cAccount.setOutTradeNo(outTradeNo);
        cAccount.setTransactionId(transactionId);
        cAccount.setUpdateTime(time.getTime());
        Document cAccountDoc = Document.parse(JSONObject.toJSONString(cAccount));
        cAccountDoc.append("totalFee", fee);
        cAccountDoc.append("createTime", time);
        return cAccountDoc;
    }

    @Override
    public ServerResponse<EGridResult> customerAccountList(Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        Query<CustomerAccount> query = this.createQuery();
        query.filter("cid", cid);
        query.order(Sort.descending("createTime"));
        EGridResult<CustomerAccount> eGridResult = this.findPage(page, rows, query);

        List<CustomerAccount> accountList = eGridResult.getRows();
        for (CustomerAccount account : accountList) {
            //将账户中的金额 计算单位由 分 转化为 元
            account.setTotalFee(account.getTotalFee().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        }
        return ServerResponse.createSuccess("查询成功", new EGridResult(eGridResult.getTotal(), accountList));
    }
}
