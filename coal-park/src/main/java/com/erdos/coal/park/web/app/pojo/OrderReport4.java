package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class OrderReport4 implements Serializable {
    private String outUnitCode;                     //发货单位编码
    private String outUnitName;                     //发货单位名称
    private String inUnitCode;                      //收货单位编码
    private String inUnitName;                      //收货单位名称
    private String outDefaultDownUnit;             //发货单位 默认二级单位编码
    private String outSubName;                      //发货单位 默认二级单位名称
    private String inDefaultDownUnit;              //收货单位 默认二级单位编码
    private String inSubName;                       //收货单位 默认二级单位名称

    private String time1;
    private String time3;
    private String time5;

    private Integer count;                          //车数
    private Double outFee0;     //平台收费（分）
    private Double inFee0;      //平台收费（分）
    private Double amountToFee0;      //平台收费（分）
    private Double outFee5;     //第三方收费（分）
    private Double inFee5;      //第三方收费（分）
    private Double amountToFee5;      //第三方收费（分）


    private List<String> counts;

    public List<String> getCounts() {
        return counts;
    }

    public void setCounts(List<String> counts) {
        this.counts = counts;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getTime1() {
        return time1;
    }

    public void setTime1(String time1) {
        this.time1 = time1;
    }

    public String getTime3() {
        return time3;
    }

    public void setTime3(String time3) {
        this.time3 = time3;
    }

    public String getTime5() {
        return time5;
    }

    public void setTime5(String time5) {
        this.time5 = time5;
    }

    public Integer getCount() {
        return counts.size();
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getOutFee0() {
        return outFee0;
    }

    public void setOutFee0(Double outFee0) {
        this.outFee0 = outFee0;
    }

    public Double getInFee0() {
        return inFee0;
    }

    public void setInFee0(Double inFee0) {
        this.inFee0 = inFee0;
    }

    public Double getAmountToFee0() {
        return outFee0 + inFee0;
    }

    public void setAmountToFee0(Double amountToFee0) {
        this.amountToFee0 = amountToFee0;
    }

    public Double getOutFee5() {
        return outFee5;
    }

    public void setOutFee5(Double outFee5) {
        this.outFee5 = outFee5;
    }

    public Double getInFee5() {
        return inFee5;
    }

    public void setInFee5(Double inFee5) {
        this.inFee5 = inFee5;
    }

    public Double getAmountToFee5() {
        return outFee5 + inFee5;
    }

    public void setAmountToFee5(Double amountToFee5) {
        this.amountToFee5 = amountToFee5;
    }
}
