package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.park.api.business.entity.TradeUnit;

import java.util.List;
import java.util.Map;

public interface ITradeUnitService extends IBaseMongoService<TradeUnit> {
    boolean add(List<TradeUnit> tradeUnits);

    List<Integer> edit(Map<Integer, TradeUnit> updateTradeUnitMap);

    boolean del(List<String> bizUnitCodes);
}
