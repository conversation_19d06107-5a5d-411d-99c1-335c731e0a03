package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.app.entity.DistrictCode;
import org.springframework.web.multipart.MultipartFile;

public interface IDistrictCodeService extends IBaseMongoService<DistrictCode> {
    //区县码表列表
    ServerResponse<EGridResult> districtCodeList(Integer page, Integer rows);

    //区县码表excel上传
    ServerResponse<String> upDistrictCode(MultipartFile file);

    //区县码表新增
    ServerResponse<String> addDistrictCode(DistrictCode districtCode);

    //区县码表修改
    ServerResponse<String> editDistrictCode(DistrictCode districtCode);

}
