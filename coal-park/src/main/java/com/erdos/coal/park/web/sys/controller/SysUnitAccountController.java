package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import com.erdos.coal.park.web.sys.service.ISysUnitAccountService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

@RestController
@RequestMapping("/web/sys/unitAccount")
public class SysUnitAccountController extends BaseController {
    @Resource
    private ISysUnitAccountService sysUnitAccountService;

    @PostMapping("/save_unit_account")
    public ServerResponse<String> addHandler(@RequestBody UnitData data) throws GlobalException {
        return sysUnitAccountService.addUnitAccount(data);
    }

    @PostMapping("/details_account")
    public ServerResponse<EGridResult> detailsAccount(Integer page, Integer rows) throws GlobalException {
        return sysUnitAccountService.detailsAccount(page, rows);
    }

    @PostMapping("/getAvailableFee")
    public ServerResponse<BigDecimal> getAvailableFee() throws GlobalException {
        return sysUnitAccountService.responseAvailableFee();
    }
}
