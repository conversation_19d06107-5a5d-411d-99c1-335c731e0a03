package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;
import com.erdos.coal.park.web.sys.pojo.PwdData;
import com.erdos.coal.park.web.sys.pojo.UnitData;

import java.util.List;
import java.util.Map;

public interface ISysUnitService extends IBaseMongoService<SysUnit> {

    EGridResult loadGrid(Integer page, Integer rows, Integer type);

    List<SysUnit> searchByUnitCodes(List<String> unitCodes);

    ServerResponse<String> deleteUnit(UnitData sysUnit);

    ServerResponse<String> addUnit(UnitData sysUnit);
    ServerResponse<String> addUnit2(UnitData sysUnit);

    ServerResponse<String> editUnit(UnitData sysUnit);
    ServerResponse<String> editUnit2(UnitData sysUnit);

    ServerResponse<String> saveGeofenceUnit(UnitData sysUnit);
    ServerResponse<String> saveGeofenceUnit2(UnitData sysUnit);
    ServerResponse<String> saveGeofenceUnit3(UnitData sysUnit);

    ServerResponse<String> onOrOffGeofenceUnit();
    ServerResponse<String> onOrOffGeofenceUnit2();

    ServerResponse<String> delGeofenceUnit();
    ServerResponse<String> delGeofenceUnit2();
    ServerResponse<String> delGeofenceUnit3();

    ServerResponse<List<CheckBoxData>> getQuarantineInfo();

    Map<String, Object> getPQInfoG(String unitCode);

    SysUnit findUserByName(String name);

    List<String> findPermissions(String name);

    //小程序维护二级单位地理围栏 查询单位
    ServerResponse<List<CompanyUnit>> searchUnit(String unitCode);

    //小程序维护二级单位地理围栏(单点)
    ServerResponse<String> saveUnitGeoCenter(String subCode, String longitude, String latitude, String radius);
    //小程序维护二级单位地理围栏(多点)
    ServerResponse<String> saveUnitGeoCenter2(String subCode, String longitude, String latitude, String radius);

    //二级单位搜索框查询
    ServerResponse<List<SysUnit>> searchSubUnitByName(String name);

    //查询一二级单位
    List<SysUnit> getSysUnitListByType(Integer type, String pcode);

    //一级单位修改用户名和密码
    ServerResponse<String> editUNitPwd(PwdData pwdData);

    //二级单位 采掘区域 编辑
    ServerResponse<String> saveSubUnitExcavateArea(UnitData sysUnit);

    // 一级单位 代收装卸费
    ServerResponse<String> editUnitHandlingCost(UnitData sysUnit);
}
