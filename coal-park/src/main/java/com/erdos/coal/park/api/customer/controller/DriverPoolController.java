package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP司机池管理接口列表"
@RestController
@RequestMapping("/api/cus/driver_pool")
public class DriverPoolController extends BaseController {
    @Resource
    private IDriverPoolService driverPoolService;

    @InvokeLog(description = "查询客商司机池中司机 接口", printReturn = false) //日志
    @PostMapping(value = "/driver_list_data")
    public ServerResponse<EGridResult> driverPoolListDataHandler(
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
        //return driverPoolService.driverPoolListData(null, null, page, rows);
    }

    @InvokeLog(description = "司机池中移除司机 接口") //日志
    @PostMapping(value = "/delete_driver")
    public ServerResponse<String> driverPoolDelDvrHandler(
            @RequestParam(value = "id") String id,         //"司机在司机池中的编号"
            @RequestParam(value = "mobile") String mobile  //"司机手机号"
    ) throws GlobalException {
        return driverPoolService.driverPoolDelDvr(mobile, id);
    }
}