package com.erdos.coal.park.api.business.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.httpclient.HttpResult;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.business.service.ICompanyOrderService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service("companyOrderService")
public class CompanyOrderServiceImpl implements ICompanyOrderService {
    @Resource
    private IHttpAPIService httpAPIService;

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 企业系统服务器地址+不同的功能接口+盐
     */
    private String urlEncrypt(String url, Integer type) {
        switch (type) {
            case 1:     //同步订单信息到企业系统
                url = url + "/app/external/interface/get_bill_pub_list.do";
                break;
            /*case 2:     //修改企业系统订单
                url = url + "/app/external/interface/edit_bill_pub_list.do";
                break;*/
            case 3:     //修改企业系统订单
                url = url + "/app/external/interface/edit_bill_pub_list.do";
                break;
            case 4:     //查询企业系统收费金额
                url = url + "/app/external/interface/get_falewithholding_fee.do";
                break;
        }
        return url;
    }

    /**
     * 请求企业业务系统下单，并返回billCode
     * map.put("userCode","DW00010003");
     * map.put("bizContractCode","TODW00010006");
     * map.put("variety","品种1");
     * map.put("defaultDownUnit","DW0001001");
     * map.put("defaultArea","DW0001001001");
     * map.put("min","1");
     * map.put("max","10");
     * map.put("bizType",1);        //0采购，1销售
     * map.put("payValue",0.0);
     * map.put("limitValue",0.0);
     * map.put("carNum","京A-12345"); 单车订单车牌号必须有，批量订单车牌号可以为空
     */
    @Override
    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public CompletableFuture synOrderMsg(String url, Map<String, Object> map, int type) {
        if (StrUtil.isEmpty(url)) return CompletableFuture.completedFuture("url空");//return "url空";
        url = urlEncrypt(url, type);

        try {
            map.put("sign", IdUnit.getSign(map));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String jsonStr = JSONObject.toJSONString(map);

        HttpResult result = null;
        try {
            result = httpAPIService.sendPost(url, jsonStr);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        if (result == null || result.getCode() != 200) {
            return CompletableFuture.completedFuture("网络异常");//return "网络异常";
        } else {
            String bodyStr = result.getBody();
            JSONObject jsonObject = JSONObject.parseObject(bodyStr);

            //订单修改 返回成功或失败 是 通过判断 resultList是否为空
            if (StrUtil.isNotEmpty(jsonObject.get("resultList"))) {  //resultList内容是修改成功的订单
                JSONArray array = jsonObject.getJSONArray("resultList");
                List<Object> strList = array.subList(0, array.size());
                return CompletableFuture.completedFuture(strList);
            }

            //企业系统下单，成功status=1，失败status=0
            if (StrUtil.isNotEmpty(jsonObject.get("status")) && jsonObject.get("status").equals(1)) {
                return null;
            } else {
                logger.error("busTaskExecutor+synOrderMsg" + bodyStr);
                if (StrUtil.isEmpty(jsonObject.get("msg"))) return CompletableFuture.completedFuture("网络派单出错");
                return CompletableFuture.completedFuture((String) jsonObject.get("msg"));
            }
        }
    }

    //请求企业系统，查询单位要求代扣的金额
    @Override
    public Map<String, Object> queryCompanyCost(String url, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        if (StrUtil.isEmpty(url)) {
            resultMap.put("msg", "url空");
            return resultMap;
        }
        url = urlEncrypt(url, 4);

        try {
            map.put("sign", IdUnit.getSign(map));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String jsonStr = JSONObject.toJSONString(map);

        HttpResult result = null;
        try {
            result = httpAPIService.sendPost(url, jsonStr);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        if (result == null || result.getCode() != 200) {
            resultMap.put("msg", "网络异常");
            return resultMap;
        } else {
            String bodyStr = result.getBody();
            JSONObject jsonObject = JSONObject.parseObject(bodyStr);
            if (jsonObject.get("status").equals(0)) {
                resultMap.put("msg", "接受请求出错：" + jsonObject.get("msg"));
                return resultMap;
            }
            Double fUnitFee = jsonObject.getDouble("fUnitFee");
            Double sUnitFee = jsonObject.getDouble("sUnitFee");
            resultMap.put("fee1", new BigDecimal(String.valueOf(fUnitFee)).multiply(new BigDecimal(100)).intValue());//jsonObject.get("fUnitFee")); //返回查询到的一级单位要求代扣金额数,单位为 分
            resultMap.put("fee2", new BigDecimal(String.valueOf(sUnitFee)).multiply(new BigDecimal(100)).intValue());//jsonObject.get("sUnitFee")); //返回查询到的二级单位要求代扣金额数,单位为 分
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> queryCompanyDelivery(String url, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        if (StrUtil.isEmpty(url)) {
            resultMap.put("msg", "url空");
            return resultMap;
        }
        url = url + "/app/external/interface/create_delivery_order";

        try {
            map.put("sign", IdUnit.getSign(map));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String jsonStr = JSONObject.toJSONString(map);

        HttpResult result = null;
        try {
            result = httpAPIService.sendPost(url, jsonStr);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        if (result == null || result.getCode() != 200) {
            resultMap.put("errorMsg", "网络异常");
            return resultMap;
        } else {
            String bodyStr = result.getBody();
            try {
                JSONObject jsonObject = JSONObject.parseObject(bodyStr);
                if (jsonObject.get("status").equals(0)) {
    //                resultMap.put("errorMsg", "接受请求出错：" + jsonObject.get("msg"));
                    resultMap.put("errorMsg", jsonObject.get("msg"));
                    return resultMap;
                } else {
                    resultMap.put("successMsg", jsonObject.get("msg"));
                }
            } catch (Exception e) {
                resultMap.put("errorMsg", "请求返回错误");
                logger.error("queryCompanyDelivery 异常返回结果 ： ["+bodyStr+"]");
            }
            return resultMap;
        }
    }

    @Override
    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public Map<String, Object> queryCompanyDeliveryPound(String url, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        if (StrUtil.isEmpty(url)) {
            resultMap.put("msg", "url空");
            return resultMap;
        }
            url = url + "/app/external/interface/create_delivery_order_pound";

        try {
            map.put("sign", IdUnit.getSign(map));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String jsonStr = JSONObject.toJSONString(map);

        HttpResult result = null;
        try {
            result = httpAPIService.sendPost(url, jsonStr);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        if (result == null || result.getCode() != 200) {
            resultMap.put("errorMsg", "网络异常");
            return resultMap;
        } else {
            String bodyStr = result.getBody();
            try {
                JSONObject jsonObject = JSONObject.parseObject(bodyStr);
                if (jsonObject.get("status").equals(0)) {
                    resultMap.put("errorMsg", jsonObject.get("msg"));
                    return resultMap;
                } else {
                    resultMap.put("successMsg", jsonObject.get("msg"));
                }
            } catch (Exception e) {
                resultMap.put("errorMsg", "请求返回错误");
                logger.error("queryCompanyDeliveryPound 异常返回结果 ： ["+bodyStr+"]");
            }
            return resultMap;
        }
    }

    @Override
    @Async("busTaskExecutor")   //异步执行和业务系统的交互
//    public Map<String, Object> queryCompanyRevokeDeliveryOrder(String url, Map<String, Object> map) {
    public CompletableFuture queryCompanyRevokeDeliveryOrder(String url, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        if (StrUtil.isEmpty(url)) return CompletableFuture.completedFuture("单位缺少IP配置");

        url = url + "/app/external/interface/revoke_delivery_order";

        try {
            map.put("sign", IdUnit.getSign(map));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String jsonStr = JSONObject.toJSONString(map);

        HttpResult result = null;
        try {
            result = httpAPIService.sendPost(url, jsonStr);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        if (result == null || result.getCode() != 200) {
            return CompletableFuture.completedFuture("网络异常");
        } else {
            String bodyStr = result.getBody();
            try {
                JSONObject jsonObject = JSONObject.parseObject(bodyStr);
                if (jsonObject.get("status").equals(0)) {
                    return CompletableFuture.completedFuture(jsonObject.getString("msg"));
                } else {
                    // resultMap.put("successMsg", jsonObject.get("msg"));
                    return null;
                }
            } catch (Exception e) {
                resultMap.put("errorMsg", "请求返回错误");
                logger.error("queryCompanyRevokeDeliveryOrder 异常返回结果 ： ["+bodyStr+"]");
                return CompletableFuture.completedFuture("请求返回错误");
            }
//            return resultMap;
        }
    }
}
