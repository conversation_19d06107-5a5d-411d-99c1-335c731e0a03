package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.business.service.IContractService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.driver.dao.IOrderPositionDao;
import com.erdos.coal.park.api.driver.entity.OrderPosition;
import com.erdos.coal.park.api.driver.pojo.Position;
import com.erdos.coal.park.api.driver.service.IOrderPositionService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.geo.GeoJson;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service("orderPositionService")
public class OrderPositionServiceImpl extends BaseMongoServiceImpl<OrderPosition, IOrderPositionDao> implements IOrderPositionService {
    @Resource
    private IContractService contractService;

    private Map<String, Object> getCurrentPosition(String carNum) {
        /*
         * {"success":true,"msg":"车辆位置获取成功！","data":{"course":323,"gpsTime":"2023-04-10 17:33:51","recTime":"2023-04-10 17:34:08","latitude":36.312763333333336,"longitude":112.71584333333334,"milesKM":118755.0,"simId":"晋KP6372","speed":65.0,"power":null,"alarmState":0,"carState":0,"terminalState":1,"truckNo":"晋KP6372","speedKm":6.5,"load":0.0,"baiDuPosition":{"longitude":112.72869285835598,"latitude":36.318551195650066},"googlePosition":{"longitude":112.72213997199539,"latitude":36.312755762689775},"elevation":0,"oilMass":0,"parkingTime":null,"dataSource":0,"acc":1,"positionStatusStr":"ACC开;未定位;北纬;东经;无载重传感器;","haveLoad":true,"offline":false,"located":true,"parkingMinuteTime":0},"total":0}
         * */
        String jsonStr = contractService.getCurrentPosition(carNum);
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        boolean success = jsonObject.getBooleanValue("success");
        Position position = new Position();
        Point point = null;
        if (success) {
            JSONObject data = jsonObject.getJSONObject("data");
            position.setLongitude(data.getString("longitude"));
            position.setLatitude(data.getString("latitude"));
            position.setSpeed(data.getString("speedKm"));
            JSONObject baiDuPosition = data.getJSONObject("baiDuPosition");
            position.setBaiduLong(baiDuPosition.getString("longitude"));
            position.setBaiduLat(baiDuPosition.getString("latitude"));
            JSONObject googlePosition = data.getJSONObject("googlePosition");
            position.setGoogleLong(googlePosition.getString("longitude"));
            position.setGoogleLat(googlePosition.getString("latitude"));

            point = GeoJson.point(Double.valueOf(data.getString("latitude")), Double.valueOf(data.getString("longitude")));
        } else {
            String msg = jsonObject.getString("msg");
            position.setMsg(msg);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("position", position);
        result.put("point", point);

        return result;
    }

    @Override
    public void addPositionWherePunchClock(Order order, Integer outOrIn) {
        Map<String, Object> map = getCurrentPosition(order.getCarNum());
        Position position = (Position) map.get("position");
        Point point = (Point) map.get("point");

        OrderPosition orderPosition = this.get("oid", order.getOid());
        if (orderPosition == null) {
            orderPosition = new OrderPosition();
            orderPosition.setOid(order.getOid());
            if (StrUtil.isNotEmpty(order.getCarNum())) orderPosition.setCarNum(order.getCarNum());
            if (StrUtil.isNotEmpty(order.getOutBillCode())) orderPosition.setOutBillCode(order.getOutBillCode());
            if (StrUtil.isNotEmpty(order.getInBillCode())) orderPosition.setInBillCode(order.getInBillCode());
            if (outOrIn == 0) {
                orderPosition.setPosition_out(position);
                if (point != null) orderPosition.setGeometry_out(point);
            } else if (outOrIn == 1) {
                orderPosition.setPosition_in(position);
                if (point != null) orderPosition.setGeometry_in(point);
            }
            this.save(orderPosition);
        } else {
            Query<OrderPosition> query = this.createQuery();
            query.criteria("oid").equal(order.getOid());
            UpdateOperations<OrderPosition> updateOperations = this.createUpdateOperations();
            if (outOrIn == 0) {
                updateOperations.set("position_out", position);
                if (point != null) updateOperations.set("geometry_out", point);
            } else if (outOrIn == 1) {
                updateOperations.set("position_in", position);
                if (point != null) updateOperations.set("geometry_in", point);
            }
            this.update(query, updateOperations);
        }
    }

    @Override
    public void addPositionWhereChecking1(Order order, Integer outOrIn) {
        Map<String, Object> map = getCurrentPosition(order.getCarNum());
        Position position = (Position) map.get("position");
        Point point = (Point) map.get("point");

        OrderPosition orderPosition = this.get("oid", order.getOid());
        if (orderPosition == null) {
            orderPosition = new OrderPosition();
            orderPosition.setOid(order.getOid());
            if (StrUtil.isNotEmpty(order.getOutBillCode())) orderPosition.setOutBillCode(order.getOutBillCode());
            if (StrUtil.isNotEmpty(order.getCarNum())) orderPosition.setCarNum(order.getCarNum());
            if (StrUtil.isNotEmpty(order.getInBillCode())) orderPosition.setInBillCode(order.getInBillCode());
            if (outOrIn == 0) {
                orderPosition.setPosition_c1_out(position);
                if (point != null) orderPosition.setGeometry_c1_out(point);
            } else if (outOrIn == 1) {
                orderPosition.setPosition_c1_in(position);
                if (point != null) orderPosition.setGeometry_c1_in(point);
            }
            this.save(orderPosition);
        } else {
            Query<OrderPosition> query = this.createQuery();
            query.criteria("oid").equal(order.getOid());
            UpdateOperations<OrderPosition> updateOperations = this.createUpdateOperations();
            if (outOrIn == 0) {
                updateOperations.set("position_c1_out", position);
                if (point != null) updateOperations.set("geometry_c1_out", point);
            } else if (outOrIn == 1) {
                updateOperations.set("position_c1_in", position);
                if (point != null) updateOperations.set("geometry_c1_in", point);
            }
            this.update(query, updateOperations);
        }
    }

    @Override
    public void addPositionWhereChecking3(Order order, Integer outOrIn) {
        Map<String, Object> map = getCurrentPosition(order.getCarNum());
        Position position = (Position) map.get("position");
        Point point = (Point) map.get("point");

        OrderPosition orderPosition = this.get("oid", order.getOid());
        if (orderPosition == null) {
            orderPosition = new OrderPosition();
            orderPosition.setOid(order.getOid());
            if (StrUtil.isNotEmpty(order.getOutBillCode())) orderPosition.setOutBillCode(order.getOutBillCode());
            if (StrUtil.isNotEmpty(order.getInBillCode())) orderPosition.setInBillCode(order.getInBillCode());
            if (StrUtil.isNotEmpty(order.getCarNum())) orderPosition.setCarNum(order.getCarNum());
            if (outOrIn == 0) {
                orderPosition.setPosition_c3_out(position);
                if (point != null) orderPosition.setGeometry_c3_out(point);
            } else if (outOrIn == 1) {
                orderPosition.setPosition_c3_in(position);
                if (point != null) orderPosition.setGeometry_c3_in(point);
            }
            this.save(orderPosition);
        } else {
            Query<OrderPosition> query = this.createQuery();
            query.criteria("oid").equal(order.getOid());
            UpdateOperations<OrderPosition> updateOperations = this.createUpdateOperations();
            if (outOrIn == 0) {
                updateOperations.set("position_c3_out", position);
                if (point != null) updateOperations.set("geometry_c3_out", point);
            } else if (outOrIn == 1) {
                updateOperations.set("position_c3_in", position);
                if (point != null) updateOperations.set("geometry_c3_in", point);
            }
            this.update(query, updateOperations);
        }
    }
}
