package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysMenu;
import com.erdos.coal.park.web.sys.service.ISysMenuService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/web/sys/menu")
public class SysMenuController extends BaseController {

    @Resource
    private ISysMenuService sysMenuService;

    @RequestMapping("/menu_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(sysMenuService.loadTreeGrid(page, rows));
    }

    // vue -------------------------------------------------------------------------------------------------------------
    @RequestMapping("/tree_object")
    public ServerResponse<SysMenu> treeObjHandler() throws GlobalException {
        return ServerResponse.createSuccess(sysMenuService.getTreeObject(null));
    }

    @PostMapping("/tree_append_node")
    public ServerResponse treeNodeAddHandler(@RequestBody SysMenu sysMenu) throws GlobalException {
        return sysMenuService.appendMenu(sysMenu);
    }

    @PostMapping("/tree_edit_node")
    public ServerResponse treeNodeEditHandler(@RequestBody SysMenu sysMenu) throws GlobalException {
        return sysMenuService.editMenu(sysMenu);
    }

    @PostMapping("/tree_delete_node")
    public ServerResponse treeNodeDeleteHandler(String id) throws GlobalException {
        return sysMenuService.deleteMenu(id);
    }

    // vue -------------------------------------------------------------------------------------------------------------

    //TODO: 根据权限获取菜单树
    @PostMapping(value = "list_menu", produces = {MediaType.APPLICATION_JSON_VALUE})
    public List<SysMenu> listMenuHandler(String pId) {
        return sysMenuService.loadMenuTree(pId);
    }
}
