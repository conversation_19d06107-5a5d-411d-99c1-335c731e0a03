package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.dao.IDriverLogisticsDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverLogistics;
import com.erdos.coal.park.api.driver.service.IDriverLogisticsService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.geo.GeoJson;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("driverLogisticsService")
public class DriverLogisticsServiceImpl extends BaseMongoServiceImpl<DriverLogistics, IDriverLogisticsDao> implements IDriverLogisticsService {

    //地球的赤道半径约为3,963.2 英里或6,378.1公里
    private final static Double MILE_RADIUS = 3963.2;
    private final static Double KILOMETRE_RADIUS = 6378.1;

    @Override
    public ServerResponse<String> addDriverLogistics(DriverInfo driverInfo) {

        DriverLogistics driverLogistics = new DriverLogistics();
        driverLogistics.setIsTag(0);
        driverLogistics.setDriverInfo(driverInfo);
        this.save(driverLogistics);

        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> updateLogisticsOrIsTag(ObjectId objectId, String longitude, String latitude, Integer isTag) {

        DriverLogistics driverLogistics = this.get(objectId);
        if (driverLogistics != null) {
            UpdateOperations<DriverLogistics> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(longitude) && StrUtil.isNotEmpty(latitude)) {
                updateOperations.set("longitude", longitude);
                updateOperations.set("latitude", latitude);

                Point newPoint = GeoJson.point(Double.valueOf(latitude), Double.valueOf(longitude));
                updateOperations.set("geometry", newPoint);
            }
            if (ObjectUtil.isNotEmpty(isTag))
                updateOperations.set("isTag", isTag);

            Query<DriverLogistics> query = this.createQuery().filter("_id", objectId);
            this.update(query, updateOperations);
        }

        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<String> delDriverLogistics(ObjectId driverID) {

        DriverLogistics driverLogistics = this.get("driverID", driverID);
        if (driverLogistics != null)
            this.delete(driverLogistics.getObjectId());

        return ServerResponse.createSuccess("删除成功");
    }

    @Override
    public ServerResponse<List<DriverLogistics>> selectDriverByLogistics(String longitude, String latitude, Double radius, Integer limit) {

        Object[] point = {Double.valueOf(longitude), Double.valueOf(latitude)};
        Object[] centerSphere = {point, radius / KILOMETRE_RADIUS};
        Map<String, Object> centerSphereMap = new HashMap<>();
        centerSphereMap.put("$centerSphere", centerSphere);
        Map<String, Object> geoWithinMap = new HashMap<>();
        geoWithinMap.put("$geoWithin", centerSphereMap);

        Query<DriverLogistics> query = this.createQuery().filter("geometry", geoWithinMap);
        List<DriverLogistics> dlList = this.list(query);

        if (limit != null && dlList.size() > limit) {
            dlList = dlList.subList(0, limit);
        }

        return ServerResponse.createSuccess("查询成功", dlList);
    }
}
