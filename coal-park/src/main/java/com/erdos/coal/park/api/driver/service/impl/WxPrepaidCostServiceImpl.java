package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.pojo.VehicleTypeCost;
import com.erdos.coal.park.api.business.service.ITradeContractService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.driver.dao.IWxPrepaidCostDao;
import com.erdos.coal.park.api.driver.dao.IWxResultCostDao;
import com.erdos.coal.park.api.driver.entity.WxPrepaidCost;
import com.erdos.coal.park.api.driver.entity.WxResultCost;
import com.erdos.coal.park.api.driver.service.IWxPrepaidCostService;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.api.manage.service.IAppLogService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.transaction.wxpay.service.WxPayWechatCostConfig;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.BsonDocument;
import org.bson.Document;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("wxPrepaidCostService")
public class WxPrepaidCostServiceImpl extends BaseMongoServiceImpl<WxPrepaidCost, IWxPrepaidCostDao> implements IWxPrepaidCostService {

    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private ILockedService lockedService;
    @Lazy
    @Resource
    private ISysUnitService sysUnitService;
    @Lazy
    @Resource
    private IOrderService orderService;
    @Resource
    private IWxResultCostDao wxResultCostDao;
    @Resource
    private IAppLogService appLogService;
    @Resource
    private MongoClient client;
    @Resource
    private ITradeContractService tradeContractService;

    /*--------********--------********--------********--------******** 处理请求支付，返回信息前段调起支付 ********--------********--------********--------********--------*/
    @Override
    public ServerResponse<Map<String, String>> weChatRequestPay(String did, String openid, String oid, int totalFee, Integer type, String vehicleCode, String bizContractCode) {
        // todo: 1.查询订单中一级单位配置的商户号等支付信息
        Order order = orderService.get("oid", oid);
        if (order == null) return ServerResponse.createError("运输订单不存在");
        SysUnit unit;
        int outOrIn = 0;    //0-out, 1-in
        String vehicleName = null;
        String subName;
        if (bizContractCode.equals(order.getOutBizContractCode())) {        // 判断是发货单位还是收货单位 发起代收
            unit = sysUnitService.get("code", order.getOutDefaultDownUnit());
            if (StrUtil.isEmpty(vehicleCode)) {
                vehicleCode = order.getVehicleCodeOutAPP();
                vehicleName = order.getVehicleNameOutAPP();
            }
            subName = order.getOutSubName();
        } else {
            unit = sysUnitService.get("code", order.getInDefaultDownUnit());
            if (StrUtil.isEmpty(vehicleCode)) {
                vehicleCode = order.getVehicleCodeInAPP();
                vehicleName = order.getVehicleNameInAPP();
            }
            outOrIn = 1;
            subName = order.getInSubName();
        }
        if (StrUtil.isEmpty(vehicleName)) {
            TradeContract tradeContract = tradeContractService.get("bizContractCode", bizContractCode);
            List<VehicleTypeCost> vehicleTypeCostList = tradeContract.getVehicleTypeCostList();
            for (VehicleTypeCost cost : vehicleTypeCostList) {
                if (vehicleCode.equals(cost.getVehicleCode())) {
                    vehicleName = cost.getVehicleName();
                    break;
                }
            }
        }
        if (unit == null || StrUtil.isEmpty(unit.getMchId()) || StrUtil.isEmpty(unit.getKey())) {
            if (bizContractCode.equals(order.getOutBizContractCode())) {        // 判断是发货单位还是收货单位 发起代收
                unit = sysUnitService.get("code", order.getOutUnitCode());
            } else {
                unit = sysUnitService.get("code", order.getInUnitCode());
            }
            if (unit == null || StrUtil.isEmpty(unit.getMchId()) || StrUtil.isEmpty(unit.getKey()))
                return ServerResponse.createError("企业收款商户未设置");
        }

        // todo: 2.请求微信发起支付
        boolean lock = lockedService.getLock(did, SysConstants.LockType.WX_PAY_PREPAID.getType());    //加锁
        if (!lock) {//未得到锁
            return ServerResponse.createError("重复提交");
        } else {
            try {
                if (StrUtil.isEmpty(totalFee) || totalFee == 0) return ServerResponse.createError("支付金额不能为空");
                String tradeType = "JSAPI"; //，JSAPI-平台小程序调起微信支付

                //订单修改司机选择的车型和装卸费字段
                Query<Order> orderQuery = orderService.createQuery();
                orderQuery.criteria("oid").equal(oid);
                UpdateOperations<Order> orderUpdateOperations = orderService.createUpdateOperations();
                if (outOrIn == 0) {
                    if (type == 0) {
                        orderUpdateOperations.set("vehicleNameOutDvr", vehicleName);
                        orderUpdateOperations.set("vehicleCodeOutDvr", vehicleCode);
                        orderUpdateOperations.set("handlingCostOutDvr", totalFee);
                    } else {
                        orderUpdateOperations.set("handlingCostOutDvrSupplementary", totalFee);
                    }
                } else {
                    if (type == 0) {
                        orderUpdateOperations.set("vehicleNameInDvr", vehicleName);
                        orderUpdateOperations.set("vehicleCodeInDvr", vehicleCode);
                        orderUpdateOperations.set("handlingCostInDvr", totalFee);
                    } else {
                        orderUpdateOperations.set("handlingCostInDvrSupplementary", totalFee);
                    }
                }
                UpdateResults updateResults = orderService.update(orderQuery, orderUpdateOperations);
                if (updateResults.getUpdatedCount() > 0) {
                    //1.平台向微信支付系统请求下单，创建订单
                    long timeStart = System.currentTimeMillis();
                    String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
                    String body = "装卸费|" + subName + "-" + order.getCarNum() + "-" + order.getOid();
                    String mchId = unit.getMchId();
                    String key = unit.getKey();
                    String spbillCreateIp = InetAddress.getLocalHost().getHostAddress();
                    String attach = oid + "," + type + "," + bizContractCode;
                    Map<String, String> unifiedOrderResMap = createWEChatOrder4(out_trade_no, timeStart, body, openid, totalFee, tradeType, mchId, key, spbillCreateIp, attach);
                    if (StrUtil.isEmpty(unifiedOrderResMap)) return ServerResponse.createError("统一下单失败");
                    if (unifiedOrderResMap.get("return_code").equals("FAIL"))
                        return ServerResponse.createError(unifiedOrderResMap.get("return_msg"));
                    if (StrUtil.isNotEmpty(unifiedOrderResMap.get("err_code")))
                        return ServerResponse.createError(unifiedOrderResMap.get("err_code_des"));

                    //2.微信后台系统成功创建订单，平台添加预支付信息，等待用户支付结果
                    savePrepaidCost(did, oid, out_trade_no, openid, bizContractCode, vehicleName, vehicleCode, totalFee, tradeType, body, spbillCreateIp, unifiedOrderResMap, attach);

                    //3.对微信支付系统成功创建对订单信息生成二次签名，返回前端调起支付界面
                    return signWechatCostOrder(wxPayConstants.wechat_appid, key, out_trade_no, timeStart, unifiedOrderResMap, tradeType);
                } else {
                    return null;
                }
            } catch (UnknownHostException e) {
                e.printStackTrace();
                return null;
            } finally {
                lockedService.unLock(did, SysConstants.LockType.WX_PAY_PREPAID.getType());//释放锁

            }
        }

    }

    //平台向微信支付系统请求下单，创建订单
    private Map<String, String> createWEChatOrder4(String out_trade_no, long timeStart, String body, String openid, Integer totalFee, String tradeType, String mchId, String key, String spbillCreateIp, String attach) {
        try {
            WxPayWechatCostConfig wxPayWechatCostConfig = new WxPayWechatCostConfig();
            wxPayWechatCostConfig.setAppid(wxPayConstants.wechat_appid);
            wxPayWechatCostConfig.setDomainApi(wxPayConstants.DOMAIN_API);
            wxPayWechatCostConfig.setMchId(mchId);
            wxPayWechatCostConfig.setKey(key);

            WXPay wxpay = new WXPay(wxPayWechatCostConfig, wxPayConstants.notifyUrl_cost, true, false);

            Map<String, String> map = new HashMap<>();
            map.put("total_fee", String.valueOf(totalFee));
            map.put("trade_type", tradeType);//  "APP" ,"JSAPI"小程序
            map.put("spbill_create_ip", spbillCreateIp);
            map.put("out_trade_no", out_trade_no);
            map.put("body", body);
            if (tradeType.equals("JSAPI")) {
                map.put("openid", openid);
            } else if (tradeType.equals("APP")) {
                map.put("time_start", new SimpleDateFormat("yyyyMMddHHmmss").format(timeStart));
            } else {
                return null;
            }
            map.put("attach", attach);
            return wxpay.unifiedOrder(map);//统一下单
        } catch (Exception ignored) {
            return null;//ServerResponse.createError("统一下单失败");
        }
    }

    //微信支付系统成功创建订单，平台添加预支付信息，等待用户支付结果
    private void savePrepaidCost(String did, String oid, String out_trade_no, String openid, String bizContractCode, String vehicleName, String vehicleCode, Integer totalFee, String tradeType, String body, String spbillCreateIp, Map<String, String> unifiedOrderResMap, String attach) {
        WxPrepaidCost prepaidCost = new WxPrepaidCost();
        prepaidCost.setDid(did);
        prepaidCost.setOid(oid);
        prepaidCost.setOpenid(openid);
        prepaidCost.setBizContractCode(bizContractCode);
        prepaidCost.setVehicleName(vehicleName);
        prepaidCost.setVehicleCode(vehicleCode);
        prepaidCost.setTotalFee(totalFee);

        prepaidCost.setNotifyUrl(wxPayConstants.notifyUrl_cost);
        prepaidCost.setTradeType(tradeType);
        prepaidCost.setSpbillCreateIp(spbillCreateIp);
        prepaidCost.setOutTradeNo(out_trade_no);
        prepaidCost.setBody(body);
        prepaidCost.setAttach(attach);

        prepaidCost.setSign(unifiedOrderResMap.get("sign"));
        prepaidCost.setNonceStr(unifiedOrderResMap.get("nonce_str"));
        prepaidCost.setAppid(unifiedOrderResMap.get("appid"));
        prepaidCost.setMchId(unifiedOrderResMap.get("mch_id"));
        prepaidCost.setResultCode(unifiedOrderResMap.get("result_code"));
        if (StrUtil.isNotEmpty(unifiedOrderResMap.get("err_code")))
            prepaidCost.setErrCode(unifiedOrderResMap.get("err_code"));

        this.save(prepaidCost);
    }

    //对微信支付系统成功创建对订单，生成签名
    private ServerResponse<Map<String, String>> signWechatCostOrder(String appid, String key, String out_trade_no, long timeStart, Map<String, String> unifiedOrderResMap, String tradeType) {
        //生成二次签名
        Map<String, String> reMap = new HashMap<>();
        try {
            reMap.put("timeStamp", String.format("%010d", timeStart / 1000));//以秒为单位的10位数字
            reMap.put("nonceStr", unifiedOrderResMap.get("nonce_str"));
            reMap.put("appId", appid);
            reMap.put("package", "prepay_id=" + unifiedOrderResMap.get("prepay_id"));
            reMap.put("signType", "MD5");
            reMap.put("paySign", WXPayUtil.generateSignature(reMap, key, WXPayConstants.SignType.MD5));
        } catch (Exception e) {
            e.printStackTrace();
        }
        reMap.put("out_trade_no", out_trade_no);
        return ServerResponse.createSuccess("统一下单成功", reMap);
    }


    /*--------********--------********--------********--------******** 处理支付成功回调通知 ********--------********--------********--------********--------*/
    @Override
    public String weChatPayResult(Map<String, String> map) {
        String attach = map.get("attach");                  //attach String(128) 商家数据包，原样返回
        String[] attachArr = attach.split(",");
        String oid = attachArr[0];
        String type = attachArr[1];
        String bizContractCode = attachArr[2];

        Order order = orderService.get("oid", oid);
        if (order != null) {
            SysUnit unit;
            int outOrIn = 0;    //0-out, 1-in
            int payTarget = 1;  //1-发货支付，2-发货补交，3-收货支付，4-收货补交
            if (bizContractCode.equals(order.getOutBizContractCode())) {        // 判断是发货单位还是收货单位 发起代收
                unit = sysUnitService.get("code", order.getOutDefaultDownUnit());
                if ("1".equals(type)) payTarget = 2;
            } else {
                unit = sysUnitService.get("code", order.getOutDefaultDownUnit());
                outOrIn = 1;
                if ("0".equals(type)) {
                    payTarget = 3;
                } else {
                    payTarget = 4;
                }
            }

            if (unit == null || StrUtil.isEmpty(unit.getMchId()) || StrUtil.isEmpty(unit.getKey())) {
                if (bizContractCode.equals(order.getOutBizContractCode())) {        // 判断是发货单位还是收货单位 发起代收
                    unit = sysUnitService.get("code", order.getOutUnitCode());
                } else {
                    unit = sysUnitService.get("code", order.getInUnitCode());
                }
            }

            if (unit != null) {
                String mchId = unit.getMchId();
                String key = unit.getKey();

                try {
                    String sign = WXPayUtil.generateSignature(map, key, WXPayConstants.SignType.MD5);
                    if (sign.equals(map.get("sign"))) { //校验签名是否正确

                        AppLog appLog = new AppLog();
                        appLog.setUser("WXPayCost");
                        appLog.setParameter(map.toString());
                        appLogService.writeLog(appLog);

                        WxResultCost wxResultCost = weChatPayResult(mchId, key, map, 0, oid, payTarget);
                        if (wxResultCost == null) {
                            return "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[回调失败]]></return_msg>" + "</xml> ";
                        } else {
                            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                        }

                    } else {
                        logger.error("微信支付回调通知签名错误");
                        return "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
    }

    private WxResultCost weChatPayResult(String mchId, String key, Map<String, String> map, Integer flag, String oid, int payTarget) {

        WxResultCost wxResultCost = wxResultCostDao.get("transactionId", map.get("transaction_id"));
        if (ObjectUtil.isNull(wxResultCost)) {
            wxResultCost = wxResultCostDao.get("outTradeNo", map.get("out_trade_no"));
            if (ObjectUtil.isNull(wxResultCost)) {  //平台支付结果信息为空时，处理支付结果；否则直接返回
                try {
                    if (flag == 1) {    //判断是查询订单还是回调,1 订单查询
                        //获取WXPay对象
                        WxPayWechatCostConfig wxPayWechatCostConfig = new WxPayWechatCostConfig();
                        wxPayWechatCostConfig.setAppid(wxPayConstants.wechat_appid);
                        wxPayWechatCostConfig.setDomainApi(wxPayConstants.DOMAIN_API);
                        wxPayWechatCostConfig.setMchId(mchId);
                        wxPayWechatCostConfig.setKey(key);

                        WXPay wxpay = new WXPay(wxPayWechatCostConfig, wxPayConstants.notifyUrl_cost, true, false);
                        map = wxpay.orderQuery(map);
                    }

                    if (map.get("return_code").equals("SUCCESS") && map.get("result_code").equals("SUCCESS")) {
                        //判断微信支付系统推送支付结果信息和预支付信息金额是否一致
                        WxPrepaidCost prepaidCost = this.get("outTradeNo", map.get("out_trade_no"));
                        if (prepaidCost != null || StrUtil.isNotEmpty(map.get("total_fee")) || Integer.valueOf(map.get("total_fee")) - prepaidCost.getTotalFee() == 0)
                            wxResultCost = payResult(map, oid, payTarget);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            }
        }
        return wxResultCost;
    }

    /**
     * 处理微信订单支付成功信息
     */
    //加锁
    @Transactional
    public WxResultCost payResult(Map<String, String> map, String oid, int payTarget) {
        WxResultCost wxResultCost = saveWxResult(map);

        MongoCollection<Document> resultCollection = wxResultCostDao.getCollection();
        MongoCollection<Document> prepaidCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        ClientSession clientSession = client.startSession();

        try {
            clientSession.startTransaction();
            //1.微信订单支付结果保存到数据库中
            Document document = Document.parse(JSONObject.toJSONString(wxResultCost));
            document.append("createTime", new Date());
            resultCollection.insertOne(clientSession, document);

            //2.更新预支付表
            Document updateQuery = new Document("outTradeNo", map.get("out_trade_no"));
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("pay", true);
            updMap.put("updateTime", new Date().getTime());
            Map<String, Object> params = new HashMap<>();
            params.put("$set", updMap);
            prepaidCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(params)));

            //3.更新order表
            Document updateOrderQuery = new Document("oid", oid);
            Map<String, Object> updOrderMap = new HashMap<>();
            if (payTarget == 1) {
//                updOrderMap.put("handlingCostOutDvr", wxResultCost.getTotalFee());
                updOrderMap.put("pay1", true);
            }
            if (payTarget == 2) {
//                updOrderMap.put("handlingCostOutDvrSupplementary", wxResultCost.getTotalFee());
                updOrderMap.put("pay2", true);
            }
            if (payTarget == 3) {
//                updOrderMap.put("handlingCostInDvr", wxResultCost.getTotalFee());
                updOrderMap.put("pay3", true);
            }
            if (payTarget == 4) {
//                updOrderMap.put("handlingCostInDvrSupplementary", wxResultCost.getTotalFee());
                updOrderMap.put("pay4", true);
            }
            Map<String, Object> orderParams = new HashMap<>();
            orderParams.put("$set", updOrderMap);
            orderCollection.updateOne(clientSession, updateOrderQuery, BsonDocument.parse(JSON.toJSONString(orderParams)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
        return wxResultCost;
    }

    //微信支付结果信息 添加 到WxResult对象
    private WxResultCost saveWxResult(Map<String, String> map) {
        WxResultCost wxResultCost = new WxResultCost();
        wxResultCost.setReturnCode(map.get("return_code"));
        wxResultCost.setResultCode(map.get("result_code"));
        wxResultCost.setNonceStr(map.get("nonce_str"));
        wxResultCost.setSign(map.get("sign"));
        wxResultCost.setOpenid(map.get("openid"));
        wxResultCost.setTradeType(map.get("trade_type"));
        wxResultCost.setBankType(map.get("bank_type"));
        Integer l = Utils.parseInt(map.get("total_fee"), 0);
        wxResultCost.setTotalFee(l);
        wxResultCost.setFeeType(map.get("fee_type"));
        wxResultCost.setCashFee(Integer.parseInt(map.get("cash_fee")));
        wxResultCost.setTransactionId(map.get("transaction_id"));
        wxResultCost.setOutTradeNo(map.get("out_trade_no"));
        wxResultCost.setTimeEnd(map.get("time_end"));
        wxResultCost.setUpdateTime(new Date().getTime());
        return wxResultCost;
    }


    /*--------********--------********--------********--------******** 判断订单装卸费支付结果 ********--------********--------********--------********--------*/
    @Override
    public Integer[] orderCost(Order order) {
        // 判断订单一级单位是否要求代收装卸费
        Integer[] cost = new Integer[]{0, 0};
        if (StrUtil.isNotEmpty(order.getOutUnitCode())) {
            SysUnit outUnit = sysUnitService.get("code", order.getOutUnitCode());
            if (StrUtil.isNotEmpty(outUnit.getHandlingCost()) && outUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(outUnit.getMchId()) && StrUtil.isNotEmpty(outUnit.getKey())) {
                if (order.getPay1()) {  // 司机完成选择车型的装卸费支付
                    if (!order.getPay2()) {     //司机未完成选择车型的装卸费补交
                        if (StrUtil.isNotEmpty(order.getVehicleNameOutAPP())) {     //厂区确认
                            if (order.getVehicleNameOutDvr().equals(order.getVehicleNameOutAPP())) {    //司机选择和厂区确认不一致
                                if (order.getHandlingCostOutAPP() - order.getHandlingCostOutDvr() > 0) {    //费用差额大于0
                                    // 查询预支付表、支付结果表，以及微信支付查询接口，核验是否支付
                                    Query<WxPrepaidCost> prepaidCostQuery = this.createQuery();
                                    prepaidCostQuery.criteria("oid").equal(order.getOid());
                                    prepaidCostQuery.criteria("bizContractCode").equal(order.getOutBizContractCode());
                                    prepaidCostQuery.criteria("vehicleName").equal(order.getVehicleNameOutAPP());
                                    prepaidCostQuery.order(Sort.descending("createTime"));  //按创建时间倒叙排序，查询最新的一条记录
                                    WxPrepaidCost prepaidCost = this.get(prepaidCostQuery);
                                    if (prepaidCost != null) { //预支付表不为空
                                        if (!prepaidCost.getPay()) {    //预支付表未完成支付
                                            /*Map<String, String> map = new HashMap<>();
                                            map.put("out_trade_no", prepaidCost.getOutTradeNo());
                                            WxResultCost resultCost = weChatPayResult(outUnit.getMchId(), outUnit.getKey(), map, 1, order.getOid(), 2);
                                            if (resultCost == null ) cost[0] = 2;*/
                                            cost[0] = 2;
                                        }
                                    } else {        //预支付表为空
                                        Query<WxResultCost> resultCostQuery = wxResultCostDao.createQuery();
                                        resultCostQuery.criteria("oid").equal(order.getOid());
                                        resultCostQuery.criteria("bizContractCode").equal(order.getOutBizContractCode());
                                        resultCostQuery.criteria("vehicleName").equal(order.getVehicleNameOutAPP());
                                        WxResultCost resultCost = wxResultCostDao.get(resultCostQuery);
                                        if (resultCost == null) cost[0] = 2;
                                    }
                                }
                            }
                        }
                    }
                } else {                // 司机未完成选择车型的装卸费支付
                    if (StrUtil.isEmpty(order.getVehicleNameOutDvr())) {
                        cost[0] = 1;
                    } else {
                        // 查询预支付表、支付结果表，以及微信支付查询接口，核验是否支付
                        Query<WxPrepaidCost> prepaidCostQuery = this.createQuery();
                        prepaidCostQuery.criteria("oid").equal(order.getOid());
                        prepaidCostQuery.criteria("bizContractCode").equal(order.getOutBizContractCode());
                        prepaidCostQuery.criteria("vehicleName").equal(order.getVehicleNameOutDvr());
                        prepaidCostQuery.order(Sort.descending("createTime"));  //按创建时间倒叙排序，查询最新的一条记录
                        WxPrepaidCost prepaidCost = this.get(prepaidCostQuery);
                        if (prepaidCost != null) { //预支付表不为空
                            if (!prepaidCost.getPay()) {    //预支付表未完成支付
                                /*Map<String, String> map = new HashMap<>();
                                map.put("out_trade_no", prepaidCost.getOutTradeNo());
                                WxResultCost resultCost = weChatPayResult(outUnit.getMchId(), outUnit.getKey(), map, 1, order.getOid(), 2);
                                if (resultCost == null ) cost[0] = 2;*/
                                cost[0] = 1;
                            }
                        } else {        //预支付表为空
                            Query<WxResultCost> resultCostQuery = wxResultCostDao.createQuery();
                            resultCostQuery.criteria("oid").equal(order.getOid());
                            resultCostQuery.criteria("bizContractCode").equal(order.getOutBizContractCode());
                            resultCostQuery.criteria("vehicleName").equal(order.getVehicleNameOutDvr());
                            WxResultCost resultCost = wxResultCostDao.get(resultCostQuery);
                            if (resultCost == null) cost[0] = 1;
                        }
                    }
                }
            }
        }
        if (StrUtil.isNotEmpty(order.getInUnitCode())) {
            SysUnit inUnit = sysUnitService.get("code", order.getInUnitCode());
            if (StrUtil.isNotEmpty(inUnit.getHandlingCost()) && inUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(inUnit.getMchId()) && StrUtil.isNotEmpty(inUnit.getKey())) {
                if (order.getPay3()) {  // 司机完成选择车型的装卸费支付
                    if (!order.getPay4()) {     //司机未完成选择车型的装卸费补交
                        if (StrUtil.isNotEmpty(order.getVehicleNameInAPP())) {     //厂区确认
                            if (order.getVehicleNameInDvr().equals(order.getVehicleNameInAPP())) {    //司机选择和厂区确认不一致
                                if (order.getHandlingCostInAPP() - order.getHandlingCostInDvr() > 0) {    //费用差额大于0
                                    // 查询预支付表、支付结果表，以及微信支付查询接口，核验是否支付
                                    Query<WxPrepaidCost> prepaidCostQuery = this.createQuery();
                                    prepaidCostQuery.criteria("oid").equal(order.getOid());
                                    prepaidCostQuery.criteria("bizContractCode").equal(order.getInBizContractCode());
                                    prepaidCostQuery.criteria("vehicleName").equal(order.getVehicleNameInAPP());
                                    prepaidCostQuery.order(Sort.descending("createTime"));  //按创建时间倒叙排序，查询最新的一条记录
                                    WxPrepaidCost prepaidCost = this.get(prepaidCostQuery);
                                    if (prepaidCost != null) { //预支付表不为空
                                        if (!prepaidCost.getPay()) {    //预支付表未完成支付
                                            /*Map<String, String> map = new HashMap<>();
                                            map.put("out_trade_no", prepaidCost.getOutTradeNo());
                                            WxResultCost resultCost = weChatPayResult(inUnit.getMchId(), inUnit.getKey(), map, 1, order.getOid(), 2);
                                            if (resultCost == null ) cost[0] = 2;*/
                                            cost[1] = 2;
                                        }
                                    } else {        //预支付表为空
                                        Query<WxResultCost> resultCostQuery = wxResultCostDao.createQuery();
                                        resultCostQuery.criteria("oid").equal(order.getOid());
                                        resultCostQuery.criteria("bizContractCode").equal(order.getInBizContractCode());
                                        resultCostQuery.criteria("vehicleName").equal(order.getVehicleNameInAPP());
                                        WxResultCost resultCost = wxResultCostDao.get(resultCostQuery);
                                        if (resultCost == null) cost[1] = 2;
                                    }
                                }
                            }
                        }
                    }
                } else {                // 司机未完成选择车型的装卸费支付
                    if (StrUtil.isEmpty(order.getVehicleNameInDvr())) {
                        cost[0] = 1;
                    } else {
                        // 查询预支付表、支付结果表，以及微信支付查询接口，核验是否支付
                        Query<WxPrepaidCost> prepaidCostQuery = this.createQuery();
                        prepaidCostQuery.criteria("oid").equal(order.getOid());
                        prepaidCostQuery.criteria("bizContractCode").equal(order.getInBizContractCode());
                        prepaidCostQuery.criteria("vehicleName").equal(order.getVehicleNameInDvr());
                        prepaidCostQuery.order(Sort.descending("createTime"));  //按创建时间倒叙排序，查询最新的一条记录
                        WxPrepaidCost prepaidCost = this.get(prepaidCostQuery);
                        if (prepaidCost != null) { //预支付表不为空
                            if (!prepaidCost.getPay()) {    //预支付表未完成支付
                                /*Map<String, String> map = new HashMap<>();
                                map.put("out_trade_no", prepaidCost.getOutTradeNo());
                                WxResultCost resultCost = weChatPayResult(inUnit.getMchId(), inUnit.getKey(), map, 1, order.getOid(), 2);
                                if (resultCost == null ) cost[0] = 2;*/
                                cost[1] = 1;
                            }
                        } else {        //预支付表为空
                            Query<WxResultCost> resultCostQuery = wxResultCostDao.createQuery();
                            resultCostQuery.criteria("oid").equal(order.getOid());
                            resultCostQuery.criteria("bizContractCode").equal(order.getInBizContractCode());
                            resultCostQuery.criteria("vehicleName").equal(order.getVehicleNameInDvr());
                            WxResultCost resultCost = wxResultCostDao.get(resultCostQuery);
                            if (resultCost == null) cost[1] = 1;
                        }
                    }
                }
            }
        }

        return cost;
    }

    @Override
    public Map<String, List<String>> searchPayId(String oid, String bizContractCode) {
        Map<String, List<String>> map = new HashMap<>();

        List<String> outTradeNoList = new ArrayList<>();
        List<String> transactionIdList = new ArrayList<>();

        Query<WxPrepaidCost> prepaidCostQuery = this.createQuery();
        prepaidCostQuery.criteria("oid").equal(oid);
        prepaidCostQuery.criteria("bizContractCode").equal(bizContractCode);
        List<WxPrepaidCost> prepaidCosts = this.list(prepaidCostQuery);
        for (WxPrepaidCost prepaidCost : prepaidCosts) {
            outTradeNoList.add(prepaidCost.getOutTradeNo());
        }
        map.put("outTradeNoList", outTradeNoList);

        Query<WxResultCost> resultCostQuery = wxResultCostDao.createQuery();
        resultCostQuery.criteria("oid").equal(oid);
        resultCostQuery.criteria("bizContractCode").equal(bizContractCode);
        List<WxResultCost> resultCosts = wxResultCostDao.list(resultCostQuery);
        for (WxResultCost resultCost : resultCosts) {
            transactionIdList.add(resultCost.getTransactionId());
        }
        map.put("transactionIdList", transactionIdList);

        return map;
    }
}
