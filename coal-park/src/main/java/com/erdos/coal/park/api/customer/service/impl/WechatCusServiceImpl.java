package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.customer.dao.IWechatCusDao;
import com.erdos.coal.park.api.customer.entity.WechatCus;
import com.erdos.coal.park.api.customer.service.IWechatCusService;
import com.erdos.coal.park.api.manage.pojo.WeChatData;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("wechatCusService")
public class WechatCusServiceImpl extends BaseMongoServiceImpl<WechatCus, IWechatCusDao> implements IWechatCusService {
    @Resource
    private IHttpAPIService httpAPIService;

    @Override
    public ServerResponse<WeChatData> wechatUserLogin(String code) {

        String requestUrl = WXPayConstants.sessionHost;
        requestUrl += "?appid=" + WXPayConstants.wechat_appid_cus + "&secret=" + WXPayConstants.wechat_secret_cus + "&js_code=" + code + "&grant_type=" + WXPayConstants.wechat_grant_type;
        JSONObject object = null;
        try {
            object = JSON.parseObject(httpAPIService.doGet(requestUrl));
        } catch (Exception e) {
            e.printStackTrace();
        }
        assert object != null;
        String openid = object.getString("openid");
        String sessionKey = object.getString("session_key");
        String unionid = object.getString("unionid");

        if (openid == null || sessionKey == null)
            throw new GlobalException(ServerResponse.createError(object.getString("errmsg")));

        WeChatData weChatData = new WeChatData(openid, unionid);

        return ServerResponse.createSuccess("授权成功", weChatData);
    }

}
