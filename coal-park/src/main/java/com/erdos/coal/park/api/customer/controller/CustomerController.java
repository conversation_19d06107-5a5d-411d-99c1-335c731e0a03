package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.park.web.sys.entity.ThirdPartyAccount;
import com.erdos.coal.park.web.sys.service.IThirdPartyAccountService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by LIGX on 2018/12/27.
 * 客商接口
 */
//"客商APP业务接口列表"
@RestController
@RequestMapping("/api/cus")
public class CustomerController extends BaseController {
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    @InvokeLog(description = "查询客商信息 接口", printReturn = false) //日志
    @PostMapping(value = "/main")
    public ServerResponse<CustomerUser> customerMainHandler() throws GlobalException {
        return customerUserService.getCustomerUser();
    }

    @InvokeLog(description = "查询客商账户 接口", printReturn = false) //日志
    @PostMapping(value = "/get_customer_account")
    public ServerResponse<CustomerAccount> getCustomerAccountHandler() throws GlobalException {
        return customerAccountService.getCustomerAccount();
    }

    @InvokeLog(description = "设置默认金额 接口") //日志
    @PostMapping(value = "/set_customer_fee")
    public ServerResponse<String> setCustomerFeeHandler(
            @RequestParam(value = "fee") double fee        //"默认金额"
    ) throws GlobalException {
        return customerUserService.setCustomerFee(fee);
    }

    @InvokeLog(description = "查询默认金额 接口", printReturn = false) //日志
    @PostMapping(value = "/get_customer_fee")
    public ServerResponse<String> getCustomerFeeHandler() throws GlobalException {
        return customerUserService.getCustomerFee();
    }

    @InvokeLog(description = "更换手机号 接口") //日志
    @PostMapping(value = "/change_mobile")
    public ServerResponse<String> changeMobileHandler(
            @RequestParam(value = "mobile") String mobile, //"新手机号码"
            @RequestParam(value = "code") String code      //"短信验证码"
    ) throws GlobalException {
        return customerUserService.updateMobile(mobile, code);
    }

    @InvokeLog(description = "客商完善姓名 接口") //日志
    @PostMapping(value = "/save_name")
    public ServerResponse<String> saveNameHandler(
            @RequestParam(value = "name") String name      //"客商姓名"
    ) throws GlobalException {
        return customerUserService.updateName(name);
    }

    @InvokeLog(description = "查询客商三方账户 接口", printReturn = false) //日志
    @PostMapping(value = "/get_third_part_account")
    public ServerResponse<ThirdPartyAccount> getThirdPartAccountHandler() throws GlobalException {
        return thirdPartyAccountService.getAccount();
    }

    @InvokeLog(description = "客商三方账户申请提现 接口", printReturn = false) //日志
    @PostMapping(value = "/third_part_refund")
    public ServerResponse<String> thirdPartRefundHandler(
            @RequestParam(value = "name") String name,                      //"真实姓名"
            @RequestParam(value = "amount") Double amount                   //"提现金额"
    ) throws GlobalException {
        return thirdPartyAccountService.RefundPre(amount, name);
    }

    @InvokeLog(description = "企业付款到零钱进度查询 接口", printReturn = false) //日志
    @PostMapping(value = "/query_refund")
    public ServerResponse<List<RefundPreData>> queryRefundHandler() throws GlobalException {
        return thirdPartyAccountService.queryRefundThirdPart();
    }

    @InvokeLog(description = "查询客商账户明细 接口", printReturn = false) //日志
    @PostMapping(value = "/customer_account_list")
    public ServerResponse<EGridResult> customerAccountListHandler(
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return customerAccountService.customerAccountList(page, rows);
    }

    @InvokeLog(description = "查询客商三方账户明细 接口", printReturn = false) //日志
    @PostMapping(value = "/third_part_account_list")
    public ServerResponse<EGridResult> thirdPartAccountListHandler(
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return thirdPartyAccountService.thirdPartAccountList(page, rows);
    }
}