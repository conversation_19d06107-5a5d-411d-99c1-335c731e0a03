package com.erdos.coal.park.web.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.dao.IGoodsDao;
import com.erdos.coal.park.api.customer.dao.IOrderDao;
import com.erdos.coal.park.api.customer.entity.*;
import com.erdos.coal.park.api.customer.service.IAppraiseService;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WxPrepaid;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.*;
import com.erdos.coal.park.web.app.pojo.*;
import com.erdos.coal.park.web.app.service.IWebOrderService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;
import com.erdos.coal.park.web.sys.service.IPlatFormAccountService;
import com.erdos.coal.park.web.sys.service.ISysUnitAccountService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.transaction.wxpay.service.WXPayWechatConfig;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.BsonDocument;
import org.bson.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

@Service("webOrderService")
public class WebOrderServiceImpl extends BaseMongoServiceImpl<Order, IOrderDao> implements IWebOrderService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private IGoodsDao goodsDao;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IAppraiseService appraiseService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private WXPayWechatConfig config;
    @Resource
    private IWxPrepaidService wxPrepaidService;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private ISysUnitAccountService sysUnitAccountService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IOrderRecordService orderRecordService;
    @Resource
    private IOrderLogisticsService orderLogisticsService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private MongoClient client;
    @Resource
    private IPlatFormAccountService platFormAccountService;
    @Resource
    private IDriverInfoService driverInfoService;

    public EGridResult listGoods(Integer page, Integer rows, String type) {
        //1.查询手机号条件匹配的客商
        String mobile = request.getParameter("mobile");
        List<String> cIds = new ArrayList<>();
        if (!StrUtil.isEmpty(mobile)) {
            Query<CustomerUser> query = customerUserService.createQuery();
            query.filter("mobile", request.getParameter("mobile"));

            List<CustomerUser> customerUserList = customerUserService.list(query);
            for (CustomerUser cUser : customerUserList) {
                cIds.add(cUser.getObjectId().toString());
            }
        }

        //2.查询客商们 添加的货运信息Goods
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");

        Query<Goods> gQuery = goodsDao.createQuery();

        if (StrUtil.isNotEmpty(mobile)) gQuery.filter("cid in", cIds.toArray());
        if (!StrUtil.isEmpty(startDate)) gQuery.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        if (!StrUtil.isEmpty(endDate)) gQuery.filter("createTime <= ", IdUnit.weeHours(endDate, 1));

//        query.order(Sort.descending("updateTime"));
        EGridResult<Goods> eGridResult = goodsDao.findPage(page, rows, gQuery);
        List<Goods> goodsList = eGridResult.getRows();

        List<GoodsData> gdList = new ArrayList<>();
        for (Goods goods : goodsList) {

            if (type.equals("1")) {
                Query<Order> query = this.createQuery().filter("gid", goods.getGid());
                List<Order> orders = this.list(query);

                if (orders.size() <= 0 || null == orders.get(0).getIsHand() || !orders.get(0).getIsHand().equals(2)) //只显示批量下单（抢单）
                    continue;
            }

            GoodsData gd = new GoodsData();

            CustomerUser user = goods.getCustomerUser();
            BeanUtils.copyProperties(goods, gd);
            if (ObjectUtil.isNotEmpty(user))
                gd.setMobile(user.getMobile());

            gdList.add(gd);
        }

        return new EGridResult(eGridResult.getTotal(), gdList);
        /*int size = gdList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        return new EGridResult(gdList.size(), gdList.subList(fromIndex, toIndex));*/
    }

    @Override
    public EGridResult listOrders(Integer page, Integer rows) {
        String gid = request.getParameter("gid");
        Query<Order> oQuery = this.createQuery().filter("gid", gid);
        List<Order> orders = this.list(oQuery);

        Query<OrderTaking> otQuery = orderTakingService.createQuery().filter("gid", gid);
        List<OrderTaking> orderTakings = orderTakingService.list(otQuery);

        List<OrderData> orderDataList = new ArrayList<>();
        for (Order o : orders) {

            OrderData od = new OrderData();
            od.setOid(o.getOid());
            od.setDelete(o.isDelete());
            od.setOutMinMax(o.getOutMinMax());
            od.setInMinMax(o.getInMinMax());

            for (OrderTaking ot : orderTakings) {
                if (!ot.getOid().equals(o.getOid())) continue;

                od.setMobile(ot.getMobile());
                od.setCarNum(ot.getCarNum());
                od.setFinishTag(ot.getFinishTag());
                od.setCid(ot.getCid());
                od.setDid(ot.getDid());

            }
            orderDataList.add(od);
        }

        int size = orderDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        return new EGridResult(orderDataList.size(), orderDataList.subList(fromIndex, toIndex));
    }

    @Override
    public EGridResult appraiseList(Integer page, Integer rows) {
        String mobile = request.getParameter("mobile");

        Query<Appraise> query = appraiseService.createQuery();
        if (StrUtil.isNotEmpty(mobile)) {
            CustomerUser customerUser = customerUserService.get("mobile", mobile);
            if (ObjectUtil.isNotEmpty(customerUser))    //输入手机号没有对应的客商显示记录条数为0，找到客商则取出来对应的客商id
                query.filter("cid", customerUser.getObjectId().toString());
            else
                query.filter("cid", null);
        }
        if (StrUtil.isNotEmpty(request.getParameter("oid")))
            query.filter("oid", request.getParameter("oid"));
        /*Integer[] type = {1, 2};//0-未评价，1-已评价，2-已投诉；
        query.filter("type in ", type);*/
        query.criteria("type").notEqual(0);

        EGridResult<Appraise> eGridResult = appraiseService.findPage(page, rows, query);
        List<Appraise> appraiseList = eGridResult.getRows();
//        List<Appraise> appraiseList = query.find().toList();

        List<AppraiseData> appraiseDataList = new ArrayList<>();
        for (Appraise appraise : appraiseList) {
            AppraiseData appraiseData = new AppraiseData();
            appraiseData.setOid(appraise.getOid());
            appraiseData.setStartPoint(appraise.getStartPoint());
            appraiseData.setEndPoint(appraise.getEndPoint());
            appraiseData.setCreateTime(appraise.getCreateTime());
            appraiseData.setUpdateTime(appraise.getUpdateTime());
            appraiseData.setDriverName(appraise.getDriverName());
            appraiseData.setCarNum(appraise.getCarNum());
            appraiseData.setComplain(appraise.isComplain());
            appraiseData.setReasons(appraise.getReasons());
            appraiseData.setResult(appraise.getResult());
            appraiseData.setType(appraise.getType());
            appraiseData.setStarsNum(appraise.getStarsNum());

            CustomerUser user = appraise.getCustomerUser();
            if (user == null) user = customerUserService.getByPK(appraise.getCid());
            if (ObjectUtil.isNotEmpty(user)) {
                appraiseData.setCustomerMobile(user.getMobile());
                appraiseData.setCustomerName(user.getName());
            }
            appraiseDataList.add(appraiseData);
        }

        return new EGridResult(eGridResult.getTotal(), appraiseDataList);
        /*int size = appraiseDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(appraiseDataList.size(), appraiseDataList.subList(fromIndex, toIndex));*/
    }

    @Override
    public ServerResponse<String> appraiseSave(Appraise appraise) {
        Query<Appraise> query = appraiseService.createQuery();
        query.filter("oid", appraise.getOid());
        query.filter("updateTime", appraise.getUpdateTime());

        UpdateOperations<Appraise> updateOperations = appraiseService.createUpdateOperations();
        updateOperations.set("result", appraise.getResult());
        appraiseService.update(query, updateOperations);

        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public EGridResult checkOrders(Integer page, Integer rows) {
        //订单撤销，未审核，并且客商/司机已付款   的信息列表
        Query<Order> query = this.createQuery();

        String oid = request.getParameter("oid");
        if (StrUtil.isNotEmpty(oid)) query.filter("oid", oid);

        query.filter("delete", true);
//        query.filter("isCheck !=", 1);
        query.or(
                query.criteria("fees").greaterThan(0),
                query.criteria("balanceFees").greaterThan(0),
                query.criteria("shareFees").greaterThan(0),
                query.criteria("feesOut").greaterThan(0),
                query.criteria("feesIn").greaterThan(0)
        );
        query.order(
                Sort.ascending("isCheck"),
                Sort.ascending("createTime")
        );
//        List<Order> orderList = this.list(query);

        EGridResult<Order> eGridResult = this.findPage(page, rows, query);
        List<Order> orderList = eGridResult.getRows();

        List<OrderData> orderDataList = new ArrayList<>();
        for (Order order : orderList) {
            OrderData orderData = new OrderData();
            BeanUtils.copyProperties(order, orderData);

            Map<String, Integer> result = getFees(order);
            int cusFees = result.get("cusFees");
            int driverFees = result.get("driverFees");
            int shareFees = result.get("shareFees");
            int feesOut = result.get("feesOut");
            int feesIn = result.get("feesIn");
            int fees1In = result.get("fees1In");
            int fees2In = result.get("fees2In");
            int fees1Out = result.get("fees1Out");
            int fees2Out = result.get("fees2Out");

            orderData.setCusFees(BigDecimal.valueOf(cusFees).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setDriverFees(BigDecimal.valueOf(driverFees).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setShareFees(BigDecimal.valueOf(shareFees).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setFeesOut(BigDecimal.valueOf(feesOut).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setFeesIn(BigDecimal.valueOf(feesIn).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setFees1In(BigDecimal.valueOf(fees1In).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setFees2In(BigDecimal.valueOf(fees2In).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setFees1Out(BigDecimal.valueOf(fees1Out).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
            orderData.setFees2Out(BigDecimal.valueOf(fees2Out).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元

            orderDataList.add(orderData);
        }

        return new EGridResult(eGridResult.getTotal(), orderDataList);
        /*int size = orderDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        return new EGridResult(orderDataList.size(), orderDataList.subList(fromIndex, toIndex));*/
    }

    /**
     * 收/发 一/二 级单位 收到的代扣费用 只能为客商或司机中的一个人
     * 客商退款费用 = 平台费用 + 收/发 一/二 级单位 收到的代扣费用
     * 司机退款费用 = 平台费用 + 收/发 一/二 级单位 收到的代扣费用 + 客商/友商 收取的信息费
     * 发货单位退款费用 = 发货单位平台费用 - 收/发 一/二 级单位 收到的代扣费用
     * 发货单位退款费用 = 收货单位平台费用 - 收/发 一/二 级单位 收到的代扣费用
     * 删除orderTaking,orderLogistics
     */
    @Override
    public ServerResponse<String> checkSave(OrderData data) {
        Order order = this.get("oid", data.getOid());
        if (order.getIsCheck() != null && (order.getIsCheck() == 1 || order.getIsCheck() == 3))
            return ServerResponse.createError("该订单已审核通过或无需审核，请勿重复提交");
        return refundOrder(data.getOid(), data.getIsCheck());
    }

    @Transactional
    ServerResponse<String> refundOrder(String oid, int isCheck) {
        Order order = this.get("oid", oid);
        Integer isWeChat = order.getIsWeChat();//0-给app司机下单的货运信息；1-给小程序司机下单的货运信息
        Goods goods = goodsDao.get("gid", order.getGid());
        Date date = new Date();
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> updMap = new HashMap<>();

        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> orderCollection = this.getCollection();
        MongoCollection<Document> orderLogisticsCollection = orderLogisticsService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> pushInfoCollection = pushInfoService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        ClientSession clientSession = client.startSession();

        try {
            clientSession.startTransaction();
            if (isCheck == 1 || isCheck == 3) {   // 审核通过或无需审核  都退钱

                int cusFees = order.getFees();         // 客商付款金额
                String cusPayerId = order.getPayerId();// 客商id

                int driverFees = 0;
                int balanceFees = order.getBalanceFees();// 司机付款金额
                int shareFees = order.getShareFees();   // 友商收取的费用
                String driverPayerId = order.getDriverPayerId();// 司机id
                if (balanceFees > 0 || shareFees > 0) {
                    driverFees += balanceFees + shareFees;
                }

                int feesOut = order.getFeesOut();   // 平台收取发货单位 信息费
                String payerIdOut = order.getPayerIdOut();//发货单位id
                int feesIn = order.getFeesIn();     // 平台收取收货单位 信息费
                String payerIdIn = order.getPayerIdIn();//收货单位id

                //平台账户退钱
                List<Document> pfAccountDocs = new ArrayList<>();
                if (cusFees > 0)    //平台减 客商代付的信息费
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(cusPayerId, new BigDecimal(-cusFees), 12, oid, date));
                if (balanceFees > 0)//平台减 司机支付的信息费
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(driverPayerId, new BigDecimal(-balanceFees), 13, oid, date));
                if (feesOut > 0)    //平台减 发货单位代付的信息费
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(payerIdOut, new BigDecimal(-feesOut), 11, oid, date));
                if (feesIn > 0)     //平台减 收货单位代付的信息费
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(payerIdIn, new BigDecimal(-feesIn), 11, oid, date));
                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);

                int fees1Out = order.getFees1Out(); // 发货一级单位收取信息费
                String payerId1Out = order.getPayerId1Out();// 支付人id
                if (fees1Out > 0) {
                    String inUnitCode = order.getOutUnitCode();
                    SysUnit sysUnit = sysUnitService.get("code", inUnitCode);
                    String uid = sysUnit.getObjectId().toString();
                    if (uid.equals(payerIdOut) && StrUtil.isNotEmpty(payerIdOut)) {// 如果是已支付平台信息费的单位，支付平台信息费 - 客商或司机支付单位信息费，只保存一次即可
                        feesOut -= fees1Out;
                    } else if (uid.equals(payerIdIn) && StrUtil.isNotEmpty(payerIdIn)) {
                        feesIn -= fees1Out;
                    } else {
                        Document uAccount = uAccountInsert(uid, order.getOid(), -fees1Out);
                        unitAccountCollection.insertOne(clientSession, uAccount);
                    }

                    // 单位收取的信息费 退回 司机或客商账户
                    Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees1Out, payerId1Out);
                    driverFees = (int) map.get("driverFees");
                    cusFees = (int) map.get("cusFees");
                    driverPayerId = (String) map.get("driverPayerId");
                    cusPayerId = (String) map.get("cusPayerId");
                }

                int fees1In = order.getFees1In();   // 收货一级单位收取信息费
                if (fees1In > 0) {
                    String payerId1In = order.getPayerId1In();// 支付人id
                    String inUnitCode = order.getInUnitCode();
                    SysUnit sysUnit = sysUnitService.get("code", inUnitCode);
                    String uid = sysUnit.getObjectId().toString();
                    if (uid.equals(payerIdOut) && StrUtil.isNotEmpty(payerIdOut)) {// 如果是已支付平台信息费的单位，支付平台信息费 - 客商或司机支付单位信息费，只保存一次即可
                        feesOut -= fees1In;
                    } else if (uid.equals(payerIdIn) && StrUtil.isNotEmpty(payerIdIn)) {
                        feesIn -= fees1In;
                    } else {
                        Document uAccount = uAccountInsert(uid, order.getOid(), -fees1In);
                        unitAccountCollection.insertOne(clientSession, uAccount);
                    }

                    // 单位收取的信息费 退回 司机或客商账户
                    Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees1In, payerId1In);
                    driverFees = (int) map.get("driverFees");
                    cusFees = (int) map.get("cusFees");
                    driverPayerId = (String) map.get("driverPayerId");
                    cusPayerId = (String) map.get("cusPayerId");
                }

                int fees2Out = order.getFees2Out(); // 发货二级单位收取信息费
                if (fees2Out > 0) {
                    String payerId2Out = order.getPayerId2Out();// 支付人id
                    String inUnitCode = order.getOutDefaultDownUnit();
                    SysUnit sysUnit = sysUnitService.get("code", inUnitCode);
                    String uid = sysUnit.getObjectId().toString();
                    if (uid.equals(payerIdOut) && StrUtil.isNotEmpty(payerIdOut)) {// 如果是已支付平台信息费的单位，支付平台信息费 - 客商或司机支付单位信息费，只保存一次即可
                        feesOut -= fees2Out;
                    } else if (uid.equals(payerIdIn) && StrUtil.isNotEmpty(payerIdIn)) {
                        feesIn -= fees2Out;
                    } else {
                        Document uAccount = uAccountInsert(uid, order.getOid(), -fees2Out);
                        unitAccountCollection.insertOne(clientSession, uAccount);
                    }

                    // 单位收取的信息费 退回 司机或客商账户
                    Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees2Out, payerId2Out);
                    driverFees = (int) map.get("driverFees");
                    cusFees = (int) map.get("cusFees");
                    driverPayerId = (String) map.get("driverPayerId");
                    cusPayerId = (String) map.get("cusPayerId");
                }

                int fees2In = order.getFees2In();   // 收货二级单位收取信息费
                if (fees2In > 0) {
                    String payerId2In = order.getPayerId2In();// 支付人id
                    String inUnitCode = order.getInDefaultDownUnit();
                    SysUnit sysUnit = sysUnitService.get("code", inUnitCode);
                    String uid = sysUnit.getObjectId().toString();
                    if (uid.equals(payerIdOut) && StrUtil.isNotEmpty(payerIdOut)) {// 如果是已支付平台信息费的单位，支付平台信息费 - 客商或司机支付单位信息费，只保存一次即可
                        feesOut -= fees2In;
                    } else if (uid.equals(payerIdIn) && StrUtil.isNotEmpty(payerIdIn)) {
                        feesIn -= fees2In;
                    } else {
                        Document uAccount = uAccountInsert(uid, order.getOid(), -fees2In);
                        unitAccountCollection.insertOne(clientSession, uAccount);
                    }

                    // 单位收取的信息费 退回 司机或客商账户
                    Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees2In, payerId2In);
                    driverFees = (int) map.get("driverFees");
                    cusFees = (int) map.get("cusFees");
                    driverPayerId = (String) map.get("driverPayerId");
                    cusPayerId = (String) map.get("cusPayerId");
                }

                if (feesOut != 0) { // 发货单位账户表
                    Document uAccount = uAccountInsert(payerIdOut, order.getOid(), feesOut);
                    unitAccountCollection.insertOne(clientSession, uAccount);
                }

                if (feesIn != 0) {  // 收货单位账户表
                    Document uAccount = uAccountInsert(payerIdIn, order.getOid(), feesIn);
                    unitAccountCollection.insertOne(clientSession, uAccount);
                }

                int shareCusFee = 0;
                String shareCusId = "";
                if (shareFees > 0) {
                    if (StrUtil.isEmpty(cusPayerId) && cusFees == 0 || cusPayerId == goods.getCid()) {  // 客商直接分享给司机
                        cusFees = cusFees - shareFees;
                        cusPayerId = goods.getCid();
                    } else {
                        shareCusFee = -shareFees;
                        shareCusId = goods.getCid();
                    }
                }

                //TODO: 1.客商付款金额,或司机付款给客商,退回客商账户
                List<Document> cusAccountDocs = new ArrayList<>();
                if (cusFees != 0)
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(cusPayerId, new BigDecimal(Math.abs(cusFees)), 4, oid, date));
                // 客商下单，友商收取司机信息费，修改友商账户
                if (shareCusFee != 0 && StrUtil.isNotEmpty(shareCusId))
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(shareCusId, new BigDecimal(shareCusFee), 9, oid, date));
                if (cusAccountDocs.size() > 0) cAccountCollection.insertMany(clientSession, cusAccountDocs);

                //TODO: 2.司机付款金额分两种
                if (driverFees > 0) {
                    //TODO: 2.1 app下单，平台信息费+友商或客商信息费+收发单位信息费 退回司机账户
                    if (isWeChat == 0) {
                        DriverAccount driverAccount = new DriverAccount();
                        driverAccount.setDid(driverPayerId);
                        driverAccount.setType(3);
                        driverAccount.setCdid(driverPayerId);
                        driverAccount.setOid(order.getOid());
                        driverAccount.setUpdateTime(date.getTime());

                        Document drvDocument = Document.parse(JSONObject.toJSONString(driverAccount));
                        drvDocument.append("totalFee", new BigDecimal(driverFees));
                        drvDocument.append("createTime", date);
                        dAccountCollection.insertOne(clientSession, drvDocument);
                    } else {
                        //TODO: 2.2 给小程序司机下单,退回微信，并更新order表
                        MongoCollection<Document> resultCollection = wxResultService.getCollection();
                        MongoCollection<Document> prepaidCollection = wxPrepaidService.getCollection();

                        Map<String, String> map = new HashMap<>();
                        Query<WxPrepaid> query = wxPrepaidService.createQuery();
                        String outTradeNo = order.getOutTradeNo();
                        String transactionId = order.getTransactionId();

                        WxPrepaid wxPrepaid = new WxPrepaid();
                        wxPrepaid.setDid(driverPayerId);
                        wxPrepaid.setType(2);//退款

                        if (StrUtil.isNotEmpty(outTradeNo)) {
                            map.put("out_trade_no", outTradeNo);
                            query.filter("outTradeNo", outTradeNo);
                            wxPrepaid.setOutTradeNo(date.getTime() + WXPayUtil.generateNonceStr(19));
                        }
                        if (StrUtil.isNotEmpty(transactionId)) {
                            map.put("transaction_id", transactionId);
                            query.filter("transactionId", transactionId);
                            wxPrepaid.setTransactionId(transactionId);
                        }
                        wxPrepaid.setAppid(config.getAppID());
                        wxPrepaid.setMchId(config.getMchID());
                        wxPrepaid.setOutRefundNo(order.getOid());//商户退款单号
                        wxPrepaid.setTotalFee(driverFees);//订单金额
                        wxPrepaid.setRefundFee(driverFees);//退款金额
                        wxPrepaid.setRefundDesc("客商撤单");//退款原因
                        wxPrepaid.setUpdateTime(date.getTime());

                        map.put("out_refund_no", order.getOid());//商户退款单号
                        map.put("total_fee", String.valueOf(driverFees));//订单金额
                        map.put("refund_fee", String.valueOf(driverFees));//退款金额
                        if (order.getDelType() == 1) {
                            map.put("refund_desc", "司机主动撤单");
                            wxPrepaid.setRefundDesc("司机主动撤单");//退款原因
                        } else if (order.getDelType() == 0) {
                            map.put("refund_desc", "客商撤单");
                            wxPrepaid.setRefundDesc("客商撤单");//退款原因
                        }

                        query.filter("outRefundNo", order.getOid());
                        query.filter("pay", true);
                        //根据“退款单号，微信订单号，商户订单号”判断订单是否多次重复提交
                        WxPrepaid prepaid = wxPrepaidService.get(query);
                        if (prepaid != null) {
                            clientSession.abortTransaction();
                            return ServerResponse.createSuccess("该订单已经退款");
                        }

                        WXPay wxpay = new WXPay(config, "", true, false);
                        map = wxpay.refund(map);//退款
                        String returnCode = map.get("return_code");

                        if (returnCode.equals("SUCCESS")) {

                            String resultCode = map.get("result_code");
                            if (resultCode.equals("SUCCESS")) {
                                //预支付表
                                wxPrepaid.setNonceStr(map.get("nonce_str"));
                                wxPrepaid.setSign(map.get("sign"));
                                wxPrepaid.setPay(true);
                                wxPrepaid.setTransactionId(map.get("transaction_id"));
                                Document prepaidDoc = Document.parse(JSONObject.toJSONString(wxPrepaid));
                                prepaidDoc.append("createTime", date);
                                prepaidCollection.insertOne(clientSession, prepaidDoc);

                                //支付结果保存
                                WxResult wxResult = new WxResult();
                                wxResult.setSign(map.get("sign"));
                                wxResult.setNonceStr(map.get("nonce_str"));
                                wxResult.setOutTradeNo(map.get("out_trade_no"));
                                wxResult.setTransactionId(map.get("transaction_id"));
                                wxResult.setOutRefundNo(map.get("out_refund_no"));//商户退款单号
                                wxResult.setRefundId(map.get("refund_id"));//微信退款单号
                                wxResult.setRefundFee(Utils.parseInt(map.get("refund_fee"), 0));//退款总金额
                                wxResult.setSettlementRefundFee(Utils.parseInt(map.get("settlement_refund_fee"), 0));//应结退款金额
                                wxResult.setTotalFee(Utils.parseInt(map.get("total_fee"), 0));
                                wxResult.setSettlementTotalFee(Utils.parseInt(map.get("settlement_total_fee"), 0));//应结订单金额
                                wxResult.setFeeType(map.get("fee_type"));
                                wxResult.setCashFee(Utils.parseInt(map.get("cash_fee"), 0));
                                wxResult.setCashFeeType(map.get("cash_fee_type"));
                                wxResult.setCashRefundFee(Utils.parseInt(map.get("cash_refund_fee"), 0));
                                wxResult.setResultCode(resultCode);
                                wxResult.setReturnCode(returnCode);
                                wxResult.setUpdateTime(date.getTime());
                                Document document = Document.parse(JSONObject.toJSONString(wxResult));
                                document.append("createTime", date);
                                resultCollection.insertOne(clientSession, document);//保存支付成功结果

//                            return ServerResponse.createSuccess("审核通过，零钱支付的退款20分钟内到账，银行卡支付的退款3个工作日");
                            } else {
                                clientSession.abortTransaction();
                                return ServerResponse.createError(map.get("err_code_des"));
                            }

                        } else {
                            clientSession.abortTransaction();
                            return ServerResponse.createError(map.get("return_msg"));
                        }
                    }
                }
            }
            //TODO:3.申请退款成功，更新order表审核通过
            Document updateQuery = new Document("oid", order.getOid());

            orderLogisticsCollection.deleteOne(clientSession, updateQuery); // 删除物流信息表
            orderTakingCollection.deleteOne(clientSession, updateQuery);    // 删除司机接单表
            pushInfoCollection.deleteOne(clientSession, updateQuery); // 删除推送消息

            updateQuery.append("updateTime", order.getUpdateTime());
            params.clear();
            updMap.clear();
            params.put("isCheck", isCheck);
            params.put("updateTime", date.getTime());
            updMap.put("$set", params);
            orderCollection.updateOne(clientSession, updateQuery, BsonDocument.parse(JSON.toJSONString(updMap)));//更新order表   支付成功 格式为：updateOne( { "id" : "123" }, { $set: { "name" : "abc" } );

            clientSession.commitTransaction();
            return ServerResponse.createSuccess("审核通过，app接单退款到账户成功；微信小程序接单零钱支付的退款20分钟内到账，银行卡支付的退款3个工作日");
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("保存失败，请重试");
        } finally {
            clientSession.close();
        }
    }

    private Document uAccountInsert(String uid, String oid, int fees) {
        SysUnitAccount sysUnitAccount = new SysUnitAccount();
        sysUnitAccount.setTotalFee(new BigDecimal(fees));
        sysUnitAccount.setUid(uid);// 发货单位id
        sysUnitAccount.setOid(oid);
        if (fees > 0) { // 单位付平台费用
            sysUnitAccount.setType(3);
        } else {    // 客商或司机付单位费用
            sysUnitAccount.setType(5);
        }
        sysUnitAccount.setUpdateTime(new Date().getTime());
        Document uAccount = Document.parse(JSONObject.toJSONString(sysUnitAccount));
        uAccount.append("createTime", new Date());
        return uAccount;
    }

    // 撤单，客商、司机、单位 退款和扣款金额
    private Map<String, Integer> getFees(Order order) {

        int cusFees = order.getFees();         // 客商付款金额
        String cusPayerId = order.getPayerId();// 客商id

        int driverFees = 0;
        int balanceFees = order.getBalanceFees();// 司机付款金额
        int shareFees = order.getShareFees();   // 友商收取的费用
        String driverPayerId = order.getDriverPayerId();// 司机id
        if (balanceFees > 0 || shareFees > 0) {
            driverFees += balanceFees + shareFees;
        }

        int feesOut = order.getFeesOut();   // 平台收取发货单位 信息费
        String payerIdOut = order.getPayerIdOut();//发货单位id
        int feesIn = order.getFeesIn();     // 平台收取收货单位 信息费
        String payerIdIn = order.getPayerIdIn();//收货单位id

        int fees1Out = order.getFees1Out(); // 发货一级单位收取信息费
        String payerId1Out = order.getPayerId1Out();// 支付人id
        if (fees1Out > 0) {
            // 单位收取的信息费 退回 司机或客商账户
            Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees1Out, payerId1Out);
            driverFees = (int) map.get("driverFees");
            cusFees = (int) map.get("cusFees");
        }

        int fees1In = order.getFees1In();   // 收货一级单位收取信息费
        if (fees1In > 0) {
            String payerId1In = order.getPayerId1In();// 支付人id

            // 单位收取的信息费 退回 司机或客商账户
            Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees1In, payerId1In);
            driverFees = (int) map.get("driverFees");
            cusFees = (int) map.get("cusFees");
        }

        int fees2Out = order.getFees2Out(); // 发货二级单位收取信息费
        if (fees2Out > 0) {
            String payerId2Out = order.getPayerId2Out();// 支付人id
            // 单位收取的信息费 退回 司机或客商账户
            Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees2Out, payerId2Out);
            driverFees = (int) map.get("driverFees");
            cusFees = (int) map.get("cusFees");
        }

        int fees2In = order.getFees2In();   // 收货二级单位收取信息费
        if (fees2In > 0) {
            String payerId2In = order.getPayerId2In();// 支付人id
            // 单位收取的信息费 退回 司机或客商账户
            Map map = orderRecordService.getUnitFees(driverFees, cusFees, driverPayerId, cusPayerId, fees2In, payerId2In);
            driverFees = (int) map.get("driverFees");
            cusFees = (int) map.get("cusFees");
        }

        Map<String, Integer> result = new HashMap<>();
        result.put("cusFees", cusFees);
        result.put("driverFees", driverFees);
        result.put("shareFees", shareFees);
        result.put("feesOut", feesOut);
        result.put("feesIn", feesIn);
        result.put("fees1In", fees1In);
        result.put("fees2In", fees2In);
        result.put("fees1Out", fees1Out);
        result.put("fees2Out", fees2Out);

        return result;
    }

    @Override
    public EGridResult refundQuery(Integer page, Integer rows) {
        //订单撤销，通过审核，并且客商/司机已付款   的信息列表
        Query<Order> query = this.createQuery();

        String carNum = request.getParameter("carNum");
        if (StrUtil.isNotEmpty(carNum)) query.filter("carNum", carNum);

        query.filter("delete", true);
        query.filter("isCheck", 1);
        query.criteria("balanceFees").greaterThan(0);
        query.filter("isWeChat", 1);//只查询小程序退款是否成功
//        List<Order> orderList = this.list(query);
        EGridResult<Order> eGridResult = this.findPage(page, rows, query);
        List<Order> orderList = eGridResult.getRows();

        List<RefundData> refundDataList = new ArrayList<>();
        for (Order order : orderList) {
            String errMsg = "";
            String refundSuccTime = "";
            String refundRecv = "";
            String oid = order.getOid();
            WxResult wxResult = wxResultService.get("outRefundNo", oid);

            RefundData refundData = new RefundData();
            refundData.setOid(oid);
            refundData.setBalanceFees(BigDecimal.valueOf(order.getBalanceFees()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元

            //wxResult 为空说明管理员还未审核，
            if (wxResult == null) {
                errMsg = "商家审核中";
            } else if (wxResult.getRefundSuccessTime() == null) {   //管理员审核，但是未从微信查询退款是否成功
                try {
                    WXPay wxpay = new WXPay(config, "", true, false);
                    Map<String, String> map = new HashMap<>();
                    map.put("out_refund_no", oid);
                    map = wxpay.refundQuery(map);
                    errMsg = map.get("refund_status_0");
                    refundRecv = map.get("refund_recv_accout_0");
                    refundSuccTime = map.get("refund_success_time_0");

                    if (StrUtil.isNotEmpty(refundSuccTime)) {
                        Query<WxResult> query1 = wxResultService.createQuery().filter("outRefundNo", oid);
                        UpdateOperations<WxResult> updateOperations = wxResultService.createUpdateOperations();
                        updateOperations.set("refundSuccessTime", refundSuccTime);
                        updateOperations.set("refundRecv", refundRecv);
                        updateOperations.set("updateTime", new Date().getTime());
                        wxResultService.update(query1, updateOperations);//更新退款成功时间
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {    //已从微信查询退款成功
                refundSuccTime = wxResult.getRefundSuccessTime();
                refundRecv = wxResult.getRefundRecv();
                errMsg = "SUCCESS";
            }

            refundData.setErrMsg(errMsg);
            refundData.setRefundTime(refundSuccTime);
            refundData.setBeginPoint(order.getBeginPoint());
            refundData.setEndPoint(order.getEndPoint());
            refundData.setCarNum(order.getCarNum());
            refundData.setTradeName(order.getTradeName());
            refundData.setIsWeChat(order.getIsWeChat());
            refundData.setRefundRecv(refundRecv);
            refundData.setDriverPayerId(order.getDriverPayerId());
            refundDataList.add(refundData);
        }

        return new EGridResult(eGridResult.getTotal(), refundDataList);
        /*int size = refundDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        return new EGridResult(refundDataList.size(), refundDataList.subList(fromIndex, toIndex));*/
    }

    @Override
    public ServerResponse<EGridResult> getOrders(Integer page, Integer rows) {
        Query<Order> query = this.createQuery();

        String dvrMobile = request.getParameter("dvrMobile");
        if (StrUtil.isNotEmpty(dvrMobile)) {
            DriverInfo driverInfo = driverInfoService.get("mobile", dvrMobile);
            if (driverInfo == null) return ServerResponse.createError("手机号错误");
            query.criteria("did").equal(driverInfo.getObjectId().toHexString());
        }
        String cusMobile = request.getParameter("cusMobile");
        if (StrUtil.isNotEmpty(cusMobile)) {
            CustomerUser customerUser = customerUserService.get("mobile", cusMobile);
            if (customerUser == null) return ServerResponse.createError("手机号错误");
            query.criteria("cid").equal(customerUser.getObjectId().toHexString());
        }
        String oid = request.getParameter("oid");
        if (StrUtil.isNotEmpty(oid)) query.criteria("oid").equal(oid);
        EGridResult<Order> result = this.findPage(page, rows, query);

        List<OrderDetailData> list = new ArrayList<>();
        for (Order o : result.getRows()) {
            OrderDetailData orderDetailData = new OrderDetailData();
            BeanUtils.copyProperties(o, orderDetailData);
            if (StrUtil.isNotEmpty(o.getOutFee0()) && o.getOutFee0() > 0)
                orderDetailData.setOutFee0(o.getOutFee0() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee0()) && o.getInFee0() > 0)
                orderDetailData.setInFee0(o.getInFee0() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee1()) && o.getOutFee1() > 0)
                orderDetailData.setOutFee1(o.getOutFee1() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee2()) && o.getOutFee2() > 0)
                orderDetailData.setOutFee2(o.getOutFee2() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee1()) && o.getInFee1() > 0)
                orderDetailData.setInFee1(o.getInFee1() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee2()) && o.getInFee2() > 0)
                orderDetailData.setInFee2(o.getInFee2() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee3()) && o.getOutFee3() > 0)
                orderDetailData.setOutFee3(o.getOutFee3() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee4()) && o.getOutFee4() > 0)
                orderDetailData.setOutFee4(o.getOutFee4() / 100.0);
            if (StrUtil.isNotEmpty(o.getOutFee5()) && o.getOutFee5() > 0)
                orderDetailData.setOutFee5(o.getOutFee5() / 100.0);
            if (StrUtil.isNotEmpty(o.getInFee5()) && o.getInFee5() > 0)
                orderDetailData.setInFee5(o.getInFee5() / 100.0);

            list.add(orderDetailData);
        }
        return ServerResponse.createSuccess("查询成功", new EGridResult(result.getTotal(), list));
    }
}
