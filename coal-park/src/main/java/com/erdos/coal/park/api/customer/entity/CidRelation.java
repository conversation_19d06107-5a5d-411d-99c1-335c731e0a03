package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Reference;

@Entity(value = "t_cid_relation", noClassnameStored = true)
public class CidRelation extends BaseMongoInfo {
    private String cid1;
    private String cid2;
    @Reference(value = "customerUserID1", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser1;
    @Reference(value = "customerUserID2", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser2;

    public String getCid1() {
        return cid1;
    }

    public void setCid1(String cid1) {
        this.cid1 = cid1;
    }

    public String getCid2() {
        return cid2;
    }

    public void setCid2(String cid2) {
        this.cid2 = cid2;
    }

    public CustomerUser getCustomerUser1() {
        return customerUser1;
    }

    public void setCustomerUser1(CustomerUser customerUser1) {
        this.customerUser1 = customerUser1;
    }

    public CustomerUser getCustomerUser2() {
        return customerUser2;
    }

    public void setCustomerUser2(CustomerUser customerUser2) {
        this.customerUser2 = customerUser2;
    }
}
