package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.IDriverGroupDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.DriverGroup;
import com.erdos.coal.park.api.customer.entity.DriverGroupPrequel;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.pojo.DriverGroupData;
import com.erdos.coal.park.api.customer.pojo.DriverInfoData;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IDriverGroupPrequelService;
import com.erdos.coal.park.api.customer.service.IDriverGroupService;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service("driverGroupService")
public class DriverGroupServiceImpl extends BaseMongoServiceImpl<DriverGroup, IDriverGroupDao> implements IDriverGroupService {
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private ILockedService lockedService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IDriverGroupPrequelService driverGroupPrequelService;

    /**
     * 添加司机组 接口
     * param: String cid
     * param: String groupName
     * param: String[] mobiles
     */
    @Override
    public ServerResponse<DriverGroup> addDriverGroup(String groupName, String[] mobiles) {
        String cid = ShiroUtils.getUserId();

        DriverGroup group = checkGroupName(cid, groupName);
        if (group != null) return ServerResponse.createError("名称《" + groupName + "》已经存在");

        CustomerUser cUser = customerUserService.getByPK(cid);

        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.Add_Driver_Group.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {
            try {
                //通过司机mobile 查询司机id
                Query<DriverInfo> query = driverInfoService.createQuery();
                query = query.filter("mobile in ", mobiles);
                List<DriverInfo> driverInfos = query.find().toList();
                List<String> idList = new ArrayList<>();
                for (DriverInfo di : driverInfos) {
                    idList.add(di.getObjectId().toString());
                }

                //添加并保存司机组信息
                DriverGroup dg = new DriverGroup();
                dg.setCid(cid);
                dg.setGroupName(groupName);
                dg.setDvrId(idList.toArray(new String[0]));
                dg.setDriverInfos(driverInfos);
                dg.setCustomerUser(cUser);

                DriverGroup result = this.save(dg);
                result.setGroupNo(result.getObjectId().toString());//将新生成的 _id 作为司机组编号返回。
                result.setCustomerUser(null);
                result.setDriverInfos(null);

                return ServerResponse.createSuccess("添加成功", result);
            } finally {
                lockedService.unLock(cid, SysConstants.LockType.Add_Driver_Group.getType());
            }
        }
    }

    /**
     * 查询客商的司机组 接口
     * param: String cid
     * param: String groupNo
     * return:
     */
    @Override
    //public ServerResponse<List<DriverGroupData>> driverGroupListData(String groupNo) {
    public ServerResponse<EGridResult> driverGroupListData(String groupNo, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        //TODO:1.查询客商下的 所有司机组
        Query<DriverGroup> query = this.createQuery();
        query.filter("cid", cid);
        if (groupNo != null) {
            query.filter("_id", new ObjectId(groupNo));
        }
        //List<DriverGroup> dgList = this.list(query);
        EGridResult<DriverGroup> eGridResult = findPage(page, rows, query);
        List<DriverGroup> dgList = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        //TODO:2.遍历司机组，得到司机id 再查询 司机姓名和车牌号
        List<DriverGroupData> resultData = new ArrayList<>();    //要返回的司机组和司机的信息
        for (DriverGroup dg : dgList) {
            String[] driverIds = dg.getDvrId();

            //查询司机池中没有加入了黑名单的司机，黑名单的不显示
            Query<DriverPool> dpQuery = driverPoolService.createQuery();
            dpQuery.criteria("whiteOrBlack").notEqual(0);
            dpQuery.filter("cid", cid);
            dpQuery.filter("did in", driverIds);
            List<DriverPool> driverPools = dpQuery.find().toList();
            List<ObjectId> objectIds = new ArrayList<>();
            for (DriverPool dp : driverPools) {
                objectIds.add(new ObjectId(dp.getDid()));
            }

            /*//查询包含id数组中所有id的司机信息（in）
            Query<DriverInfo> diQuery = driverInfoService.createQuery();
            diQuery.filter("objectId in ", objectIds);
            List<DriverInfo> driverInfos = diQuery.find().toList();*/
            List<DriverInfo> driverInfos = dg.getDriverInfos();

            //转pojo
            List<DriverInfoData> driverInfoDatas = new ArrayList<>();
            for (DriverInfo di : driverInfos) {
                if (!objectIds.contains(di.getObjectId())) continue;
                DriverInfoData driverInfoData = new DriverInfoData();
                driverInfoData.setId(di.getObjectId().toString());
                driverInfoData.setName(di.getName());
                driverInfoData.setCarNum(di.getCarNum());
                driverInfoData.setMobile(di.getMobile());

                driverInfoDatas.add(driverInfoData);
            }

            //包装要返回数据
            DriverGroupData driverGroupData = new DriverGroupData();
            driverGroupData.setGroupNo(dg.getObjectId().toString());
            driverGroupData.setGroupName(dg.getGroupName());
            driverGroupData.setDriverInfos(driverInfoDatas);
            resultData.add(driverGroupData);
        }

        //TODO:3.返回查询结果
        //return ServerResponse.createSuccess("查询成功", resultData);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    /**
     * 删除司机组 接口
     * param: String groupNo
     * 主要动作: 直接主键删除 t_driver_group 集合中满足条件的数据
     */
    @Override
    public ServerResponse<String> delDriverGroup(String groupNo) {

        this.delete(groupNo);

        //TODO:返回查询结果
        return ServerResponse.createSuccess("删除成功");
    }

    private DriverGroup checkGroupName(String cid, String groupName) {
        Query<DriverGroup> groupQuery = this.createQuery();
        groupQuery.criteria("cid").equal(cid);
        groupQuery.criteria("groupName").equal(groupName);
        DriverGroup group = this.get(groupQuery);
        return group;
    }

    @Override
    public ServerResponse<String> editDriverGroup(String groupNo, String groupName) {
        String cid = ShiroUtils.getUserId();
        DriverGroup group = checkGroupName(cid, groupName);
        if (group != null) return ServerResponse.createError("名称《" + groupName + "》已经存在");

        Query<DriverGroup> query = this.createQuery();
        query.criteria("_id").equal(new ObjectId(groupNo));

        UpdateOperations<DriverGroup> updateOperations = this.createUpdateOperations();
        updateOperations.set("groupName", groupName);

        this.update(query, updateOperations);

        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<String> addDriverGroup2(String groupNo, String groupName, String[] mobiles) {
        if (StrUtil.isEmpty(groupNo) && StrUtil.isEmpty(groupName)) return ServerResponse.createError("司机组编号或名称不能都为空");
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        if (StrUtil.isEmpty(groupNo)) {
            DriverGroup group = checkGroupName(cid, groupName);
            if (group == null) {
                group = new DriverGroup();
                group.setCid(cid);
                group.setGroupName(groupName);
                group.setCustomerUser(cUser);
                group = this.save(group);
            }
            groupNo = group.getObjectId().toHexString();
        } else {
            DriverGroup group = this.getByPK(groupNo);
            groupName = group.getGroupName();
        }

        if (mobiles != null && mobiles.length > 0) {
            //通过司机mobile 查询司机id
            Query<DriverInfo> query = driverInfoService.createQuery();
            query = query.filter("mobile in ", mobiles);
            List<DriverInfo> driverInfos = query.find().toList();

            List<DriverGroupPrequel> prequelList = new ArrayList<>();
            for (DriverInfo di : driverInfos) {
                //添加司机消息，征的司机同意后将司机加入司机组
                DriverGroupPrequel prequel = new DriverGroupPrequel();
                prequel.setGroupNo(groupNo);
                prequel.setCid(cid);
                prequel.setcName(cUser.getName());
                prequel.setDid(di.getObjectId().toHexString());
                prequel.setCustomerUser(cUser);
                prequel.setDriverInfo(di);
                prequelList.add(prequel);
            }
            driverGroupPrequelService.saveAndSend(prequelList, groupNo, groupName);
            return ServerResponse.createSuccess("添加成功,等待司机同意");
        }
        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> delDriver(String groupNo, String[] mobile) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        DriverGroup group = this.getByPK(groupNo);
        String[] dvrId = group.getDvrId();
        if (dvrId == null) return ServerResponse.createError("司机组下无司机，删除失败");

        List<String> dvrIds = new ArrayList<>(Arrays.asList(dvrId));
        List<DriverInfo> dvrs = group.getDriverInfos();

        List<String> delMobile = new ArrayList<>();
        List<DriverGroupPrequel> prequelList = new ArrayList<>();
        for (String mob : mobile) {
            DriverInfo driverInfo = driverInfoService.get("mobile", mob);
            if (dvrs.contains(driverInfo)) {
                DriverGroupPrequel prequel = new DriverGroupPrequel();

                prequel.setcName(cUser.getName());
                prequel.setDid(driverInfo.getObjectId().toHexString());
                prequel.setDriverInfo(driverInfo);
                prequelList.add(prequel);

                delMobile.add(mob);
                dvrIds.remove(driverInfo.getObjectId().toHexString());
                dvrs.remove(driverInfo);
            }
        }

        if (delMobile.size() > 0) {
//            driverGroupPrequelService.sendDelInformation(prequelList, groupName);

            Query<DriverGroup> query = this.createQuery().filter("_id", group.getObjectId());
            query.criteria("updateTime").equal(group.getUpdateTime());
            UpdateOperations<DriverGroup> updateOperations = this.createUpdateOperations().set("dvrId", dvrIds.toArray());
            updateOperations.set("driverInfo", dvrs);
        }
        return ServerResponse.createSuccess("移除成功");
    }

    @Override
    public ServerResponse<EGridResult> driverGroupListData2(String groupNo, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        //TODO:1.查询客商下的 所有司机组
        Query<DriverGroup> query = this.createQuery();
        query.filter("cid", cid);
        if (groupNo != null) {
            query.filter("_id", new ObjectId(groupNo));
        }
        EGridResult<DriverGroup> eGridResult = findPage(page, rows, query);
        List<DriverGroup> dgList = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        //TODO:2.遍历司机组，得到司机id 再查询 司机姓名和车牌号
        List<DriverGroupData> resultData = new ArrayList<>();    //要返回的司机组和司机的信息
        for (DriverGroup dg : dgList) {
            String[] driverIds = dg.getDvrId();

            //查询司机池中没有加入了黑名单的司机，黑名单的不显示
            /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
            dpQuery.criteria("whiteOrBlack").notEqual(0);
            dpQuery.filter("cid", cid);
            dpQuery.filter("did in", driverIds);
            List<DriverPool> driverPools = dpQuery.find().toList();
            List<ObjectId> objectIds = new ArrayList<>();
            for (DriverPool dp : driverPools) {
                objectIds.add(new ObjectId(dp.getDid()));
            }*/

            //查询包含id数组中所有id的司机信息（in）
            List<DriverInfo> driverInfos = dg.getDriverInfos();

            //转pojo
            List<DriverInfoData> driverInfoDatas = new ArrayList<>();
            for (DriverInfo di : driverInfos) {
                //if (!objectIds.contains(di.getObjectId())) continue;
                DriverInfoData driverInfoData = new DriverInfoData();
                driverInfoData.setId(di.getObjectId().toString());
                driverInfoData.setName(di.getName());
                driverInfoData.setCarNum(di.getCarNum());
                driverInfoData.setMobile(di.getMobile());

                driverInfoDatas.add(driverInfoData);
            }

            //包装要返回数据
            DriverGroupData driverGroupData = new DriverGroupData();
            driverGroupData.setGroupNo(dg.getObjectId().toString());
            driverGroupData.setGroupName(dg.getGroupName());
            driverGroupData.setDriverInfos(driverInfoDatas);
            resultData.add(driverGroupData);
        }

        //TODO:3.返回查询结果
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }
}
