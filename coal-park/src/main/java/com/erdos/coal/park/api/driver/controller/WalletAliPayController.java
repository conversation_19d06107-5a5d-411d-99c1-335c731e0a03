package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.service.IPayAliPrepaidService;
import com.erdos.coal.park.api.driver.service.IPayAliResultService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"司机钱包支付宝接口"
@RestController
@RequestMapping("/api/dvr/ali_pay")
public class WalletAliPayController extends BaseController {

    @Resource
    private IPayAliPrepaidService payAliPrepaidService;
    @Resource
    private IPayAliResultService payAliResultService;

    @InvokeLog(description = "2.请求商户服务端，获取签名后的订单信息 接口", printReturn = false) //日志
    @PostMapping(value = "/create_ali_order")
    public ServerResponse<String> createAliOrderHandler(
            @RequestParam(value = "body") String body,         //"订单描述"
            @RequestParam(value = "subject") String subject,   //"订单标题"
            @RequestParam(value = "totalAmount") String totalAmount//"订单总金额"
    ) throws GlobalException {
        return payAliPrepaidService.createAliOrder(body, subject, totalAmount);
    }

    /**
     * 第3个同步通知接口
     * 支付宝支付成功后.通知页面
     */
    @InvokeLog(description = "9.同步支付结果返回客商服务端，验签，解析支付结果 接口", printReturn = false) //日志
    @PostMapping(value = "/analysis_aliPay_result")
    public ServerResponse<String> analysisAliPayResultHandler(
            @RequestParam(value = "jsonParam") String jsonParam    //"支付宝返回的支付结果"
    ) throws GlobalException {
        return payAliResultService.checkPayResult(jsonParam);
    }

    @InvokeLog(description = "查询支付结果 接口", printReturn = false) //日志
    @PostMapping(value = "/search_aliPay_result")
    public ServerResponse<String> searchAliPayResultHandler(
            @RequestParam(value = "outTradeNo") String outTradeNo,             //"订单号"
            @RequestParam(value = "tradeNo", required = false) String tradeNo  //"支付宝订单号"
    ) throws GlobalException {
        return payAliResultService.driverSearchAliPayResult(outTradeNo, tradeNo);
    }

}
