package com.erdos.coal.park.api.manage.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

// 设置固定集合, 如果 占用空间、文档个数都设置，哪个首先达到按哪个算。

// 目前 平均单个文档 485 bytes, 平均每天 173138 个
// 单个文档 * 文档个数 = 占用空间 [(平均单个文档 485 bytes) * (431,868个) = 209,455,980 bytes 约等于 200M]

// 500M 或 100W 个文档的固定集合
// 单个文档 * 文档个数 = 占用空间 [(平均单个文档 485 bytes) * (100_0000个) 约等于 463M]

// 单个文档 * 文档个数 = 占用空间 [(平均单个文档 500 bytes) * (200_0000个) 约等于 954M]
@Entity(value = "t_app_log", noClassnameStored = true, cap = @CappedAt(count = 200_0000, value = 1024 * 1024 * 1024))
@Indexes({ // 在其它集合中也有可能需要创建 createTime 索引，避免重名，设置名称为 appLog_createTime_1
        @Index(fields = @Field("createTime"), options = @IndexOptions(name = "appLog_createTime_1")),
        @Index(fields = @Field("consumes"))
})
public class AppLog extends BaseMongoInfo {

    public static class Server {
        private String host; //主机
        private String thread; //当前线程
        private String position; //位置

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public String getThread() {
            return thread;
        }

        public void setThread(String thread) {
            this.thread = thread;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }
    }

    public static class Client {
        private String browserName; //浏览器名称
        private String browserVersion;  //浏览器版本
        private String systemName; //操作系统名称
        private String ip; //ip

        public String getBrowserName() {
            return browserName;
        }

        public void setBrowserName(String browserName) {
            this.browserName = browserName;
        }

        public String getBrowserVersion() {
            return browserVersion;
        }

        public void setBrowserVersion(String browserVersion) {
            this.browserVersion = browserVersion;
        }

        public String getSystemName() {
            return systemName;
        }

        public void setSystemName(String systemName) {
            this.systemName = systemName;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }
    }

    private Client client; // client info

    private String user; //用户
    private String userType; //用户类型
    private String method; //方法
    private String description; //描述
    private String parameter; //参数
    private String result; //结果
    private long consumes;//消耗

    private Server server; // server info

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public long getConsumes() {
        return consumes;
    }

    public void setConsumes(long consumes) {
        this.consumes = consumes;
    }

    public Server getServer() {
        return server;
    }

    public void setServer(Server server) {
        this.server = server;
    }
}
