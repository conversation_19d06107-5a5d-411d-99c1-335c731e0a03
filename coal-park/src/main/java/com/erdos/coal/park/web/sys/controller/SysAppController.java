package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysApp;
import com.erdos.coal.park.web.sys.service.ISysAppService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/sys/app")
public class SysAppController extends BaseController {

    @Resource
    private ISysAppService appService;

    @RequestMapping("/app_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(appService.loadGrid(page, rows));
    }

    @RequestMapping("/save")
    public ServerResponse saveHandler(@RequestParam(value = "file") MultipartFile file) throws GlobalException {
        return appService.saveApp(file);
    }

    @RequestMapping("/delete")
    public ServerResponse deleteHandler(@RequestBody SysApp sysApp) throws GlobalException {
        return appService.deleteApp(sysApp);
    }
}
