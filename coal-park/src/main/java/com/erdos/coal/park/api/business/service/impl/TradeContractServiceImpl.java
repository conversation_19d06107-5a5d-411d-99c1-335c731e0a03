package com.erdos.coal.park.api.business.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.business.dao.ITradeContractDao;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.service.ITradeContractService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("tradeContractService")
public class TradeContractServiceImpl extends BaseMongoServiceImpl<TradeContract, ITradeContractDao> implements ITradeContractService {
    @Override
    public boolean add(List<TradeContract> tradeContracts) {
        try {
            this.save(tradeContracts);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public List<Integer> edit(Map<Integer, TradeContract> updateTradeContractMap) {
        List<Integer> successIds = new ArrayList<>();
        for (Map.Entry<Integer, TradeContract> entry : updateTradeContractMap.entrySet()){
            TradeContract tradeContract = entry.getValue();
            Query<TradeContract> query = this.createQuery();
            query.criteria("bizContractCode").equal(tradeContract.getBizContractCode());
            UpdateOperations<TradeContract> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(tradeContract.getAllowLimit())) updateOperations.set("allowLimit",tradeContract.getAllowLimit());
            if (StrUtil.isNotEmpty(tradeContract.getBalanceCtrl())) updateOperations.set("balanceCtrl",tradeContract.getBalanceCtrl());
            if (StrUtil.isNotEmpty(tradeContract.getBizContractName())) updateOperations.set("bizContractName",tradeContract.getBizContractName());
            if (StrUtil.isNotEmpty(tradeContract.getBizModule())) updateOperations.set("bizModule",tradeContract.getBizModule());
            if (StrUtil.isNotEmpty(tradeContract.getBizType())) updateOperations.set("bizType",tradeContract.getBizType());
            if (StrUtil.isNotEmpty(tradeContract.getBizUnitCode())) updateOperations.set("bizUnitCode",tradeContract.getBizUnitCode());
            if (StrUtil.isNotEmpty(tradeContract.getContractnum())) updateOperations.set("contractnum",tradeContract.getContractnum());
            if (StrUtil.isNotEmpty(tradeContract.getContractType())) updateOperations.set("contractType",tradeContract.getContractType());
            if (StrUtil.isNotEmpty(tradeContract.getEffeDate())) updateOperations.set("effeDate",tradeContract.getEffeDate());
            if (StrUtil.isNotEmpty(tradeContract.getKindChange())) updateOperations.set("kindChange",tradeContract.getKindChange());
            if (StrUtil.isNotEmpty(tradeContract.getLimitAllow())) updateOperations.set("limitAllow",tradeContract.getLimitAllow());
            if (StrUtil.isNotEmpty(tradeContract.getLimitWeight())) updateOperations.set("limitWeight",tradeContract.getLimitWeight());
            if (StrUtil.isNotEmpty(tradeContract.getMoneyType())) updateOperations.set("moneyType",tradeContract.getMoneyType());
            if (StrUtil.isNotEmpty(tradeContract.getOverdraft())) updateOperations.set("overdraft",tradeContract.getOverdraft());
            if (StrUtil.isNotEmpty(tradeContract.getPerformState())) updateOperations.set("performState",tradeContract.getPerformState());
            if (StrUtil.isNotEmpty(tradeContract.getSubCode())) updateOperations.set("subCode",tradeContract.getSubCode());
            if (StrUtil.isNotEmpty(tradeContract.getTermDate())) updateOperations.set("termDate",tradeContract.getTermDate());
            if (StrUtil.isNotEmpty(tradeContract.getTicketType())) updateOperations.set("ticketType",tradeContract.getTicketType());
            if (StrUtil.isNotEmpty(tradeContract.getTradeType())) updateOperations.set("tradeType",tradeContract.getTradeType());
            if (StrUtil.isNotEmpty(tradeContract.getTriggerBalance())) updateOperations.set("triggerBalance",tradeContract.getTriggerBalance());
            if (StrUtil.isNotEmpty(tradeContract.getTriggerSwitch())) updateOperations.set("triggerSwitch",tradeContract.getTriggerSwitch());
            if (StrUtil.isNotEmpty(tradeContract.getUnitCode())) updateOperations.set("unitCode",tradeContract.getUnitCode());
            if (StrUtil.isNotEmpty(tradeContract.getVoucherEarlyProcess())) updateOperations.set("voucherEarlyProcess",tradeContract.getVoucherEarlyProcess());
            if (StrUtil.isNotEmpty(tradeContract.getVoucherExpire())) updateOperations.set("voucherExpire",tradeContract.getVoucherExpire());
            if (StrUtil.isNotEmpty(tradeContract.getWeightLimit())) updateOperations.set("weightLimit",tradeContract.getLimitWeight());
            TradeContract contract = this.findAndModify(query, updateOperations);
            if (contract != null) successIds.add(entry.getKey());
        }
        return successIds;
    }

    @Override
    public boolean del(List<String> bizContractCodes) {
        try {
            Query<TradeContract> query = this.createQuery();
            query.criteria("bizContractCode").in(bizContractCodes);
            this.delete(query);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
