package com.erdos.coal.park.api.manage.pojo;

import java.io.Serializable;

public class ImageResult implements Serializable {
    private String id;
    private int xpos;               // 滑块的坐标x轴
    private int ypos;               // 滑块的坐标y轴
    private int cutImageWidth;      // 滑块的宽
    private int cutImageHeight;     // 滑块的高

    private String cutImage;        // 滑块图片
    private String oriImage;        // 背景图（初扣掉滑块的图）

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getXpos() {
        return xpos;
    }

    public void setXpos(int xpos) {
        this.xpos = xpos;
    }

    public int getYpos() {
        return ypos;
    }

    public void setYpos(int ypos) {
        this.ypos = ypos;
    }

    public int getCutImageWidth() {
        return cutImageWidth;
    }

    public void setCutImageWidth(int cutImageWidth) {
        this.cutImageWidth = cutImageWidth;
    }

    public int getCutImageHeight() {
        return cutImageHeight;
    }

    public void setCutImageHeight(int cutImageHeight) {
        this.cutImageHeight = cutImageHeight;
    }

    public String getCutImage() {
        return cutImage;
    }

    public void setCutImage(String cutImage) {
        this.cutImage = cutImage;
    }

    public String getOriImage() {
        return oriImage;
    }

    public void setOriImage(String oriImage) {
        this.oriImage = oriImage;
    }
}
