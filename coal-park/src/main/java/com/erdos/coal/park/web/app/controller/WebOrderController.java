package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.entity.Appraise;
import com.erdos.coal.park.web.app.pojo.OrderData;
import com.erdos.coal.park.web.app.service.IWebOrderService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/order")
public class WebOrderController extends BaseController {

    @Resource
    private IWebOrderService webOrderService;

    @RequestMapping("/listGoods")
    public ServerResponse<EGridResult> listGoodsHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webOrderService.listGoods(page, rows, "0"));
    }

    @PostMapping("/listOrders")
    public ServerResponse<EGridResult> listOrdersHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webOrderService.listOrders(page, rows));
    }

    @PostMapping("/appraise_list")
    public ServerResponse<EGridResult> appealListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webOrderService.appraiseList(page, rows));
    }

    @PostMapping("/appraise_save")
    public ServerResponse<String> appraiseSave(@RequestBody Appraise appraise) {
        return webOrderService.appraiseSave(appraise);
    }

    @PostMapping("/check_orders")
    public ServerResponse<EGridResult> checkOrders(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webOrderService.checkOrders(page, rows));
    }

    @PostMapping("/check_save")
    public ServerResponse<String> checkSave(@RequestBody OrderData order) {
        return webOrderService.checkSave(order);
    }

    @PostMapping("/refund_query")
    public ServerResponse<EGridResult> refundQuery(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webOrderService.refundQuery(page, rows));
    }

    @PostMapping("/get_orders")
    public ServerResponse<EGridResult> getOrdersHandler(Integer page, Integer rows) throws GlobalException {
        return webOrderService.getOrders(page, rows);
    }
}
