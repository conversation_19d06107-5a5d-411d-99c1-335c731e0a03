package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderData implements Serializable {
    private String oid;     //订单号
    private String gid;     ////货运信息编号
    private String carNum;  //车牌号
    private String mobile;  //接单时司机的手机号
    private boolean delete;   //订单撤销标记
    private Integer delType;    //订单废除类型 0：客商，1：司机主动撤单，2：司机拒绝接单
    private Integer finishTag;  //订单完成标识（0：下单成功；1：运输途中；2：订单完成）

    private String cid;         //客商编号
    private String did;      //司机编号
    private String outMinMax;   //发货单位 票号区间
    private String inMinMax;    //收货单位 票号区间

    private String tradeName;                       //商品名称
    private String beginPoint;                      //起点
    private String endPoint;                        //终点
    private Double price;                           //单价
    private String outUnitName;                     //发货单位名称
    private String inUnitName;                      //收货单位名称

    private Integer mold;                            //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2

    private BigDecimal cusFees = new BigDecimal(0);     //客商退款金额（元）
    private BigDecimal driverFees = new BigDecimal(0);   //司机退款金额（元）
    private BigDecimal shareFees = new BigDecimal(0);   //客商或友商扣款金额（元）
    private BigDecimal feesOut = new BigDecimal(0);     //发货单位退款金额（元）
    private BigDecimal feesIn = new BigDecimal(0);     //收货单位退款金额（元）
    private BigDecimal fees1In = new BigDecimal(0);     //收货企业一级单位扣款金额（元）
    private BigDecimal fees2In = new BigDecimal(0);     //收货企业二级单位扣款金额（元）
    private BigDecimal fees1Out = new BigDecimal(0);     //发货企业一级单位扣款金额（元）
    private BigDecimal fees2Out = new BigDecimal(0);     //发货企业二级单位扣款金额（元）

    private String driverPayerId;   //司机id
    private String payerId; //客商支付信息费人的id

    private Integer isWeChat;    //0-给app司机下单的货运信息；1-给小程序司机下单的货运信息
    private Integer isCheck;    //订单撤销是否审核 0:未审核，1：审核，2：未通过，3：无需审核

    private String transactionId;   //微信订单号
    private String outTradeNo;  //商户订单号

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public boolean isDelete() {
        return delete;
    }

    public boolean getDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }

    public Integer getDelType() {
        return delType;
    }

    public void setDelType(Integer delType) {
        this.delType = delType;
    }

    public Integer getFinishTag() {
        return finishTag;
    }

    public void setFinishTag(Integer finishTag) {
        this.finishTag = finishTag;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getOutMinMax() {
        return outMinMax;
    }

    public void setOutMinMax(String outMinMax) {
        this.outMinMax = outMinMax;
    }

    public String getInMinMax() {
        return inMinMax;
    }

    public void setInMinMax(String inMinMax) {
        this.inMinMax = inMinMax;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getDriverPayerId() {
        return driverPayerId;
    }

    public void setDriverPayerId(String driverPayerId) {
        this.driverPayerId = driverPayerId;
    }

    public Integer getIsWeChat() {
        return isWeChat;
    }

    public void setIsWeChat(Integer isWeChat) {
        this.isWeChat = isWeChat;
    }

    public Integer getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(Integer isCheck) {
        this.isCheck = isCheck;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public BigDecimal getCusFees() {
        return cusFees;
    }

    public void setCusFees(BigDecimal cusFees) {
        this.cusFees = cusFees;
    }

    public BigDecimal getDriverFees() {
        return driverFees;
    }

    public void setDriverFees(BigDecimal driverFees) {
        this.driverFees = driverFees;
    }

    public BigDecimal getShareFees() {
        return shareFees;
    }

    public void setShareFees(BigDecimal shareFees) {
        this.shareFees = shareFees;
    }

    public BigDecimal getFeesOut() {
        return feesOut;
    }

    public void setFeesOut(BigDecimal feesOut) {
        this.feesOut = feesOut;
    }

    public BigDecimal getFeesIn() {
        return feesIn;
    }

    public void setFeesIn(BigDecimal feesIn) {
        this.feesIn = feesIn;
    }

    public BigDecimal getFees1In() {
        return fees1In;
    }

    public void setFees1In(BigDecimal fees1In) {
        this.fees1In = fees1In;
    }

    public BigDecimal getFees2In() {
        return fees2In;
    }

    public void setFees2In(BigDecimal fees2In) {
        this.fees2In = fees2In;
    }

    public BigDecimal getFees1Out() {
        return fees1Out;
    }

    public void setFees1Out(BigDecimal fees1Out) {
        this.fees1Out = fees1Out;
    }

    public BigDecimal getFees2Out() {
        return fees2Out;
    }

    public void setFees2Out(BigDecimal fees2Out) {
        this.fees2Out = fees2Out;
    }
}
