package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.pojo.OrderStatistics;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//"客商APP订单管理接口列表"
@RestController
@RequestMapping("/api/cus/order")
public class OrderController extends BaseController {
    @Resource
    private IOrderService orderService;

    @InvokeLog(description = "扫码下单-扫码 接口") //日志
    @PostMapping(value = "/sweep_code3")
    public ServerResponse<Order> sweepCode3Handler(
            @RequestParam(value = "mobile") String mobile,     //"司机手机号"
            @RequestParam(value = "carNum") String carNum,     //"司机车牌号"
            @RequestParam(value = "gid") String gid,           //"货运信息编号"
            @RequestParam(value = "longitude", required = false) String longitude, //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,   //"纬度"
            @RequestParam(value = "fee", required = false) String fee              //"友商收取金额"
    ) throws GlobalException {
        /*if (StrUtil.isNotEmpty(fee)) fee = new BigDecimal(fee).multiply(new BigDecimal(100)).toPlainString();//金额单位 元转为分
        return orderService.sweepCode3(mobile, carNum, gid, longitude, latitude, fee);*/
        return ServerResponse.createError("接口停用！");
    }

    @InvokeLog(description = "扫码下单-出示订单二维码 接口", printReturn = false) //日志
    @PostMapping(value = "/two_dimension_code2")
    public ServerResponse<Object> twoDimensionCode2Handler(
            @RequestParam(value = "gid") String gid    //"货运信息编号"
    ) throws GlobalException {
        return orderService.twoDimensionCode2(gid);
    }

    @InvokeLog(description = "订单查询 接口", printReturn = false) //日志
    @PostMapping(value = "/search_order")
    public ServerResponse<EGridResult> searchOrderHandler(
            @RequestParam(value = "orderTime", required = false) Long orderTime,   //"接单时间"
            @RequestParam(value = "endPoint", required = false) String endPoint,   //"目的地"
            @RequestParam(value = "finishTag") String finishTag,       //"0-司机接单成功,1-运输途中,2-订单完成,3-客商下单成功,4-废除订单查询"
            @RequestParam(value = "isHand", required = false) Integer isHand,      //"是否手工下单 1-手工下单，0或空-否"
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        //接口至今未调用 2022年4月25
        return ServerResponse.createError("接口停用！");
        //return orderService.searchOrder(orderTime, endPoint, finishTag.split(","), isHand, page, rows);
    }

    @InvokeLog(description = "订单查询 接口", printReturn = false) //日志
    @PostMapping(value = "/search_order2")
    public ServerResponse<EGridResult> searchOrder2Handler(
            @RequestParam(value = "orderTime", required = false) Long orderTime,   //"接单时间"
            @RequestParam(value = "endPoint", required = false) String endPoint,   //"目的地"
            @RequestParam(value = "finishTag") String finishTag,       //"0-司机接单成功,1-运输途中,2-订单完成,3-客商下单成功,4-废除订单查询"
            @RequestParam(value = "isHand", required = false) Integer isHand,      //"是否手工下单 1-手工下单，0或空-否"
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return orderService.searchOrder2(orderTime, endPoint, finishTag.split(","), isHand, page, rows);
    }

    @InvokeLog(description = "订单修改 接口") //日志
    @PostMapping(value = "/update_orders2")
    public ServerResponse<List<String>> updateOrders2Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "oids") String[] oids,   //"订单编号"
            @RequestParam(value = "outVariety", required = false) String outVariety,   //"发货的商品名称"
            @RequestParam(value = "inVariety", required = false) String inVariety      //"收货的商品名称"
    ) throws GlobalException {
        return orderService.updateOrders2(gid, oids, outVariety, inVariety, null, null);
    }

    @InvokeLog(description = "订单修改 运往地 接口") //日志
    @PostMapping(value = "/update_orders_place2")
    public ServerResponse<List<String>> updateOrdersPlace2Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "oids") String[] oids,   //"订单编号"
            @RequestParam(value = "spell") String spell,   //"运往地编号"
            @RequestParam(value = "place") String place      //"运往地名称"
    ) throws GlobalException {
        return orderService.updateOrders2(gid, oids, null, null, spell, place);
    }

    @InvokeLog(description = "货运信息中展开订单 查询接口", printReturn = false) //日志
    @PostMapping(value = "/search_gid_order2")
    public ServerResponse<EGridResult> searchGidOrder2Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "type", required = false) Integer type,  //"要展开的订单类型行 0-未接单，1-已接单，2-历史单，空-全部"
            @RequestParam(value = "page", required = false) Integer page,  //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows   //"每页多少条"
    ) throws GlobalException {
        return orderService.searchGidOrder2(gid, type, page, rows);
    }

    @InvokeLog(description = "货运信息中展开订单 查询接口", printReturn = false) //日志
    @PostMapping(value = "/search_gid_order3")
    public ServerResponse<EGridResult> searchGidOrder3Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "type", required = false) Integer type,  //"要展开的订单类型行 0-未接单，1-已接单，2-历史单，空-全部"
            @RequestParam(value = "page", required = false) Integer page,  //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows   //"每页多少条"
    ) throws GlobalException {
        return orderService.searchGidOrder3(gid, type, page, rows);
    }

    @InvokeLog(description = "友商(客商)指定车牌号派单 接口") //日志
    @PostMapping(value = "/appoint_order_car2")
    public ServerResponse<String> appointOrderCarNum2Handler(
            @RequestParam(value = "oid") String oid,            //"订单编号"
            @RequestParam(value = "carNum") String carNum,      //"指定的车牌号"
            @RequestParam(value = "driverId") String driverId   //"指定车牌的司机"
    ) throws GlobalException {
        return orderService.appointOrderCarNum2(oid, carNum, driverId);
    }

    @InvokeLog(description = "友商(客商)查询要指定的车牌号 接口") //日志
    @PostMapping(value = "/search_appoint_dvr")
    public ServerResponse<List<DriverToCar>> searchDvrByCarNumOrMobileHandler(
            @RequestParam(value = "mobile", required = false) String mobile,   //"手机号"
            @RequestParam(value = "carNum", required = false) String carNum    //"指定的车牌号"
    ) throws GlobalException {
        return orderService.searchDvrByCarNumOrMobile(mobile, carNum);
    }

    @InvokeLog(description = "订单 禁止或恢复运输 接口") //日志
    @PostMapping(value = "/update_order_transport")
    public ServerResponse<EGridResult> updateOrderIsTransportHandler(
            @RequestParam(value = "oid") String[] oid,       //"订单编号"
            @RequestParam(value = "isTransport") Integer isTransport  //"禁止或恢复运输订单，0-恢复，1-禁止"
    ) throws GlobalException {
        return orderService.updateOrderIsTransport(oid, isTransport);
    }

    @InvokeLog(description = "客商每日订单量统计 接口") //日志
    @PostMapping(value = "/statistics")
    public ServerResponse<List<OrderStatistics>> statisticsHandler(
            @RequestParam(value = "startTime", required = false) Long startTime,   //要统计的开始日期
            @RequestParam(value = "endTime", required = false) Long endTime    //要统计的结束日期
    ) throws GlobalException {
        return orderService.statisticsOrder(startTime, endTime);
    }

    @InvokeLog(description = "客商每日订单量统计 接口") //日志
    @PostMapping(value = "/statistics2")
    public ServerResponse<List<OrderStatistics>> statistics2Handler(
            @RequestParam(value = "startTime", required = false) Long startTime,   //要统计的开始日期
            @RequestParam(value = "endTime", required = false) Long endTime    //要统计的结束日期
    ) throws GlobalException {
        return orderService.statisticsOrder2(startTime, endTime);
    }

    @InvokeLog(description = "订单修改 收货业务对方净重 接口") //日志
    @PostMapping(value = "/update_order_inNetWeight")
    public ServerResponse<List<String>> updateOrderInNetWeightHandler(
            @RequestParam(value = "oid") String oid,   //"订单编号"
            @RequestParam(value = "inNetWeight", required = false) Double inNetWeight,      //"收货业务对方净重"
            @RequestParam(value = "loadPound", required = false) String loadPound,      //"收货业务对方装货单号"
            @RequestParam(value = "loadPoundPho", required = false) String loadPoundPho,      //"收货业务对方装货单照片"
            @RequestParam(value = "loadTime", required = false) Long loadTime      //"收货业务对方装货时间"
    ) throws GlobalException {
        return orderService.updateOrdersInNetWeight(oid, inNetWeight, loadPound, loadPoundPho, loadTime);
    }
}
