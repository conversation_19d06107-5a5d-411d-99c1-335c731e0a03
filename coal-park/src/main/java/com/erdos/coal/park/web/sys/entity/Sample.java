package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.util.Date;
import java.util.List;

@Entity(value = "sample", noClassnameStored = true)
public class Sample extends BaseMongoInfo {
    private Integer id;
    private Integer field1;
    private Double field2;
    private Date field3;
    private Boolean field4;
    private Long field5;
    private List<Integer> field6;
    private String[] field7;
    private String field8;
    private String field9;
    private String field10; // null

    private Integer field11;
    private Double field12;
    private Date field13;
    private Boolean field14;
    private Long field15;
    private List<Integer> field16;
    private String[] field17;
    private String field18;
    private String field19;
    private String field20; // null

    private Integer field21;
    private Double field22;
    private Date field23;
    private Boolean field24;
    private Long field25;
    private List<Integer> field26;
    private String[] field27;
    private String field28;
    private String field29;
    private String field30; // null

    private Integer field31;
    private Double field32;
    private Date field33;
    private Boolean field34;
    private Long field35;
    private List<Integer> field36;
    private String[] field37;
    private String field38;
    private String field39;
    private String field40; // null

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getField1() {
        return field1;
    }

    public void setField1(Integer field1) {
        this.field1 = field1;
    }

    public Double getField2() {
        return field2;
    }

    public void setField2(Double field2) {
        this.field2 = field2;
    }

    public Date getField3() {
        return field3;
    }

    public void setField3(Date field3) {
        this.field3 = field3;
    }

    public Boolean getField4() {
        return field4;
    }

    public void setField4(Boolean field4) {
        this.field4 = field4;
    }

    public Long getField5() {
        return field5;
    }

    public void setField5(Long field5) {
        this.field5 = field5;
    }

    public List<Integer> getField6() {
        return field6;
    }

    public void setField6(List<Integer> field6) {
        this.field6 = field6;
    }

    public String[] getField7() {
        return field7;
    }

    public void setField7(String[] field7) {
        this.field7 = field7;
    }

    public String getField8() {
        return field8;
    }

    public void setField8(String field8) {
        this.field8 = field8;
    }

    public String getField9() {
        return field9;
    }

    public void setField9(String field9) {
        this.field9 = field9;
    }

    public String getField10() {
        return field10;
    }

    public void setField10(String field10) {
        this.field10 = field10;
    }

    public Integer getField11() {
        return field11;
    }

    public void setField11(Integer field11) {
        this.field11 = field11;
    }

    public Double getField12() {
        return field12;
    }

    public void setField12(Double field12) {
        this.field12 = field12;
    }

    public Date getField13() {
        return field13;
    }

    public void setField13(Date field13) {
        this.field13 = field13;
    }

    public Boolean getField14() {
        return field14;
    }

    public void setField14(Boolean field14) {
        this.field14 = field14;
    }

    public Long getField15() {
        return field15;
    }

    public void setField15(Long field15) {
        this.field15 = field15;
    }

    public List<Integer> getField16() {
        return field16;
    }

    public void setField16(List<Integer> field16) {
        this.field16 = field16;
    }

    public String[] getField17() {
        return field17;
    }

    public void setField17(String[] field17) {
        this.field17 = field17;
    }

    public String getField18() {
        return field18;
    }

    public void setField18(String field18) {
        this.field18 = field18;
    }

    public String getField19() {
        return field19;
    }

    public void setField19(String field19) {
        this.field19 = field19;
    }

    public String getField20() {
        return field20;
    }

    public void setField20(String field20) {
        this.field20 = field20;
    }

    public Integer getField21() {
        return field21;
    }

    public void setField21(Integer field21) {
        this.field21 = field21;
    }

    public Double getField22() {
        return field22;
    }

    public void setField22(Double field22) {
        this.field22 = field22;
    }

    public Date getField23() {
        return field23;
    }

    public void setField23(Date field23) {
        this.field23 = field23;
    }

    public Boolean getField24() {
        return field24;
    }

    public void setField24(Boolean field24) {
        this.field24 = field24;
    }

    public Long getField25() {
        return field25;
    }

    public void setField25(Long field25) {
        this.field25 = field25;
    }

    public List<Integer> getField26() {
        return field26;
    }

    public void setField26(List<Integer> field26) {
        this.field26 = field26;
    }

    public String[] getField27() {
        return field27;
    }

    public void setField27(String[] field27) {
        this.field27 = field27;
    }

    public String getField28() {
        return field28;
    }

    public void setField28(String field28) {
        this.field28 = field28;
    }

    public String getField29() {
        return field29;
    }

    public void setField29(String field29) {
        this.field29 = field29;
    }

    public String getField30() {
        return field30;
    }

    public void setField30(String field30) {
        this.field30 = field30;
    }

    public Integer getField31() {
        return field31;
    }

    public void setField31(Integer field31) {
        this.field31 = field31;
    }

    public Double getField32() {
        return field32;
    }

    public void setField32(Double field32) {
        this.field32 = field32;
    }

    public Date getField33() {
        return field33;
    }

    public void setField33(Date field33) {
        this.field33 = field33;
    }

    public Boolean getField34() {
        return field34;
    }

    public void setField34(Boolean field34) {
        this.field34 = field34;
    }

    public Long getField35() {
        return field35;
    }

    public void setField35(Long field35) {
        this.field35 = field35;
    }

    public List<Integer> getField36() {
        return field36;
    }

    public void setField36(List<Integer> field36) {
        this.field36 = field36;
    }

    public String[] getField37() {
        return field37;
    }

    public void setField37(String[] field37) {
        this.field37 = field37;
    }

    public String getField38() {
        return field38;
    }

    public void setField38(String field38) {
        this.field38 = field38;
    }

    public String getField39() {
        return field39;
    }

    public void setField39(String field39) {
        this.field39 = field39;
    }

    public String getField40() {
        return field40;
    }

    public void setField40(String field40) {
        this.field40 = field40;
    }
}
