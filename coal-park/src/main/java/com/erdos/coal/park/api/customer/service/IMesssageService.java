package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.CusMessage;
import com.erdos.coal.park.api.customer.entity.OrderTaking;

import java.util.List;

public interface IMesssageService extends IBaseMongoService<OrderTaking> {
    //ServerResponse<List<OrderInfoData>> queryMes(Integer type);
    ServerResponse<EGridResult> queryMes(Integer type, Integer page, Integer rows);

    //客商消息表查询
    ServerResponse<EGridResult<CusMessage>> searchCusMsg(Integer type, Integer page, Integer rows);
}
