package com.erdos.coal.park.api.customer.pojo;

import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import dev.morphia.annotations.Reference;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class GoodsPipeline implements Serializable {
    private String gid;             //货运信息编号
    private String cid;             //客商编号
    private String tradeName;      //商品名称
    private String beginPoint;     //起点
    private String endPoint;        //终点
    private Integer total;           //总车数
    private Double weight;          //车载重量要求

    private Double price;           //单价
    private Double distance;          //总里程
    private Double tolls;           //预估过路费

    private Integer pType;          //下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单 5-企业下单

    private Integer mold;          //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2
    private String outUnitCode;             //发货单位编码
    private String outUnitName;             //发货单位名称
    private String inUnitCode;              //收货单位编码
    private String inUnitName;              //收货单位名称
    private String outBizContractCode;      //发货单位业务合同编码
    private String outBizContractName;      //发货单位业务合同名称
    private String inBizContractCode;       //收货单位业务合同编码
    private String inBizContractName;       //收货单位业务合同名称
    private String outVariety;          //发货单位产品名称
    private String inVariety;           //收货单位产品名称
    private String outDefaultDownUnit;              //发货单位 默认二级单位编码
    private String outSubName;                      //发货单位 默认二级单位名称
    private String inDefaultDownUnit;               //收货单位 默认二级单位编码
    private String inSubName;                       //收货单位 默认二级单位名称
    private String outDefaultArea;             //发货单位 默认场区编码
    private String outAreaName;                 //发货单位 默认场区名称
    private String inDefaultArea;              //收货单位 默认场区编码
    private String inAreaName;                  //收货单位 默认场区名称

    private Integer outMin;         //发货单位 最小票号
    private Integer outMax;         //发货单位 最大票号
    private Integer inMin;          //收货单位 最小票号
    private Integer inMax;          //收货单位 最大票号

    private Integer status; //货运信息状态 0-货运信息下订单可接单 1-货运信息下订单暂停接单
    private Integer delNum = 0; //废除的车数
    private Integer orderNum0 = 0;  //货运信息下未接单车数
    private Integer orderNum1 = 0;  //货运信息下已接单未完成车数
    private Integer orderNum2 = 0;  //货运信息下已接单已完成车数
    private Integer orderNum3 = 0;  //货运信息下已入场车数
    /*
     * checking 3退回1 orderNum2-1
     *       发货 或 收货，checking=3 orderNum2+1，orderNum1-1
     *       收发货，两边checking=3，才是订单完成。orderNum2+1，orderNum1-1
     * */
    //货运信息下所有订单毛皮中合计值
    private Double outGrossWeight;
    private Double outTareWeight;
    private Double inGrossWeight;
    private Double inTareWeight;

    private String carNum;          //指定车号下单时的车牌号
    private String driverId;        //指定车号下单时的司机编号
    private int isPay = 0;          //客商是否代付(0-不代付，1-代付)
    private String payCid;          //愿意代付的客商编号

    private String groupNo; //发布货运信息分配可抢单的司机组编号

    private Integer isWeChat;    //0-给app司机下单的货运信息；1-给小程序司机下单的货运信息

    /*
     * 0-未分享货单给好友客商
     * 1-分享一部分货单给好友客商,还有部分可以分享
     * 2-可以分享的订单已经全部分享给好友客商了
     * */
    private Integer share = 0;
    private String shareCid;    //货单来自编号为cid的客商分享   改成货单分享给友商的id
    private String shareGid;    //货单来自编号为gid的客商货单分享 字段作废

    @Reference(value = "customerUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser;

    //以下需要收取的信息费均为"单价"，即单车的费用，总费还需要乘以车数total
    private Integer feesOut = 0;    //平台需要收取发货企业的信息费（分）
    private String outUnitPayId;    //愿意代付的发货单位编号
    private Integer feesIn = 0;    //平台需要收取收货企业的信息费（分）
    private String inUnitPayId;    //愿意代付的收货单位编号
    private Integer fees1Out = 0;    //发货企业一级单位需要收取的信息费（分）
    private Integer fees1In = 0;    //收货企业一级单位需要收取的信息费（分）
    private Integer fees2Out = 0;    //发货企业二级单位需要收取的信息费（分）
    private Integer fees2In = 0;    //收货企业二级单位需要收取的信息费（分）
    private Integer fees3 = 0;    //客商或友商需要收取的信息费（分）
    private Integer fee5Out = 0;    //三方分账
    private Integer fee5In = 0;     //三方分账
    private String fee5OutId;       //三方账户
    private String fee5InId;        //三方账户
    private Integer fee5OutType;    //三方账户类型 0-商户，1-个人
    private Integer fee5InType;     //三方账户类型 0-商户，1-个人

    private String longitude;
    private String latitude;

    private List<GOrder> orders;
    private List<GOrder> shareOrders;

    private String spell;   //运往地编码
    private String place;   //运往地名称

    private String outTradingUnit;      //发货交易单位编号
    private String outTradingUnitName;      //发货交易单位名称
    private String inTradingUnit;       //收货交易单位编号
    private String inTradingUnitName;       //收货交易单位名称

    private String outCusDesc;       //发货单位客商备注
    private String inCusDesc;       //收货单位客商备注

    private Integer partSurplusNum; //货运信息按车数批次分享给司机微信后剩余的车数

    private Date createTime;
    private Long updateTime;

    private String shareCid012; //0-表示订单是客商的，1-表示订单分享给友商，2-表示订单来自客商分享
    private List<CustomerUser> cusUser;   //客商信息
    private List<CustomerUser> shareCustomerUser;   //友商信息

    private Integer mold2;          //是否往货业务，空或0-否，1-是
    private String beginDistrictCode;//起点区划编码
    private String endDistrictCode; //终点区划编码

    private String outVarietyCode;  // 智慧能源-合同备案中的煤种ID

    private List<SysUnit> inSubUnit;   // 收货二级单位信息
    private Double inNetWeightOne;     // 采购业务需要录入对方的净重(单车净重)
    private String loadPound;           // 采购业务需要录入装货单号（下单一车时传参）
    private Long loadTime;           // 采购业务需要录入装货时间（下单一车时传参）

    // 2024-12-28 急加
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;

    public Integer getMold2() {
        return mold2;
    }

    public void setMold2(Integer mold2) {
        this.mold2 = mold2;
    }

    public String getBeginDistrictCode() {
        return beginDistrictCode;
    }

    public void setBeginDistrictCode(String beginDistrictCode) {
        this.beginDistrictCode = beginDistrictCode;
    }

    public String getEndDistrictCode() {
        return endDistrictCode;
    }

    public void setEndDistrictCode(String endDistrictCode) {
        this.endDistrictCode = endDistrictCode;
    }

    public String getShareCid012() {
        return shareCid012;
    }

    public void setShareCid012(String shareCid012) {
        this.shareCid012 = shareCid012;
    }

    public List<CustomerUser> getCusUser() {
        return cusUser;
    }

    public void setCusUser(List<CustomerUser> cusUser) {
        this.cusUser = cusUser;
    }

    public List<CustomerUser> getShareCustomerUser() {
        return shareCustomerUser;
    }

    public void setShareCustomerUser(List<CustomerUser> shareCustomerUser) {
        this.shareCustomerUser = shareCustomerUser;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public Integer getpType() {
        return pType;
    }

    public void setpType(Integer pType) {
        this.pType = pType;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getOutDefaultArea() {
        return outDefaultArea;
    }

    public void setOutDefaultArea(String outDefaultArea) {
        this.outDefaultArea = outDefaultArea;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getInDefaultArea() {
        return inDefaultArea;
    }

    public void setInDefaultArea(String inDefaultArea) {
        this.inDefaultArea = inDefaultArea;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public Integer getOutMin() {
        return outMin;
    }

    public void setOutMin(Integer outMin) {
        this.outMin = outMin;
    }

    public Integer getOutMax() {
        return outMax;
    }

    public void setOutMax(Integer outMax) {
        this.outMax = outMax;
    }

    public Integer getInMin() {
        return inMin;
    }

    public void setInMin(Integer inMin) {
        this.inMin = inMin;
    }

    public Integer getInMax() {
        return inMax;
    }

    public void setInMax(Integer inMax) {
        this.inMax = inMax;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDelNum() {
        return delNum;
    }

    public void setDelNum(Integer delNum) {
        this.delNum = delNum;
    }

    public Integer getOrderNum0() {
        return orderNum0;
    }

    public void setOrderNum0(Integer orderNum0) {
        this.orderNum0 = orderNum0;
    }

    public Integer getOrderNum1() {
        return orderNum1;
    }

    public void setOrderNum1(Integer orderNum1) {
        this.orderNum1 = orderNum1;
    }

    public Integer getOrderNum2() {
        return orderNum2;
    }

    public void setOrderNum2(Integer orderNum2) {
        this.orderNum2 = orderNum2;
    }

    public Integer getOrderNum3() {
        return orderNum3;
    }

    public void setOrderNum3(Integer orderNum3) {
        this.orderNum3 = orderNum3;
    }

    public Double getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(Double outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public Double getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(Double outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public Double getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(Double inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public Double getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(Double inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public int getIsPay() {
        return isPay;
    }

    public void setIsPay(int isPay) {
        this.isPay = isPay;
    }

    public String getPayCid() {
        return payCid;
    }

    public void setPayCid(String payCid) {
        this.payCid = payCid;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Integer getIsWeChat() {
        return isWeChat;
    }

    public void setIsWeChat(Integer isWeChat) {
        this.isWeChat = isWeChat;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getShareCid() {
        return shareCid;
    }

    public void setShareCid(String shareCid) {
        this.shareCid = shareCid;
    }

    public String getShareGid() {
        return shareGid;
    }

    public void setShareGid(String shareGid) {
        this.shareGid = shareGid;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }

    public Integer getFeesOut() {
        return feesOut;
    }

    public void setFeesOut(Integer feesOut) {
        this.feesOut = feesOut;
    }

    public String getOutUnitPayId() {
        return outUnitPayId;
    }

    public void setOutUnitPayId(String outUnitPayId) {
        this.outUnitPayId = outUnitPayId;
    }

    public Integer getFeesIn() {
        return feesIn;
    }

    public void setFeesIn(Integer feesIn) {
        this.feesIn = feesIn;
    }

    public String getInUnitPayId() {
        return inUnitPayId;
    }

    public void setInUnitPayId(String inUnitPayId) {
        this.inUnitPayId = inUnitPayId;
    }

    public Integer getFees1Out() {
        return fees1Out;
    }

    public void setFees1Out(Integer fees1Out) {
        this.fees1Out = fees1Out;
    }

    public Integer getFees1In() {
        return fees1In;
    }

    public void setFees1In(Integer fees1In) {
        this.fees1In = fees1In;
    }

    public Integer getFees2Out() {
        return fees2Out;
    }

    public void setFees2Out(Integer fees2Out) {
        this.fees2Out = fees2Out;
    }

    public Integer getFees2In() {
        return fees2In;
    }

    public void setFees2In(Integer fees2In) {
        this.fees2In = fees2In;
    }

    public Integer getFees3() {
        return fees3;
    }

    public void setFees3(Integer fees3) {
        this.fees3 = fees3;
    }

    public Integer getFee5Out() {
        return fee5Out;
    }

    public void setFee5Out(Integer fee5Out) {
        this.fee5Out = fee5Out;
    }

    public Integer getFee5In() {
        return fee5In;
    }

    public void setFee5In(Integer fee5In) {
        this.fee5In = fee5In;
    }

    public String getFee5OutId() {
        return fee5OutId;
    }

    public void setFee5OutId(String fee5OutId) {
        this.fee5OutId = fee5OutId;
    }

    public String getFee5InId() {
        return fee5InId;
    }

    public void setFee5InId(String fee5InId) {
        this.fee5InId = fee5InId;
    }

    public Integer getFee5OutType() {
        return fee5OutType;
    }

    public void setFee5OutType(Integer fee5OutType) {
        this.fee5OutType = fee5OutType;
    }

    public Integer getFee5InType() {
        return fee5InType;
    }

    public void setFee5InType(Integer fee5InType) {
        this.fee5InType = fee5InType;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public List<GOrder> getOrders() {
        return orders;
    }

    public void setOrders(List<GOrder> orders) {
        this.orders = orders;
    }

    public List<GOrder> getShareOrders() {
        return shareOrders;
    }

    public void setShareOrders(List<GOrder> shareOrders) {
        this.shareOrders = shareOrders;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getOutTradingUnit() {
        return outTradingUnit;
    }

    public void setOutTradingUnit(String outTradingUnit) {
        this.outTradingUnit = outTradingUnit;
    }

    public String getOutTradingUnitName() {
        return outTradingUnitName;
    }

    public void setOutTradingUnitName(String outTradingUnitName) {
        this.outTradingUnitName = outTradingUnitName;
    }

    public String getInTradingUnit() {
        return inTradingUnit;
    }

    public void setInTradingUnit(String inTradingUnit) {
        this.inTradingUnit = inTradingUnit;
    }

    public String getInTradingUnitName() {
        return inTradingUnitName;
    }

    public void setInTradingUnitName(String inTradingUnitName) {
        this.inTradingUnitName = inTradingUnitName;
    }

    public String getOutCusDesc() {
        return outCusDesc;
    }

    public void setOutCusDesc(String outCusDesc) {
        this.outCusDesc = outCusDesc;
    }

    public String getInCusDesc() {
        return inCusDesc;
    }

    public void setInCusDesc(String inCusDesc) {
        this.inCusDesc = inCusDesc;
    }

    public Integer getPartSurplusNum() {
        return partSurplusNum;
    }

    public void setPartSurplusNum(Integer partSurplusNum) {
        this.partSurplusNum = partSurplusNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getOutVarietyCode() {
        return outVarietyCode;
    }

    public void setOutVarietyCode(String outVarietyCode) {
        this.outVarietyCode = outVarietyCode;
    }

    public Double getInNetWeightOne() {
        return inNetWeightOne;
    }

    public void setInNetWeightOne(Double inNetWeightOne) {
        this.inNetWeightOne = inNetWeightOne;
    }

    public String getLoadPound() {
        return loadPound;
    }

    public void setLoadPound(String loadPound) {
        this.loadPound = loadPound;
    }

    public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }

    public List<SysUnit> getInSubUnit() {
        return inSubUnit;
    }

    public void setInSubUnit(List<SysUnit> inSubUnit) {
        this.inSubUnit = inSubUnit;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }
}
