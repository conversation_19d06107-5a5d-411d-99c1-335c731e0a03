package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;
import java.util.Date;

public class AppraiseData implements Serializable {

    private String oid;//订单编号
    private String customerName;//客商姓名
    private String customerMobile;//客商手机号
    private String driverName;//司机姓名
    private String carNum;//车牌号
    private String startPoint;//起点
    private String endPoint;//终点
    private Date createTime;//评价日期
    private Integer type;//0-未评价，1-已评价，2-已投诉；
    private Integer starsNum;//服务评价--几颗星
    private boolean complain;//是否投诉 false-未投诉，true-投诉
    private String reasons;//投诉理由
    private String result;//投诉处理结果
    private Long updateTime;//更新时间

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerMobile() {
        return customerMobile;
    }

    public void setCustomerMobile(String customerMobile) {
        this.customerMobile = customerMobile;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStarsNum() {
        return starsNum;
    }

    public void setStarsNum(Integer starsNum) {
        this.starsNum = starsNum;
    }

    public boolean isComplain() {
        return complain;
    }

    public void setComplain(boolean complain) {
        this.complain = complain;
    }

    public String getReasons() {
        return reasons;
    }

    public void setReasons(String reasons) {
        this.reasons = reasons;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
