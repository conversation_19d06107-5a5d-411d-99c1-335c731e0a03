package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.driver.entity.EmergencyContact;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IEmergencyContactService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

//"司机APP设置接口列表"
@RestController
@RequestMapping("/api/dvr")
public class InstallController extends BaseController {

    @Resource
    private IEmergencyContactService emergencyContactService;

    @Resource
    private IDriverInfoService driverInfoService;

    @InvokeLog(description = "应急联系人 接口") //日志
    @PostMapping(value = "/emergency_contact")
    public ServerResponse<String> emergencyContactHandler(
            @RequestParam(value = "name", required = false) String name,   //"姓名"
            @RequestParam(value = "mobile") String mobile,                 //"手机号码"
            @RequestParam(value = "code") String code                      //"短信验证码"
    ) throws GlobalException {
        return emergencyContactService.saveEmergencyContact(name, mobile, code);
    }

    @InvokeLog(description = "应急联系人查询 接口", printReturn = false)//日志\
    @PostMapping(value = "/emergency_contact_query")
    public ServerResponse<EmergencyContact> emergencyContactQueryHandler() throws GlobalException {
        String did = ShiroUtils.getUserId();
        EmergencyContact ec = emergencyContactService.get("did", did);
        return ServerResponse.createSuccess("查询成功", ec);
    }

    @InvokeLog(description = "更换手机号 接口") //日志
    @PostMapping(value = "/change_mobile")
    public ServerResponse<String> changeMobileHandler(
            @RequestParam(value = "mobile") String mobile, //"新手机号码"
            @RequestParam(value = "code") String code      //"短信验证码"
    ) throws GlobalException {
        return driverInfoService.updateMobile(mobile, code);
    }

    @InvokeLog(description = "交易密码 接口")//日志
    @PostMapping(value = "/trade_pwd")
    public ServerResponse<String> tradePwdHandler(
            @RequestParam(value = "identity", required = false) String identity,   //"司机身份证号（后4位）"
            @RequestParam(value = "pwd") String pwd,                               //"交易密码"
            @RequestParam(value = "confirmPwd") String confirmPwd                  //"确认交易密码"
    ) throws GlobalException {
        return driverInfoService.tradePwd(identity, pwd, confirmPwd);
    }

    @InvokeLog(description = "交易密码是否需要输入司机身份证号 接口")//日志
    @PostMapping(value = "/get_identity")
    public ServerResponse<Map<String, String>> getIdentityHandler() throws GlobalException {
        return driverInfoService.getIdentity();
    }
}
