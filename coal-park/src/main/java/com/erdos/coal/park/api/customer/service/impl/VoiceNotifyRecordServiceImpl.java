package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.customer.dao.IVoiceNotifyRecordDao;
import com.erdos.coal.park.api.customer.entity.VoiceNotifyRecord;
import com.erdos.coal.park.api.customer.service.IVoiceNotifyRecordService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("voiceNotifyRecordService")
public class VoiceNotifyRecordServiceImpl extends BaseMongoServiceImpl<VoiceNotifyRecord, IVoiceNotifyRecordDao> implements IVoiceNotifyRecordService {

    @Override
    public void saveVoiceNotify(String carNum, String unitName, String mobile, Map<String, String> resultMap) {
        try {
            if (!"0".equals(resultMap.get("resultcode"))) {
                VoiceNotifyRecord voiceNotifyRecord = new VoiceNotifyRecord();
                voiceNotifyRecord.setMobile(mobile);
                voiceNotifyRecord.setResultCode(resultMap.get("resultcode"));
                voiceNotifyRecord.setPlateNumber(carNum);
                voiceNotifyRecord.setUnitName(unitName);
                this.save(voiceNotifyRecord);
            }
        } catch (Exception e) {
            logger.error("saveVoiceNotify 异常，语音通知返回结果：" + resultMap);
            logger.error("saveVoiceNotify 数据处理异常：" + e.getMessage());
        }
    }
}
