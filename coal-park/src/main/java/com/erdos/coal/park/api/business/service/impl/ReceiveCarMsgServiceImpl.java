package com.erdos.coal.park.api.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.business.entity.ShipmentOrder;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.pojo.BizOrderData;
import com.erdos.coal.park.api.business.pojo.OrderStatistics;
import com.erdos.coal.park.api.business.pojo.VehicleTypeCost;
import com.erdos.coal.park.api.business.service.IReceiveCarMsgService;
import com.erdos.coal.park.api.business.service.IShipmentOrderService;
import com.erdos.coal.park.api.business.service.ITradeContractService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.*;
import com.erdos.coal.park.api.driver.pojo.CoalTicket;
import com.erdos.coal.park.api.driver.service.*;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.app.entity.CusHDDKS;
import com.erdos.coal.park.web.app.entity.DeviceInfo;
import com.erdos.coal.park.web.app.service.ICusHDDKSService;
import com.erdos.coal.park.web.app.service.IDeviceService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.hdd.http.HddEncryptRequest;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.aggregation.Projection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Struct;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("receiveCarMsgService")
public class ReceiveCarMsgServiceImpl implements IReceiveCarMsgService {
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private IOrderDimensionService orderDimensionService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private ICarDao carDao;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverSignInService driverSignInService;
    @Resource
    private IQuarantineInfoService quarantineInfoService;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    private IGoodsService goodsService;
    @Resource
    private MongoClient client;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IShipmentOrderService shipmentOrderService;
    @Resource
    private ICusHDDKSService cusHDDKSService;
    @Resource
    private IOrderPositionService orderPositionService;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private ITradeContractService tradeContractService;

    protected final static Logger logger = LoggerFactory.getLogger(ReceiveCarMsgServiceImpl.class);

    @Override
    public ServerResponse<BizOrderData> searchOrder(String md5Oid, String unitCode, String subCode, boolean isDvrApp) {
        /*
         * 返回结果：企业系统对应的票号inMinMax 或者 outMinMax，订单的检票状态checking
         * 当物流模式 为 发货物流（0） 或者 收货物流（1) 可直接返回查询结果
         * 当物流模式 为 收发货物流（2）时，查询发货单位时 可直接返回查询结果
         *                                  查询收货单位时 要判断outChecking为3检票状态时，才可以返回查询结果
         *
         * 2020-0715,当收发货一级单位相同时，
         * 先判断outChecking是否为3 ，不为3，返回outBillCode和outChecking
         *                  outChecking为3时，去判断inChecking是否为3，不为3，返回inBillCode和inChecking，
         *                                                             为3时，返回订单已完结
         * */
        Order order;
        String oid = md5Oid;
        if (md5Oid.length() == 32) {
            if (isDvrApp) {
                OrderDimension orderDimension = orderDimensionService.get("md5Oid", md5Oid);
                if (orderDimension == null)
                    return ServerResponse.createError("订单二维码无效");
                oid = orderDimension.getOid();
            } else {
                OrderTaking ot = orderTakingService.get("oid", oid);
                if (null == ot || ot.getDriverIsAgree() != 1) return ServerResponse.createError("司机未接受订单");
            }
            order = orderService.get("oid", oid);
        } else {
            order = orderService.get("cipcherText", md5Oid);
        }

        if (order == null || order.isDelete() || order.getCarNum() == null)
            return ServerResponse.createError("查无此订单");
        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("此订单被客商禁止运输");

        //TODO: LIGX
        //if (!unitCode.equals(order.getOutUnitCode()) || !unitCode.equals(order.getInUnitCode()))
        if (!unitCode.equals(order.getOutUnitCode()) && !unitCode.equals(order.getInUnitCode()))
            return ServerResponse.createError("订单和单位信息不一致");

        String billCode;
        Integer checking;
        String carNum;
        if (unitCode.equals(order.getOutUnitCode()) && unitCode.equals(order.getInUnitCode())) {
            if (order.getOutChecking() != 3 || (StrUtil.isNotEmpty(subCode) && subCode.equals(order.getOutDefaultDownUnit()))) {
                billCode = order.getOutBillCode();
                checking = order.getOutChecking();
                carNum = order.getCarNum();
            } else {
                billCode = order.getInBillCode();
                checking = order.getInChecking();
                carNum = order.getCarNum();
            }
            /*else if (order.getInChecking() != 3 || (StrUtil.isNotEmpty(subCode) && subCode.equals(order.getInDefaultDownUnit()))) {
                billCode = order.getInBillCode();
                checking = order.getInChecking();
                carNum = order.getCarNum();
            } else {
                return ServerResponse.createError("订单已完结！");
            }*/
        } else {
            if (unitCode.equals(order.getOutUnitCode())) {
                billCode = order.getOutBillCode();
                checking = order.getOutChecking();
                carNum = order.getCarNum();
            } else if (unitCode.equals(order.getInUnitCode()) && (order.getMold() != 2 || (order.getOutChecking() == 3))) {
                billCode = order.getInBillCode();
                checking = order.getInChecking();
                carNum = order.getCarNum();
            } else {
                return ServerResponse.createError("订单未（发货）出库");
            }
        }


        // 返回值添加接单车辆的皮重信息
        Double tareWeight = 0.0;

        Double capacity = null;    //载重kg 识别行驶证照片
        String axlesNumber = ""; //车轴数
        String carType = "";    //车型
        String carTypeName = "";//车型名称
        if (StrUtil.isNotEmpty(carNum)) {
            OrderTaking ot = orderTakingService.get("oid", oid);
            if (ot != null) {
                String driverId = ot.getDid();
                Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
                dtcQuery.filter("carNum", carNum);
                dtcQuery.filter("driverId", driverId);
                DriverToCar dtc = driverToCarDao.get(dtcQuery);
                Car car = carDao.getByPK(dtc.getCarId());
                if (car != null) {
                    capacity = car.getCarInfo().getCapacity();
                    axlesNumber = car.getCarInfo().getAxlesNumber();
                    carType = car.getCarInfo().getCarTypeNum().toString();
                    carTypeName = car.getCarInfo().getCarType();
                    if (StrUtil.isNotEmpty(car.getTareWeight())) tareWeight = car.getTareWeight();
                }

            }
            /*DriverInfo driverInfo = driverInfoService.get("carNum", carNum);
            if (driverInfo != null) {
                capacity = driverInfo.getCapacity();
                axlesNumber = driverInfo.getAxlesNumber();
                carType = driverInfo.getCarType();
            }*/
        }

        double otherGross = (double) 0;
        double otherTare = (double) 0;
//        Date otherOutTime = new Date();
//        Date otherInTime = new Date();
        String otherOutTime = IdUnit.date2DateString(new Date());
        String otherInTime = IdUnit.date2DateString(new Date());
        if (order.getMold() == 2 && unitCode.equals(order.getInUnitCode())) {
            if (StrUtil.isNotEmpty(order.getOutGrossWeight()))
                otherGross = Double.parseDouble(order.getOutGrossWeight());
            if (StrUtil.isNotEmpty(order.getOutTareWeight()))
                otherTare = Double.valueOf(order.getOutTareWeight());
//            if (StrUtil.isNotEmpty(order.getOtherOutTime())) otherOutTime = order.getOtherOutTime();
//            if (StrUtil.isNotEmpty(order.getOtherInTime())) otherInTime = order.getOtherInTime();
            if (StrUtil.isNotEmpty(order.getOtherOutTime()))
                otherOutTime = IdUnit.date2DateString(order.getOtherOutTime());
            if (StrUtil.isNotEmpty(order.getOtherInTime()))
                otherInTime = IdUnit.date2DateString(order.getOtherInTime());
        }

        //返回值，添加订单防疫信息申报以及审核结果
        String isQua;
        String quaResult = "0";
        QuarantineInfo quarantineInfo = quarantineInfoService.get("oid", order.getOid());
        if (quarantineInfo != null) {
            isQua = "1";
            if (StrUtil.isNotEmpty(quarantineInfo.getAudit()) && quarantineInfo.getAudit() != 0)
                quaResult = String.valueOf(quarantineInfo.getAudit() % 2);
        } else {
            isQua = "0";
        }

        //返回值添加订单运往地信息
        String spell = order.getSpell();   //运往地编码
        String place = order.getPlace();   //运往地名称

        BizOrderData result = new BizOrderData(billCode, checking, carNum, capacity, axlesNumber, carType, otherGross, otherTare, otherOutTime, otherInTime);
        result.setIsQua(isQua);
        result.setQuaResult(quaResult);
        if (StrUtil.isNotEmpty(spell)) result.setSpell(spell);
        if (StrUtil.isNotEmpty(place)) result.setPlace(place);
        result.setOid(oid);
        if (StrUtil.isNotEmpty(order.getDid())) {
            //返回值添加司机姓名和司机电话
            DriverInfo driverInfo = driverInfoService.getByPK(order.getDid());
            result.setDvrName(driverInfo.getName());
            result.setDvrMobile(driverInfo.getMobile());
            if (StrUtil.isNotEmpty(driverInfo.getIdentity())) result.setDvrIdentity(driverInfo.getIdentity());
            result.setCipcherText(order.getCipcherText());
            result.setTareWeight(tareWeight);
        }
        // 返回值添加 采购业务需要添加的 对方净重、装货单号、装货时间
        if (StrUtil.isNotEmpty(order.getInNetWeight())) result.setInNetWeight(order.getInNetWeight());
        if (StrUtil.isNotEmpty(order.getInNetWeightPho())) {
            String phoUrl = photoFileService.publicReadUrl(order.getInNetWeightPho());
            result.setInNetWeightPho(phoUrl);
        } else if (StrUtil.isNotEmpty(order.getLoadPoundPho())) {
            String phoUrl = photoFileService.publicReadUrl(order.getLoadPoundPho());
            result.setInNetWeightPho(phoUrl);
        }
        if (StrUtil.isNotEmpty(order.getLoadPound())) result.setLoadPound(order.getLoadPound());
        if (StrUtil.isNotEmpty(order.getLoadTime())) result.setLoadTime(IdUnit.timeStamp2Date(order.getLoadTime()));
        result.setCarTypeName(carTypeName);
        // 返回值添加 司机需要支付装卸费或补交判断 (以下逻辑基于 "司机必须先做出车型选择，厂区app确认车型在后" 使用流程做出判断)
        String needPayCost = "0";
        String needAddPayCost = "0";
        String defaultDownUnit ;
        if (unitCode.equals(order.getOutUnitCode())) {
            defaultDownUnit = order.getOutDefaultDownUnit();
        } else {
            defaultDownUnit = order.getInDefaultDownUnit();
        }
        SysUnit subUnit = sysUnitService.get("code", defaultDownUnit);
        if (StrUtil.isNotEmpty(subUnit.getHandlingCost()) && subUnit.getHandlingCost() == 1 && StrUtil.isNotEmpty(subUnit.getMchId()) && StrUtil.isNotEmpty(subUnit.getKey())) {
            if (unitCode.equals(order.getOutUnitCode())) {
                TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getOutBizContractCode());
                if (tradeContract.isVehicleFee()) {
                    result.setVehicleCode(order.getVehicleCodeOutDvr());

                    if (!order.getPay1()) needPayCost = "1";

                    if (StrUtil.isNotEmpty(order.getHandlingCostOutAPP()) && StrUtil.isNotEmpty(order.getHandlingCostOutDvr())
                            && (order.getHandlingCostOutAPP() - order.getHandlingCostOutDvr() > 0) && !order.getPay2())
                        needAddPayCost = "1";
                }
            } else if (unitCode.equals(order.getInUnitCode())) {
                TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getInBizContractCode());
                if (tradeContract.isVehicleFee()) {
                    result.setVehicleCode(order.getVehicleCodeInDvr());

                    if (!order.getPay3()) needPayCost = "1";

                    if (StrUtil.isNotEmpty(order.getHandlingCostInAPP()) && StrUtil.isNotEmpty(order.getHandlingCostInDvr())
                            && (order.getHandlingCostInAPP() - order.getHandlingCostInDvr() > 0) && !order.getPay4())
                        needAddPayCost = "1";
                }
            }
        } else {
            SysUnit unit = sysUnitService.get("code", unitCode);
            if (StrUtil.isNotEmpty(unit.getHandlingCost()) && unit.getHandlingCost() == 1 && StrUtil.isNotEmpty(unit.getMchId()) && StrUtil.isNotEmpty(unit.getKey())) {
                if (unitCode.equals(order.getOutUnitCode())) {
                    TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getOutBizContractCode());
                    if (tradeContract.isVehicleFee()) {
                        result.setVehicleCode(order.getVehicleCodeOutDvr());

                        if (!order.getPay1()) needPayCost = "1";

                        if (StrUtil.isNotEmpty(order.getHandlingCostOutAPP()) && StrUtil.isNotEmpty(order.getHandlingCostOutDvr())
                                && (order.getHandlingCostOutAPP() - order.getHandlingCostOutDvr() > 0) && !order.getPay2())
                            needAddPayCost = "1";
                    }
                } else if (unitCode.equals(order.getInUnitCode())) {
                    TradeContract tradeContract = tradeContractService.get("bizContractCode", order.getInBizContractCode());
                    if (tradeContract.isVehicleFee()) {
                        result.setVehicleCode(order.getVehicleCodeInDvr());

                        if (!order.getPay3()) needPayCost = "1";

                        if (StrUtil.isNotEmpty(order.getHandlingCostInAPP()) && StrUtil.isNotEmpty(order.getHandlingCostInDvr())
                                && (order.getHandlingCostInAPP() - order.getHandlingCostInDvr() > 0) && !order.getPay4())
                            needAddPayCost = "1";
                    }
                }
            }
        }
        result.setNeedPayCost(needPayCost);
        result.setNeedAddPayCost(needAddPayCost);

        return ServerResponse.createSuccess(result);
    }

    @Override
    public ServerResponse<Object> checkingOrder(Map<String, Object> id, JSONArray inData, JSONArray outData) {
        /*
         * billCode 组成： (5-销售。6-采购)
         * 单位特定编码10位 + 5/6（一位数） + 最大最小号补零后生成的9位数
         * 001   0   011001	5	000000001 共20位
         *
         * String grossWeight,毛重
         * String tareWeight  皮重
         * 可能要做个数据的记录
         * */
        Query<Order> queryIn = orderService.createQuery();
        queryIn.filter("share", 0);
        UpdateOperations<Order> updateOperationsIn = orderService.createUpdateOperations();
        for (Object o : inData) {
            String jsonStr = JSONObject.toJSONString(o);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            queryIn.filter("inBillCode", String.valueOf(jsonObject.get("billCode")));//订单企业系统票号
            Order order = orderService.get(queryIn);
            if (order == null || order.getDelete()) continue;
            if (StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutChecking() != 3) continue;
//            if (StrUtil.isNotEmpty(checkDvrOrder((Integer) jsonObject.get("checking"), order))) continue;

            updateOperationsIn.set("inChecking", jsonObject.get("checking"));//1-一检，2-二检，3-检票
            updateOperationsIn.set("inGrossWeight", String.valueOf(jsonObject.get("grossWeight")));//毛重
            updateOperationsIn.set("inTareWeight", String.valueOf(jsonObject.get("tareWeight")));//皮重
            if (jsonObject.get("checking").equals(1))
                updateOperationsIn.set("otherInTime", new Date()); //记录收货单位车辆一检（入场）时间

            Order newOrder = orderService.findAndModify(queryIn, updateOperationsIn);

            //修改订单在平台的运输状态
            if (order.getInChecking() < 3 && newOrder.getInChecking() == 3) driverPurpose(order, 2);
        }

        Query<Order> queryOut = orderService.createQuery();
        queryOut.filter("share", 0);
        UpdateOperations<Order> updateOperationsOut = orderService.createUpdateOperations();
        for (Object o : outData) {
            String jsonStr = JSONObject.toJSONString(o);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            queryOut.filter("outBillCode", String.valueOf(jsonObject.get("billCode")));//订单企业系统票号
            Order order = orderService.get(queryOut);
            if (order == null || order.getDelete()) continue;

            updateOperationsOut.set("outChecking", jsonObject.get("checking"));//1-一检，2-二检，3-检票
            updateOperationsOut.set("outGrossWeight", String.valueOf(jsonObject.get("grossWeight")));//毛重
            updateOperationsOut.set("outTareWeight", String.valueOf(jsonObject.get("tareWeight"))); //皮重
            if (jsonObject.get("checking").equals(3))
                updateOperationsOut.set("otherOutTime", new Date());   //记录发货单位车辆检票（出场）时间

            Order newOrder = orderService.findAndModify(queryIn, updateOperationsIn);

            int purpose = -1;
            //修改订单在平台的运输状态
            if (order.getOutChecking() == 0 && newOrder.getOutChecking() == 1) {
                purpose = 0;
            } else if (order.getOutChecking() < 3 && newOrder.getOutChecking() == 3 && StrUtil.isNotEmpty(order.getInUnitCode())) {
                purpose = 1;
            } else if (order.getOutChecking() < 3 && newOrder.getOutChecking() == 3 && StrUtil.isEmpty(order.getInUnitCode())) {
                purpose = 2;
            }
            if (purpose >= 0) driverPurpose(order, purpose);
        }

        return ServerResponse.createSuccess("修改成功", JSONObject.toJSON(id));
    }

    @Override
    public ServerResponse<String> checkingOneOrder(String billCode, Integer bizType, Integer checking, String grossWeight, String tareWeight) {
        if (bizType != 1 && bizType != 0) return ServerResponse.createError("票据类型错误");
        Query<Order> query = orderService.createQuery();
        query.filter("share", 0);
        UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
        if (bizType == 0) {//订单企业系统票号
            query.filter("inBillCode", billCode);
        } else {
            query.filter("outBillCode", billCode);
        }
        Order order = orderService.get(query);
        if (order == null || order.getDelete()) return ServerResponse.createError("订单不存在");

        //TODO: ligx
        if (bizType == 0) {
            String msg = checkDvrOrder(checking, order);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);
            if (StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutChecking() < 3)
                return ServerResponse.createError("修改失败，订单还未出库");
            updateOperations.set("inChecking", checking);//1-一检，2-二检，3-检票，4-在途
            if (StrUtil.isNotEmpty(grossWeight)) updateOperations.set("inGrossWeight", grossWeight);//毛重
            if (StrUtil.isNotEmpty(tareWeight)) updateOperations.set("inTareWeight", tareWeight); //皮重
            if (checking == 1) updateOperations.set("otherInTime", new Date()); //记录收货单位车辆一检（入场）时间
        } else {
            updateOperations.set("outChecking", checking);//1-一检，2-二检，3-检票，4-在途
            if (StrUtil.isNotEmpty(grossWeight)) updateOperations.set("outGrossWeight", grossWeight);//毛重
            if (StrUtil.isNotEmpty(tareWeight)) updateOperations.set("outTareWeight", tareWeight); //皮重
            if (checking == 3) updateOperations.set("otherOutTime", new Date());   //记录发货单位车辆检票（出场）时间
        }

        Order newOrder = orderService.findAndModify(query, updateOperations);
        if (newOrder == null) return ServerResponse.createError("检票状态修改失败", billCode);

        //修改订单在平台的运输状态
        int purpose = -1;
        if (order.getOutChecking() == 0 && newOrder.getOutChecking() == 1) {
            purpose = 0;
        } else if (order.getOutChecking() != 3 && newOrder.getOutChecking() == 3 && StrUtil.isNotEmpty(order.getInUnitCode())) {
            purpose = 1;
        } else if (order.getOutChecking() != 3 && newOrder.getOutChecking() == 3 && StrUtil.isEmpty(order.getInUnitCode())) {
            purpose = 2;
        } else if (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInChecking() < 3 && newOrder.getInChecking() == 3) {
            purpose = 2;
        }
        if (purpose >= 0) driverPurpose(order, purpose);

        return ServerResponse.createSuccess("订单检票状态修改成功！", billCode);
    }

    //将司机已完成订单，inChecking从3改为1时，判断司机没有新接单
    private String checkDvrOrder(Integer checking, Order order) {
        if (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInChecking() == 3 && checking != 3) {
            //司机在完成收货单位检票后，还可能会再次进行一检
            OrderTaking ot = orderTakingService.get("oid", order.getOid());
            String did = ot.getDid();
            Query<OrderTaking> query = orderTakingService.createQuery();
            query.filter("did", did);
            query.filter("finishTag !=", 2);
            query.filter("driverIsAgree !=", 2);
            List<OrderTaking> list = orderTakingService.list(query);
            if (list.size() > 0) return "司机已经接到新的订单，修改失败！";
        }
        return null;
    }

    @Override
    public ServerResponse<String> checkingOneOrder2(String billCode, Integer bizType, Integer checking, String grossWeight, String tareWeight) {
        if (bizType != 1 && bizType != 0) return ServerResponse.createError("票据类型错误");
        Query<Order> query = orderService.createQuery();
        if (bizType == 0) {//订单企业系统票号
            query.filter("inBillCode", billCode);
        } else {
            query.filter("outBillCode", billCode);
        }
        Order order = orderService.get(query);
        if (order == null || order.getDelete()) return ServerResponse.createError("订单不存在");

        Map<String, Object> orderUpMap = new HashMap<>();
        //TODO: ligx
        if (bizType == 0) {
            String msg = checkDvrOrder(checking, order);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);
            if (StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutChecking() < 3)
                return ServerResponse.createError("修改失败，订单还未出库");
            orderUpMap.put("inChecking", checking);//1-一检，2-二检，3-检票，4-在途
            if (StrUtil.isNotEmpty(grossWeight)) orderUpMap.put("inGrossWeight", grossWeight);//毛重
            if (StrUtil.isNotEmpty(tareWeight)) orderUpMap.put("inTareWeight", tareWeight); //皮重
            if (checking == 1) orderUpMap.put("otherInTime", new Date()); //记录收货单位车辆一检（入场）时间
        } else {
            orderUpMap.put("outChecking", checking);//1-一检，2-二检，3-检票，4-在途
            if (StrUtil.isNotEmpty(grossWeight)) orderUpMap.put("outGrossWeight", grossWeight);//毛重
            if (StrUtil.isNotEmpty(tareWeight)) orderUpMap.put("outTareWeight", tareWeight); //皮重
            if (checking == 3) orderUpMap.put("otherOutTime", new Date());   //记录发货单位车辆检票（出场）时间
        }

        //修改订单在平台的运输状态
        Map<String, Object> otUpMap = new HashMap<>();
        if (bizType == 1 && order.getOutChecking() == 0 && checking == 1) {
            otUpMap.put("loadTime", new Date().getTime());
        } else if (bizType == 1 && order.getOutChecking() != 3 && checking == 3 && StrUtil.isEmpty(order.getInUnitCode())) {
            otUpMap.put("finishTag", 2);
        } else if (StrUtil.isNotEmpty(order.getInUnitCode()) && bizType == 0 && order.getInChecking() != 3 && checking == 3) {
            otUpMap.put("finishTag", 2);
        } else if ((bizType == 1 && order.getOutChecking() == 3 && checking != 3) || (bizType == 0 && order.getInChecking() == 3 && checking != 3)) {
            otUpMap.put("finishTag", 0);
        }

        //修改订单状态字段 tranStatus,统计goods毛皮重
        Map<String, Object> gUpMap = new HashMap<>();
        Map<String, Object> driverUpMap = new HashMap<>();
        if (bizType == 1 && order.getOutChecking() == 0 && checking == 1) {
            orderUpMap.put("tranStatus", 2);
            gUpMap.put("orderNum3", 1);
        } else if (bizType == 1 && order.getOutChecking() != 3 && checking == 3 && StrUtil.isNotEmpty(order.getInUnitCode())) {
            orderUpMap.put("tranStatus", 3);
            gUpMap.put("outGrossWeight", StrUtil.isNotEmpty(grossWeight) ? Double.valueOf(grossWeight) : 0);
            gUpMap.put("outTareWeight", StrUtil.isNotEmpty(tareWeight) ? Double.valueOf(tareWeight) : 0);
        } else if (bizType == 0 && order.getInChecking() == 0 && checking == 1) {
            if (order.getMold() == 1) gUpMap.put("orderNum3", 1);
            orderUpMap.put("tranStatus", 4);
        } else if (bizType == 1 && order.getOutChecking() != 3 && checking == 3 && StrUtil.isEmpty(order.getInUnitCode())) {
            orderUpMap.put("tranStatus", 5);

            gUpMap.put("orderNum1", -1);
            gUpMap.put("orderNum2", 1);
            gUpMap.put("orderNum3", -1);

            gUpMap.put("outGrossWeight", StrUtil.isNotEmpty(grossWeight) ? Double.valueOf(grossWeight) : 0);
            gUpMap.put("outTareWeight", StrUtil.isNotEmpty(tareWeight) ? Double.valueOf(tareWeight) : 0);
            driverUpMap.put("haveOrder", 0);
        } else if (bizType == 0 && order.getInChecking() != 3 && checking == 3) {
            orderUpMap.put("tranStatus", 5);

            gUpMap.put("orderNum1", -1);
            gUpMap.put("orderNum2", 1);
            gUpMap.put("orderNum3", -1);

            gUpMap.put("inGrossWeight", StrUtil.isNotEmpty(grossWeight) ? Double.valueOf(grossWeight) : 0);
            gUpMap.put("inTareWeight", StrUtil.isNotEmpty(tareWeight) ? Double.valueOf(tareWeight) : 0);
            driverUpMap.put("haveOrder", 0);
        } else if (bizType == 1 && order.getOutChecking() == 3 && checking != 3) {
            orderUpMap.put("tranStatus", 2);

            gUpMap.put("orderNum1", 1);
            gUpMap.put("orderNum2", -1);
            gUpMap.put("orderNum3", 1);

            gUpMap.put("outGrossWeight", StrUtil.isNotEmpty(order.getOutGrossWeight()) ? -Double.valueOf(order.getOutGrossWeight()) : 0);
            gUpMap.put("outTareWeight", StrUtil.isNotEmpty(order.getOutTareWeight()) ? -Double.valueOf(order.getOutTareWeight()) : 0);
            driverUpMap.put("haveOrder", 1);
        } else if (bizType == 0 && order.getInChecking() == 3 && checking != 3) {
            orderUpMap.put("tranStatus", 4);

            gUpMap.put("orderNum1", 1);
            gUpMap.put("orderNum2", -1);
            gUpMap.put("orderNum3", 1);

            gUpMap.put("inGrossWeight", StrUtil.isNotEmpty(order.getInGrossWeight()) ? -Double.valueOf(order.getInGrossWeight()) : 0);
            gUpMap.put("inTareWeight", StrUtil.isNotEmpty(order.getInTareWeight()) ? -Double.valueOf(order.getInTareWeight()) : 0);
            driverUpMap.put("haveOrder", 1);
        } else if ((bizType == 1 && order.getOutChecking() == 1 && checking == 0) || (StrUtil.isEmpty(order.getOutUnitCode()) && bizType == 0 && order.getInChecking() == 1 && checking == 0)) {
            orderUpMap.put("tranStatus", 1);
        }

        if ((bizType == 1 && order.getOutChecking() == 1 && checking == 0) || (bizType == 0 && order.getMold() == 1 && order.getInChecking() == 1 && checking == 0)) {
            gUpMap.put("orderNum3", -1);
        }

        String msg = upGoodsOrderOtDriver(order, orderUpMap, otUpMap, gUpMap, driverUpMap);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //异步执行推送订单数据
        if (StrUtil.isNotEmpty(order.getMold2()) && order.getMold2() == 1) {    //往货业务才进行推送
            Order newOrder = orderService.getByPK(order.getObjectId());
            if ((newOrder.getMold() == 0 && newOrder.getOutChecking() == 3) || newOrder.getInChecking() == 3)
                mold2OrderHuoDa(newOrder);
        }
        //异步添加GPS，目前仅限于准泰测试单位使用。
        if ((StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutUnitCode().equals("0010011001")) || (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInUnitCode().equals("0010011001")))
            saveCurrentPosition(order, checking, bizType);
        return ServerResponse.createSuccess("订单检票状态修改成功！", billCode);
    }

    @Override
    public ServerResponse<String> checkingOneOrder3(JSONObject data) {
        String billCode = data.getString("billCode");
        if (StrUtil.isEmpty(billCode)) return ServerResponse.createError("参数错误");
        Integer bizType = data.getInteger("bizType");
        Integer checking = data.getInteger("checking");
        String grossWeight = data.getString("grossWeight");
        String tareWeight = data.getString("tareWeight");
        //String tradingUnit = data.getString("tradingUnit");      //交易单位
        String inspector = data.getString("inspector");        //质检员
        String busOrderNo = data.getString("busOrderNo");       //业务单号
        String weight = data.getString("weight");           //入场重量

        // 2025-06-05 checking=3和离场状态综合判断司机是否完成订单
        String isExit = data.getString("isExit");       // 离场状态
        if (StrUtil.isEmpty(isExit)) isExit = "1";
        // if (!"0".equals(isExit) && !"1".equals(isExit)) return ServerResponse.createError("离场状态参数错误！");

        Date time = new Date();

        if (bizType != 1 && bizType != 0) return ServerResponse.createError("票据类型错误");
        Query<Order> query = orderService.createQuery();
        if (bizType == 0) {//订单企业系统票号
            query.filter("inBillCode", billCode);
        } else {
            query.filter("outBillCode", billCode);
        }
        Order order = orderService.get(query);
        if (order == null || order.getDelete()) return ServerResponse.createError("订单不存在");

        Map<String, Object> orderUpMap = new HashMap<>();
        //TODO: ligx
        if (bizType == 0) {
            String msg = checkDvrOrder(checking, order);
            if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);
            if (StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutChecking() < 3)
                return ServerResponse.createError("修改失败，订单还未出库");
            orderUpMap.put("inChecking", checking);//1-一检，2-二检，3-检票，4-在途
            if (StrUtil.isNotEmpty(grossWeight)) orderUpMap.put("inGrossWeight", grossWeight);//毛重
            if (StrUtil.isNotEmpty(grossWeight)) orderUpMap.put("inGrossWeight7", Double.valueOf(grossWeight));//毛重
            if (StrUtil.isNotEmpty(tareWeight)) orderUpMap.put("inTareWeight", tareWeight); //皮重
            if (StrUtil.isNotEmpty(tareWeight)) orderUpMap.put("inTareWeight7", Double.valueOf(tareWeight)); //皮重
            if (checking == 1) orderUpMap.put("otherInTime", time); //记录收货单位车辆一检（入场）时间

            //if (StrUtil.isNotEmpty(tradingUnit)) orderUpMap.put("outTradingUnit", tradingUnit);
            if (StrUtil.isNotEmpty(inspector)) orderUpMap.put("outInspector", inspector);
            if (StrUtil.isNotEmpty(busOrderNo)) orderUpMap.put("outBusOrderNo", busOrderNo);
            if (StrUtil.isNotEmpty(weight)) orderUpMap.put("outWeight", weight);
        } else {
            // checking从3改为1时，判断订单是否有收货单位且inchecking必须为0，否则修改失败
            if (order.getOutChecking() == 3 && checking != 3 && StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInChecking() > 0)
                return ServerResponse.createError("订单已入场检票，修改失败");

            orderUpMap.put("outChecking", checking);//1-一检，2-二检，3-检票，4-在途
            if (StrUtil.isNotEmpty(grossWeight)) orderUpMap.put("outGrossWeight", grossWeight);//毛重
            if (StrUtil.isNotEmpty(grossWeight)) orderUpMap.put("outGrossWeight7", Double.valueOf(grossWeight));//毛重
            if (StrUtil.isNotEmpty(tareWeight)) orderUpMap.put("outTareWeight", tareWeight); //皮重
            if (StrUtil.isNotEmpty(tareWeight)) orderUpMap.put("outTareWeight7", Double.valueOf(tareWeight)); //皮重
            if (checking == 3) orderUpMap.put("otherOutTime", time);   //记录发货单位车辆检票（出场）时间

            //if (StrUtil.isNotEmpty(tradingUnit)) orderUpMap.put("inTradingUnit", tradingUnit);
            if (StrUtil.isNotEmpty(inspector)) orderUpMap.put("inInspector", inspector);
            if (StrUtil.isNotEmpty(busOrderNo)) orderUpMap.put("inBusOrderNo", busOrderNo);
            if (StrUtil.isNotEmpty(weight)) orderUpMap.put("inWeight", weight);
        }

        //修改订单在平台的运输状态
        Map<String, Object> otUpMap = new HashMap<>();
        if (bizType == 1 && order.getOutChecking() == 0 && checking == 1) {
            otUpMap.put("loadTime", new Date().getTime());
        } else if (bizType == 1 && order.getOutChecking() != 3 && checking == 3 && StrUtil.isEmpty(order.getInUnitCode())) {
            otUpMap.put("finishTag", 2);
        } else if (StrUtil.isNotEmpty(order.getInUnitCode()) && bizType == 0 && order.getInChecking() != 3 && checking == 3) {
            otUpMap.put("finishTag", 2);
        } else if ((bizType == 1 && order.getOutChecking() == 3 && checking != 3) || (bizType == 0 && order.getInChecking() == 3 && checking != 3)) {
            otUpMap.put("finishTag", 0);
        }

        //修改订单状态字段 tranStatus,统计goods毛皮重
        Map<String, Object> gUpMap = new HashMap<>();
        Map<String, Object> driverUpMap = new HashMap<>();
        if (bizType == 1 && order.getOutChecking() == 0 && checking == 1) {
            orderUpMap.put("tranStatus", 2);
            gUpMap.put("orderNum3", 1);
            orderUpMap.put("time2", time);  //发货入场时间
        } else if (bizType == 1 && order.getOutChecking() != 3 && checking == 3 && StrUtil.isNotEmpty(order.getInUnitCode())) {
            orderUpMap.put("tranStatus", 3);
            gUpMap.put("outGrossWeight", StrUtil.isNotEmpty(grossWeight) ? Double.valueOf(grossWeight) : 0);
            gUpMap.put("outTareWeight", StrUtil.isNotEmpty(tareWeight) ? Double.valueOf(tareWeight) : 0);
            orderUpMap.put("time3", time);  //发货出场时间

            orderUpMap.put("outIsExit", isExit);
        } else if (bizType == 0 && order.getInChecking() == 0 && checking == 1) {
            if (order.getMold() == 1) gUpMap.put("orderNum3", 1);
            orderUpMap.put("tranStatus", 4);
            orderUpMap.put("time4", time);  //收货入场时间
        } else if (bizType == 1 && order.getOutChecking() != 3 && checking == 3 && StrUtil.isEmpty(order.getInUnitCode())) {
            orderUpMap.put("tranStatus", 5);

            gUpMap.put("orderNum1", -1);
            gUpMap.put("orderNum2", 1);
            gUpMap.put("orderNum3", -1);

            gUpMap.put("outGrossWeight", StrUtil.isNotEmpty(grossWeight) ? Double.valueOf(grossWeight) : 0);
            gUpMap.put("outTareWeight", StrUtil.isNotEmpty(tareWeight) ? Double.valueOf(tareWeight) : 0);
            driverUpMap.put("haveOrder", 0);
            orderUpMap.put("time3", time);  //发货出场时间

            orderUpMap.put("outIsExit", isExit);
        } else if (bizType == 0 && order.getInChecking() != 3 && checking == 3) {
            orderUpMap.put("tranStatus", 5);

            gUpMap.put("orderNum1", -1);
            gUpMap.put("orderNum2", 1);
            gUpMap.put("orderNum3", -1);

            gUpMap.put("inGrossWeight", StrUtil.isNotEmpty(grossWeight) ? Double.valueOf(grossWeight) : 0);
            gUpMap.put("inTareWeight", StrUtil.isNotEmpty(tareWeight) ? Double.valueOf(tareWeight) : 0);
            driverUpMap.put("haveOrder", 0);
            orderUpMap.put("time5", time);  //收货出场时间

            orderUpMap.put("inIsExit", isExit);
        } else if (bizType == 1 && order.getOutChecking() == 3 && checking != 3) {
            orderUpMap.put("tranStatus", 2);

            gUpMap.put("orderNum1", 1);
            gUpMap.put("orderNum2", -1);
            gUpMap.put("orderNum3", 1);

            gUpMap.put("outGrossWeight", StrUtil.isNotEmpty(order.getOutGrossWeight()) ? -Double.valueOf(order.getOutGrossWeight()) : 0);
            gUpMap.put("outTareWeight", StrUtil.isNotEmpty(order.getOutTareWeight()) ? -Double.valueOf(order.getOutTareWeight()) : 0);
            driverUpMap.put("haveOrder", 1);

            orderUpMap.put("outIsExit", "0");
        } else if (bizType == 0 && order.getInChecking() == 3 && checking != 3) {
            orderUpMap.put("tranStatus", 4);

            gUpMap.put("orderNum1", 1);
            gUpMap.put("orderNum2", -1);
            gUpMap.put("orderNum3", 1);

            gUpMap.put("inGrossWeight", StrUtil.isNotEmpty(order.getInGrossWeight()) ? -Double.valueOf(order.getInGrossWeight()) : 0);
            gUpMap.put("inTareWeight", StrUtil.isNotEmpty(order.getInTareWeight()) ? -Double.valueOf(order.getInTareWeight()) : 0);
            driverUpMap.put("haveOrder", 1);

            orderUpMap.put("inIsExit", "0");
        } else if ((bizType == 1 && order.getOutChecking() == 1 && checking == 0) || (StrUtil.isEmpty(order.getOutUnitCode()) && bizType == 0 && order.getInChecking() == 1 && checking == 0)) {
            orderUpMap.put("tranStatus", 1);
        }

        if ((bizType == 1 && order.getOutChecking() == 1 && checking == 0) || (bizType == 0 && order.getMold() == 1 && order.getInChecking() == 1 && checking == 0)) {
            gUpMap.put("orderNum3", -1);
        }

        String msg = upGoodsOrderOtDriver(order, orderUpMap, otUpMap, gUpMap, driverUpMap);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        //异步执行推送订单数据
        if (StrUtil.isNotEmpty(order.getMold2()) && order.getMold2() == 1) {    //往货业务才进行推送
            Order newOrder = orderService.getByPK(order.getObjectId());
            if ((newOrder.getMold() == 0 && newOrder.getOutChecking() == 3) || newOrder.getInChecking() == 3)
                mold2OrderHuoDa(newOrder);
        }
        //异步添加GPS，目前仅限于准泰测试单位使用。
        if ((StrUtil.isNotEmpty(order.getOutUnitCode()) && order.getOutUnitCode().equals("0010011001")) || (StrUtil.isNotEmpty(order.getInUnitCode()) && order.getInUnitCode().equals("0010011001")))
            saveCurrentPosition(order, checking, bizType);
        // 检票状态为3时，异步发送短信给司机(有收费的司机提供)
//        if (checking == 3){
//            DriverInfo driverInfo = driverInfoService.getByPK(order.getDid());
//            goodsService.sendSms_HuaWei(order,driverInfo, bizType+1);
//        }
        // 发货方检票时，调用智慧能源状态变更接口
        if (bizType == 1) orderService.coalDeliveryNoteStatusUpdate(order, checking);
        return ServerResponse.createSuccess("订单检票状态修改成功！", billCode);
    }

    @Transactional
    public String upGoodsOrderOtDriver(Order order, Map<String, Object> orderUpMap, Map<String, Object> otUpMap, Map<String, Object> gUpMap, Map<String, Object> driverUpMap) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();

//        OrderTaking taking = orderTakingService.get("oid", order.getOid());
        try {
            clientSession.startTransaction();

            if (ObjectUtil.isNotEmpty(orderUpMap)) {
                Map<String, Object> upMap = new HashMap<>();
                upMap.put("$set", orderUpMap);
                orderCollection.updateOne(clientSession, new Document("oid", order.getOid()), Document.parse(JSONObject.toJSONString(upMap)));
            }

            if (ObjectUtil.isNotEmpty(otUpMap)) {
                Map<String, Object> upMap = new HashMap<>();
                upMap.put("$set", otUpMap);
                otCollection.updateOne(clientSession, new Document("oid", order.getOid()), Document.parse(JSONObject.toJSONString(upMap)));
            }

            if (ObjectUtil.isNotEmpty(gUpMap)) {
                Map<String, Object> upMap = new HashMap<>();
                upMap.put("$inc", gUpMap);
                gCollection.updateOne(clientSession, new Document("gid", order.getGid()), Document.parse(JSONObject.toJSONString(upMap)));
            }

            if (ObjectUtil.isNotEmpty(driverUpMap)) {
                //修改司机是否有运输中订单字段haveOrder
                Map<String, Object> upMap = new HashMap<>();
                upMap.put("$set", driverUpMap);
//                driverCollection.updateMany(clientSession, new Document("_id", new ObjectId(taking.getDid())), Document.parse(JSONObject.toJSONString(upMap)));
                driverCollection.updateMany(clientSession, new Document("_id", new ObjectId(order.getDid())), Document.parse(JSONObject.toJSONString(upMap)));
            }
            clientSession.commitTransaction();
            //goods更新数据放到事务外面
            /*Query<Goods> query = goodsService.createQuery();
            query.criteria("gid").equal(taking.getGid());
            UpdateOperations<Goods> updateOperations = goodsService.createUpdateOperations();
            if (StrUtil.isNotEmpty(gUpMap.get("orderNum1")))
                updateOperations.inc("orderNum1", Integer.valueOf(gUpMap.get("orderNum1").toString()));
            if (StrUtil.isNotEmpty(gUpMap.get("orderNum2")))
                updateOperations.inc("orderNum2", Integer.valueOf(gUpMap.get("orderNum2").toString()));
            if (StrUtil.isNotEmpty(gUpMap.get("orderNum3")))
                updateOperations.inc("orderNum3", Integer.valueOf(gUpMap.get("orderNum3").toString()));
            if (StrUtil.isNotEmpty(gUpMap.get("outGrossWeight")))
                updateOperations.inc("outGrossWeight", Double.valueOf(gUpMap.get("outGrossWeight").toString()));
            if (StrUtil.isNotEmpty(gUpMap.get("outTareWeight")))
                updateOperations.inc("outTareWeight", Double.valueOf(gUpMap.get("outTareWeight").toString()));
            if (StrUtil.isNotEmpty(gUpMap.get("inGrossWeight")))
                updateOperations.inc("inGrossWeight", Double.valueOf(gUpMap.get("inGrossWeight").toString()));
            if (StrUtil.isNotEmpty(gUpMap.get("inTareWeight")))
                updateOperations.inc("inTareWeight", Double.valueOf(gUpMap.get("inTareWeight").toString()));
            goodsService.update(query, updateOperations);*/

        } catch (Exception e) {
            clientSession.abortTransaction();
            loggerError(e, "upGoodsOrderOtDriver");
            return e.getMessage();
        } finally {
            clientSession.close();
        }
        return null;
    }


    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public void loggerError(Exception e, String name) {
        for (int i = 0; i < e.getStackTrace().length; i++) {
            StackTraceElement element = e.getStackTrace()[i];
            // if ("ReceiveCarMsgServiceImpl.java".equals(element.getFileName()) && name.equals(element.getMethodName()))
            logger.error("fileName:" + element.getFileName() + ",lineNumber:" + element.getLineNumber() + ",methodName:" + element.getMethodName());
        }
        logger.error(e.getMessage());
    }

    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public void mold2OrderHuoDa(Order order) {

        try {
            //1.当前订单检票为3时，推送订单数据到货达
            CusHDDKS cusHDDKS = cusHDDKSService.get("cid", order.getCid());
            String appKey = cusHDDKS.getAppKey();
            String appSecret = cusHDDKS.getAppSecret();

            ShipmentOrder shipmentOrder = createShipmentOrder(order);
            Map<String, Object> map = pushShipmentOrder(shipmentOrder.getParamMap(), appKey, appSecret);
            if (StrUtil.isEmpty(map.get("errno")) || !map.get("errno").equals("0")) {//推送返回结果失败，保存数据到shipmentOrder，下次再推送
                if (StrUtil.isNotEmpty(map.get("errmsg"))) {
                    shipmentOrder.setErrmsg(map.get("errmsg").toString());
                    shipmentOrder.setIsPush("1");
                    shipmentOrder.setOid(order.getOid());
                }
                shipmentOrderService.save(shipmentOrder);
            }

            //2.查询shipmentOrder表，是否有未完成推送的订单，再次推送
            Query<ShipmentOrder> query = shipmentOrderService.createQuery();
            query.criteria("isPush").notEqual("1");
            List<ShipmentOrder> list = shipmentOrderService.list(query);
            for (ShipmentOrder shipmentOrder1 : list) {
                Order order1 = orderService.get("oid", shipmentOrder1.getShipmentPinId());

                CusHDDKS cusHDDKS1 = cusHDDKSService.get("cid", order1.getCid());
                String appKey1 = cusHDDKS1.getAppKey();
                String appSecret1 = cusHDDKS1.getAppSecret();

                ShipmentOrder so = createShipmentOrder(order1);
                Map<String, Object> map1 = pushShipmentOrder(so.getParamMap(), appKey1, appSecret1);
                if (StrUtil.isEmpty(map1.get("errno")) || !map1.get("errno").equals("0")) {//推送返回结果失败，保存数据到shipmentOrder，下次再推送
                    if (StrUtil.isNotEmpty(map1.get("errmsg"))) {
                        Query<ShipmentOrder> upQuery = shipmentOrderService.createQuery();
                        upQuery.criteria("_id").equal(shipmentOrder1.getObjectId());
                        UpdateOperations<ShipmentOrder> options = shipmentOrderService.createUpdateOperations();
                        options.set("errmsg", map1.get("errmsg"));
                        options.set("isPush", 1);
                        shipmentOrderService.update(upQuery, options);
                    }
                } else {
                    shipmentOrderService.delete(shipmentOrder1.getObjectId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * checking =1或3 时，记录车辆GPS坐标
     */
    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public void saveCurrentPosition(Order order, Integer check, Integer bizType) {
        if (check == 1) {
            if (bizType == 0) {
                orderPositionService.addPositionWhereChecking1(order, 1);
            } else if (bizType == 1) {
                orderPositionService.addPositionWhereChecking1(order, 0);
            }
        } else if (check == 3) {
            if (bizType == 0) {
                orderPositionService.addPositionWhereChecking3(order, 1);
            } else if (bizType == 1) {
                orderPositionService.addPositionWhereChecking3(order, 0);
            }
        }

    }

    public ShipmentOrder createShipmentOrder(Order order) {
        DriverInfo driverInfo = driverInfoService.getByPK(order.getDid());
        ShipmentOrder shipmentOrder = new ShipmentOrder(order.getBeginDistrictCode(), order.getBeginPoint(), order.getEndDistrictCode(), order.getEndPoint(), order.getCarNum(), driverInfo.getMobile(), driverInfo.getName(), driverInfo.getIdentity());
        if (order.getMold() == 1 || order.getMold() == 2) {
            shipmentOrder.setBizType(20);
            shipmentOrder.setOfflineDispatchType("10");
            shipmentOrder.setDeliveryId(order.getInBillCode());
            shipmentOrder.setSelfCode(order.getInBillCode());
            shipmentOrder.setType("1");
            shipmentOrder.setGoodsDetailName(order.getInVariety());
            shipmentOrder.setUnloadNetWeight(String.valueOf(Double.valueOf(order.getInGrossWeight()) - Double.valueOf(order.getInTareWeight())));
            if (order.getMold() == 2) {
                shipmentOrder.setLoadNetWeight(String.valueOf(Double.valueOf(order.getOutGrossWeight()) - Double.valueOf(order.getOutTareWeight())));
            }
        } else {
            shipmentOrder.setBizType(30);
            shipmentOrder.setOfflineDispatchType("20");
            shipmentOrder.setDeliveryId(order.getOutBillCode());
            shipmentOrder.setSelfCode(order.getOutBillCode());
            shipmentOrder.setType("2");
            shipmentOrder.setGoodsDetailName(order.getOutVariety());
            shipmentOrder.setLoadNetWeight(String.valueOf(Double.valueOf(order.getOutGrossWeight()) - Double.valueOf(order.getOutTareWeight())));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StrUtil.isNotEmpty(order.getTime3())) shipmentOrder.setLoadGrossTime(sdf.format(order.getTime3()));
        if (StrUtil.isNotEmpty(order.getTime5())) shipmentOrder.setUnloadTareTime(sdf.format(order.getTime5()));
        return shipmentOrder;
    }

    public Map<String, Object> pushShipmentOrder(Map<String, Object> paramMap, String appKey, String appSecret) {
        // appUrl/appKey/appSecret 由链接平台提供
//        String appUrl = String.format("%s/%s", "http://beta-laas.yunxiaobao.com", "api/hlink-tss/shipmentSync/addRecord");    //测试环境
        String appUrl = String.format("%s/%s", "https://laas.yunxiaobao.com", "api/hlink-tss/shipmentSync/addRecord");          //生产环境
        /*String appKey = "qlt4vQ_90UKX5czGGhWFcA";
        String appSecret = "rUrp_el148I";*/

        /*
        *   app_key:NjT0u6Y38x9_1Q0moiTSjw
            app_secret:hSwgGQLaRZI
            url:http://beta-laas.yunxiaobao.com
        * */
        Map<String, Object> map = new HashMap<>();
        try {
            // Asserts.notEmpty(paramMap, "链接平台回写运单状态参数不能为空！");
            // 兼容参数
            // paramMap.put("extInfo", ParamBuilder.create().append("dataSource", "OFFLINE").append("requestSource", 3).build());
            // log.debug("当前推送明文参数 - {}", paramMap);
            // 发送请求
            String bodyStr = HddEncryptRequest.doRequest(appUrl, appKey, appSecret, paramMap);
            // 反序列化
            // errno	返回码，0：成功，其他说明失败
            // errmsg	返回信息
            JSONObject jsonObject = JSONObject.parseObject(bodyStr);

            String errno = jsonObject.getString("errno");
            map.put("errno", errno);
            String errmsg = jsonObject.getString("errmsg");
            map.put("errmsg", errmsg);
            if (errno.equals("0")) {
                JSONObject res = jsonObject.getJSONObject("res");
                String shipmentPinId = res.getString("shipmentPinId");
                Integer bizExist = res.getInteger("bizExist");
                map.put("shipmentPinId", shipmentPinId);
                map.put("bizExist", bizExist);
            }

            //VenusStringResp resp = JsonMapper.getInstance().fromJson(res, VenusStringResp.class);
            //return resp;
        } catch (Exception e) {
            //log.error("链接平台更新运单状态出错 - {}", ExceptionUtil.stacktraceToString(e));
            //throw new JointErrFControlException("链接平台更新运单状态出错", e);
            logger.warn("pushShipmentOrder -- " + e);
        }
        return map;
    }

    //根据车牌号推送消息给司机
    @Override
    public ServerResponse<String> pushByCarNum(String list) {
        List<Map> listStr = JSON.parseArray(list, Map.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        String startTime = sdf.format(now);
        for (Map<String, Object> map : listStr) {
            String carNum = String.valueOf(map.get("carNum"));
            String msg = String.valueOf(map.get("msg"));
            String billCode = String.valueOf(map.get("billCode"));
            Integer expireTime = (Integer) map.get("expireTime");

            Query<Order> query = orderService.createQuery();
            char a = billCode.charAt(10);
            if (a == '6') {
                query.criteria("outBillCode").equal(billCode);
            } else if (a == '5') {
                query.criteria("inBillCode").equal(billCode);
            }
            /*query.or(
                    query.criteria("outBillCode").equal(billCode),
                    query.criteria("inBillCode").equal(billCode)
            );*/
            query.criteria("delete").equal(false);
            Order order = orderService.get(query);
            if (order == null) continue;
            /*OrderTaking ot = orderTakingService.get("oid", order.getOid());
            if (ot == null) continue;*/

            String endTime = sdf.format(now.getTime() + expireTime * 60 * 1000);
            UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
            updateOperations.set("callStartTime", startTime);
            updateOperations.set("callEndTime", endTime);
            orderService.update(query, updateOperations);

            String subName = billCode.equals(order.getOutBillCode()) ? order.getOutSubName() : order.getInSubName();
//            DriverInfo driverInfo = driverInfoService.getByPK(ot.getDid());
            DriverInfo driverInfo = driverInfoService.getByPK(order.getDid());
            String thing5 = expireTime > 0 ? order.getCarNum() + "入场限" + expireTime + "分钟" : msg;
            //Map<String, String> resMap = pushInfoService.weChatSend4081(driverInfo.getPhoneId(), subName, startTime, msg);// + "(注：" + endTime + "将过期)");
            //Map<String, String> resMap = pushInfoService.weChatSend36090(driverInfo.getPhoneId(), subName, startTime, thing5);// + "(注：" + endTime + "将过期)");
            //pushInfoService.addPushInfo(subName + "叫号", msg, ot.getDid(), 8, resMap.get("errCode"), resMap.get("errMsg"), resMap.get("msGid"));
            pushInfoService.weChatSend36090(driverInfo.getPhoneId(), subName, startTime, thing5, msg, order.getDid());

            // 发送华为语音通知
            String subUnitCode;
            if (a == '6') {
                subUnitCode = order.getOutDefaultDownUnit();
            } else {
                subUnitCode = order.getInDefaultDownUnit();
            }
            SysUnit sysUnit = sysUnitService.get("code", subUnitCode);
            if (sysUnit.getIsCall() != null && sysUnit.getIsCall() == 1)
                goodsService.sendCall_HuaWei(order.getOid(), carNum, subName, driverInfo);
        }
        return ServerResponse.createSuccess();
    }

    //根据车牌号推送消息给司机
    @Override
    public ServerResponse<String> pushByBillCode(String list) {
        List<Map> listStr = JSON.parseArray(list, Map.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        String startTime = sdf.format(now);
        for (Map<String, Object> map : listStr) {
            String billCode = String.valueOf(map.get("billCode"));
            Integer expireTime = (Integer) map.get("expireTime");

            char a = billCode.charAt(10);
            Query<Order> query = orderService.createQuery();
            if (a == '6') {
                query.criteria("outBillCode").equal(billCode);
            } else if (a == '5') {
                query.criteria("inBillCode").equal(billCode);
            }
            query.criteria("delete").equal(false);
            Order order = orderService.get(query);
            if (order == null) continue;

            String endTime = sdf.format(now.getTime() + expireTime * 60 * 1000);
            UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
            updateOperations.set("callStartTime", startTime);
            updateOperations.set("callEndTime", endTime);
            orderService.update(query, updateOperations);

            DriverInfo driverInfo = driverInfoService.getByPK(order.getDid());
            String vriety = billCode.equals(order.getOutBillCode()) ? order.getOutVariety() : order.getInVariety();
            String thing6 = "请到" + vriety + "区装卸";
            pushInfoService.weChatSend14370(driverInfo.getPhoneId(), order.getOid(), order.getCarNum(), endTime, thing6, order.getDid());
        }
        return ServerResponse.createSuccess();
    }

    /**
     * 企业修改司机订单检票状态后,平台对订单运输状态进行相应修改，进行平台相应的推送等业务。
     * purpose:
     * 0- 有发货单位，发货单位第一次进行 一检 状态的修改，此时给订单添加装货时间
     * 1- 有发货单位，发货单位第一次进行 检票 状态的修改，此时也有收货单位，订单运输状态改为运输中
     * 2- 有收货单位，收货单位第一次进行 检票 状态的修改，此时订单运输状态改为运输完成
     * ---有发货单位，发货单位第一次进行 检票 状态的修改，此时没有收货单位，订单运输状态改为运输完成
     * 3- 检票完成改为一检，则orderTaking的finishTag改为0
     */
    private void driverPurpose(Order order, Integer purpose) {
        OrderTaking orderTaking = orderTakingService.get("oid", order.getOid());

        Query<OrderTaking> query = orderTakingService.createQuery();
        query.filter("oid", order.getOid());
        query.filter("updateTime", orderTaking.getUpdateTime());

        UpdateOperations<OrderTaking> updateOperations = orderTakingService.createUpdateOperations();
        switch (purpose) {
            case 0: //订单添加装货日期
                updateOperations.set("loadTime", new Date().getTime());
                //司机到发货单位开始拉货时，平台添加该车订单的物流信息（orderLogistics）
                //addOrderLogistics(order);
                break;
            case 1: //修改订单状态为运输中
                //updateOperations.set("finishTag", 1);
                break;
            case 2:
                updateOperations.set("finishTag", 2);
                break;
            case 3:
                updateOperations.set("finishTag", 0);
                break;
        }
        orderTakingService.update(query, updateOperations);
    }

    @Override
    public ServerResponse<String> cancelAppointmentOrCheckin(String billCode, int type) {
        Query<Order> query = orderService.createQuery();
        query.or(
                query.criteria("outBillCode").equal(billCode),
                query.criteria("inBillCode").equal(billCode)
        );
        Order order = orderService.get(query);
        if (order == null) return ServerResponse.createError("订单不存在");

        OrderTaking ot = orderTakingService.get("oid", order.getOid());
        if (ot == null) return ServerResponse.createError("订单已撤销");

        Query<OrderTaking> otQuery = orderTakingService.createQuery();
        otQuery.filter("oid", order.getOid());
        UpdateOperations<OrderTaking> otUpdate = orderTakingService.createUpdateOperations();
        if (type == 0) {    //取消订单预约
            otUpdate.set("sa", ot.getSa() - 1 <= 0 ? 0 : ot.getSa() - 1);
            otUpdate.set("aStartTime", 0);
            otUpdate.set("aEndTime", 0);
        }                   //取消订单签到
        //删除签到记录
        String defaultDownUnit;
        if (billCode.equals(order.getOutBillCode())) {
            defaultDownUnit = order.getOutDefaultDownUnit();
        } else {
            defaultDownUnit = order.getInDefaultDownUnit();
        }
        Query<DriverSignIn> dsiQuery = driverSignInService.createQuery();
        dsiQuery.filter("oid", order.getOid());
        dsiQuery.filter("defaultDownUnit", defaultDownUnit);
        driverSignInService.delete(dsiQuery);
        otUpdate.set("isPunchClock", ot.getIsPunchClock() - 1 <= 0 ? 0 : ot.getIsPunchClock() - 1);
        orderTakingService.update(otQuery, otUpdate);

        return ServerResponse.createSuccess("取消成功");
    }

    @Override
    public ServerResponse<List<QuarantineInfo>> searchQuarantineInfosBySubCode(String subCode, Integer bizType, Integer page, Integer rows) {
        Query<QuarantineInfo> query = quarantineInfoService.createQuery();
        query.filter("defaultDownUnit", subCode);
        query.order(Sort.descending("createTime"));

        Query<QuarantineInfo> oQuery = quarantineInfoService.createQuery();
        if (bizType == 0) {
            /*oQuery.or(
                    oQuery.and(
                            oQuery.or(
                                    oQuery.criteria("order.outDefaultDownUnit").equal(null),
                                    oQuery.criteria("order.outDefaultDownUnit").equal("")
                            ),
                            oQuery.criteria("order.inDefaultDownUnit").equal(subCode),
                            oQuery.criteria("order.inChecking").lessThanOrEq(0)
                    ),
                    oQuery.and(
                            oQuery.or(
                                    oQuery.criteria("order.outDefaultDownUnit").notEqual(null),
                                    oQuery.criteria("order.outDefaultDownUnit").notEqual("")
                            ),
                            oQuery.criteria("order.outChecking").greaterThan(0),
                            oQuery.criteria("order.inDefaultDownUnit").equal(subCode),
                            oQuery.criteria("order.inChecking").lessThanOrEq(0)
                    )
            );*/
            oQuery.criteria("orders.inDefaultDownUnit").equal(subCode);
            oQuery.criteria("orders.inChecking").lessThanOrEq(0);
        } else if (bizType == 1) {
            oQuery.criteria("orders.outDefaultDownUnit").equal(subCode);
            oQuery.criteria("orders.outChecking").lessThanOrEq(0);
        }

        AggregationPipeline pipeline = quarantineInfoService.createAggregation();
        pipeline.match(query);
        pipeline.lookup("t_order", "oid", "oid", "orders")
                .match(oQuery);
        pipeline.skip((page - 1) * rows);
        pipeline.limit(rows);
        Iterator<QuarantineInfo> infos = pipeline.aggregate(QuarantineInfo.class);

        List<QuarantineInfo> list = new ArrayList<>();
        while (infos.hasNext()) {
            QuarantineInfo qua = infos.next();
            if (StrUtil.isNotEmpty(qua.getHealthCodePho()))
                qua.setHealthCodePho(photoFileService.signUrl(qua.getHealthCodePho()));
            if (StrUtil.isNotEmpty(qua.getTravelCardPho()))
                qua.setTravelCardPho(photoFileService.signUrl(qua.getTravelCardPho()));
            if (StrUtil.isNotEmpty(qua.getTemperaturePro()))
                qua.setTemperaturePro(photoFileService.signUrl(qua.getTemperaturePro()));
            if (StrUtil.isNotEmpty(qua.getNucleicAcidPho()))
                qua.setNucleicAcidPho(photoFileService.signUrl(qua.getNucleicAcidPho()));
            if (StrUtil.isNotEmpty(qua.getVaccinationPho()))
                qua.setVaccinationPho(photoFileService.signUrl(qua.getVaccinationPho()));

            list.add(qua);
        }

        return ServerResponse.createSuccess(list);
    }

    public ServerResponse<EGridResult> searchQuarantineInfos2BySubCode(String unitCode, String subCode, Integer bizType, Integer page, Integer rows, String mobile, String date, Integer quaType) {
        Query<QuarantineInfo> query = quarantineInfoService.createQuery();

        if (StrUtil.isNotEmpty(subCode) && !subCode.equals("null")) {
            query.filter("defaultDownUnit", subCode);
        } else {
            Query<SysUnit> sysQuery = sysUnitService.createQuery();
            sysQuery.criteria("pCode").equal(unitCode);
            List<SysUnit> subUnits = sysUnitService.list(sysQuery);
            List<String> subCodes = new ArrayList<>();
            for (SysUnit unit : subUnits) {
                subCodes.add(unit.getCode());
            }
            query.criteria("defaultDownUnit").in(subCodes);
        }
        if (StrUtil.isNotEmpty(mobile)) {
            DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
            query.criteria("did").equal(driverInfo.getObjectId().toHexString());
        }
        if (StrUtil.isNotEmpty(date)) {
            query.and(
                    query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours(date, 0)),
                    query.criteria("createTime").lessThan(IdUnit.weeHours(date, 1))
            );
        }
        if (StrUtil.isNotEmpty(quaType)) {
            if (quaType == 0) {
                query.or(
                        query.criteria("audit").equal(0),
                        query.criteria("audit").equal(""),
                        query.criteria("audit").equal(null)
                );
            } else {
                query.criteria("audit").equal(quaType);
            }
        }
        query.order(Sort.descending("createTime"));

        Query<QuarantineInfo> oQuery = quarantineInfoService.createQuery();
        if (bizType == 0) {
            if (StrUtil.isNotEmpty(subCode) && !subCode.equals("null")) {
                oQuery.criteria("orders.inDefaultDownUnit").equal(subCode);
            } else {
                oQuery.criteria("orders.inUnitCode").equal(unitCode);
            }
//            oQuery.criteria("orders.inChecking").lessThanOrEq(0);
        } else if (bizType == 1) {
            if (StrUtil.isNotEmpty(subCode) && !subCode.equals("null")) {
                oQuery.criteria("orders.outDefaultDownUnit").equal(subCode);
            } else {
                oQuery.criteria("orders.outUnitCode").equal(unitCode);
            }
//            oQuery.criteria("orders.outChecking").lessThanOrEq(0);
        }

        AggregationPipeline pipeline = quarantineInfoService.createAggregation();
        pipeline.match(query);
        pipeline.lookup("t_order", "oid", "oid", "orders")
                .match(oQuery);

        Iterator<QuarantineInfo> infos2 = pipeline.aggregate(QuarantineInfo.class);

        /*pipeline.skip((page - 1) * rows);
        pipeline.limit(rows);
        Iterator<QuarantineInfo> infos = pipeline.aggregate(QuarantineInfo.class);*/

        int total = 0;
        int first = 0;
        List<QuarantineInfo> list = new ArrayList<>();
        while (infos2.hasNext()) {
            QuarantineInfo qua = infos2.next();
            if (first >= (page - 1) * rows && first < page * rows) {
                if (StrUtil.isNotEmpty(qua.getHealthCodePho()))
                    qua.setHealthCodePho(photoFileService.signUrl(qua.getHealthCodePho()));
                if (StrUtil.isNotEmpty(qua.getTravelCardPho()))
                    qua.setTravelCardPho(photoFileService.signUrl(qua.getTravelCardPho()));
                if (StrUtil.isNotEmpty(qua.getTemperaturePro()))
                    qua.setTemperaturePro(photoFileService.signUrl(qua.getTemperaturePro()));
                if (StrUtil.isNotEmpty(qua.getNucleicAcidPho()))
                    qua.setNucleicAcidPho(photoFileService.signUrl(qua.getNucleicAcidPho()));
                if (StrUtil.isNotEmpty(qua.getVaccinationPho()))
                    qua.setVaccinationPho(photoFileService.signUrl(qua.getVaccinationPho()));

                list.add(qua);
            }
            total++;
            first++;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("rows", list);
        result.put("total", total);
        return ServerResponse.createSuccess(new EGridResult(total, list));
    }

    @Override
    public ServerResponse<String> auditQuarantine(String id, Integer audit, String auditDes) {
        Query<QuarantineInfo> quaQuery = quarantineInfoService.createQuery();
        quaQuery.filter("id", id);
        QuarantineInfo quarantineInfo = quarantineInfoService.get(quaQuery);
        DriverInfo driverInfo = quarantineInfo.getDriverInfo();
        UpdateOperations<QuarantineInfo> quaUpdate = quarantineInfoService.createUpdateOperations();
        quaUpdate.set("audit", audit);
        quaUpdate.set("auditDes", auditDes);
        quarantineInfoService.update(quaQuery, quaUpdate);

        //推送审核结果给微信司机用户
        if (StrUtil.isNotEmpty(auditDes)) {
            Map<String, String> map = pushInfoService.weChatSendDvr(driverInfo.getPhoneId(), driverInfo.getMobile(), driverInfo.getName(), "防疫申报审核", quarantineInfo.getDefaultDownUnit() + ":" + auditDes);
            pushInfoService.addPushInfo("企业疫情防控", "司机订单：" + quarantineInfo.getOid() + "防疫申报审核结果(" + quarantineInfo.getDefaultDownUnit() + ")" + auditDes, quarantineInfo.getDid(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));
        }

        return ServerResponse.createSuccess("修改审核结果成功！");
    }

    @Override
    public ServerResponse<String> auditQuarantine2(String id, Integer audit, String auditDes) {
        Query<QuarantineInfo> quaQuery = quarantineInfoService.createQuery();
        quaQuery.filter("id", id);
        QuarantineInfo quarantineInfo = quarantineInfoService.get(quaQuery);
        if (quarantineInfo == null) return ServerResponse.createError("防疫信息不存在");
        DriverInfo driverInfo = quarantineInfo.getDriverInfo();
        UpdateOperations<QuarantineInfo> quaUpdate = quarantineInfoService.createUpdateOperations();
        quaUpdate.set("audit", audit);
        quaUpdate.set("auditDes", auditDes);
        quarantineInfoService.update(quaQuery, quaUpdate);

        //企业审核拒绝的情况下，修改orderTaking防疫申报按钮
        if (audit == 4 && StrUtil.isNotEmpty(quarantineInfo.getOid())) {
            UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();

            Order order = orderService.get("oid", quarantineInfo.getOid());
            /*若审核拒绝的是发货单位，则修改orderTaking的isQuarantine字段值为0
             *若发货单位为空，或发货单位为空，或发货单位无提交防疫信息，则修改isQuarantine字段值为0
             *若审核拒绝的是收货单位，则先判断发货单位是否审核通过,若审核通过，则isQuarantine字段值改为1，若未通过，则isQuarantine字段值改为0
             */
            if (quarantineInfo.getDefaultDownUnit().equals(order.getOutDefaultDownUnit()) || StrUtil.isEmpty(order.getOutDefaultDownUnit())) {
                otUpdateOperations.set("isQuarantine", 0);
            } else {
                Query<QuarantineInfo> query = quarantineInfoService.createQuery();
                query.filter("did", quarantineInfo.getDid());
                query.filter("oid", quarantineInfo.getOid());
                query.filter("defaultDownUnit", order.getOutDefaultDownUnit());
                query.order(Sort.descending("createTime"));
                List<QuarantineInfo> quarantineInfos = quarantineInfoService.list(query);
                if (quarantineInfos.size() <= 0 || (StrUtil.isEmpty(quarantineInfos.get(0).getAudit()) || quarantineInfos.get(0).getAudit() == 0
                        || quarantineInfos.get(0).getAudit() == 2 || quarantineInfos.get(0).getAudit() == 4))
                    otUpdateOperations.set("isQuarantine", 0);
                if (quarantineInfos.size() > 0 && (quarantineInfos.get(0).getAudit() == 1 || quarantineInfos.get(0).getAudit() == 3))
                    otUpdateOperations.set("isQuarantine", 1);
            }

            Query<OrderTaking> query = orderTakingService.createQuery().filter("oid", order.getOid());
            orderTakingService.update(query, otUpdateOperations);
        }
        //推送审核结果给微信司机用户
        if (StrUtil.isNotEmpty(auditDes)) {
            Map<String, String> map = pushInfoService.weChatSendDvr(driverInfo.getPhoneId(), driverInfo.getMobile(), driverInfo.getName(), "防疫申报审核", quarantineInfo.getDefaultDownUnit() + ":" + auditDes);
            pushInfoService.addPushInfo("企业疫情防控", "司机订单：" + quarantineInfo.getOid() + "防疫申报审核结果(" + quarantineInfo.getDefaultDownUnit() + ")" + auditDes, quarantineInfo.getDid(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));
        }

        return ServerResponse.createSuccess("修改审核结果成功！");
    }

    @Override
    public ServerResponse<String> putCoalTicket(String billCode, CoalTicket coalTicket) {
        Order order = orderService.get("outBillCode", billCode);
        if (order == null) return ServerResponse.createError("订单不存在！");

        UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
        updateOperations.set("coalTicket", coalTicket);
        orderService.update(orderService.createQuery().filter("oid", order.getOid()), updateOperations);
        return ServerResponse.createSuccess("成功");
    }

    @Override
    public ServerResponse<EGridResult> orderStatistics(String unitCode, String subCode, String startTime, String endTime, Integer page, Integer rows) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query<Order> query = orderService.createQuery();
        query.criteria("tranStatus").greaterThanOrEq(1);
        query.criteria("delete").equal(false);
        query.criteria("mold").notEqual(1);
        if (StrUtil.isNotEmpty(subCode)) {
            query.criteria("outDefaultDownUnit").equal(subCode);
        } else {
            query.criteria("outUnitCode").equal(unitCode);
        }
        try {
            if (StrUtil.isNotEmpty(startTime) && StrUtil.isNotEmpty(endTime)) {
                query.and(
                        query.criteria("time3").greaterThanOrEq(sdf.parse(startTime).getTime()),
                        query.criteria("time3").lessThan(sdf.parse(endTime).getTime())
                );
            } else if (StrUtil.isNotEmpty(startTime)) {
                query.criteria("time3").greaterThanOrEq(sdf.parse(startTime).getTime());
            } else if (StrUtil.isNotEmpty(endTime)) {
                query.criteria("time3").lessThan(sdf.parse(endTime).getTime());
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        long total = orderService.findPage(1, 5, query).getTotal();

        AggregationPipeline pipeline = orderService.createAggregation();
        pipeline.match(query);

        Projection didProjection = Projection.projection(
                "did",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$did").append("to", "objectId")
                )
        );
        pipeline.project(
                Projection.projection("carNum"),
                Projection.projection("outSubName"),
                Projection.projection("place"),
                Projection.projection("time2"),
                Projection.projection("time3"),
                didProjection);

        pipeline.lookup("t_driver_info", "did", "_id", "driverInfo");

        if (StrUtil.isNotEmpty(page)) {
            pipeline.skip((page - 1) * rows);
            pipeline.limit(rows);
        }
        Iterator<OrderStatistics> infos = pipeline.aggregate(OrderStatistics.class);

        List<OrderStatistics> resultList = new ArrayList<>();
        while (infos.hasNext()) {
            OrderStatistics synOrder = infos.next();
            resultList.add(synOrder);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult(total, resultList));
    }

    @Override
    public ServerResponse<String> searchBillCodeByIdentity(String identity, String unitCode, String subCode) {
        String billCode;
        Query<DriverInfo> dvrQuery = driverInfoService.createQuery();
        dvrQuery.criteria("identity").equal(identity);
        dvrQuery.criteria("haveOrder").equal(1);
        DriverInfo driverInfo = driverInfoService.get(dvrQuery);
        if (driverInfo == null) return ServerResponse.createError("无该身份号司机或司机暂无运单");
        String did = driverInfo.getObjectId().toHexString();
        Query<Order> query = orderService.createQuery();
        query.criteria("did").equal(did);
        if (StrUtil.isEmpty(subCode)) {
            query.criteria("outUnitCode").equal(unitCode);
        } else {
            query.criteria("inDefaultDown").equal(subCode);
            query.criteria("inUnitCode").equal(unitCode);
        }
        query.order(Sort.descending("time1"));      //按接单时间降序，查询最近的订单。
        Order order = orderService.get(query);
        if (order != null) {
            if (StrUtil.isEmpty(subCode)) {
                billCode = order.getOutBillCode();
            } else {
                billCode = order.getInBillCode();
            }
            return ServerResponse.createSuccess("查询成功", billCode);
        } else {
            return ServerResponse.createError("司机暂无订单");
        }
    }

    @Override
    public ServerResponse<String> udpateDeviceIdByDeviceCode(String deviceCode) {
        Query<DeviceInfo> query = deviceService.createQuery();
        query.field("deviceCode").equal(deviceCode);

        String deviceId = Utils.getUUID();
        UpdateOperations<DeviceInfo> updateOperations = deviceService.createUpdateOperations();
        updateOperations.set("deviceId", deviceId);
        updateOperations.set("updateTime", new Date().getTime());
        // 2, 保存
        UpdateResults results = deviceService.update(query, updateOperations);
        if (results.getUpdatedCount() == 1)
            return ServerResponse.createSuccess("修改成功", deviceId);
        else
            return ServerResponse.createError("修改失败");
    }


    @Override
    public ServerResponse<String> updateOrderVehicleName(String billCode, String vehicleCode) {
        TradeContract tradeContract;
        int outOrIn = 0;
        Order order = orderService.get("outBillCode", billCode);
        if (order == null) {
            order = orderService.get("inBillCode", billCode);
            if (order == null) return ServerResponse.createError("订单不存在");
//            if (StrUtil.isEmpty(order.getVehicleCodeInDvr())) return ServerResponse.createError("司机还未选择车型付费");
            tradeContract = tradeContractService.get("bizContractCode", order.getInBizContractCode());
            outOrIn = 1;    //收货
        } else {
//            if (StrUtil.isEmpty(order.getVehicleCodeOutDvr())) return ServerResponse.createError("司机还未选择车型付费");
            tradeContract = tradeContractService.get("bizContractCode", order.getOutBizContractCode());
            outOrIn = 0;    //发货
        }

        if (tradeContract == null) return ServerResponse.createError("缺少tradeContract数据");
        if (StrUtil.isEmpty(tradeContract.getVehicleTypeCostList()))
            return ServerResponse.createError("tradeContract缺少车型费用数据");
        Double loadCost = null;
        String vehicleName = null;
        List<VehicleTypeCost> costList = tradeContract.getVehicleTypeCostList();
        for (VehicleTypeCost cost : costList) {
            if (vehicleCode.equals(cost.getVehicleCode())) {
                loadCost = cost.getLoadCost();
                vehicleName = cost.getVehicleName();
                break;
            }
        }
        if (StrUtil.isEmpty(loadCost)) return ServerResponse.createError("tradeContract缺少车型费用数据");

        //费用金额单位元转分
        String loadCostStr = String.valueOf(loadCost);
        int point = loadCostStr.indexOf(".");
        if (point>0){
            String loadCostStr1 = loadCostStr.substring(0,point);
            String loadCostStr2 = loadCostStr.substring(point+1);
            if (loadCostStr2.length()>2) {
                loadCostStr2 = loadCostStr2.substring(0,2);
            } else {
                for (int i=0; i<2-loadCostStr2.length(); i++){
                    loadCostStr2 = loadCostStr2 + "0";
                }
            }
            loadCostStr = loadCostStr1+loadCostStr2;
        }
        int loadCostInt = Integer.valueOf(loadCostStr);

        Query<Order> query = orderService.createQuery();
        query.criteria("oid").equal(order.getOid());

        UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
        if (outOrIn == 0) {
            query.or(
                    query.criteria("pay2").equal(null),
                    query.criteria("pay2").equal(false)
            );

            updateOperations.set("vehicleCodeOutAPP", vehicleCode);
            updateOperations.set("vehicleNameOutAPP", vehicleName);
            updateOperations.set("handlingCostOutAPP", loadCostInt);
        } else {
            query.or(
                    query.criteria("pay4").equal(null),
                    query.criteria("pay4").equal(false)
            );

            updateOperations.set("vehicleCodeInAPP", vehicleCode);
            updateOperations.set("vehicleNameInAPP", vehicleName);
            updateOperations.set("handlingCostInAPP", loadCostInt);
        }

        UpdateResults results = orderService.update(query, updateOperations);
        if (results.getUpdatedExisting()) {
            return ServerResponse.createSuccess("修改成功");
        } else {
            return ServerResponse.createError("修改失败");
        }
    }

    @Override
    public ServerResponse<String> updateOrderRemark4(String billCode, String remark4) {
        Order order = orderService.get("outBillCode", billCode);
        if (order == null) order = orderService.get("inBillCode", billCode);
        if (order == null) return ServerResponse.createError("订单不存在");

        Query<Order> query = orderService.createQuery();
        query.criteria("oid").equal(order.getOid());
        UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
        updateOperations.set("remark4", remark4);
        UpdateResults results = orderService.update(query, updateOperations);
        if (results.getUpdatedExisting()) {
            return ServerResponse.createSuccess("修改成功");
        } else {
            return ServerResponse.createError("修改失败");
        }
    }

    @Override
    public ServerResponse<String> updateOrderExit(String billCode, Integer bizType) {
        if (bizType != 1 && bizType != 0) return ServerResponse.createError("票据类型错误");
        Query<Order> query = orderService.createQuery();
        UpdateOperations<Order> updateOperations = orderService.createUpdateOperations();
        if (bizType == 0) {//订单企业系统票号
            query.filter("inBillCode", billCode);
            updateOperations.set("inIsExit", "1");
        } else {
            query.filter("outBillCode", billCode);
            updateOperations.set("outIsExit", "1");
        }
        Order order = orderService.get(query);
        if (order == null || order.getDelete()) return ServerResponse.createError("订单不存在");
        orderService.update(query, updateOperations);

        return ServerResponse.createSuccess("修改成功");
    }
}
