package com.erdos.coal.park.api.customer.dao.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.park.api.customer.dao.IUserDefinedAddressDao;
import com.erdos.coal.park.api.customer.entity.UserDefinedAddress;
import org.springframework.stereotype.Repository;

@Repository("userDefinedAddressDao")
public class UserDefinedAddressDaoImpl extends BaseMongoDAOImpl<UserDefinedAddress> implements IUserDefinedAddressDao {
}
