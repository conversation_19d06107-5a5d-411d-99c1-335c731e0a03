package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.dao.ICustomerUserDao;
import com.erdos.coal.park.api.customer.entity.CustomerOpenid;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerOpenidService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.entity.UserTokenRecord;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.api.manage.service.IUserTokenRecordService;
import com.erdos.coal.utils.NumFmtUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("customerService")
public class CustomerUserServiceImpl extends BaseMongoServiceImpl<CustomerUser, ICustomerUserDao> implements ICustomerUserService {
    @Resource
    private ISMSService smsService;
    @Resource
    private IUserTokenRecordService userTokenRecordService;
    @Resource
    private ICustomerOpenidService customerOpenidService;

    @Override
    public ServerResponse<CustomerUser> getCustomerUser() {
        String cid = ShiroUtils.getUserId();
        CustomerUser customerUser = this.getByPK(cid);

        CustomerUser result = new CustomerUser();
        result.setName(customerUser.getName());
        result.setMobile(customerUser.getMobile());
        result.setState(customerUser.getState());
        result.setThirdPartyUser(customerUser.isThirdPartyUser());
        return ServerResponse.createSuccess("查询成功", result);
    }

    @Override
    public ServerResponse<AccessToken> customReg(String phoneId, String mobile, String password, String code) {

        //TODO: 1, 手机号合法性校验(测试放开)
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        //TODO: 2, 验证码校验
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null || !sms.getCode().equals(code)) {
            return ServerResponse.createError("验证码错误");
        }

        //TODO: 3, 判断手机号是否存在
        CustomerUser customerUser = this.get("mobile", mobile);
        if (customerUser == null) {
            //TODO: 未注册
            customerUser = new CustomerUser();
            customerUser.setMobile(mobile);
            /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
            customerUser.setPassword(passwordEncoder.encode(password));*/
            customerUser.setPassword(Utils.md5(password));
            customerUser.setName(mobile);
            customerUser.setState(0);
            customerUser.setPhoneId(phoneId);

            CustomerUser result = this.save(customerUser);

            //shiroUserSecurity.putShiroUserToCache(result.getObjectId().toHexString(), mobile, result.getPassword(), UserType.CU.toString());

            String token = JwtUtil.sign(UserType.CU, mobile, customerUser.getPassword());
            return ServerResponse.createSuccess("注册成功", new AccessToken(token));
        } else {
            //TODO: 已注册
            return ServerResponse.createError("该号码已经存在");
        }
    }

    @Override
    public ServerResponse<AccessToken> customReg2(String phoneId, String mobile, String password, String code) {

        //TODO: 1, 手机号合法性校验(测试放开)
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        //TODO: 2, 验证码校验
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null || !sms.getCode().equals(code)) {
            return ServerResponse.createError("验证码错误");
        }

        //TODO: 3, 判断手机号是否存在
        CustomerUser customerUser = this.get("mobile", mobile);
        if (customerUser == null) {
            //TODO: 未注册
            customerUser = new CustomerUser();
            customerUser.setMobile(mobile);
            customerUser.setPassword(Utils.md5(password));
            customerUser.setName(mobile);
            customerUser.setState(0);
            customerUser.setPhoneId(phoneId);
            customerUser.setInitialOpenid(phoneId);

            CustomerUser result = this.save(customerUser);

            String token = JwtUtil.sign(UserType.CU, mobile, customerUser.getPassword());

            //添加用户token记录
            UserTokenRecord tokenRecord = new UserTokenRecord();
            tokenRecord.setUserId(result.getObjectId().toHexString());
            tokenRecord.setUserType("CU");
            tokenRecord.setToken(token);
            userTokenRecordService.save(tokenRecord);

            return ServerResponse.createSuccess("注册成功", new AccessToken(token));
        } else {
            //TODO: 已注册
            return ServerResponse.createError("该号码已经存在");
        }
    }

    @Override
    public ServerResponse<AccessToken> customLogin(String phoneId, String mobile, String password, String code) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");
        //TODO:1.验证用户登录信息是否正确
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String newPD = passwordEncoder.encode(password);*/
        String newPD = Utils.md5(password);
        Query<CustomerUser> query = this.createQuery();
        query.filter("mobile", mobile);
        query.filter("password", newPD);

        CustomerUser cUser = this.get(query);   //this.get("mobile", mobile);
        if (cUser == null) return ServerResponse.createError("用户名或密码错误");

        //TODO:3.没有短信验证码，且登录的手机序列号 和 数据库存储的不一致 则需要发送短信验证码，并返回登录失败
        if (StrUtil.isEmpty(code) && !cUser.getPhoneId().equals(phoneId)) {
            ServerResponse<String> sr = new ServerResponse<>();
            sr.setStatus(2);
            sr.setMsg("检测到新设备登录，需要短信验证");
            sr.setData(cUser.getMobile());
            throw new GlobalException(sr);
        }

        //TODO:5.有短信验证码，且登录的手机序列号 和 数据库存储的不一致 则修改数据存储的手机序列号
        if (!StrUtil.isEmpty(code) && !cUser.getPhoneId().equals(phoneId)) {
            //验证码校验
            SMS sms = smsService.get("mobile", cUser.getMobile());
            if (sms == null || !sms.getCode().equals(code))
                throw new GlobalException(ServerResponse.createError("验证码错误"));

            //修改用户 手机序列号
            UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
            updateOperations.set("phoneId", phoneId);
            this.update(this.createQuery().filter("_id", cUser.getObjectId()), updateOperations);
        }

        //异步添加用户信息到缓存
        //shiroUserSecurity.putShiroUserToCache(cUser.getObjectId().toHexString(), cUser.getMobile(), cUser.getPassword(), UserType.CU.toString());

        String token = JwtUtil.sign(UserType.CU, mobile, cUser.getPassword());
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }

    @Override
    public ServerResponse<AccessToken> customLogin2(String phoneId, String mobile, String password, String code) {
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        //TODO:1.验证用户登录信息是否正确
        String newPD = Utils.md5(password);
        Query<CustomerUser> query = this.createQuery();
        query.filter("password", newPD);
        query.filter("mobile", mobile);

        CustomerUser cUser = this.get(query);
        if (cUser == null) return ServerResponse.createError("用户名或密码错误");
        String initialOpenid = cUser.getPhoneId();

        /*if (!cUser.getPhoneId().equals(phoneId)) {
            //限制用户三天才可以更换openid
            Query<CustomerOpenid> openidQuery = customerOpenidService.createQuery();
            openidQuery.criteria("cid").equal(cUser.getObjectId().toHexString());
            openidQuery.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(null, "00:00:00", -3));

//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(new Date());
//            calendar.add(Calendar.HOUR_OF_DAY, -1);
//            Date d = calendar.getTime();
//            openidQuery.criteria("createTime").greaterThanOrEq(d);
            List<CustomerOpenid> openids = customerOpenidService.list(openidQuery);
            if (openids.size() > 0) return ServerResponse.createError("频繁更换微信，登录失败");
        }*/

        //TODO:3.没有短信验证码，且登录的手机序列号 和 数据库存储的不一致 则需要发送短信验证码，并返回登录失败
        if (StrUtil.isEmpty(code) && !cUser.getPhoneId().equals(phoneId)) {
            ServerResponse<String> sr = new ServerResponse<>();
            sr.setStatus(2);
            sr.setMsg("检测到新微信登录，需要短信验证");
            sr.setData(cUser.getMobile());
            throw new GlobalException(sr);
        }

        //TODO:5.有短信验证码，且登录的手机序列号 和 数据库存储的不一致 则修改数据存储的用户微信openId
        if (!StrUtil.isEmpty(code) && !cUser.getPhoneId().equals(phoneId)) {

            //验证码校验
            SMS sms = smsService.get("mobile", cUser.getMobile());
            if (sms == null || !sms.getCode().equals(code))
                throw new GlobalException(ServerResponse.createError("验证码错误"));

            //修改用户 手机序列号
            UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
            updateOperations.set("phoneId", phoneId);
            this.update(this.createQuery().filter("_id", cUser.getObjectId()), updateOperations);

            //添加用户openid使用记录
            CustomerOpenid customerOpenid = new CustomerOpenid();
            customerOpenid.setOpenid(phoneId);
            customerOpenid.setCid(cUser.getObjectId().toHexString());
            customerOpenidService.save(customerOpenid);
        }

        String token = JwtUtil.sign(UserType.CU, mobile, cUser.getPassword());

        //更新用户token记录
        Query<UserTokenRecord> tokenRecordQuery = userTokenRecordService.createQuery();
        tokenRecordQuery.criteria("userId").equal(cUser.getObjectId().toHexString());
        tokenRecordQuery.criteria("userType").equal("CU");
        UserTokenRecord tokenRecord = userTokenRecordService.get(tokenRecordQuery);
        if (tokenRecord == null) {
            tokenRecord = new UserTokenRecord();
            tokenRecord.setUserId(cUser.getObjectId().toHexString());
            tokenRecord.setUserType("CU");
            tokenRecord.setToken(token);
            userTokenRecordService.save(tokenRecord);
        } else {
            UpdateOperations<UserTokenRecord> updateTokenRecord = userTokenRecordService.createUpdateOperations();
            updateTokenRecord.set("token", token);
            userTokenRecordService.update(userTokenRecordService.createQuery().filter("userId", cUser.getObjectId().toHexString()), updateTokenRecord);
        }
        if (StrUtil.isEmpty(cUser.getInitialOpenid()))
            this.update(this.createQuery().filter("_id", cUser.getObjectId()), this.createUpdateOperations().set("initialOpenid", initialOpenid));
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }


    @Override
    public ServerResponse<Boolean> checkPoneId(String mobile, String phoneId) {
        CustomerUser user = this.get("mobile", mobile);
        if (user != null && !phoneId.equals(user.getPhoneId())) {
            return ServerResponse.createSuccess("检查成功",true);
        } else {
            return ServerResponse.createSuccess("检查成功",false);
        }
    }

    /**
     * mobile手机号查询用户
     * password密码不为空时判断密码是否正确
     */
    @Override
    public CustomerUser getCUByMobile(String mobile, String password) {
        CustomerUser cUser = this.get("mobile", mobile);
        if (cUser == null)
            cUser = this.get("name", mobile);

        //使用BCryptPasswordEncoder进行加密的密码是否正确
        //if ((cUser == null) || (StrUtil.isNotEmpty(password) && !new BCryptPasswordEncoder().matches(password, cUser.getPassword())))
        if ((cUser == null) || (StrUtil.isNotEmpty(password) && !password.equals(cUser.getPassword())))
            return null;

        return cUser;
    }

    @Override
    public ServerResponse<AccessToken> editPwd(String oldPwd, String newPwd, String confirmPwd) {
        //TODO: 1 先判断新密码两次输入的是否一样
        if (!newPwd.equals(confirmPwd)) {
            return ServerResponse.createError("两次密码输入不一致");
        }

        String cid = ShiroUtils.getUserId();
        CustomerUser customerUser = this.getByPK(cid);
        //TODO: 2 判断旧密码是否正确
        String pwd = customerUser.getPassword();
        //if (!new BCryptPasswordEncoder().matches(oldPwd, pwd)) {
        if (StrUtil.isNotEmpty(pwd) && !pwd.equals(oldPwd)) {
            return ServerResponse.createError("旧密码输入错误");
        }

        //更新密码
        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        updateOperations.set("password", passwordEncoder.encode(newPwd));*/
        updateOperations.set("password", Utils.md5(newPwd));
        this.update(this.createQuery().filter("_id", customerUser.getObjectId()), updateOperations);

        return ServerResponse.createSuccess("修改密码成功");
    }

    @Override
    public ServerResponse<AccessToken> resetPwd(String username, String code, String newPwd, String confirmPwd) {
        //TODO: 1 判断新密码两次输入的是否一样
        if (!newPwd.equals(confirmPwd))
            return ServerResponse.createError("两次密码输入不一致");

        //TODO: 2 先按手机号 或 名称查询
        CustomerUser customerUser = this.get("mobile", username);
        if (customerUser == null)
            customerUser = this.get("name", username);

        //TODO: 3 用户不存在
        if (customerUser == null)
            return ServerResponse.createError("用户不存在");

        //TODO: 4 验证码校验
        String mobile = customerUser.getMobile();
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null)
            return ServerResponse.createError("请先获取验证码");
        if (!sms.getCode().equals(code))
            return ServerResponse.createError("验证码错误");

        //更新密码
        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        updateOperations.set("password", passwordEncoder.encode(newPwd));*/
        updateOperations.set("password", Utils.md5(newPwd));
        this.findAndModify(this.createQuery().filter("_id", customerUser.getObjectId()), updateOperations);

        UsernamePasswordToken token = new UsernamePasswordToken(customerUser.getMobile(), newPwd);
        AccessToken accessToken = new AccessToken(token.toString());
        return ServerResponse.createSuccess("重置密码成功", accessToken);
    }

    @Override
    public ServerResponse<Integer> checkBarPwd() {
        String cid = ShiroUtils.getUserId();
        CustomerUser customerUser = this.getByPK(cid);
        if (StrUtil.isEmpty(customerUser.getBarPwd()))
            return ServerResponse.createSuccess("可设置交易密码", 0);
        return ServerResponse.createSuccess("可修改交易密码", 1);
    }

    @Override
    public ServerResponse<String> setBarPwd(String barPwd, String confirmPwd) {
        if (!barPwd.equals(confirmPwd))
            return ServerResponse.createError("两次密码不一致");

        String cid = ShiroUtils.getUserId();
        CustomerUser customerUser = this.getByPK(cid);
        if (!StrUtil.isEmpty(customerUser.getBarPwd()))
            return ServerResponse.createError("交易密码已成功设置，不可重复设置");

        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        updateOperations.set("barPwd", passwordEncoder.encode(barPwd));*/
        updateOperations.set("barPwd", Utils.md5(barPwd));
        this.update(this.createQuery().filter("_id", customerUser.getObjectId()), updateOperations);

        return ServerResponse.createSuccess("交易密码设置成功");
    }

    @Override
    public ServerResponse<String> updateBarPwd(String oldBarPwd, String newBarPwd, String confirmPwd) {
        if (!newBarPwd.equals(confirmPwd))
            return ServerResponse.createError("两次密码不一致");

        String cid = ShiroUtils.getUserId();
        CustomerUser customerUser = this.getByPK(cid);
        //if (!new BCryptPasswordEncoder().matches(oldBarPwd, customerUser.getBarPwd()))
        if (StrUtil.isNotEmpty(customerUser.getBarPwd()) && !customerUser.getBarPwd().equals(Utils.md5(oldBarPwd)))
            return ServerResponse.createError("旧交易密码输入错误");

        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        updateOperations.set("barPwd", passwordEncoder.encode(newBarPwd));*/
        updateOperations.set("barPwd", Utils.md5(newBarPwd));
        this.update(this.createQuery().filter("_id", customerUser.getObjectId()), updateOperations);

        return ServerResponse.createSuccess("交易密码修改成功");
    }

    @Override
    public ServerResponse<String> smsUpBarPwd(String smsCode, String barPwd, String confirmPwd) {
        if (!barPwd.equals(confirmPwd))
            return ServerResponse.createError("两次密码不一致");

        String cid = ShiroUtils.getUserId();
        CustomerUser customerUser = this.getByPK(cid);
        //验证码校验
        SMS sms = smsService.get("mobile", customerUser.getMobile());
        if (sms == null || !sms.getCode().equals(smsCode)) {
            return ServerResponse.createError("验证码错误");
        }

        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        updateOperations.set("barPwd", passwordEncoder.encode(barPwd));*/
        updateOperations.set("barPwd", Utils.md5(barPwd));
        this.update(this.createQuery().filter("_id", customerUser.getObjectId()), updateOperations);

        return ServerResponse.createSuccess("交易密码修改成功");
    }

    @Override
    public ServerResponse<String> checkBarPwd(String barPwd) {
        String cid = ShiroUtils.getUserId();
        CustomerUser dataUser = this.getByPK(cid);
        if (StrUtil.isEmpty(dataUser.getBarPwd()))
            return ServerResponse.createError("客商还未设置交易密码");

        //if (new BCryptPasswordEncoder().matches(barPwd, dataUser.getBarPwd()))
        if (dataUser.getBarPwd().equals(Utils.md5(barPwd)))
            return ServerResponse.createSuccess("验证通过");

        return ServerResponse.createError("密码错误myte");
    }

    @Override
    public ServerResponse<String> setCustomerFee(double fee) {
        String je = NumFmtUtil.finance(fee);//格式化金额

        String cid = ShiroUtils.getUserId();
        assert cid != null;
        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations();
        updateOperations.set("fee", je);
        this.update(this.createQuery().filter("_id", new ObjectId(cid)), updateOperations);
        return ServerResponse.createSuccess("设置成功", je);
    }

    @Override
    public ServerResponse<String> getCustomerFee() {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = this.getByPK(cid);

        String fee = cUser.getFee();
        return ServerResponse.createSuccess("查询成功", fee);
    }

    @Override
    public ServerResponse<String> updateMobile(String mobile, String code) {
        //TODO: 1, 验证码校验
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null) return ServerResponse.createError("请先获取验证码");

        if (!sms.getCode().equals(code)) return ServerResponse.createError("验证码错误");

        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = this.getByPK(cid);
        //TODO: 2, 手机号合法性校验(测试放开)
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("手机号码不正确");

        //TODO: 3, 判断手机号是否已使用
        CustomerUser isExist = this.get("mobile", mobile);

        if (isExist == null) {   //未使用
            Query<CustomerUser> query = this.createQuery().filter("mobile", cUser.getMobile());
            UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations().set("mobile", mobile);
            this.update(query, updateOperations);

            return ServerResponse.createSuccess("修改成功");
        } else {
            return ServerResponse.createError("该手机号已使用");
        }
    }

    @Override
    public ServerResponse<String> updateName(String name) {
        String cid = ShiroUtils.getUserId();

        Query<CustomerUser> query = this.createQuery().filter("_id", new ObjectId(cid));
        UpdateOperations<CustomerUser> updateOperations = this.createUpdateOperations().set("name", name);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public List<String> findPermissions(String name) {
        //返回当前用户权限列表
        List<String> permissions = new ArrayList<>();
        permissions.add("sys:menu:add");
        permissions.add("sys:menu:edit");
        permissions.add("sys:menu:delete");
        return permissions;
    }
}