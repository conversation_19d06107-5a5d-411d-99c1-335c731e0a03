package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.sys.dao.ISysMenuDao;
import com.erdos.coal.park.web.sys.dao.ISysRoleDao;
import com.erdos.coal.park.web.sys.dao.ISysRoleMenuDao;
import com.erdos.coal.park.web.sys.dao.ISysUserDao;
import com.erdos.coal.park.web.sys.entity.SysMenu;
import com.erdos.coal.park.web.sys.entity.SysRole;
import com.erdos.coal.park.web.sys.entity.SysRoleMenu;
import com.erdos.coal.park.web.sys.pojo.MenuData;
import com.erdos.coal.park.web.sys.service.ISysRoleService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service("sysRoleService")
public class SysRoleServiceImpl extends BaseMongoServiceImpl<SysRole, ISysRoleDao> implements ISysRoleService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private ISysMenuDao sysMenuDao;
    @Resource
    private ISysRoleMenuDao roleMenuDao;
    @Resource
    private ISysUserDao sysUserDao;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {

        Query<SysRole> query = this.createQuery();
        // query.criteria("id").notEqual("1"); // 去掉管理员角色
        query.order(Sort.descending("order"));

        return this.findPage(page, rows, query);

//        List<SysRole> roleList = this.list(null, "order");
//
//        List<RoleData> roleDataList = new ArrayList<RoleData>();
//        for (SysRole role : roleList) {
//            RoleData rd = new RoleData();
//            String id = role.getId();
//            rd.setName(role.getName());
//            rd.setId(role.getId());
//            rd.setOrder(role.getOrder());
//            rd.setState(role.getState());
//
//            Map<String, Object> map = new HashMap<>();
//            map.put("roleId", id);
//            List<SysUser> userList = sysUserDao.list(map);//根据角色id查询用户列表
//            List<String> list = new ArrayList<>();
//            for (SysUser user : userList) {
//                list.add(user.getName());//获取用户名
//            }
//
//            rd.setRoleUser(list.toArray(new String[list.size()]));
//            list.clear();
//            roleDataList.add(rd);
//        }
//
//        int size = roleDataList.size();
//        int pageCount = size / rows;
//        int fromIndex = rows * (page - 1);
//        int toIndex = fromIndex + rows;
//        if (toIndex >= size) {
//            toIndex = size;
//        }
//        if (page > pageCount + 1) {
//            fromIndex = 0;
//            toIndex = 0;
//        }
//
//        EGridResult eGridResult = new EGridResult(roleDataList.size(), roleDataList.subList(fromIndex, toIndex));
//        return eGridResult;
    }

    @Override
    //查询菜单权限
    public EGridResult loadTreeGrid(Integer page, Integer rows) {

        //查询当前role
        SysRole sysRole = this.get("id", request.getParameter("id"));

        //查询所有menu
        Query<SysMenu> mQuery = sysMenuDao.createQuery();
        mQuery.filter("type", 1); //只取功能模块的
        mQuery.order(Sort.ascending("type"), Sort.ascending("order"));
        //List<SysMenu> menuList = sysMenuDao.list(map, "type,order");
        List<SysMenu> menuList = sysMenuDao.list(mQuery);

        //查询role 对应的角色列表
        List<SysRoleMenu> rmList = new ArrayList<>();
        if (sysRole != null) {
            Query<SysRoleMenu> rmQuery = roleMenuDao.createQuery().filter("rid", sysRole.getId());
            rmList = roleMenuDao.list(rmQuery);
        }

        List<MenuData> mdList = new ArrayList<>();

        for (SysMenu menu : menuList) {
            //封装MenuData
            MenuData md = new MenuData();

            md.setMenuId(menu.getId());
            md.set_parentId(menu.get_parentId());
            //md.setAttributes(menu.getAttributes());
            md.setChildren(menu.getChildren());
            md.setEnable(menu.getEnable());
            md.setOrder(menu.getOrder());
            md.setState(menu.getState());
            md.setText(menu.getText());
            md.setType(menu.getType());
            md.setUrl(menu.getUrl());

            if (ObjectUtil.isNotEmpty(sysRole)) { //增加还是修改
                for (SysRoleMenu rm : rmList) {
                    String mid = rm.getMid();
                    String[] attr = rm.getAttr();
                    if (!StrUtil.isEmpty(mid) && mid.equals(menu.getId())) {
                        md.setAdd(attr[0]);
                        md.setDelete(attr[1]);
                        md.setEdit(attr[2]);
                        md.setQuery(attr[3]);
                        break;
                    }
                }
            }
            mdList.add(md);
        }

        int size = mdList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        EGridResult eGridResult = new EGridResult(mdList.size(), mdList.subList(fromIndex, toIndex));
        return eGridResult;

    }

    boolean checkValue(SysRole sysRole) {
        if (sysRole == null) return false;
        if (ObjectUtil.isEmpty(sysRole.getId())) return false;
        if (ObjectUtil.isEmpty(sysRole.getName())) return false;
        // menus, funcs 必须有值, 否则没有意义
        if (ObjectUtil.isEmpty(sysRole.getMenus())) return false;
        if (ObjectUtil.isEmpty(sysRole.getFuncs())) return false;
        return true;
    }

    @Override
    public ServerResponse addRole(SysRole sysRole) {
        //检查
        if (sysRole != null)
            sysRole.setId(Utils.getUUID());
        if (checkValue(sysRole)) {
            this.save(sysRole);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数错误");
        }
    }

    @Override
    public ServerResponse editRole(SysRole sysRole) {
        //检查
        if (checkValue(sysRole)) {
            Query<SysRole> cond = this.createQuery();
            UpdateOperations<SysRole> value = this.createUpdateOperations();

            cond.filter("id", sysRole.getId());
            cond.filter("updateTime", sysRole.getUpdateTime());

            value.set("name", sysRole.getName());
            value.set("menus", sysRole.getMenus());
            value.set("funcs", sysRole.getFuncs());

            this.update(cond, value);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数错误");
        }
    }

    @Override
    public ServerResponse deleteRole(SysRole sysRole) {
        //检查
        if (checkValue(sysRole)) {
            this.delete("id", sysRole.getId());
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数错误");
        }
    }

    @Override
    public List<HashMap<String, Object>> roleSel() {

        Query<SysRole> query = this.createQuery();
        query.order(Sort.ascending("order"));
        List<SysRole> roleList = this.list(query);
        //List<SysRole> roleList = this.list(null, "order");

        List<HashMap<String, Object>> roleDataList = new ArrayList<>();

        for (SysRole role : roleList) {
            HashMap<String, Object> hashMap = new HashMap<>();//必须在循环里，否则会覆盖
            hashMap.put("id", role.getId());
            hashMap.put("name", role.getName());
            roleDataList.add(hashMap);
        }

        return roleDataList;
    }
}

















