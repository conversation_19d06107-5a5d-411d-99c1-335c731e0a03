package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidCostService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidService;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;


/**
 * Created by LIGX on 2018/12/27.
 * 司机端接口
 */
//"司机钱包微信接口"
@RestController
@RequestMapping("/api/dvr/transaction")
public class TransactionController extends BaseController {

    @Resource
    private IWxPrepaidService wxPrepaidService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IWxPrepaidCostService wxPrepaidCostService;

    @InvokeLog(description = "微信请求生成支付订单 接口") //日志
    @PostMapping(value = "/request_pay")
    public ServerResponse<Map<String, String>> requestPayHandler(
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "body") String body,          //"商品描述 APP名字-实际商品名称"
            @RequestParam(value = "totalFee") Integer totalFee  //"订单金额（分）"
    ) throws GlobalException {
        //DriverInfo driverInfo = (DriverInfo) request.getAttribute(Constant.CURRENT_USER_KEY);
        //return wxPrepaidService.wxRequestPay(body, totalFee, did, "driver");
        //司机给自己充值。
        /*if (StrUtil.isNotEmpty(totalFee))
            totalFee = new BigDecimal(totalFee).multiply(new BigDecimal(100)).intValue();//金额单位 元转为分*/
        return ServerResponse.createError("接口停用");
        // String did = ShiroUtils.getUserId();

        /*if (StrUtil.isEmpty(openid)) openid = driverInfoService.getByPK(ShiroUtils.getUserId()).getPhoneId();
        if ("黑金宝司机充值".equals(body) && totalFee > 50000) return ServerResponse.createError("充值金额不能大于五百元");
        return wxPrepaidService.weChatRequestPay(openid, body, totalFee, did, 1, null, null, null);*/
    }

    @InvokeLog(description = "微信请求生成支付订单(分账) 接口") //日志
    @PostMapping(value = "/request_pay2")
    public ServerResponse<Map<String, String>> requestPay2Handler(
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "body") String body,          //"商品描述 APP名字-实际商品名称"
            @RequestParam(value = "totalFee") Integer totalFee  //"订单金额（分）"
    ) throws GlobalException {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(ShiroUtils.getUserId());
        if (StrUtil.isEmpty(openid)) openid = driverInfo.getPhoneId();
        if ("黑金宝司机充值".equals(body) && totalFee > 50000) return ServerResponse.createError("充值金额不能大于五百元");
        body = "黑金宝司机[" + driverInfo.getMobile() + "]充值";
        return wxPrepaidService.weChatRequestPay3(openid, body, totalFee, did, 1, null, null, null);
    }

    @InvokeLog(description = "微信订单查询  接口", printReturn = false)
    @PostMapping(value = "/query_pay")
    public ServerResponse<WxResult> queryPayHandler(
            @RequestParam(value = "transactionId", required = false) String transactionId, //"订单号 微信订单号"
            @RequestParam(value = "outTradeNo", required = false) String outTradeNo        //"商户订单号"
    ) throws GlobalException {
//        DriverInfo driverInfo = (DriverInfo) request.getAttribute(Constant.CURRENT_USER_KEY);
        /*String did = ShiroUtils.getUserId();
        return wxPrepaidService.wxQueryPay(transactionId, outTradeNo, did, "driver");*/
        return wxPrepaidService.weChatQueryPay(transactionId, outTradeNo);
    }

    @InvokeLog(description = "账户余额查询  接口", printReturn = false)
    @PostMapping(value = "/query_account")
    public ServerResponse<BigDecimal> queryAccountHandler() throws GlobalException {
        return driverAccountService.queryAccount();
    }

    @InvokeLog(description = "查询司机账户明细 接口", printReturn = false) //日志
    @PostMapping(value = "/driver_account_list")
    public ServerResponse<EGridResult> driverAccountListHandler(
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return driverAccountService.driverAccountList(page, rows);
    }


    @InvokeLog(description = "微信请求生成支付订单(代收) 接口") //日志
    @PostMapping(value = "/request_cost_pay")
    public ServerResponse<Map<String, String>> requestCostPayHandler(
            @RequestParam(value = "oid") String oid,                            //订单编号
            @RequestParam(value = "totalFee") Integer totalFee,                 //"订单金额（分）"
            @RequestParam(value = "vehicleCode", required = false) String vehicleCode,      //车型编码
            @RequestParam(value = "bizContractCode") String bizContractCode,          //"商品描述 APP名字-实际商品名称"
            @RequestParam(value = "type") Integer type                          //缴费类型：0-装卸费支付，1-装卸费补缴
    ) throws GlobalException {
        String did = ShiroUtils.getUserId();
        DriverInfo driverInfo = driverInfoService.getByPK(did);
        String openid = driverInfo.getPhoneId();
        if (StrUtil.isEmpty(openid)) openid = driverInfoService.getByPK(ShiroUtils.getUserId()).getPhoneId();

        return wxPrepaidCostService.weChatRequestPay(did, openid, oid, totalFee, type, vehicleCode, bizContractCode);
    }
}
