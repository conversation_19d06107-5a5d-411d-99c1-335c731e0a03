package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.ICidRelationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP添加好友接口列表"
@RestController
@RequestMapping("/api/cus/relation")
public class CidRelationController extends BaseController {
    @Resource
    private ICidRelationService cidRelationService;

    @InvokeLog(description = "查询好友 接口") //日志
    @PostMapping(value = "/list")
    public ServerResponse<EGridResult> listHandler(
            @RequestParam(value = "page", required = false) Integer page,      //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows       //"每页多少条"
    ) throws GlobalException {
        return cidRelationService.listCidRelation(page, rows);
    }

    @InvokeLog(description = "添加好友 接口") //日志
    @PostMapping(value = "/add")
    public ServerResponse<String> addHandler(
            @RequestParam(value = "mobile") String mobile                       //"添加手机号"
    ) throws GlobalException {
        return cidRelationService.addCidRelation(mobile);
    }

    @InvokeLog(description = "删除好友 接口") //日志
    @PostMapping(value = "/delete")
    public ServerResponse<String> deleteHandler(
            @RequestParam(value = "id") String id                               //"好友编号"
    ) throws GlobalException {
        return cidRelationService.delCidRelation(id);
    }

}
