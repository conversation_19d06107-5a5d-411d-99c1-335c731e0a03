package com.erdos.coal.park.web;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.alibaba.sms.service.SMSService;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.business.entity.ShipmentOrder;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.pojo.OrderAggregate;
import com.erdos.coal.park.api.business.service.IShipmentOrderService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.ICustomerOpenidService;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IDriverOpenidService;
import com.erdos.coal.park.api.driver.service.IOrderLogisticsService;
import com.erdos.coal.park.web.app.entity.CusHDDKS;
import com.erdos.coal.park.web.app.service.ICusHDDKSService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.pojo.SubUnitGeo;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.hdd.http.HddEncryptRequest;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

@RestController("webTestController")
@RequestMapping("/web/test")
public class TestController extends BaseController {
    @Resource
    private MongoClient client;
    @Resource
    private ISysUnitService sysUnitService;                         //t_sys_unit  一级单位、二级单位
    @Resource
    private IGoodsService goodsService;                             //t_goods 货运信息表
    @Resource
    private IOrderService orderService;                             //t_order 订单表
    @Resource
    private IOrderTakingService orderTakingService;                 //t_order_taking 司机接单表（注：司机接单信息都整合到order中了，但是部分旧代码依旧会用到这个表）
    @Resource
    private IOrderLogisticsService orderLogisticsService;           //t_order_logistics 订单物流表
    @Resource
    private ICustomerOpenidService customerOpenidService;           //t_customer_openid 记录客商登录时的微信openid，功能：限制客商频繁换微信号登录
    @Resource
    private IDriverOpenidService driverOpenidService;               //t_driver_openid 记录司机登录时的微信openid，功能：限制司机频繁换微信号登录
    @Resource
    private IShipmentOrderService shipmentOrderService;
    @Resource
    private ICusHDDKSService cusHDDKSService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IUnitInfoService unitInfoService;

    @InvokeLog(description = "unique 接口") //日志
    @PostMapping("/unique")
    public ServerResponse<String> UniqueController(
            @RequestParam(value = "sign") String sign,
            @RequestParam(value = "table") String table,    //表名称
            @RequestParam(value = "objId") String objId,    //要修改的数据的objectId
            @RequestParam(value = "field") String field,    //要修改的字段名称
            @RequestParam(value = "value") String value     //要修改的字段值
    ) {
        if (sign.equals("3a6d7c47cf5e4c158ece460829f22bde")) {
            Long t1 = System.currentTimeMillis();
            // 整合二级单位围栏坐标等信息 到新字段 subUnitGeoList
            Query<SysUnit> query = sysUnitService.createQuery();
            query.criteria("pCode").notEqual(null);
            List<SysUnit> subUnitList = sysUnitService.list(query);

            for (SysUnit subUnit : subUnitList){
                List<SubUnitGeo> geoList = new ArrayList<>();
                if (StrUtil.isEmpty(subUnit.getGid())) continue;
                boolean add = true;
                if (subUnit.getSubUnitGeoList() != null && subUnit.getSubUnitGeoList().size()>0) {
                    geoList = subUnit.getSubUnitGeoList();

                    for (SubUnitGeo geo : geoList){
                        if (subUnit.getGid().equals(geo.getGid())){
                            add = false;
                            break;
                        }
                    }

                }

                if (add) {
                    SubUnitGeo subunitGeo = new SubUnitGeo();
                    subunitGeo.setGid(subUnit.getGid());
                    subunitGeo.setGeoCenter(subUnit.getGeoCenter());
                    subunitGeo.setGeoRadius(subUnit.getGeoRadius());
                    subunitGeo.setStartUsing(subUnit.getStartUsing());        // 电子围栏创建初状态
                    geoList.add(subunitGeo);

                    Query<SysUnit> upQuery = sysUnitService.createQuery();
                    upQuery.criteria("_id").equal(subUnit.getObjectId());
                    UpdateOperations<SysUnit> updateOperations = sysUnitService.createUpdateOperations();
                    updateOperations.set("subUnitGeoList", geoList);
                    sysUnitService.update(upQuery, updateOperations);
                }
            }

            Long t2 = System.currentTimeMillis();

            System.out.println("耗时： "+ (t2-t1) + "ms");

            /*ClientSession clientSession = client.startSession();
            Map<String, Object> driverUpMap = new HashMap<>();
            driverUpMap.put(field, value);
            Map<String, Object> upMap = new HashMap<>();
            upMap.put("$set", driverUpMap);

            MongoCollection<Document> collection = null;
            switch (table) {
                case "sys_unit":
                    collection = sysUnitService.getCollection();
                    break;
                case "t_goods":
                    collection = goodsService.getCollection();
                    break;
                case "t_order":
                    collection = orderService.getCollection();
                    break;
                case "t_order_taking":
                    collection = orderTakingService.getCollection();
                    break;
                case "t_order_logistics":
                    collection = orderLogisticsService.getCollection();
                    break;
                case "t_customer_openid":
                    collection = customerOpenidService.getCollection();
                    break;
                case "t_driver_openid":
                    collection = driverOpenidService.getCollection();
                    break;
            }

            if (collection != null)
                collection.updateOne(clientSession, new Document("_id", new ObjectId(objId)), Document.parse(JSONObject.toJSONString(upMap)));*/
        }
        return ServerResponse.createSuccess("成功");
    }

    @InvokeLog(description = "unique2 接口") //日志
    @PostMapping("/unique_2")
    public ServerResponse<String> unique2(
            @RequestParam(value = "sign") String sign,
            @RequestParam(value = "errmsg") String errmsg
    ) {
        if (!sign.equals("a71f58eb08d84f83bc15fab55e29a2b1")) return ServerResponse.createError("非法请求");

        Query<ShipmentOrder> query = shipmentOrderService.createQuery();
        //query.criteria("isPush").notEqual("1");
        query.criteria("errmsg").contains(errmsg);
        List<ShipmentOrder> list = shipmentOrderService.list(query);
        for (ShipmentOrder shipmentOrder1 : list) {
            Order order1 = orderService.get("oid", shipmentOrder1.getOid());

            CusHDDKS cusHDDKS1 = cusHDDKSService.get("cid", order1.getCid());
            String appKey1 = cusHDDKS1.getAppKey();
            String appSecret1 = cusHDDKS1.getAppSecret();

            Map<String, Object> map1 = pushShipmentOrder(shipmentOrder1.getParamMap(), appKey1, appSecret1);
            if (StrUtil.isEmpty(map1.get("errno")) || !map1.get("errno").equals("0")) {//推送返回结果失败，保存数据到shipmentOrder，下次再推送
                if (StrUtil.isNotEmpty(map1.get("errmsg"))) {
                    Query<ShipmentOrder> upQuery = shipmentOrderService.createQuery();
                    upQuery.criteria("_id").equal(shipmentOrder1.getObjectId());
                    UpdateOperations<ShipmentOrder> options = shipmentOrderService.createUpdateOperations();
                    options.set("errmsg", map1.get("errmsg"));
                    options.set("isPush", 1);
                    shipmentOrderService.update(upQuery, options);
                }
            } else {
                shipmentOrderService.delete(shipmentOrder1.getObjectId());
            }
        }

        return ServerResponse.createSuccess("成功");
    }

    public Map<String, Object> pushShipmentOrder(Map<String, Object> paramMap, String appKey, String appSecret) {
        // appUrl/appKey/appSecret 由链接平台提供
//        String appUrl = String.format("%s/%s", "http://beta-laas.yunxiaobao.com", "api/hlink-tss/shipmentSync/addRecord");    //测试环境
        String appUrl = String.format("%s/%s", "https://laas.yunxiaobao.com", "api/hlink-tss/shipmentSync/addRecord");          //生产环境
        /*String appKey = "qlt4vQ_90UKX5czGGhWFcA";
        String appSecret = "rUrp_el148I";*/

        /*
        *   app_key:NjT0u6Y38x9_1Q0moiTSjw
            app_secret:hSwgGQLaRZI
            url:http://beta-laas.yunxiaobao.com
        * */
        Map<String, Object> map = new HashMap<>();
        try {
            // Asserts.notEmpty(paramMap, "链接平台回写运单状态参数不能为空！");
            // 兼容参数
            // paramMap.put("extInfo", ParamBuilder.create().append("dataSource", "OFFLINE").append("requestSource", 3).build());
            // log.debug("当前推送明文参数 - {}", paramMap);
            // 发送请求
            String bodyStr = HddEncryptRequest.doRequest(appUrl, appKey, appSecret, paramMap);
            // 反序列化
            // errno	返回码，0：成功，其他说明失败
            // errmsg	返回信息
            JSONObject jsonObject = JSONObject.parseObject(bodyStr);

            String errno = jsonObject.getString("errno");
            map.put("errno", errno);
            String errmsg = jsonObject.getString("errmsg");
            map.put("errmsg", errmsg);
            if (errno.equals("0")) {
                JSONObject res = jsonObject.getJSONObject("res");
                String shipmentPinId = res.getString("shipmentPinId");
                Integer bizExist = res.getInteger("bizExist");
                map.put("shipmentPinId", shipmentPinId);
                map.put("bizExist", bizExist);
            }

            //VenusStringResp resp = JsonMapper.getInstance().fromJson(res, VenusStringResp.class);
            //return resp;
        } catch (Exception e) {
            //log.error("链接平台更新运单状态出错 - {}", ExceptionUtil.stacktraceToString(e));
            //throw new JointErrFControlException("链接平台更新运单状态出错", e);
            logger.warn("pushShipmentOrder -- " + e);
        }
        return map;
    }

    @InvokeLog(description = "推送货达失败的订单手动再次推送 接口") //日志
    @PostMapping("/unique_3")
    public ServerResponse<String> unique3(
            @RequestParam(value = "sign") String sign,
            @RequestParam(value = "oid") String oid
    ) {
        if (!sign.equals("cbee53acef2c490aa7f73c08dbd91fe1")) return ServerResponse.createError("非法请求");

        Order order = orderService.get("oid", oid);
        if ((order.getMold() == 0 && order.getOutChecking() == 3) || order.getInChecking() == 3) {
            //1.当前订单检票为3时，推送订单数据到货达
            CusHDDKS cusHDDKS = cusHDDKSService.get("cid", order.getCid());
            String appKey = cusHDDKS.getAppKey();
            String appSecret = cusHDDKS.getAppSecret();

            ShipmentOrder shipmentOrder = createShipmentOrder(order);
            Map<String, Object> map = pushShipmentOrder(shipmentOrder.getParamMap(), appKey, appSecret);
            if (StrUtil.isEmpty(map.get("errno")) || !map.get("errno").equals("0")) {//推送返回结果失败，保存数据到shipmentOrder，下次再推送
                if (StrUtil.isNotEmpty(map.get("errmsg"))) {
                    shipmentOrder.setErrmsg(map.get("errmsg").toString());
                    shipmentOrder.setIsPush("1");
                    shipmentOrder.setOid(order.getOid());
                }
                shipmentOrderService.save(shipmentOrder);
            }
        }

        return ServerResponse.createSuccess("成功");
    }

    private ShipmentOrder createShipmentOrder(Order order) {
        DriverInfo driverInfo = driverInfoService.getByPK(order.getDid());
        ShipmentOrder shipmentOrder = new ShipmentOrder(order.getBeginDistrictCode(), order.getBeginPoint(), order.getEndDistrictCode(), order.getEndPoint(), order.getCarNum(), driverInfo.getMobile(), driverInfo.getName(), driverInfo.getIdentity());
        if (order.getMold() == 1 || order.getMold() == 2) {
            shipmentOrder.setBizType(20);
            shipmentOrder.setOfflineDispatchType("10");
            shipmentOrder.setDeliveryId(order.getInBillCode());
            shipmentOrder.setSelfCode(order.getInBillCode());
            shipmentOrder.setType("1");
            shipmentOrder.setGoodsDetailName(order.getInVariety());
            shipmentOrder.setUnloadNetWeight(String.valueOf(Double.valueOf(order.getInGrossWeight()) - Double.valueOf(order.getInTareWeight())));
            if (order.getMold() == 2) {
                shipmentOrder.setLoadNetWeight(String.valueOf(Double.valueOf(order.getOutGrossWeight()) - Double.valueOf(order.getOutTareWeight())));
            }
        } else {
            shipmentOrder.setBizType(30);
            shipmentOrder.setOfflineDispatchType("20");
            shipmentOrder.setDeliveryId(order.getOutBillCode());
            shipmentOrder.setSelfCode(order.getOutBillCode());
            shipmentOrder.setType("2");
            shipmentOrder.setGoodsDetailName(order.getOutVariety());
            shipmentOrder.setLoadNetWeight(String.valueOf(Double.valueOf(order.getOutGrossWeight()) - Double.valueOf(order.getOutTareWeight())));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StrUtil.isNotEmpty(order.getTime3())) shipmentOrder.setLoadGrossTime(sdf.format(order.getTime3()));
        if (StrUtil.isNotEmpty(order.getTime5())) shipmentOrder.setUnloadTareTime(sdf.format(order.getTime5()));
        return shipmentOrder;
    }

    @InvokeLog(description = "查询一段时间的作废订单集合耗时 接口") //日志
    @PostMapping("/unique_4")
    public ServerResponse<String> unique4(
            @RequestParam(value = "sign") String sign,
            @RequestParam(value = "unitCode") String unitCode,
            @RequestParam(value = "hour") Integer hour
    ) {
        if (!sign.equals("89c667b5a11547a086a1be40c5785153")) return ServerResponse.createError("非法请求");

        Long time_s = System.currentTimeMillis();
        //todo:一、查询删除了的订单。
        List<OrderAggregate> resultList = new ArrayList<>();

        //当前日期 前推/后移 24小时
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR_OF_DAY, -hour);
        Date startTime = calendar.getTime();    //修改为查询24小时内的作废订单

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("delete", true);
        oQuery.or(oQuery.criteria("outUnitCode").equal(Pattern.compile(unitCode + "$")),
                oQuery.criteria("inUnitCode").equal(Pattern.compile(unitCode + "$")));
        oQuery.criteria("time6").greaterThanOrEq(startTime.getTime());
        oQuery.order(Sort.ascending("cid"));
        List<Order> orderList = oQuery.find().toList();

        int size = 0;
        OrderAggregate orderAgg = null;
        String cid = "";
        String outMinMax = "";
        String inMinMax = "";
        for (Order order : orderList) {
            //当前cid不同于上次cid时，将统计的最大最小号拼接的字符串结束，加入orderAggregate对象，且对象加入list列表，当前对象结束。
            if (StrUtil.isNotEmpty(cid) && !cid.equals(order.getCid())) {
                orderAgg.setOutMinMax(outMinMax.equals("") ? "" : outMinMax.substring(0, outMinMax.length() - 1));
                orderAgg.setInMinMax(inMinMax.equals("") ? "" : inMinMax.substring(0, inMinMax.length() - 1));
                resultList.add(orderAgg);

                outMinMax = ""; //初始化字符串对象
                inMinMax = "";  //初始化字符串对象
            }
            //第一次进入循环 和 当前cid不同于上一次cid时，创建新的OrderAggregate对象
            if (!cid.equals(order.getCid())) {
                cid = order.getCid();
                Query<UnitInfo> queryUnitInfo = unitInfoService.createQuery();
                queryUnitInfo.filter("unitCode", Pattern.compile(unitCode + "$"));
                queryUnitInfo.filter("cid", cid);
                UnitInfo unitInfo = unitInfoService.get(queryUnitInfo);
                orderAgg = new OrderAggregate();
                if (unitInfo != null) orderAgg.setUserCode(unitInfo.getUserCode());
            }
            //将订单的最大最小号拼接到字符串中。
            if ((order.getOutMinMax() != null)) {
                outMinMax = outMinMax.concat(order.getOutMinMax());
                outMinMax = outMinMax.concat(",");
            }
            if ((order.getInMinMax() != null)) {
                inMinMax = inMinMax.concat(order.getInMinMax());
                inMinMax = inMinMax.concat(",");
            }

            //当到达最后一条数据时，结束对象orderAggregate，并加入list列表
            size++;
            if (size >= orderList.size()) {
                orderAgg.setOutMinMax(outMinMax.equals("") ? "" : outMinMax.substring(0, outMinMax.length() - 1));
                orderAgg.setInMinMax(inMinMax.equals("") ? "" : inMinMax.substring(0, inMinMax.length() - 1));
                resultList.add(orderAgg);
            }
        }

        Long time_e = System.currentTimeMillis();
        return ServerResponse.createSuccess("查询成功", "耗时" + (time_e - time_s) + "毫秒");
    }

    @InvokeLog(description = "测试短信发送 接口") //日志
    @PostMapping("/unique_5")
    public ServerResponse<String> Unique5Controller(
            @RequestParam(value = "sign") String sign,
            @RequestParam(value = "type") Integer type
    ) {
        if (sign.equals("3a6d7c47cf5e4c158ece460829f22bde")) {
            /*SMSService.sendNoticeSMS("测试单位", "测试姓名", 1.0, "123456");

            Order order = new Order();
            order.setOid("1454481d7b834878b5afad6122813c87");
            order.setCarNum("宁AE2763");
            order.setOutSubName("测试单位");
            order.setOutVariety("测试物料");
            order.setOutGrossWeight("32.3");
            order.setOutTareWeight("0.8");

            order.setInSubName("in测试单位");
            order.setInVariety("in测试物料");
            order.setInGrossWeight("31.9");
            order.setInTareWeight("0.83");
            DriverInfo driverInfo = new DriverInfo();
            driverInfo.setMobile("15357039213");*/
            // goodsService.sendSms_ali(order, driverInfo,type);
            // goodsService.sendSms_HuaWei(order, driverInfo, type);
        }
        return ServerResponse.createSuccess("成功");
    }

}
