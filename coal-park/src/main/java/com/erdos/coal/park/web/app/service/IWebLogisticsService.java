package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;

import java.util.List;
import java.util.Map;

public interface IWebLogisticsService extends IBaseMongoService<OrderLogistics> {
    EGridResult loadGrid(Integer page, Integer rows);

    List getPointList();

    Map<String, Object> getMap();
}
