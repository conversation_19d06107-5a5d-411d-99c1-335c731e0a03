package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysMenu;

import java.util.List;

public interface ISysMenuService extends IBaseMongoService<SysMenu> {

    List<SysMenu> loadMenuTree(String pid);//加载业务模块树

    List<SysMenu> loadMenuTree(String pid, boolean hasSysTree);

    EGridResult loadTreeGrid(Integer page, Integer rows);

    // vue -------------------------------------------------------------------------------------------------------------
    SysMenu getTreeObject(String pid);

    ServerResponse appendMenu(SysMenu sysMenu);

    ServerResponse editMenu(SysMenu sysMenu);

    ServerResponse deleteMenu(String id);

    ServerResponse getLoginInfo(); //登录菜单
    // vue -------------------------------------------------------------------------------------------------------------

}