package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.web.sys.dao.ISysSwitchDao;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;
import com.erdos.coal.park.web.sys.service.ISysSwitchService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("sysSwitchService")
public class SysSwitchServiceImpl extends BaseMongoServiceImpl<SysSwitch, ISysSwitchDao> implements ISysSwitchService {

    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;
    @Resource
    private ICarDao carDao;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        return this.findPage(page, rows, this.createQuery());
    }

    @Override
    public ServerResponse addSwitch(SysSwitch sysSwitch) {
        if (checkSwitch(sysSwitch)) {
            Query<SysSwitch> swQuery = this.createQuery();
            swQuery.filter("code", sysSwitch.getCode());
            swQuery.filter("type", sysSwitch.getType());
            swQuery.filter("refundType", sysSwitch.getRefundType());
            SysSwitch sysSwitch1 = this.get(swQuery);

            if (sysSwitch1 == null) {
                sysSwitch.setId(Utils.getUUID());
                this.save(sysSwitch);
            } else {
                UpdateOperations<SysSwitch> updateOperations = this.createUpdateOperations();
                if (StrUtil.isNotEmpty(sysSwitch.getShare())) updateOperations.set("share", sysSwitch.getShare());
                //if (StrUtil.isNotEmpty(sysSwitch.getPhoto())) updateOperations.set("photo", sysSwitch.getPhoto());
                if (StrUtil.isNotEmpty(sysSwitch.getType())) updateOperations.set("type", sysSwitch.getType());
                if (StrUtil.isNotEmpty(sysSwitch.getCheck())) updateOperations.set("check", sysSwitch.getCheck());
                if (StrUtil.isNotEmpty(sysSwitch.getRefundType()))
                    updateOperations.set("refundType", sysSwitch.getRefundType());
                if (ObjectUtil.isNotEmpty(sysSwitch.getDvrPhotoTypes()))
                    updateOperations.set("dvrPhotoTypes", sysSwitch.getDvrPhotoTypes());
                if (ObjectUtil.isNotEmpty(sysSwitch.getCarPhotoTypes()))
                    updateOperations.set("carPhotoTypes", sysSwitch.getCarPhotoTypes());
                if (StrUtil.isNotEmpty(sysSwitch.getIsRefund()))
                    updateOperations.set("isRefund", sysSwitch.getIsRefund());
                if (StrUtil.isNotEmpty(sysSwitch.getSmsFee()))
                    updateOperations.set("smsFee", sysSwitch.getSmsFee());
                if (StrUtil.isNotEmpty(sysSwitch.getPhoneFee()))
                    updateOperations.set("phoneFee", sysSwitch.getPhoneFee());
                if (StrUtil.isNotEmpty(sysSwitch.getQueuingFee()))
                    updateOperations.set("queuingFee", sysSwitch.getQueuingFee());
                if (StrUtil.isNotEmpty(sysSwitch.getQueuingNum()))
                    updateOperations.set("queuingNum", sysSwitch.getQueuingNum());
                if (StrUtil.isNotEmpty(sysSwitch.getPayType()))
                    updateOperations.set("payType", sysSwitch.getPayType());
                if (StrUtil.isNotEmpty(sysSwitch.getNeedGPS())) updateOperations.set("needGPS", sysSwitch.getNeedGPS());

                this.update(swQuery, updateOperations);
            }

            if (sysSwitch.getCode() == 1) { //司机
                /*List<Integer> state = new ArrayList<>();
                state.add(1);
                state.add(2);*/
                //valueMap.put("updateTime", new Date().getTime());
                if (sysSwitch.getType() == 0) {     //app,修改driver表中审核状态
                    if (sysSwitch.getCheck() == 0) {        //须审核，则将未审核可用的司机 改为 未审核不可用
                        Query<DriverInfo> query = driverInfoService.createQuery().filter("state", 4);
                        driverInfoService.update(query, driverInfoService.createUpdateOperations().set("state", 0));
                    } else if (sysSwitch.getCheck() == 1) {   //无需审核，则将未审核不可用的司机 改为  未审核可用
                        Query<DriverInfo> query = driverInfoService.createQuery().filter("state", 0);
                        driverInfoService.update(query, driverInfoService.createUpdateOperations().set("state", 4));
                    }
                } else if (sysSwitch.getType() == 1) {    //微信小程序
                    if (sysSwitch.getCheck() == 0) {        //须审核，则将未审核可用的微信小程序司机 改为 未审核不可用
                        Query<WechatDriverInfo> query = wechatDriverInfoService.createQuery().filter("state", 4);
                        wechatDriverInfoService.update(query, wechatDriverInfoService.createUpdateOperations().set("state", 0));
                    } else if (sysSwitch.getCheck() == 1) {   //无需审核，则将未审核不可用的微信小程序司机 改为  未审核可用
                        Query<WechatDriverInfo> query = wechatDriverInfoService.createQuery().filter("state", 0);
                        wechatDriverInfoService.update(query, wechatDriverInfoService.createUpdateOperations().set("state", 4));
                    }
                }
            } else if (sysSwitch.getCode() == 2) {  //车辆
                if (sysSwitch.getCheck() == 1) {        //无需审核，则将未审核不可用的车辆 改为 未审核可用
                    Query<Car> query = carDao.createQuery().filter("verify", 0);
                    UpdateOperations<Car> updateOperations = carDao.createUpdateOperations().set("verify", 3);
                    carDao.update(query, updateOperations);
                } else if (sysSwitch.getCheck() == 0) { //须审核，则将未审核可用的车辆 改为 未审核不可用
                    Query<Car> query = carDao.createQuery().filter("verify", 3);
                    UpdateOperations<Car> updateOperations = carDao.createUpdateOperations().set("verify", 0);
                    carDao.update(query, updateOperations);
                }
            }

            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("请将表单填选完整！");
        }

    }

    @Override
    public ServerResponse deleteSwitch(SysSwitch sysSwitch) {
        ObjectId objectId = sysSwitch.getObjectId();
        SysSwitch has = this.getByPK(objectId);
        if (has != null) {
            this.delete(objectId);
            return ServerResponse.createSuccess();
        }
        return ServerResponse.createError();
    }

    Boolean checkSwitch(SysSwitch sysSwitch) {
        if (sysSwitch == null) return false;
        if (sysSwitch.getCode() == null) return false;
        switch (sysSwitch.getCode()) {
            case 0://客商开关，type必不能为空
                if (sysSwitch.getType() == null) return false;
                //type为3时(下单)，开关为是订单否分享能微信小程序，即share不能为空
                if (sysSwitch.getType() == 3 && sysSwitch.getShare() == null) return false;
                //type为4时(订单退款),开关为退款是否需审核，即check不能为空
                if (sysSwitch.getType() == 4 && sysSwitch.getCheck() == null) return false;
                break;
            case 1://司机开关，type必不能为空；
                if (sysSwitch.getType() == null) return false;
                //type为0,1时(司机app用户,司机微信小程序用户)，开关为用户是否需要审核和用户必填照片信息，即check和dvrPhotoTYpes不能为空
                if ((sysSwitch.getType() == 0 || sysSwitch.getType() == 1) && (sysSwitch.getCheck() == null || sysSwitch.getDvrPhotoTypes() == null))
                    return false;
                //type为3时(订单退款)，司机退款分为客商撤单退款和司机主动撤单退款，开关为退款是否需要审核，即check不能为空
                if (sysSwitch.getType() == 3 && (sysSwitch.getRefundType() == null || sysSwitch.getCheck() == null))
                    return false;
                break;
            case 2://车辆开关，车辆是否需要审核 和 车辆上传照片必填
                if (sysSwitch.getCheck() == null || sysSwitch.getCarPhotoTypes() == null) return false;
                break;
        }
        return true;
    }

    @Override
    public Map<String, String> getCarPhoto(Integer type) {
        if (StrUtil.isEmpty(type)) return null;
        Query<SysSwitch> query = this.createQuery();
        //query.filter("type", type);    //0-app,1-微信小程序
        query.filter("code", 2);    //code=2 车辆

        SysSwitch sysSwitch = this.get(query);

        Map<String, String> map = new HashMap<>();
        if (sysSwitch == null) {
            map.put("number", "7");
            map.put("value", "drivingPho1,drivingPho2,drivingPho3,roadTCPho,carIdentityPhoBef,carIdentityPhoBack,driverCarPho");
        } else {
            String value = "";
            int count = 0;
            List<CheckBoxData> checkBoxList = sysSwitch.getCarPhotoTypes();
            for (CheckBoxData checkBox : checkBoxList) {
                if (checkBox.getChecked()) {
                    value = value + checkBox.getId() + ",";
                    count += 1;
                }
            }
            if (StrUtil.isNotEmpty(value)) value = value.substring(0, value.length() - 1);
            map.put("number", String.valueOf(count));
            map.put("value", value);
        }
        return map;
    }

    /**
     * type:0-司机app完善信息时需要上传的照片数量
     * type:1-司机微信小程序完善信息时需要上传的照片数量
     */
    @Override
    public Map<String, String> getDvrPhoto(Integer type) {
        if (StrUtil.isEmpty(type)) return null;
        Query<SysSwitch> query = this.createQuery();
        query.filter("type", type);    //0-app,1-微信小程序
        query.filter("code", 1);    //code=1 司机

        SysSwitch sysSwitch = this.get(query);

        Map<String, String> map = new HashMap<>();
        if (sysSwitch == null) {
            map.put("number", "6");
            map.put("value", "driverPho,driverPho2,driIdentityPhoBef,driIdentityPhoBack,roadQCPho,bankCardPho");
        } else {
            String value = "";
            int count = 0;
            List<CheckBoxData> checkBoxList = sysSwitch.getDvrPhotoTypes();
            for (CheckBoxData checkBox : checkBoxList) {
                if (checkBox.getChecked()) {
                    value = value + checkBox.getId() + ",";
                    count += 1;
                }
            }
            if (StrUtil.isNotEmpty(value)) value = value.substring(0, value.length() - 1);
            map.put("number", String.valueOf(count));
            map.put("value", value);
        }
        return map;
    }

    /**
     * type:UserType.DU.toString() 司机 UserType.WECHAT.toString() 小程序
     */
    @Override
    public boolean isCheck(String type) {
        if (StrUtil.isEmpty(type)) return true;
        Query<SysSwitch> query = this.createQuery();

        if (type.equals(UserType.DU.toString())) {//0-app,1-微信小程序
            query.filter("type", 0);
        } else {
            query.filter("type", 1);
        }
        query.filter("code", 1);    //code=1 司机

        SysSwitch sysSwitch = this.get(query);
        if (sysSwitch == null || StrUtil.isEmpty(sysSwitch.getCheck())) return true;     //默认用户是需审核才能使用给的
        return sysSwitch.getCheck() != 1;
    }

    @Override
    public ServerResponse<List<CheckBoxData>> getDvrPhoTypes() {
        List<CheckBoxData> result = new ArrayList<>();

        CheckBoxData driverPho = new CheckBoxData("driverPho", "驾驶证照片");
        result.add(driverPho);
        CheckBoxData driverPho2 = new CheckBoxData("driverPho2", "驾驶证照片副页");
        result.add(driverPho2);
        CheckBoxData driIdentityPhoBef = new CheckBoxData("driIdentityPhoBef", "司机身份证照片正面");
        result.add(driIdentityPhoBef);
        CheckBoxData driIdentityPhoBack = new CheckBoxData("driIdentityPhoBack", "司机身份证照片反面");
        result.add(driIdentityPhoBack);
        CheckBoxData roadQCPho = new CheckBoxData("roadQCPho", "道路从业资格证照片");
        result.add(roadQCPho);
        CheckBoxData bankCardPho = new CheckBoxData("bankCardPho", "银行卡照片");
        result.add(bankCardPho);

        return ServerResponse.createSuccess(result);
    }

    @Override
    public ServerResponse<List<CheckBoxData>> getCarPhoTypes() {

        //,,,,,,
        List<CheckBoxData> result = new ArrayList<>();

        CheckBoxData drivingPho1 = new CheckBoxData("drivingPho1", "行驶证照片（正页）");
        result.add(drivingPho1);
        CheckBoxData drivingPho2 = new CheckBoxData("drivingPho2", "行驶证照片（副页正面）");
        result.add(drivingPho2);
        CheckBoxData drivingPho3 = new CheckBoxData("drivingPho3", "行驶证照片（副页反面）");
        result.add(drivingPho3);
        CheckBoxData roadTCPho = new CheckBoxData("roadTCPho", "车辆道路运输证");
        result.add(roadTCPho);
        CheckBoxData carIdentityPhoBef = new CheckBoxData("carIdentityPhoBef", "车主身份证正（单位证件）");
        result.add(carIdentityPhoBef);
        CheckBoxData carIdentityPhoBack = new CheckBoxData("carIdentityPhoBack", "车主身份证反（单位证件）");
        result.add(carIdentityPhoBack);
        CheckBoxData driverCarPho = new CheckBoxData("driverCarPho", "司机和车的合影照片");
        result.add(driverCarPho);

        return ServerResponse.createSuccess(result);
    }
}
