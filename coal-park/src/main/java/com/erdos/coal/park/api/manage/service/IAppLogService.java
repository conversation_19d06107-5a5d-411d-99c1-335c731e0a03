package com.erdos.coal.park.api.manage.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.entity.AppLog;
import dev.morphia.query.Query;

import java.util.List;

public interface IAppLogService extends IBaseMongoService<AppLog> {

    void writeLog(AppLog appLog);

    ServerResponse getGreaterThanOneSecondList(Integer page, String st);

    List<AppLog> getAppLogList(Query<AppLog> query);
}
