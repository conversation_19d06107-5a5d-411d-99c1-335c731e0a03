package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

import java.util.HashMap;
import java.util.Map;

@Entity(value = "t_shipment_order", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("shipmentPinId")})
})
public class ShipmentOrder extends BaseMongoInfo {
    private String shipmentPinId;       //oid
    private Integer bizType;            //20:采购运单 30: 销售运单
    private Integer dispatchType = 50;       //10:合同派车,20:计划派车,30:自建运单,40:货源接单,50:扫码接单  默认50
    private String offlineDispatchType; //线下派车方（采10，销20）
    private String deliveryId;          //线下业务id  填入billCode
    private String goodsClass = "2";          //业务类型:1.大宗，2.整车，默认1      为2时，装货数量 和 卸货数量非必填
    private String selfCode;            //运单编号(客户) 填入billCode, 注：重复上传该字段相同的数据，会覆盖前面上传的数据
    private String type;                //1.采购运单；2.销售运单，默认1; 业务类型为2(整车),非必填
    private String startProvinceCode;   //发货地省行政区划代码
    private String startCityCode;       //发货地市行政区划代码
    private String startCountyCode;     //发货地区县行政区划代码
    private String startStationName;    //出发站点 beginPoint
    private String endProvinceCode;     //收货地省行政区划代码
    private String endCityCode;         //收货地市行政区划代码
    private String endCountyCode;       //收货地区县行政区划代码
    private String endStationName;      //目的站点,否必填 endPoint
    private String goodsCategoryName = "煤炭及制品";   //货物类型 "煤炭及制品"
    private String goodsDetailName;     //货物名称 品种variety
    private String vehicleNo;           //车牌号
    private String driverPhone;         //司机电话
    private String driverName;          //司机姓名
    private String driverlDcardNo;      //司机身份证号
    private String chargeUnit = "元/吨";          //司机运费计价单位 元/吨
    private String transportPaymentType = "2";//司机运费支付方式 1、手机钱包2、银行卡 默认为2
    private String isAutomaticCalculation = "1";//是否自动计算运费 1是2.否，默认2  自动计算运费，则系统根据运价*结算数量自动计算运,无需自动计算运费则按照接口传入的实付司机运费及车队长运费作为运
    private String paymentMethod = "1";       //收款方式 1全额支付给司机,2全额支付给车队长,3部分支付给车队长 默认为1
    private String loadGrossTime;       //装货时间 销售运单type为2必填，业务类型为2整车必填 格式：yyyy-MM-dd  HH:mm:ss
    private String loadNetWeight;       //装货数量 销售运单type为2必填；业务类型为2整车，装货数量=卸货数量=结算数量，装货数量非必填         毛重减皮重
    private String unloadTareTime;      //卸货时间 (1)yyyy-MM-dd HH:mm:ss(2)卸货时间要大于装货时间;type为1采购运单必填；
    private String unloadNetWeight;     //卸货数量 (1) type为1采购运单，必填(2)业务类型为2整车装货数量=卸货数量=结算数量，卸货数量非必填    毛重减皮重
    private String freight = "0";         //运价
    private String errmsg;              //推送结果错误描述
    private String isPush;             //是否推送，1-不推送,0或空正常推送
    private String oid;                 //order 的oid

    public ShipmentOrder() {
    }

    public ShipmentOrder(String startCountyCode, String startStationName, String endCountyCode, String endStationName, String vehicleNo, String driverPhone, String driverName, String driverlDcardNo) {
        if (StrUtil.isNotEmpty(startCountyCode)) {
            this.startProvinceCode = startCountyCode.substring(0, 2) + "0000";
            this.startCityCode = startCountyCode.substring(0, 4) + "00";
            this.startCountyCode = startCountyCode;
        }
        if (StrUtil.isNotEmpty(startStationName)) this.startStationName = startStationName;
        if (StrUtil.isNotEmpty(endCountyCode)) {
            this.endProvinceCode = endCountyCode.substring(0, 2) + "0000";
            this.endCityCode = endCountyCode.substring(0, 4) + "00";
            this.endCountyCode = endCountyCode;
        }
        if (StrUtil.isNotEmpty(endStationName)) this.endStationName = endStationName;
        this.vehicleNo = vehicleNo;
        this.driverPhone = driverPhone;
        this.driverName = driverName;
        this.driverlDcardNo = driverlDcardNo;
    }

    public ShipmentOrder(String shipmentPinId, String startCountyCode, String startStationName, String endCountyCode, String endStationName, String vehicleNo, String driverPhone, String driverName, String driverlDcardNo) {
        this.shipmentPinId = shipmentPinId;
        if (StrUtil.isNotEmpty(startCountyCode)) {
            this.startProvinceCode = startCountyCode.substring(0, 2) + "0000";
            this.startCityCode = startCountyCode.substring(0, 4) + "00";
            this.startCountyCode = startCountyCode;
        }
        if (StrUtil.isNotEmpty(startStationName)) this.startStationName = startStationName;
        if (StrUtil.isNotEmpty(endCountyCode)) {
            this.endProvinceCode = endCountyCode.substring(0, 2) + "0000";
            this.endCityCode = endCountyCode.substring(0, 4) + "00";
            this.endCountyCode = endCountyCode;
        }
        if (StrUtil.isNotEmpty(endStationName)) this.endStationName = endStationName;
        this.vehicleNo = vehicleNo;
        this.driverPhone = driverPhone;
        this.driverName = driverName;
        this.driverlDcardNo = driverlDcardNo;
    }

    public Map<String, Object> getParamMap() {
        Map<String, Object> paramMap = new HashMap<>();

        paramMap.put("bizType", bizType);
        paramMap.put("chargeUnit", chargeUnit);
        paramMap.put("deliveryId", deliveryId);
        paramMap.put("dispatchType", dispatchType);
        paramMap.put("driverlDcardNo", driverlDcardNo);
        paramMap.put("driverName", driverName);
        paramMap.put("driverPhone", driverPhone);
        paramMap.put("endCityCode", endCityCode);
        paramMap.put("endCountyCode", endCountyCode);
        paramMap.put("endProvinceCode", endProvinceCode);
        paramMap.put("endStationName", endStationName);
        paramMap.put("goodsCategoryName", goodsCategoryName);
        paramMap.put("goodsClass", goodsClass);
        paramMap.put("goodsDetailName", goodsDetailName);
        paramMap.put("isAutomaticCalculation", isAutomaticCalculation);
        paramMap.put("offlineDispatchType", offlineDispatchType);
        paramMap.put("paymentMethod", paymentMethod);
        paramMap.put("selfCode", selfCode);
        //paramMap.put("shipmentPinId", shipmentPinId);
        paramMap.put("startCityCode", startCityCode);
        paramMap.put("startCountyCode", startCountyCode);
        paramMap.put("startProvinceCode", startProvinceCode);
        paramMap.put("startStationName", startStationName);
        paramMap.put("transportPaymentType", transportPaymentType);
        paramMap.put("type", type);
        paramMap.put("vehicleNo", vehicleNo);
        paramMap.put("loadGrossTime", loadGrossTime);
        paramMap.put("loadNetWeight", loadNetWeight);
        paramMap.put("unloadTareTime", unloadTareTime);
        paramMap.put("unloadNetWeight", unloadNetWeight);
        paramMap.put("freight", freight);

        return paramMap;
    }

    public String getShipmentPinId() {
        return shipmentPinId;
    }

    public void setShipmentPinId(String shipmentPinId) {
        this.shipmentPinId = shipmentPinId;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getDispatchType() {
        return dispatchType;
    }

    public void setDispatchType(Integer dispatchType) {
        this.dispatchType = dispatchType;
    }

    public String getOfflineDispatchType() {
        return offlineDispatchType;
    }

    public void setOfflineDispatchType(String offlineDispatchType) {
        this.offlineDispatchType = offlineDispatchType;
    }

    public String getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(String deliveryId) {
        this.deliveryId = deliveryId;
    }

    public String getGoodsClass() {
        return goodsClass;
    }

    public void setGoodsClass(String goodsClass) {
        this.goodsClass = goodsClass;
    }

    public String getSelfCode() {
        return selfCode;
    }

    public void setSelfCode(String selfCode) {
        this.selfCode = selfCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStartProvinceCode() {
        return startProvinceCode;
    }

    public void setStartProvinceCode(String startProvinceCode) {
        this.startProvinceCode = startProvinceCode;
    }

    public String getStartCityCode() {
        return startCityCode;
    }

    public void setStartCityCode(String startCityCode) {
        this.startCityCode = startCityCode;
    }

    public String getStartCountyCode() {
        return startCountyCode;
    }

    public void setStartCountyCode(String startCountyCode) {
        this.startCountyCode = startCountyCode;
    }

    public String getStartStationName() {
        return startStationName;
    }

    public void setStartStationName(String startStationName) {
        this.startStationName = startStationName;
    }

    public String getEndProvinceCode() {
        return endProvinceCode;
    }

    public void setEndProvinceCode(String endProvinceCode) {
        this.endProvinceCode = endProvinceCode;
    }

    public String getEndCityCode() {
        return endCityCode;
    }

    public void setEndCityCode(String endCityCode) {
        this.endCityCode = endCityCode;
    }

    public String getEndCountyCode() {
        return endCountyCode;
    }

    public void setEndCountyCode(String endCountyCode) {
        this.endCountyCode = endCountyCode;
    }

    public String getEndStationName() {
        return endStationName;
    }

    public void setEndStationName(String endStationName) {
        this.endStationName = endStationName;
    }

    public String getGoodsCategoryName() {
        return goodsCategoryName;
    }

    public void setGoodsCategoryName(String goodsCategoryName) {
        this.goodsCategoryName = goodsCategoryName;
    }

    public String getGoodsDetailName() {
        return goodsDetailName;
    }

    public void setGoodsDetailName(String goodsDetailName) {
        this.goodsDetailName = goodsDetailName;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverlDcardNo() {
        return driverlDcardNo;
    }

    public void setDriverlDcardNo(String driverlDcardNo) {
        this.driverlDcardNo = driverlDcardNo;
    }

    public String getChargeUnit() {
        return chargeUnit;
    }

    public void setChargeUnit(String chargeUnit) {
        this.chargeUnit = chargeUnit;
    }

    public String getTransportPaymentType() {
        return transportPaymentType;
    }

    public void setTransportPaymentType(String transportPaymentType) {
        this.transportPaymentType = transportPaymentType;
    }

    public String getIsAutomaticCalculation() {
        return isAutomaticCalculation;
    }

    public void setIsAutomaticCalculation(String isAutomaticCalculation) {
        this.isAutomaticCalculation = isAutomaticCalculation;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getLoadGrossTime() {
        return loadGrossTime;
    }

    public void setLoadGrossTime(String loadGrossTime) {
        this.loadGrossTime = loadGrossTime;
    }

    public String getLoadNetWeight() {
        return loadNetWeight;
    }

    public void setLoadNetWeight(String loadNetWeight) {
        this.loadNetWeight = loadNetWeight;
    }

    public String getUnloadTareTime() {
        return unloadTareTime;
    }

    public void setUnloadTareTime(String unloadTareTime) {
        this.unloadTareTime = unloadTareTime;
    }

    public String getUnloadNetWeight() {
        return unloadNetWeight;
    }

    public void setUnloadNetWeight(String unloadNetWeight) {
        this.unloadNetWeight = unloadNetWeight;
    }

    public String getFreight() {
        return freight;
    }

    public void setFreight(String freight) {
        this.freight = freight;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public String getIsPush() {
        return isPush;
    }

    public void setIsPush(String isPush) {
        this.isPush = isPush;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }
}
