package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import com.erdos.coal.park.api.customer.service.IPrepaidForDriverService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.PayAliPrepaid;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IPayAliPrepaidService;
import com.erdos.coal.park.api.driver.service.IPayAliResultService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.transaction.alipay.bean.AlipayConfig;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

@Service("prepaidForDriverService")
public class PrepaidForDriverServiceImpl implements IPrepaidForDriverService {
    @Resource
    private IPayAliPrepaidService payAliPrepaidService;
    @Resource
    private IPayAliResultService payAliResultService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private ILockedService lockedService;
    @Resource
    private AlipayConfig alipayConfig;

    @Override
    public ServerResponse<String> createAliOrder(String body, String subject, String totalAmount, String did) {
        String cid = ShiroUtils.getUserId();

        //判断订单金额不能为空.
        if (StrUtil.isEmpty(totalAmount) || new BigDecimal(totalAmount).compareTo(new BigDecimal(0)) == 0)
            return ServerResponse.createError("订单金额不能为空");

        DriverPool dp = driverPoolService.getByPK(did); //参数中的did实际是从客商司机池中查询所得，所以did是司机池的编号
        DriverInfo driverInfo = driverInfoService.getByPK(dp.getDid());
        if ((driverInfo == null) || (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)))
            return ServerResponse.createError("司机账户不可用！");

        PayAliPrepaid aliPrepaid = new PayAliPrepaid();
        aliPrepaid.setAppId(alipayConfig.APP_ID_CUSTOMER);

        //type=0-客商给司机充值
        //cusOrDri=0 表示客商
        return payAliPrepaidService.createAliOrder(cid, body, subject, totalAmount, did, 0, aliPrepaid, 0);
    }

    @Override
    public ServerResponse<String> checkPayResult(String jsonParam) {
        Map map = JSON.parseObject(jsonParam, Map.class);

        Map contentMap = (Map) map.get("alipay_trade_app_pay_response");
        String sign = (String) map.get("sign");
        String signType = (String) map.get("sign_type");

        contentMap.put("sign", sign);
        contentMap.put("sign_type", signType);
        PayAliPrepaid prepaid = payAliResultService.rsaCheckV1(contentMap);

        if (prepaid != null) {
            return ServerResponse.createSuccess("支付成功");
        } else {
            return ServerResponse.createError("支付异常");
        }
    }

    @Override
    public ServerResponse<String> searchPayResult(String outTradeNo, String tradeNo) {
        String cid = ShiroUtils.getUserId();

        //用户重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.ALI_PAY_RESULT.getType());
        if (!lock) {
            return ServerResponse.result(8, "系统忙，稍后重试！");
        } else {
            try {
                //判断交易订单是否存在
                PayAliPrepaid prepaid = payAliPrepaidService.get("outTradeNo", outTradeNo);
                if (ObjectUtil.isEmpty(prepaid)) return ServerResponse.createError("交易不存在");

                //cusOrDri=0 表示客商黑金宝app
                ServerResponse<String> response = payAliResultService.processAliPayInformation(cid, outTradeNo, tradeNo, prepaid, 0);
                return response;

            } finally {
                lockedService.unLock(cid, SysConstants.LockType.ALI_PAY_RESULT.getType());
            }
        }
    }

    @Override
    public ServerResponse<String> createAliOrder3(String body, String subject, String totalAmount) {
        String cid = ShiroUtils.getUserId();

        //判断订单金额不能为空.
        if (StrUtil.isEmpty(totalAmount) || new BigDecimal(totalAmount).compareTo(new BigDecimal(0)) == 0)
            return ServerResponse.createError("订单金额不能为空");

        PayAliPrepaid aliPrepaid = new PayAliPrepaid();
        aliPrepaid.setAppId(alipayConfig.APP_ID_CUSTOMER);

        //type=3-客商给自己充值
        //cusOrDri=0 表示客商
        return payAliPrepaidService.createAliOrder(cid, body, subject, totalAmount, cid, 3, aliPrepaid, 0);
    }
}
