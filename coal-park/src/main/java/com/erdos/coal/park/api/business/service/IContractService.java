package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.customer.entity.Order;

import java.util.List;
import java.util.Map;

public interface IContractService {
    //扫码下单-选择客户一级单位 接口
    ServerResponse<List<CompanyUnit>> getUnitCode();

    //扫码下单-选择合同 接口      (bizType-  销售1，采购0)
    ServerResponse<Object> getBizContractCode(String unitCode, Integer bizType);

    //扫码下单-选择商品 接口
    ServerResponse<Object> getVarietyCode(String unitCode, String bizContractCode);

    ServerResponse<Object> getVarietyCode2(String unitCode, String bizContractCode);

    //计划 选择一级单位后查询二级单位
    ServerResponse<Object> getSubUnitList(String unitCode);

    //获取抢运计划列表
    ServerResponse<Object> getRushPlanList(String unitCode, String defaultDownUnit, Integer bizType, Integer page);

    //计划抢运
    ServerResponse<Object> addRushPlan(String unitCode, String id, Integer expectCarCount, Boolean isTakeAll, String bizUnitCode);

    //计划提交 查询品种
    ServerResponse<Object> getVarietyList(String unitCode);

    //计划提交
    ServerResponse<Object> saveRushDetails(String unitCode, String defaultDownUnit, String bizUnitCode, String varietyCode, Integer bizType, Integer carCount, Double weightCount, Long planStartTime, Long planEndTime, Integer planLeadTime);

    //查询成功的计划抢运列表
    ServerResponse<Object> getRushDetailsList(String unitCode, String defaultDownUnit, String bizUnitCode, Integer bizType, Integer page);

    //查询成功的计划提交列表
    ServerResponse<Object> getRushDetailsFromRushPlan(String unitCode, String defaultDownUnit, String bizUnitCode, Integer bizType, Integer page);

    //查看品种合同车数毛皮净重损益检票时间，明细
    ServerResponse<Object> getInfoFromWorked(String unitCode, String subCode, Integer bizType, Long startTime, Long endTime, Integer page);

    //查看品种合同车数毛皮净重损益检票时间，统计
    ServerResponse<Object> getTotalInfoFromWorked(String unitCode, String subCode, Integer bizType, Long startTime, Long endTime);

    //查询业务单位
    ServerResponse<Object> getBizUnitCodeList(String unitCode, Integer bizType);

    //二级单位进场预约
    String saveRushDriver(String ip, String saSubCode, Order order, Long startTime, Long endTime);

    //司机查询预约排队情况
    ServerResponse<Object> getQueuingReservation(String unitCode, String billCode);

    //二级单位进场签到
    String saveLineCheckIn(String ip, String saSubCode, Order order, String longitude, String latitude);

    //查询企业系统二级单位 运往地信息
    ServerResponse<Object> getPlaceAreaInfo(String unitCode, String subCode, String spell);

    //****************************hdd*********************
    //b.车辆入网核验
    Map<String, Object> checkTruckExistV2(String licensePlate);

    //c.车辆实时定位
    String getCurrentPosition(String licensePlate);

    //d.车辆历史轨迹  //startTime\endTime yyyy-MM-dd HH:mm:ss
    String getHistoricalTrack(String licensePlate, String startTime, String endTime);
}
