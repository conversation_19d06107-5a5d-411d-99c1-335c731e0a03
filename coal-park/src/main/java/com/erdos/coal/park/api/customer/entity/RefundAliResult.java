package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

@Entity(value = "t_refund_ali_result", noClassnameStored = true)
public class RefundAliResult extends BaseMongoInfo {
    private String orderId;             //String  是 64  20190703110075000006530004756875 支付宝转账单据号，     查询失败不返回。
    private String payFundOrderId;   //String  否 64  20190801110070001506380000251556 支付宝支付资金流水号， 转账失败不返回。
    private String outBizNo;          //String  是 64  20190619000000001                 商户转账唯一订单号
    private String status;              //String  是 64   SUCCESS    转账单据状态。    可能出现的状态如下：SUCCESS：转账成功
    private String payDate;            //String  否      2013-01-01 08:08:08支付时间，格式为yyyy-MM-dd HH:mm:ss，转账失败不返回。

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPayFundOrderId() {
        return payFundOrderId;
    }

    public void setPayFundOrderId(String payFundOrderId) {
        this.payFundOrderId = payFundOrderId;
    }

    public String getOutBizNo() {
        return outBizNo;
    }

    public void setOutBizNo(String outBizNo) {
        this.outBizNo = outBizNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }
}
