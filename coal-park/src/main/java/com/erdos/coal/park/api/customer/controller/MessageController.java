package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.entity.CusMessage;
import com.erdos.coal.park.api.customer.service.IMesssageService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客商信息提示接口
 */
//"客商APP消息提示接口列表"
@RestController
@RequestMapping("/api/cus/mes")
public class MessageController extends BaseController {
    @Resource
    private IMesssageService messsageService;

    //消息提示列表查询
    @InvokeLog(description = "客商消息提示接口", printReturn = false) //日志
    @PostMapping(value = "/queryMes")
    public ServerResponse<EGridResult> queryMesHandler(
            @RequestParam(value = "type") Integer type,                    //"0-物流信息提示,1-下单信息提示"
            @RequestParam(value = "page", required = false) Integer page,  //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows   //"每页多少条"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
        //syreturn messsageService.queryMes(type, page, rows);
    }

    //消息提示列表查询
    @InvokeLog(description = "客商消息 (cusMsg) 接口", printReturn = false) //日志
    @PostMapping(value = "/cusMsg")
    public ServerResponse<EGridResult<CusMessage>> cusMsgHandler(
            @RequestParam(value = "type") Integer type,                    //"0-司机撤单消息"
            @RequestParam(value = "page", required = false) Integer page,  //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows   //"每页多少条"
    ) throws GlobalException {
        return messsageService.searchCusMsg(type, page, rows);
    }
}
