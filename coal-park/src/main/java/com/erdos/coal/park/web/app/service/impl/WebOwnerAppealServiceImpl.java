package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.api.manage.dao.IOwnerAppealDao;
import com.erdos.coal.park.api.manage.entity.OwnerAppeal;
import com.erdos.coal.park.web.app.service.IWebOwnerAppealService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Service("webOwnerAppealService")
public class WebOwnerAppealServiceImpl extends BaseMongoServiceImpl<OwnerAppeal, IOwnerAppealDao> implements IWebOwnerAppealService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        Query<OwnerAppeal> query = this.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("mobile"))) {
            query.filter("mobile", request.getParameter("mobile"));
        }
        if (!StrUtil.isEmpty(request.getParameter("carNum"))) {
            query.filter("carNum", request.getParameter("carNum"));
        }
        query.order(Sort.descending("createTime"));
        return this.findPage(page, rows, query);
    }

    @Override
    public ServerResponse<OwnerAppeal> edit() {
        OwnerAppeal appeal = this.get("id", request.getParameter("id"));
        String photo = fileInfoDao.readFile(appeal.getCardBef(), "t_owner_appeal");//车主身份证正面
        //       appeal.setCardBef(photo);
/*        if (!StrUtil.isEmpty(photo)) {
            appeal.setCardBef("/web" + photo);
        } else {
//            appeal.setCardBef("@/assets/images/photo.png");
            appeal.setCardBef("");
        }*/
        photo = fileInfoDao.readFile(appeal.getCardBack(), "t_owner_appeal");//车主身份证背面
        //       appeal.setCardBack(photo);
/*        if (!StrUtil.isEmpty(photo)) {
            appeal.setCardBack("/web" + photo);
        } else {
            appeal.setCardBack("@/assets/images/photo.png");
            appeal.setCardBack("");
        }*/
        photo = fileInfoDao.readFile(appeal.getOwnerCard(), "t_owner_appeal");//车主和身份证合照
        //      appeal.setOwnerCard(photo);
        /*if (!StrUtil.isEmpty(photo)) {
            appeal.setOwnerCard("/web" + photo);
        } else {
//            appeal.setOwnerCard("@/assets/images/photo.png");
            appeal.setOwnerCard("");
        }*/
        photo = fileInfoDao.readFile(appeal.getLicense(), "t_owner_appeal");//行驶证照片
        //       appeal.setLicense(photo);
        /*if (!StrUtil.isEmpty(photo)) {
            appeal.setLicense("/web" + photo);
        } else {
//            appeal.setLicense("@/assets/images/photo.png");
            appeal.setLicense("");
        }*/
        return ServerResponse.createSuccess(appeal);

    }

    @Override
    public ServerResponse saveCheck(OwnerAppeal ownerAppeal) {
        Query<OwnerAppeal> oaQuery = this.createQuery();
        oaQuery.filter("id", ownerAppeal.getId());
        oaQuery.filter("updateTime", ownerAppeal.getUpdateTime());
        this.update(oaQuery, this.createUpdateOperations().set("check", ownerAppeal.getCheck()));
        if (ownerAppeal.getCheck() == 1) {  //审核通过，司机表记录冻结
            if (ownerAppeal.getAppOrWx() == 0) {
                Query<DriverInfo> query = driverInfoService.createQuery().filter("carNum", ownerAppeal.getCarNum());
                UpdateOperations<DriverInfo> updateOperations = driverInfoService.createUpdateOperations().set("state", 2);
                driverInfoService.update(query, updateOperations);
            } else {
                Query<WechatDriverInfo> query = wechatDriverInfoService.createQuery();
                query.filter("carNum", ownerAppeal.getCarNum());//微信小程序与车牌号绑定的记录冻结
                query.filter("state", 1);
                UpdateOperations<WechatDriverInfo> updateOperations = wechatDriverInfoService.createUpdateOperations().set("state", 2);
                wechatDriverInfoService.update(query, updateOperations);
            }
        }
        return ServerResponse.createSuccess("保存成功");
    }
}
