package com.erdos.coal.park.web.app.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.core.base.mongo.entity.GeoPoint;
import dev.morphia.annotations.*;
import dev.morphia.geo.Geometry;

import java.util.Date;

@Entity(value = "t_hot_line", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("lineId")}, options = @IndexOptions(unique = true))
})
public class HotLine extends BaseMongoInfo {
    // 主键
    private String lineId;

    // 二级单位
    private String subCode;
    private String subName;

    private String name;        // 姓名
    private String mobile;      // 手机号
    private String online;        // 在线起始时间 HH:mm:ss
    private String offline;       // 离线起始时间 HH:mm:ss

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOnline() {
        return online;
    }

    public void setOnline(String online) {
        this.online = online;
    }

    public String getOffline() {
        return offline;
    }

    public void setOffline(String offline) {
        this.offline = offline;
    }
}
