package com.erdos.coal.park.api.driver.entity;

import com.alipay.api.domain.FundBillListEco;
import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.Date;
import java.util.List;

@Entity(value = "t_pay_ali_result", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("notify_id")}, options = @IndexOptions(unique = true))
})
/*
 * 支付宝支付完成后订单记录
 */
public class PayAliResult extends BaseMongoInfo {
    private String code;        //结果码
    private String msg;         //处理结果的描述，信息来自于code返回结果的描述	success

    private Date notify_time;               //通知时间。格式为yyyy-MM-dd HH:mm:ss	2015-14-27 15:45:58
    private String notify_type;             //通知类型	通知的类型	trade_status_sync
    //@Indexed(options = @IndexOptions(name = "_notify_id", unique = true, background = true))
    private String notify_id;               //通知校验ID
    private String app_id;                  //支付宝分配给开发者的应用Id
    private String version;                 //接口版本，固定为：1.0
    private String trade_no;                //支付宝交易凭证号
    private String out_trade_no;            //	原支付请求的商户订单号
    private String out_biz_no;              //商户业务ID，主要是退款通知中返回退款申请的流水号
    private String buyer_id;                //	买家支付宝账号对应的支付宝唯一用户号。以2088开头的纯16位数字
    private String buyer_logon_id;          //买家支付宝账号
    private String seller_id;               //卖家支付宝用户号
    private String seller_email;            //卖家支付宝账号
    private String trade_status;            //	交易目前所处的状态	TRADE_CLOSED

    private String total_amount;            //	订单金额	本次交易支付的订单金额，单位为人民币（元）
    private String receipt_amount;          //实收金额	商家在交易中实际收到的款项，单位为元
    private String invoice_amount;          //开票金额	用户在交易中支付的可开发票的金额
    private String buyer_pay_amount;        //付款金额	用户在交易中支付的金额
    private String point_amount;            //	集分宝金额	使用集分宝支付的金额
    private String refund_fee;              //总退款金额		退款通知中，返回总退款金额，单位为元，支持两位小数	2.58

    private String subject;             //订单标题
    private String body;                //商品描述
    private Date gmt_create;            //	交易创建时间。格式为yyyy-MM-dd HH:mm:ss	2015-04-27 15:45:57
    private Date gmt_payment;           //交易付款时间。格式为yyyy-MM-dd HH:mm:ss	2015-04-27 15:45:57
    private Date gmt_refund;            //	交易退款时间。格式为yyyy-MM-dd HH:mm:ss.S	2015-04-28 15:45:57.320
    private Date gmt_close;             //	交易结束时间。格式为yyyy-MM-dd HH:mm:ss	2015-04-29 15:45:57
    private List<FundBillListEco> fund_bill_list;      //资金明细信息说明。[{“amount”:“15.00”,“fundChannel”:“ALIPAYACCOUNT”}]，支付成功的各个渠道金额信息
    private String passback_params;     //回传参数,公共回传参数，如果请求时传递了该参数，则返回给商户时会在异步通知时将该参数原样返回。

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Date getNotify_time() {
        return notify_time;
    }

    public void setNotify_time(Date notify_time) {
        this.notify_time = notify_time;
    }

    public String getNotify_type() {
        return notify_type;
    }

    public void setNotify_type(String notify_type) {
        this.notify_type = notify_type;
    }

    public String getNotify_id() {
        return notify_id;
    }

    public void setNotify_id(String notify_id) {
        this.notify_id = notify_id;
    }

    public String getApp_id() {
        return app_id;
    }

    public void setApp_id(String app_id) {
        this.app_id = app_id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTrade_no() {
        return trade_no;
    }

    public void setTrade_no(String trade_no) {
        this.trade_no = trade_no;
    }

    public String getOut_trade_no() {
        return out_trade_no;
    }

    public void setOut_trade_no(String out_trade_no) {
        this.out_trade_no = out_trade_no;
    }

    public String getOut_biz_no() {
        return out_biz_no;
    }

    public void setOut_biz_no(String out_biz_no) {
        this.out_biz_no = out_biz_no;
    }

    public String getBuyer_id() {
        return buyer_id;
    }

    public void setBuyer_id(String buyer_id) {
        this.buyer_id = buyer_id;
    }

    public String getBuyer_logon_id() {
        return buyer_logon_id;
    }

    public void setBuyer_logon_id(String buyer_logon_id) {
        this.buyer_logon_id = buyer_logon_id;
    }

    public String getSeller_id() {
        return seller_id;
    }

    public void setSeller_id(String seller_id) {
        this.seller_id = seller_id;
    }

    public String getSeller_email() {
        return seller_email;
    }

    public void setSeller_email(String seller_email) {
        this.seller_email = seller_email;
    }

    public String getTrade_status() {
        return trade_status;
    }

    public void setTrade_status(String trade_status) {
        this.trade_status = trade_status;
    }

    public String getTotal_amount() {
        return total_amount;
    }

    public void setTotal_amount(String total_amount) {
        this.total_amount = total_amount;
    }

    public String getReceipt_amount() {
        return receipt_amount;
    }

    public void setReceipt_amount(String receipt_amount) {
        this.receipt_amount = receipt_amount;
    }

    public String getInvoice_amount() {
        return invoice_amount;
    }

    public void setInvoice_amount(String invoice_amount) {
        this.invoice_amount = invoice_amount;
    }

    public String getBuyer_pay_amount() {
        return buyer_pay_amount;
    }

    public void setBuyer_pay_amount(String buyer_pay_amount) {
        this.buyer_pay_amount = buyer_pay_amount;
    }

    public String getPoint_amount() {
        return point_amount;
    }

    public void setPoint_amount(String point_amount) {
        this.point_amount = point_amount;
    }

    public String getRefund_fee() {
        return refund_fee;
    }

    public void setRefund_fee(String refund_fee) {
        this.refund_fee = refund_fee;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Date getGmt_create() {
        return gmt_create;
    }

    public void setGmt_create(Date gmt_create) {
        this.gmt_create = gmt_create;
    }

    public Date getGmt_payment() {
        return gmt_payment;
    }

    public void setGmt_payment(Date gmt_payment) {
        this.gmt_payment = gmt_payment;
    }

    public Date getGmt_refund() {
        return gmt_refund;
    }

    public void setGmt_refund(Date gmt_refund) {
        this.gmt_refund = gmt_refund;
    }

    public Date getGmt_close() {
        return gmt_close;
    }

    public void setGmt_close(Date gmt_close) {
        this.gmt_close = gmt_close;
    }

    public List<FundBillListEco> getFund_bill_list() {
        return fund_bill_list;
    }

    public void setFund_bill_list(List<FundBillListEco> fund_bill_list) {
        this.fund_bill_list = fund_bill_list;
    }

    public String getPassback_params() {
        return passback_params;
    }

    public void setPassback_params(String passback_params) {
        this.passback_params = passback_params;
    }
}
