package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.sys.dao.ISysMenuDao;
import com.erdos.coal.park.web.sys.dao.ISysRoleDao;
import com.erdos.coal.park.web.sys.entity.SysMenu;
import com.erdos.coal.park.web.sys.entity.SysRole;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.park.web.sys.pojo.LoginMenu;
import com.erdos.coal.park.web.sys.service.ISysMenuService;
import com.erdos.coal.park.web.sys.service.ISysUserService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.TreeNodeUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("sysMenuService")
public class SysMenuServiceImpl extends BaseMongoServiceImpl<SysMenu, ISysMenuDao> implements ISysMenuService {

    @Resource
    private ISysRoleDao sysRoleDao;
    @Resource
    private ISysUserService sysUserService;

    @Override
    public List<SysMenu> loadMenuTree(String pid) {
        //SessionUser sessionUser = (SessionUser) session.getAttribute(Constant.CURRENT_USER_KEY);
        //return loadMenuTree(pid, sessionUser.isSuperAdmin());
        return loadMenuTree(pid, false);
    }

    @Override
    public List<SysMenu> loadMenuTree(String pid, boolean hasSysTree) {

        Query<SysMenu> query = this.createQuery();

        if (!hasSysTree) {
            query.filter("type", 1); //去除系统菜单
        }

        query.order(Sort.descending("type"), Sort.ascending("order"));

        List<SysMenu> list;

        // pid 为 null 时为根节点
        if (StrUtil.isEmpty(pid)) {
            //根节点 第一层
            query.and(
                    query.criteria("_parentId").equal(pid)
            );
            list = query.find().toList();
            return TreeNodeUtil.getFatherNode(list);
        } else {
            //单节点的树(包括根节点本身)
            //  query.or(
            //          query.criteria("id").equal(pid), //这样只能查询两层
            //          query.criteria("_parentId").equal(pid)
            //  );

            list = query.find().toList();
            List<SysMenu> menus = TreeNodeUtil.getFatherNode(list);
            for (int i = menus.size() - 1; i >= 0; i--) {
                if (!pid.equals(menus.get(i).getId())) {
                    menus.remove(i);
                }
            }
            //去掉一级
            List<SysMenu> listMenu = new ArrayList<>();
            for (SysMenu sysMenu : menus) {
                List<SysMenu> tmpList = sysMenu.getChildren();
                if (tmpList != null && tmpList.size() > 0) {
                    listMenu.addAll(tmpList);
                }
            }
            return listMenu;
        }
    }

    @Override
    public EGridResult loadTreeGrid(Integer page, Integer rows) {
        Query<SysMenu> query = this.createQuery().filter("type", 1); //只取功能模块的
        query.order(Sort.descending("type"), Sort.ascending("pId"), Sort.ascending("order"));
        EGridResult eGridResult = this.findPage(page, rows, query);

        return eGridResult;
    }

    @Override
    public SysMenu getTreeObject(String pid) {

//        if (!request.getAttribute(Constant.CURRENT_USER_TYPE_KEY).equals(SysConstants.UserType.UT_SYSTEM)) {
        ShiroUser selfUserEntity = ShiroUtils.getCurrentUser();
        //SelfUserEntity selfUserEntity = ShiroUtils.getUserInfo();
        //if (!selfUserEntity.getUserType().equals(SysConstants.UserType.UT_SYSTEM.toString())) {
        if (!selfUserEntity.getUserType().equals(UserType.WEB.toString())) {
            return null;
        }

        // 说明 :
        // 从数据库中加载数据，按规则生成树, 规则如下:
        // 1, 先虚拟一个树节点 root
        // 2, 遍历所有数据，把 pId 为空的挂载到 root 上为第一级节点
        // 3, 遍历所有数据，把 pId 为第一级节点的 id 的, 做相应挂载
        // 4, 以上用递归方式生成

        SysMenu root = this.get("pId", null);

        if (root != null) {

            Query<SysMenu> query = this.createQuery();

//            SysUser sysUser = (SysUser) request.getAttribute(Constant.CURRENT_USER_KEY);
            String objectId = selfUserEntity.getUserId();
//            SysUser sysUser = sysUserService.getByPK(objectId);
            SysUser sysUser = sysUserService.get("id", objectId);

            if (sysUser.getType() == 0) { //管理员用户
                // query.filter("type", 1);
            } else {
                query.filter("type", 0);//只取功能模块的
            }

            query.criteria("pId").notEqual(null);

            query.order(Sort.descending("type"), Sort.ascending("pId"), Sort.ascending("order"));

            List<SysMenu> list = this.list(query);

            root.setChildren(TreeNodeUtil.getFatherNode(list));

            return root;
        }
        return null;
    }

    boolean checkValue(SysMenu sysMenu) {
        if (sysMenu == null) return false;
        if (ObjectUtil.isEmpty(sysMenu.getText())) return false;
        return true;
    }

    @Override
    public ServerResponse appendMenu(SysMenu sysMenu) {
        if (checkValue(sysMenu)) {
            // 产生新的ID
            sysMenu.setId(Utils.getUUID());
            this.save(sysMenu);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数错误");
        }
    }

    @Override
    public ServerResponse editMenu(SysMenu sysMenu) {
        if (checkValue(sysMenu)) {
            if (!ObjectUtils.isEmpty(sysMenu.getId())) {
                Query<SysMenu> query = this.createQuery();
                UpdateOperations<SysMenu> updateOperations = this.createUpdateOperations();

                query.filter("id", sysMenu.getId());
                //query.filter("updateTime", sysMenu.getUpdateTime());

                updateOperations.set("text", sysMenu.getText());
                updateOperations.set("url", sysMenu.getUrl());

                this.update(query, updateOperations);

                return ServerResponse.createSuccess();
            }
        }
        return ServerResponse.createError("参数错误");
    }

    @Override
    public ServerResponse deleteMenu(String id) {
        SysMenu sysMenu = this.get("id", id);
        if (checkValue(sysMenu)) {
            if (!ObjectUtils.isEmpty(sysMenu.getId())) {

                List<SysMenu> list = this.list();
                List<String> willDeleted = recurrenceChildren(id, list);
                willDeleted.add(id); // 当前节点
                //this.delete("id", willDeleted); //删除子节点及当前节点

                Query<SysMenu> query = this.createQuery();
                query.filter("id in", willDeleted.toArray());
                this.delete(query);//删除子节点及当前节点
            }
        }
        return ServerResponse.createSuccess();
    }

    @Override
    public ServerResponse getLoginInfo() {

//        if (!request.getAttribute(Constant.CURRENT_USER_TYPE_KEY).equals(SysConstants.UserType.UT_SYSTEM)) {
        //SelfUserEntity selfUserEntity = ShiroUtils.getUserInfo();
        ShiroUser selfUserEntity = ShiroUtils.getCurrentUser();
        String userType = selfUserEntity.getUserType();
        //if (!userType.equals(SysConstants.UserType.UT_SYSTEM.toString())) {
        if (!userType.equals(UserType.WEB.toString())) {
            return null;
        }

        // 取当前用户
        String objectId = selfUserEntity.getUserId();
//        SysUser sysUser = sysUserService.getByPK(objectId);
        SysUser sysUser = sysUserService.get("id", objectId);
//        SysUser sysUser = (SysUser) request.getAttribute(Constant.CURRENT_USER_KEY);
        if (sysUser != null && sysUser.getRoles() != null) {
            // 取当前用户的角色列表
            Query<SysRole> roleQuery = sysRoleDao.createQuery();
            roleQuery.filter("id in", sysUser.getRoles());
            List<SysRole> roleList = sysRoleDao.list(roleQuery);

            //处理角色对应的菜单列表
            List<String> listMenuIds = new ArrayList<>();
            for (SysRole role : roleList) {
                for (String str : role.getFuncs()) {
                    if (!listMenuIds.contains(str)) listMenuIds.add(str);
                }
            }

            //加载有权限的菜单
            Query<SysMenu> queryMenu = this.createQuery();
            queryMenu.filter("id in", listMenuIds.toArray());
            queryMenu.and(
                    queryMenu.criteria("id").notEqual("1"), // 去掉根节点
                    queryMenu.criteria("enable").equal(1) // 并且可用
            );

            queryMenu.order(Sort.ascending("order"));

            List<SysMenu> list = this.list(queryMenu);

            List<LoginMenu> loginMenus = new ArrayList<>();

            // 把 List<SysMenu> 转化为 List<LoginMenu>
            for (SysMenu menu : list) {
                LoginMenu lm = new LoginMenu();
                lm.setId(menu.getId());
                lm.setPid(menu.get_parentId());
                if (menu.get_parentId().equals("1")) {
                    lm.setComponent("Layout");
                    lm.setPath("/" + list.indexOf(menu));
                } else {
                    lm.setComponent(menu.getUrl());
                    lm.setPath("/" + menu.getUrl());
                }
                LoginMenu.MetaObj meta = new LoginMenu.MetaObj();
                meta.setTitle(menu.getText());
                //meta.setIcon("");
                lm.setMeta(meta);

                loginMenus.add(lm);
            }

            loginMenus = TreeNodeUtil.getFatherLoginNode(loginMenus);

            Map<String, Object> map = new HashMap<>();
            map.put("name", sysUser.getLoginName());
            map.put("avatar", "");

            map.put("menus", loginMenus);

            return ServerResponse.createSuccess(map);
        }
        return ServerResponse.createError("读取登录用户信息失败"); // 可能: token 失效， 用户不存在， 没有分配角色
    }

    private List<String> recurrenceChildren(String pid, List<SysMenu> list) {
        List<String> ids = new ArrayList<>();
        for (SysMenu menu : list) {
            if (pid.equals(menu.get_parentId())) {
                ids.add(menu.getId());
                ids.addAll(recurrenceChildren(menu.getId(), list));
            }
        }
        return ids;
    }
}