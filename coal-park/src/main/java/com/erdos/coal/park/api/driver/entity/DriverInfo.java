package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

@Entity(value = "t_driver_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("mobile")}),
        @Index(fields = {@Field("carNum")})
})
public class DriverInfo extends BaseMongoInfo {
    private String address;     //身份证识别住址
    private String birthday;    //身份证识别生日
    private String identity;    //身份证号
    private String name;        //姓名
    //@Indexed(options = @IndexOptions(name = "mobile", background = true))
    private String mobile;      //电话号码
    //@Indexed(options = @IndexOptions(name = "_carNum", background = true))
    private String carNum;      //关联车牌
    private String drivingPho1;      //行驶证照片（第一页）
    private String drivingPho2;      //行驶证照片（第二页）
    private String drivingPho3;      //行驶证照片（第三页）
    private String driverPho;       //驾驶证照片（正页）
    private String driverPho2;       //驾驶证照片（副页）
    private String carIdentityPhoBef;     //车主身份证照片正面
    private String carIdentityPhoBack;     //车主身份证照片背面
    private String driIdentityPhoBef;       //司机身份证照片正面
    private String driIdentityPhoBack;      //司机身份证照片背面
    private String roadQCPho;    //道路从业资格证照片
    private String bankCardPho;      //司机银行卡照片
    private String driverCarPho;    //司机和车的合影照片

    private Integer state = 0;        //用戶状态 0：已提交个人信息未审核不可用 1：通过审核 2：冻结 3:审核未通过 4：未审核但可用
                                                //（2023Y09M18d新增） -1:未提交个人信息未审核不可用
    private String stateStr;          //用戶审核不可用原因
    private String phoneId;         //手机序列号
    private String abilityTag; //黑白名单标识
    private String deviceId;    //阿里云推送 设备号
    private String tradePwd;    //交易密码

    private String openid;  //微信号
    private String nickname;//微信昵称
    private String wxName;  //微信账户真实姓名

    private String wechatOpenid;    //微信小程序司机注册时的openid，即司机的初始openid

    private String carInfoId;   // 码表 车型id
    private String carType; //车型
    private Double capacity = 0.0;    //载重（kg） 识别行驶证照片
    private String axlesNumber; //车轴数

    private String payeeType;//支付宝账户类型。可取值：
    // 1、ALIPAY_USERID：支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成。
    // 2、ALIPAY_LOGONID：支付宝登录号，支持邮箱和手机号格式。
    private String payeeAccount;//支付宝账户名称。

    private Integer haveOrder = 0;  //司机当前是否有在运输的订单 0-无，1-有

    private String initialOpenid;   //用户初始注册时的微信openid

    private String zytOpenid;       // 智运通司机小程序openid
    private String zytInitialOpenid;// 智运通司机小程序初始openid

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getDrivingPho1() {
        return drivingPho1;
    }

    public void setDrivingPho1(String drivingPho1) {
        this.drivingPho1 = drivingPho1;
    }

    public String getDrivingPho2() {
        return drivingPho2;
    }

    public void setDrivingPho2(String drivingPho2) {
        this.drivingPho2 = drivingPho2;
    }

    public String getDrivingPho3() {
        return drivingPho3;
    }

    public void setDrivingPho3(String drivingPho3) {
        this.drivingPho3 = drivingPho3;
    }

    public String getDriverPho() {
        return driverPho;
    }

    public void setDriverPho(String driverPho) {
        this.driverPho = driverPho;
    }

    public String getCarIdentityPhoBef() {
        return carIdentityPhoBef;
    }

    public void setCarIdentityPhoBef(String carIdentityPhoBef) {
        this.carIdentityPhoBef = carIdentityPhoBef;
    }

    public String getCarIdentityPhoBack() {
        return carIdentityPhoBack;
    }

    public void setCarIdentityPhoBack(String carIdentityPhoBack) {
        this.carIdentityPhoBack = carIdentityPhoBack;
    }

    public String getDriIdentityPhoBef() {
        return driIdentityPhoBef;
    }

    public void setDriIdentityPhoBef(String driIdentityPhoBef) {
        this.driIdentityPhoBef = driIdentityPhoBef;
    }

    public String getDriIdentityPhoBack() {
        return driIdentityPhoBack;
    }

    public void setDriIdentityPhoBack(String driIdentityPhoBack) {
        this.driIdentityPhoBack = driIdentityPhoBack;
    }

    public String getDriverCarPho() {
        return driverCarPho;
    }

    public void setDriverCarPho(String driverCarPho) {
        this.driverCarPho = driverCarPho;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStateStr() {
        return stateStr;
    }

    public void setStateStr(String stateStr) {
        this.stateStr = stateStr;
    }

    public String getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(String phoneId) {
        this.phoneId = phoneId;
    }

    public String getAbilityTag() {
        return abilityTag;
    }

    public void setAbilityTag(String abilityTag) {
        this.abilityTag = abilityTag;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getTradePwd() {
        return tradePwd;
    }

    public void setTradePwd(String tradePwd) {
        this.tradePwd = tradePwd;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getWxName() {
        return wxName;
    }

    public void setWxName(String wxName) {
        this.wxName = wxName;
    }

    public String getCarInfoId() {
        return carInfoId;
    }

    public void setCarInfoId(String carInfoId) {
        this.carInfoId = carInfoId;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getAxlesNumber() {
        return axlesNumber;
    }

    public void setAxlesNumber(String axlesNumber) {
        this.axlesNumber = axlesNumber;
    }

    public String getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(String payeeType) {
        this.payeeType = payeeType;
    }

    public String getPayeeAccount() {
        return payeeAccount;
    }

    public void setPayeeAccount(String payeeAccount) {
        this.payeeAccount = payeeAccount;
    }

    public String getDriverPho2() {
        return driverPho2;
    }

    public void setDriverPho2(String driverPho2) {
        this.driverPho2 = driverPho2;
    }

    public String getRoadQCPho() {
        return roadQCPho;
    }

    public void setRoadQCPho(String roadQCPho) {
        this.roadQCPho = roadQCPho;
    }

    public String getBankCardPho() {
        return bankCardPho;
    }

    public void setBankCardPho(String bankCardPho) {
        this.bankCardPho = bankCardPho;
    }

    public Integer getHaveOrder() {
        return haveOrder;
    }

    public void setHaveOrder(Integer haveOrder) {
        this.haveOrder = haveOrder;
    }

    public String getInitialOpenid() {
        return initialOpenid;
    }

    public void setInitialOpenid(String initialOpenid) {
        this.initialOpenid = initialOpenid;
    }

    public String getZytOpenid() {
        return zytOpenid;
    }

    public void setZytOpenid(String zytOpenid) {
        this.zytOpenid = zytOpenid;
    }

    public String getZytInitialOpenid() {
        return zytInitialOpenid;
    }

    public void setZytInitialOpenid(String zytInitialOpenid) {
        this.zytInitialOpenid = zytInitialOpenid;
    }
}
