package com.erdos.coal.park.web.app.pojo;

import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.entity.TradeUnit;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class WebTradePriceData implements Serializable {
    private boolean allowLimit;      //额度限定
    private String bizContractCode; //业务合同编号
    private Integer bizType;        //业务类型
    private boolean checkContainDeduct;  //验收包含扣除
    private boolean checkPayLimit;       //验收结算限制
    private boolean checkPayLimitWithHold;//验收结算限制预扣
    private String defaultArea;     //默认场区
    private String defaultDownUnit; //限定二级单位
    private Double internalPrice;   //内部单价
    private Double limitAllow;      //限定额度
    private Double limitWeight;     //限定计量
    private Double makeupInternalPrice; //内部补差单价
    private Double makeupPayPrice;  //结算补差单价
    private Double makeupPublicPrice;   //外部补差单价
    private boolean onceMeter;           //单次计量
    private Double payPrice;        //结算单价
    private Double publicPrice;     //外部单价
    private boolean reOnceMeter;         //反向计量
    private String selfCode;            //自身代码
    private Integer status;              //状态
    private String subCode;         //二级单位编码
    private Double tradeAmount;         //交易额
    private Double tradeCount;          //交易量
    private String unitCode;        //单位编码
    private boolean useOppositePrice;    //使用对方单价
    private String variety;         //品种
    private boolean weightLimit;     //计量限定

    private Integer fee;                //平台收费（分）-- 自定义
    private BigDecimal bigFee;          //平台收费（分转为元展示)

    private Date createTime;
    private Long updateTime;

    private List<TradeUnit> tradeUnit;
    private List<TradeContract> tradeContract;

    private List<SysUnitData> sysUnit;

    public List<SysUnitData> getSysUnit() {
        return sysUnit;
    }

    public void setSysUnit(List<SysUnitData> sysUnit) {
        this.sysUnit = sysUnit;
    }

    public List<TradeUnit> getTradeUnit() {
        return tradeUnit;
    }

    public void setTradeUnit(List<TradeUnit> tradeUnit) {
        this.tradeUnit = tradeUnit;
    }

    public List<TradeContract> getTradeContract() {
        return tradeContract;
    }

    public void setTradeContract(List<TradeContract> tradeContract) {
        this.tradeContract = tradeContract;
    }

    public boolean isAllowLimit() {
        return allowLimit;
    }

    public void setAllowLimit(boolean allowLimit) {
        this.allowLimit = allowLimit;
    }

    public String getBizContractCode() {
        return bizContractCode;
    }

    public void setBizContractCode(String bizContractCode) {
        this.bizContractCode = bizContractCode;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public boolean isCheckContainDeduct() {
        return checkContainDeduct;
    }

    public void setCheckContainDeduct(boolean checkContainDeduct) {
        this.checkContainDeduct = checkContainDeduct;
    }

    public boolean isCheckPayLimit() {
        return checkPayLimit;
    }

    public void setCheckPayLimit(boolean checkPayLimit) {
        this.checkPayLimit = checkPayLimit;
    }

    public boolean isCheckPayLimitWithHold() {
        return checkPayLimitWithHold;
    }

    public void setCheckPayLimitWithHold(boolean checkPayLimitWithHold) {
        this.checkPayLimitWithHold = checkPayLimitWithHold;
    }

    public String getDefaultArea() {
        return defaultArea;
    }

    public void setDefaultArea(String defaultArea) {
        this.defaultArea = defaultArea;
    }

    public String getDefaultDownUnit() {
        return defaultDownUnit;
    }

    public void setDefaultDownUnit(String defaultDownUnit) {
        this.defaultDownUnit = defaultDownUnit;
    }

    public Double getInternalPrice() {
        return internalPrice;
    }

    public void setInternalPrice(Double internalPrice) {
        this.internalPrice = internalPrice;
    }

    public Double getLimitAllow() {
        return limitAllow;
    }

    public void setLimitAllow(Double limitAllow) {
        this.limitAllow = limitAllow;
    }

    public Double getLimitWeight() {
        return limitWeight;
    }

    public void setLimitWeight(Double limitWeight) {
        this.limitWeight = limitWeight;
    }

    public Double getMakeupInternalPrice() {
        return makeupInternalPrice;
    }

    public void setMakeupInternalPrice(Double makeupInternalPrice) {
        this.makeupInternalPrice = makeupInternalPrice;
    }

    public Double getMakeupPayPrice() {
        return makeupPayPrice;
    }

    public void setMakeupPayPrice(Double makeupPayPrice) {
        this.makeupPayPrice = makeupPayPrice;
    }

    public Double getMakeupPublicPrice() {
        return makeupPublicPrice;
    }

    public void setMakeupPublicPrice(Double makeupPublicPrice) {
        this.makeupPublicPrice = makeupPublicPrice;
    }

    public boolean isOnceMeter() {
        return onceMeter;
    }

    public void setOnceMeter(boolean onceMeter) {
        this.onceMeter = onceMeter;
    }

    public Double getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(Double payPrice) {
        this.payPrice = payPrice;
    }

    public Double getPublicPrice() {
        return publicPrice;
    }

    public void setPublicPrice(Double publicPrice) {
        this.publicPrice = publicPrice;
    }

    public boolean isReOnceMeter() {
        return reOnceMeter;
    }

    public void setReOnceMeter(boolean reOnceMeter) {
        this.reOnceMeter = reOnceMeter;
    }

    public String getSelfCode() {
        return selfCode;
    }

    public void setSelfCode(String selfCode) {
        this.selfCode = selfCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public Double getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(Double tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public Double getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Double tradeCount) {
        this.tradeCount = tradeCount;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public boolean isUseOppositePrice() {
        return useOppositePrice;
    }

    public void setUseOppositePrice(boolean useOppositePrice) {
        this.useOppositePrice = useOppositePrice;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public boolean isWeightLimit() {
        return weightLimit;
    }

    public void setWeightLimit(boolean weightLimit) {
        this.weightLimit = weightLimit;
    }

    public Integer getFee() {
        return fee;
    }

    public void setFee(Integer fee) {
        this.fee = fee;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getBigFee() {
        return bigFee;
    }

    public void setBigFee(BigDecimal bigFee) {
        this.bigFee = bigFee;
    }
}
