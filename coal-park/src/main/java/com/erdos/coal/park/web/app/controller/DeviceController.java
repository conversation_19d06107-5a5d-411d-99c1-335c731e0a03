package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.entity.DeviceInfo;
import com.erdos.coal.park.web.app.service.IDeviceService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/device")
public class DeviceController extends BaseController {

    @Resource
    private IDeviceService deviceService;

    @Resource
    private ISysUnitService sysUnitService;

    @PostMapping("/device_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(deviceService.deviceList(page, rows));
    }

    @PostMapping("/add_device")
    public ServerResponse addHandler(@RequestBody DeviceInfo deviceInfo) throws GlobalException {
        return deviceService.addDevice(deviceInfo);
    }

    @PostMapping("/edit_device")
    public ServerResponse editHandler(@RequestBody DeviceInfo deviceInfo) throws GlobalException {
        return deviceService.editDevice(deviceInfo);
    }

    @PostMapping("/del_device")
    public ServerResponse deleteHandler(@RequestBody DeviceInfo deviceInfo) throws GlobalException {
        return deviceService.deleteDevice(deviceInfo);
    }

    @PostMapping("/update_device")
    public ServerResponse updateDeviceHandler(@RequestBody DeviceInfo deviceInfo) throws GlobalException {
        return deviceService.updateDevice(deviceInfo);
    }

    //-----------------
    // 一级单位，二级单位，暂时写到这里，应该放到一级二级单位对应的接口中
    @PostMapping("/unit_list")
    public ServerResponse listHandler(Integer type, String pcode) throws GlobalException {
        return ServerResponse.createSuccess(sysUnitService.getSysUnitListByType(type, pcode));
    }
}
