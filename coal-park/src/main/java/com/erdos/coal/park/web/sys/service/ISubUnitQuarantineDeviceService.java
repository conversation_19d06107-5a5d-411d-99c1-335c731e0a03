package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SubUnitQuarantineDevice;

public interface ISubUnitQuarantineDeviceService extends IBaseMongoService<SubUnitQuarantineDevice> {

    // 二级单位 防疫终端设备列表（增删改）
    EGridResult loadQuarantineDeviceGrid(Integer page, Integer rows);

    ServerResponse<String> deleteQuarantineDevice();

    ServerResponse<String> addQuarantineDevice(SubUnitQuarantineDevice device);

    ServerResponse<String> editQuarantineDevice(SubUnitQuarantineDevice device);
}
