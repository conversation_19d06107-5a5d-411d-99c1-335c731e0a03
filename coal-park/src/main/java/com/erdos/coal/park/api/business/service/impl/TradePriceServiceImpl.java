package com.erdos.coal.park.api.business.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.dao.ITradePriceDao;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.entity.TradePrice;
import com.erdos.coal.park.api.business.entity.TradeUnit;
import com.erdos.coal.park.api.business.service.ITradeContractService;
import com.erdos.coal.park.api.business.service.ITradePriceService;
import com.erdos.coal.park.api.business.service.ITradeUnitService;
import com.erdos.coal.park.web.app.pojo.WebTradePriceData;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

@Service("tradePriceService")
public class TradePriceServiceImpl extends BaseMongoServiceImpl<TradePrice, ITradePriceDao> implements ITradePriceService {
    @Resource
    private ITradeUnitService tradeUnitService;
    @Resource
    private ITradeContractService tradeContractService;
    @Resource
    private HttpServletRequest request;

    @Override
    public boolean add(List<TradePrice> tradePrices) {
        try {
            this.save(tradePrices);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public List<Integer> edit(Map<Integer, TradePrice> updateTradePriceMap) {
        List<Integer> successIds = new ArrayList<>();
        for (Map.Entry<Integer, TradePrice> entry : updateTradePriceMap.entrySet()) {
            TradePrice tradePrice = entry.getValue();
            Query<TradePrice> query = this.createQuery();
            query.criteria("selfCode").equal(tradePrice.getSelfCode());
            UpdateOperations<TradePrice> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(tradePrice.getAllowLimit()))
                updateOperations.set("allowLimit", tradePrice.getAllowLimit());
            if (StrUtil.isNotEmpty(tradePrice.getBizContractCode()))
                updateOperations.set("bizContractCode", tradePrice.getBizContractCode());
            if (StrUtil.isNotEmpty(tradePrice.getBizType())) updateOperations.set("bizType", tradePrice.getBizType());
            if (StrUtil.isNotEmpty(tradePrice.getCheckContainDeduct()))
                updateOperations.set("checkContainDeduct", tradePrice.getCheckContainDeduct());
            if (StrUtil.isNotEmpty(tradePrice.getCheckPayLimit()))
                updateOperations.set("checkPayLimit", tradePrice.getCheckPayLimit());
            if (StrUtil.isNotEmpty(tradePrice.getCheckPayLimitWithHold()))
                updateOperations.set("checkPayLimitWithHold", tradePrice.getCheckPayLimitWithHold());
            if (StrUtil.isNotEmpty(tradePrice.getDefaultArea()))
                updateOperations.set("defaultArea", tradePrice.getDefaultArea());
            if (StrUtil.isNotEmpty(tradePrice.getDefaultDownUnit()))
                updateOperations.set("defaultDownUnit", tradePrice.getDefaultDownUnit());
            if (StrUtil.isNotEmpty(tradePrice.getInternalPrice()))
                updateOperations.set("internalPrice", tradePrice.getInternalPrice());
            if (StrUtil.isNotEmpty(tradePrice.getLimitAllow()))
                updateOperations.set("limitAllow", tradePrice.getLimitAllow());
            if (StrUtil.isNotEmpty(tradePrice.getLimitWeight()))
                updateOperations.set("limitWeight", tradePrice.getLimitWeight());
            if (StrUtil.isNotEmpty(tradePrice.getMakeupInternalPrice()))
                updateOperations.set("makeupInternalPrice", tradePrice.getMakeupInternalPrice());
            if (StrUtil.isNotEmpty(tradePrice.getMakeupPayPrice()))
                updateOperations.set("makeupPayPrice", tradePrice.getMakeupPayPrice());
            if (StrUtil.isNotEmpty(tradePrice.getMakeupPublicPrice()))
                updateOperations.set("makeupPublicPrice", tradePrice.getMakeupPublicPrice());
            if (StrUtil.isNotEmpty(tradePrice.getOnceMeter()))
                updateOperations.set("onceMeter", tradePrice.getOnceMeter());
            if (StrUtil.isNotEmpty(tradePrice.getPayPrice()))
                updateOperations.set("payPrice", tradePrice.getPayPrice());
            if (StrUtil.isNotEmpty(tradePrice.getPublicPrice()))
                updateOperations.set("publicPrice", tradePrice.getPublicPrice());
            if (StrUtil.isNotEmpty(tradePrice.getReOnceMeter()))
                updateOperations.set("reOnceMeter", tradePrice.getReOnceMeter());
            if (StrUtil.isNotEmpty(tradePrice.getStatus())) updateOperations.set("status", tradePrice.getStatus());
            if (StrUtil.isNotEmpty(tradePrice.getSubCode())) updateOperations.set("subCode", tradePrice.getSubCode());
            if (StrUtil.isNotEmpty(tradePrice.getTradeAmount()))
                updateOperations.set("tradeAmount", tradePrice.getTradeAmount());
            if (StrUtil.isNotEmpty(tradePrice.getTradeCount()))
                updateOperations.set("tradeCount", tradePrice.getTradeCount());
            if (StrUtil.isNotEmpty(tradePrice.getUnitCode()))
                updateOperations.set("unitCode", tradePrice.getUnitCode());
            if (StrUtil.isNotEmpty(tradePrice.getUseOppositePrice()))
                updateOperations.set("useOppositePrice", tradePrice.getUseOppositePrice());
            if (StrUtil.isNotEmpty(tradePrice.getVariety())) updateOperations.set("variety", tradePrice.getVariety());
            if (StrUtil.isNotEmpty(tradePrice.getWeightLimit()))
                updateOperations.set("weightLimit", tradePrice.getLimitWeight());
            TradePrice price = this.findAndModify(query, updateOperations);
            if (price != null) {
                successIds.add(entry.getKey() - 1);
                successIds.add(entry.getKey());
            }
        }
        return successIds;
    }

    @Override
    public boolean del(List<String> selfCodes) {
        try {
            Query<TradePrice> query = this.createQuery();
            query.criteria("selfCode").in(selfCodes);
            this.delete(query);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    private List<String> removeDuplicates(List<String> list) {
        Set<String> set = new HashSet<>(list);
        return new ArrayList<>(set);
    }

    @Override
    public EGridResult loadGrid(String unitCode, Integer mold, Integer page, Integer rows) {
        Query<TradePrice> query = this.createQuery();
        query.criteria("unitCode").equal(unitCode);
        if (StrUtil.isNotEmpty(mold) && 2 != mold) query.criteria("bizType").equal(mold);
        String bizUnitName = request.getParameter("bizUnitName");
        String selfCode = request.getParameter("selfCode");

        String bizContractName = request.getParameter("bizContractName");
        String variety = request.getParameter("variety");

        if (StrUtil.isNotEmpty(bizUnitName)) {
            Query<TradeUnit> tradeUnitQuery = tradeUnitService.createQuery();
            tradeUnitQuery.criteria("bizUnitName").contains(bizUnitName);
            tradeUnitQuery.order(Sort.ascending("bizUnitCode"));

            List<String> bizUnitCodes = new ArrayList<>();
            List<TradeUnit> tradeUnits = tradeUnitService.list(tradeUnitQuery);
            for (TradeUnit tradeUnit : tradeUnits) {
                bizUnitCodes.add(tradeUnit.getBizUnitCode());
            }

            Query<TradeContract> tradeContractQuery = tradeContractService.createQuery();
            tradeContractQuery.criteria("bizUnitCode").in(removeDuplicates(bizUnitCodes));
            tradeContractQuery.order(Sort.ascending("bizContractCode"));

            List<String> bizContractCodes = new ArrayList<>();
            List<TradeContract> tradeContracts = tradeContractService.list(tradeContractQuery);
            for (TradeContract tradeContract : tradeContracts) {
                bizContractCodes.add(tradeContract.getBizContractCode());
            }

            query.criteria("bizContractCode").in(removeDuplicates(bizContractCodes));
        }
        if (StrUtil.isNotEmpty(selfCode)) query.criteria("selfCode").equal(selfCode);

        if (StrUtil.isNotEmpty(bizContractName)) {
            Query<TradeContract> tradeContractQuery = tradeContractService.createQuery();
            tradeContractQuery.criteria("bizContractName").contains(bizContractName);
            tradeContractQuery.order(Sort.ascending("bizContractCode"));

            List<String> bizContractCodes = new ArrayList<>();
            List<TradeContract> tradeContracts = tradeContractService.list(tradeContractQuery);
            for (TradeContract tradeContract : tradeContracts) {
                bizContractCodes.add(tradeContract.getBizContractCode());
            }

            query.criteria("bizContractCode").in(removeDuplicates(bizContractCodes));
        }
        if (StrUtil.isNotEmpty(variety)) query.criteria("variety").equal(variety);

        AggregationPipeline pipeline = this.createAggregation();
        pipeline.lookup("t_app_trade_contract", "bizContractCode", "bizContractCode", "tradeContract")
                .lookup("t_app_trade_unit", "tradeContract.bizUnitCode", "bizUnitCode", "tradeUnit")
                .lookup("sys_unit", "defaultDownUnit", "code", "sysUnit")
                .match(query)
                .skip((page - 1) * rows)
                .limit(rows);

        Iterator<WebTradePriceData> iterator = pipeline.aggregate(WebTradePriceData.class);
        List<WebTradePriceData> result = new ArrayList<>();
        while (iterator.hasNext()) {
            WebTradePriceData data = iterator.next();
            data.setBigFee(BigDecimal.valueOf(data.getFee()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
            result.add(data);
        }

//        pipeline.sort(Sort.ascending("time1"), Sort.ascending("outDefaultDownUnit"));

        List<TradePrice> list = this.list(query);
        int total = list.size();
        return new EGridResult(total, result);

    }

    @Override
    public ServerResponse editTradePrice(WebTradePriceData webTradePriceData) {
        if (StrUtil.isNotEmpty(webTradePriceData.getBigFee()) && webTradePriceData.getBigFee().multiply(new BigDecimal(100)).compareTo(new BigDecimal(webTradePriceData.getFee())) != 0) {
            Query<TradePrice> query = this.createQuery();
            query.criteria("selfCode").equal(webTradePriceData.getSelfCode());
            UpdateOperations<TradePrice> updateOperations = this.createUpdateOperations();
            updateOperations.set("fee", webTradePriceData.getBigFee().multiply(new BigDecimal(100)).intValue());
            this.update(query, updateOperations);
        }
        return ServerResponse.createSuccess();
    }
}
