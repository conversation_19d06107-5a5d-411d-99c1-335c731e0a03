package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.pojo.CarData;
import com.erdos.coal.park.api.manage.pojo.WeChatData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface IWechatDriverInfoService extends IBaseMongoService<WechatDriverInfo> {
    //微信小程序授权
    ServerResponse<AccessToken> wechatUserLogin(String code);

    //微信小程序 jscode2session 接口，返回openid和unionid
    ServerResponse<WeChatData> wechatUserAuth(String code);

    //
    WechatDriverInfo findUserByName(String name);

    //返回当前客商用户权限列表
    List<String> findPermissions(String name);

    ServerResponse<String> uploadPho(MultipartFile uploadPho, String type);
    ServerResponse<String> uploadPhoPublicRead(MultipartFile uploadPho, String type);

    ServerResponse<String> updateDriverInfo2(String driverPho, String driverPho2, String driIdentityPhoBef, String driIdentityPhoBack, String driverCarPho, String roadQCPho, String bankCardPho);

    //司机保存车辆信息 微信小程序
    ServerResponse<String> saveDvrCar(String carNum, String carInfoId, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack, String driverCarPho);

    ServerResponse<String> saveDvrCar2(String carNum, String carInfoId, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack, String driverCarPho);
    ServerResponse<String> saveDvrCar3(String carNum, String carInfoId, Double tareWeight, String drivingPho1, String drivingPho2, String drivingPho3, String roadTCPho, String carIdentityPhoBef, String carIdentityPhoBack, String driverCarPho);

}
