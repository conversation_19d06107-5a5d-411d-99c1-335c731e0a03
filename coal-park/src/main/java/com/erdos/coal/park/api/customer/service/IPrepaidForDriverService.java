package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.common.ServerResponse;

public interface IPrepaidForDriverService {
    //2.请求商户服务端，获取签名后的订单信息-客商充值预下单
    ServerResponse<String> createAliOrder(String body, String subject, String totalAmount, String did);

    //9.同步支付结果返回客商服务端，验签，解析支付结果
    ServerResponse<String> checkPayResult(String jsonParam);

    //查询支付结果
    ServerResponse<String> searchPayResult(String outTradeNo, String tradeNo);

    //2.请求商户服务端，获取签名后的订单信息-客商账户充值
    ServerResponse<String> createAliOrder3(String body, String subject, String totalAmount);
}
