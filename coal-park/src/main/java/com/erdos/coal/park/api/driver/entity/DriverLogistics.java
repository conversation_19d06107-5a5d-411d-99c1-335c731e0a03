package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;
import dev.morphia.geo.Geometry;
import dev.morphia.utils.IndexDirection;
import dev.morphia.utils.IndexType;

@Entity(value = "t_driver_logistics", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field(value = "geometry",type = IndexType.GEO2DSPHERE)})
})
public class DriverLogistics extends BaseMongoInfo {

    private String longitude;   //经度
    private String latitude;    //纬度
    private Integer isTag;   //是否接单标识（0：未接单1：接单）

    //@Indexed(value = IndexDirection.GEO2DSPHERE, name = "_geometry", background = true)
    private Geometry geometry;
    //点（Point）,线（LineString）,多边形（Polygon）,多点（MultiPoint）,多线（MultiLineString）,多个多边形（MultiPolygon）,几何集合（GeometryCollection）

    @Reference(value = "driverID", idOnly = true, lazy = true, ignoreMissing = true)
    private DriverInfo driverInfo;

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Integer getIsTag() {
        return isTag;
    }

    public void setIsTag(Integer isTag) {
        this.isTag = isTag;
    }

    public Geometry getGeometry() {
        return geometry;
    }

    public void setGeometry(Geometry geometry) {
        this.geometry = geometry;
    }

    public DriverInfo getDriverInfo() {
        return driverInfo;
    }

    public void setDriverInfo(DriverInfo driverInfo) {
        this.driverInfo = driverInfo;
    }
}
