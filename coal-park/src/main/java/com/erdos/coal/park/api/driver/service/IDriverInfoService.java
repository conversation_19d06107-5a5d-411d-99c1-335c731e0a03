package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.pojo.CarData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface IDriverInfoService extends IBaseMongoService<DriverInfo> {
    //司机注册接口
    ServerResponse<AccessToken> driverUserReg(String phoneId, String mobile, String code, String deviceId);
    ServerResponse<AccessToken> driverUserReg2(String phoneId, String mobile, String code, String deviceId);

    //司机登录接口
    ServerResponse<AccessToken> driverUserLogin(String phoneId, String mobile, String code, String deviceId);
    ServerResponse<AccessToken> driverUserLogin2(String phoneId, String mobile, String code, String deviceId);

    // 检查司机手机号是否更换微信登录
    ServerResponse<Boolean> checkPoneId(String mobile, String phoneId);

    //完善司机信息
//    ServerResponse<String> updateDvrInfo(String name, String identity, MultipartFile drivingPho, MultipartFile driverPho, MultipartFile carIdentityPhoBef, MultipartFile carIdentityPhoBack, MultipartFile driIdentityPhoBef, MultipartFile driIdentityPhoBack, MultipartFile driverCarPho);
    ServerResponse<String> updateDvrInfo(MultipartFile driverPho, MultipartFile driverPho2, MultipartFile driIdentityPhoBef, MultipartFile driIdentityPhoBack, MultipartFile driverCarPho, MultipartFile roadQCPho, MultipartFile bankCardPho);

    //查询司机信息
    ServerResponse<DriverInfo> selectOne();

    //司机手机号更改  t_driver_info   mobile
    ServerResponse<String> updateMobile(String mobile, String code);

    //设置交易密码
    ServerResponse<String> tradePwd(String identity, String pwd, String confirmPwd);

    //按照身份证数组查询DriverInfo
//    List<DriverInfo> searchByIds(String[] ids);

    //
    DriverInfo findUserByName(String name);

    //返回当前客商用户权限列表
    List<String> findPermissions(String name);

    // 设置几张照片，是否需要审核
    ServerResponse<Map<String, String>> getSwitch();

    // 身份证号是否需要设置
    ServerResponse<Map<String, String>> getIdentity();

    // 车辆设置几张照片，是否需要审核
    ServerResponse<Map<String, String>> getCarSwitch();

    //司机保存车辆信息接口
    ServerResponse<String> saveDriverCar(String carNum, String carInfoId, MultipartFile drivingPho1, MultipartFile drivingPho2, MultipartFile drivingPho3, MultipartFile roadTCPho, MultipartFile carIdentityPhoBef, MultipartFile carIdentityPhoBack, MultipartFile driverCarPho);

    //查询司机关联车辆信息
    ServerResponse<List<CarData>> getDriverCars();

    //司机取消车辆关联
    ServerResponse<String> cancelToCar(String id);

    //司机选择正在使用车辆车牌
    ServerResponse<String> choseCar(String carNum);
    ServerResponse<String> choseCar2(String carNum);

    //清空设置的正在使用车牌号
    ServerResponse<String> cleanCarNum();

    //修改司机haveOrder字段，标志司机是否有运输中订单
    void updateHaveOrder(String did, Integer haveOrder);
    void updateHaveOrder(List<String> dids, Integer haveOrder);

    // 智运通司机小程序注册
    ServerResponse<String> zytDriverReg(String zytOpenid, String mobile, String code);
    // 智运通司机小程序登录
    ServerResponse<DriverInfo> zytDriverLogin(String zytOpenid, String mobile, String code);
    // 智运通司机小程序过度token
    ServerResponse<AccessToken> zytGetDvrToken(String zytOpenid, String mobile);

    // 智运通司机小程序查询已关联可用车辆列表
    ServerResponse<List<String>> zytSearchDvrCar();

    // 司机修改关联车辆的皮重信息
    ServerResponse<String> updateTareWeight(String dvrToCarId, Double tareWeight);

    // 判断司机车牌号是否完善皮重信息
    boolean checkCarTareWeight(String did, String carNum);

}
