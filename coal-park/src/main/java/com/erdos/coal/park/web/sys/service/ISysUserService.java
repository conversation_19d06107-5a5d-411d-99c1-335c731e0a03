package com.erdos.coal.park.web.sys.service;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysUser;

import java.util.List;
import java.util.Set;

public interface ISysUserService extends IBaseMongoService<SysUser> {

    EGridResult loadUserGrid(Integer page, Integer rows);

    ServerResponse addUser(SysUser sysUser);

    ServerResponse editUser(SysUser sysUser);

    ServerResponse deleteUser(SysUser sysUser);

    SysUser findUserByName(String name);

    List<String> findPermissions(String name);
}