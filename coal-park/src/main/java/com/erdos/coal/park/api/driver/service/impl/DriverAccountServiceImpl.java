package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.driver.dao.IDriverAccountDao;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import com.erdos.coal.park.api.driver.pojo.DriverAccountPipeline;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.bson.Document;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import static dev.morphia.aggregation.Group.*;

@Service("driverAccountService")
public class DriverAccountServiceImpl extends BaseMongoServiceImpl<DriverAccount, IDriverAccountDao> implements IDriverAccountService {

    public ServerResponse<BigDecimal> queryAccount() {
        String did = ShiroUtils.getUserId();
        BigDecimal availableFee = getAvailableFee(did);
        availableFee = availableFee.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);//转换为元

        return ServerResponse.createSuccess("查询成功", availableFee);
    }

    @Override
    public BigDecimal getAvailableFee(String did) {
        BigDecimal totalFee = new BigDecimal(0);
        Query<DriverAccount> query = this.createQuery();
        query.criteria("did").equal(did);
        query.criteria("type").notEqual("8");

        AggregationPipeline pipeline = this.createAggregation();
        pipeline.match(query);
        pipeline.group(
                id(grouping("did")),
                //求和
                grouping("totalFee", sum("totalFee"))
        );

        Iterator<DriverAccountPipeline> iterator = pipeline.aggregate(DriverAccountPipeline.class);

        while (iterator.hasNext()) {
            DriverAccountPipeline driverAccountPipeline = iterator.next();
            totalFee = driverAccountPipeline.getTotalFee();
        }
        return totalFee;
        /*Query<DriverAccount> query = this.createQuery();
        query.filter("did", did);
        query.criteria("type").notEqual(8);     //类型8属于司机直接付款接单，不属于司机在平台的可用余额
        query.order(Sort.descending("createTime"));
        List<DriverAccount> accountList = this.list(query);
        BigDecimal availableFee = new BigDecimal("0");
        for (DriverAccount account : accountList) {
            availableFee = availableFee.add(account.getTotalFee());//单位为分
        }
        return availableFee;*/
    }

    @Override
    public ServerResponse<String> saveFees(String did, Integer fees, String oid) {
        DriverAccount driverAccount = new DriverAccount();
        driverAccount.setType(3);//订单退款
        driverAccount.setDid(did);
        driverAccount.setCdid(did);
        driverAccount.setOid(oid);
        driverAccount.setTotalFee(new BigDecimal(fees));
        this.save(driverAccount);
        return ServerResponse.createSuccess(ResponseCode.SUCCESS.getDesc());
    }

    @Override
    public Document createDriAccountDoc(String did, String payerId, BigDecimal fee, Integer type, String oid, Date time) {
        DriverAccount driverAccount = new DriverAccount();
        driverAccount.setDid(did);
        driverAccount.setOid(oid);
        driverAccount.setType(type);
        if (payerId != null) driverAccount.setCdid(payerId);
        driverAccount.setUpdateTime(time.getTime());
        Document drvDocument = Document.parse(JSONObject.toJSONString(driverAccount));
        drvDocument.append("totalFee", fee);
        drvDocument.append("createTime", time);
        return drvDocument;
    }

    @Override
    public Document createDriAccountDoc(String did, String payerId, BigDecimal fee, Integer type, String outTradeNo, String transactionId, Date time) {
        DriverAccount driverAccount = new DriverAccount();
        driverAccount.setDid(did);
        driverAccount.setOutTradeNo(outTradeNo);
        driverAccount.setTransactionId(transactionId);
        driverAccount.setType(type);
        driverAccount.setCdid(payerId);
        driverAccount.setUpdateTime(time.getTime());
        Document drvDocument = Document.parse(JSONObject.toJSONString(driverAccount));
        drvDocument.append("totalFee", fee);
        drvDocument.append("createTime", time);
        return drvDocument;
    }

    @Override
    public Document createDriAccountDoc(String did, BigDecimal fee, Integer type, Date time, String preferentialRefundNo) {
        DriverAccount driverAccount = new DriverAccount();
        driverAccount.setDid(did);
        driverAccount.setType(type);
        driverAccount.setUpdateTime(time.getTime());
        driverAccount.setPreferentialRefundNo(preferentialRefundNo);
        Document drvDocument = Document.parse(JSONObject.toJSONString(driverAccount));
        drvDocument.append("totalFee", fee);
        drvDocument.append("createTime", time);
        return drvDocument;
    }

    @Override
    public ServerResponse<EGridResult> driverAccountList(Integer page, Integer rows) {
        String did = ShiroUtils.getUserId();

        Query<DriverAccount> query = this.createQuery();
        query.filter("did", did);
        query.order(Sort.descending("createTime"));
        EGridResult<DriverAccount> eGridResult = this.findPage(page, rows, query);

        List<DriverAccount> accountList = eGridResult.getRows();
        for (DriverAccount account : accountList) {
            //将账户中的金额 计算单位由 分 转化为 元
            account.setTotalFee(account.getTotalFee().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        }
        return ServerResponse.createSuccess("查询成功", new EGridResult(eGridResult.getTotal(), accountList));
    }
}
