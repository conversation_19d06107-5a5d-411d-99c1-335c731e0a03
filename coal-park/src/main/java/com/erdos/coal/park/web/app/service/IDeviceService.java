package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.app.entity.DeviceInfo;

import java.util.List;

public interface IDeviceService extends IBaseMongoService<DeviceInfo> {

    // 分页
    EGridResult deviceList(Integer page, Integer rows);

    // 增加
    ServerResponse addDevice(DeviceInfo deviceInfo);

    // 编辑
    ServerResponse editDevice(DeviceInfo deviceInfo);

    // 删除
    ServerResponse deleteDevice(DeviceInfo deviceInfo);

    // 注册
    ServerResponse updateDevice(DeviceInfo deviceInfo);

    //app端维护设备经纬度时，按单位编号查询设备列表
    ServerResponse<List<DeviceInfo>> searchDevice(String unitCode, String subCode, String deviceCode);

    //保存app端上传的 设备经纬度
    ServerResponse<String> saveDeviceGeo(String deviceCode, String longitude, String latitude);
}
