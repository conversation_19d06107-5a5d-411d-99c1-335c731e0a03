package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.service.IBusGoodsService;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.WXsDvrGoods;
import com.erdos.coal.park.api.customer.pojo.GoodsInUnitData;
import com.erdos.coal.park.api.customer.pojo.GoodsVerbInfoData;
import com.erdos.coal.park.api.customer.service.IGoodsService;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//"客商APP货运信息管理接口列表"
@RestController
@RequestMapping("/api/cus/goods")
public class GoodsController extends BaseController {
    @Resource
    private IGoodsService goodsService;
    @Resource
    private IBusGoodsService busGoodsService;

    @InvokeLog(description = "添加货运信息 接口") //日志
    @PostMapping(value = "/add_goods6")
    public ServerResponse<Object> addGoods6Handler(
            @RequestParam(value = "outUnitName", required = false) String outUnitName,                 //"发货的一级单位名称"
            @RequestParam(value = "outUnitCode", required = false) String outUnitCode,                 //"发货的一级单位编码"
            @RequestParam(value = "outBizContractName", required = false) String outBizContractName,   //"发货的合同名称"
            @RequestParam(value = "outBizContractCode", required = false) String outBizContractCode,   //"发货的合同编码"
            @RequestParam(value = "outVariety", required = false) String outVariety,                   //"发货的商品名称"
            @RequestParam(value = "outSubName", required = false) String outSubName,                   //"发货的二级单位名称"
            @RequestParam(value = "outDefaultDownUnit", required = false) String outDefaultDownUnit,   //"发货的二级单位编码"
            @RequestParam(value = "outAreaName", required = false) String outAreaName,                 //"发货的默认场区名称"
            @RequestParam(value = "outDefaultArea", required = false) String outDefaultArea,           //"发货的默认场区编码"
            @RequestParam(value = "outMin", required = false) Integer outMin,                          //"发货的最小票号"
            @RequestParam(value = "outMax", required = false) Integer outMax,                          //"发货的最大票号"

            @RequestParam(value = "inUnitName", required = false) String inUnitName,                   //"收货的一级单位名称"
            @RequestParam(value = "inUnitCode", required = false) String inUnitCode,                   //"收货的一级单位编码"
            @RequestParam(value = "inBizContractName", required = false) String inBizContractName,     //"收货的合同名称"
            @RequestParam(value = "inBizContractCode", required = false) String inBizContractCode,     //"收货的合同编码"
            @RequestParam(value = "inVariety", required = false) String inVariety,                     //"收货的商品名称"
            @RequestParam(value = "inSubName", required = false) String inSubName,                     //"收货的二级单位名称"
            @RequestParam(value = "inDefaultDownUnit", required = false) String inDefaultDownUnit,     //"收货的二级单位编码"
            @RequestParam(value = "inAreaName", required = false) String inAreaName,                   //"收货的默认场区名称"
            @RequestParam(value = "inDefaultArea", required = false) String inDefaultArea,             //"收货的默认场区编码"
            @RequestParam(value = "inMin", required = false) Integer inMin,                            //"收货的最小票号"
            @RequestParam(value = "inMax", required = false) Integer inMax,                            //"收货的最大票号"

            @RequestParam(value = "tradeName") String tradeName,           //"商品名称"
            @RequestParam(value = "mold") Integer mold,                    //"物流模式(0-发货物流，1-收货物流，2-收发货物流)"
            @RequestParam(value = "type") Integer type,                    //"物流信息类型(0-指定物流信息，1-面议)"
            @RequestParam(value = "pType", required = false) Integer pType,//"下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单"

            @RequestParam(value = "beginPoint", required = false) String beginPoint,           //"起点"
            @RequestParam(value = "endPoint", required = false) String endPoint,               //"终点"
            @RequestParam(value = "total", required = false) Integer total,                    //"车数"
            @RequestParam(value = "price", required = false) Double price,                     //"运输单价"
            @RequestParam(value = "tolls", required = false) Double tolls,                     //"预估过路费"
            @RequestParam(value = "distance", required = false) Double distance,               //"总里程（公里）"
            @RequestParam(value = "designCarNum", required = false) String designCarNum,       //"指定车牌号"
            @RequestParam(value = "designDriverId", required = false) String designDriverId,   //"指定车牌号的司机"
            @RequestParam(value = "longitude", required = false) String longitude,             //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,               //"纬度"
            @RequestParam(value = "weight", required = false) Double weight,                   //"载重量要求"

            @RequestParam(value = "isPay") Integer isPay,   //"客商是否代付(0-不代付，1-代付)"
            @RequestParam(value = "spell", required = false) String spell,   //运往地编码
            @RequestParam(value = "place", required = false) String place,   //运往地名称
            @RequestParam(value = "outTradingUnit", required = false) String outTradingUnit,   //交易单位编号
            @RequestParam(value = "outTradingUnitName", required = false) String outTradingUnitName,   //交易单位名称
            @RequestParam(value = "inTradingUnit", required = false) String inTradingUnit,   //交易单位编号
            @RequestParam(value = "inTradingUnitName", required = false) String inTradingUnitName,  //交易单位名称

            @RequestParam(value = "outCusDesc", required = false) String outCusDesc,             //"客商描述订单"
            @RequestParam(value = "inCusDesc", required = false) String inCusDesc,              //"客商描述订单"

            @RequestParam(value = "mold2") Integer mold2,                    //"是否往货业务(1-是，0-否)"
            @RequestParam(value = "beginDistrictCode", required = false) String beginDistrictCode,           //"起点区划编码"
            @RequestParam(value = "endDistrictCode", required = false) String endDistrictCode,               //"终点区划编码"

            @RequestParam(value = "productID", required = false) String productID               //品种code-智慧能源需要的 合同备案中的煤种ID
    ) throws GlobalException {
        //  鄂绒集团 - 下单车数不能超过10车
        /*if ("0010011427".equals(outUnitCode) || "0010011427".equals(inUnitCode)) {
            if (total>10) return ServerResponse.createError("车数不能超过10车");
        }*/

        return ServerResponse.createSuccess("接口停用");
        /*Goods goods = new Goods();
        goods.setOutUnitName(outUnitName);
        goods.setOutUnitCode(outUnitCode);
        goods.setOutBizContractName(outBizContractName);
        goods.setOutBizContractCode(outBizContractCode);
        goods.setOutVariety(outVariety);
        goods.setOutSubName(outSubName);
        goods.setOutDefaultDownUnit(outDefaultDownUnit);
        goods.setOutAreaName(outAreaName);
        goods.setOutDefaultArea(outDefaultArea);
        goods.setOutMin(outMin);
        goods.setOutMax(outMax);
        goods.setInUnitName(inUnitName);
        goods.setInUnitCode(inUnitCode);
        goods.setInBizContractName(inBizContractName);
        goods.setInBizContractCode(inBizContractCode);
        goods.setInVariety(inVariety);
        goods.setInSubName(inSubName);
        goods.setInDefaultDownUnit(inDefaultDownUnit);
        goods.setInAreaName(inAreaName);
        goods.setInDefaultArea(inDefaultArea);
        goods.setInMin(inMin);
        goods.setInMax(inMax);
        goods.setTradeName(tradeName);
        goods.setMold(mold);
        goods.setBeginPoint(type == 1 ? "面议" : beginPoint);
        goods.setEndPoint(type == 1 ? "面议" : endPoint);
        //goods.setTotal(type == 1 || StrUtil.isEmpty(total) ? 1 : total);//面议时默认是1车
        goods.setTotal(StrUtil.isEmpty(total) ? 1 : total);//面议时也可以下多车的单
        // if (total > 100) return ServerResponse.createError("车数太多，不能超过100");

        String msg = goodsService.queryUnitGoodsTotal(outUnitCode, inUnitCode, goods.getTotal());
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        goods.setPrice(price);
        goods.setTolls(tolls);
        goods.setDistance(distance);
        goods.setWeight(weight);
        goods.setIsWeChat(0);
        goods.setShare(0);
        goods.setpType(pType);
        goods.setLongitude(longitude);
        goods.setLatitude(latitude);
        goods.setSpell(spell);

        goods.setPlace(place);
        goods.setIsPay(isPay);
        if (pType == 1) {    //指定车号下单
            if (StrUtil.isEmpty(designCarNum) || StrUtil.isEmpty(designDriverId))
                return ServerResponse.createError("请指定司机和车号");
            goods.setCarNum(designCarNum);
            goods.setDriverId(designDriverId);
            goods.setOrderNum1(1);
        } else {
            if (StrUtil.isNotEmpty(designCarNum)) return ServerResponse.createError("下单类型错误");
            goods.setOrderNum0(StrUtil.isEmpty(total) ? 1 : total);
        }

        goods.setOutTradingUnit(outTradingUnit);
        goods.setOutTradingUnitName(outTradingUnitName);
        goods.setInTradingUnit(inTradingUnit);
        goods.setInTradingUnitName(inTradingUnitName);

        goods.setOutCusDesc(outCusDesc);
        goods.setInCusDesc(inCusDesc);

        goods.setMold2(mold2);
        goods.setBeginDistrictCode(beginDistrictCode);
        goods.setEndDistrictCode(endDistrictCode);
        if (StrUtil.isNotEmpty(productID) && !"null".equals(productID)) goods.setOutVarietyCode(productID);
        return goodsService.addGoods6(goods, type, designCarNum, designDriverId, longitude, latitude, isPay);*/
    }

    @InvokeLog(description = "添加货运信息 接口") //日志
    @PostMapping(value = "/add_goods7")
    public ServerResponse<Object> addGoods7Handler(
            @RequestParam(value = "outUnitName", required = false) String outUnitName,                 //"发货的一级单位名称"
            @RequestParam(value = "outUnitCode", required = false) String outUnitCode,                 //"发货的一级单位编码"
            @RequestParam(value = "outBizContractName", required = false) String outBizContractName,   //"发货的合同名称"
            @RequestParam(value = "outBizContractCode", required = false) String outBizContractCode,   //"发货的合同编码"
            @RequestParam(value = "outVariety", required = false) String outVariety,                   //"发货的商品名称"
            @RequestParam(value = "outSubName", required = false) String outSubName,                   //"发货的二级单位名称"
            @RequestParam(value = "outDefaultDownUnit", required = false) String outDefaultDownUnit,   //"发货的二级单位编码"
            @RequestParam(value = "outAreaName", required = false) String outAreaName,                 //"发货的默认场区名称"
            @RequestParam(value = "outDefaultArea", required = false) String outDefaultArea,           //"发货的默认场区编码"
            @RequestParam(value = "outMin", required = false) Integer outMin,                          //"发货的最小票号"
            @RequestParam(value = "outMax", required = false) Integer outMax,                          //"发货的最大票号"

            @RequestParam(value = "inUnitName", required = false) String inUnitName,                   //"收货的一级单位名称"
            @RequestParam(value = "inUnitCode", required = false) String inUnitCode,                   //"收货的一级单位编码"
            @RequestParam(value = "inBizContractName", required = false) String inBizContractName,     //"收货的合同名称"
            @RequestParam(value = "inBizContractCode", required = false) String inBizContractCode,     //"收货的合同编码"
            @RequestParam(value = "inVariety", required = false) String inVariety,                     //"收货的商品名称"
            @RequestParam(value = "inSubName", required = false) String inSubName,                     //"收货的二级单位名称"
            @RequestParam(value = "inDefaultDownUnit", required = false) String inDefaultDownUnit,     //"收货的二级单位编码"
            @RequestParam(value = "inAreaName", required = false) String inAreaName,                   //"收货的默认场区名称"
            @RequestParam(value = "inDefaultArea", required = false) String inDefaultArea,             //"收货的默认场区编码"
            @RequestParam(value = "inMin", required = false) Integer inMin,                            //"收货的最小票号"
            @RequestParam(value = "inMax", required = false) Integer inMax,                            //"收货的最大票号"

            @RequestParam(value = "tradeName") String tradeName,           //"商品名称"
            @RequestParam(value = "mold") Integer mold,                    //"物流模式(0-发货物流，1-收货物流，2-收发货物流)"
            @RequestParam(value = "type") Integer type,                    //"物流信息类型(0-指定物流信息，1-面议)"
            @RequestParam(value = "pType", required = false) Integer pType,//"下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单"

            @RequestParam(value = "beginPoint", required = false) String beginPoint,           //"起点"
            @RequestParam(value = "endPoint", required = false) String endPoint,               //"终点"
            @RequestParam(value = "total", required = false) Integer total,                    //"车数"
            @RequestParam(value = "price", required = false) Double price,                     //"运输单价"
            @RequestParam(value = "tolls", required = false) Double tolls,                     //"预估过路费"
            @RequestParam(value = "distance", required = false) Double distance,               //"总里程（公里）"
            @RequestParam(value = "designCarNum", required = false) String designCarNum,       //"指定车牌号"
            @RequestParam(value = "designDriverId", required = false) String designDriverId,   //"指定车牌号的司机"
            @RequestParam(value = "longitude", required = false) String longitude,             //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,               //"纬度"
            @RequestParam(value = "weight", required = false) Double weight,                   //"载重量要求"

            @RequestParam(value = "isPay") Integer isPay,   //"客商是否代付(0-不代付，1-代付)"
            @RequestParam(value = "spell", required = false) String spell,   //运往地编码
            @RequestParam(value = "place", required = false) String place,   //运往地名称
            @RequestParam(value = "outTradingUnit", required = false) String outTradingUnit,   //交易单位编号
            @RequestParam(value = "outTradingUnitName", required = false) String outTradingUnitName,   //交易单位名称
            @RequestParam(value = "inTradingUnit", required = false) String inTradingUnit,   //交易单位编号
            @RequestParam(value = "inTradingUnitName", required = false) String inTradingUnitName,  //交易单位名称

            @RequestParam(value = "outCusDesc", required = false) String outCusDesc,             //"客商描述订单"
            @RequestParam(value = "inCusDesc", required = false) String inCusDesc,              //"客商描述订单"

            @RequestParam(value = "mold2") Integer mold2,                    //"是否往货业务(1-是，0-否)"
            @RequestParam(value = "beginDistrictCode", required = false) String beginDistrictCode,           //"起点区划编码"
            @RequestParam(value = "endDistrictCode", required = false) String endDistrictCode,               //"终点区划编码"

            @RequestParam(value = "productID", required = false) String productID,               //品种code-智慧能源需要的 合同备案中的煤种ID
            @RequestParam(value = "inNetWeight", required = false) Double inNetWeight,               //收货，二级单位配置需要录入对方的净重(单车净重)
            @RequestParam(value = "loadPound", required = false) String loadPound,               //收货，二级单位配置需要录入对方的装货单号
            @RequestParam(value = "loadPoundPho", required = false) String loadPoundPho,               //收货，二级单位配置需要上传对方的装货单照片
            @RequestParam(value = "loadTime", required = false) Long loadTime               //收货，二级单位配置需要录入对方的装货时间
    ) throws GlobalException {
        //  鄂绒集团 - 下单车数不能超过10车
        /*if ("0010011427".equals(outUnitCode) || "0010011427".equals(inUnitCode)) {
            if (total>10) return ServerResponse.createError("车数不能超过10车");
        }*/

        //2025-03-10 当物流模式为收发货时，判断二级单位是否相同，相同则不可下单
        if (mold == 2 && outDefaultDownUnit.equals(inDefaultDownUnit)) return ServerResponse.createError("收发货二级单位不能相同");

        Goods goods = new Goods();
        goods.setOutUnitName(outUnitName);
        goods.setOutUnitCode(outUnitCode);
        goods.setOutBizContractName(outBizContractName);
        goods.setOutBizContractCode(outBizContractCode);
        goods.setOutVariety(outVariety);
        goods.setOutSubName(outSubName);
        goods.setOutDefaultDownUnit(outDefaultDownUnit);
        goods.setOutAreaName(outAreaName);
        goods.setOutDefaultArea(outDefaultArea);
        goods.setOutMin(outMin);
        goods.setOutMax(outMax);
        goods.setInUnitName(inUnitName);
        goods.setInUnitCode(inUnitCode);
        goods.setInBizContractName(inBizContractName);
        goods.setInBizContractCode(inBizContractCode);
        goods.setInVariety(inVariety);
        goods.setInSubName(inSubName);
        goods.setInDefaultDownUnit(inDefaultDownUnit);
        goods.setInAreaName(inAreaName);
        goods.setInDefaultArea(inDefaultArea);
        goods.setInMin(inMin);
        goods.setInMax(inMax);
        goods.setTradeName(tradeName);
        goods.setMold(mold);
        goods.setBeginPoint(type == 1 ? "面议" : beginPoint);
        goods.setEndPoint(type == 1 ? "面议" : endPoint);
        //goods.setTotal(type == 1 || StrUtil.isEmpty(total) ? 1 : total);//面议时默认是1车
        goods.setTotal(StrUtil.isEmpty(total) ? 1 : total);//面议时也可以下多车的单
//        if (total > 100) return ServerResponse.createError("车数太多，不能超过100");

        String msg = goodsService.queryUnitGoodsTotal(outUnitCode, inUnitCode, goods.getTotal());
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        goods.setPrice(price);
        goods.setTolls(tolls);
        goods.setDistance(distance);
        goods.setWeight(weight);
        goods.setIsWeChat(0);
        goods.setShare(0);
        goods.setpType(pType);
        goods.setLongitude(longitude);
        goods.setLatitude(latitude);
        goods.setSpell(spell);

        goods.setPlace(place);
        goods.setIsPay(isPay);
        if (pType == 1) {    //指定车号下单
            if (StrUtil.isEmpty(designCarNum) || StrUtil.isEmpty(designDriverId))
                return ServerResponse.createError("请指定司机和车号");
            goods.setCarNum(designCarNum);
            goods.setDriverId(designDriverId);
            goods.setOrderNum1(1);
        } else {
            if (StrUtil.isNotEmpty(designCarNum)) return ServerResponse.createError("下单类型错误");
            goods.setOrderNum0(StrUtil.isEmpty(total) ? 1 : total);
        }

        goods.setOutTradingUnit(outTradingUnit);
        goods.setOutTradingUnitName(outTradingUnitName);
        goods.setInTradingUnit(inTradingUnit);
        goods.setInTradingUnitName(inTradingUnitName);

        goods.setOutCusDesc(outCusDesc);
        goods.setInCusDesc(inCusDesc);

        goods.setMold2(mold2);
        goods.setBeginDistrictCode(beginDistrictCode);
        goods.setEndDistrictCode(endDistrictCode);
        if (StrUtil.isNotEmpty(productID) && !"null".equals(productID)) goods.setOutVarietyCode(productID);
        if (mold == 1 && StrUtil.isNotEmpty(inNetWeight) && !"null".equals(inNetWeight))
            goods.setInNetWeightOne(inNetWeight);
        if (goods.getTotal() == 1 && StrUtil.isNotEmpty(loadPound) && !"null".equals(loadPound))
            goods.setLoadPound(loadPound);
        if (goods.getTotal() == 1 && StrUtil.isNotEmpty(loadTime) && !"null".equals(loadTime))
            goods.setLoadTime(loadTime);
        if (goods.getTotal() == 1 && StrUtil.isNotEmpty(loadPoundPho) && !"null".equals(loadPoundPho))
            goods.setLoadPoundPho(loadPoundPho);
        return goodsService.addGoods7(goods, type, designCarNum, designDriverId, longitude, latitude, isPay, loadPound, loadTime);
    }

    @InvokeLog(description = "查询货运信息 接口", printReturn = false) //日志
    @PostMapping(value = "/goods_list_data2")
    public ServerResponse<EGridResult> goodsListData2Handler(
            @RequestParam(value = "goodsTime", required = false) Long goodsTime,   //"发布货运信息的日期"
            @RequestParam(value = "pointName", required = false) String pointName, //"起点/终点(模糊查询)"
            @RequestParam(value = "type", required = false) Integer type,          //"要查询的货运信息类型（0-未接单，1-未完成订单，2-历史订单）"
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
//        return goodsService.goodsListData2(goodsTime, pointName, type, page, rows);
        return goodsService.goodsListData3(goodsTime, pointName, type, page, rows);
    }

    @InvokeLog(description = "查询货运信息 接口", printReturn = false) //日志
    @PostMapping(value = "/goods_list_data3")
    public ServerResponse<EGridResult> goodsListData3Handler(
            @RequestParam(value = "goodsTime", required = false) Long goodsTime,   //"发布货运信息的日期"
            @RequestParam(value = "pointName", required = false) String pointName, //"起点/终点(模糊查询)"
            @RequestParam(value = "type", required = false) Integer type,          //"要查询的货运信息类型（0-未接单，1-未完成订单，2-历史订单）"
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return goodsService.goodsListData4(goodsTime, pointName, type, page, rows);
    }

    @InvokeLog(description = "废除货运信息下边的选定或所有订单 接口") //日志
    @PostMapping(value = "/del_goods4")
    public ServerResponse<String> delGoods4Handler(
            @RequestParam(value = "gid") String gid,                       //"货运信息编号"
            @RequestParam(value = "oids", required = false) String[] oids  //"订单编号数组"
    ) throws GlobalException {
        return goodsService.delGoods5(gid, oids);
    }

    @InvokeLog(description = "修改货运信息发收货商品名称 接口") //日志
    @PostMapping(value = "/update_Goods2")
    public ServerResponse<List<String>> updateGoods2Handler(
            @RequestParam(value = "gid") String gid,       //"要修改的订单所在货运信息的编号"
            @RequestParam(value = "outVariety", required = false) String outVariety,//"发货的商品名称"
            @RequestParam(value = "inVariety", required = false) String inVariety  //"收货的商品名称"
    ) throws GlobalException {
        return goodsService.updateGoods2(gid, outVariety, inVariety);
    }

    @InvokeLog(description = "修改货运信息 运往地 接口") //日志
    @PostMapping(value = "/update_goods_place2")
    public ServerResponse<List<String>> updateGoodsPlace2Handler(
            @RequestParam(value = "gid") String gid,        //"要修改的订单所在货运信息的编号"
            @RequestParam(value = "spell") String spell,    //"运往地编号"
            @RequestParam(value = "place") String place     //"运往地名称"
    ) throws GlobalException {
        return goodsService.updateGoodsPlace2(gid, spell, place);
    }

    @InvokeLog(description = "分享货运信息给好友客商 接口") //日志
    @PostMapping(value = "/goods_to_friend2")
    public ServerResponse<String> goodsToFriend2Handler(
            @RequestParam(value = "gid") String gid,       //"货运信息编号"
            @RequestParam(value = "cid") String cid,       //"好友客商编号"
            @RequestParam(value = "oids", required = false) String[] oids,//"订单编号"
            @RequestParam(value = "total", required = false) Integer total//"车数"
    ) throws GlobalException {
        return goodsService.goodsToFriend2(gid, cid, oids, total);
    }

    @InvokeLog(description = "从好友客商回收分享的货运信息 接口") //日志
    @PostMapping(value = "/regain_friend_goods2")
    public ServerResponse<String> regainFriendGoods2Handler(
            @RequestParam(value = "gid") String gid,                        //"货运信息编号"
            @RequestParam(value = "oids", required = false) String[] oids   //"订单编号"
    ) throws GlobalException {
        return goodsService.regainFriendGoods2(gid, oids);
    }

    @InvokeLog(description = "退回好友客商分享的货运信息 接口") //日志
    @PostMapping(value = "/back_friend_goods2")
    public ServerResponse<String> backFriendGoods2Handler(
            @RequestParam(value = "gid") String gid,                       //"货运信息编号"
            @RequestParam(value = "oids", required = false) String[] oids  //"订单编号"
    ) throws GlobalException {
        return goodsService.backFriendGoods2(gid, oids);
    }

    @InvokeLog(description = "客商查询最近下单记录，自动填充收货单位 接口") //日志
    @PostMapping(value = "/search_in_unit_history2")
    public ServerResponse<GoodsInUnitData> searchInUnitHistory2Handler(
            @RequestParam(value = "outUnitCode") String outUnitCode,                                   //"发货一级单位编码"
            @RequestParam(value = "outBizContractCode") String outBizContractCode,                     //"发货的合同编码"
            @RequestParam(value = "outVariety", required = false) String outVariety,                   //"发货的商品名称"
            @RequestParam(value = "outDefaultDownUnit", required = false) String outDefaultDownUnit,   //"发货的二级单位编码"
            @RequestParam(value = "outDefaultArea", required = false) String outDefaultArea            //"发货的默认场区编码"
    ) throws GlobalException {
        return goodsService.searchGoodsByOutUnit2(outUnitCode, outBizContractCode, outVariety, outDefaultDownUnit, outDefaultArea);
    }

    @InvokeLog(description = "客商查询最近下单记录，自动填充附加信息 接口") //日志
    @PostMapping(value = "/search_verb_information")
    public ServerResponse<GoodsVerbInfoData> searchVerbInfoHandler(
            @RequestParam(value = "outUnitCode", required = false) String outUnitCode,                 //"发货的一级单位编码"
            @RequestParam(value = "outBizContractCode", required = false) String outBizContractCode,   //"发货的合同编码"
            @RequestParam(value = "outVariety", required = false) String outVariety,                   //"发货的商品名称"
            @RequestParam(value = "outDefaultDownUnit", required = false) String outDefaultDownUnit,   //"发货的二级单位编码"
            @RequestParam(value = "outDefaultArea", required = false) String outDefaultArea,           //"发货的默认场区编码"

            @RequestParam(value = "inUnitCode", required = false) String inUnitCode,                   //"收货一级单位编码"
            @RequestParam(value = "inBizContractCode", required = false) String inBizContractCode,     //"收货的合同编码"
            @RequestParam(value = "inVariety", required = false) String inVariety,                     //"收货的商品名称"
            @RequestParam(value = "inDefaultDownUnit", required = false) String inDefaultDownUnit,     //"收货的二级单位编码"
            @RequestParam(value = "inDefaultArea", required = false) String inDefaultArea,             //"收货的默认场区编码"

            @RequestParam(value = "mold") Integer mold //"物流模式(0-发货物流，1-收货物流，2-收发货物流)"
    ) throws GlobalException {
        Goods goods = new Goods();
        goods.setOutUnitCode(outUnitCode);
        goods.setOutBizContractCode(outBizContractCode);
        goods.setOutVariety(outVariety);
        goods.setOutDefaultDownUnit(outDefaultDownUnit);
        goods.setOutDefaultArea(outDefaultArea);
        goods.setInUnitCode(inUnitCode);
        goods.setInBizContractCode(inBizContractCode);
        goods.setInVariety(inVariety);
        goods.setInDefaultDownUnit(inDefaultDownUnit);
        goods.setInDefaultArea(inDefaultArea);
        goods.setMold(mold);
        return goodsService.searchVerbInformation(goods);
    }

    @InvokeLog(description = "货运信息暂停或恢复接单 接口") //日志
    @PostMapping(value = "/update_Goods_status2")
    public ServerResponse<List<String>> updateGoodsStatus2Handler(
            @RequestParam(value = "gid") String gid,        //"货运信息的编号"
            @RequestParam(value = "status") Integer status   //"货运信息的状态：0-恢复接单，1-暂停接单"
    ) throws GlobalException {
        return goodsService.updateGoodsStatus2(gid, status);
    }

    @InvokeLog(description = "客商查询企业订单 接口") //日志
    @PostMapping(value = "/search_bus_goods")
    public ServerResponse<EGridResult> searchBusGoodsHandler(
            @RequestParam(value = "unitCode", required = false) String unitCode,    //一级单位编码
            @RequestParam(value = "mold", required = false) Integer mold,            //物流模式，mold：0-发货，1-收货
            @RequestParam(value = "busGid", required = false) String busGid,        //"货运信息的编号"
            @RequestParam(value = "type", required = false) Integer type,       //查询类型，null-全部，0-有可拆分单数企业单，1-无可拆分单数企业单（历史企业单）
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return busGoodsService.searchBusGoods(unitCode, mold, busGid, type, rows, page);
    }

    @InvokeLog(description = "客商查询企业订单 接口") //日志
    @PostMapping(value = "/search_bus_goods2")
    public ServerResponse<EGridResult> searchBusGoods2Handler(
            @RequestParam(value = "unitCode", required = false) String unitCode,    //一级单位编码
            @RequestParam(value = "mold", required = false) Integer mold,            //物流模式，mold：0-发货，1-收货
            @RequestParam(value = "busGid", required = false) String busGid,        //"货运信息的编号"
            @RequestParam(value = "type", required = false) Integer type,       //查询类型，null-全部，0-有可拆分单数企业单，1-无可拆分单数企业单（历史企业单）
            @RequestParam(value = "page", required = false) Integer page,          //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows           //"每页多少条"
    ) throws GlobalException {
        return busGoodsService.searchBusGoods2(unitCode, mold, busGid, type, rows, page);
    }

    @InvokeLog(description = "客商拆分企业订单 接口") //日志
    @PostMapping(value = "/use_bus_goods3")
    public ServerResponse<Object> useBusGoods3Handler(
            @RequestParam(value = "busGid") String busGid,        //"货运信息的编号"
            @RequestParam(value = "type") Integer type,                    //"物流信息类型(0-指定物流信息，1-面议)"
            @RequestParam(value = "pType") Integer pType,//"下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单"

            @RequestParam(value = "beginPoint", required = false) String beginPoint,           //"起点"
            @RequestParam(value = "endPoint", required = false) String endPoint,               //"终点"
            @RequestParam(value = "total", required = false) Integer total,                    //"车数"
            @RequestParam(value = "price", required = false) Double price,                     //"运输单价"
            @RequestParam(value = "tolls", required = false) Double tolls,                     //"预估过路费"
            @RequestParam(value = "distance", required = false) Double distance,               //"总里程（公里）"
            @RequestParam(value = "designCarNum", required = false) String designCarNum,       //"指定车牌号"
            @RequestParam(value = "designDriverId", required = false) String designDriverId,   //"指定车牌号的司机"
            @RequestParam(value = "longitude", required = false) String longitude,             //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,               //"纬度"
            @RequestParam(value = "weight", required = false) Double weight,                   //"载重量要求"

            @RequestParam(value = "isPay") Integer isPay,   //"客商是否代付(0-不代付，1-代付)"
            @RequestParam(value = "spell", required = false) String spell,   //运往地编码
            @RequestParam(value = "place", required = false) String place,   //运往地名称

            @RequestParam(value = "cusDesc", required = false) String cusDesc,             //"客商描述订单"

            @RequestParam(value = "mold2") Integer mold2,   //"是否往货业务(1-是，0-否)"
            @RequestParam(value = "beginDistrictCode", required = false) String beginDistrictCode,           //"起点"
            @RequestParam(value = "endDistrictCode", required = false) String endDistrictCode               //"终点"
    ) throws GlobalException {
        // if (StrUtil.isNotEmpty(total) && total > 100) return ServerResponse.createError("车数太多，不能超过100");

        return ServerResponse.createSuccess("接口停用");
        /*Goods goods = new Goods();
        goods.setTotal(StrUtil.isEmpty(total) ? 1 : total);//面议时也可以下多车的单
        goods.setBeginPoint(type == 1 ? "面议" : beginPoint);
        goods.setEndPoint(type == 1 ? "面议" : endPoint);
        goods.setPrice(price);
        goods.setTolls(tolls);
        goods.setDistance(distance);
        goods.setWeight(weight);
        goods.setShare(0);
        goods.setIsWeChat(0);
        goods.setpType(pType);
        goods.setLongitude(longitude);
        goods.setLatitude(latitude);
        goods.setPlace(place);
        goods.setSpell(spell);
        goods.setIsPay(isPay);
        if (pType == 1) {    //指定车号下单
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车
            if (StrUtil.isEmpty(designCarNum) || StrUtil.isEmpty(designDriverId))
                return ServerResponse.createError("请指定司机和车号");
            goods.setCarNum(designCarNum);
            goods.setDriverId(designDriverId);
            goods.setOrderNum1(1);
        } else {
            if (StrUtil.isNotEmpty(designCarNum)) return ServerResponse.createError("下单类型错误");
            goods.setOrderNum0(StrUtil.isEmpty(total) ? 1 : total);
        }
        goods.setMold2(mold2);
        goods.setBeginDistrictCode(beginDistrictCode);
        goods.setEndDistrictCode(endDistrictCode);
        return busGoodsService.useBusGoods3(busGid, goods, cusDesc);*/
    }

    @InvokeLog(description = "客商拆分企业订单 接口") //日志
    @PostMapping(value = "/use_bus_goods4")
    public ServerResponse<Object> useBusGoods4Handler(
            @RequestParam(value = "busGid") String busGid,        //"货运信息的编号"
            @RequestParam(value = "type") Integer type,                    //"物流信息类型(0-指定物流信息，1-面议)"
            @RequestParam(value = "pType") Integer pType,//"下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单"

            @RequestParam(value = "beginPoint", required = false) String beginPoint,           //"起点"
            @RequestParam(value = "endPoint", required = false) String endPoint,               //"终点"
            @RequestParam(value = "total", required = false) Integer total,                    //"车数"
            @RequestParam(value = "price", required = false) Double price,                     //"运输单价"
            @RequestParam(value = "tolls", required = false) Double tolls,                     //"预估过路费"
            @RequestParam(value = "distance", required = false) Double distance,               //"总里程（公里）"
            @RequestParam(value = "designCarNum", required = false) String designCarNum,       //"指定车牌号"
            @RequestParam(value = "designDriverId", required = false) String designDriverId,   //"指定车牌号的司机"
            @RequestParam(value = "longitude", required = false) String longitude,             //"经度"
            @RequestParam(value = "latitude", required = false) String latitude,               //"纬度"
            @RequestParam(value = "weight", required = false) Double weight,                   //"载重量要求"

            @RequestParam(value = "isPay") Integer isPay,   //"客商是否代付(0-不代付，1-代付)"
            @RequestParam(value = "spell", required = false) String spell,   //运往地编码
            @RequestParam(value = "place", required = false) String place,   //运往地名称

            @RequestParam(value = "cusDesc", required = false) String cusDesc,             //"客商描述订单"

            @RequestParam(value = "mold2") Integer mold2,   //"是否往货业务(1-是，0-否)"
            @RequestParam(value = "beginDistrictCode", required = false) String beginDistrictCode,           //"起点"
            @RequestParam(value = "endDistrictCode", required = false) String endDistrictCode,               //"终点"

            @RequestParam(value = "inNetWeight", required = false) Double inNetWeight,               //收货，二级单位配置需要录入对方的净重(单车净重)
            @RequestParam(value = "loadPound", required = false) String loadPound,               //收货，二级单位配置需要录入对方的装货单号
            @RequestParam(value = "loadPoundPho", required = false) String loadPoundPho,               //收货，二级单位配置需要上传对方的装货单照片
            @RequestParam(value = "loadTime", required = false) Long loadTime               //收货，二级单位配置需要录入对方的装货时间
    ) throws GlobalException {
//        if (StrUtil.isNotEmpty(total) && total > 100) return ServerResponse.createError("车数太多，不能超过100");
        Goods goods = new Goods();
        goods.setTotal(StrUtil.isEmpty(total) ? 1 : total);//面议时也可以下多车的单
        goods.setBeginPoint(type == 1 ? "面议" : beginPoint);
        goods.setEndPoint(type == 1 ? "面议" : endPoint);
        goods.setPrice(price);
        goods.setTolls(tolls);
        goods.setDistance(distance);
        goods.setWeight(weight);
        goods.setShare(0);
        goods.setIsWeChat(0);
        goods.setpType(pType);
        goods.setLongitude(longitude);
        goods.setLatitude(latitude);
        goods.setPlace(place);
        goods.setSpell(spell);
        goods.setIsPay(isPay);
        if (pType == 1) {    //指定车号下单
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车
            if (StrUtil.isEmpty(designCarNum) || StrUtil.isEmpty(designDriverId))
                return ServerResponse.createError("请指定司机和车号");
            goods.setCarNum(designCarNum);
            goods.setDriverId(designDriverId);
            goods.setOrderNum1(1);
        } else {
            if (StrUtil.isNotEmpty(designCarNum)) return ServerResponse.createError("下单类型错误");
            goods.setOrderNum0(StrUtil.isEmpty(total) ? 1 : total);
        }
        goods.setMold2(mold2);
        goods.setBeginDistrictCode(beginDistrictCode);
        goods.setEndDistrictCode(endDistrictCode);
        return busGoodsService.useBusGoods4(busGid, goods, cusDesc, inNetWeight, loadPound, loadPoundPho, loadTime);
    }

    @InvokeLog(description = "客商查询企业订单 接口") //日志
    @PostMapping(value = "/get_bus_goods")
    public ServerResponse<EGridResult> getBusGoodsHandler(
            @RequestParam(value = "busGid") String busGid,        //"货运信息的企业编号"
            @RequestParam(value = "type", required = false) Integer type,  //"要展开的订单类型行 0-未接单，1-已接单，2-历史单，空-全部"
            @RequestParam(value = "page", required = false) Integer page,  //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows   //"每页多少条"
    ) throws GlobalException {
        return busGoodsService.getBusGoods(busGid, type, page, rows);
    }

    @InvokeLog(description = "客（友）商指定车数分享给司机微信 接口") //日志
    @PostMapping(value = "/share_wxdvr")
    public ServerResponse<WXsDvrGoods> shareToWXsDvrHandler(
            @RequestParam(value = "gid") String gid,        //"货运信息的编号"
            @RequestParam(value = "total") Integer total    //"要分享的车数"
    ) throws GlobalException {
        return goodsService.shareToWXsDvrWithTotal(gid, total);
    }

    @InvokeLog(description = "客（友）商 展开查询 指定车数分享给司机微信的订单列表 接口") //日志
    @PostMapping(value = "/get_share_wxdvr_list")
    public ServerResponse<List<WXsDvrGoods>> getShareToWXsDvrListHandler(
            @RequestParam(value = "gid") String gid        //"货运信息的编号"
    ) throws GlobalException {
        return goodsService.getShareToWXsDvrGoodsList(gid);
    }

    @InvokeLog(description = "客商下单，收货业务查询二级单位是否需要录入对方净重 接口") //日志
    @PostMapping(value = "/need_net_weight")
    public ServerResponse<List<Boolean>> checkInSubUnitNeedNetWeightHandler(
            @RequestParam(value = "subCode") String subCode        //"二级单位编号"
    ) throws GlobalException {
        return goodsService.checkInSubUnitNeedNetWeight(subCode);
    }

    @InvokeLog(description = "货运信息收货业务修改对方净重 接口") //日志
    @PostMapping(value = "/update_Goods_inNetWeight")
    public ServerResponse<List<String>> updateGoodsInNetWeightHandler(
            @RequestParam(value = "gid") String gid,        //"货运信息的编号"
            @RequestParam(value = "inNetWeight") Double inNetWeight
    ) throws GlobalException {
        return goodsService.updateGoodsInNetWeight(gid, inNetWeight);
    }
}
