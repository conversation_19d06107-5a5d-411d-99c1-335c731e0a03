package com.erdos.coal.park.api.driver.dao.impl;

import com.erdos.coal.config.CoalConfig;
import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.driver.entity.FileInfo;
import com.erdos.coal.utils.StrUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.gridfs.GridFS;
import com.mongodb.gridfs.GridFSDBFile;
import com.mongodb.gridfs.GridFSInputFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.Date;
import java.util.regex.Pattern;

@Repository("fileInfoDao")
public class FileInfoDaoImpl extends BaseMongoDAOImpl<FileInfo> implements IFileInfoDao {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private CoalConfig coalConfig;

    //保存文件（图片）
    public String fileSave(String id, MultipartFile inputStream) {
        return fileSave(id, inputStream, null);
    }

    public String fileSave(String id, MultipartFile mFile, String bucket) {
        //利用GridFS 管理数据库
        GridFS gridFS;
        if (StrUtil.isEmpty(bucket)) {
            gridFS = new GridFS(this.dataStore.getDB());
        } else {
            gridFS = new GridFS(this.dataStore.getDB(), bucket);
        }
        /*测试 图片是固定位置
        File file = new File("E:/a.jpg");
        GridFSInputFile gfs = null;
        try {
            gfs = gridFS.createFile(file);
            // 文件名
            String fileName = gfs.getFilename();
            // 文件后缀
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            // 重新生成唯一文件名，用于存储数据库
            String newFileName = id + suffixName;
            logger.info("新的文件名： " + newFileName);
            //创建文件
            File dest = new File(path + newFileName);
            gfs.put("uid", newFileName);
            gfs.save();
            return newFileName;

        } catch (IOException e) {
            e.printStackTrace();
        }*/

        if (mFile.getSize() > 0) {
            String fileName = mFile.getOriginalFilename();
            File file = new File(coalConfig.uploadPath);
            File origFile = null;
            GridFSInputFile gfs = null;
            InputStream inputStream = null;
            try {
                inputStream = mFile.getInputStream();
                if (!file.exists()) {
                    file.mkdirs();
                }
                origFile = new File(coalConfig.uploadPath + File.separator + fileName + new Date().getTime());//原图片名 + 时间戳
                mFile.transferTo(origFile);
                gfs = gridFS.createFile(origFile);

            } catch (IOException e) {
                e.printStackTrace();
            }
            // 文件名
            //fileName = gfs.getFilename();
            // 文件后缀
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            // 重新生成唯一文件名，用于存储数据库
            String newFileName = id + suffixName;
            logger.info("新的文件名： " + newFileName);
            gfs.put("uid", newFileName);
            gfs.save();

            if (origFile.exists()) {//文件存在删除
                origFile.delete();
            }
            //文件（图片）流写入磁盘
            String filePath = coalConfig.uploadPath + File.separator + newFileName;
            BufferedInputStream in = null;
            BufferedOutputStream out = null;

            try {
                in = new BufferedInputStream(inputStream);

                out = new BufferedOutputStream(new FileOutputStream(filePath));
                int len = -1;
                byte[] b = new byte[1024];
                while ((len = in.read(b)) != -1) {
                    out.write(b, 0, len);
                }
                in.close();
                out.close();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
            return newFileName;
        } else {
            return null;
        }

    }

    /**
     * 保存文件
     *      * @param stream
     *      * @param path
     *      * @param filename
     *      * @throws IOException
     *      
     */
    public void SaveFileFromInputStream(InputStream stream, String filename) throws IOException {

        FileOutputStream fs = new FileOutputStream(coalConfig.uploadPath + "/" + filename);
        byte[] buffer = new byte[1024 * 1024];
        int bytesum = 0;
        int byteread = 0;
        while ((byteread = stream.read(buffer)) != -1) {
            bytesum += byteread;
            fs.write(buffer, 0, byteread);
            fs.flush();
        }
        fs.close();
        stream.close();
    }

    //取出文件（图片）流
    public String readFile(String uid, String bucket) {
        if (downToPath(uid, bucket)) {
            return File.separator + "file" + File.separator + uid;
        } else {
            return "";
        }
    }

    // 从mongodb下载文件到磁盘
    private Boolean downToPath(String uid, String bucket) {
        File folder = new File(coalConfig.uploadPath);   //判断文件夹是否存在，不存在先创建
        if (!folder.exists()) {
            folder.mkdirs();
        }

        if (StrUtil.isEmpty(uid)) {
            return false;
        }
        String filePath = coalConfig.uploadPath + File.separator + uid;
        File file = new File(filePath);
        if (!file.exists()) {   //文件不存在先从mongodb中下载到磁盘上
            //利用GridFS 管理数据库
            GridFS gridFS = new GridFS(this.dataStore.getDB(), bucket);
            //设置查找条件
            DBObject query = new BasicDBObject("uid", uid);
            GridFSDBFile gridDBFile = gridFS.findOne(query);
            if (gridDBFile == null) return false;

            File writeFile = new File(filePath);
            try {
                if (!writeFile.exists()) {
                    writeFile.createNewFile();
                }
                //写入记录的存储
                gridDBFile.writeTo(writeFile);
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }
        }
        return true;
    }

    //取出文件（图片）流 返回文件名
    public String readFileName(String uid, String bucket) {
        if (downToPath(uid, bucket)) {
            return File.separator + uid;
        } else {
            return "";
        }
    }

    //删除文件（图片）
    public void deleteByUId(String id) {
        deleteByUId(id, null);
    }

    //删除文件（图片）
    public void deleteByUId(String uid, String bucket) {
        //利用GridFS 管理数据库
        GridFS gridFS;
        if (StrUtil.isEmpty(bucket)) {
            gridFS = new GridFS(this.dataStore.getDB());
        } else {
            gridFS = new GridFS(this.dataStore.getDB(), bucket);
        }
        //设置查找条件
        BasicDBObject query = new BasicDBObject();
        Pattern pattern = Pattern.compile(uid + ".*$", Pattern.CASE_INSENSITIVE);
        query.put("uid", pattern);

        GridFSDBFile gFile = gridFS.findOne(query);

        gridFS.remove(query);

        //删除磁盘文件
        if (gFile != null) {
            File delFile = new File(coalConfig.uploadPath + File.separator + gFile.get("uid"));
            boolean b = delFile.delete();
        }
    }

    //保存文件（图片）到磁盘并返回该文件路径
    public String fileSaveDisc(String id, MultipartFile mFile) {

        if (mFile.getSize() > 0) {
            String fileName = mFile.getOriginalFilename();
            File file = new File(coalConfig.uploadPath);
            InputStream inputStream = null;
            try {
                inputStream = mFile.getInputStream();
                if (!file.exists()) {
                    file.mkdirs();
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
            // 文件名
            //fileName = gfs.getFilename();
            // 文件后缀
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            // 重新生成唯一文件名，用于存储数据库
            String newFileName = id + suffixName;
            logger.info("新的文件名： " + newFileName);

            //文件（图片）流写入磁盘
            String filePath = coalConfig.uploadPath + File.separator + newFileName;
            BufferedInputStream in = null;
            BufferedOutputStream out = null;

            try {
                in = new BufferedInputStream(inputStream);

                out = new BufferedOutputStream(new FileOutputStream(filePath));
                int len = -1;
                byte[] b = new byte[1024];
                while ((len = in.read(b)) != -1) {
                    out.write(b, 0, len);
                }
                in.close();
                out.close();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
            return newFileName;
        } else {
            return null;
        }
    }

    public String fileSaveDB(String photo, String bucket) {
        //利用GridFS 管理数据库
        GridFS gridFS;
        if (StrUtil.isEmpty(bucket)) {
            gridFS = new GridFS(this.dataStore.getDB());
        } else {
            gridFS = new GridFS(this.dataStore.getDB(), bucket);
        }
        //图片是固定位置
        File file = new File(coalConfig.uploadPath + File.separator + photo);
        GridFSInputFile gfs = null;
        try {
            gfs = gridFS.createFile(file);
            // 文件名
            String fileName = gfs.getFilename();
            gfs.put("uid", fileName);
            gfs.save();
            return fileName;

        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void deleteDB(String uid, String bucket) {
        //利用GridFS 管理数据库
        GridFS gridFS;
        if (StrUtil.isEmpty(bucket)) {
            gridFS = new GridFS(this.dataStore.getDB());
        } else {
            gridFS = new GridFS(this.dataStore.getDB(), bucket);
        }
        //设置查找条件
        BasicDBObject query = new BasicDBObject();
        Pattern pattern = Pattern.compile(uid + ".*$", Pattern.CASE_INSENSITIVE);
        query.put("uid", pattern);

        GridFSDBFile gFile = gridFS.findOne(query);

        gridFS.remove(query);
    }
}
