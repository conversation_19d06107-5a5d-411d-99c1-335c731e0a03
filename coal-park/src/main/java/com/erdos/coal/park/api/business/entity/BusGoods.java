package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_bus_goods", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("cid")}),
        @Index(fields = {@Field("busGid")}, options = @IndexOptions(unique = true))
})
public class BusGoods extends BaseMongoInfo {
    private String cid;             //客商编号
    private String busGid;             //企业单编号
    private String userCode;
    private Integer billType;       //5-采购 入库, 6-销售 出库
    private Integer startNum;
    private Integer endNum;

    private String unitCode;        //单位编码
    private String unitName;        //单位名称
    private String tradeName;      //商品名称
    private Integer mold;           //物流模式  发货物流 - 0，收货物流 - 1
    private Integer delNum;           //作废单数
    private Integer useNum;           //已拆分单数
    private Integer availableNum;     //可用单数
    private Integer totalNum;         //总单数

    private String dvrMobile;
    private String carNum;
    private Integer carType;

    private String bizunitname;
    private String bizcontractcode;
    private String bizcontractname;
    private String productname;         //variety
    private String tradesubcode;        //defaultDownUnit
    private String subname;
    private String defaultarea;
    private String areaname;

    private String tradingUnit;
    private String tradingUnitName;

    private String outVarietyCode;  // 智慧能源-合同备案中的煤种ID

    // 2024-12-28 急加
    private String remark1;
    private String remark2;
    private String remark3;
    private String remark4;

    public Integer getDelNum() {
        return delNum;
    }

    public void setDelNum(Integer delNum) {
        this.delNum = delNum;
    }

    public Integer getUseNum() {
        return useNum;
    }

    public void setUseNum(Integer useNum) {
        this.useNum = useNum;
    }

    public Integer getAvailableNum() {
        return availableNum;
    }

    public void setAvailableNum(Integer availableNum) {
        this.availableNum = availableNum;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getBusGid() {
        return busGid;
    }

    public void setBusGid(String busGid) {
        this.busGid = busGid;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public Integer getStartNum() {
        return startNum;
    }

    public void setStartNum(Integer startNum) {
        this.startNum = startNum;
    }

    public Integer getEndNum() {
        return endNum;
    }

    public void setEndNum(Integer endNum) {
        this.endNum = endNum;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getDvrMobile() {
        return dvrMobile;
    }

    public void setDvrMobile(String dvrMobile) {
        this.dvrMobile = dvrMobile;
    }

    public Integer getCarType() {
        return carType;
    }

    public void setCarType(Integer carType) {
        this.carType = carType;
    }

    public String getBizunitname() {
        return bizunitname;
    }

    public void setBizunitname(String bizunitname) {
        this.bizunitname = bizunitname;
    }

    public String getBizcontractcode() {
        return bizcontractcode;
    }

    public void setBizcontractcode(String bizcontractcode) {
        this.bizcontractcode = bizcontractcode;
    }

    public String getBizcontractname() {
        return bizcontractname;
    }

    public void setBizcontractname(String bizcontractname) {
        this.bizcontractname = bizcontractname;
    }

    public String getProductname() {
        return productname;
    }

    public void setProductname(String productname) {
        this.productname = productname;
    }

    public String getTradesubcode() {
        return tradesubcode;
    }

    public void setTradesubcode(String tradesubcode) {
        this.tradesubcode = tradesubcode;
    }

    public String getSubname() {
        return subname;
    }

    public void setSubname(String subname) {
        this.subname = subname;
    }

    public String getDefaultarea() {
        return defaultarea;
    }

    public void setDefaultarea(String defaultarea) {
        this.defaultarea = defaultarea;
    }

    public String getAreaname() {
        return areaname;
    }

    public void setAreaname(String areaname) {
        this.areaname = areaname;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getTradingUnit() {
        return tradingUnit;
    }

    public void setTradingUnit(String tradingUnit) {
        this.tradingUnit = tradingUnit;
    }

    public String getTradingUnitName() {
        return tradingUnitName;
    }

    public void setTradingUnitName(String tradingUnitName) {
        this.tradingUnitName = tradingUnitName;
    }

    public String getOutVarietyCode() {
        return outVarietyCode;
    }

    public void setOutVarietyCode(String outVarietyCode) {
        this.outVarietyCode = outVarietyCode;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getRemark3() {
        return remark3;
    }

    public void setRemark3(String remark3) {
        this.remark3 = remark3;
    }

    public String getRemark4() {
        return remark4;
    }

    public void setRemark4(String remark4) {
        this.remark4 = remark4;
    }

    /*
    goods.setBeginPoint("面议");
    goods.setEndPoint("面议");
    goods.setTotal(endNum -startNum +1);
    goods.setIsWeChat(0);
    goods.setShare(0);
    goods.setpType(5);  //5-企业下单
    goods.setStatus(0);
    goods.setIsPay(0);

    goods.setCarNum(carNum);
    goods.setDriverId(driverInfo.getObjectId().toHexString());
    goods.setOrderNum1(1);
    goods.setOrderNum0(endNum - startNum + 1);
    *//*

    //@Indexed(options = @IndexOptions(name = "_cid", background = true))
    private String cid;             //客商编号
    private String tradeName;      //商品名称
    private String beginPoint;     //起点
    private String endPoint;        //终点
    private Integer total;           //总车数
    private Double weight;          //车载重量要求
    //private Long releaseTime;     //发布时间
    private Double price;           //单价
    private Double distance;          //总里程
    private Double tolls;           //预估过路费

    private Integer pType;          //下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单  X-> 5-企业下单

    private Integer outMin;         //发货单位 最小票号
    private Integer outMax;         //发货单位 最大票号
    private Integer inMin;          //收货单位 最小票号
    private Integer inMax;          //收货单位 最大票号

    private Integer status; //货运信息状态 0-货运信息下订单可接单 1-货运信息下订单暂停接单
    private Integer delNum = 0; //废除的车数
    private Integer orderNum0 = 0;  //货运信息下未接单车数
    private Integer orderNum1 = 0;  //货运信息下已接单未完成车数
    private Integer orderNum2 = 0;  //货运信息下已接单已完成车数
    private Integer orderNum3 = 0;  //货运信息下已入场车数
    *//*
     * checking 3退回1 orderNum2-1
     *       发货 或 收货，checking=3 orderNum2+1，orderNum1-1
     *       收发货，两边checking=3，才是订单完成。orderNum2+1，orderNum1-1
     * *//*
    //货运信息下所有订单毛皮中合计值
    private Double outGrossWeight;
    private Double outTareWeight;
    private Double inGrossWeight;
    private Double inTareWeight;

    private String carNum;          //指定车号下单时的车牌号
    private String driverId;        //指定车号下单时的司机编号
    private int isPay = 0;          //客商是否代付(0-不代付，1-代付)
    private String payCid;          //愿意代付的客商编号

    private String groupNo; //发布货运信息分配可抢单的司机组编号

    private Integer isWeChat;    //0-给app司机下单的货运信息；1-给小程序司机下单的货运信息

    *//*
     * 0-未分享货单给好友客商
     * 1-分享一部分货单给好友客商,还有部分可以分享
     * 2-可以分享的订单已经全部分享给好友客商了
     * *//*
    private Integer share = 0;
    private String shareCid;    //货单来自编号为cid的客商分享   改成货单分享给友商的id
    private String shareGid;    //货单来自编号为gid的客商货单分享 字段作废

    @Reference(value = "customerUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser;

    //以下需要收取的信息费均为"单价"，即单车的费用，总费还需要乘以车数total
    private Integer feesOut = 0;    //平台需要收取发货企业的信息费（分）
    private String outUnitPayId;    //愿意代付的发货单位编号
    private Integer feesIn = 0;    //平台需要收取收货企业的信息费（分）
    private String inUnitPayId;    //愿意代付的收货单位编号
    private Integer fees1Out = 0;    //发货企业一级单位需要收取的信息费（分）
    private Integer fees1In = 0;    //收货企业一级单位需要收取的信息费（分）
    private Integer fees2Out = 0;    //发货企业二级单位需要收取的信息费（分）
    private Integer fees2In = 0;    //收货企业二级单位需要收取的信息费（分）
    private Integer fees3 = 0;    //客商或友商需要收取的信息费（分）
    private Integer fee5Out = 0;    //三方分账
    private Integer fee5In = 0;     //三方分账
    private String fee5OutId;       //三方账户
    private String fee5InId;        //三方账户

    private String longitude;
    private String latitude;

    private List<GOrder> orders;
    private List<GOrder> shareOrders;

    private String spell;   //运往地编码
    private String place;   //运往地名称*/
}
