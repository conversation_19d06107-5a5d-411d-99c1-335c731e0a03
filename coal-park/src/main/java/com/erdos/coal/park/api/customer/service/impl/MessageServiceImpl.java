package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.ICusMessageDao;
import com.erdos.coal.park.api.customer.dao.IOrderDao;
import com.erdos.coal.park.api.customer.dao.IOrderTakingDao;
import com.erdos.coal.park.api.customer.entity.CusMessage;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.pojo.OrderInfoData;
import com.erdos.coal.park.api.customer.service.IMesssageService;
import com.erdos.coal.park.api.driver.dao.IOrderLogisticsDao;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("messageService")
public class MessageServiceImpl extends BaseMongoServiceImpl<OrderTaking, IOrderTakingDao> implements IMesssageService {
    @Resource
    private IOrderDao orderDao;
    @Resource
    private IOrderLogisticsDao orderLogisticsDao;
    @Resource
    private ICusMessageDao cusMessageDao;

    @Override
    //public ServerResponse<List<OrderInfoData>> queryMes(Integer type) {
    public ServerResponse<EGridResult> queryMes(Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        Query<OrderTaking> query = this.createQuery();
        query.filter("cid", cid);
        EGridResult<OrderTaking> eGridResult;
        List<OrderTaking> orderTakingList;
        long total;
        List<OrderInfoData> orderInfoList = new ArrayList<>();
        if (type == 1) {//0-物流信息提示,1-下单信息提示
            //query.criteria("finishTag").greaterThanOrEq(0);//大于等于0代表已经接单成功的
            query.criteria("finishTag").equal(0);//0：接单成功；1：运输途中；2：订单完成）
            query.order(Sort.descending("createTime"));
            //orderTakingList = query.find().toList();
            eGridResult = findPage(page, rows, query);
            orderTakingList = eGridResult.getRows();
            total = eGridResult.getTotal();

            for (OrderTaking ot : orderTakingList) {
                OrderInfoData orderInfoData = new OrderInfoData();
                orderInfoData.setOid(ot.getOid());
                orderInfoData.setCarNum(ot.getCarNum());
                Order order = orderDao.get("oid", ot.getOid());
                orderInfoData.setBeginPoint(StrUtil.isNotEmpty(order.getBeginPoint()) ? order.getBeginPoint() : "面议出发点");
                orderInfoData.setEndPoint(StrUtil.isNotEmpty(order.getEndPoint()) ? order.getEndPoint() : "面议目的地");
                orderInfoData.setTradeName(order.getTradeName());

                orderInfoList.add(orderInfoData);
            }
        } else {
            //orderTakingList = query.find().toList();
            eGridResult = findPage(page, rows, query);
            orderTakingList = eGridResult.getRows();
            total = eGridResult.getTotal();
            List<String> oids = new ArrayList<>();
            for (OrderTaking ot : orderTakingList) {
                oids.add(ot.getOid());
            }
            Query<OrderLogistics> olQuery = orderLogisticsDao.createQuery();
            olQuery.criteria("finishTag").equal(2);
            olQuery.filter("oid in", oids.toArray());
            olQuery.order(Sort.descending("updateTime"));
            List<OrderLogistics> olList = olQuery.find().toList();

            for (OrderLogistics ol : olList) {
                OrderInfoData orderInfoData = new OrderInfoData();
                orderInfoData.setOid(ol.getOid());
                Order order = orderDao.get("oid", ol.getOid());
                orderInfoData.setCarNum(order.getCarNum());
                orderInfoData.setBeginPoint(StrUtil.isNotEmpty(order.getBeginPoint()) ? order.getBeginPoint() : "面议出发点");
                orderInfoData.setEndPoint(StrUtil.isNotEmpty(order.getEndPoint()) ? order.getEndPoint() : "面议目的地");
                orderInfoData.setTradeName(order.getTradeName());

                orderInfoList.add(orderInfoData);
            }
        }

        //return ServerResponse.createSuccess("查询成功", orderInfoList);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, orderInfoList));
    }

    @Override
    public ServerResponse<EGridResult<CusMessage>> searchCusMsg(Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        Query<CusMessage> query = cusMessageDao.createQuery();
        query.criteria("cid").equal(cid);
        if (StrUtil.isNotEmpty(type)) query.criteria("type").equal(type);

        EGridResult<CusMessage> eGridResult = cusMessageDao.findPage(page, rows, query);
        return ServerResponse.createSuccess("查询成功", eGridResult);
    }
}
