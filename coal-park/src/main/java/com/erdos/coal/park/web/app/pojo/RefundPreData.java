package com.erdos.coal.park.web.app.pojo;

import java.util.Date;

public class RefundPreData {
    private String userId;   //提现人（客商或者司机id），（customerUser和driverInfo关联数据）
    private int userType;   //用户类型 0：客商customerUser 1：司机driverInfo
    private String mobile;  //手机号
    private Integer refundType; //提现方式（0-支付宝/1-微信），支付宝账号（identity） 和 微信账号（openid）
    /*ALIPAY_USER_ID 支付宝的会员ID，
    ALIPAY_LOGON_ID：支付宝登录号，支持邮箱和手机号格式*/
    private String transferNo;  //提现订单唯一订单号（out_biz_no/partner_trade_no）

    private double amount; //提现金额（trans_amount/amount）单位为分
    private String accountNumber;   //收款人账号（identity/openid）
    private String nickname;    //微信昵称
    private String name;   //收款人姓名（name/re_user_name）//支付宝和微信都是非必填参数,当identity_type=ALIPAY_LOGON_ID时，name字段必填
    private String title;   //转账业务标题（order_title/）
    private String remark;  //企业付款备注（remark/desc）
    private Integer check = 0;  //审核 0-未审核，1-通过审核，2-审核未通过
    private String reason;      //审核未通过原因
    private String resultCode;  //业务结果 success-成功 FAIL-失败
    private String errCode;     //失败原因编码
    private String errCodeDes;  //失败原因汉字
    private Date createTime;
    private long updateTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    public String getTransferNo() {
        return transferNo;
    }

    public void setTransferNo(String transferNo) {
        this.transferNo = transferNo;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCheck() {
        return check;
    }

    public void setCheck(Integer check) {
        this.check = check;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }
}
