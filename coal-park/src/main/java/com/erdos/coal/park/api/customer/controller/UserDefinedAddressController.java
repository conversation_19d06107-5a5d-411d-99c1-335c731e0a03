package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.IUserDefinedAddressService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP自定义地址管理接口列表"
@RestController
@RequestMapping("/api/cus/address")
public class UserDefinedAddressController extends BaseController {
    @Resource
    private IUserDefinedAddressService userDefinedAddressService;

    @InvokeLog(description = "添加自定义地址 接口") //日志
    @PostMapping(value = "/add_user_defined_address")
    public ServerResponse<Object> addUserDefinedAddressHandler(
            @RequestParam(value = "addName", required = false) String addName, //"自定义地址名称"
            @RequestParam(value = "fullName") String fullName,                 //"地址全称"
            @RequestParam(value = "location") Double[] location,                //"地址坐标(经度，纬度)"

            @RequestParam(value = "province", required = false) String province,    //"省"
            @RequestParam(value = "city", required = false) String city,            //"市"
            @RequestParam(value = "district", required = false) String district    //"区/县"
    ) throws GlobalException {
        return userDefinedAddressService.addUserDefinedAddress(addName, fullName, location, province, city, district);
    }

    @InvokeLog(description = "删除自定义地址 接口") //日志
    @PostMapping(value = "/del_user_defined_address")
    public ServerResponse<String> delUserDefinedAddressHandler(
            @RequestParam(value = "caId") String caId  //"自定义地址编号"
    ) throws GlobalException {
        return userDefinedAddressService.delUserDefinedAddress(caId);
    }

    @InvokeLog(description = "查询自定义地址 接口", printReturn = false) //日志
    @PostMapping(value = "/search_user_defined_address")
    public ServerResponse<EGridResult> searchUserDefinedAddressHandler(
            @RequestParam(value = "unitCode", required = false) String unitCode,    //"一级单位编码（出发地查询时若有选择发货一级单位，加上该参数；目的地查询时若有选择收货一级单位，加上该参数）"
            @RequestParam(value = "addName", required = false) String addName,      //"自定义地址名称"
            @RequestParam(value = "page", required = false) Integer page,           //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows            //"每页多少条"
    ) throws GlobalException {
        return userDefinedAddressService.searchUserDefinedAddress(unitCode, addName, page, rows);
    }
}
