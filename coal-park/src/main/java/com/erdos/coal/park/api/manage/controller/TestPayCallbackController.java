package com.erdos.coal.park.api.manage.controller;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.park.api.driver.entity.PayAliPrepaid;
import com.erdos.coal.park.api.driver.entity.PayAliResult;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IPayAliResultService;
import com.erdos.coal.park.api.driver.service.IWechatOrderRecordService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidCostService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidService;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.api.manage.service.IAppLogService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.utils.ObjectUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.util.HashMap;
import java.util.Map;

//TODO: 这个 controller 只在测试时用
@RestController
@RequestMapping("/api/manage")
public class TestPayCallbackController extends BaseController {
    @Resource
    private IAppLogService appLogService;
    @Resource
    private IWxPrepaidService prepaidService;
    @Resource
    private IWechatOrderRecordService wechatOrderRecordService;
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private IPayAliResultService payAliResultService;
    @Resource
    private IWxPrepaidCostService wxPrepaidCostService;

    //TODO: 微信支付回调用
    @PostMapping(value = "/wxpay/notify", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String payWeiXinNotifyUrl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        BufferedReader reader;

        reader = request.getReader();
        String line;
        String xmlString;
        StringBuilder inputString = new StringBuilder();

        while ((line = reader.readLine()) != null) {
            inputString.append(line);
        }
        xmlString = inputString.toString();
        request.getReader().close();
        logger.info("----接收到的数据如下：---" + xmlString);
        Map<String, String> map;

        try {
            map = WXPayUtil.xmlToMap(xmlString);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

        if (ObjectUtil.isNotNull(map)) {

            String sign = WXPayUtil.generateSignature(map, wxPayConstants.key, WXPayConstants.SignType.MD5);
            if (sign.equals(map.get("sign"))) { //校验签名是否正确

                AppLog appLog = new AppLog();
                appLog.setUser("WXPay");
                appLog.setParameter(xmlString);
                appLogService.writeLog(appLog);

                WxResult wxResult = null;

                //attach String(128) 商家数据包，原样返回
                String attach = map.get("attach");
                /*if (attach.contains("Semicolon")) { //客商代支付货运信息 或 客商自己账户充值
                    wxResult = prepaidService.payResult2(map, 0, attach.split(",")[1]);

                } else if (attach.equals("routine")) {   //微信小程序回调
                    wxResult = wechatOrderRecordService.payResult(map);   //加锁 存储回调结果，预支付单

                } else { //司机账户充值
                    wxResult = prepaidService.payResult(map, 0, null, null);   //加锁 存储回调结果，账户信息，预支付单
                }*/
                if (attach.equals("fee5")) {    //包含三方分账的订单，司机直接微信付款结果通知
                    wxResult = prepaidService.weChatPayResult2(map, 0);
                } else if (attach.equals("routine")) {//微信小程序回调
                    wxResult = wechatOrderRecordService.payResult(map);
                } else if (attach.equals("separate")) {
                    wxResult = prepaidService.weChatPayResult3(attach, map, 0);
                } else {
                    wxResult = prepaidService.weChatPayResult(attach, map, 0);
                }
                if (wxResult == null) {
                    return "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[回调失败]]></return_msg>" + "</xml> ";
                } else {
                    return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                }

            } else {
                logger.error("微信支付回调通知签名错误");
                return "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
            }
        } else {
            logger.error("微信支付回调通知失败");
            return "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
        }

    }

    //TODO: 支付宝支付回调
    @PostMapping(value = "/alipay/notify", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String payAlipayNotifyUrl(HttpServletRequest request, HttpServletResponse response) throws Exception {

        //1.获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<>();
        Map requestParams = request.getParameterMap();

        for (Object o : requestParams.keySet()) {
            String name = (String) o;
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }

        //2.签名验证(对支付宝返回的数据验证，确定是支付宝返回的)
        PayAliPrepaid prepaid = payAliResultService.rsaCheckV1(params);
        //3.对验签进行处理
        if (prepaid != null) {    //验签通过
            String out_trade_no = request.getParameter("out_trade_no");            // 商户订单号

            //查询是否已经成功处理推送信息
            PayAliResult payAliResult = payAliResultService.get("notify_id", request.getParameter("notify_id"));
            if (ObjectUtil.isNotEmpty(payAliResult)) {
                logger.info("异步通知已经成功处理过-" + request.getParameter("notify_id"));
                return "success";
            }

            String strParams = JSON.toJSONString(params);
            AppLog appLog = new AppLog();
            appLog.setUser("ALIPay");
            appLog.setMethod("pay");
            appLog.setParameter(strParams);
            appLog.setDescription(params.toString());
            appLogService.writeLog(appLog);     //异步处理日志

            //String orderType = request.getParameter("body");                    // 订单内容
            String tradeStatus = request.getParameter("trade_status");            //交易状态
            return payAliResultService.checkNotify(params, out_trade_no, tradeStatus, prepaid);
        } else {  //验签不通过
            System.err.println("验签失败-" + request.getParameter("notify_id"));
            return "failure";
        }
    }

    //TODO: 微信支付回调用
    @PostMapping(value = "/wxpay/notify_cost", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String payWeiXinNotifyCostUrl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        BufferedReader reader;

        reader = request.getReader();
        String line;
        String xmlString;
        StringBuilder inputString = new StringBuilder();

        while ((line = reader.readLine()) != null) {
            inputString.append(line);
        }
        xmlString = inputString.toString();
        request.getReader().close();
        logger.info("----接收到的数据如下：---" + xmlString);
        Map<String, String> map;

        try {
            map = WXPayUtil.xmlToMap(xmlString);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

        if (ObjectUtil.isNotNull(map)) {
            return wxPrepaidCostService.weChatPayResult(map);
        } else {
            logger.error("微信支付回调通知失败");
            return "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
        }

    }
}
