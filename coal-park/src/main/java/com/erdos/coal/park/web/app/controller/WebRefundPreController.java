package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.park.web.app.service.IWebRefundPreService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/web/app/message")
public class WebRefundPreController extends BaseController {
    @Resource
    private IWebRefundPreService webRefundPreService;

    @PostMapping("/refundPre/list")
    public ServerResponse<EGridResult> refundPreListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webRefundPreService.refundPreLoadGrid(page, rows));
    }

    @PostMapping("/refundPre/saveCheck")
    public ServerResponse<String> saveCheckHandler(@RequestBody RefundPreData data) throws GlobalException {
        return webRefundPreService.saveCheck(data);
    }

    @PostMapping("/refundPre/retry")
    public ServerResponse<Map<String, String>> retryHandler() throws GlobalException {
        return webRefundPreService.retry();
    }

    @PostMapping("/refundPre/review")
    public ServerResponse<Map<String, String>> reviewHandler(String transferNo) throws GlobalException {
        return webRefundPreService.review(transferNo);
    }

}
