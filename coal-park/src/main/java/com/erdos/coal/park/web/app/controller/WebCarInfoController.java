package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.web.app.entity.CarInfo;
import com.erdos.coal.park.web.app.service.IWebCarInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/carInfo")
public class WebCarInfoController extends BaseController {

    @Resource
    private IWebCarInfoService webCarInfoService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webCarInfoService.loadGrid(page, rows));
    }

    @PostMapping("/add_carInfo")
    public ServerResponse addCarTypeHandler(@RequestBody CarInfo carInfo) throws GlobalException {
        return webCarInfoService.addCarInfo(carInfo);
    }

    @PostMapping("/edit_carInfo")
    public ServerResponse editCarInfoHandler(@RequestBody CarInfo carInfo) throws GlobalException {
        return webCarInfoService.editCarInfo(carInfo);
    }

    @PostMapping("/del_carInfo")
    public ServerResponse delCarInfoHandler(@RequestBody CarInfo carInfo) throws GlobalException {
        return webCarInfoService.delCarInfo(carInfo);
    }

    @PostMapping("/car_list")
    public ServerResponse<EGridResult> carListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webCarInfoService.carsLoadGrid(page, rows));
    }

    @PostMapping("/verify_car")
    public ServerResponse verifyCarHandler() throws GlobalException {
        return webCarInfoService.verifyCar();
    }
}
