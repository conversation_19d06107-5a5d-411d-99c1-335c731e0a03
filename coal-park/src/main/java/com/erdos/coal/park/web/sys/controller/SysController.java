package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.entity.ServerInfo;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysMenu;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.utils.RandomValidateCodeUtil;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/web/sys")
public class SysController extends BaseController {
    /**
     * 生成验证码
     */
    @RequestMapping("/getVerify")
    public void getVerify() {
        try {
            response.setContentType("image/jpeg");//设置相应类型,告诉浏览器输出的内容为图片
            response.setHeader("Pragma", "No-cache");//设置响应头信息，告诉浏览器不要缓存此内容
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expire", 0);
            RandomValidateCodeUtil randomValidateCode = new RandomValidateCodeUtil();
            randomValidateCode.getRandomCode(request, response);//输出验证码图片方法
        } catch (Exception e) {
            logger.error("获取验证码失败>>>>   ", e);
        }
    }

    @RequestMapping("/getSysInfo")
    public ServerResponse treeObjHandler() throws GlobalException {
        return ServerResponse.createSuccess(new ServerInfo());
    }
}
