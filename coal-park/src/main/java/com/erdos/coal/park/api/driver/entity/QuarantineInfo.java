package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.pojo.GOrder;
import dev.morphia.annotations.*;

import java.util.Date;
import java.util.List;

@Entity(value = "t_quarantine_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")}),
        @Index(fields = {@Field("oid")})
})
public class QuarantineInfo extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "qua_id", background = true))
    private String id;
    //司机防疫申报
    private String did;             //司机编号(当接单的是app司机时，则为DriverInfo的ObjectID; 当接单的是微信司机时，则为WeChatDriverInfo的ObjectID)
    private DriverInfo driverInfo;  //司机信息

    private String defaultDownUnit; //二级单位编码
    private String subName;         //二级单位名称
    private String areaCode;        //场区编码

    private String nucleicAcidPho;   //核酸检测照片
    private String vaccinationPho;   //疫苗接种照片

    private String healthCodePho;   //健康码照片
    private String travelCardPho;   //行程卡照片
    private String temperaturePro;  //体温照片
    private String temperature;     //体温数值

    private String touchPro;    //是否同行密接照片

    private String healthColor;     //健康码颜色
    private String travelColor;     //行程卡颜色
    private Date healthCodeTime;    //健康码时间
    private Date nucleicAcidTime;   //核酸检测时间
    private String nucleicAcidDes;  //核酸检测结果
    private String vaccinationInfo; //疫苗接种信息（接种针次及接种时间）
    private Date travelCardTime;    //行程卡查询时间
    private String travelArea;      //行程卡信息（14天内所到之处）
    private boolean inHighRiskArea;     //是否到达高风险区
    private boolean passHighRiskArea;   //是否路过高风险区
    private boolean inMiddleRiskArea;   //是否到达中风险区
    private boolean passMiddleRiskArea; //是否路过中风险区

    private Integer audit;      //审核结果 0-未审核，1-自动审核通过，2-自动审核未通过，3-人工审核通过，4-人工审核未通过
    private String auditDes;    //审核结果描述：自动审核通过，自动审核未通过等待人工审核，审核通过，"人工审核未通的原因描述"
    private String auditor;     //审核员

    private String oid;         //订单号
    private String carNum;      //接单时司机的车牌号

    private String msg;

    private Integer type;   //0-接单前申报的信息，null和1-接单后入场前申报的信息

    private List<GOrder> orders;

    public List<GOrder> getOrders() {
        return orders;
    }

    public void setOrders(List<GOrder> orders) {
        this.orders = orders;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public DriverInfo getDriverInfo() {
        return driverInfo;
    }

    public void setDriverInfo(DriverInfo driverInfo) {
        this.driverInfo = driverInfo;
    }

    public String getDefaultDownUnit() {
        return defaultDownUnit;
    }

    public void setDefaultDownUnit(String defaultDownUnit) {
        this.defaultDownUnit = defaultDownUnit;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getNucleicAcidPho() {
        return nucleicAcidPho;
    }

    public void setNucleicAcidPho(String nucleicAcidPho) {
        this.nucleicAcidPho = nucleicAcidPho;
    }

    public String getVaccinationPho() {
        return vaccinationPho;
    }

    public void setVaccinationPho(String vaccinationPho) {
        this.vaccinationPho = vaccinationPho;
    }

    public String getHealthCodePho() {
        return healthCodePho;
    }

    public void setHealthCodePho(String healthCodePho) {
        this.healthCodePho = healthCodePho;
    }

    public String getTravelCardPho() {
        return travelCardPho;
    }

    public void setTravelCardPho(String travelCardPho) {
        this.travelCardPho = travelCardPho;
    }

    public String getTemperaturePro() {
        return temperaturePro;
    }

    public void setTemperaturePro(String temperaturePro) {
        this.temperaturePro = temperaturePro;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getTouchPro() {
        return touchPro;
    }

    public void setTouchPro(String touchPro) {
        this.touchPro = touchPro;
    }

    public String getHealthColor() {
        return healthColor;
    }

    public void setHealthColor(String healthColor) {
        this.healthColor = healthColor;
    }

    public String getTravelColor() {
        return travelColor;
    }

    public void setTravelColor(String travelColor) {
        this.travelColor = travelColor;
    }

    public Date getHealthCodeTime() {
        return healthCodeTime;
    }

    public void setHealthCodeTime(Date healthCodeTime) {
        this.healthCodeTime = healthCodeTime;
    }

    public Date getNucleicAcidTime() {
        return nucleicAcidTime;
    }

    public void setNucleicAcidTime(Date nucleicAcidTime) {
        this.nucleicAcidTime = nucleicAcidTime;
    }

    public String getNucleicAcidDes() {
        return nucleicAcidDes;
    }

    public void setNucleicAcidDes(String nucleicAcidDes) {
        this.nucleicAcidDes = nucleicAcidDes;
    }

    public String getVaccinationInfo() {
        return vaccinationInfo;
    }

    public void setVaccinationInfo(String vaccinationInfo) {
        this.vaccinationInfo = vaccinationInfo;
    }

    public Date getTravelCardTime() {
        return travelCardTime;
    }

    public void setTravelCardTime(Date travelCardTime) {
        this.travelCardTime = travelCardTime;
    }

    public String getTravelArea() {
        return travelArea;
    }

    public void setTravelArea(String travelArea) {
        this.travelArea = travelArea;
    }

    public boolean isInHighRiskArea() {
        return inHighRiskArea;
    }

    public void setInHighRiskArea(boolean inHighRiskArea) {
        this.inHighRiskArea = inHighRiskArea;
    }

    public boolean isPassHighRiskArea() {
        return passHighRiskArea;
    }

    public void setPassHighRiskArea(boolean passHighRiskArea) {
        this.passHighRiskArea = passHighRiskArea;
    }

    public boolean isInMiddleRiskArea() {
        return inMiddleRiskArea;
    }

    public void setInMiddleRiskArea(boolean inMiddleRiskArea) {
        this.inMiddleRiskArea = inMiddleRiskArea;
    }

    public boolean isPassMiddleRiskArea() {
        return passMiddleRiskArea;
    }

    public void setPassMiddleRiskArea(boolean passMiddleRiskArea) {
        this.passMiddleRiskArea = passMiddleRiskArea;
    }

    public Integer getAudit() {
        return audit;
    }

    public void setAudit(Integer audit) {
        this.audit = audit;
    }

    public String getAuditDes() {
        return auditDes;
    }

    public void setAuditDes(String auditDes) {
        this.auditDes = auditDes;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
