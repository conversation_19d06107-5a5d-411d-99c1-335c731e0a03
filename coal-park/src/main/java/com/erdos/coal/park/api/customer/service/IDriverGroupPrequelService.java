package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.DriverGroupPrequel;

import java.util.List;

public interface IDriverGroupPrequelService extends IBaseMongoService<DriverGroupPrequel> {
    //保存客商添加司机到司机组信息，并发送消息给司机
    void saveAndSend(List<DriverGroupPrequel> prequelList, String groupNo, String groupName);

    //推送司机被移出司机组消息
    void sendDelInformation(List<DriverGroupPrequel> prequelList, String groupNo, String groupName);

    //司机同意或拒绝 客商司机组的邀请
    ServerResponse<String> saveDvrGroupInvite(String groupNo, Integer manner);
}
