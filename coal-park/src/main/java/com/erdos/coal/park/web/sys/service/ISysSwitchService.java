package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;

import java.util.List;
import java.util.Map;

public interface ISysSwitchService extends IBaseMongoService<SysSwitch> {
    EGridResult loadGrid(Integer page, Integer rows);

    ServerResponse addSwitch(SysSwitch sysSwitch);

    ServerResponse deleteSwitch(SysSwitch sysSwitch);

    //sysSwitch 查询车辆设置几张照片
    Map<String, String> getCarPhoto(Integer type);

    //sysSwitch 查询司机设置几张照片
    Map<String, String> getDvrPhoto(Integer type);

    boolean isCheck(String type);

    //后台获取页面将要展示的 司机完善信息必传照片 多选框
    ServerResponse<List<CheckBoxData>> getDvrPhoTypes();

    //后台获取页面将要展示的 车辆完善信息必传照片 多选框
    ServerResponse<List<CheckBoxData>> getCarPhoTypes();
}
