package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

@Entity(value = "t_app_trade_contract_white_list", noClassnameStored = true)
public class TradeContractWhiteList extends BaseMongoInfo {
    private String bizContractCode;
    private String bizContractName;

    private String variety;

    public String getBizContractCode() {
        return bizContractCode;
    }

    public void setBizContractCode(String bizContractCode) {
        this.bizContractCode = bizContractCode;
    }

    public String getBizContractName() {
        return bizContractName;
    }

    public void setBizContractName(String bizContractName) {
        this.bizContractName = bizContractName;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }
}
