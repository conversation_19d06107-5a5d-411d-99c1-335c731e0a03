package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.DriverGroup;

public interface IDriverGroupService extends IBaseMongoService<DriverGroup> {

    //添加新司机组接口
    ServerResponse<DriverGroup> addDriverGroup(String groupName, String[] mobiles);

    //查询司机组司机接口
    //ServerResponse<List<DriverGroupData>> driverGroupListData(String groupNo);
    ServerResponse<EGridResult> driverGroupListData(String groupNo, Integer page, Integer rows);

    //删除司机组接口
    ServerResponse<String> delDriverGroup(String groupNo);

    //修改司机组
    ServerResponse<String> editDriverGroup(String groupNo, String groupName);

    //客商添加司机组，及按手机号添加组中司机
    ServerResponse<String> addDriverGroup2(String groupNo, String groupName, String[] mobiles);

    //移除司机组中司机
    ServerResponse<String> delDriver(String groupNo, String[] mobile);

    //查询司机组2
    ServerResponse<EGridResult> driverGroupListData2(String groupNo, Integer page, Integer rows);
}
