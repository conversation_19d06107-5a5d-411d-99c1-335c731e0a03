package com.erdos.coal.park.api.driver.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

public class DriverAccountPipeline implements Serializable {
    private String did;     //司机id\
    private BigDecimal totalFee = new BigDecimal("0");   //金额(分)

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }
}
