package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/web/app/settlement")
public class WebSettlementController extends BaseController {

    @RequestMapping(value = "/payment/list", produces = {"application/json;charset=UTF-8"})
    public String paylisHandler() {
        return "{\"total\":5,\"rows\":[\n" +
                "\t{\"id\":\"1\",\"mobile\":\"15712345678\",\"name\":\"黄锦欣\",\"carNum\":\"蒙KD0670\",\"date\":\"2019-05-05\",\"money\":\"4000\"},\n" +
                "\t{\"id\":\"2\",\"mobile\":\"13412345678\",\"name\":\"刘佰佩\",\"carNum\":\"冀A019SZ\",\"date\":\"2019-04-12\",\"money\":\"4500\"},\n" +
                "\t{\"id\":\"3\",\"mobile\":\"15612345678\",\"name\":\"代兵斗\",\"carNum\":\"黑RF0876\",\"date\":\"2019-03-20\",\"money\":\"5300\"},\n" +
                "\t{\"id\":\"4\",\"mobile\":\"18312345678\",\"name\":\"孙宏\",\"carNum\":\"冀ATJ932\",\"date\":\"2019-05-01\",\"money\":\"4800\"},\n" +
                "\t{\"id\":\"5\",\"mobile\":\"13812345678\",\"name\":\"谢建锋\",\"carNum\":\"冀ATH760\",\"date\":\"2019-04-05\",\"money\":\"5000\"}\n" +
                "]}";
    }

    @RequestMapping(value = "/freight/list", produces = {"application/json;charset=UTF-8"})
    public String frelisHandler() {
        return "{\"total\":5,\"rows\":[\n" +
                "\t{\"id\":\"1\",\"company\":\"乌兰集团\",\"name\":\"白明\",\"date\":\"2019-05-05\",\"money\":\"4100\"},\n" +
                "\t{\"id\":\"2\",\"company\":\"鄂尔多斯工业园\",\"name\":\"姜建勇\",\"date\":\"2019-04-12\",\"money\":\"4600\"},\n" +
                "\t{\"id\":\"3\",\"company\":\"华丰集团\",\"name\":\"张永会\",\"date\":\"2019-03-20\",\"money\":\"5400\"},\n" +
                "\t{\"id\":\"4\",\"company\":\"东山煤矿\",\"name\":\"王磊英\",\"date\":\"2019-05-01\",\"money\":\"4900\"},\n" +
                "\t{\"id\":\"5\",\"company\":\"花旗工业园\",\"name\":\"张孟明\",\"date\":\"2019-04-05\",\"money\":\"5000\"}\n" +
                "]}";
    }
}
