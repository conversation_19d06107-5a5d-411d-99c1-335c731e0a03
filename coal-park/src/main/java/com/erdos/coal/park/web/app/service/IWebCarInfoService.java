package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.web.app.entity.CarInfo;

public interface IWebCarInfoService extends IBaseMongoService<CarInfo> {

    EGridResult loadGrid(Integer page, Integer rows);

    ServerResponse addCarInfo(CarInfo carInfo);

    ServerResponse editCarInfo(CarInfo carInfo);

    ServerResponse delCarInfo(CarInfo carInfo);

    //加载所有车辆列表
    EGridResult carsLoadGrid(Integer page, Integer rows);

    //修改车辆信息
    ServerResponse verifyCar();
}
