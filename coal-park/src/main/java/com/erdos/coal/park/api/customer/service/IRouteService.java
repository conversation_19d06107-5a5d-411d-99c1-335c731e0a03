package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.Route;

import java.util.List;

public interface IRouteService extends IBaseMongoService<Route> {

    //地图调用总里程和过路费 接口
    ServerResponse<Object> testMapRoadLine(String origin, String destination);    //"&origin=" + startPoint + "&destination=" + endPoint;

    //添加自定义路线
    ServerResponse<Object> addRoute(String beginPoint, String endPoint, Double price, Long distance, Double tolls);

    //查询自定义路线
    //ServerResponse<List<Route>> searchRoute(String beginPoint, String endPoint);
    ServerResponse<EGridResult> searchRoute(String beginPoint, String endPoint, Integer page, Integer rows);

    //删除自定义路线
    ServerResponse<String> delRoute(String routeId);
}
