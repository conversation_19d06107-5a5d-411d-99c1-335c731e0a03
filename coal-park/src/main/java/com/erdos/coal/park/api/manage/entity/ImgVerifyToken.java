package com.erdos.coal.park.api.manage.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.Date;


@Entity(value = "t_img_verify_token", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("makeTime")}, options = @IndexOptions(expireAfterSeconds = 60))
})
public class ImgVerifyToken extends BaseMongoInfo {
    private String id;
    private int xpos;

    private String verifyToken;
    private Date makeTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getXpos() {
        return xpos;
    }

    public void setXpos(int xpos) {
        this.xpos = xpos;
    }

    public String getVerifyToken() {
        return verifyToken;
    }

    public void setVerifyToken(String verifyToken) {
        this.verifyToken = verifyToken;
    }

    public Date getMakeTime() {
        return makeTime;
    }

    public void setMakeTime(Date makeTime) {
        this.makeTime = makeTime;
    }
}
