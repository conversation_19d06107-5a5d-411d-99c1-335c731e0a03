package com.erdos.coal.park.api.customer.pojo;

import com.erdos.coal.park.web.sys.pojo.CheckBoxData;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class GOrderTaking implements Serializable {
    private String oid;         //订单号
    private String cid;         //客商编号
    private String did;      //司机编号(当接单的是app司机时，则为DriverInfo的ObjectID; 当接单的是微信司机时，则为WeChatDriverInfo的ObjectID)
    private String mobile;  //接单时司机的手机号
    private String carNum;  //接单时司机的车牌号
    private Integer finishTag;  //订单完成标识（0：接单成功；1：运输途中；2：订单完成）
    private String gid; //货运信息编号
    private Integer isHand; //是否手工下单0：扫码下单 1：是（手工下单） 2：批量下单（抢单）
    private Long loadTime;//装货日期
    private Integer delete;   //订单废除标记  0：订单请求废除
    private Integer driverIsAgree = 0;  //司机是否接受订单 - 0-司机还没有接受；1-司机接受订单；2-司机拒绝订单
    //0-订单还未提交预约，1-已经成功提交发货二级单位的预约，2-已经成功提交收货二级单位的预约
    private Integer sa = 0; //需要预约的二级单位司机是否已经提交预约（提交过预约，不代表预约成功，预约成功由企业系统决定，平台只记录提交过预约的记录）
    private Long aStartTime; //预约的起始时间
    private Long aEndTime; //预约的结束时间
    private Integer isPunchClock = 0; //车辆进场是否完成签到 0-否，1-发货二级单位签到完成，2-收货二级单位签到完成
    private Integer isQuarantine = 0; //车辆进场是否完成防疫申报 0-否，1-发货二级单位防疫申报完成，2-收货二级单位防疫申报完成
    private List<CheckBoxData> quarantineInfo;    //进场车辆防疫申报内容 健康码照片、行程卡照片、核酸检测照片、疫苗接种照片、体温值、体温照片
    private Integer isGrabNumber = 0;   //车辆是否完成抢号 0-发货二级单位抢号完成，2-收货二级单位抢号完成
    private Integer share;  //0-订单未分享给好友客商, 1-订单分享给好友客商
    private String shareCid;    //订单来自编号为cid的客商分享
    private String shareGid;    //订单来自编号为gid的客商货单分享

    private Date createTime;
    private Long updateTime;

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Integer getFinishTag() {
        return finishTag;
    }

    public void setFinishTag(Integer finishTag) {
        this.finishTag = finishTag;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public Integer getIsHand() {
        return isHand;
    }

    public void setIsHand(Integer isHand) {
        this.isHand = isHand;
    }

    public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    public Integer getDriverIsAgree() {
        return driverIsAgree;
    }

    public void setDriverIsAgree(Integer driverIsAgree) {
        this.driverIsAgree = driverIsAgree;
    }

    public Integer getSa() {
        return sa;
    }

    public void setSa(Integer sa) {
        this.sa = sa;
    }

    public Long getaStartTime() {
        return aStartTime;
    }

    public void setaStartTime(Long aStartTime) {
        this.aStartTime = aStartTime;
    }

    public Long getaEndTime() {
        return aEndTime;
    }

    public void setaEndTime(Long aEndTime) {
        this.aEndTime = aEndTime;
    }

    public Integer getIsPunchClock() {
        return isPunchClock;
    }

    public void setIsPunchClock(Integer isPunchClock) {
        this.isPunchClock = isPunchClock;
    }

    public Integer getIsQuarantine() {
        return isQuarantine;
    }

    public void setIsQuarantine(Integer isQuarantine) {
        this.isQuarantine = isQuarantine;
    }

    public List<CheckBoxData> getQuarantineInfo() {
        return quarantineInfo;
    }

    public void setQuarantineInfo(List<CheckBoxData> quarantineInfo) {
        this.quarantineInfo = quarantineInfo;
    }

    public Integer getIsGrabNumber() {
        return isGrabNumber;
    }

    public void setIsGrabNumber(Integer isGrabNumber) {
        this.isGrabNumber = isGrabNumber;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getShareCid() {
        return shareCid;
    }

    public void setShareCid(String shareCid) {
        this.shareCid = shareCid;
    }

    public String getShareGid() {
        return shareGid;
    }

    public void setShareGid(String shareGid) {
        this.shareGid = shareGid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
