package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.utils.DateUtils;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.web.sys.entity.Sample;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public interface ISampleService extends IBaseMongoService<Sample> {
    EGridResult slow(Integer page, Integer rows);

    EGridResult fast(Integer page, Integer rows);

    /**
     * 获取当前所有接口信息
     *
     * @return html 页面
     */
    String getApiListHtmlString();

    default String outHtmlString(List<AppLog> list) {
        StringBuilder str = new StringBuilder("<html>");

        str.append("<style type='text/css'>");
        str.append(" table {border-collapse: collapse; margin: 0 auto;text-align: center;width:100%}");
        str.append(" table td, table th {border: 1px solid #cad9ea;color: #666;height: 30px;}");
        str.append(" table thead th{background-color: #CCE8EB; width: 100px;}");
        str.append(" table tr:nth-child(odd){background: #fff;}");
        str.append(" table tr:nth-child(even){background: #F5FAFA;}");
        str.append("</style>");

        str.append("同一接口，只保留耗时最长记录.");

        str.append("<table>");

        str.append("<tr>");
        str.append("<th width='3%'>");
        str.append("序号");
        str.append("</th>");
        str.append("<th width='8%'>");
        str.append("用户类型");
        str.append("</th>");
        str.append("<th width='15%'>");
        str.append("方法名称");
        str.append("</th>");
        str.append("<th width='30%'>");
        str.append("方法描述");
        str.append("</th>");
        str.append("<th>");
        str.append("执行耗时(ms)");
        str.append("</th>");
        str.append("<th>");
        str.append("完成时间");
        str.append("</th>");
        str.append("</tr>");

        Map<String, Integer> internalMap = Maps.newHashMap();

        int i = 1;
        for (AppLog appLog : list) {

            // 去重
            if (internalMap.containsKey(appLog.getMethod()))
                continue;
            internalMap.put(appLog.getMethod(), 1);

            str.append("<tr>");

            str.append("<td>");
            str.append(i++);
            str.append("</td>");

            str.append("<td>");
            str.append(appLog.getUserType());
            str.append("</td>");
            str.append("<td>");
            str.append(appLog.getMethod());
            str.append("</td>");
            str.append("<td>");
            str.append(appLog.getDescription());
            str.append("</td>");
            str.append("<td>");
            str.append(appLog.getConsumes());
            str.append("</td>");
            str.append("<td>");
            str.append(DateUtils.format(appLog.getCreateTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
            str.append("</td>");

            str.append("</tr>");
        }
        str.append("</table>");

        str.append("</html>");
        return str.toString();
    }

    /**
     * 一小时内最慢请求
     *
     * @return html 页面
     */
    String getRequestListWithinAnHour();

    /**
     * 一天内最慢请求, 昨天当前时间 <= 范围 <= 今天当前时间
     *
     * @return html 页面
     */
    String getRequestListWithinAnDay();

    /**
     * 昨天一天内最慢请求,昨天0点 <= 范围 < 今天0点
     *
     * @return html 页面
     */
    String getRequestListWithinAnYesterday();

    /**
     * 今天最慢请求,0点到现在
     *
     * @return html 页面
     */
    String getRequestListWithinAnToday();
}
