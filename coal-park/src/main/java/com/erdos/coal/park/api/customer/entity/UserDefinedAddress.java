package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;
import dev.morphia.geo.Geometry;
import dev.morphia.utils.IndexType;

import java.io.Serializable;

@Entity(value = "t_user_defined_address", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field(value = "geometry", type = IndexType.GEO2DSPHERE)})
})
public class UserDefinedAddress extends BaseMongoInfo implements Serializable {
    private String addName;     //自定义名称
    private String fullName;    //地点全称
    //private Integer sign;       //地址类型（出发地0，目的地1）
    //private String location;    //地址坐标

    //@Indexed(value = IndexDirection.GEO2DSPHERE, name = "_geometry", background = true)
    private Geometry geometry;
    //点（Point）,线（LineString）,多边形（Polygon）,多点（MultiPoint）,多线（MultiLineString）,多个多边形（MultiPolygon）,几何集合（GeometryCollection）

    private String cid;         //客商编号
    private String unitCode;    //企业单位对应后台维护的地址

    private String id;  //ObjectId转String

    private String code;    //区划编码

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAddName() {
        return addName;
    }

    public void setAddName(String addName) {
        this.addName = addName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Geometry getGeometry() {
        return geometry;
    }

    public void setGeometry(Geometry geometry) {
        this.geometry = geometry;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
}
