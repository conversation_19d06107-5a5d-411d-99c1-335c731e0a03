package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.alibaba.push.bean.PushResult;
import com.erdos.coal.alibaba.push.service.PushService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.customer.dao.ICusMessageDao;
import com.erdos.coal.park.api.customer.entity.CusMessage;
import com.erdos.coal.park.api.driver.entity.PushInfo;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import com.erdos.coal.park.web.app.service.IWebPushService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("webPushService")
public class WebPushService implements IWebPushService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private ICusMessageDao cusMessageDao;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");

        List<PushResult> pushResultList = new ArrayList<>();
        int total = 0;
        try {
            Map<String, Object> map = PushService.testQueryPushList(page, rows, startTime, endTime, "NOTICE");
            if (map.get("error").equals("只能查询31天以内的记录")) {
                return new EGridResult(total, pushResultList.subList(0, pushResultList.size()));
            }
            List<PushResult> resultList = (List<PushResult>) map.get("resultList");
            total = (int) map.get("total");
            for (PushResult result : resultList) {
                PushResult pushResult = PushService.testQueryPushStatByMsg(result.getMessageId());
                result.setSentCount(pushResult.getSentCount());
                result.setReceivedCount(pushResult.getReceivedCount());
                result.setOpenedCount(pushResult.getOpenedCount());
                result.setDeletedCount(pushResult.getDeletedCount());

                pushResultList.add(result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        int size = total;
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }

        return new EGridResult(size, pushResultList.subList(fromIndex, toIndex));
    }

    @Override
    public EGridResult loadCusMessageGrid(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");

        Query<CusMessage> query = cusMessageDao.createQuery();
        if (StrUtil.isNotEmpty(startTime)) {
            Date startDate = IdUnit.weeHours(startTime, 0);
            query.criteria("createTime").greaterThanOrEq(startDate);
        }
        if (StrUtil.isNotEmpty(endTime)) {
            Date endDate = IdUnit.weeHours(endTime, 1);
            query.criteria("createTime").lessThan(endDate);
        }

        return cusMessageDao.findPage(page, rows, query);
    }

    @Override
    public EGridResult loadDvrMessageGrid(Integer page, Integer rows) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String type = request.getParameter("type");

        Query<PushInfo> query = pushInfoService.createQuery();
        if (StrUtil.isNotEmpty(startTime)) {
            Date startDate = IdUnit.weeHours(startTime, 0);
            query.criteria("createTime").greaterThanOrEq(startDate);
        }
        if (StrUtil.isNotEmpty(endTime)) {
            Date endDate = IdUnit.weeHours(endTime, 1);
            query.criteria("createTime").lessThan(endDate);
        }
        if (StrUtil.isNotEmpty(type)) query.criteria("type").equal(Integer.valueOf(type));

        return pushInfoService.findPage(page, rows, query);
    }
}
