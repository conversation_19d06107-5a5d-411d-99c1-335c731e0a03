package com.erdos.coal.park.api.driver.pojo;

import com.erdos.coal.utils.StrUtil;

import java.io.Serializable;

public class Receiver implements Serializable {
    private String type;
    private String account;
    private String name;
    private Integer amount;
    private String description;
    private String result;
    private String failReason;
    private String detailId;
    private String createTime;
    private String finishTime;

    @Override
    public String toString() {
        String toString = "";
        //if (StrUtil.isNotEmpty(type))

        return "{" +
                "type:'" + type + '\'' +
                ", account:'" + account + '\'' +
                ", name:'" + name + '\'' +
                ", amount:" + amount +
                ", description:'" + description + '\'' +
                ", result:'" + result + '\'' +
                ", failReason:'" + failReason + '\'' +
                ", detailId:'" + detailId + '\'' +
                ", createTime:'" + createTime + '\'' +
                ", finishTime:'" + finishTime + '\'' +
                '}';
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }
}
