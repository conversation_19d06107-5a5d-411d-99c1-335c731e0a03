package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.Date;

@Entity(value = "t_order_dimension",noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("makeTime")}, options = @IndexOptions(expireAfterSeconds = 40))
})
public class OrderDimension extends BaseMongoInfo {
    private String oid;
    private String md5Oid;

    //@Indexed(options = @IndexOptions(name = "_idx_makeTime", expireAfterSeconds = 40))
    private Date makeTime;  //二维码过期时间

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getMd5Oid() {
        return md5Oid;
    }

    public void setMd5Oid(String md5Oid) {
        this.md5Oid = md5Oid;
    }

    public Date getMakeTime() {
        return makeTime;
    }

    public void setMakeTime(Date makeTime) {
        this.makeTime = makeTime;
    }
}
