package com.erdos.coal.park.api.manage.service.impl;

import com.aliyun.oss.model.OSSObject;
import com.erdos.coal.alibaba.oss.service.OssService;
import com.erdos.coal.config.CoalConfig;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.utils.FastDFSClientUtil;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

@Service("photoFileService")
public class PhotoFileServiceImpl implements IPhotoFileService {
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private CoalConfig coalConfig;


    @Override
    public String readPhoto0(String photoFileName, String dbTable) {
        return fileInfoDao.readFile(photoFileName, dbTable);
    }

    @Override
    public String uploadPhoto0(String uid, MultipartFile photoFile) {
        return fileInfoDao.fileSaveDisc(uid, photoFile);
    }

    @Override
    public String uploadPhotoByUid(String uid, String dbTable, MultipartFile photoFile) {
        fileInfoDao.deleteByUId(uid, dbTable);
        return fileInfoDao.fileSave(uid, photoFile, dbTable);
    }

    @Override
    public String uploadPhotoByDB(String uid, String dbTable, String photoFileName) {
        fileInfoDao.deleteDB(uid, dbTable);
        return fileInfoDao.fileSaveDB(photoFileName, dbTable);
    }

    @Override
    public Map<String, String> uploadPhotosByUid(String userId, String dbTable, Map<String, MultipartFile> photoFilesMap) {
        Map<String, String> resultPathMap = new HashMap<>();
        for (String key : photoFilesMap.keySet()) {
            String uid = userId + key;
            MultipartFile value = photoFilesMap.get(key);
            try {
                String path = uploadPhotoByUid(uid, dbTable, value);
                resultPathMap.put(key + "Path", path);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return resultPathMap;
    }

    @Override
    public Map<String, String> uploadPhotosByDB(String userId, String dbTable, Map<String, String> photoFileNamesMap) {
        Map<String, String> resultPathMap = new HashMap<>();
        for (String key : photoFileNamesMap.keySet()) {
            String uid = userId + key;
            String value = photoFileNamesMap.get(key);
            try {
                String path = uploadPhotoByDB(uid, dbTable, value);
                resultPathMap.put(key + "Path", path);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return resultPathMap;
    }


    @Override
    public String uploadPhoto(MultipartFile photoFile) {
        String path = null;
        try {
            String extension = FilenameUtils.getExtension(photoFile.getOriginalFilename());
            Map<String, Object> map = FastDFSClientUtil.uploadFiled(photoFile.getBytes(), photoFile.getSize(), extension, true);
            path = (String) map.get("img");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return path;
    }

    @Override
    public Map<String, String> uploadPhotos(Map<String, MultipartFile> photoMap) {

        Map<String, String> resultPathMap = new HashMap<>();
        for (String key : photoMap.keySet()) {
            MultipartFile value = photoMap.get(key);
            try {
                String path = uploadPhoto(value);
                resultPathMap.put(key + "Path", path);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return resultPathMap;
    }

    /**
     * 上传图片到oss
     *
     * @param key
     * @param inputStream
     * @return
     */
    @Override
    public String putObject(String key, InputStream inputStream) {
        OssService.putObject(key, inputStream);
        return key;
    }

    /**
     * 上传图片到oss
     *
     * @param key
     * @param file
     * @return
     */
    @Override
    public String putObject(String key, File file) {
        OssService.putObject(key, file);
        return key;
    }

    /**
     * 上传图片到oss
     *
     * @param dbPath
     * @param tableName
     * @return
     */
    @Override
    public void putObject(String dbPath, String tableName) {
        File file = new File(coalConfig.uploadPath + File.separator + dbPath);
        if (!file.exists()) {
            readPhoto0(dbPath, tableName);        //图片不存在，从数据库读到磁盘
            file = new File(coalConfig.uploadPath + File.separator + dbPath);
        }
        //磁盘文件存在，则上传图片到oss
        if (file.exists()) putObject(dbPath, file);
    }

    /**
     * 生成访问图片的URL
     *
     * @param objectName
     * @return
     */
    @Override
    public String signUrl(String objectName) {
        URL url = OssService.getObject1(objectName);
        return url.toString();
    }

    /**
     * 获取已上传图片到InputStream
     *
     * @param objectName
     * @return
     */
    @Override
    public byte[] getBytes(String objectName) {
        try {
            return OssService.getBytes(objectName);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 判断图片是否存在oss
     *
     * @param objectName
     * @return
     */
    @Override
    public boolean isOSSObjectNUll(String objectName) {
        OSSObject ossObject = OssService.getObject(objectName);
        return ObjectUtils.isEmpty(ossObject);
    }

    /**
     * 生成访问图片的URL
     *
     * @param objectName
     * @return
     */
    @Override
    public URL signUrl2(String objectName) {
        return OssService.getObject1(objectName);
    }


    /**
     * 上传图片到oss(public_read)
     *
     * @param key
     * @param inputStream
     * @return
     */
    @Override
    public String putObjectPublicRead(String key, InputStream inputStream) {
        OssService.putObjectToPublicRead(key, inputStream);
        return key;
    }

    /**
     * 生成访问图片的URL(public_read)
     *
     * @param objectName
     * @return
     */
    @Override
    public String publicReadUrl(String objectName) {
        return OssService.getObjectWithPublicRead(objectName);
    }
}
