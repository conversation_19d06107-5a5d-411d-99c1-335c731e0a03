package com.erdos.coal.park.api.driver.entity;

import com.alipay.api.domain.TradeFundBill;
import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.util.Date;
import java.util.List;

@Entity(value = "t_pay_ali_sresult", noClassnameStored = true)
public class PayAliSResult extends BaseMongoInfo {
    private String trade_no;//支付宝交易号
    private String out_trade_no;//商家订单号
    private String buyer_logon_id;//买家支付宝账号
    private String trade_status;//	交易状态：WAIT_BUYER_PAY（交易创建，等待买家付款）、TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、
    // TRADE_SUCCESS（交易支付成功）、TRADE_FINISHED（交易结束，不可退款）
    private String total_amount;//Price	必填	11	交易的订单金额，单位为元，两位小数。该参数的值为支付时传入的total_amount
    private String trans_currency;//标价币种，该参数的值为支付时传入的trans_currency，
    // 支持英镑：GBP、港币：HKD、美元：USD、新加坡元：SGD、日元：JPY、加拿大元：CAD、澳元：AUD、欧元：EUR、新西兰元：NZD、
    // 韩元：KRW、泰铢：THB、瑞士法郎：CHF、瑞典克朗：SEK、丹麦克朗：DKK、挪威克朗：NOK、马来西亚林吉特：MYR、印尼卢比：IDR、
    // 菲律宾比索：PHP、毛里求斯卢比：MUR、以色列新谢克尔：ILS、斯里兰卡卢比：LKR、俄罗斯卢布：RUB、阿联酋迪拉姆：AED、捷克克朗：CZK、
    // 南非兰特：ZAR、人民币：CNY、新台币：TWD。当trans_currency 和 settle_currency 不一致时，trans_currency支持人民币：CNY、新台币：TWD
    private String settle_currency;//订单结算币种，对应支付接口传入的settle_currency
    private String settle_amount;//	Price	选填	11	结算币种订单金额
    private String pay_currency;//	Price	选填	8	订单支付币种
    private String pay_amount;//支付币种订单金额
    private String settle_trans_rate;//结算币种兑换标价币种汇率
    private String trans_pay_rate;//标价币种兑换支付币种汇率
    private String buyer_pay_amount;//Price	选填	11	买家实付金额，单位为元，两位小数。该金额代表该笔交易买家实际支付的金额，不包含商户折扣等金额	8.88
    private String point_amount;//Price	选填	11	积分支付的金额，单位为元，两位小数。该金额代表该笔交易中用户使用积分支付的金额，比如集分宝或者支付宝实时优惠等	10
    private String invoice_amount;//	Price	选填	11	交易中用户支付的可开具发票的金额，单位为元，两位小数。该金额代表该笔交易中可以给用户开具发票的金额	12.11
    private Date send_pay_date;//	Date	选填	32	本次交易打款给卖家的时间	2014-11-27 15:45:57
    private String receipt_amount;//实收金额，单位为元，两位小数。该金额为本笔交易，商户账户能够实际收到的金额
    private String store_id;//商户门店编号
    private String terminal_id;//商户机具终端编号
    private List<TradeFundBill> fund_bill_list;//	TradeFundBill 必填		交易支付使用的资金渠道
    private String store_name;//请求交易支付中的商户店铺的名称
    private String buyer_user_id;//买家在支付宝的用户id
    private String charge_amount;//该笔交易针对收款方的收费金额； 默认不返回该信息，需与支付宝约定后配置返回；
    private String charge_flags;//费率活动标识，当交易享受活动优惠费率时，返回该活动的标识；默认不返回该信息，需与支付宝约定后配置返回；
    //可能的返回值列表：蓝海活动标识：bluesea_1
    private String settlement_id;//支付清算编号，用于清算对账使用； 只在银行间联交易场景下返回该信息；
    private String auth_trade_pay_mode;//	预授权支付模式，该参数仅在信用预授权支付场景下返回。信用预授权支付：CREDIT_PREAUTH_PAY
    private String buyer_user_type;//买家用户类型。CORPORATE:企业用户；PRIVATE:个人用户。
    private String mdiscount_amount;//商家优惠金额
    private String discount_amount;//平台优惠金额
    private String buyer_user_name;//买家名称；买家为个人用户时为买家姓名，买家为企业用户时为企业名称； //默认不返回该信息，需与支付宝约定后配置返回
    private String subject;//订单标题；
    private String body;//订单描述;
    private String alipay_sub_merchant_id;//间连商户在支付宝端的商户编号；
    private String ext_infos;//交易额外信息，特殊场景下与支付宝约定返回。json格式。	{"action":"cancel"}

    public String getTrade_no() {
        return trade_no;
    }

    public void setTrade_no(String trade_no) {
        this.trade_no = trade_no;
    }

    public String getOut_trade_no() {
        return out_trade_no;
    }

    public void setOut_trade_no(String out_trade_no) {
        this.out_trade_no = out_trade_no;
    }

    public String getBuyer_logon_id() {
        return buyer_logon_id;
    }

    public void setBuyer_logon_id(String buyer_logon_id) {
        this.buyer_logon_id = buyer_logon_id;
    }

    public String getTrade_status() {
        return trade_status;
    }

    public void setTrade_status(String trade_status) {
        this.trade_status = trade_status;
    }

    public String getTotal_amount() {
        return total_amount;
    }

    public void setTotal_amount(String total_amount) {
        this.total_amount = total_amount;
    }

    public String getTrans_currency() {
        return trans_currency;
    }

    public void setTrans_currency(String trans_currency) {
        this.trans_currency = trans_currency;
    }

    public String getSettle_currency() {
        return settle_currency;
    }

    public void setSettle_currency(String settle_currency) {
        this.settle_currency = settle_currency;
    }

    public String getSettle_amount() {
        return settle_amount;
    }

    public void setSettle_amount(String settle_amount) {
        this.settle_amount = settle_amount;
    }

    public String getPay_currency() {
        return pay_currency;
    }

    public void setPay_currency(String pay_currency) {
        this.pay_currency = pay_currency;
    }

    public String getPay_amount() {
        return pay_amount;
    }

    public void setPay_amount(String pay_amount) {
        this.pay_amount = pay_amount;
    }

    public String getSettle_trans_rate() {
        return settle_trans_rate;
    }

    public void setSettle_trans_rate(String settle_trans_rate) {
        this.settle_trans_rate = settle_trans_rate;
    }

    public String getTrans_pay_rate() {
        return trans_pay_rate;
    }

    public void setTrans_pay_rate(String trans_pay_rate) {
        this.trans_pay_rate = trans_pay_rate;
    }

    public String getBuyer_pay_amount() {
        return buyer_pay_amount;
    }

    public void setBuyer_pay_amount(String buyer_pay_amount) {
        this.buyer_pay_amount = buyer_pay_amount;
    }

    public String getPoint_amount() {
        return point_amount;
    }

    public void setPoint_amount(String point_amount) {
        this.point_amount = point_amount;
    }

    public String getInvoice_amount() {
        return invoice_amount;
    }

    public void setInvoice_amount(String invoice_amount) {
        this.invoice_amount = invoice_amount;
    }

    public Date getSend_pay_date() {
        return send_pay_date;
    }

    public void setSend_pay_date(Date send_pay_date) {
        this.send_pay_date = send_pay_date;
    }

    public String getReceipt_amount() {
        return receipt_amount;
    }

    public void setReceipt_amount(String receipt_amount) {
        this.receipt_amount = receipt_amount;
    }

    public String getStore_id() {
        return store_id;
    }

    public void setStore_id(String store_id) {
        this.store_id = store_id;
    }

    public String getTerminal_id() {
        return terminal_id;
    }

    public void setTerminal_id(String terminal_id) {
        this.terminal_id = terminal_id;
    }

    public List<TradeFundBill> getFund_bill_list() {
        return fund_bill_list;
    }

    public void setFund_bill_list(List<TradeFundBill> fund_bill_list) {
        this.fund_bill_list = fund_bill_list;
    }

    public String getStore_name() {
        return store_name;
    }

    public void setStore_name(String store_name) {
        this.store_name = store_name;
    }

    public String getBuyer_user_id() {
        return buyer_user_id;
    }

    public void setBuyer_user_id(String buyer_user_id) {
        this.buyer_user_id = buyer_user_id;
    }

    public String getCharge_amount() {
        return charge_amount;
    }

    public void setCharge_amount(String charge_amount) {
        this.charge_amount = charge_amount;
    }

    public String getCharge_flags() {
        return charge_flags;
    }

    public void setCharge_flags(String charge_flags) {
        this.charge_flags = charge_flags;
    }

    public String getSettlement_id() {
        return settlement_id;
    }

    public void setSettlement_id(String settlement_id) {
        this.settlement_id = settlement_id;
    }

    public String getAuth_trade_pay_mode() {
        return auth_trade_pay_mode;
    }

    public void setAuth_trade_pay_mode(String auth_trade_pay_mode) {
        this.auth_trade_pay_mode = auth_trade_pay_mode;
    }

    public String getBuyer_user_type() {
        return buyer_user_type;
    }

    public void setBuyer_user_type(String buyer_user_type) {
        this.buyer_user_type = buyer_user_type;
    }

    public String getMdiscount_amount() {
        return mdiscount_amount;
    }

    public void setMdiscount_amount(String mdiscount_amount) {
        this.mdiscount_amount = mdiscount_amount;
    }

    public String getDiscount_amount() {
        return discount_amount;
    }

    public void setDiscount_amount(String discount_amount) {
        this.discount_amount = discount_amount;
    }

    public String getBuyer_user_name() {
        return buyer_user_name;
    }

    public void setBuyer_user_name(String buyer_user_name) {
        this.buyer_user_name = buyer_user_name;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getAlipay_sub_merchant_id() {
        return alipay_sub_merchant_id;
    }

    public void setAlipay_sub_merchant_id(String alipay_sub_merchant_id) {
        this.alipay_sub_merchant_id = alipay_sub_merchant_id;
    }

    public String getExt_infos() {
        return ext_infos;
    }

    public void setExt_infos(String ext_infos) {
        this.ext_infos = ext_infos;
    }
}
