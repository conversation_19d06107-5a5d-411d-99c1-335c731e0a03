package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.Date;

public interface IDriverAccountService extends IBaseMongoService<DriverAccount> {
    //司机账户金额查询
    ServerResponse<BigDecimal> queryAccount();

    //司机账户可用金额查询
    BigDecimal getAvailableFee(String did);

    //撤销订单，将司机付的金额返回司机账户
    ServerResponse<String> saveFees(String did, Integer fees, String oid);

    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
    Document createDriAccountDoc(String did, String payerId, BigDecimal fee, Integer type, String oid, Date time);

    Document createDriAccountDoc(String did, String payerId, BigDecimal fee, Integer type, String outTradeNo, String transactionId, Date time);

    Document createDriAccountDoc(String did, BigDecimal fee, Integer type, Date time, String preferentialRefundNo);

    //司机查询账户交易明细
    ServerResponse<EGridResult> driverAccountList(Integer page, Integer rows);
}
