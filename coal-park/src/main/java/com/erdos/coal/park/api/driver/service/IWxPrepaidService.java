package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.WxPrepaid;
import com.erdos.coal.park.api.driver.entity.WxResult;

import java.util.Map;

public interface IWxPrepaidService extends IBaseMongoService<WxPrepaid> {

    //---微信给司机账户账户充值
    ServerResponse<WxResult> wxQueryPay(String transactionId, String outTradeNo, String cdid, String driOrCus);

    WxResult payResult(Map<String, String> map, Integer flag, String cdid, String driOrCus);

    ServerResponse<Map<String, String>> wxRequestPay(String body, Integer totalFee, String did, String driOrCus);

    //---微信给客商账户充值
    //微信请求生成支付订单-客商给自己充值
    ServerResponse<Map<String, String>> wxRequestPay3(String body, Integer totalFee);

    //查询 客商微信支付货运信息或者给自己充值 结果
    ServerResponse<WxResult> wxQueryPay2(String transactionId, String outTradeNo);

    //客商微信支付货运信息或者给自己充值结果处理
    WxResult payResult2(Map<String, String> map, Integer flag, String cdid);

    //---微信小程序支付
    ServerResponse<WxResult> weChatQueryPay(String transactionId, String outTradeNo);

    //微信支付完成，微信服务 支付通知，平台业务处理
    WxResult weChatPayResult(String userId, Map<String, String> map, Integer flag);
    //微信支付完成，微信服务 支付通知，平台业务处理 -- 充值分账
    WxResult weChatPayResult3(String userId, Map<String, String> map, Integer flag);
    //微信支付完成，微信服务 支付通知，平台业务处理 -- 接单分账
    WxResult weChatPayResult2(Map<String, String> map, Integer flag);

    //用户在商户侧使用微信支付请求支付（充值） -- 不分账
    ServerResponse<Map<String, String>> weChatRequestPay(String openid, String body, int totalFee, String payeeid, Integer useType, Integer shareFee, String carNum, String driverId);
    //用户在商户侧使用微信支付请求支付（充值） -- 分账
    ServerResponse<Map<String, String>> weChatRequestPay3(String openid, String body, int totalFee, String payeeid, Integer useType, Integer shareFee, String carNum, String driverId);
    //用户在商户侧使用微信支付请求支付（接单） -- 分账
    ServerResponse<Map<String, String>> weChatRequestPay2(String openid, WxPrepaid wxPrepaid);

}
