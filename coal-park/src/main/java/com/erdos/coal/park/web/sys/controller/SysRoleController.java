package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.entity.SysRole;
import com.erdos.coal.park.web.sys.service.ISysRoleService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/web/sys/role")
public class SysRoleController extends BaseController {

    @Resource
    private ISysRoleService roleService;

    // for vue ------------------------------------------------------------

    @PostMapping("/role_list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(roleService.loadGrid(page, rows));
    }

    @PostMapping("/append_role")
    public ServerResponse addHandler(@RequestBody SysRole sysRole) throws GlobalException {
        return ServerResponse.createSuccess(roleService.addRole(sysRole));
    }

    @PostMapping("/edit_role")
    public ServerResponse editHandler(@RequestBody SysRole sysRole) throws GlobalException {
        return ServerResponse.createSuccess(roleService.editRole(sysRole));
    }

    @PostMapping("/delete_role")
    public ServerResponse deleteHandler(@RequestBody SysRole sysRole) throws GlobalException {
        return ServerResponse.createSuccess(roleService.deleteRole(sysRole));
    }

    // for vue ------------------------------------------------------------

    @RequestMapping("/role_menu_list")
    public ServerResponse<EGridResult> roleMenuListHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(roleService.loadTreeGrid(page, rows));
    }

    @RequestMapping("role_sel")
    public List<HashMap<String, Object>> roleSelHandler() throws GlobalException {
        return roleService.roleSel();
    }
}
