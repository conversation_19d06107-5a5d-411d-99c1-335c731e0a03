package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import com.erdos.coal.park.api.manage.pojo.WeChatData;
import com.erdos.coal.park.api.manage.service.IImgVerifyTokenService;
import com.erdos.coal.park.api.manage.service.IRangerService;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"司机管理接口列表"
@RestController
@RequestMapping("/api/manage/dri")
public class DriverManageController extends BaseController {
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;
    @Resource
    private IRangerService rangerService;
    @Resource
    private IImgVerifyTokenService iImgVerifyTokenService;

    //注册 -------------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "司机注册接口") //日志
    @PostMapping(value = "/reg2")
    public ServerResponse<AccessToken> reg2Handler(
            @RequestParam(value = "phoneId") String phoneId,   //"手机序列号"
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "code") String code,         //"短信验证码"
            @RequestParam(value = "deviceId") String deviceId  //"设备id"
    ) throws GlobalException {
        return driverInfoService.driverUserReg2(phoneId, mobile, code, deviceId);
    }

    //登录 -------------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "司机登录接口") //日志
    @PostMapping(value = "/login2")
    public ServerResponse<AccessToken> login2Handler(
            @RequestParam(value = "phoneId") String phoneId,   //"手机序列号"
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "code", required = false) String code,         //"短信验证码"
            @RequestParam(value = "deviceId") String deviceId  //"设备id"
    ) throws GlobalException {
        return driverInfoService.driverUserLogin2(phoneId, mobile, code, deviceId);
    }

    @InvokeLog(description = "司机登录接口") //日志
    @PostMapping(value = "/login3")
    public ServerResponse<AccessToken> login3Handler(
            @RequestParam(value = "phoneId") String phoneId,   //"手机序列号"
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "code", required = false) String code,         //"短信验证码"
            @RequestParam(value = "deviceId") String deviceId,  //"设备id"
            @RequestParam(value = "imgCode", required = false) String imgCode    //"图片滑块验证成功授权码"
    ) throws GlobalException {
        if ((StrUtil.isEmpty(code) && iImgVerifyTokenService.checkImaVerifyToken(imgCode)) || (StrUtil.isNotEmpty(code))) {
            return driverInfoService.driverUserLogin2(phoneId, mobile, code, deviceId);
        } else {
            return ServerResponse.createError("验证失败！");
        }
    }

    @InvokeLog(description = "校验司机是否更换微信接口") //日志
    @PostMapping(value = "/check_phoneId")
    public ServerResponse<Boolean> checkPhoneIdHandler(
            @RequestParam(value = "phoneId") String phoneId,   //"手机序列号"
            @RequestParam(value = "mobile") String mobile     //"手机号码"
    ) throws GlobalException {
        return driverInfoService.checkPoneId(mobile, phoneId);
    }

    //微信小程序授权---------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "微信小程序授权 接口") //日志
    @PostMapping(value = "/weChatAuth")
    public ServerResponse<WeChatData> weChatAuthHandler(
            @RequestParam(value = "code") String code   //"登录时获取的 code"
    ) throws GlobalException {
        //return wechatDriverInfoService.wechatUserLogin(code);
        return wechatDriverInfoService.wechatUserAuth(code);
        //return loginManageHandler.wechatUserLogin(code);
    }

    //注册和登录 -------------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "用户登录接口") //日志
    @PostMapping(value = "/ranger_login")
    public ServerResponse<AccessToken> rangerLoginHandler(
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "password") String password,  //"密码"
            @RequestParam(value = "code", required = false) String code         //"短信验证码"
    ) throws GlobalException {
        return rangerService.regAndLogin(mobile, password, code);
    }

    @InvokeLog(description = "智运通司机注册接口") //日志
    @PostMapping(value = "/zyt_reg")
    public ServerResponse<String> zytRegHandler(
            @RequestParam(value = "zytOpenid") String zytOpenid,   //"openid"
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "code") String code         //"短信验证码"
    ) throws GlobalException {
        return driverInfoService.zytDriverReg(zytOpenid, mobile, code);
    }

    @InvokeLog(description = "智运通司机登录接口") //日志
    @PostMapping(value = "/zyt_login")
    public ServerResponse<DriverInfo> zytLoginHandler(
            @RequestParam(value = "zytOpenid") String zytOpenid,   //"openid"
            @RequestParam(value = "mobile") String mobile,     //"手机号码"
            @RequestParam(value = "code", required = false) String code         //"短信验证码"
    ) throws GlobalException {
        return driverInfoService.zytDriverLogin(zytOpenid, mobile, code);
    }

    @InvokeLog(description = "智运通司机更新token接口") //日志
    @PostMapping(value = "/zyt_get_token")
    public ServerResponse<AccessToken> zytGetTokenHandler(
            @RequestParam(value = "zytOpenid") String zytOpenid,   //"openid"
            @RequestParam(value = "mobile") String mobile     //"手机号码"
    ) throws GlobalException {
        return driverInfoService.zytGetDvrToken(zytOpenid, mobile);
    }
}
