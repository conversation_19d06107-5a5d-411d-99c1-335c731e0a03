package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.manage.dao.IAppLogDao;
import com.erdos.coal.park.api.manage.entity.AppLog;
import com.erdos.coal.park.web.sys.service.ISysLogService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

//只做web端查询使用
@Service("sysLogService")
public class SysLogServiceImpl extends BaseMongoServiceImpl<AppLog, IAppLogDao> implements ISysLogService {

    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) throws ParseException {
        String user = request.getParameter("user");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        String description = request.getParameter("description");

        Query<AppLog> query = this.createQuery();
        if (StrUtil.isNotEmpty(startDate)) {
            query.filter("createTime >= ", IdUnit.weeHours(startDate, 0));
        }
        if (StrUtil.isNotEmpty(endDate)) {
            query.filter("createTime <= ", IdUnit.weeHours(endDate, 1));
        }
        if (StrUtil.isNotEmpty(user)) {
            query.filter("user", user);
        }
        if (StrUtil.isNotEmpty(description))
            query.criteria("description").contains(description);

        query.order(Sort.descending("createTime"));
        EGridResult eGridResult = this.findPage(page, rows, query);
        return eGridResult;
    }
}
