package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_push_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("did")}),
        @Index(fields = {@Field("oid")}),
        @Index(fields = {@Field("type")}),
        @Index(fields = {@Field("createTime")})
})
public class PushInfo extends BaseMongoInfo {

    private String title;  //标题
    private String body;    //内容
    private String did;     //司机id
    private Boolean read;   //是否已读 true已读，false未读
    private Integer type;   //0:待抢订单    1:派单    2:抢单成功  6:撤单    （未用到  3：到达目的地   4：扣款及账户金额   5：账户充值 ）,7:审核结果,8:叫号，9装卸,10客商邀请司机加入司机组
    private String oid;     //撤销 订单id
    private String groupNo; //邀请司机加入司机组编号

    private String errCode;
    private String errMsg;
    private String msGid;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public Boolean getRead() {
        return read;
    }

    public void setRead(Boolean read) {
        this.read = read;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getMsGid() {
        return msGid;
    }

    public void setMsGid(String msGid) {
        this.msGid = msGid;
    }
}
