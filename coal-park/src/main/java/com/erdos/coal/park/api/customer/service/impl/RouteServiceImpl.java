package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.map.service.MapService;
import com.erdos.coal.park.api.customer.dao.IRouteDao;
import com.erdos.coal.park.api.customer.entity.Route;
import com.erdos.coal.park.api.customer.service.IRouteService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("routeService")
public class RouteServiceImpl extends BaseMongoServiceImpl<Route, IRouteDao> implements IRouteService {
    @Resource
    private IHttpAPIService httpAPIService;
    @Resource
    private ILockedService lockedService;

    /**
     * 地图调用总里程和过路费
     * param String origin, 起点坐标
     * param String destination 终点坐标
     */
    @Override
    public ServerResponse<Object> testMapRoadLine(String origin, String destination) {
        /*String url = "https://restapi.amap.com/v3/direction/driving?output=json&key=209c9214120f1670b9053b298afdc619";
        url += "&origin=" + origin + "&destination=" + destination;
        String str;

        double distance = 0.0;
        String tolls = "";
        try {
            str = httpAPIService.doGet(url);

            JSONObject jsonObject = JSON.parseObject(str);
            if (!StrUtil.isEmpty(jsonObject.get("info")) && !"OK".equals(jsonObject.get("info")))
                return ServerResponse.createError((String) jsonObject.get("info"));
            JSONObject route = jsonObject.getJSONObject("route");
            JSONArray paths = route.getJSONArray("paths");
            Object path = paths.get(0);
            JSONObject pathObj = JSON.parseObject(path.toString());
            distance = Double.valueOf((String) pathObj.get("distance"));
            tolls = (String) pathObj.get("tolls");

//            "distance":"260375",总里程
//            "duration":"14107",总耗时
//            "strategy":"速度最快",
//            "tolls":"90",过路费
//            "toll_distance":"224410",
        } catch (Throwable e) {
            e.printStackTrace();
        }
        String resultStr = "{\"distance\":" + distance / 1000 + ",\"tolls\":" + tolls + "}";*/
        String resultStr = MapService.drivingRoundBaiDu(origin, destination);
        return ServerResponse.createSuccess("", JSON.parse(resultStr));
    }

    /**
     * 添加自定义路线
     * param：String beginPoint, 起点
     * param：String endPoint,终点
     * param：Double price,单价
     * param：Long distance总里程
     * return：routeId,新添加的自定义路线的编号
     */
    @Override
    public ServerResponse<Object> addRoute(String beginPoint, String endPoint, Double price, Long distance, Double tolls) {
        String cid = ShiroUtils.getUserId();

        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.Add_Defined_Route.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {
            try {
                Route route = new Route();
                route.setCid(cid);
                route.setBeginPoint(beginPoint);
                route.setEndPoint(endPoint);
                route.setPrice(price);
                route.setDistance(distance);
                route.setTolls(tolls);
                Route result = this.save(route);

                String str = "{\"routeId\":\"" + result.getObjectId().toString() + "\"}";
                return ServerResponse.createSuccess("添加成功", JSON.parse(str));
            } finally {
                lockedService.unLock(cid, SysConstants.LockType.Add_Defined_Route.getType());
            }
        }
    }

    /**
     * 查询自定义路线接口
     */
    @Override
    public ServerResponse<EGridResult> searchRoute(String beginPoint, String endPoint, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        Query<Route> query = this.createQuery();
        query.filter("cid", cid);
        if (!StrUtil.isEmpty(beginPoint)) {
            query.filter("beginPoint", beginPoint);
        }
        if (!StrUtil.isEmpty(endPoint)) {
            query.filter("endPoint", endPoint);
        }
        //List<Route> list = this.list(query);
        //return ServerResponse.createSuccess("查询成功", list);
        return ServerResponse.createSuccess("查询成功", findPage(page, rows, query));
    }

    /**
     * 删除自定义路线 接口
     * param: routeId 自定义路线的编号
     */
    @Override
    public ServerResponse<String> delRoute(String routeId) {
        Route route = this.getByPK(routeId);
        if (route != null) {
            this.delete(route.getObjectId());
        }
        return ServerResponse.createSuccess("编号为：【" + routeId + "】的自定义路线删除成功");
    }
}
