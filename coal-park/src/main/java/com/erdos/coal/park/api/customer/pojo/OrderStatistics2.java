package com.erdos.coal.park.api.customer.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class OrderStatistics2 implements Serializable {

    private String cid;             //客商编号
    private Integer mold;           //物流类型
    private Integer tranStatus;     //物流类型
    private String tranDate;        //日期
    private Integer numberOfCars0;  //下单车数
    private Integer numberOfCars1;  //接单车数
    private Integer numberOfCars2;  //入场车数
    private Integer numberOfCars3;  //撤单车数
    private BigDecimal outNetWeight;    //发货完成净重
    private BigDecimal inNetWeight;     //收货完成净重
    private Integer numberOfCars4;  //完成车数

    private List<Integer> numberOfCars;  //车数

    private Double outGrossWeight7;
    private Double outTareWeight7;
    private Double inGrossWeight7;
    private Double inTareWeight7;

    public OrderStatistics2() {
    }

    public OrderStatistics2(Integer mold, Integer numberOfCars0, Integer numberOfCars1, Integer numberOfCars2, Integer numberOfCars3, BigDecimal outNetWeight, BigDecimal inNetWeight, Integer numberOfCars4) {
        this.mold = mold;
        this.numberOfCars0 = numberOfCars0;
        this.numberOfCars1 = numberOfCars1;
        this.numberOfCars2 = numberOfCars2;
        this.numberOfCars3 = numberOfCars3;
        this.outNetWeight = outNetWeight;
        this.inNetWeight = inNetWeight;
        this.numberOfCars4 = numberOfCars4;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public Integer getTranStatus() {
        return tranStatus;
    }

    public void setTranStatus(Integer tranStatus) {
        this.tranStatus = tranStatus;
    }

    public String getTranDate() {
        return tranDate;
    }

    public void setTranDate(String tranDate) {
        this.tranDate = tranDate;
    }

    public Integer getNumberOfCars0() {
        return numberOfCars0;
    }

    public void setNumberOfCars0(Integer numberOfCars0) {
        this.numberOfCars0 = numberOfCars0;
    }

    public Integer getNumberOfCars1() {
        return numberOfCars1;
    }

    public void setNumberOfCars1(Integer numberOfCars1) {
        this.numberOfCars1 = numberOfCars1;
    }

    public Integer getNumberOfCars2() {
        return numberOfCars2;
    }

    public void setNumberOfCars2(Integer numberOfCars2) {
        this.numberOfCars2 = numberOfCars2;
    }

    public Integer getNumberOfCars3() {
        return numberOfCars3;
    }

    public void setNumberOfCars3(Integer numberOfCars3) {
        this.numberOfCars3 = numberOfCars3;
    }

    public BigDecimal getOutNetWeight() {
        BigDecimal grossWeight = BigDecimal.valueOf(outGrossWeight7);
        BigDecimal tareWeight = BigDecimal.valueOf(outTareWeight7);
        return grossWeight.subtract(tareWeight);
    }

    public void setOutNetWeight(BigDecimal outNetWeight) {
        this.outNetWeight = outNetWeight;
    }

    public BigDecimal getInNetWeight() {
        BigDecimal grossWeight = BigDecimal.valueOf(inGrossWeight7);
        BigDecimal tareWeight = BigDecimal.valueOf(inTareWeight7);
        return grossWeight.subtract(tareWeight);
    }

    public void setInNetWeight(BigDecimal inNetWeight) {
        this.inNetWeight = inNetWeight;
    }

    public Integer getNumberOfCars4() {
        return numberOfCars4;
    }

    public void setNumberOfCars4(Integer numberOfCars4) {
        this.numberOfCars4 = numberOfCars4;
    }

    public List<Integer> getNumberOfCars() {
        return numberOfCars;
    }

    public void setNumberOfCars(List<Integer> numberOfCars) {
        this.numberOfCars = numberOfCars;
    }

    public Double getOutGrossWeight7() {
        return outGrossWeight7;
    }

    public void setOutGrossWeight7(Double outGrossWeight7) {
        this.outGrossWeight7 = outGrossWeight7;
    }

    public Double getOutTareWeight7() {
        return outTareWeight7;
    }

    public void setOutTareWeight7(Double outTareWeight7) {
        this.outTareWeight7 = outTareWeight7;
    }

    public Double getInGrossWeight7() {
        return inGrossWeight7;
    }

    public void setInGrossWeight7(Double inGrossWeight7) {
        this.inGrossWeight7 = inGrossWeight7;
    }

    public Double getInTareWeight7() {
        return inTareWeight7;
    }

    public void setInTareWeight7(Double inTareWeight7) {
        this.inTareWeight7 = inTareWeight7;
    }

    @Override
    public String toString() {
        return "OrderStatistics{" +
                "cid='" + cid + '\'' +
                ", mold=" + mold +
                ", numberOfCars0=" + numberOfCars0 +
                ", numberOfCars1=" + numberOfCars1 +
                ", numberOfCars2=" + numberOfCars2 +
                ", numberOfCars3=" + numberOfCars3 +
                ", outNetWeight=" + outNetWeight +
                ", inNetWeight=" + inNetWeight +
                ", numberOfCars4=" + numberOfCars4 +
                '}';
    }
}
