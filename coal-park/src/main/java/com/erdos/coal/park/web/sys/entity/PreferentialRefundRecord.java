package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.math.BigDecimal;

@Entity(value = "t_preferential_refund_record", noClassnameStored = true)
public class PreferentialRefundRecord extends BaseMongoInfo {
    private String subName;
    private String defaultDownUnit;
    private String unitName;
    private String unitCode;

    private String dateStr;             //退款日期 yyyy-MM-dd
    private Integer totalCar;           //退款总车数
    private Integer total;              //获取退款的司机总数
    private BigDecimal totalFee;        //退款总金额，即司机账户获取退款的总数额
    private Integer platFormAccountNum; //平台账户发生的退款总数
    private BigDecimal platFormAccountFee; //平台账户发生的退款总金额
    private Integer thirdPartyAccountNum; //三方账户发生的退款总金额
    private BigDecimal thirdPartyAccountFee; //三方账户发生的退款总金额

    public PreferentialRefundRecord() {
    }

    public PreferentialRefundRecord(String subName, String defaultDownUnit, String unitName, String unitCode, String dateStr, Integer totalCar, Integer total, BigDecimal totalFee, Integer platFormAccountNum, BigDecimal platFormAccountFee, Integer thirdPartyAccountNum, BigDecimal thirdPartyAccountFee) {
        this.subName = subName;
        this.defaultDownUnit = defaultDownUnit;
        this.unitName = unitName;
        this.unitCode = unitCode;
        this.dateStr = dateStr;
        this.totalCar = totalCar;
        this.total = total;
        this.totalFee = totalFee;
        this.platFormAccountNum = platFormAccountNum;
        this.platFormAccountFee = platFormAccountFee;
        this.thirdPartyAccountNum = thirdPartyAccountNum;
        this.thirdPartyAccountFee = thirdPartyAccountFee;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getDefaultDownUnit() {
        return defaultDownUnit;
    }

    public void setDefaultDownUnit(String defaultDownUnit) {
        this.defaultDownUnit = defaultDownUnit;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Integer getTotalCar() {
        return totalCar;
    }

    public void setTotalCar(Integer totalCar) {
        this.totalCar = totalCar;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getPlatFormAccountNum() {
        return platFormAccountNum;
    }

    public void setPlatFormAccountNum(Integer platFormAccountNum) {
        this.platFormAccountNum = platFormAccountNum;
    }

    public BigDecimal getPlatFormAccountFee() {
        return platFormAccountFee;
    }

    public void setPlatFormAccountFee(BigDecimal platFormAccountFee) {
        this.platFormAccountFee = platFormAccountFee;
    }

    public Integer getThirdPartyAccountNum() {
        return thirdPartyAccountNum;
    }

    public void setThirdPartyAccountNum(Integer thirdPartyAccountNum) {
        this.thirdPartyAccountNum = thirdPartyAccountNum;
    }

    public BigDecimal getThirdPartyAccountFee() {
        return thirdPartyAccountFee;
    }

    public void setThirdPartyAccountFee(BigDecimal thirdPartyAccountFee) {
        this.thirdPartyAccountFee = thirdPartyAccountFee;
    }
}
