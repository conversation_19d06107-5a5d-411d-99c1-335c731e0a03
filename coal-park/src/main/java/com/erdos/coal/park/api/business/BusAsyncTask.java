package com.erdos.coal.park.api.business;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Component
public class BusAsyncTask {

    @Async("busTaskExecutor")
    public CompletableFuture<String> test1(String s) {
//        try {
//            Thread.sleep(1);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
        return CompletableFuture.completedFuture(s);
    }

    @Async("busTaskExecutor")
    public CompletableFuture<String> test2(String s) {
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return CompletableFuture.completedFuture(s);
    }

    @Async("busTaskExecutor")
    public CompletableFuture<String> test3(String s) {
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return CompletableFuture.completedFuture(s);
    }

    @Async("busTaskExecutor")
    public CompletableFuture<String> test4(String s) {
        try {
            Thread.sleep(400);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return CompletableFuture.completedFuture(s);
    }
}
