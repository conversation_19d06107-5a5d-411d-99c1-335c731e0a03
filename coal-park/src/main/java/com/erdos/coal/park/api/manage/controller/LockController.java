package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.service.ILockedService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * Created by LIGX on 2018/12/27.
 * 业务接口
 */
@RestController
@RequestMapping("/api/manage")
public class LockController extends BaseController {
    //处理并发事件
    //定义资源的总数量
    private int permitsNum = 1000; //TODO: 资源池
    private Semaphore semaphore = new Semaphore(permitsNum);

    @Resource
    private ILockedService lockService;

    @PostMapping(value = "/test", produces = "application/json")
    public ServerResponse testHandler(String userCode) throws Exception {
        //相当于登录，目的创建 session
        request.getSession().setAttribute("userCode", userCode);
        return ServerResponse.createSuccess("OK", userCode);
    }

    @PostMapping(value = "/test1", produces = "application/json")
    public ServerResponse test1Handler(String code, int tid) throws Exception {

        //long beginTime = System.currentTimeMillis();

        //TODO: 没有资源，等10秒
        if (semaphore.tryAcquire(30, TimeUnit.SECONDS)) {
            //logger.info("成功获取资源,开始业务处理...");

            //请求占用一个资源
            //semaphore.acquire(1);

            //TODO: 异步调用业务处理
            CompletableFuture<String> t1 = lockService.test1(code, tid);
            //CompletableFuture<String> t2 = concurrentService.test2(e + "2");
            //CompletableFuture<String> t3 = concurrentService.test3(e + "3");
            //CompletableFuture<String> t4 = concurrentService.test4(e + "4");
//                for (; ; ) {
//                    //if (t1.isDone() && t2.isDone() && t3.isDone() && t4.isDone()) {
//                    if (t1.isDone()) {
//                        logger.info("Task1 result: {}", t1.get());
////                        logger.info("Task2 result: {}", t2.get());
////                        logger.info("Task3 result: {}", t3.get());
////                        logger.info("Task4 result: {}", t4.get());
//                        break;
//                    }
//                }

            semaphore.release(1); //TODO: 释放一个资源

            for (; ; )
                if (t1.isDone()) {
                    return ServerResponse.createSuccess("OK", t1.get());
                }

//                return ServerResponse.createSuccess("OK", "总时间:" + String.valueOf(System.currentTimeMillis() - beginTime));
        } else {
            //logger.info("*********资源已用尽，稍后再试***********");
            return ServerResponse.createError("*********资源已用尽，稍后再试***********");
        }

    }

    @PostMapping(value = "/test_lock", produces = "application/json")
    public ServerResponse testLockHandler(String code, int tid) throws Exception {

        //tid为当前线程ID

        boolean lock = lockService.getLock(code, 1, tid);
        if (lock) {

            logger.info("处理中..., threadID:" + tid);

            //lockService.unLock(code, 1);

        } else {

            logger.info("已加锁!!!");

        }
        return ServerResponse.createSuccess("OK");
    }
}
