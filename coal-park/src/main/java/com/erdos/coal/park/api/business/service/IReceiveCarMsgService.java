package com.erdos.coal.park.api.business.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.pojo.BizOrderData;
import com.erdos.coal.park.api.business.pojo.OrderStatistics;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.driver.entity.QuarantineInfo;
import com.erdos.coal.park.api.driver.pojo.CoalTicket;

import java.util.List;
import java.util.Map;

public interface IReceiveCarMsgService {

    //企业扫描司机订单二维码，查询订单信息
    ServerResponse<BizOrderData> searchOrder(String md5Oid, String unitCode, String sbuCode, boolean isDvrApp);

    //企业扫描司机订单二维码，修改订单检票状态
    ServerResponse<Object> checkingOrder(Map<String, Object> id, JSONArray inData, JSONArray outData);

    //企业扫描司机订单二维码，修改订单(一单)检票状态
    ServerResponse<String> checkingOneOrder(String billCode, Integer bizType, Integer checking, String grossWeight, String tareWeight);
    ServerResponse<String> checkingOneOrder2(String billCode, Integer bizType, Integer checking, String grossWeight, String tareWeight);
    ServerResponse<String> checkingOneOrder3(JSONObject data);

    //企业推送车牌号，调用阿里云推送
    ServerResponse<String> pushByCarNum(String listStr);
    ServerResponse<String> pushByBillCode(String listStr);

    //企业取消司机订单到预约或签到记录
    ServerResponse<String> cancelAppointmentOrCheckin(String billCode, int type);

    //二级单位查询司机防疫申报信息
    ServerResponse<List<QuarantineInfo>> searchQuarantineInfosBySubCode(String subCode, Integer bizType, Integer page, Integer rows);
//    ServerResponse<Map<String,Object>> searchQuarantineInfosBySubCode(String subCode, Integer bizType, Integer page, Integer rows);
    ServerResponse<EGridResult> searchQuarantineInfos2BySubCode(String unitCode, String subCode, Integer bizType, Integer page, Integer rows, String mobile, String date, Integer quaType);

    //企业修改防疫申报信息审核结果
    ServerResponse<String> auditQuarantine(String id, Integer audit, String auditDes);
    ServerResponse<String> auditQuarantine2(String id, Integer audit, String auditDes);

    //企业添加订单 电子煤票信息
    ServerResponse<String> putCoalTicket(String billCode, CoalTicket coalTicket);

    //企业销售信息统计
    ServerResponse<EGridResult> orderStatistics(String unitCode, String subCode, String startTime, String endTime, Integer page, Integer rows);

    //企业按司机身份证号，查询当前订单的billCode
    ServerResponse<String> searchBillCodeByIdentity(String identity, String unitCode, String subCode);

    //企业按设备编码修改设备id
    ServerResponse<String> udpateDeviceIdByDeviceCode(String deviceCode);

    //企业选择订单车型
    ServerResponse<String> updateOrderVehicleName(String billCode, String vehicleCode);

    //企业修改订单remark4字段
    ServerResponse<String> updateOrderRemark4(String billCode, String remark4);

    //企业修改订单离场状态
    ServerResponse<String> updateOrderExit(String billCode, Integer bizType);
}
