package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.math.BigDecimal;

@Entity(value = "sys_unit_Account", noClassnameStored = true)
public class UnitAccount extends BaseMongoInfo {
    private String uid;     //sysUnit的 ObjectId
    private String userId;    //扣款人（单位）或付款人（客商或司机）ObjectId
    private BigDecimal totalFee = new BigDecimal("0");   //金额(分)
    private Integer type;   //类型 0：扣款（单位代付平台费用） 1：单位代收（客商/司机付款） 2：退差价（代付费用>司机接单费用） 3：退款（客商废除订单）
    private String gid;     //扣款订单货运信息号
    private String oid;     //扣款订单号

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }
}
