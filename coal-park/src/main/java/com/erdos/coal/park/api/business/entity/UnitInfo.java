package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import dev.morphia.annotations.*;

@Entity(value = "t_unit_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("userCode")}, options = @IndexOptions(unique = true))
})
public class UnitInfo extends BaseMongoInfo {
    private String cid;         //客商编号
    private String unitCode;     //一级单位编码
    //@Indexed(options = @IndexOptions(name = "_idx_userCode", unique = true, background = true))
    private String userCode;     //企业用户编号

    @Reference(value = "customerUserID", idOnly = true, lazy = true, ignoreMissing = true)
    private CustomerUser customerUser;

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }
}
