package com.erdos.coal.park.web.app.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_car_info", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")})
})
public class CarInfo extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "id", background = true))
    private String id;
    private String carType; //车型名称
    private Integer carTypeNum; //车型
    private Double capacity = 0.0;    //载重 kg
    private String axlesNumber; //车轴数

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public Integer getCarTypeNum() {
        return carTypeNum;
    }

    public void setCarTypeNum(Integer carTypeNum) {
        this.carTypeNum = carTypeNum;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getAxlesNumber() {
        return axlesNumber;
    }

    public void setAxlesNumber(String axlesNumber) {
        this.axlesNumber = axlesNumber;
    }
}
