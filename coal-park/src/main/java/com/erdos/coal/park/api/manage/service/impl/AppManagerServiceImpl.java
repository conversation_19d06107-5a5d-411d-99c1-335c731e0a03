package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.config.CoalConfig;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.dao.IFileInfoDao;
import com.erdos.coal.park.api.manage.dao.IAppManagerDao;
import com.erdos.coal.park.api.manage.pojo.SysAppData;
import com.erdos.coal.park.api.manage.service.IAppManagerService;
import com.erdos.coal.park.web.sys.entity.SysApp;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

@Service("appManagerService")
public class AppManagerServiceImpl extends BaseMongoServiceImpl<SysApp, IAppManagerDao> implements IAppManagerService {
    @Resource
    private IFileInfoDao fileInfoDao;
    @Resource
    private CoalConfig coalConfig;

    @Override
    public ServerResponse<SysAppData> getInfo(String cusOrDri) {
        Query<SysApp> query = this.createQuery();
        query.filter("type", Integer.valueOf(cusOrDri));
        List<SysApp> sysAppList = this.list(query);
        SysAppData appData = new SysAppData();
        if (sysAppList.size() > 0) {
            SysApp sysApp = sysAppList.get(0);

            appData.setVersion(sysApp.getVersion());
            appData.setIsUpdate(sysApp.getIsUpdate());
            String url = File.separator + "api" + File.separator + "file" + File.separator + sysApp.getObjectId().toString() + ".apk";
            appData.setUrl(url);
            appData.setContent(sysApp.getContent());
        }

        return ServerResponse.createSuccess("查询成功", appData);
    }

    @Override
    public void downloadApp(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String driverPath = coalConfig.uploadPath + "driver.apk";
        String customerPath = coalConfig.uploadPath + "customer.apk";
        String filename = "";
        File folder = null;
        String url = request.getParameter("url");
        if (url.contains(driverPath)) {
            folder = new File(driverPath);   //判断文件是否存在，不存在先创建
            if (!folder.exists()) {
                renameFile(1, driverPath);
            }
            filename = "driver.apk";
        } else if (url.contains(customerPath)) {
            folder = new File(customerPath);   //判断文件是否存在，不存在先创建
            if (!folder.exists()) {
                renameFile(0, customerPath);
            }
            filename = "customer.apk";
        }
        OutputStream out = null;
        FileInputStream in = null;
        try {
            // 1.读取要下载的内容
            in = new FileInputStream(folder);

            // 2. 告诉浏览器下载的方式以及一些设置
            // 解决文件名乱码问题，获取浏览器类型，转换对应文件名编码格式，IE要求文件名必须是utf-8, firefo要求是iso-8859-1编码
            String agent = request.getHeader("user-agent");
            if (agent.contains("FireFox")) {
                filename = new String(filename.getBytes("UTF-8"), "iso-8859-1");
            } else {
                filename = URLEncoder.encode(filename, "UTF-8");
            }
            // 设置下载文件的mineType，告诉浏览器下载文件类型
            String mineType = request.getServletContext().getMimeType(filename);
            response.setContentType(mineType);
            // 设置一个响应头，无论是否被浏览器解析，都下载
            response.setHeader("Content-disposition", "attachment; filename=" + filename);
            // 将要下载的文件内容通过输出流写到浏览器
            out = response.getOutputStream();
            int len = 0;
            byte[] buffer = new byte[1024];
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
            if (in != null) {
                in.close();
            }
        }
    }

    // mongodb下载文件，并重名命
    private void renameFile(Integer type, String newFilePath) {
        Query<SysApp> query = this.createQuery();
        query.filter("type", type);
        SysApp sysApp = this.get(query);
        String suffixName = sysApp.getName().substring(sysApp.getName().lastIndexOf("."));
        String url = coalConfig.uploadPath + fileInfoDao.readFileName(sysApp.getObjectId().toString() + suffixName, "sys_app");

        // 重命名文件
        File oldFile = new File(url);
        File newFile = new File(newFilePath);
        if (oldFile.exists() && oldFile.isFile()) {
            oldFile.renameTo(newFile);
        }
    }
}
