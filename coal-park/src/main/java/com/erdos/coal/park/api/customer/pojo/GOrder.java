package com.erdos.coal.park.api.customer.pojo;

import com.erdos.coal.park.api.driver.pojo.CoalTicket;

import java.io.Serializable;
import java.util.Date;

public class GOrder implements Serializable {
    private String oid;     //订单号
    private String carNum;  //车牌号
    private String gid; //货运信息编号
    private String outMinMax;   //发货单位 票号区间
    private String inMinMax;    //收货单位 票号区间
    private boolean delete;   //订单废除标记
    private Integer delType;    //订单废除类型 0：客商废除，1：司机主动废除，2：指定车牌号，司机拒绝接单，3：司机同意客商废除订单， 4：企业撤单
    private Integer status;   //订单状态：0可用，1不可用
    private Integer isHand; //是否手工下单0：扫码下单 1：是（手工下单） 2：批量下单（抢单）
    private String tradeName;                       //商品名称
    private String beginPoint;                      //起点
    private String endPoint;                        //终点
    private Double weight;                          //车载重量要求
    private Double price;                           //单价
    private Double distance;                        //总里程
    private Double tolls;                           //预估过路费
    private Integer pType;          //下单类型：0-4对应出示二维码 指定车号下单 分享给微信好友  司机池抢单 友商下单 5-企业下单
    private Integer mold;                            //物流模式  发货物流 - 0，收货物流 - 1，收发货物流 - 2
    private String outUnitCode;                     //发货单位编码
    private String outUnitName;                     //发货单位名称
    private String inUnitCode;                      //收货单位编码
    private String inUnitName;                      //收货单位名称
    private String outBizContractCode;             //发货单位业务合同编码
    private String outBizContractName;             //发货单位业务合同名称
    private String inBizContractCode;              //收货单位业务合同编码
    private String inBizContractName;              //收货单位业务合同名称
    private String outVariety;                      //发货单位产品名称
    private String inVariety;                       //收货单位产品名称
    private String outDefaultDownUnit;             //发货单位 默认二级单位编码
    private String outSubName;                      //发货单位 默认二级单位名称
    private String inDefaultDownUnit;              //收货单位 默认二级单位编码
    private String inSubName;                       //收货单位 默认二级单位名称
    private String outDefaultArea;                 //发货单位 默认场区编码
    private String outAreaName;                    //发货单位 默认场区名称
    private String inDefaultArea;                  //收货单位 默认场区编码
    private String inAreaName;                     //收货单位 默认场区名称
    private Integer fees = 0;    //平台收取的信息费（分）
    private String payerId; //支付信息费人的id
    private Integer feesOut = 0;    //平台收取发货单位的信息费（分）
    private String payerIdOut; //支付信息费人的id
    private Integer feesIn = 0;    //平台收取收货单位的信息费（分）
    private String payerIdIn; //支付信息费人的id
    private Integer fees1Out = 0;    //发货企业一级单位收取到的代扣费（分）
    private String payerId1Out; //支付代扣费（司机或客商）  人的id
    private Integer fees1In = 0;    //收货企业一级单位收取到的代扣费（分）
    private String payerId1In; //支付代扣费  人的id
    private Integer fees2Out = 0;    //发货企业二级单位收取到的代扣费（分）
    private String payerId2Out; //支付代扣费  人的id
    private Integer fees2In = 0;    //收货企业一级单位收取到的代扣费（分）
    private String payerId2In; //支付代扣费  人的id
    private Integer balanceFees = 0;     //客商代付平台金额不足或客商未代付，司机支付平台金额（分）
    private Integer shareFees = 0;     //友商或客商 收取的信息费（分）
    private String driverPayerId;   //司机id
    private boolean locked;     //是否锁定
    private Integer isWeChat;    //0-给app司机下单的货运信息；1-给小程序司机下单的货运信息
    private Integer isCheck;    //订单废除是否审核 0:未审核，1：通过审核，2：未通过，3：无需审核
    private String transactionId;   //微信订单号
    private String outTradeNo;  //商户订单号
    private Integer share;  //0-订单未分享给好友客商, 1-订单分享给好友客商
    private String shareCid;    //订单来自编号为cid的客商分享
    private String shareGid;    //订单来自编号为gid的客商货单分享
    private String shareOid;    //订单来自编号为oid的客商订单分享
    private String outBillCode;     //发货企业票号
    private String inBillCode;      //收货企业票号
    private Integer outChecking = 0;    //发货企业系统标记订单的检票状态 ：1- 一检，2- 二检，3- 检票
    private Integer inChecking = 0;    //收货企业系统标记订单的检票状态 ：1- 一检，2- 二检，3- 检票
    //发货 或 收货 单位 checking到3-检票时，会有毛重和皮重参数要记录到订单中
    private String outGrossWeight;
    private String outTareWeight;
    private String inGrossWeight;
    private String inTareWeight;
    private Date otherOutTime;      //发货单位，checking 为 '检票' 时，记录的时间
    private Date otherInTime;       //收货单位，checking 为 '一检' 时，记录的时间
    private Integer isTransport;   //订单是否可运输：0-可运输，1-禁止运输
    private String callStartTime;   //企业叫号开始时间
    private String callEndTime;     //企业叫号结束时间（过期）
    private String spell;   //运往地编码
    private String place;   //运往地名称
    private CoalTicket coalTicket;
    private String cid;
    private Integer tranStatus; //订单状态：0-未接单，1-已接单，2-发货入场，3-运输中，4-收货入场，5-订单完成，6-订单废除
    /*
     * 下单后，订单为未接单状态，（若指定车号下单，则为已接单）
     * 发货业务 ，outchecking=1，为发货入场；outchecking=3，为订单完成
     * 收货业务 ，inchecking=1，为收货入场； inchecking=3，为订单完成
     * 收发业务 ，outchecking=1，为发货入场；outchecking=3，为运输途中；inchecking=1，为收货入场；inchecking=3为订单完成
     * */

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getOutMinMax() {
        return outMinMax;
    }

    public void setOutMinMax(String outMinMax) {
        this.outMinMax = outMinMax;
    }

    public String getInMinMax() {
        return inMinMax;
    }

    public void setInMinMax(String inMinMax) {
        this.inMinMax = inMinMax;
    }

    public boolean isDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }

    public Integer getDelType() {
        return delType;
    }

    public void setDelType(Integer delType) {
        this.delType = delType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsHand() {
        return isHand;
    }

    public void setIsHand(Integer isHand) {
        this.isHand = isHand;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public Integer getpType() {
        return pType;
    }

    public void setpType(Integer pType) {
        this.pType = pType;
    }

    public Integer getMold() {
        return mold;
    }

    public void setMold(Integer mold) {
        this.mold = mold;
    }

    public String getOutUnitCode() {
        return outUnitCode;
    }

    public void setOutUnitCode(String outUnitCode) {
        this.outUnitCode = outUnitCode;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitCode() {
        return inUnitCode;
    }

    public void setInUnitCode(String inUnitCode) {
        this.inUnitCode = inUnitCode;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getOutBizContractCode() {
        return outBizContractCode;
    }

    public void setOutBizContractCode(String outBizContractCode) {
        this.outBizContractCode = outBizContractCode;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getInBizContractCode() {
        return inBizContractCode;
    }

    public void setInBizContractCode(String inBizContractCode) {
        this.inBizContractCode = inBizContractCode;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInDefaultDownUnit() {
        return inDefaultDownUnit;
    }

    public void setInDefaultDownUnit(String inDefaultDownUnit) {
        this.inDefaultDownUnit = inDefaultDownUnit;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getOutDefaultArea() {
        return outDefaultArea;
    }

    public void setOutDefaultArea(String outDefaultArea) {
        this.outDefaultArea = outDefaultArea;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getInDefaultArea() {
        return inDefaultArea;
    }

    public void setInDefaultArea(String inDefaultArea) {
        this.inDefaultArea = inDefaultArea;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public Integer getFees() {
        return fees;
    }

    public void setFees(Integer fees) {
        this.fees = fees;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public Integer getFeesOut() {
        return feesOut;
    }

    public void setFeesOut(Integer feesOut) {
        this.feesOut = feesOut;
    }

    public String getPayerIdOut() {
        return payerIdOut;
    }

    public void setPayerIdOut(String payerIdOut) {
        this.payerIdOut = payerIdOut;
    }

    public Integer getFeesIn() {
        return feesIn;
    }

    public void setFeesIn(Integer feesIn) {
        this.feesIn = feesIn;
    }

    public String getPayerIdIn() {
        return payerIdIn;
    }

    public void setPayerIdIn(String payerIdIn) {
        this.payerIdIn = payerIdIn;
    }

    public Integer getFees1Out() {
        return fees1Out;
    }

    public void setFees1Out(Integer fees1Out) {
        this.fees1Out = fees1Out;
    }

    public String getPayerId1Out() {
        return payerId1Out;
    }

    public void setPayerId1Out(String payerId1Out) {
        this.payerId1Out = payerId1Out;
    }

    public Integer getFees1In() {
        return fees1In;
    }

    public void setFees1In(Integer fees1In) {
        this.fees1In = fees1In;
    }

    public String getPayerId1In() {
        return payerId1In;
    }

    public void setPayerId1In(String payerId1In) {
        this.payerId1In = payerId1In;
    }

    public Integer getFees2Out() {
        return fees2Out;
    }

    public void setFees2Out(Integer fees2Out) {
        this.fees2Out = fees2Out;
    }

    public String getPayerId2Out() {
        return payerId2Out;
    }

    public void setPayerId2Out(String payerId2Out) {
        this.payerId2Out = payerId2Out;
    }

    public Integer getFees2In() {
        return fees2In;
    }

    public void setFees2In(Integer fees2In) {
        this.fees2In = fees2In;
    }

    public String getPayerId2In() {
        return payerId2In;
    }

    public void setPayerId2In(String payerId2In) {
        this.payerId2In = payerId2In;
    }

    public Integer getBalanceFees() {
        return balanceFees;
    }

    public void setBalanceFees(Integer balanceFees) {
        this.balanceFees = balanceFees;
    }

    public Integer getShareFees() {
        return shareFees;
    }

    public void setShareFees(Integer shareFees) {
        this.shareFees = shareFees;
    }

    public String getDriverPayerId() {
        return driverPayerId;
    }

    public void setDriverPayerId(String driverPayerId) {
        this.driverPayerId = driverPayerId;
    }

    public boolean isLocked() {
        return locked;
    }

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    public Integer getIsWeChat() {
        return isWeChat;
    }

    public void setIsWeChat(Integer isWeChat) {
        this.isWeChat = isWeChat;
    }

    public Integer getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(Integer isCheck) {
        this.isCheck = isCheck;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public String getShareCid() {
        return shareCid;
    }

    public void setShareCid(String shareCid) {
        this.shareCid = shareCid;
    }

    public String getShareGid() {
        return shareGid;
    }

    public void setShareGid(String shareGid) {
        this.shareGid = shareGid;
    }

    public String getShareOid() {
        return shareOid;
    }

    public void setShareOid(String shareOid) {
        this.shareOid = shareOid;
    }

    public String getOutBillCode() {
        return outBillCode;
    }

    public void setOutBillCode(String outBillCode) {
        this.outBillCode = outBillCode;
    }

    public String getInBillCode() {
        return inBillCode;
    }

    public void setInBillCode(String inBillCode) {
        this.inBillCode = inBillCode;
    }

    public Integer getOutChecking() {
        return outChecking;
    }

    public void setOutChecking(Integer outChecking) {
        this.outChecking = outChecking;
    }

    public Integer getInChecking() {
        return inChecking;
    }

    public void setInChecking(Integer inChecking) {
        this.inChecking = inChecking;
    }

    public String getOutGrossWeight() {
        return outGrossWeight;
    }

    public void setOutGrossWeight(String outGrossWeight) {
        this.outGrossWeight = outGrossWeight;
    }

    public String getOutTareWeight() {
        return outTareWeight;
    }

    public void setOutTareWeight(String outTareWeight) {
        this.outTareWeight = outTareWeight;
    }

    public String getInGrossWeight() {
        return inGrossWeight;
    }

    public void setInGrossWeight(String inGrossWeight) {
        this.inGrossWeight = inGrossWeight;
    }

    public String getInTareWeight() {
        return inTareWeight;
    }

    public void setInTareWeight(String inTareWeight) {
        this.inTareWeight = inTareWeight;
    }

    public Date getOtherOutTime() {
        return otherOutTime;
    }

    public void setOtherOutTime(Date otherOutTime) {
        this.otherOutTime = otherOutTime;
    }

    public Date getOtherInTime() {
        return otherInTime;
    }

    public void setOtherInTime(Date otherInTime) {
        this.otherInTime = otherInTime;
    }

    public Integer getIsTransport() {
        return isTransport;
    }

    public void setIsTransport(Integer isTransport) {
        this.isTransport = isTransport;
    }

    public String getCallStartTime() {
        return callStartTime;
    }

    public void setCallStartTime(String callStartTime) {
        this.callStartTime = callStartTime;
    }

    public String getCallEndTime() {
        return callEndTime;
    }

    public void setCallEndTime(String callEndTime) {
        this.callEndTime = callEndTime;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public CoalTicket getCoalTicket() {
        return coalTicket;
    }

    public void setCoalTicket(CoalTicket coalTicket) {
        this.coalTicket = coalTicket;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Integer getTranStatus() {
        return tranStatus;
    }

    public void setTranStatus(Integer tranStatus) {
        this.tranStatus = tranStatus;
    }
}
