package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.customer.service.IUserDefinedAddressService;
import com.erdos.coal.park.web.app.entity.DeviceInfo;
import com.erdos.coal.park.web.app.service.IDeviceService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//"微信用户维护自定义地址"
@RestController
@RequestMapping("/we_chat/small_pro")
public class WeChatSmallProController {
    @Resource
    private IUserDefinedAddressService userDefinedAddressService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IDeviceService deviceService;

    @InvokeLog(description = "添加自定义地址 接口") //日志
    @PostMapping(value = "/add_user_defined_address")
    public ServerResponse<String> addUserDefinedAddressHandler(
            @RequestParam(value = "mobile") String mobile,                     //"客商的手机号"
            @RequestParam(value = "addName", required = false) String addName, //"自定义地址名称"
            @RequestParam(value = "fullName") String fullName,                 //"地址全称"
            @RequestParam(value = "location") Double[] location                //"地址坐标(经度，纬度)"
    ) throws GlobalException {
        return userDefinedAddressService.addUserDefinedAddressWithMobile(mobile, addName, fullName, location);
    }

    @InvokeLog(description = "查询一级单位 接口") //日志
    @PostMapping(value = "/search_unit")
    public ServerResponse<List<CompanyUnit>> searchUnitHandler() throws GlobalException {
        return sysUnitService.searchUnit(null);
    }

    @InvokeLog(description = "查询二级单位 接口") //日志
    @PostMapping(value = "/search_sub_unit")
    public ServerResponse<List<CompanyUnit>> searchSubUnitHandler(
            @RequestParam(value = "unitCode") String unitCode                     //"一级单位编号"
    ) throws GlobalException {
        return sysUnitService.searchUnit(unitCode);
    }

    @InvokeLog(description = "二级单位添加地理围栏中心坐标 接口") //日志
    @PostMapping(value = "/save_geo_center")
    public ServerResponse<String> saveUnitGeoCenterHandler(
            @RequestParam(value = "subCode") String subCode,                       //"二级单位编码"
            @RequestParam(value = "longitude") String longitude,                   //"经度"
            @RequestParam(value = "latitude") String latitude,                     //"纬度"
            @RequestParam(value = "radius", required = false) String radius        //"纬度"
    ) throws GlobalException {
        return sysUnitService.saveUnitGeoCenter(subCode, longitude, latitude, radius);
    }

    @InvokeLog(description = "查询单位设备列表 接口") //日志
    @PostMapping(value = "/search_sub_unit_device")
    public ServerResponse<List<DeviceInfo>> searchSubUnitDeviceHandler(
            @RequestParam(value = "deviceCode", required = false) String deviceCode,                       //"设备编码"
            @RequestParam(value = "subCode", required = false) String subCode,                       //"二级单位编码"
            @RequestParam(value = "unitCode", required = false) String unitCode                     //"一级单位编号"
    ) throws GlobalException {
        return deviceService.searchDevice(unitCode, subCode, deviceCode);
    }

    @InvokeLog(description = "单位设备添加经纬度坐标 接口") //日志
    @PostMapping(value = "/save_device_geo")
    public ServerResponse<String> saveDeviceGeoHandler(
            @RequestParam(value = "deviceCode") String deviceCode,                       //"设备编号"
            @RequestParam(value = "longitude") String longitude,                   //"经度"
            @RequestParam(value = "latitude") String latitude                     //"纬度"
    ) throws GlobalException {
        return deviceService.saveDeviceGeo(deviceCode, longitude, latitude);
    }
}
