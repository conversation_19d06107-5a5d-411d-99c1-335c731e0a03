package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.CustomerAccount;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ICustomerAccountService extends IBaseMongoService<CustomerAccount> {
    //客商查询账户
    ServerResponse<CustomerAccount> getCustomerAccount();

    //计算账户可用总金额
    BigDecimal getAvailableFee(String cid);

    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
    Document createCusAccountDoc(String cid, BigDecimal fee, Integer type, String oid, Date time);

    Document createCusAccountDoc(String cid, BigDecimal fee, Integer type, String outTradeNo, String transactionId, Date time);

    //客商账户明细查询
    ServerResponse<EGridResult> customerAccountList(Integer page, Integer rows);
}
