package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.app.dao.IHotLineDao;
import com.erdos.coal.park.web.app.entity.HotLine;
import com.erdos.coal.park.web.app.service.IHotLineService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Service("hotLineService")
public class HotLineServiceImpl extends BaseMongoServiceImpl<HotLine, IHotLineDao> implements IHotLineService {
    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult loadHotLineGrid(Integer page, Integer rows) {
        Query<HotLine> query = this.createQuery();

        if (StrUtil.isNotEmpty(request.getParameter("subCode")))
            query.criteria("subCode").startsWithIgnoreCase(request.getParameter("subCode"));
        if (StrUtil.isNotEmpty(request.getParameter("subName")))
            query.criteria("subName").startsWithIgnoreCase(request.getParameter("subName"));
        if (StrUtil.isNotEmpty(request.getParameter("name")))
            query.criteria("name").startsWithIgnoreCase(request.getParameter("name"));
        if (StrUtil.isNotEmpty(request.getParameter("mobile")))
            query.criteria("mobile").startsWithIgnoreCase(request.getParameter("mobile"));

        query.order(Sort.ascending("subCode"), Sort.ascending("online"));

        return findPage(page, rows, query);
    }

    @Override
    public ServerResponse<String> deleteHotLine(HotLine hotLine) {
        String lineId = hotLine.getLineId();
        this.findAndDelete(this.createQuery().filter("lineId", lineId));
        return ServerResponse.createSuccess("删除成功");
    }

    private boolean checkOnToOffTime(HotLine hotLine) {
        Query<HotLine> query = this.createQuery();
        query.criteria("subCode").equal(hotLine.getSubCode());
        query.or(
                query.and(
                        query.criteria("online").greaterThan(hotLine.getOnline()),
                        query.criteria("online").lessThan(hotLine.getOffline())
                ),
                query.and(
                        query.criteria("offline").greaterThan(hotLine.getOnline()),
                        query.criteria("offline").lessThan(hotLine.getOffline())
                ),
                query.and(
                        query.criteria("online").lessThanOrEq(hotLine.getOnline()),
                        query.criteria("offline").greaterThanOrEq(hotLine.getOffline())
                ),
                query.and(
                        query.criteria("online").greaterThanOrEq(hotLine.getOnline()),
                        query.criteria("offline").lessThanOrEq(hotLine.getOffline())
                )
        );
        if (StrUtil.isNotEmpty(hotLine.getLineId())) query.criteria("lineId").notEqual(hotLine.getLineId());
        List<HotLine> lines = this.list(query);
        return lines.size() > 0;
    }

    @Override
    public ServerResponse<String> addHotLine(HotLine hotLine) {
        hotLine.setLineId(Utils.getUUID());
        if (StrUtil.isEmpty(hotLine.getSubCode())) return ServerResponse.createError("请选择关联的二级单位");
        if (StrUtil.isEmpty(hotLine.getMobile())) return ServerResponse.createError("电话号码不能为空");

        String online = hotLine.getOnline();
        String offline = hotLine.getOffline();
        if (online.compareTo(offline) >= 0) return ServerResponse.createError("值班时间段不正确");

        // 校验在线到离线时间段是否冲突
        if (checkOnToOffTime(hotLine)) return ServerResponse.createError("值班时间不可重叠");
        this.save(hotLine);
        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> editHotLine(HotLine hotLine) {
        Query<HotLine> query = this.createQuery();
        query.filter("lineId", hotLine.getLineId());

        HotLine oldHotLine = this.get(query);
        if (oldHotLine == null) return ServerResponse.createError("热线电话不存在");

        String online;
        String offline;
        if (StrUtil.isNotEmpty(hotLine.getOnline()) && !hotLine.getOnline().equals(oldHotLine.getOnline())) {
            online = hotLine.getOnline();
        } else {
            online = oldHotLine.getOnline();
        }
        if (StrUtil.isNotEmpty(hotLine.getOffline()) && !hotLine.getOffline().equals(oldHotLine.getOffline())) {
            offline = hotLine.getOffline();
        } else {
            offline = oldHotLine.getOffline();
        }
        if (online.compareTo(offline) >= 0) return ServerResponse.createError("值班时间段不正确");

        // 校验在线到离线时间段是否冲突
        if (checkOnToOffTime(hotLine)) return ServerResponse.createError("值班时间不可重叠");

        UpdateOperations<HotLine> updateOperations = this.createUpdateOperations();
        if (StrUtil.isNotEmpty(hotLine.getSubCode()) && !hotLine.getSubCode().equals(oldHotLine.getSubCode()))
            updateOperations.set("subCode", hotLine.getSubCode());
        if (StrUtil.isNotEmpty(hotLine.getSubName()) && !hotLine.getSubName().equals(hotLine.getSubName()))
            updateOperations.set("subName", hotLine.getSubName());
        if (StrUtil.isNotEmpty(hotLine.getName()) && !hotLine.getName().equals(oldHotLine.getName()))
            updateOperations.set("name", hotLine.getName());
        if (StrUtil.isNotEmpty(hotLine.getMobile()) && !hotLine.getMobile().equals(oldHotLine.getMobile()))
            updateOperations.set("mobile", hotLine.getMobile());
        if (StrUtil.isNotEmpty(hotLine.getOnline()) && !hotLine.getOnline().equals(oldHotLine.getOnline()))
            updateOperations.set("online", hotLine.getOnline());
        if (StrUtil.isNotEmpty(hotLine.getOffline()) && !hotLine.getOffline().equals(oldHotLine.getOffline()))
            updateOperations.set("offline", hotLine.getOffline());

        this.update(query, updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }
}
