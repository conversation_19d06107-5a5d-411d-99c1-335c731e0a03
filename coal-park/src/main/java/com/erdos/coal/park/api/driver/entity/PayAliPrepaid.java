package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_pay_ali_prepaid", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("outTradeNo")}, options = @IndexOptions(unique = true))
})
/*
 * 支付宝预下单记录
 */
public class PayAliPrepaid extends BaseMongoInfo {
    private String masterId;        //充值者id
    private String guestId;         //被充值者id（或者被支付货运信息编号）

    private String subject;         //订单标题
    //@Indexed(options = @IndexOptions(name = "_out_trade_no", unique = true, background = true))
    private String outTradeNo;     //商户网站唯一订单号
    private String totalAmount;    //订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]
    private String productCode;    //销售产品码，商家和支付宝签约的产品码，为固定值 QUICK_MSECURITY_PAY

    private String body;            //订单描述
    private String timeoutExpress;   //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天

    private String sellerId;          //卖家支付宝用户ID,即为商户签约账号对应的支付宝用户ID
    private String appId;           //支付宝分配给开发者的应用ID

    private boolean isPay;          //预创建订单是否完成支付

    private Integer type;          //订单类型：0-客商给司机充值；1-司机给自己充值；2-客商代付货运信息费；3-客商给自己充值
    //
    private int number;         //商品数量（货运信息的车数）

    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    public String getGuestId() {
        return guestId;
    }

    public void setGuestId(String guestId) {
        this.guestId = guestId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getTimeoutExpress() {
        return timeoutExpress;
    }

    public void setTimeoutExpress(String timeoutExpress) {
        this.timeoutExpress = timeoutExpress;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public boolean isPay() {
        return isPay;
    }

    public boolean getIsPay() {
        return isPay;
    }

    public void setIsPay(boolean isPay) {
        this.isPay = isPay;
    }

    public void setPay(boolean pay) {
        isPay = pay;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }
}
