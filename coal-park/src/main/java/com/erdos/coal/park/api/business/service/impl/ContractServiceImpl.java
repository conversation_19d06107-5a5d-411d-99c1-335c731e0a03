package com.erdos.coal.park.api.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.httpclient.HttpResult;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.business.pojo.Variety;
import com.erdos.coal.park.api.business.service.IContractService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.hdd.http.HddEncryptRequest;
import dev.morphia.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("contractService")
public class ContractServiceImpl implements IContractService {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private IUnitInfoService unitInfoService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IHttpAPIService httpAPIService;

    @Override
    public ServerResponse<List<CompanyUnit>> getUnitCode() {
        String cid = ShiroUtils.getUserId();

        //TODO:1.查询客商 绑定后的 unitCode列表
        Query<UnitInfo> query = unitInfoService.createQuery();
        query.filter("cid", cid);
        List<UnitInfo> unitInfos = unitInfoService.list(query);
        List<String> unitCodes = new ArrayList<>();
        for (UnitInfo unitInfo : unitInfos) {
            unitCodes.add(unitInfo.getUnitCode());
        }

        //TODO:2.查询客商的一级单位，并返回
        List<SysUnit> list = sysUnitService.searchByUnitCodes(unitCodes);
        List<CompanyUnit> resultList = new ArrayList<>();
        for (SysUnit sysUnit : list) {
            CompanyUnit cu = new CompanyUnit();
            cu.setUnitCode(sysUnit.getCode());
            cu.setUnitName(sysUnit.getName());
            cu.setTradeName(sysUnit.getVariety());
            resultList.add(cu);
        }

        return ServerResponse.createSuccess("查询成功", resultList);
    }

    @Override
    public ServerResponse<Object> getBizContractCode(String unitCode, Integer bizType) {
        String cid = ShiroUtils.getUserId();

        //TODO:1.查询客商企业的服务器地址 和 客商的userCode
        SysUnit sysUnit = sysUnitService.get("code", unitCode);
        if (sysUnit == null)
            return ServerResponse.createError("等待平台维护企业信息");
        String url = sysUnit.getIp() + "/app/external/interface/get_trade_contract_list.do";

        Query<UnitInfo> query = unitInfoService.createQuery();
        query.filter("cid", cid);
        query.filter("unitCode", unitCode);
        UnitInfo unitInfo = unitInfoService.get(query);
        if (unitInfo == null) return ServerResponse.createError("无企业端认证信息，请前往企业端认证");
        String userCode = unitInfo.getUserCode();

        //TODO:2.请求企业系统接口，查询业务合同 列表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userCode", userCode);
        paramMap.put("bizType", bizType);                     //销售1，采购0
        HttpResult result = getCompanyMessage(url, paramMap);

        if (result == null || result.getCode() != 200)
            return ServerResponse.createError("网络异常，稍后重试");

        String bodyStr = result.getBody();
        JSONObject object = JSONObject.parseObject(bodyStr);
        if (StrUtil.isNotEmpty(object.get("status")) && (int) object.get("status") == 0)
            return ServerResponse.createError((String) object.get("msg"));
        /*JSONObject object = JSONObject.parseObject(bodyStr);
        String bizContract = (String) object.get("data");
        Integer min = Integer.valueOf((Integer) object.get("min"));
        Integer max = Integer.valueOf((Integer) object.get("max"));
        Integer ableNumber = max - min;
        TradeContract tradeContract = new TradeContract(bizContract,ableNumber);
        return ServerResponse.createSuccess("查询成功", JSON.toJSON(tradeContract));*/
        //logger.error("ContractServiceImpl+getBizContractCode" + bodyStr);
        return ServerResponse.createSuccess("查询成功", JSON.parse(bodyStr));
    }

    @Override
    public ServerResponse<Object> getVarietyCode(String unitCode, String bizContractCode) {
        //TODO:1.查询客商企业的服务器地址 和 客商的userCode
        SysUnit sysUnit = sysUnitService.get("code", unitCode);
        if (sysUnit == null)
            return ServerResponse.createError("等待平台维护企业信息");
        String url = sysUnit.getIp() + "/app/external/interface/get_trade_price_list.do";

        //TODO:2.请求企业系统接口，查询业务合同 列表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bizContractCode", bizContractCode);
        HttpResult result = getCompanyMessage(url, paramMap);

        if (result == null || result.getCode() != 200)
            return ServerResponse.createError("网络异常，稍后重试");

        List<Variety> tcList1 = new ArrayList<>();
        List<Variety> tcList2 = new ArrayList<>();

        String jsonStr = result.getBody();
        JSONArray varietyArr = null;
        try {
            varietyArr = JSONArray.parseArray(jsonStr);
        } catch (Exception e) {
            JSONObject object = JSONObject.parseObject(jsonStr);
            if (StrUtil.isNotEmpty(object.get("status")) && (int) object.get("status") == 0)
                return ServerResponse.createError((String) JSONObject.parseObject(jsonStr).get("msg"));
            logger.warn("方法【getVarietyCode】获取单位：" + unitCode + "的商品、二级单位、场区等信息返回的数据为" + jsonStr + ",转JSONArray和JSONObject都出错了！");
        }
        assert varietyArr != null;
        for (Object aVarietyArr : varietyArr) {
            JSONObject variety = (JSONObject) aVarietyArr;
            Variety tc1 = new Variety();
            tc1.setCode((String) variety.get("variety"));            //产品编码
            tc1.setName((String) variety.get("variety"));            //产品名称
            tc1.setDept(1);
            tcList1.add(tc1);

            Variety tc2 = new Variety();
            tc2.setParentCode((String) variety.get("variety"));      //产品编码
            tc2.setCode((String) variety.get("defaultdownunit"));   //二级单位编码
            tc2.setName((String) variety.get("subname"));            //二级单位名称
            tc2.setDept(2);
            tcList2.add(tc2);

            Variety tc3 = new Variety();
            tc3.setParentCode((String) variety.get("defaultdownunit"));      //二级单位编码
            tc3.setCode((String) variety.get("defaultarea"));                //默认产区编码
            tc3.setName((String) variety.get("areaname"));                   //默认场区名称
            tc3.setDept(3);
            tc3.setGrandParentCode((String) variety.get("variety"));
            tcList2.add(tc3);
        }
        List<Variety> resultList = tree(3, tcList1, tcList2);
        return ServerResponse.createSuccess("查询成功", JSON.toJSON(resultList));
    }

    @Override
    public ServerResponse<Object> getVarietyCode2(String unitCode, String bizContractCode) {
        //TODO:1.查询客商企业的服务器地址 和 客商的userCode
        SysUnit sysUnit = sysUnitService.get("code", unitCode);
        if (sysUnit == null)
            return ServerResponse.createError("等待平台维护企业信息");
        String url = sysUnit.getIp() + "/app/external/interface/get_trade_price_list.do";

        //TODO:2.请求企业系统接口，查询业务合同 列表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bizContractCode", bizContractCode);
        HttpResult result = getCompanyMessage(url, paramMap);

        if (result == null || result.getCode() != 200)
            return ServerResponse.createError("网络异常，稍后重试");

        List<Variety> tcList1 = new ArrayList<>();
        List<Variety> tcList2 = new ArrayList<>();

        String jsonStr = result.getBody();

        JSONArray varietyArr = null;
        try {
            varietyArr = JSONArray.parseArray(jsonStr);
        } catch (Exception e) {
            JSONObject object = JSONObject.parseObject(jsonStr);
            if (StrUtil.isNotEmpty(object.get("status")) && (int) object.get("status") == 0)
                return ServerResponse.createError((String) JSONObject.parseObject(jsonStr).get("msg"));
            logger.warn("方法【getVarietyCode】获取单位：" + unitCode + "的商品、二级单位、场区等信息返回的数据为" + jsonStr + ",转JSONArray和JSONObject都出错了！");
        }
        assert varietyArr != null;
        for (Object aVarietyArr : varietyArr) {
            JSONObject variety = (JSONObject) aVarietyArr;
            Variety tc1 = new Variety();
            tc1.setCode(variety.getString("variety"));            //产品编码
            tc1.setName(variety.getString("variety"));            //产品名称
            tc1.setProductID(variety.getString("varietycode"));   //合同备案中的煤种ID-智慧能源
            tc1.setDept(1);
            tcList1.add(tc1);

            Variety tc2 = new Variety();
            tc2.setParentCode(variety.getString("variety"));        //产品编码
            tc2.setCode(variety.getString("defaultdownunit"));      //二级单位编码
            tc2.setName(variety.getString("subname"));              //二级单位名称
            tc2.setPlaceInput(variety.getString("placeinput"));     //录入地点
            tc2.setDept(2);
            tcList2.add(tc2);

            Variety tc3 = new Variety();
            tc3.setParentCode(variety.getString("defaultdownunit"));      //二级单位编码
            tc3.setCode(variety.getString("defaultarea"));                //默认产区编码
            tc3.setName(variety.getString("areaname"));                   //默认场区名称
            tc3.setDept(3);
            tc3.setGrandParentCode(variety.getString("variety"));
            tcList2.add(tc3);
        }

        //去重
        List l = new ArrayList(new HashSet(tcList1));

        List<Variety> resultList = tree(3, l, tcList2);
        return ServerResponse.createSuccess("查询成功", JSON.toJSON(resultList));
    }

    /**
     * 递归构建树形结构数据
     */
    private List<Variety> tree(int dept, List<Variety> parentList, List<Variety> childList) {
        if (childList.size() > 0) {
            Set<String> set = new HashSet<>(childList.size());
            parentList.forEach(tc -> getChild(tc, childList, dept, set));
            return parentList;
        }
        return null;
    }

    /**
     * 递归获取子类目
     */
    private void getChild(Variety tc, List<Variety> tcList, int dept, Set<String> set) {
        if (tc.getDept() < dept || dept == 0) {
            List<Variety> childList = new ArrayList<>();
            tcList.stream()
                    .filter(c -> !set.contains(c.getCode() + c.getParentCode() + c.getGrandParentCode()))            //判断是否已循环过当前对象
                    .filter(c -> (c.getParentCode() + c.getGrandParentCode()).equals(tc.getCode() + tc.getParentCode()))    //判断是否父子关系
                    .filter(c -> set.size() <= tcList.size())           //set集合大小不超过tcList的大小
                    .forEach(c -> {
                        set.add(c.getCode() + c.getParentCode() + c.getGrandParentCode());               //放入set，递归循环时可跳过这个子目录，提高循环效率
                        getChild(c, tcList, dept, set);     //获取当前类目的子目录
                        childList.add(c);                   //加入子类目集合
                    });
            tc.setChild(childList);
        }
    }

    private ServerResponse<Object> appToSysWithRush(String unitCode, String path, Map<String, Object> paramMap) {
        //TODO:1.查询客商企业的服务器地址 和 客商的userCode
        SysUnit sysUnit = sysUnitService.get("code", unitCode);
        if (sysUnit == null)
            return ServerResponse.createError("等待平台维护企业信息");
        String url = sysUnit.getIp() + path;

        //TODO:2.请求企业系统接口，查询业务合同 列表
        HttpResult result = getCompanyMessage(url, paramMap);

        if (result == null || result.getCode() != 200) return ServerResponse.createError("网络异常，稍后重试");

        JSONObject object = JSONObject.parseObject(result.getBody());
        if (StrUtil.isNotEmpty(object.get("status")) && (int) object.get("status") == 0)
            return ServerResponse.createError((String) object.get("msg"));

        String dataStr = String.valueOf(object.get("data"));
        return ServerResponse.createSuccess("请求成功", JSON.parse(dataStr));
    }

    @Override
    public ServerResponse<Object> getSubUnitList(String unitCode) {
        String cid = ShiroUtils.getUserId();
        Query<UnitInfo> query = unitInfoService.createQuery();
        query.filter("cid", cid);
        query.filter("unitCode", unitCode);
        UnitInfo unitInfo = unitInfoService.get(query);

        String path = "/app/external/rushplan/interface/get_subunit_list";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userCode", unitInfo.getUserCode());
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getRushPlanList(String unitCode, String defaultDownUnit, Integer bizType, Integer page) {
        String path = "/app/external/rushplan/interface/get_rush_plan_list";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("unitCode", unitCode.substring(4, 10));
        paramMap.put("bizType", bizType);
        paramMap.put("defaultDownUnit", defaultDownUnit);
        if (StrUtil.isNotEmpty(page)) paramMap.put("pageNum", String.valueOf(page));
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> addRushPlan(String unitCode, String id, Integer expectCarCount, Boolean isTakeAll, String bizUnitCode) {
        String path = "/app/external/rushplan/interface/add_from_rush_plan";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        paramMap.put("expectCarCount", expectCarCount);
        paramMap.put("isTakeAll", isTakeAll);
        paramMap.put("bizUnitCode", bizUnitCode);
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getVarietyList(String unitCode) {
        String path = "/app/external/rushplan/interface/get_variety_list";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("unitCode", unitCode.substring(4, 10));
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> saveRushDetails(String unitCode, String defaultDownUnit, String bizUnitCode, String varietyCode, Integer bizType, Integer carCount, Double weightCount, Long planStartTime, Long planEndTime, Integer planLeadTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String path = "/app/external/rushplan/interface/save_rushdetails";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("unitCode", unitCode.substring(4, 10));
        paramMap.put("defaultDownUnit", defaultDownUnit);
        paramMap.put("bizUnitCode", bizUnitCode);
        paramMap.put("varietyCode", varietyCode);
        paramMap.put("bizType", bizType);
        paramMap.put("carCount", carCount);
        paramMap.put("weightCount", weightCount);
        paramMap.put("planStartTime", StrUtil.isEmpty(planStartTime) ? "" : simpleDateFormat.format(new Date(planStartTime)));
        paramMap.put("planEndTime", StrUtil.isEmpty(planEndTime) ? "" : simpleDateFormat.format(new Date(planEndTime)));
        paramMap.put("planLeadTime", planLeadTime);
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getRushDetailsList(String unitCode, String defaultDownUnit, String bizUnitCode, Integer bizType, Integer page) {
        String path = "/app/external/rushplan/interface/get_rushdetails_list";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bizType", bizType);
        paramMap.put("defaultDownUnit", defaultDownUnit);
        paramMap.put("bizUnitCode", bizUnitCode);
        paramMap.put("unitCode", unitCode.substring(4, 10));
        if (StrUtil.isNotEmpty(page)) paramMap.put("pageNum", String.valueOf(page));
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getRushDetailsFromRushPlan(String unitCode, String defaultDownUnit, String bizUnitCode, Integer bizType, Integer page) {
        String path = "/app/external/rushplan/interface/get_rush_details_from_rush_plan";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("unitCode", unitCode.substring(4, 10));
        paramMap.put("defaultDownUnit", defaultDownUnit);
        paramMap.put("bizUnitCode", bizUnitCode);
        paramMap.put("bizType", bizType);
        if (StrUtil.isNotEmpty(page)) paramMap.put("pageNum", String.valueOf(page));
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getInfoFromWorked(String unitCode, String subCode, Integer bizType, Long startTime, Long endTime, Integer page) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String cid = ShiroUtils.getUserId();
        Query<UnitInfo> query = unitInfoService.createQuery();
        query.filter("cid", cid);
        query.filter("unitCode", unitCode);
        UnitInfo unitInfo = unitInfoService.get(query);
        if (unitInfo == null) return ServerResponse.createError("一级单位错误");

        String path = "/app/external/interface/get_info_from_worked";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bizType", bizType);
        paramMap.put("userCode", unitInfo.getUserCode());
        paramMap.put("unitCode", unitCode.substring(4, 10));
        paramMap.put("startTime", StrUtil.isEmpty(startTime) ? "" : simpleDateFormat.format(new Date(startTime)));
        paramMap.put("endTime", StrUtil.isEmpty(endTime) ? "" : simpleDateFormat.format(new Date(endTime)));
        if (StrUtil.isNotEmpty(subCode)) paramMap.put("subCode", subCode);
        if (StrUtil.isNotEmpty(page)) paramMap.put("pageNum", String.valueOf(page));
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getTotalInfoFromWorked(String unitCode, String subCode, Integer bizType, Long startTime, Long endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String cid = ShiroUtils.getUserId();
        Query<UnitInfo> query = unitInfoService.createQuery();
        query.filter("unitCode", unitCode);
        query.filter("cid", cid);
        UnitInfo unitInfo = unitInfoService.get(query);

        String path = "/app/external/interface/get_total_info_from_worked";
        Map<String, Object> paramMap = new HashMap<>();
        if (StrUtil.isNotEmpty(subCode)) paramMap.put("subCode", subCode);
        paramMap.put("bizType", bizType);
        paramMap.put("userCode", unitInfo.getUserCode());
        paramMap.put("unitCode", unitCode.substring(4, 10));
        paramMap.put("endTime", StrUtil.isEmpty(endTime) ? "" : simpleDateFormat.format(new Date(endTime)));
        paramMap.put("startTime", StrUtil.isEmpty(startTime) ? "" : simpleDateFormat.format(new Date(startTime)));
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public ServerResponse<Object> getBizUnitCodeList(String unitCode, Integer bizType) {
        String cid = ShiroUtils.getUserId();
        Query<UnitInfo> query = unitInfoService.createQuery();
        query.filter("cid", cid);
        query.filter("unitCode", unitCode);
        UnitInfo unitInfo = unitInfoService.get(query);

        String path = "/app/external/rushplan/interface/get_bizunitcode_list";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userCode", unitInfo.getUserCode());
        paramMap.put("bizType", bizType);
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public String saveRushDriver(String ip, String saSubCode, Order order, Long startTime, Long endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String url = ip + "/app/external/rushplan/interface/save_rush_driver";
        Map<String, Object> paramMap = new HashMap<>();
        if (saSubCode.equals(order.getOutDefaultDownUnit())) {
            paramMap.put("billCode", order.getOutBillCode());
        } else if (saSubCode.equals(order.getInDefaultDownUnit())) {
            paramMap.put("billCode", order.getInBillCode());
        } else {
            return "参数错误";
        }
        paramMap.put("forwardStartTime", simpleDateFormat.format(startTime));
        paramMap.put("forwardEndTime", simpleDateFormat.format(endTime));
        paramMap.put("carNum", order.getCarNum());
        HttpResult result = getCompanyMessage(url, paramMap);

        if (result == null || result.getCode() != 200) return "网络异常，稍后重试";

        JSONObject object = JSONObject.parseObject(result.getBody());
        if (StrUtil.isNotEmpty(object.get("status")) && (int) object.get("status") == 0)
            return (String) object.get("msg");

        return null;
    }

    @Override
    public ServerResponse<Object> getQueuingReservation(String unitCode, String billCode) {
        /*
         * 查询排队预约接口 （IP验证，签名验证）
         * Url:/app/external/rushplan/interface/get_queuing_reservation
         *
         * 参数：
         * billCode（订单号，20位长度）
         *
         * 返回参数：
         * info.put("status", 1);
         * info.put("msg", "操作成功!");
         * info.put("data", param);
         *
         * info.put("status", 0);
         * info.put("msg", "操作失败!");
         * info.put("data", null);
         */
        String path = "/app/external/rushplan/interface/get_queuing_reservation";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("billCode", billCode);
        return appToSysWithRush(unitCode, path, paramMap);
    }

    @Override
    public String saveLineCheckIn(String ip, String saSubCode, Order order, String longitude, String latitude) {
        String url = ip + "/app/external/rushplan/interface/save_line_check_in";
        Map<String, Object> paramMap = new HashMap<>();
        if (saSubCode.equals(order.getOutDefaultDownUnit())) {
            paramMap.put("billCode", order.getOutBillCode());
        } else if (saSubCode.equals(order.getInDefaultDownUnit())) {
            paramMap.put("billCode", order.getInBillCode());
        } else {
            return "参数错误";
        }

        paramMap.put("carNum", order.getCarNum());
        //2023年4月20日 请求接口新加参数
        paramMap.put("oid", order.getOid());
        paramMap.put("longitude", longitude);
        paramMap.put("latitude", latitude);
        HttpResult result = getCompanyMessage(url, paramMap);

        if (result == null || result.getCode() != 200) return "网络异常，稍后重试";

        JSONObject object = JSONObject.parseObject(result.getBody());
        if (StrUtil.isNotEmpty(object.get("status")) && (int) object.get("status") == 0)
            return (String) object.get("msg");

        return null;
    }

    @Override
    public ServerResponse<Object> getPlaceAreaInfo(String unitCode, String subCode, String spell) {
        String path = "/app/external/interface/get_place_area_info";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("unitCode", unitCode);
        paramMap.put("subCode", subCode);
        if (StrUtil.isNotEmpty(spell)) paramMap.put("spell", spell);
        return appToSysWithRush(unitCode, path, paramMap);
    }

    //查询企业客商的合同、商品、二级单位等信息
    //计划抢运查询二级单位，品种，计划抢运，计划提交，计划抢运列表，计划提交列表
    private HttpResult getCompanyMessage(String url, Map<String, Object> map) {
        //TODO:1.URL请求加盐
        try {
            map.put("sign", IdUnit.getSign(map));
        } catch (Exception e) {
            e.printStackTrace();
        }

        HttpResult result = null;
        try {
            String jsonStr = JSONObject.toJSONString(map);
            result = httpAPIService.sendPost(url, jsonStr);

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return result;
    }

    private String loginByTruckNumAndKey(String licensePlate) throws Exception {
        String appUrl = "https://webapi.zkgis.cn/login/loginByTruckNumAndKey";
        String appKey = "Kb468d1c32ba3ed3710dd0e960bf1eabb";
        String appSecret = "S8754c721732ac9ef7fcc4dcf5a35deb3";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appkey", appKey);
        paramMap.put("appsecret", appSecret);
        paramMap.put("identity", "*");
        paramMap.put("truckNums", licensePlate);
        String bodyStr = HddEncryptRequest.doRequest(appUrl, appKey, appSecret, paramMap);
        JSONObject jsonObject = JSONObject.parseObject(bodyStr);
        String authorization = jsonObject.getString("data");

        return authorization;
    }

    @Override
    public Map<String, Object> checkTruckExistV2(String licensePlate) {
        Map<String, Object> result = new HashMap<>();
        try {
            String authorization = loginByTruckNumAndKey(licensePlate);
            String appUrl_2 = "https://webapi.zkgis.cn/validate/checkTruckExistV2?licensePlate=" + licensePlate;
            String bodyStr_2 = HddEncryptRequest.doRequest(appUrl_2, authorization);
            JSONObject jsonObject = JSONObject.parseObject(bodyStr_2);
            String msg = jsonObject.getString("msg");
            String data = jsonObject.getString("data");

            result.put("msg", msg);
            result.put("data", data);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String getCurrentPosition(String licensePlate) {
        try {
            String authorization = loginByTruckNumAndKey(licensePlate);
            String appUrl_3 = "https://webapi.zkgis.cn/latest/getCurrentPosition?licensePlate=" + licensePlate;
            String bodyStr_3 = HddEncryptRequest.doRequest(appUrl_3, authorization);

            return bodyStr_3;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String getHistoricalTrack(String licensePlate, String startTime, String endTime) {
        try {
            String authorization = loginByTruckNumAndKey(licensePlate);
            String appUrl_4 = "https://webapi.zkgis.cn/history/getHistoricalTrack?licensePlate=" + licensePlate + "&startTime=" + startTime + "&endTime=" + endTime;
            String bodyStr_4 = HddEncryptRequest.doRequest(appUrl_4, authorization);

            return bodyStr_4;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
