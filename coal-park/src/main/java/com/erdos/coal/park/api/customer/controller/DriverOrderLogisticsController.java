package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.OrderLogistics;
import com.erdos.coal.park.api.driver.service.IOrderLogisticsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单物流位置信息接口
 */
//"客商APP订单物流接口列表"
@RestController
@RequestMapping("/api/cus/order_logistics")
public class DriverOrderLogisticsController extends BaseController {
    @Resource
    private IOrderLogisticsService orderLogisticsService;

    @InvokeLog(description = "查询订单物流信息 接口", printReturn = false)//日志
    @PostMapping(value = "/query_order_logistics")
    public ServerResponse<OrderLogistics> queryOrderLogisticsHandler(
            @RequestParam(value = "oid") String oid     //"订单编号"
    ) throws GlobalException {
        return orderLogisticsService.getByOid(oid);
    }

    @InvokeLog(description = "查询客商物流订单列表 接口", printReturn = false)//日志
    @PostMapping(value = "/logistics_order_list")
    public ServerResponse<EGridResult> logisticsOrderListHandler(
            @RequestParam(value = "page", required = false) Integer page,       //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows        //"每页多少条"
    ) throws GlobalException {
//        return orderLogisticsService.logisticsOrderList(page, rows);
        return orderLogisticsService.logisticsOrderList2(page, rows);
    }

}
