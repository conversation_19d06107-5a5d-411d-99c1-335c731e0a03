package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.park.api.business.entity.TradeContract;

import java.util.List;
import java.util.Map;

public interface ITradeContractService extends IBaseMongoService<TradeContract> {
    boolean add(List<TradeContract> tradeContracts);

    List<Integer> edit(Map<Integer, TradeContract> updateTradeContractMap);

    boolean del(List<String> bizContractCodes);
}
