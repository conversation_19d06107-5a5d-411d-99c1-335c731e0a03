package com.erdos.coal.park.api.customer.pojo;

import java.io.Serializable;

public class GoodsVerbInfoData implements Serializable {
    private String beginPoint;     //起点
    private String endPoint;        //终点
    private Integer total;           //总车数
    private Double weight;          //车载重量要求
    private Double price;           //单价
    private Double distance;          //总里程
    private Double tolls;           //预估过路费

    private Integer mold2;          //是否往货业务，空或0-否，1-是
    private String beginDistrictCode;//起点区划编码
    private String endDistrictCode; //终点区划编码

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public Integer getMold2() {
        return mold2;
    }

    public void setMold2(Integer mold2) {
        this.mold2 = mold2;
    }

    public String getBeginDistrictCode() {
        return beginDistrictCode;
    }

    public void setBeginDistrictCode(String beginDistrictCode) {
        this.beginDistrictCode = beginDistrictCode;
    }

    public String getEndDistrictCode() {
        return endDistrictCode;
    }

    public void setEndDistrictCode(String endDistrictCode) {
        this.endDistrictCode = endDistrictCode;
    }
}
