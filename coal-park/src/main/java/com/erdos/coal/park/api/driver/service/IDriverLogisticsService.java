package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverLogistics;
import org.bson.types.ObjectId;

import java.util.List;

public interface IDriverLogisticsService extends IBaseMongoService<DriverLogistics> {

    //添加司机实时位置信息
    ServerResponse<String> addDriverLogistics(DriverInfo driverInfo);

    //更新司机实时位置信息 和 司机是否接单状态
    ServerResponse<String> updateLogisticsOrIsTag(ObjectId objectId, String longitude, String latitude, Integer isTag);

    //删除司机实时位置信息
    ServerResponse<String> delDriverLogistics(ObjectId driverID);

    //查询指定坐标范围内的司机列表
    ServerResponse<List<DriverLogistics>> selectDriverByLogistics(String longitude, String latitude, Double radius, Integer limit);
}
