package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.manage.entity.OwnerAppeal;
import com.erdos.coal.park.web.app.service.IWebOwnerAppealService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/message/ownerAppeal")
public class WebOwnerAppealController extends BaseController {

    @Resource
    private IWebOwnerAppealService appealService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(appealService.loadGrid(page, rows));
    }

    @PostMapping("/edit")
    public ServerResponse<OwnerAppeal> editHandler() {

        return appealService.edit();
    }

    @PostMapping("/saveCheck")
    public ServerResponse saveCheckHandler(@RequestBody OwnerAppeal ownerAppeal) {
        return appealService.saveCheck(ownerAppeal);
    }
}
