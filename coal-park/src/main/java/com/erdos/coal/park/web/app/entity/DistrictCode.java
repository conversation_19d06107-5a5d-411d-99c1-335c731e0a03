package com.erdos.coal.park.web.app.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_district_code", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")}),
        @Index(fields = {@Field("code")})
})
public class DistrictCode extends BaseMongoInfo {
    private String id;          //id
    private String name;        //名称
    private String code;        //编码
    private String longitude;   //经度
    private String latitude;    //纬度

    public DistrictCode() {
    }

    public DistrictCode(String id, String name, String code) {
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public DistrictCode(String id, String name, String code, String longitude, String latitude) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.longitude = longitude;
        this.latitude = latitude;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}
