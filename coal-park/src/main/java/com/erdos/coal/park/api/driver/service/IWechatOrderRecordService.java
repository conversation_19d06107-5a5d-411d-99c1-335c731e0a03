package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.driver.entity.OrderRecord;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;

import java.util.List;
import java.util.Map;

public interface IWechatOrderRecordService extends IBaseMongoService<OrderRecord> {
    //批量下单
//    ServerResponse<Map<String, String>> batch(String carNum, String gid, String[] oids, String longitude, String latitude);
//    ServerResponse<Map<String, String>> batch(String carNum, String gid, String[] oids, String longitude, String latitude, String fee);
    ServerResponse<Map<String, String>> batch(String carNum, String carInfoId, String gid, String[] oids, String longitude, String latitude, String fee);

    //查询支付是否成功
    ServerResponse<Map<String, String>> search(String transactionId, String outTradeNo, String longitude, String latitude, String gid, String oid);
    ServerResponse<Map<String, String>> search2(String transactionId, String outTradeNo, String longitude, String latitude, String gid, String oid);

    //存储回调结果，
    WxResult payResult(Map<String, String> map);

    //查询司机订单号
    ServerResponse<List<DriverOrderData>> queryOrder(int[] finishTag, Long finishTime);

    //查询货物信息
    ServerResponse<GoodsInfoData> searchGoods(String gid);

    //司机需要支付金额
//    ServerResponse<Double> searchFees(String gid);
}
