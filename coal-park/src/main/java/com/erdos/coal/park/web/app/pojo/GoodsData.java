package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;

public class GoodsData implements Serializable {
    private String mobile;
    private String gid;             //货运编号
    private String tradeName;      //商品名称
    private String beginPoint;     //起点
    private String endPoint;        //终点
    private Integer total;           //总车数
    private Double weight;          //车载重量要求
    //private Long releaseTime;     //发布时间

    private Double price;           //单价
    private Double distance;          //总里程
    private Double tolls;           //预估过路费

    private String outUnitName;             //发货单位名称
    private String inUnitName;              //收货单位名称
    private String outBizContractName;      //发货单位业务合同名称
    private String inBizContractName;       //收货单位业务合同名称
    private String outVariety;          //发货单位产品名称
    private String inVariety;           //收货单位产品名称
    private String outSubName;                      //发货单位 默认二级单位名称
    private String inSubName;                       //收货单位 默认二级单位名称
    private String outAreaName;                 //发货单位 默认场区名称
    private String inAreaName;                  //收货单位 默认场区名称

    private Integer outMin;         //发货单位 最小票号
    private Integer outMax;         //发货单位 最大票号
    private Integer inMin;          //收货单位 最小票号
    private Integer inMax;          //收货单位 最大票号

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getBeginPoint() {
        return beginPoint;
    }

    public void setBeginPoint(String beginPoint) {
        this.beginPoint = beginPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getTolls() {
        return tolls;
    }

    public void setTolls(Double tolls) {
        this.tolls = tolls;
    }

    public String getOutUnitName() {
        return outUnitName;
    }

    public void setOutUnitName(String outUnitName) {
        this.outUnitName = outUnitName;
    }

    public String getInUnitName() {
        return inUnitName;
    }

    public void setInUnitName(String inUnitName) {
        this.inUnitName = inUnitName;
    }

    public String getOutBizContractName() {
        return outBizContractName;
    }

    public void setOutBizContractName(String outBizContractName) {
        this.outBizContractName = outBizContractName;
    }

    public String getInBizContractName() {
        return inBizContractName;
    }

    public void setInBizContractName(String inBizContractName) {
        this.inBizContractName = inBizContractName;
    }

    public String getOutVariety() {
        return outVariety;
    }

    public void setOutVariety(String outVariety) {
        this.outVariety = outVariety;
    }

    public String getInVariety() {
        return inVariety;
    }

    public void setInVariety(String inVariety) {
        this.inVariety = inVariety;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public String getOutAreaName() {
        return outAreaName;
    }

    public void setOutAreaName(String outAreaName) {
        this.outAreaName = outAreaName;
    }

    public String getInAreaName() {
        return inAreaName;
    }

    public void setInAreaName(String inAreaName) {
        this.inAreaName = inAreaName;
    }

    public Integer getOutMin() {
        return outMin;
    }

    public void setOutMin(Integer outMin) {
        this.outMin = outMin;
    }

    public Integer getOutMax() {
        return outMax;
    }

    public void setOutMax(Integer outMax) {
        this.outMax = outMax;
    }

    public Integer getInMin() {
        return inMin;
    }

    public void setInMin(Integer inMin) {
        this.inMin = inMin;
    }

    public Integer getInMax() {
        return inMax;
    }

    public void setInMax(Integer inMax) {
        this.inMax = inMax;
    }
}
