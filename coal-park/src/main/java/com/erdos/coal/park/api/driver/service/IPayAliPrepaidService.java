package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.PayAliPrepaid;

public interface IPayAliPrepaidService extends IBaseMongoService<PayAliPrepaid> {
    //2.请求商户服务端，获取签名后的订单信息-司机充值预下单
    ServerResponse<String> createAliOrder(String body, String subject, String totalAmount);

    //2.请求商户服务端，获取签名后的订单信息-充值预下单
    ServerResponse<String> createAliOrder(String masterId, String body, String subject, String totalAmount, String guestId, Integer type, PayAliPrepaid aliPrepaid, Integer cusOrDri);
}
