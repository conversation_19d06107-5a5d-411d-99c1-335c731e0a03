package com.erdos.coal.park.api.business.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.business.dao.ITradeUnitDao;
import com.erdos.coal.park.api.business.entity.TradeUnit;
import com.erdos.coal.park.api.business.service.ITradeUnitService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("tradeUnitService")
public class TradeUnitServiceImpl extends BaseMongoServiceImpl<TradeUnit, ITradeUnitDao> implements ITradeUnitService {
    @Override
    public boolean add(List<TradeUnit> tradeUnits) {
        try {
            this.save(tradeUnits);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public List<Integer> edit(Map<Integer, TradeUnit> updateTradeUnitMap) {
        List<Integer> successIds = new ArrayList<>();
        for (Map.Entry<Integer, TradeUnit> entry : updateTradeUnitMap.entrySet()){
            TradeUnit tradeUnit = entry.getValue();
            Query<TradeUnit> query = this.createQuery();
            query.criteria("bizUnitCode").equal(tradeUnit.getBizUnitCode());
            UpdateOperations<TradeUnit> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(tradeUnit.getAllowLimit())) updateOperations.set("allowLimit",tradeUnit.getAllowLimit());
            if (StrUtil.isNotEmpty(tradeUnit.getAutoChangeCon())) updateOperations.set("autoChangeCon",tradeUnit.getAutoChangeCon());
            if (StrUtil.isNotEmpty(tradeUnit.getAutoChangeProduct())) updateOperations.set("autoChangeProduct",tradeUnit.getAutoChangeProduct());
            if (StrUtil.isNotEmpty(tradeUnit.getBalanceCtrl())) updateOperations.set("balanceCtrl",tradeUnit.getBalanceCtrl());
            if (StrUtil.isNotEmpty(tradeUnit.getBizType())) updateOperations.set("bizType",tradeUnit.getBizType());
            if (StrUtil.isNotEmpty(tradeUnit.getBizUnitABR())) updateOperations.set("bizUnitABR",tradeUnit.getBizUnitABR());
            if (StrUtil.isNotEmpty(tradeUnit.getBizUnitName())) updateOperations.set("bizUnitName",tradeUnit.getBizUnitName());
            if (StrUtil.isNotEmpty(tradeUnit.getCarSummary())) updateOperations.set("carSummary",tradeUnit.getCarSummary());
            if (StrUtil.isNotEmpty(tradeUnit.getDaySummary())) updateOperations.set("daySummary",tradeUnit.getDaySummary());
            if (StrUtil.isNotEmpty(tradeUnit.getEarlyWarn())) updateOperations.set("earlyWarn",tradeUnit.getEarlyWarn());
            if (StrUtil.isNotEmpty(tradeUnit.getLimitAllow())) updateOperations.set("limitAllow",tradeUnit.getLimitAllow());
            if (StrUtil.isNotEmpty(tradeUnit.getLimitWeight())) updateOperations.set("limitWeight",tradeUnit.getLimitWeight());
            if (StrUtil.isNotEmpty(tradeUnit.getMessageInteraction())) updateOperations.set("messageInteraction",tradeUnit.getMessageInteraction());
            if (StrUtil.isNotEmpty(tradeUnit.getOrderContractFill())) updateOperations.set("orderContractFill",tradeUnit.getOrderContractFill());
            if (StrUtil.isNotEmpty(tradeUnit.getOrderContractNull())) updateOperations.set("orderContractNull",tradeUnit.getOrderContractNull());
            if (StrUtil.isNotEmpty(tradeUnit.getOrderProductFill())) updateOperations.set("orderProductFill",tradeUnit.getOrderProductFill());
            if (StrUtil.isNotEmpty(tradeUnit.getOrderProductNull())) updateOperations.set("orderProductNull",tradeUnit.getOrderProductNull());
            if (StrUtil.isNotEmpty(tradeUnit.getOverdraft())) updateOperations.set("overdraft",tradeUnit.getOverdraft());
            if (StrUtil.isNotEmpty(tradeUnit.getStatus())) updateOperations.set("status",tradeUnit.getStatus());
            if (StrUtil.isNotEmpty(tradeUnit.getSubCode())) updateOperations.set("subCode",tradeUnit.getSubCode());
            if (StrUtil.isNotEmpty(tradeUnit.getTransparent())) updateOperations.set("transparent",tradeUnit.getTransparent());
            if (StrUtil.isNotEmpty(tradeUnit.getTriggerBalance())) updateOperations.set("triggerBalance",tradeUnit.getTriggerBalance());
            if (StrUtil.isNotEmpty(tradeUnit.getTriggerSwitch())) updateOperations.set("triggerSwitch",tradeUnit.getTriggerSwitch());
            if (StrUtil.isNotEmpty(tradeUnit.getUnitCode())) updateOperations.set("unitCode",tradeUnit.getUnitCode());
            if (StrUtil.isNotEmpty(tradeUnit.getWeightLimit())) updateOperations.set("weightLimit",tradeUnit.getLimitWeight());
            TradeUnit unit = this.findAndModify(query, updateOperations);
            if (unit != null) successIds.add(entry.getKey());
        }
        return successIds;
    }

    @Override
    public boolean del(List<String> bizUnitCodes) {
        try {
            Query<TradeUnit> query = this.createQuery();
            query.criteria("bizUnitCode").in(bizUnitCodes);
            this.delete(query);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
