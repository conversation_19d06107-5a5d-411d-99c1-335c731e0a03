package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.config.CoalConfig;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.sys.dao.ISysAppDao;
import com.erdos.coal.park.web.sys.entity.SysApp;
import com.erdos.coal.park.web.sys.service.ISysAppService;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.Date;

@Service("sysAppService")
public class SysAppServiceImpl extends BaseMongoServiceImpl<SysApp, ISysAppDao> implements ISysAppService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private ILockedService lockedService;
    @Resource
    private CoalConfig coalConfig;

    @Override
    public EGridResult loadGrid(Integer page, Integer rows) {
        Query<SysApp> query = this.createQuery();
        query.order(Sort.ascending("type"), Sort.descending("createTime"));
        return this.findPage(page, rows, query);
    }

    @Override
    public ServerResponse saveApp(MultipartFile file) {
        int type = Integer.parseInt(request.getParameter("type"));  //0-客商app文件，1-司机文件

        //防抖动重复提交加锁
//        String userType = request.getAttribute(Constant.CURRENT_USER_TYPE_KEY).toString();    //统一对web用户操作加索
        String userType = ShiroUtils.getUserType();
        boolean lock = lockedService.getLock(userType, type);
        if (!lock) {
            return ServerResponse.createError("请获取最新数据");
        } else {
            try {
                SysApp sysApp = this.get("type", type);
                if (sysApp == null) {
                    sysApp = new SysApp();
                    sysApp.setVersion(request.getParameter("version"));
                    sysApp.setType(type);
                    sysApp.setIsUpdate(Integer.parseInt(request.getParameter("isUpdate")));
                    sysApp.setName(file.getOriginalFilename());
                    sysApp.setContent(request.getParameter("content"));
                    sysApp = this.save(sysApp);
                } else {
                    sysApp.setContent(request.getParameter("content"));
                    UpdateOperations<SysApp> updateOperations = this.createUpdateOperations();
                    updateOperations.set("version", request.getParameter("version"));
                    updateOperations.set("isUpdate", request.getParameter("isUpdate"));
                    updateOperations.set("name", file.getOriginalFilename());
                    updateOperations.set("updateTime", new Date().getTime());
                    updateOperations.set("content", request.getParameter("content"));
                    this.update(this.createQuery().filter("_id", sysApp.getObjectId()), updateOperations);
                }
//        fileInfoDao.fileSave(sysApp.getObjectId().toString(), file, "sys_app");   // 存磁盘，不保存数据库

                saveFile(file, sysApp.getObjectId().toString(), type);

                return ServerResponse.createSuccess("保存成功！");
            } finally {
                lockedService.unLock(userType, type);
            }
        }

    }

    public ServerResponse deleteApp(SysApp sysApp) {
        this.delete(sysApp.getObjectId());

        File origFile = new File(coalConfig.uploadPath + sysApp.getObjectId().toString() + ".apk");
        if (origFile.exists()) {//磁盘文件存在删除
            origFile.delete();
        }
//        fileInfoDao.deleteByUId(sysApp.getObjectId().toString(), "sys_app");

        return ServerResponse.createSuccess();
    }

    // 保存文件到磁盘，并复制一份文件修改名称
    private void saveFile(MultipartFile file, String id, Integer type) {
        File origFile = new File(coalConfig.uploadPath);
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            if (!origFile.exists()) {
                origFile.mkdirs();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        //文件（图片）流写入磁盘
        String filePath = coalConfig.uploadPath + id + ".apk";
        BufferedInputStream in = null;
        BufferedOutputStream out = null;

        try {
            in = new BufferedInputStream(inputStream);

            out = new BufferedOutputStream(new FileOutputStream(filePath));
            int len = -1;
            byte[] b = new byte[1024];
            while ((len = in.read(b)) != -1) {
                out.write(b, 0, len);
            }
            in.close();
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        File rawFile = new File(coalConfig.uploadPath + id + ".apk");
        try {
            if (!rawFile.exists())
                logger.warn("文件不存在:" + rawFile.getName());
            if (type == 0) {
                FileCopyUtils.copy(
                        rawFile,
                        new File(coalConfig.uploadPath + "customer.apk"));
            } else {
                FileCopyUtils.copy(
                        rawFile,
                        new File(coalConfig.uploadPath + "driver.apk"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
