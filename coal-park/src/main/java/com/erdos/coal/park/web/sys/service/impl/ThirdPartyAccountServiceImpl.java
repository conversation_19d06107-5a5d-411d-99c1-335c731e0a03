package com.erdos.coal.park.web.sys.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.alibaba.sms.service.SMSService;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.RefundPre;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IRefundPreService;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IWxResultService;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.park.web.sys.dao.IThirdPartyAccountDao;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.ThirdPartyAccount;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.park.web.sys.service.IThirdPartyAccountService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.WXPay;
import com.erdos.coal.transaction.wxpay.service.WXPayUtil;
import com.erdos.coal.transaction.wxpay.service.WXPayV3;
import com.erdos.coal.transaction.wxpay.service.WXPayWechatCusConfig;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.huawei.service.HuaWeiSMSService;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

@Service("thirdPartyAccountService")
public class ThirdPartyAccountServiceImpl extends BaseMongoServiceImpl<ThirdPartyAccount, IThirdPartyAccountDao> implements IThirdPartyAccountService {
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private WXPayWechatCusConfig wxPayWechatCusConfig;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private ISMSService smsService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private MongoClient client;
    @Resource
    private IRefundPreService refundPreService;
    @Resource
    private WXPayV3 wxPayV3;
//    @Override
//    public Document createAccountDoc(String id, String payerId, int fee, Integer type, String oid, Date time) {
//        ThirdPartyAccount account = new ThirdPartyAccount();
//        account.setId(id);
//        if (StrUtil.isNotEmpty(payerId)) account.setPayerId(payerId);
//        account.setOid(oid);
//        account.setType(type);
//        account.setUpdateTime(time.getTime());
//        Document accountDoc = Document.parse(JSONObject.toJSONString(account));
//        accountDoc.append("totalFee", new BigDecimal(fee));
//        accountDoc.append("createTime", time);
//        return accountDoc;
//    }

    @Override
    public Document createAccountDoc(String unitCode, String id, String name, String payerId, int fee, Integer type, String oid, Date time) {
        ThirdPartyAccount account = new ThirdPartyAccount();
        account.setUnitCode(unitCode);
        account.setId(id);
        account.setName(name);
        if (StrUtil.isNotEmpty(payerId)) account.setPayerId(payerId);
        account.setOid(oid);
        account.setType(type);
        account.setUpdateTime(time.getTime());
        Document accountDoc = Document.parse(JSONObject.toJSONString(account));
        accountDoc.append("totalFee", new BigDecimal(fee));
        accountDoc.append("createTime", time);
        return accountDoc;
    }

    @Override
    public Document createAccountDoc(SysUnit sysUnit, String payerId, int fee, Integer type, String oid, Date time) {
        ThirdPartyAccount account = new ThirdPartyAccount();
        account.setUnitCode(sysUnit.getCode());
        account.setId(sysUnit.getThirdPartyAccount());
        if (StrUtil.isNotEmpty(sysUnit.getThirdPartyAccount2())) {
            account.setId(sysUnit.getThirdPartyAccount2());
        }
        account.setName(sysUnit.getThirdPartyName());
        if (StrUtil.isNotEmpty(payerId)) account.setPayerId(payerId);
        account.setOid(oid);
        account.setType(type);
        account.setUpdateTime(time.getTime());
        Document accountDoc = Document.parse(JSONObject.toJSONString(account));
        accountDoc.append("totalFee", new BigDecimal(fee));
        accountDoc.append("createTime", time);
        return accountDoc;
    }

    @Override
    public Document createAccountDoc(SysUnit sysUnit, String payerId, BigDecimal fee, Integer type, Date time, String preferentialRefundNo) {
        ThirdPartyAccount account = new ThirdPartyAccount();
        account.setUnitCode(sysUnit.getCode());
        account.setId(sysUnit.getThirdPartyAccount());
        if (StrUtil.isNotEmpty(sysUnit.getThirdPartyAccount2())) account.setId(sysUnit.getThirdPartyAccount2());
        account.setName(sysUnit.getThirdPartyName());
        if (StrUtil.isNotEmpty(payerId)) account.setPayerId(payerId);
        account.setType(type);
        account.setUpdateTime(time.getTime());
        account.setPreferentialRefundNo(preferentialRefundNo);
        Document accountDoc = Document.parse(JSONObject.toJSONString(account));
        accountDoc.append("totalFee", fee);
        accountDoc.append("createTime", time);
        return accountDoc;
    }

    @Override
    @Async("busTaskExecutor")
    public void partyWXChange(String oid) {
        Query<ThirdPartyAccount> query = this.createQuery();
        query.criteria("oid").equal(oid);
        query.criteria("type").equal(0);    //司机接单，向三方账户转账
        List<ThirdPartyAccount> list = this.list(query);

        for (ThirdPartyAccount account : list) {
            //"微信转账，单日单次金额不能超过5000元"
            //转账金额应小于平台和单位所收费用
            SysUnit unit = null;
            if (StrUtil.isNotEmpty(account.getUnitCode()))
                unit = sysUnitService.get("code", account.getUnitCode());

            Date date = new Date();
            long timeStart = date.getTime();
            String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);

            Query<ThirdPartyAccount> noQuery = this.createQuery();
            noQuery.criteria("objectId").equal(account.getObjectId());
            UpdateOperations<ThirdPartyAccount> updateOP = this.createUpdateOperations();
            updateOP.set("outTradeNo", out_trade_no);
            UpdateResults a = this.update(noQuery, updateOP);
            if (a.getUpdatedExisting()) {    // 转账到三方账户前，生成并成功修改转账需要到唯一订单号outTradeNo，才能开始转账
                String openid = account.getId();
                String name = account.getName();
                if (unit != null) {
                    openid = unit.getThirdPartyAccount();
                    name = unit.getThirdPartyName();
                }
                toThirdParty(openid, name, out_trade_no, account.getTotalFee().toString());
            }
        }
    }

    private void toThirdParty(String openid, String name, String outTradeNo, String totalFee) {// ThirdPartyAccount thirdPartyAccount) {
        /*RefundPre refundPre = new RefundPre();

        refundPre.setAccountNumber(openid);
        refundPre.setRemark("合作伙伴订单货款");
        refundPre.setName(name);    //收款人姓名

        Date date = new Date();
        long timeStart = date.getTime();
        String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);

        refundPre.setUserId(openid);
        refundPre.setAmount(fee);//提现金额
        refundPre.setTransferNo(out_trade_no);//订单号
        refundPre.setRefundType(1);//微信
        refundPre.setCheck(0);
        refundPre.setUpdateTime(date.getTime());
        Document refundDocument = Document.parse(JSONObject.toJSONString(refundPre));
        refundDocument.append("createTime", date);
        if (UserType.DU.toString().equals(userType)) {
            refundDocument.append("driverInfoID", new ObjectId(userId));
        } else {
            refundDocument.append("customerUserID", new ObjectId(userId));
        }

        return ServerResponse.createSuccess("提现申请已提交");*/

        logger.info("===============================================toThirdParty============================================");
        WXPay wxpay = null;
        try {
            wxpay = new WXPay(wxPayWechatCusConfig, wxPayConstants.notifyUrl, true, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //1.0 拼凑企业支付需要的参数
        String appid = wxPayWechatCusConfig.getAppID();  //微信公众号的appid
        String mch_id = wxPayWechatCusConfig.getMchID(); //商户号

        String nonce_str = WXPayUtil.generateNonceStr(32); //生成随机数
        InetAddress address = null;
        try {
            address = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        //2.0 生成map集合
        Map<String, String> packageParams = new HashMap<>();
        packageParams.put("mch_appid", appid);         //微信公众号的appid
        packageParams.put("mchid", mch_id);       //商户号
        packageParams.put("nonce_str", nonce_str);  //随机生成后数字，保证安全性
        packageParams.put("partner_trade_no", outTradeNo); //生成商户订单号
        packageParams.put("openid", openid);            // 支付给用户openid
        packageParams.put("check_name", "FORCE_CHECK");    //是否验证真实姓名呢
        packageParams.put("re_user_name", name);//收款用户姓名
        packageParams.put("amount", totalFee);            //企业付款金额，单位为分
        packageParams.put("desc", "合作伙伴订单货款");                   //企业付款操作说明信息。必填。
        packageParams.put("spbill_create_ip", address.getHostAddress()); //调用接口的机器Ip地址
        String sign = null;
        try {
            sign = WXPayUtil.generateSignature(packageParams, wxPayWechatCusConfig.getKey(), WXPayConstants.SignType.MD5);
        } catch (Exception e) {
            e.printStackTrace();
        }
        packageParams.put("sign", sign);

        //3.0 调用微信转账接口
        Map<String, String> result = null;
        try {
            result = wxpay.transfers(packageParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("===============================================wxpay.transfers.result========================================");
        logger.info(result.toString());

        //4.0 处理微信转账结果
        this.saveWxResult(packageParams, result);

    }

    /**
     * 1.记录转账结果
     * 2.修改三方账户表转账结果标记
     */
    private void saveWxResult(Map<String, String> params, Map<String, String> result) {
        int pay = 0;
        String tradeNo = params.get("partner_trade_no");
        //TODO 1.将转账参数和结果存入WxResult
        WxResult wxResult = wxResultService.get("outTradeNo", tradeNo);
        if (wxResult == null || !"SUCCESS".equals(wxResult.getResultCode())) {

            //支付结果表添加或修改数据
            if (wxResult == null) wxResult = new WxResult();
            wxResult.setReturnCode(result.get("return_code"));

            wxResult.setOutTradeNo(tradeNo);    //商户订单号

            if ("SUCCESS".equals(result.get("return_code"))) { //以下字段在return_code为SUCCESS的时候有返回
                wxResult.setMchAppid(result.get("mch_appid"));
                wxResult.setMchid(result.get("mchid"));
                wxResult.setDeviceInfo(result.get("device_info"));
                wxResult.setNonceStr(result.get("nonce_str"));

                wxResult.setResultCode(result.get("result_code"));
                wxResult.setErrCode(result.get("err_code"));
                wxResult.setErrCodeDes(result.get("err_code_des"));

                if ("SUCCESS".equals(result.get("result_code"))) { //企业付款成功扣除customerAccount 和 driverAccount 账户余额
                    logger.info(result.get("partner_trade_no") + "partner_trade_no");
                    wxResult.setPaymentNo(result.get("payment_no"));//微信付款订单号
                    wxResult.setPaymentTime(result.get("payment_time"));//付款成功时间

                    pay = 1;
                }
            } else {
                wxResult.setReturnMsg(result.get("return_msg"));
            }
            wxResult.setUpdateTime(new Date().getTime());
            wxResultService.save(wxResult);
        }

        //TODO 2.修改三方账户表转账结果标记
        Query<ThirdPartyAccount> query = this.createQuery();
        query.criteria("outTradeNo").equal(tradeNo);
        UpdateOperations<ThirdPartyAccount> updateOP = this.createUpdateOperations();
        updateOP.set("pay", pay);
        this.update(query, updateOP);
    }

    @Override
    public ServerResponse<String> editThirdParty(UnitData unit) {
        if (StrUtil.isNotEmpty(unit.getThirdPartyLedger()) && unit.getThirdPartyLedger() > 0 && StrUtil.isEmpty(unit.getThirdPartyAccount()) && StrUtil.isEmpty(unit.getThirdPartyAccount2()))
            return ServerResponse.createError("三方账户，商户或个人类型，必填其一");

        Query<SMS> smsQuery = smsService.createQuery();
        smsQuery.criteria("mobile").equal(unit.getThirdPartyPhone());

        String codeStr = unit.getThirdPartyName() + unit.getSmsCode() + unit.getThirdPartyLedger();
        if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) codeStr = codeStr + unit.getThirdPartyAccount();
        if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2())) codeStr = codeStr + unit.getThirdPartyAccount2();
        codeStr = codeStr + unit.getThirdPartyPhone();
        smsQuery.criteria("code").equal(codeStr);

        SMS sms = smsService.get(smsQuery);
        if (sms == null) return ServerResponse.createError("验证码错误！");

        Query<SysUnit> query = sysUnitService.createQuery();
        query.filter("id", unit.getId());
        query.filter("updateTime", unit.getUpdateTime());
        SysUnit sysUnit = sysUnitService.get(query);

        //第三方账户信息修改
        UpdateOperations<SysUnit> updateOperations = sysUnitService.createUpdateOperations();
        if (StrUtil.isNotEmpty(unit.getThirdPartyLedger()))
            updateOperations.set("thirdPartyLedger", unit.getThirdPartyLedger() * 100);
        if (StrUtil.isNotEmpty(unit.getThirdPartyAccount()))
            updateOperations.set("thirdPartyAccount", unit.getThirdPartyAccount().equals("置空") ? "" : unit.getThirdPartyAccount());
        if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2()))
            updateOperations.set("thirdPartyAccount2", unit.getThirdPartyAccount2());
        if ((StrUtil.isEmpty(unit.getThirdPartyAccount()) || unit.getThirdPartyAccount().equals("置空")) && StrUtil.isEmpty(unit.getThirdPartyAccount2())) {
            updateOperations.set("thirdPartyLedger", 0);
            updateOperations.set("thirdPartyName", "");
            updateOperations.set("thirdPartyPhone", "");
        } else {
            updateOperations.set("thirdPartyName", unit.getThirdPartyName());
            updateOperations.set("thirdPartyPhone", unit.getThirdPartyPhone());
        }

        sysUnitService.update(query, updateOperations);

        //不启用分账
        /*if ((StrUtil.isEmpty(sysUnit.getThirdPartyAccount2()) && StrUtil.isNotEmpty(unit.getThirdPartyAccount2()))
                || (StrUtil.isNotEmpty(unit.getThirdPartyAccount2()) && !sysUnit.getThirdPartyAccount2().equals(unit.getThirdPartyAccount2()))) {
            addProfitSharing("MERCHANT_ID", unit.getThirdPartyAccount2(), unit.getThirdPartyName());
        } else if ((StrUtil.isEmpty(sysUnit.getThirdPartyAccount()) && StrUtil.isNotEmpty(unit.getThirdPartyAccount()))
                || (StrUtil.isNotEmpty(unit.getThirdPartyAccount()) && !sysUnit.getThirdPartyAccount().equals(unit.getThirdPartyAccount()))) {
            addProfitSharing("PERSONAL_OPENID", unit.getThirdPartyAccount(), unit.getThirdPartyName());
        }*/

        return ServerResponse.createSuccess();
    }

    /**
     * public static final String RECEIVERIS_URL_SUFFIX = "/v3/profitsharing/receivers/add";//添加分账接收方
     * public static final String PROFITSHARING_URL_SUFFIX = "/v3/profitsharing/orders";//请求分账
     */
    //请求微信服务器，添加分账接收方
    @Async("busTaskExecutor")
    public void addProfitSharing(String type, String account, String name) {
        String serialNo = null;

        JSONObject param = new JSONObject();
        param.put("appid", wxPayWechatCusConfig.getAppID());                //微信公众号的appid
        param.put("type", type);                                            //分账接收方类型。MERCHANT_ID：商户ID ；PERSONAL_OPENID：个人openid（由父商户APPID转换得到）
        param.put("account", account);                                      //分账接收方账号。类型是MERCHANT_ID时，是商户号；类型是PERSONAL_OPENID时，是个人openid
        if (StrUtil.isNotEmpty(name)) {
            Map<String, String> certMap = wxPayV3.getCertificates();
            serialNo = certMap.get("serialNo");
            String rsaName = wxPayV3.rsaEncryptOAEP(name, certMap);
            param.put("name", rsaName);                                     /*分账个人接收方姓名。分账接收方类型是MERCHANT_ID时，是商户全称（必传），当商户是小微商户或个体户时，是开户人姓名 分账接收方类型是PERSONAL_OPENID时，是个人姓名（选传，传则校验）
                                                                                    1、此字段需要加密，加密方法详见：敏感信息加密说明
                                                                                    2、使用微信支付平台证书中的公钥：获取平台证书
                                                                                    3、使用RSAES-OAEP算法进行加密
                                                                                    4、将请求中HTTP头部的Wechatpay-Serial设置为证书序列号*/
        }
        param.put("relation_type", "PARTNER");                              //与分账方的关系类型。 PARTNER：合作伙伴；DISTRIBUTOR：分销商；USER：用户；SUPPLIER： 供应商；CUSTOM：自定义
        String strParam = param.toJSONString();

        String url = wxPayConstants.RECEIVERIS_URL_SUFFIX;

        String responseEntity = wxPayV3.execute("POST", url, strParam, serialNo);

        JSONObject jsonObject = JSON.parseObject(responseEntity);
        if (StrUtil.isNotEmpty(jsonObject.getString("message"))) {
            logger.error(responseEntity);
            logger.error("===============================================.addProfitSharing========================================");
        }
    }


    @Override
    public ServerResponse<String> smsCode(UnitData unit) {
        if (StrUtil.isNotEmpty(unit.getThirdPartyLedger()) && unit.getThirdPartyLedger() > 0 && StrUtil.isEmpty(unit.getThirdPartyAccount()) && StrUtil.isEmpty(unit.getThirdPartyAccount2()))
            return ServerResponse.createError("三方账户，商户或个人类型，必填其一");

        if (StrUtil.isNotEmpty(unit.getThirdPartyLedger()) && unit.getThirdPartyLedger() > 0 && StrUtil.isEmpty(unit.getThirdPartyName()))
            return ServerResponse.createError("姓名不可为空");
        if (!StrUtil.isPhone(unit.getThirdPartyPhone())) return ServerResponse.createError("WX手机号错误");

        String code = String.valueOf(Math.random()).substring(2, 8);//6位数随机码

        String codeStr = unit.getThirdPartyName() + code + unit.getThirdPartyLedger();

        if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) codeStr = codeStr + unit.getThirdPartyAccount();
        if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2())) codeStr = codeStr + unit.getThirdPartyAccount2();
        codeStr = codeStr + unit.getThirdPartyPhone();

        SMS sms = new SMS();
        sms.setMobile(unit.getThirdPartyPhone());//("***********");
        sms.setCode(codeStr);
        sms.setSendStatus(1);
        sms.setSendTime(new Date());
        try {
            smsService.save(sms);
            SMSService.sendNoticeSMS(unit.getName(), unit.getThirdPartyName(), unit.getThirdPartyLedger(), code);
            /*List<String> templateParasList = new ArrayList<>();
            templateParasList.add(unit.getName());
            templateParasList.add(unit.getThirdPartyName());
            templateParasList.add(String.valueOf(unit.getThirdPartyLedger()));
            templateParasList.add(code);
            HuaWeiSMSService.sendSmsThirdParty("***********", JSONArray.parseArray(JSON.toJSONString(templateParasList)).toJSONString());*/
        } catch (Exception e) {
            return ServerResponse.createError("发送过于频繁！请稍等");
        }

        return ServerResponse.createSuccess("验证码发送成功");
    }

    @Override
    public ServerResponse<ThirdPartyAccount> getAccount() {
        String cid = ShiroUtils.getUserId();
        CustomerUser user = customerUserService.getByPK(cid);
        String openid = user.getPhoneId();

        ThirdPartyAccount result = new ThirdPartyAccount();
        result.setId(openid);
        //将账户中的金额 计算单位由 分 转化为 元
        result.setTotalFee(getAvailableFee(openid).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));

        return ServerResponse.createSuccess("查询成功", result);
    }

    private BigDecimal getAvailableFee(String openid) {
        Query<ThirdPartyAccount> query = this.createQuery();
        query.filter("id", openid);
        query.criteria("pay").notEqual(1);  //1是转账成功
        query.order(Sort.ascending("createTime"));
        List<ThirdPartyAccount> accountList = this.list(query);
        BigDecimal availableFee = new BigDecimal("0");
        for (ThirdPartyAccount account : accountList) {
            availableFee = availableFee.add(account.getTotalFee());//单位为分
        }
        return availableFee;
    }

    @Override
    @Transactional
    public ServerResponse<String> RefundPre(double amount, String name) {
        if (amount > 5000) return ServerResponse.createError("单次提现金额不能超过5000元");
        String cid = ShiroUtils.getUserId();
        CustomerUser user = customerUserService.getByPK(cid);
        String openid = user.getPhoneId();

        Query<ThirdPartyAccount> query = this.createQuery();
        query.criteria("id").equal(openid);
        query.criteria("type").equal(2);
        query.criteria("createTime").greaterThanOrEq(IdUnit.weeHours1(null, "00:00:00", 0));
        ThirdPartyAccount account = this.get(query);
        if (account != null) return ServerResponse.createError("单日提现金额不能超过5000元");

        RefundPre refundPre = new RefundPre();

        BigDecimal bigDecimal = this.getAvailableFee(openid);
        refundPre.setAccountNumber(openid);
        refundPre.setRemark("三方账户余额提现到微信零钱");
        refundPre.setName(name);

        BigDecimal fee = new BigDecimal(String.valueOf(amount)).multiply(new BigDecimal(100));
        if (fee.compareTo(bigDecimal) > 0)
            return ServerResponse.createError("提现金额应小于等于账户余额");

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> accountCollection = this.getCollection();
        MongoCollection<Document> refundCollection = refundPreService.getCollection();

        Date date = new Date();
        long timeStart = date.getTime();
        String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
        try {
            clientSession.startTransaction();
            //TODO 1：扣除余额
            ThirdPartyAccount dAccount = new ThirdPartyAccount();
            dAccount.setId(openid);
            dAccount.setTotalFee(fee.negate());
            dAccount.setType(2);
            dAccount.setOutTradeNo(out_trade_no);
            dAccount.setUpdateTime(date.getTime());
            Document driDocument = Document.parse(JSONObject.toJSONString(dAccount));
            driDocument.append("createTime", date);
            accountCollection.insertOne(clientSession, driDocument);

            refundPre.setUserId(openid);
            refundPre.setAmount(fee);               //提现金额
            refundPre.setTransferNo(out_trade_no);  //订单号
            refundPre.setRefundType(1);             //微信
            refundPre.setCheck(0);
            refundPre.setUpdateTime(date.getTime());
            Document refundDocument = Document.parse(JSONObject.toJSONString(refundPre));
            refundDocument.append("createTime", date);
            refundDocument.append("customerUserID", new ObjectId(cid));
            //TODO 2：保存提现申请
            refundCollection.insertOne(clientSession, refundDocument);
            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("提现申请提交失败");
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("提现申请已提交");
    }

    @Override
    public ServerResponse<List<RefundPreData>> queryRefundThirdPart() {
        String cid = ShiroUtils.getUserId();
        CustomerUser user = customerUserService.getByPK(cid);
        String openid = user.getPhoneId();
        Query<RefundPre> query = refundPreService.createQuery();
        query.filter("userId", openid);
        query.order(Sort.descending("createTime"));
        List<RefundPre> refundPreList = refundPreService.list(query);

        List<RefundPreData> list = new ArrayList<>();
        for (RefundPre refundPre : refundPreList) {
            RefundPreData refundPreData = new RefundPreData();
            BeanUtils.copyProperties(refundPre, refundPreData);
            refundPreData.setUserType(1);
            refundPreData.setMobile(refundPre.getCustomerUser().getMobile());

            refundPreData.setAmount(refundPre.getAmount().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue());
            list.add(refundPreData);
        }
        return ServerResponse.createSuccess("查询成功", list);
    }

    @Override
    public ServerResponse<EGridResult> thirdPartAccountList(Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        CustomerUser user = customerUserService.getByPK(cid);
        String openid = user.getPhoneId();

        Query<ThirdPartyAccount> query = this.createQuery();
        query.filter("id", openid);
        query.order(Sort.descending("createTime"));
        EGridResult<ThirdPartyAccount> eGridResult = this.findPage(page, rows, query);

        List<ThirdPartyAccount> accountList = eGridResult.getRows();
        for (ThirdPartyAccount account : accountList) {
            //将账户中的金额 计算单位由 分 转化为 元
            account.setTotalFee(account.getTotalFee().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        }
        return ServerResponse.createSuccess("查询成功", new EGridResult(eGridResult.getTotal(), accountList));
    }
}
