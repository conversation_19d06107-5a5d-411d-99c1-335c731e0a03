package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.park.api.business.entity.TradeContractWhiteList;
import com.erdos.coal.park.api.business.entity.TradePrice;

import java.util.List;
import java.util.Map;

public interface ITradeContractWhiteListService extends IBaseMongoService<TradeContractWhiteList> {
    // 检查推送的交易价格信息所属交易合同是否包含在白名单，以及是否包含煤种 矸石 的交易价格信息变更，有则发送短信
    void sendTradeSms(List<TradePrice> addList, Map<Integer, TradePrice> editMap);
}
