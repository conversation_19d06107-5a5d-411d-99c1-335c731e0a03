package com.erdos.coal.park.api.manage.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.manage.entity.OwnerAppeal;
import com.erdos.coal.park.api.manage.service.IOwnerAppealService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

//"车主申诉"
@RestController
@RequestMapping("/api/manage/own")
public class OwnerAppealController extends BaseController {
    @Resource
    private IOwnerAppealService ownerAppealService;

    @InvokeLog(description = "车主申诉 接口") //日志
    @PostMapping(value = "/appeal")
    public ServerResponse<OwnerAppeal> updateAppHandler(
            @RequestParam(value = "cardBef") MultipartFile cardBef,    //"车主身份证照片正面"
            @RequestParam(value = "cardBack") MultipartFile cardBack,  //"车主身份证照片反面"
            @RequestParam(value = "ownCard") MultipartFile ownerCard,  //"车主和身份证的合照"
            @RequestParam(value = "license") MultipartFile license,    //"行驶证照片"
            @RequestParam(value = "mobile") String mobile,             //"车主手机号"
            @RequestParam(value = "identity") String identity,         //"身份证号"
            @RequestParam(value = "carNum") String carNum,             //"车牌号"
            @RequestParam(value = "reason") String reason,             //"申诉理由"
            @RequestParam(value = "appOrWx") Integer appOrWx           //"App：0  微信小程序：1"
    ) throws GlobalException {
        return ownerAppealService.saveAppeal(cardBef, cardBack, ownerCard, license, mobile, identity, carNum, reason, appOrWx);
    }

}
