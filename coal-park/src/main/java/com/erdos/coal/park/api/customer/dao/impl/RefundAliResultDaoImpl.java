package com.erdos.coal.park.api.customer.dao.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.park.api.customer.dao.IRefundAliResultDao;
import com.erdos.coal.park.api.customer.entity.RefundAliResult;
import org.springframework.stereotype.Repository;

@Repository("refundAliResultDao")
public class RefundAliResultDaoImpl extends BaseMongoDAOImpl<RefundAliResult> implements IRefundAliResultDao {
}
