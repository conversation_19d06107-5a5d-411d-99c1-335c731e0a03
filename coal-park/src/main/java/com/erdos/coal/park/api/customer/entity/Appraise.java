package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_appraise", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("oid")}, options = @IndexOptions(unique = true))
})
public class Appraise extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "_oid", unique = true, background = true))
    private String oid;//订单编号
    private String startPoint;//起点
    private String endPoint;//终点
    private Long loadTime;//装货日期
    private Long unloadTime;//卸货日期
    private String driverName;//司机姓名
    private String carNum;//车牌号
    private String variety;//运输货物品种
    private String gid;//货运编号，关联商品信息的
    private Integer type;//0-未评价，1-已评价，2-已投诉；
    private Integer starsNum;//服务评价--几颗星
    private boolean complain;//是否投诉 false-未投诉，true-投诉
    private String reasons;//投诉理由
    private String result;//投诉处理结果

    private String cid;//客商id
    private String driverPro;//司机头像

    @Reference(value = "customerUserID", idOnly = true, lazy = true, ignoreMissing = true)
    private CustomerUser customerUser;

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public Long getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(Long loadTime) {
        this.loadTime = loadTime;
    }

    public Long getUnloadTime() {
        return unloadTime;
    }

    public void setUnloadTime(Long unloadTime) {
        this.unloadTime = unloadTime;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStarsNum() {
        return starsNum;
    }

    public void setStarsNum(Integer starsNum) {
        this.starsNum = starsNum;
    }

    public boolean isComplain() {
        return complain;
    }

    public boolean getComplain() {
        return complain;
    }

    public void setComplain(boolean complain) {
        this.complain = complain;
    }

    public String getReasons() {
        return reasons;
    }

    public void setReasons(String reasons) {
        this.reasons = reasons;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getDriverPro() {
        return driverPro;
    }

    public void setDriverPro(String driverPro) {
        this.driverPro = driverPro;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }
}
