package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.pojo.DriverInfoData;

import java.util.List;

public interface IDriverPoolService extends IBaseMongoService<DriverPool> {

    //查询客商司机池中司机 接口
    //ServerResponse<List<DriverInfoData>> driverPoolListData(String carNum, Integer whiteOrBlack);
    ServerResponse<EGridResult> driverPoolListData(String carNum, Integer whiteOrBlack, Integer page, Integer rows);

    //司机池中移除司机 接口
    ServerResponse<String> driverPoolDelDvr(String mobile, String id);

    //司机池中添加黑白名单
    ServerResponse<String> whiteOrBlackAdd(String[] ids, Integer whiteOrBlack);
}
