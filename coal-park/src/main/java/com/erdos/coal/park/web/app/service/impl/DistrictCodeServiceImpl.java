package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.app.dao.IDistrictCodeDao;
import com.erdos.coal.park.web.app.entity.DistrictCode;
import com.erdos.coal.park.web.app.service.IDistrictCodeService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service("districtCodeService")
public class DistrictCodeServiceImpl extends BaseMongoServiceImpl<DistrictCode, IDistrictCodeDao> implements IDistrictCodeService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private IDistrictCodeDao districtCodeDao;

    @Override
    public ServerResponse<EGridResult> districtCodeList(Integer page, Integer rows) {
        String name = request.getParameter("name");
        String code = request.getParameter("code");

        Query<DistrictCode> query = districtCodeDao.createQuery();
        if (StrUtil.isNotEmpty(name)) query.criteria("name").contains(name);
        if (StrUtil.isNotEmpty(code)) query.criteria("code").equal(code);

        EGridResult<DistrictCode> result = districtCodeDao.findPage(page, rows, query);

        return ServerResponse.createSuccess(result);
    }

    @Override
    public ServerResponse<String> upDistrictCode(MultipartFile file) {
        Query<DistrictCode> query = districtCodeDao.createQuery();
        List<DistrictCode> check = districtCodeDao.list(query);
        if (check.size() > 0) return ServerResponse.createError("文本已上传并保存数据库，请勿重复上传");

        List<DistrictCode> list = new ArrayList<>();

        try {
            InputStream inputStream = file.getInputStream();

            //创建Excel工作簿
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            Row row;
            //遍历sheet中的所有行
            for (int i = 1; i < sheet.getLastRowNum(); i++) {   //i=1跳过标题
                row = sheet.getRow(i);

                //遍历所有的列
                list.add(new DistrictCode(Utils.getUUID(), row.getCell(0).toString(), row.getCell(1).toString(), row.getCell(2).toString(), row.getCell(3).toString()));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        districtCodeDao.save(list);
        return ServerResponse.createSuccess("上传成功");
    }

    @Override
    public ServerResponse<String> addDistrictCode(DistrictCode districtCode) {
        if (StrUtil.isEmpty(districtCode.getName())) return ServerResponse.createError("名称不能为空");
        if (StrUtil.isEmpty(districtCode.getCode())) return ServerResponse.createError("编码不能为空");
        DistrictCode oldDistrictCode = districtCodeDao.get("code", districtCode.getCode());
        if (oldDistrictCode != null) return ServerResponse.createError("编码" + districtCode.getCode() + "已存在");

        DistrictCode newDistrictCode = new DistrictCode();
        BeanUtils.copyProperties(districtCode, newDistrictCode);
        newDistrictCode.setId(Utils.getUUID());
        districtCodeDao.save(newDistrictCode);
        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> editDistrictCode(DistrictCode districtCode) {
        if (StrUtil.isEmpty(districtCode.getName())) return ServerResponse.createError("名称不能为空");
        if (StrUtil.isEmpty(districtCode.getCode())) return ServerResponse.createError("编码不能为空");

        DistrictCode code = districtCodeDao.get("code", districtCode.getCode());
        if (code != null && !code.getId().equals(districtCode.getId()))
            return ServerResponse.createError("编码重复，修改失败");

        DistrictCode oldDistrictCode = districtCodeDao.get("id", districtCode.getId());

        Query<DistrictCode> query = districtCodeDao.createQuery();
        query.criteria("id").equal(districtCode.getId());

        UpdateOperations<DistrictCode> updateOperations = districtCodeDao.createUpdateOperations();
        if (StrUtil.isNotEmpty(districtCode.getCode()) && !districtCode.getCode().equals(oldDistrictCode.getCode()))
            updateOperations.set("code", districtCode.getCode());
        if (StrUtil.isNotEmpty(districtCode.getName()) && !districtCode.getName().equals(oldDistrictCode.getName()))
            updateOperations.set("name", districtCode.getName());
        if (StrUtil.isNotEmpty(districtCode.getLongitude()) && !districtCode.getLongitude().equals(oldDistrictCode.getLongitude()))
            updateOperations.set("longitude", districtCode.getLongitude());
        if (StrUtil.isNotEmpty(districtCode.getLatitude()) && !districtCode.getLatitude().equals(oldDistrictCode.getLatitude()))
            updateOperations.set("latitude", districtCode.getLatitude());

        districtCodeDao.update(query, updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }

}
