package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_unit_qua_device", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")})
})
public class SubUnitQuarantineDevice extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "id", background = true))
    private String id;
    private String deviceNo;
    private String subCode;
    private String subName;
    private String localDes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getLocalDes() {
        return localDes;
    }

    public void setLocalDes(String localDes) {
        this.localDes = localDes;
    }
}
