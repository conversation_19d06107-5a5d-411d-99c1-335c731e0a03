package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

@Entity(value = "t_app_trade_unit", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("unitCode")}),
        @Index(fields = {@Field("subCode")}),
        @Index(fields = {@Field("bizUnitCode")})
})
public class TradeUnit extends BaseMongoInfo {
    private boolean allowLimit;          //额度限定
    private boolean autoChangeCon;       //自动变更合同
    private boolean autoChangeProduct;   //自动变更品种
    private boolean balanceCtrl;         //余额控制
    private Integer bizType;            //业务类型
    private String bizUnitABR;          //简称
    private String bizUnitCode;         //业务单位编号
    private String bizUnitName;         //业务单位名称
    private boolean carSummary;          //每车简报
    private boolean daySummary;          //每日简报
    private Double earlyWarn;           //预警额
    private Double limitAllow;          //限定额度
    private Double limitWeight;         //限定计量
    private boolean messageInteraction;  //短信互动
    private boolean orderContractFill;   //下单合同手动填充
    private boolean orderContractNull;   //下单合同为空
    private boolean orderProductFill;    //下单品种手动填充
    private boolean orderProductNull;    //下单品种为空
    private Double overdraft;           //透支额
    private Integer status;             //状态
    private String subCode;             //二级单位编码
    private boolean transparent;         //透明
    private Double triggerBalance;      //触发余额
    private boolean triggerSwitch;       //触发开关
    private String unitCode;            //单位编码
    private boolean weightLimit;         //计量限定

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizUnitCode() {
        return bizUnitCode;
    }

    public void setBizUnitCode(String bizUnitCode) {
        this.bizUnitCode = bizUnitCode;
    }

    public String getBizUnitName() {
        return bizUnitName;
    }

    public void setBizUnitName(String bizUnitName) {
        this.bizUnitName = bizUnitName;
    }

    public String getBizUnitABR() {
        return bizUnitABR;
    }

    public void setBizUnitABR(String bizUnitABR) {
        this.bizUnitABR = bizUnitABR;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Double getLimitAllow() {
        return limitAllow;
    }

    public void setLimitAllow(Double limitAllow) {
        this.limitAllow = limitAllow;
    }

    public Double getLimitWeight() {
        return limitWeight;
    }

    public void setLimitWeight(Double limitWeight) {
        this.limitWeight = limitWeight;
    }

    public Double getOverdraft() {
        return overdraft;
    }

    public void setOverdraft(Double overdraft) {
        this.overdraft = overdraft;
    }

    public Double getEarlyWarn() {
        return earlyWarn;
    }

    public void setEarlyWarn(Double earlyWarn) {
        this.earlyWarn = earlyWarn;
    }

    public Double getTriggerBalance() {
        return triggerBalance;
    }

    public void setTriggerBalance(Double triggerBalance) {
        this.triggerBalance = triggerBalance;
    }

    public boolean isOrderContractNull() {
        return orderContractNull;
    }

    public boolean getOrderContractNull() {
        return orderContractNull;
    }

    public void setOrderContractNull(boolean orderContractNull) {
        this.orderContractNull = orderContractNull;
    }

    public boolean isOrderProductNull() {
        return orderProductNull;
    }

    public boolean getOrderProductNull() {
        return orderProductNull;
    }

    public void setOrderProductNull(boolean orderProductNull) {
        this.orderProductNull = orderProductNull;
    }

    public boolean isOrderContractFill() {
        return orderContractFill;
    }

    public boolean getOrderContractFill() {
        return orderContractFill;
    }

    public void setOrderContractFill(boolean orderContractFill) {
        this.orderContractFill = orderContractFill;
    }

    public boolean isOrderProductFill() {
        return orderProductFill;
    }

    public boolean getOrderProductFill() {
        return orderProductFill;
    }

    public void setOrderProductFill(boolean orderProductFill) {
        this.orderProductFill = orderProductFill;
    }

    public boolean isAutoChangeCon() {
        return autoChangeCon;
    }

    public boolean getAutoChangeCon() {
        return autoChangeCon;
    }

    public void setAutoChangeCon(boolean autoChangeCon) {
        this.autoChangeCon = autoChangeCon;
    }

    public boolean isAutoChangeProduct() {
        return autoChangeProduct;
    }

    public boolean getAutoChangeProduct() {
        return autoChangeProduct;
    }

    public void setAutoChangeProduct(boolean autoChangeProduct) {
        this.autoChangeProduct = autoChangeProduct;
    }

    public boolean isTriggerSwitch() {
        return triggerSwitch;
    }

    public boolean getTriggerSwitch() {
        return triggerSwitch;
    }

    public void setTriggerSwitch(boolean triggerSwitch) {
        this.triggerSwitch = triggerSwitch;
    }

    public boolean isBalanceCtrl() {
        return balanceCtrl;
    }

    public boolean getBalanceCtrl() {
        return balanceCtrl;
    }

    public void setBalanceCtrl(boolean balanceCtrl) {
        this.balanceCtrl = balanceCtrl;
    }

    public boolean isAllowLimit() {
        return allowLimit;
    }

    public boolean getAllowLimit() {
        return allowLimit;
    }

    public void setAllowLimit(boolean allowLimit) {
        this.allowLimit = allowLimit;
    }

    public boolean isWeightLimit() {
        return weightLimit;
    }

    public boolean getWeightLimit() {
        return weightLimit;
    }

    public void setWeightLimit(boolean weightLimit) {
        this.weightLimit = weightLimit;
    }

    public boolean isTransparent() {
        return transparent;
    }

    public boolean getTransparent() {
        return transparent;
    }

    public void setTransparent(boolean transparent) {
        this.transparent = transparent;
    }

    public boolean isMessageInteraction() {
        return messageInteraction;
    }

    public boolean getMessageInteraction() {
        return messageInteraction;
    }

    public void setMessageInteraction(boolean messageInteraction) {
        this.messageInteraction = messageInteraction;
    }

    public boolean isCarSummary() {
        return carSummary;
    }

    public boolean getCarSummary() {
        return carSummary;
    }

    public void setCarSummary(boolean carSummary) {
        this.carSummary = carSummary;
    }

    public boolean isDaySummary() {
        return daySummary;
    }

    public boolean getDaySummary() {
        return daySummary;
    }

    public void setDaySummary(boolean daySummary) {
        this.daySummary = daySummary;
    }
}
