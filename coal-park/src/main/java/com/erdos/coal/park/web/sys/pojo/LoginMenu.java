package com.erdos.coal.park.web.sys.pojo;

import java.util.List;

public class LoginMenu {

    private String id;
    private String pid;
    private String name; //Sys
    private String path; //'/sys'
    private MetaObj meta; //meta: { title: '系统管理', icon: 'user' }
    private String component; //Layout
    private String redirect; //'/message/table'

    private List<LoginMenu> children;

    public static class MetaObj {
        private String title;
        private String icon;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public MetaObj getMeta() {
        return meta;
    }

    public void setMeta(MetaObj meta) {
        this.meta = meta;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getRedirect() {
        return redirect;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public List<LoginMenu> getChildren() {
        return children;
    }

    public void setChildren(List<LoginMenu> children) {
        this.children = children;
    }
}
