package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.alibaba.sms.service.SMSService;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.business.entity.TradePrice;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.service.ICompanyOrderService;
import com.erdos.coal.park.api.business.service.IContractService;
import com.erdos.coal.park.api.business.service.ITradePriceService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.dao.IGoodsDao;
import com.erdos.coal.park.api.customer.entity.*;
import com.erdos.coal.park.api.customer.pojo.GoodsInUnitData;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.customer.pojo.GoodsPipeline;
import com.erdos.coal.park.api.customer.pojo.GoodsVerbInfoData;
import com.erdos.coal.park.api.customer.service.*;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.*;
import com.erdos.coal.park.api.driver.service.*;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.app.service.IDistrictCodeService;
import com.erdos.coal.park.web.sys.entity.SysSwitch;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.*;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.huawei.demo.VoiceNotify;
import com.mongodb.AggregationOptions;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.UpdateResult;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.aggregation.Projection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.mongodb.client.model.Filters.in;
import static dev.morphia.aggregation.Group.*;

@Service("goodsService")
public class GoodsServiceImpl extends BaseMongoServiceImpl<Goods, IGoodsDao> implements IGoodsService {
    @Resource
    @Lazy
    private IOrderService orderService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    ICompanyOrderService companyOrderService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IUnitInfoService unitInfoService;
    @Resource
    private IDriverGroupService driverGroupService;
    @Resource
    private IGidAndDidService gidAndDidService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private ILockedService lockedService;
    @Resource
    private IOrderRecordService orderRecordService;
    @Resource
    private IDriverLogisticsService driverLogisticsService;
    @Resource
    private IHttpAPIService httpAPIService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private ISysUnitAccountService sysUnitAccountService;
    @Resource
    private ISysSwitchService sysSwitchService;
    @Resource
    private IPlatFormAccountService platFormAccountService;
    @Resource
    private IContractService contractService;
    @Resource
    private ICarDao carDao;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private MongoClient client;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;
    @Resource
    private IWXsDvrGoodsService wXsDvrGoodsService;
    @Resource
    private IDistrictCodeService districtCodeService;
    @Resource
    private IVoiceNotifyRecordService voiceNotifyRecordService;
    @Resource
    private ITradePriceService tradePriceService;

    //货运信息 及其下属订单 联合删除行为 添加事务
    @Transactional
    boolean delGoodsAndOrder(String gid) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        try {
            clientSession.startTransaction();
            Document filter = new Document("gid", gid);
            goodsCollection.deleteOne(clientSession, filter);
            orderCollection.deleteMany(clientSession, filter);
            clientSession.commitTransaction();
            return true;
        } catch (Exception e) {
            clientSession.abortTransaction();
            return false;
        } finally {
            clientSession.close();
        }
    }

    //以客商编号 和 最小编号值是否重复，判断货运信息是否为重复添加，是则返回已有的gid
    private String checkGoods(String cid, Goods goods) {
        int model = goods.getMold();
        Query<Goods> query = this.createQuery();
        query.filter("cid", cid);
        query.filter("shareGid", null);    //不是来自友商的分享
        switch (model) {
            case 0:
                query.filter("outUnitCode", goods.getOutUnitCode());
                query.filter("outMin", StrUtil.isEmpty(goods.getOutMin()) ? "" : goods.getOutMin());
                break;
            case 1:
                query.filter("inUnitCode", goods.getInUnitCode());
                query.filter("inMin", StrUtil.isEmpty(goods.getInMin()) ? "" : goods.getInMin());
                break;
            case 2:
                query.filter("outUnitCode", goods.getOutUnitCode());
                query.filter("inUnitCode", goods.getInUnitCode());
                query.filter("outMin", StrUtil.isEmpty(goods.getOutMin()) ? "" : goods.getOutMin());
                query.filter("inMin", StrUtil.isEmpty(goods.getInMin()) ? "" : goods.getInMin());
                break;
        }
        Goods g = this.get(query);
        if (g == null) return null;     //无货运信息，则添加

        List<Order> orderList = orderService.list(orderService.createQuery().filter("gid", g.getGid()));
        if (orderList.size() <= 0) {        //有货运信息无订单，则删除货运信息，重新添加
            this.delete(g.getObjectId());
            return null;
        }

        /*boolean b = false;
        if (g.getTotal() == 1 && StrUtil.isEmpty(orderList.get(0).getCarNum())) {    //车数为一车 且 未指定车牌号，则删除货运信息和订单
            b = delGoodsAndOrder(g.getGid());
        }
        if (b) return null; // 删除成功，重新添加*/
        return g.getGid();
    }

    //参数合法性校验  校验参数不能为空
    private String checkGoodsParam(Goods goods) {
        int mold = goods.getMold();
        if (mold == 0 || mold == 2) {
            if (StrUtil.isEmpty(goods.getOutUnitCode()) || StrUtil.isEmpty(goods.getOutUnitName()))
                return "一级单位不能为空";
            if (StrUtil.isEmpty(goods.getOutBizContractCode()) || StrUtil.isEmpty(goods.getOutBizContractName()))
                return "业务合同不能为空";
            if (StrUtil.isEmpty(goods.getOutVariety())) return "产品名称不能为空";
            if (StrUtil.isEmpty(goods.getOutDefaultDownUnit()) || StrUtil.isEmpty(goods.getOutSubName()))
                return "二级单位不能为空";
            if (StrUtil.isEmpty(goods.getOutDefaultArea()) || StrUtil.isEmpty(goods.getOutAreaName()))
                return "默认场区不能为空";
            if (StrUtil.isEmpty(goods.getOutMin())) return "可用最小票号不能为空";
            if (StrUtil.isEmpty(goods.getOutMax())) return "可用最大票号不能为空";
        }
        if (mold == 1 || mold == 2) {
            if (StrUtil.isEmpty(goods.getInUnitCode()) || StrUtil.isEmpty(goods.getInUnitName())) return "一级单位不能为空";
            if (StrUtil.isEmpty(goods.getInBizContractCode()) || StrUtil.isEmpty(goods.getInBizContractName()))
                return "业务合同不能为空";
            if (StrUtil.isEmpty(goods.getInVariety())) return "产品名称不能为空";
            if (StrUtil.isEmpty(goods.getInDefaultDownUnit()) || StrUtil.isEmpty(goods.getInSubName()))
                return "二级单位不能为空";
            if (StrUtil.isEmpty(goods.getInDefaultArea()) || StrUtil.isEmpty(goods.getInAreaName()))
                return "默认场区不能为空";
            if (StrUtil.isEmpty(goods.getInMin())) return "可用最小票号不能为空";
            if (StrUtil.isEmpty(goods.getInMax())) return "可用最大票号不能为空";
        }
        return null;
    }

    //校验车数是否小于发货和收货企业之间可用票号的最小值
    private String checkTotal(Goods goods) {
        int total = goods.getTotal() == null ? 1 : goods.getTotal();
        Integer outMin = goods.getOutMin();
        Integer outMax = goods.getOutMax();
        int outCount = outMin != null ? outMax - outMin + 1 : -1;
        Integer inMin = goods.getInMin();
        Integer inMax = goods.getInMax();
        int inCount = inMin != null ? inMax - inMin + 1 : -1;
        if ((outCount > -1 && inCount <= -1 && outMax != 0 && total <= outCount)       //发货物流，要求车数小于等于可用票号数
                || (outCount <= -1 && inCount > -1 && inMax != 0 && total <= inCount) //收货物流，要求车数小于等于可用票号数
                || (outCount > -1 && inCount > -1 && outMax != 0 && inMax != 0 && total <= outCount && total <= inCount)//收发货物流，要求车数小于等于两个企业可用票号数
        ) {
            return null;
        } else {
            return "车数过多！";
        }
    }

    //校验order的billCode，避免同一unitCode下出现min重复导致的billCode重复
    private String checkBillCode(String outUnitCode, Integer outMin, String inUnitCode, Integer inMin) {
        String outBillCode = StrUtil.isNotEmpty(outUnitCode) ? outUnitCode + "6" + String.format("%09d", outMin) : null;
        String inBillCode = StrUtil.isNotEmpty(inUnitCode) ? inUnitCode + "5" + String.format("%09d", inMin) : null;
        Query<Order> query = orderService.createQuery();
        if (StrUtil.isNotEmpty(outBillCode) && StrUtil.isNotEmpty(inBillCode)) {
            query.or(
                    query.criteria("outBillCode").equal(outBillCode),
                    query.criteria("inBillCode").equal(inBillCode)
            );
        } else if (StrUtil.isNotEmpty(outBillCode) && StrUtil.isEmpty(inBillCode)) {
            query.filter("outBillCode", outBillCode);
        } else if (StrUtil.isEmpty(outBillCode) && StrUtil.isNotEmpty(inBillCode)) {
            query.filter("inBillCode", inBillCode);
        } else {
            return "票号错误";
        }
        List<Order> list = query.find().toList();
        if (list.size() > 0) {
            logger.warn("下单执行校验方法checkBillCode时，(outMin 和 inMin)来自billused网络派单minnum" +
                    "参数outUnitCode、outMin【" + outUnitCode + "、" + outMin + "】组成票号billCode" + outBillCode + "在order表中有重复了 或者" +
                    "参数inUnitCode、inMin【" + inUnitCode + "、" + inMin + "】组成票号billCode" + inBillCode + "在order表中有重复了");
            return "票号错误";
        }
        return null;
    }

    /**
     * 添加货运信息接口 即客商下单操作前，保存订单的货物信息
     * param: 扫码下单界面填写的所有信息项
     * return: 新生成的货运信息编号 gid
     */
    @Override
    public ServerResponse<Object> addGoods(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);
        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.Face_To_Face_Add_Goods.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {

            try {

                goods.setCid(cid);
                //2022年4月19号，还未启用收费功能，屏蔽客商自主收费
                goods.setFees3(0);
                //goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

                //todo:收发货物流模式下，收发货的商品必须保持一致
                /*if (goods.getMold() == 2 && !goods.getOutVariety().equals(goods.getInVariety())) {
                    return ServerResponse.createError("收发货商品不一致，不符合运输规则!");
                }*/
                //2022年1月19号，可能收发单位对物料名称定义不同

                //todo:校验参数不能为空
                String cMsg = checkGoodsParam(goods);
                if (!StrUtil.isEmpty(cMsg)) {
                    return ServerResponse.createError(cMsg);
                }

                //todo:排除反复添加同一条货运信息
                String oGid = checkGoods(cid, goods);
                if (!StrUtil.isEmpty(oGid)) {
                    return ServerResponse.createError("已存在货运信息");
                }

                //todo: 校验票号outMin 和 inMin
                String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
                if (StrUtil.isNotEmpty(billCodeStr)) {
                    return ServerResponse.createError(billCodeStr);
                }

                //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
                String totalStr = checkTotal(goods);
                if (!StrUtil.isEmpty(totalStr)) {
                    return ServerResponse.createError("车数大于企业可用票号数量");
                }

                //todo:新车牌，添加到客商司机池
                DriverInfo driverInfo = null;
                if (!StrUtil.isEmpty(designCarNum)) {
                    goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

                    //0.判断车辆是否通过审核
                    Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
                    dtcQuery.filter("carNum", designCarNum);
                    dtcQuery.filter("driverId", designDriverId);
                    DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
                    if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
                    if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
                    if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
                    Car car = carDao.getByPK(driverToCar.getCarId());
                    if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
                    if (car.getVerify() == 0 || car.getVerify() == 2)
                        return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
                    if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

                    //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
                    //driverInfo = driverInfoService.get("carNum", designCarNum);
                    driverInfo = driverInfoService.getByPK(designDriverId);
                    if (driverInfo == null) {
                        return ServerResponse.createError("指定的车牌号司机不存在");
                    }
                    if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                        return ServerResponse.createError("指定的车牌号司机未通过审核");
                    }

                    //2.判断司机是否存在司机池中，无则添加
                    //暂时屏蔽司机池添加司机
                    /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
                    dpQuery.filter("cid", cid);
                    dpQuery.filter("did", driverInfo.getObjectId().toString());
                    DriverPool dp = driverPoolService.get(dpQuery);
                    if (dp == null) {
                        dp = new DriverPool();
                        dp.setCid(cid);
                        dp.setDid(driverInfo.getObjectId().toString());
                        dp.setCustomerUser(cUser);
                        dp.setDriverInfo(driverInfo);
                        driverPoolService.save(dp);
                    } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                        return ServerResponse.createError("指定车牌号司机在黑名单中");
                    }*/

                    //3.判断司机是否有未完成订单
                    /*if (orderTakingService.searchDriverOrder(designCarNum, 2).size() > 0)
                     return ServerResponse.createError("车辆：" + designCarNum + "有未完成订单,不能接单");*/
                    if (orderTakingService.searchById(designDriverId, 2).size() > 0)
                        return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
                }

                //todo:查询将要添加的货运信息 企业要求代扣的情况
                Map<String, Object> payMap = sysUnitTotalFees(goods);
                if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg"))) {
                    return ServerResponse.createError((String) payMap.get("msg"));
                }
                //todo:客商选择代付的情况，判断客商账户余额
                BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee");
                BigDecimal fee = (BigDecimal) payMap.get("sysUnitFee"); //客商代付时，每一单需要代付的金额
                if (isPay != 0) {
                    goods.setFees3(0);  //客商代付时，客商需要收取的信息费设置为0 即 自己需要收取的钱不用从自己账户扣除
                    BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
                    if (cusAvailableFees.compareTo(payFee) < 0) {
                        return ServerResponse.result(7, "账户金额不足，请先充值");
                    }
                } else {
                    fee = new BigDecimal(0);    //客商不代付，则为0
                }

                int[] outFees = (int[]) payMap.get("outFees");
                int[] inFees = (int[]) payMap.get("inFees");
                SysUnit[] sysOutUnits = (SysUnit[]) payMap.get("sysOutUnits");
                SysUnit[] sysInUnits = (SysUnit[]) payMap.get("sysInUnits");
                goods.setTotal(goods.getTotal() == null || goods.getTotal() == 0 ? 1 : goods.getTotal());
                goods.setStatus(0);
                //todo:请求企业系统下单
                Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysOutUnits[1], sysInUnits[1], false);
                if (ObjectUtil.isNotEmpty(msg)) {
                    //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
                    String outSynMsg = msg.get("outSynMsg");
                    String inSynMsg = msg.get("inSynMsg");
                    if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                        goods.setOutMin(null);
                        addGoodsAndOrder(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    }
                    if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                        goods.setInMin(null);
                        addGoodsAndOrder(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    }
                    return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
                }

                Goods result;
                if (StrUtil.isNotEmpty(designCarNum)) {
                    //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
                    result = addGoodsAndOrder(designCarNum, goods, false, 1, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    if (result == null) {
                        return ServerResponse.createError("手工下单失败");
                    }
                    //todo:手工下单成功后要推送信息给司机
                    if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                        String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                        pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
                    }
                } else {
                    //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
                    result = addGoodsAndOrder(designCarNum, goods, false, 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    if (result == null) {
                        return ServerResponse.createError("添加失败");
                    }
                }

                return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));

            } finally {
                lockedService.unLock(cid, SysConstants.LockType.Face_To_Face_Add_Goods.getType());

            }
        }
    }

    //添加货运信息，添加订单，关联货运信息和订单
    @Transactional
    Goods addGoodsAndOrder(String designCarNum, Goods goods, boolean isDel, Integer isHand, Integer isPay, BigDecimal fee, int[] outFees, int[] inFees, SysUnit[] sysOutUnits, SysUnit[] sysInUnits, DriverInfo driverInfo, String longitude, String latitude) {
        Integer outMin = goods.getOutMin();
        Integer inMin = goods.getInMin();
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        //查询企业是否愿意代付 平台需要收取的信息费
        boolean outIsReplacePay = StrUtil.isNotEmpty(sysOutUnits[0]);  //发货企业是否愿意代付
        boolean inIsReplacePay = StrUtil.isNotEmpty(sysInUnits[0]);    //收货企业是否愿意代付
        int[] types = new int[]{0, 1};  //0-单位代付，1-单位代扣
        String[] outUids = new String[3];
        if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
            outUids = new String[]{null, sysOutUnits[1].getObjectId().toString(), sysOutUnits[2].getObjectId().toString()};
        String[] inUids = new String[3];
        if (StrUtil.isNotEmpty(goods.getInUnitCode()))
            inUids = new String[]{null, sysInUnits[1].getObjectId().toString(), sysInUnits[2].getObjectId().toString()};
        String[] outUserIds = new String[]{null, goods.getCid(), goods.getCid()};
        String[] inUserIds = new String[]{null, goods.getCid(), goods.getCid()};
        if (outIsReplacePay) {
            outUids[0] = sysOutUnits[0].getObjectId().toString();
            outUserIds[0] = sysOutUnits[0].getObjectId().toString();
        }
        if (inIsReplacePay) {
            inUids[0] = sysInUnits[0].getObjectId().toString();
            inUserIds[0] = sysInUnits[0].getObjectId().toString();
        }
        if (isPay == 0) {    //客商不代付
            outFees[1] = 0;
            outFees[2] = 0;
            inFees[1] = 0;
            inFees[2] = 0;
        }

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> customerAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> platFormAccountCollection = platFormAccountService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = goods.getTotal() == null ? 1 : goods.getTotal();
            List<Document> list = new ArrayList<>();
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            List<Document> cusAccountDocs = new ArrayList<>();
            List<Document> pfAccountDocs = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();
                //订单涉及各个账户金额明细
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, oid, outFees, time);
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, oid, inFees, time);
                if (isPay == 1 && fee.compareTo(new BigDecimal(0)) > 0)
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), fee.negate(), 3, oid, time));
                if (outIsReplacePay && outFees[0] != 0) {
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outUserIds[0], new BigDecimal(-outFees[0]), 5, oid, time));
                    outFees[0] = 0;
                }
                if (inIsReplacePay && inFees[0] != 0) {
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inUserIds[0], new BigDecimal(-inFees[0]), 5, oid, time));
                    inFees[0] = 0;
                }
                if (isPay == 1 && (outFees[0] + inFees[0]) != 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getCid(), new BigDecimal(-(outFees[0] + inFees[0])), 6, oid, time));

                Order o = new Order();
                o.setOid(oid);
                //5，采购-入库。6，销售-出库
                if (outMin != null) {
                    o.setOutMinMax(String.valueOf(outMin + i));
                    o.setOutBillCode(goods.getOutUnitCode() + "6" + String.format("%09d", (outMin + i)));
                    o.setOutChecking(0);
                }
                if (inMin != null) {
                    o.setInMinMax(String.valueOf(inMin + i));
                    o.setInBillCode(goods.getInUnitCode() + "5" + String.format("%09d", (inMin + i)));
                    o.setInChecking(0);
                }
                o.setDelete(isDel);
                o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setpType(goods.getpType());
                o.setSpell(goods.getSpell());
                addOrderFees(goods, o, goods.getCid(), sysOutUnits, sysInUnits, outIsReplacePay, inIsReplacePay, isPay);
                if (StrUtil.isNotEmpty(designCarNum) && !isDel) {//指定车牌号，生成接单信息, 注意：企业系统未下单成功的不应该生成接单信息
                    o.setCarNum(designCarNum);
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));
                }
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);
            if (cusAccountDocs.size() > 0) customerAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (pfAccountDocs.size() > 0) platFormAccountCollection.insertMany(clientSession, pfAccountDocs);

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<Object> addGoods2(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);
        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.Face_To_Face_Add_Goods.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {

            try {

                goods.setCid(cid);
                //2022年4月19号，还未启用收费功能，屏蔽客商自主收费
                goods.setFees3(0);
                //goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

                //todo:收发货物流模式下，收发货的商品必须保持一致
                /*if (goods.getMold() == 2 && !goods.getOutVariety().equals(goods.getInVariety())) {
                    return ServerResponse.createError("收发货商品不一致，不符合运输规则!");
                }*/
                //2022年1月19号，可能收发单位对物料名称定义不同

                //todo:校验参数不能为空
                String cMsg = checkGoodsParam(goods);
                if (!StrUtil.isEmpty(cMsg)) {
                    return ServerResponse.createError(cMsg);
                }

                //todo:排除反复添加同一条货运信息
                String oGid = checkGoods(cid, goods);
                if (!StrUtil.isEmpty(oGid)) {
                    return ServerResponse.createError("已存在货运信息");
                }

                //todo: 校验票号outMin 和 inMin
                String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
                if (StrUtil.isNotEmpty(billCodeStr)) {
                    return ServerResponse.createError(billCodeStr);
                }

                //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
                String totalStr = checkTotal(goods);
                if (!StrUtil.isEmpty(totalStr)) {
                    return ServerResponse.createError("车数大于企业可用票号数量");
                }

                //todo:新车牌，添加到客商司机池
                DriverInfo driverInfo = null;
                if (!StrUtil.isEmpty(designCarNum)) {
                    goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

                    //0.判断车辆是否通过审核
                    Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
                    dtcQuery.filter("carNum", designCarNum);
                    dtcQuery.filter("driverId", designDriverId);
                    DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
                    if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
                    if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
                    if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
                    Car car = carDao.getByPK(driverToCar.getCarId());
                    if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
                    if (car.getVerify() == 0 || car.getVerify() == 2)
                        return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
                    if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

                    //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
                    //driverInfo = driverInfoService.get("carNum", designCarNum);
                    driverInfo = driverInfoService.getByPK(designDriverId);
                    if (driverInfo == null) {
                        return ServerResponse.createError("指定的车牌号司机不存在");
                    }
                    if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                        return ServerResponse.createError("指定的车牌号司机未通过审核");
                    }

                    //2.判断司机是否存在司机池中，无则添加
                    //暂时屏蔽司机池添加司机
                    /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
                    dpQuery.filter("cid", cid);
                    dpQuery.filter("did", driverInfo.getObjectId().toString());
                    DriverPool dp = driverPoolService.get(dpQuery);
                    if (dp == null) {
                        dp = new DriverPool();
                        dp.setCid(cid);
                        dp.setDid(driverInfo.getObjectId().toString());
                        dp.setCustomerUser(cUser);
                        dp.setDriverInfo(driverInfo);
                        driverPoolService.save(dp);
                    } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                        return ServerResponse.createError("指定车牌号司机在黑名单中");
                    }*/

                    //3.判断司机是否有未完成订单
                    /*if (orderTakingService.searchDriverOrder(designCarNum, 2).size() > 0)
                     return ServerResponse.createError("车辆：" + designCarNum + "有未完成订单,不能接单");*/
                    /*if (orderTakingService.searchById(designDriverId, 2).size() > 0)
                        return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");*/
                    if (driverInfo.getHaveOrder() > 0)
                        return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
                }

                //todo:查询将要添加的货运信息 企业要求代扣的情况
                //下一步，平台配置企业带扣信息，不调用接口
                Map<String, Object> payMap = sysUnitTotalFees(goods);
                if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg"))) {
                    return ServerResponse.createError((String) payMap.get("msg"));
                }
                //todo:客商选择代付的情况，判断客商账户余额
                BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee");
                BigDecimal fee = (BigDecimal) payMap.get("sysUnitFee"); //客商代付时，每一单需要代付的金额
                if (isPay != 0) {
                    goods.setFees3(0);  //客商代付时，客商需要收取的信息费设置为0 即 自己需要收取的钱不用从自己账户扣除
                    BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
                    if (cusAvailableFees.compareTo(payFee) < 0) {
                        return ServerResponse.result(7, "账户金额不足，请先充值");
                    }
                } else {
                    fee = new BigDecimal(0);    //客商不代付，则为0
                }

                int[] outFees = (int[]) payMap.get("outFees");
                int[] inFees = (int[]) payMap.get("inFees");
                SysUnit[] sysOutUnits = (SysUnit[]) payMap.get("sysOutUnits");
                SysUnit[] sysInUnits = (SysUnit[]) payMap.get("sysInUnits");
                goods.setTotal(goods.getTotal() == null || goods.getTotal() == 0 ? 1 : goods.getTotal());
                goods.setStatus(0);
                //todo:请求企业系统下单
                Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysOutUnits[1], sysInUnits[1], true);
                if (ObjectUtil.isNotEmpty(msg)) {
                    //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
                    String outSynMsg = msg.get("outSynMsg");
                    String inSynMsg = msg.get("inSynMsg");
                    if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                        goods.setOutMin(null);
                        addGoodsAndOrder2(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    }
                    if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                        goods.setInMin(null);
                        addGoodsAndOrder2(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    }
                    return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
                }

                Goods result;
                if (StrUtil.isNotEmpty(designCarNum)) {
                    //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
                    result = addGoodsAndOrder2(designCarNum, goods, false, 1, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    if (result == null) {
                        return ServerResponse.createError("手工下单失败");
                    }
                    //todo:手工下单成功后要推送信息给司机
                    if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                        String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                        pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
                    }
                } else {
                    //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
                    result = addGoodsAndOrder2(designCarNum, goods, false, 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, driverInfo, longitude, latitude);
                    if (result == null) {
                        return ServerResponse.createError("添加失败");
                    }
                }

                if (result.getpType() == 2) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("gid", result.getGid());
                    map.put("mold", result.getMold());
                    map.put("beginPoint", result.getBeginPoint());
                    map.put("endPoint", result.getEndPoint());
                    map.put("total", result.getTotal());
                    map.put("price", result.getPrice());
                    map.put("outVariety", result.getOutVariety());
                    map.put("inVariety", result.getInVariety());
                    return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
                } else {
                    return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
                }

            } finally {
                lockedService.unLock(cid, SysConstants.LockType.Face_To_Face_Add_Goods.getType());

            }
        }
    }

    @Override
    public ServerResponse<Object> addGoods3(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        //根据客商本身设置的收费，添加货运信息时加入计费金额，2022年4月19号，还未启用收费功能，屏蔽客商自主收费
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

        //todo:校验参数不能为空
        String cMsg = checkGoodsParam(goods);
        if (!StrUtil.isEmpty(cMsg)) return ServerResponse.createError(cMsg);

        //todo:排除反复添加同一条货运信息
        String oGid = checkGoods(cid, goods);
        if (!StrUtil.isEmpty(oGid)) return ServerResponse.createError("已存在货运信息");

        //todo: 校验票号outMin 和 inMin
        String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
        if (StrUtil.isNotEmpty(billCodeStr)) return ServerResponse.createError(billCodeStr);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        String totalStr = checkTotal(goods);
        if (!StrUtil.isEmpty(totalStr)) return ServerResponse.createError("车数大于企业可用票号数量");

        //todo:新车牌，添加到客商司机池
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(designCarNum)) {
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", designCarNum);
            dtcQuery.filter("driverId", designDriverId);
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(designDriverId);
            if (driverInfo == null) {
                return ServerResponse.createError("指定的车牌号司机不存在");
            }
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                return ServerResponse.createError("指定的车牌号司机未通过审核");
            }

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
                    /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
                    dpQuery.filter("cid", cid);
                    dpQuery.filter("did", driverInfo.getObjectId().toString());
                    DriverPool dp = driverPoolService.get(dpQuery);
                    if (dp == null) {
                        dp = new DriverPool();
                        dp.setCid(cid);
                        dp.setDid(driverInfo.getObjectId().toString());
                        dp.setCustomerUser(cUser);
                        dp.setDriverInfo(driverInfo);
                        driverPoolService.save(dp);
                    } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                        return ServerResponse.createError("指定车牌号司机在黑名单中");
                    }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = sysUnitTotalFees2(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        SysUnit[] sysUnits = (SysUnit[]) payMap.get("sysUnits");
        goods.setStatus(0);
        //todo:请求企业系统下单
        Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysUnits[0], sysUnits[2], true);
        if (ObjectUtil.isNotEmpty(msg)) {
            //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
            String outSynMsg = msg.get("outSynMsg");
            String inSynMsg = msg.get("inSynMsg");
            if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                goods.setOutMin(null);
                addGoodsAndOrder3(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                goods.setInMin(null);
                addGoodsAndOrder3(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder3(designCarNum, goods, false, 1, driverInfo);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder3(designCarNum, goods, false, 0, driverInfo);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    //添加货运信息，添加订单，关联货运信息和订单
    @Transactional
    Goods addGoodsAndOrder2(String designCarNum, Goods goods, boolean isDel, Integer isHand, Integer isPay, BigDecimal fee, int[] outFees, int[] inFees, SysUnit[] sysOutUnits, SysUnit[] sysInUnits, DriverInfo driverInfo, String longitude, String latitude) {
        Integer outMin = goods.getOutMin();
        Integer inMin = goods.getInMin();
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        //查询企业是否愿意代付 平台需要收取的信息费
        boolean outIsReplacePay = StrUtil.isNotEmpty(sysOutUnits[0]);  //发货企业是否愿意代付
        boolean inIsReplacePay = StrUtil.isNotEmpty(sysInUnits[0]);    //收货企业是否愿意代付
        int[] types = new int[]{0, 1};  //0-单位代付，1-单位代扣
        String[] outUids = new String[3];
        if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
            outUids = new String[]{null, sysOutUnits[1].getObjectId().toString(), sysOutUnits[2].getObjectId().toString()};
        String[] inUids = new String[3];
        if (StrUtil.isNotEmpty(goods.getInUnitCode()))
            inUids = new String[]{null, sysInUnits[1].getObjectId().toString(), sysInUnits[2].getObjectId().toString()};
        String[] outUserIds = new String[]{null, goods.getCid(), goods.getCid()};
        String[] inUserIds = new String[]{null, goods.getCid(), goods.getCid()};
        if (outIsReplacePay) {
            outUids[0] = sysOutUnits[0].getObjectId().toString();
            outUserIds[0] = sysOutUnits[0].getObjectId().toString();
        }
        if (inIsReplacePay) {
            inUids[0] = sysInUnits[0].getObjectId().toString();
            inUserIds[0] = sysInUnits[0].getObjectId().toString();
        }
        if (isPay == 0) {    //客商不代付
            outFees[1] = 0;
            outFees[2] = 0;
            inFees[1] = 0;
            inFees[2] = 0;
        }

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> customerAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> platFormAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = goods.getTotal() == null ? 1 : goods.getTotal();
            List<Document> list = new ArrayList<>();
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            List<Document> cusAccountDocs = new ArrayList<>();
            List<Document> pfAccountDocs = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();
                //订单涉及各个账户金额明细
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, oid, outFees, time);
                sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, oid, inFees, time);
                if (isPay == 1 && fee.compareTo(new BigDecimal(0)) > 0)
                    cusAccountDocs.add(customerAccountService.createCusAccountDoc(goods.getCid(), fee.negate(), 3, oid, time));
                if (outIsReplacePay && outFees[0] != 0) {
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outUserIds[0], new BigDecimal(-outFees[0]), 5, oid, time));
                    outFees[0] = 0;
                }
                if (inIsReplacePay && inFees[0] != 0) {
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inUserIds[0], new BigDecimal(-inFees[0]), 5, oid, time));
                    inFees[0] = 0;
                }
                if (isPay == 1 && (outFees[0] + inFees[0]) != 0)
                    pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(goods.getCid(), new BigDecimal(-(outFees[0] + inFees[0])), 6, oid, time));

                Order o = new Order();
                o.setOid(oid);
                //5，采购-入库。6，销售-出库
                if (outMin != null) {
                    o.setOutMinMax(String.valueOf(outMin + i));
                    o.setOutBillCode(goods.getOutUnitCode() + "6" + String.format("%09d", (outMin + i)));
                    o.setOutChecking(0);
                }
                if (inMin != null) {
                    o.setInMinMax(String.valueOf(inMin + i));
                    o.setInBillCode(goods.getInUnitCode() + "5" + String.format("%09d", (inMin + i)));
                    o.setInChecking(0);
                }
                o.setDelete(isDel);
                o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setpType(goods.getpType());
                o.setSpell(goods.getSpell());
                o.setPlace(goods.getPlace());
                o.setCid(goods.getCid());
                //2022年4月17号 +订单状态 0-未接单
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(designCarNum)) {
                    o.setTranStatus(1);
                }
                if (isDel) o.setTranStatus(6);
                addOrderFees(goods, o, goods.getCid(), sysOutUnits, sysInUnits, outIsReplacePay, inIsReplacePay, isPay);
                if (StrUtil.isNotEmpty(designCarNum) && !isDel) {//指定车牌号，生成接单信息, 注意：企业系统未下单成功的不应该生成接单信息
                    o.setCarNum(designCarNum);
                    o.setDid(goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);
            if (cusAccountDocs.size() > 0) customerAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (pfAccountDocs.size() > 0) platFormAccountCollection.insertMany(clientSession, pfAccountDocs);

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder2" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Transactional
    Goods addGoodsAndOrder3(String designCarNum, Goods goods, boolean isDel, Integer isHand, DriverInfo driverInfo) {
        Integer outMin = goods.getOutMin();
        Integer inMin = goods.getInMin();
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = goods.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setOid(oid);
                //5，采购-入库。6，销售-出库
                if (outMin != null) {
                    o.setOutMinMax(String.valueOf(outMin + i));
                    o.setOutBillCode(goods.getOutUnitCode() + "6" + String.format("%09d", (outMin + i)));
                    o.setOutChecking(0);
                }
                if (inMin != null) {
                    o.setInMinMax(String.valueOf(inMin + i));
                    o.setInBillCode(goods.getInUnitCode() + "5" + String.format("%09d", (inMin + i)));
                    o.setInChecking(0);
                }
                o.setDelete(isDel);
                o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setpType(goods.getpType());
                o.setSpell(goods.getSpell());
                o.setPlace(goods.getPlace());
                o.setCid(goods.getCid());
                //2022年4月17号 +订单状态 0-未接单
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(designCarNum)) o.setTranStatus(1);
                if (isDel) o.setTranStatus(6);
                addOrderFees2(goods, o);
                if (StrUtil.isNotEmpty(designCarNum) && !isDel) {//指定车牌号，生成接单信息, 注意：企业系统未下单成功的不应该生成接单信息
                    o.setCarNum(designCarNum);
                    o.setDid(goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder3" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    //同步指派车辆后的完整的订单信息到企业系统
    private Map<String, String> synOrderTooCompany(String cid, Goods goods, String designCarNum, SysUnit outSysUnit, SysUnit inSysUnit, boolean place) {
        //TODO:1.查询发货企业服务器地址
        String outUnitCode = goods.getOutUnitCode();
        Map<String, String> msgMap = new HashMap<>();
        CompletableFuture outSynMsg = new CompletableFuture<>();
        CompletableFuture inSynMsg = new CompletableFuture<>();
        if (!StrUtil.isEmpty(outUnitCode)) {
            String outUrl = outSysUnit.getIp();
            Query<UnitInfo> query = unitInfoService.createQuery();
            query.filter("cid", cid);
            query.filter("unitCode", outUnitCode);
            String outUserCode = unitInfoService.get(query).getUserCode();
            //TODO:2.构造 post请求 的map参数
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("userCode", outUserCode);
            requestMap.put("bizContractCode", goods.getOutBizContractCode());
            requestMap.put("variety", goods.getOutVariety());
            requestMap.put("defaultDownUnit", goods.getOutDefaultDownUnit());
            requestMap.put("defaultArea", goods.getOutDefaultArea());
            requestMap.put("carCount", goods.getTotal() == null ? 1 : goods.getTotal());    //车数
            requestMap.put("bizType", 1);               //销售1，采购0
            requestMap.put("payValue", "0");        //结算量
            requestMap.put("limitValue", "0");      //限定量
            requestMap.put("carNum", designCarNum);
            if (place && StrUtil.isNotEmpty(goods.getPlace())) requestMap.put("place", goods.getPlace());
            outSynMsg = companyOrderService.synOrderMsg(outUrl, requestMap, 1);
        }
        //TODO:3.查询收货企业服务器地址
        String inUnitCode = goods.getInUnitCode();
        if (!StrUtil.isEmpty(inUnitCode)) {
            Query<UnitInfo> query = unitInfoService.createQuery();
            query.filter("cid", cid);
            query.filter("unitCode", inUnitCode);
            String inUserCode = unitInfoService.get(query).getUserCode();
            String inUrl = inSysUnit.getIp();
            //TODO:4.构造 post请求 的map参数
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("userCode", inUserCode);
            requestMap.put("bizContractCode", goods.getInBizContractCode());
            requestMap.put("variety", goods.getInVariety());
            requestMap.put("defaultDownUnit", goods.getInDefaultDownUnit());
            requestMap.put("defaultArea", goods.getInDefaultArea());
            requestMap.put("carCount", goods.getTotal());     //车数
            requestMap.put("bizType", 0);               //销售1，采购0
            requestMap.put("payValue", "0");        //结算量
            requestMap.put("limitValue", "0");      //限定量
            requestMap.put("carNum", designCarNum);
            if (place && StrUtil.isNotEmpty(goods.getPlace())) requestMap.put("place", goods.getPlace());
            inSynMsg = companyOrderService.synOrderMsg(inUrl, requestMap, 1);
        }
        if (!StrUtil.isEmpty(outUnitCode)) {
            try {
                String errMsg = (String) outSynMsg.get();    //get 方法会使当前线程阻塞,并且等待直到 future 完成,并且将返回 future 的值
                if (StrUtil.isNotEmpty(errMsg)) msgMap.put("outSynMsg", goods.getOutUnitName() + "," + errMsg);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }
        if (!StrUtil.isEmpty(inUnitCode)) {
            try {
                String errMsg = (String) inSynMsg.get();     //get 方法会使当前线程阻塞,并且等待直到 future 完成,并且将返回 future 的值
                if (StrUtil.isNotEmpty(errMsg)) msgMap.put("inSynMsg", goods.getInUnitName() + "," + errMsg);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }

        return msgMap;
    }

    @Override
    public ServerResponse<Object> addGoods4(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        //根据客商本身设置的收费，添加货运信息时加入计费金额，2022年4月19号，还未启用收费功能，屏蔽客商自主收费
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

        //todo:校验参数不能为空
        String cMsg = checkGoodsParam(goods);
        if (!StrUtil.isEmpty(cMsg)) return ServerResponse.createError(cMsg);

        //todo:排除反复添加同一条货运信息
        String oGid = checkGoods(cid, goods);
        if (!StrUtil.isEmpty(oGid)) return ServerResponse.createError("已存在货运信息");

        //todo: 校验票号outMin 和 inMin
        String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
        if (StrUtil.isNotEmpty(billCodeStr)) return ServerResponse.createError(billCodeStr);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        String totalStr = checkTotal(goods);
        if (!StrUtil.isEmpty(totalStr)) return ServerResponse.createError("车数大于企业可用票号数量");

        //todo:新车牌，添加到客商司机池
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(designCarNum)) {
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", designCarNum);
            dtcQuery.filter("driverId", designDriverId);
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(designDriverId);
            if (driverInfo == null) {
                return ServerResponse.createError("指定的车牌号司机不存在");
            }
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                return ServerResponse.createError("指定的车牌号司机未通过审核");
            }

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
                    /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
                    dpQuery.filter("cid", cid);
                    dpQuery.filter("did", driverInfo.getObjectId().toString());
                    DriverPool dp = driverPoolService.get(dpQuery);
                    if (dp == null) {
                        dp = new DriverPool();
                        dp.setCid(cid);
                        dp.setDid(driverInfo.getObjectId().toString());
                        dp.setCustomerUser(cUser);
                        dp.setDriverInfo(driverInfo);
                        driverPoolService.save(dp);
                    } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                        return ServerResponse.createError("指定车牌号司机在黑名单中");
                    }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = sysUnitTotalFees2(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        SysUnit[] sysUnits = (SysUnit[]) payMap.get("sysUnits");
        goods.setStatus(0);
        //todo:请求企业系统下单
        Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysUnits[0], sysUnits[2], true);
        if (ObjectUtil.isNotEmpty(msg)) {
            //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
            String outSynMsg = msg.get("outSynMsg");
            String inSynMsg = msg.get("inSynMsg");
            if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                goods.setOutMin(null);
                addGoodsAndOrder4(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                goods.setInMin(null);
                addGoodsAndOrder4(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder4(designCarNum, goods, false, 1, driverInfo);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder4(designCarNum, goods, false, 0, driverInfo);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Transactional
    Goods addGoodsAndOrder4(String designCarNum, Goods goods, boolean isDel, Integer isHand, DriverInfo driverInfo) {
        Integer outMin = goods.getOutMin();
        Integer inMin = goods.getInMin();
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = goods.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setOid(oid);
                //5，采购-入库。6，销售-出库
                if (outMin != null) {
                    o.setOutMinMax(String.valueOf(outMin + i));
                    o.setOutBillCode(goods.getOutUnitCode() + "6" + String.format("%09d", (outMin + i)));
                    o.setOutChecking(0);
                    o.setOutTradingUnitName(goods.getOutTradingUnitName());
                    o.setOutTradingUnit(goods.getOutTradingUnit());
                }
                if (inMin != null) {
                    o.setInMinMax(String.valueOf(inMin + i));
                    o.setInBillCode(goods.getInUnitCode() + "5" + String.format("%09d", (inMin + i)));
                    o.setInChecking(0);
                    o.setInTradingUnitName(goods.getInTradingUnitName());
                    o.setInTradingUnit(goods.getInTradingUnit());
                }
                o.setDelete(isDel);
                o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setpType(goods.getpType());
                o.setSpell(goods.getSpell());
                o.setPlace(goods.getPlace());
                o.setCid(goods.getCid());
                //2022年4月17号 +订单状态 0-未接单
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(designCarNum)) o.setTranStatus(1);
                if (isDel) o.setTranStatus(6);
                //2022年8月4号 +客商对订单的描述供司机运货时查看
                o.setOutCusDesc(goods.getOutCusDesc());
                o.setInCusDesc(goods.getInCusDesc());
                addOrderFees2(goods, o);
                if (StrUtil.isNotEmpty(designCarNum) && !isDel) {//指定车牌号，生成接单信息, 注意：企业系统未下单成功的不应该生成接单信息
                    o.setCarNum(designCarNum);
                    o.setDid(goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder4" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<Object> addGoods5(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        //根据客商本身设置的收费，添加货运信息时加入计费金额，2022年4月19号，还未启用收费功能，屏蔽客商自主收费
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

        //校验是否为往货业务
        if (StrUtil.isNotEmpty(goods.getMold2()) && goods.getMold2() == 1) {
            if (StrUtil.isEmpty(goods.getBeginDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getBeginPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
            if (StrUtil.isEmpty(goods.getEndDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getEndPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
        }

        //todo:校验参数不能为空
        String cMsg = checkGoodsParam(goods);
        if (!StrUtil.isEmpty(cMsg)) return ServerResponse.createError(cMsg);

        //todo:排除反复添加同一条货运信息
        String oGid = checkGoods(cid, goods);
        if (!StrUtil.isEmpty(oGid)) return ServerResponse.createError("已存在货运信息");

        //todo: 校验票号outMin 和 inMin
        String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
        if (StrUtil.isNotEmpty(billCodeStr)) return ServerResponse.createError(billCodeStr);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        String totalStr = checkTotal(goods);
        if (!StrUtil.isEmpty(totalStr)) return ServerResponse.createError("车数大于企业可用票号数量");

        //todo:新车牌，添加到客商司机池
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(designCarNum)) {
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", designCarNum);
            dtcQuery.filter("driverId", designDriverId);
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(designDriverId);
            if (driverInfo == null) {
                return ServerResponse.createError("指定的车牌号司机不存在");
            }
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                return ServerResponse.createError("指定的车牌号司机未通过审核");
            }

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
                    /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
                    dpQuery.filter("cid", cid);
                    dpQuery.filter("did", driverInfo.getObjectId().toString());
                    DriverPool dp = driverPoolService.get(dpQuery);
                    if (dp == null) {
                        dp = new DriverPool();
                        dp.setCid(cid);
                        dp.setDid(driverInfo.getObjectId().toString());
                        dp.setCustomerUser(cUser);
                        dp.setDriverInfo(driverInfo);
                        driverPoolService.save(dp);
                    } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                        return ServerResponse.createError("指定车牌号司机在黑名单中");
                    }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = sysUnitTotalFees2(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        SysUnit[] sysUnits = (SysUnit[]) payMap.get("sysUnits");
        goods.setStatus(0);
        //todo:请求企业系统下单
        Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysUnits[0], sysUnits[2], true);
        if (ObjectUtil.isNotEmpty(msg)) {
            //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
            String outSynMsg = msg.get("outSynMsg");
            String inSynMsg = msg.get("inSynMsg");
            if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                goods.setOutMin(null);
                addGoodsAndOrder5(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                goods.setInMin(null);
                addGoodsAndOrder5(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder5(designCarNum, goods, false, 1, driverInfo);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder5(designCarNum, goods, false, 0, driverInfo);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Override
    public ServerResponse<Object> addGoods6(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        //根据客商本身设置的收费，添加货运信息时加入计费金额，2022年4月19号，还未启用收费功能，屏蔽客商自主收费
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

        //校验是否为往货业务
        if (StrUtil.isNotEmpty(goods.getMold2()) && goods.getMold2() == 1) {
            if (StrUtil.isEmpty(goods.getBeginDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getBeginPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
            if (StrUtil.isEmpty(goods.getEndDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getEndPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
        }

        //todo:校验参数不能为空
        String cMsg = checkGoodsParam(goods);
        if (!StrUtil.isEmpty(cMsg)) return ServerResponse.createError(cMsg);

        //todo:排除反复添加同一条货运信息
        String oGid = checkGoods(cid, goods);
        if (!StrUtil.isEmpty(oGid)) return ServerResponse.createError("已存在货运信息");

        //todo: 校验票号outMin 和 inMin
        String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
        if (StrUtil.isNotEmpty(billCodeStr)) return ServerResponse.createError(billCodeStr);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        String totalStr = checkTotal(goods);
        if (!StrUtil.isEmpty(totalStr)) return ServerResponse.createError("车数大于企业可用票号数量");

        //todo:新车牌，添加到客商司机池
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(designCarNum)) {
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", designCarNum);
            dtcQuery.filter("driverId", designDriverId);
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(designDriverId);
            if (driverInfo == null) {
                return ServerResponse.createError("指定的车牌号司机不存在");
            }
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                return ServerResponse.createError("指定的车牌号司机未通过审核");
            }

            //2.判断司机是否存在司机池中，无则添加
            //暂时屏蔽司机池添加司机
                    /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
                    dpQuery.filter("cid", cid);
                    dpQuery.filter("did", driverInfo.getObjectId().toString());
                    DriverPool dp = driverPoolService.get(dpQuery);
                    if (dp == null) {
                        dp = new DriverPool();
                        dp.setCid(cid);
                        dp.setDid(driverInfo.getObjectId().toString());
                        dp.setCustomerUser(cUser);
                        dp.setDriverInfo(driverInfo);
                        driverPoolService.save(dp);
                    } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                        return ServerResponse.createError("指定车牌号司机在黑名单中");
                    }*/

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = sysUnitTotalFees3(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        SysUnit[] sysUnits = (SysUnit[]) payMap.get("sysUnits");
        goods.setStatus(0);
        //todo:请求企业系统下单
        Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysUnits[0], sysUnits[2], true);
        if (ObjectUtil.isNotEmpty(msg)) {
            //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
            String outSynMsg = msg.get("outSynMsg");
            String inSynMsg = msg.get("inSynMsg");
            if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                goods.setOutMin(null);
                goods.setOrderNum0(0);
                goods.setOrderNum1(0);
                goods.setDelNum(goods.getTotal());
                addGoodsAndOrder5(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                goods.setInMin(null);
                goods.setOrderNum0(0);
                goods.setOrderNum1(0);
                goods.setDelNum(goods.getTotal());
                addGoodsAndOrder5(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo);
            }
            return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder5(designCarNum, goods, false, 1, driverInfo);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder5(designCarNum, goods, false, 0, driverInfo);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Transactional
    Goods addGoodsAndOrder5(String designCarNum, Goods goods, boolean isDel, Integer isHand, DriverInfo driverInfo) {
        Integer outMin = goods.getOutMin();
        Integer inMin = goods.getInMin();
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = goods.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setOid(oid);
                //5，采购-入库。6，销售-出库
                if (outMin != null) {
                    o.setOutMinMax(String.valueOf(outMin + i));
                    o.setOutBillCode(goods.getOutUnitCode() + "6" + String.format("%09d", (outMin + i)));
                    o.setOutChecking(0);
                    o.setOutTradingUnitName(goods.getOutTradingUnitName());
                    o.setOutTradingUnit(goods.getOutTradingUnit());
                }
                if (inMin != null) {
                    o.setInMinMax(String.valueOf(inMin + i));
                    o.setInBillCode(goods.getInUnitCode() + "5" + String.format("%09d", (inMin + i)));
                    o.setInChecking(0);
                    o.setInTradingUnitName(goods.getInTradingUnitName());
                    o.setInTradingUnit(goods.getInTradingUnit());
                }
                o.setDelete(isDel);
                o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setpType(goods.getpType());
                o.setSpell(goods.getSpell());
                o.setPlace(goods.getPlace());
                o.setCid(goods.getCid());
                //2022年4月17号 +订单状态 0-未接单
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(designCarNum)) o.setTranStatus(1);
                if (isDel) o.setTranStatus(6);
                //2022年8月4号 +客商对订单的描述供司机运货时查看
                o.setOutCusDesc(goods.getOutCusDesc());
                o.setInCusDesc(goods.getInCusDesc());
                o.setMold2(goods.getMold2());
                o.setBeginDistrictCode(goods.getBeginDistrictCode());
                o.setEndDistrictCode(goods.getEndDistrictCode());
                //2024年4月25 +智慧能源需要的  合同备案中的煤种ID 字段
                if (StrUtil.isNotEmpty(goods.getOutVarietyCode())) o.setOutVarietyCode(goods.getOutVarietyCode());
                addOrderFees2(goods, o);
                if (StrUtil.isNotEmpty(designCarNum) && !isDel) {//指定车牌号，生成接单信息, 注意：企业系统未下单成功的不应该生成接单信息
                    o.setCarNum(designCarNum);
                    o.setDid(goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder5" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<Object> addGoods7(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay, String loadPound, Long loadTime) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        goods.setCid(cid);
        //根据客商本身设置的收费，添加货运信息时加入计费金额，2022年4月19号，还未启用收费功能，屏蔽客商自主收费
        goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

        //校验是否为往货业务
        if (StrUtil.isNotEmpty(goods.getMold2()) && goods.getMold2() == 1) {
            if (StrUtil.isEmpty(goods.getBeginDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getBeginPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
            if (StrUtil.isEmpty(goods.getEndDistrictCode()))
                return ServerResponse.createError("往货业务中，" + goods.getEndPoint() + "缺少区划编码，请在地址列表中删除后重新添加");
        }

        //todo:校验参数不能为空
        String cMsg = checkGoodsParam(goods);
        if (!StrUtil.isEmpty(cMsg)) return ServerResponse.createError(cMsg);

        //todo:排除反复添加同一条货运信息
        String oGid = checkGoods(cid, goods);
        if (!StrUtil.isEmpty(oGid)) return ServerResponse.createError("已存在货运信息");

        //todo: 校验票号outMin 和 inMin
        String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
        if (StrUtil.isNotEmpty(billCodeStr)) return ServerResponse.createError(billCodeStr);

        //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
        String totalStr = checkTotal(goods);
        if (!StrUtil.isEmpty(totalStr)) return ServerResponse.createError("车数大于企业可用票号数量");

        //todo:新车牌，添加到客商司机池
        DriverInfo driverInfo = null;
        if (!StrUtil.isEmpty(designCarNum)) {
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

            //0.判断车辆是否通过审核
            Query<DriverToCar> dtcQuery = driverToCarDao.createQuery();
            dtcQuery.filter("carNum", designCarNum);
            dtcQuery.filter("driverId", designDriverId);
            DriverToCar driverToCar = driverToCarDao.get(dtcQuery);
            if (driverToCar == null) return ServerResponse.createError("指定的车号，司机还未关联！");
            if (driverToCar.getDelete() == 1) return ServerResponse.createError("司机已删除车辆");
            if (driverToCar.getDelete() == 2) return ServerResponse.createError("车号存在争议，平台已限制司机使用");
            Car car = carDao.getByPK(driverToCar.getCarId());
            if (car == null) return ServerResponse.createError("车号参数错误,车辆不存在");
            if (car.getVerify() == 0 || car.getVerify() == 2)
                return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息不存在，则下单失败
            driverInfo = driverInfoService.getByPK(designDriverId);
            if (driverInfo == null) {
                return ServerResponse.createError("指定的车牌号司机不存在");
            }
            if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4)) {
                return ServerResponse.createError("指定的车牌号司机未通过审核");
            }

            //3.判断司机是否有未完成订单
            if (driverInfo.getHaveOrder() > 0)
                return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能接单");
        }

        //todo:查询将要添加的货运信息 企业要求代扣的情况,平台配置企业带扣信息，不调用接口
        Map<String, Object> payMap = sysUnitTotalFees3(goods);
        if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg")))
            return ServerResponse.createError((String) payMap.get("msg"));

        //todo:客商选择代付的情况，判断客商账户余额
        BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee"); //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        if (isPay != 0) {
            goods.setPayCid(cid);
            BigDecimal cusAvailableFees = customerAccountService.getAvailableFee(cid);
            if (cusAvailableFees.compareTo(payFee) < 0) return ServerResponse.result(7, "账户金额不足，请先充值");
        }

        SysUnit[] sysUnits = (SysUnit[]) payMap.get("sysUnits");
        //判断收货业务是否需要录入对方净重
        if (goods.getMold() == 1){
            SysUnit inSubUnit = sysUnits[3];
            if (StrUtil.isNotEmpty(inSubUnit.getInNeedNetWeight()) && inSubUnit.getInNeedNetWeight() == 1 && StrUtil.isEmpty(goods.getInNetWeightOne()))
                return ServerResponse.createError("需要录入对方净重");
        }
        goods.setStatus(0);
        //todo:请求企业系统下单
        Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysUnits[0], sysUnits[2], true);
        if (ObjectUtil.isNotEmpty(msg)) {
            //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
            String outSynMsg = msg.get("outSynMsg");
            String inSynMsg = msg.get("inSynMsg");
            if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                goods.setOutMin(null);
                goods.setOrderNum0(0);
                goods.setOrderNum1(0);
                goods.setDelNum(goods.getTotal());
                addGoodsAndOrder6(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo, loadPound, loadTime);
            }
            if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                goods.setInMin(null);
                goods.setOrderNum0(0);
                goods.setOrderNum1(0);
                goods.setDelNum(goods.getTotal());
                addGoodsAndOrder6(designCarNum, goods, true, !StrUtil.isEmpty(designCarNum) ? 1 : 0, driverInfo, loadPound, loadTime);
            }
            return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
        }

        Goods result;
        if (StrUtil.isNotEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder6(designCarNum, goods, false, 1, driverInfo, loadPound, loadTime);
            if (result == null) {
                return ServerResponse.createError("手工下单失败");
            }
            //todo:手工下单成功后要推送信息给司机
            if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
                String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            } else {
                String mes = cUser.getMobile() + "发布的面议单";
                pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
            }
        } else {
            //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
            result = addGoodsAndOrder6(designCarNum, goods, false, 0, driverInfo, loadPound, loadTime);
            if (result == null) {
                return ServerResponse.createError("添加失败");
            }
        }

        if (result.getpType() == 2) {
            Map<String, Object> map = new HashMap<>();
            map.put("gid", result.getGid());
            map.put("mold", result.getMold());
            map.put("beginPoint", result.getBeginPoint());
            map.put("endPoint", result.getEndPoint());
            map.put("total", result.getTotal());
            map.put("price", result.getPrice());
            map.put("outVariety", result.getOutVariety());
            map.put("inVariety", result.getInVariety());
            return ServerResponse.createSuccess("添加成功", JSON.toJSONString(map));
        } else {
            return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));
        }
    }

    @Transactional
    Goods addGoodsAndOrder6(String designCarNum, Goods goods, boolean isDel, Integer isHand, DriverInfo driverInfo, String loadPound, Long loadTime) {
        Integer outMin = goods.getOutMin();
        Integer inMin = goods.getInMin();
        if (StrUtil.isNotEmpty(designCarNum)) goods.setTotal(1);

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();

            goods.setGid(Utils.getUUID());
            goods.setUpdateTime(time.getTime());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", new ObjectId(goods.getCid()));
            goodsCollection.insertOne(clientSession, gDoc);

            //TODO:1.根据车数 total 生成相应数量的订单
            int total = goods.getTotal();
            List<Document> list = new ArrayList<>();
            for (int i = 0; i < total; i++) {
                String oid = Utils.getUUID();

                Order o = new Order();
                o.setOid(oid);
                //5，采购-入库。6，销售-出库
                if (outMin != null) {
                    o.setOutMinMax(String.valueOf(outMin + i));
                    o.setOutBillCode(goods.getOutUnitCode() + "6" + String.format("%09d", (outMin + i)));
                    o.setOutChecking(0);
                    o.setOutTradingUnitName(goods.getOutTradingUnitName());
                    o.setOutTradingUnit(goods.getOutTradingUnit());
                }
                if (inMin != null) {
                    o.setInMinMax(String.valueOf(inMin + i));
                    o.setInBillCode(goods.getInUnitCode() + "5" + String.format("%09d", (inMin + i)));
                    o.setInChecking(0);
                    o.setInTradingUnitName(goods.getInTradingUnitName());
                    o.setInTradingUnit(goods.getInTradingUnit());
                }
                o.setDelete(isDel);
                o.setIsHand(isHand);
                o.setUpdateTime(time.getTime());
                o.setpType(goods.getpType());
                o.setSpell(goods.getSpell());
                o.setPlace(goods.getPlace());
                o.setCid(goods.getCid());
                //2022年4月17号 +订单状态 0-未接单
                o.setTranStatus(0);
                if (StrUtil.isNotEmpty(designCarNum)) o.setTranStatus(1);
                if (isDel) o.setTranStatus(6);
                //2022年8月4号 +客商对订单的描述供司机运货时查看
                o.setOutCusDesc(goods.getOutCusDesc());
                o.setInCusDesc(goods.getInCusDesc());
                o.setMold2(goods.getMold2());
                o.setBeginDistrictCode(goods.getBeginDistrictCode());
                o.setEndDistrictCode(goods.getEndDistrictCode());
                //2024年4月25 +智慧能源需要的  合同备案中的煤种ID 字段
                if (StrUtil.isNotEmpty(goods.getOutVarietyCode())) o.setOutVarietyCode(goods.getOutVarietyCode());
                //2024年9月2号 +采购业务需录入对方的净重
                if (StrUtil.isNotEmpty(goods.getInNetWeightOne())) {
                    o.setInNetWeight(goods.getInNetWeightOne());
                    o.setInNetWeightPerson("CU");
                }
                if (StrUtil.isNotEmpty(loadPound)) {
                    o.setLoadPound(loadPound);
                    o.setLoadPoundPerson("CU");
                }
                if (StrUtil.isNotEmpty(loadTime)) {
                    o.setLoadTime(loadTime);
                    o.setLoadTimePerson("CU");
                }
                if (StrUtil.isNotEmpty(goods.getLoadPoundPho())) {
                    o.setLoadPoundPho(goods.getLoadPoundPho());
                    o.setLoadPoundPerson("CU");
                }

                addOrderFees2(goods, o);
                if (StrUtil.isNotEmpty(designCarNum) && !isDel) {//指定车牌号，生成接单信息, 注意：企业系统未下单成功的不应该生成接单信息
                    o.setCarNum(designCarNum);
                    o.setDid(goods.getDriverId());
                    driverInfo.setCarNum(designCarNum);
                    orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));

                    //修改司机是否有运输中订单字段haveOrder
                    Map<String, Object> dvrUpMap = new HashMap<>();
                    Map<String, Object> dvrMap = new HashMap<>();
                    dvrMap.put("haveOrder", 1);
                    dvrUpMap.put("$set", dvrMap);
                    driverCollection.updateOne(clientSession, new Document("_id", driverInfo.getObjectId()), Document.parse(JSONObject.toJSONString(dvrUpMap)));
                }
                Document oDoc = Document.parse(JSONObject.toJSONString(o));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
            return goods;
        } catch (Exception e) {
            logger.error("addGoodsAndOrder6" + e.getMessage());
            clientSession.abortTransaction();
            return null;
        } finally {
            clientSession.close();
        }
    }

    /**
     * 查询客商下的货运信息，并按照创建时间降序排序
     * param: String cid;
     * return: GoodsInfoData
     */
    @Override
    //public ServerResponse<List<GoodsInfoData>> goodsListData(Long goodsTime, String pointName, Integer type) {
    public ServerResponse<EGridResult> goodsListData(Long goodsTime, String pointName, Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        //TODO:1.查询当前客商的货运信息
        Query<Goods> query = this.createQuery();
        query.filter("cid", cid);
        if (!StrUtil.isEmpty(goodsTime)) {
            Date startTime = IdUnit.weeHours1(goodsTime, "00:00:00", 0);
            Date endTime = IdUnit.weeHours1(goodsTime, "00:00:00", 1);
            query.filter("createTime >=", startTime).filter("createTime <=", endTime);
        }
        if (!StrUtil.isEmpty(pointName)) {
            query.or(
                    query.criteria("beginPoint").contains(pointName),
                    query.criteria("endPoint").contains(pointName)
            );
        }

        Long total = 0L;
        List<Goods> listTotal = query.find().toList();
        for (Goods g : listTotal) {
            List<Order> orders = orderService.searchOrderByGid(g.getGid(), type);
            if (orders != null && orders.size() > 0) total++;
        }

        //todo:2.查询order使用情况，区分要查询的货运信息类型（0-未接单，1-未完成订单，2-历史订单）
        /*
         * 0-未接单，1-未完成订单，2-历史订单
         * goods下有未被司机接的order，则goods也属于未接单的货运信息
         * goods下有司机接单后未完成的order，则goods也属于未完成订单的货运信息
         * goods下有司机完成的order，则goods也属于历史货运信息
         * */
        List<GoodsInfoData> resultData = new ArrayList<>();
        int i = 0, j = 0;
        while (resultData.size() < rows && i < listTotal.size()) {
            Goods g = listTotal.get(i);

            List<Order> orders = orderService.searchOrderByGid(g.getGid(), type);

            /*isPay代付
            groupNo选择抢单司机组
            latitude//纬度
            longitude //经度
            designCarNum // 指定车牌号
            designDriverId //指定司机编号
            */

            if (orders != null && orders.size() > 0) {
                Order firstOrder = orders.get(0);
                int isPay = 0;  //客商是否代付(0-不代付，1-代付)
                String designCarNum = null;
                String designDriverId = null;
                if (cid.equals(firstOrder.getPayerId()) || cid.equals(firstOrder.getPayerIdOut()) || cid.equals(firstOrder.getPayerIdIn()) ||
                        cid.equals(firstOrder.getPayerId1Out()) || cid.equals(firstOrder.getPayerId1In()) || cid.equals(firstOrder.getPayerId2Out()) || cid.equals(firstOrder.getPayerId2In()))
                    isPay = 1;
                if (g.getTotal() == 1) {
                    OrderTaking ot = orderTakingService.get("oid", firstOrder.getOid());
                    if (ot != null) {
                        designDriverId = ot.getDid();
                        designCarNum = ot.getCarNum();
                    }
                }

                if (j++ >= (page - 1) * rows) {
                    //TODO:处理要返回的数据包装到GoodsInfoData对象中
                    GoodsInfoData result = new GoodsInfoData();
                    BeanUtils.copyProperties(g, result);
                    result.setAppType(addAppType(g));
                    result.setReleaseTime(g.getCreateTime());
                    result.setIsPay(isPay);
                    if (StrUtil.isNotEmpty(designCarNum)) result.setDesignCarNum(designCarNum);
                    if (StrUtil.isNotEmpty(designDriverId)) result.setDesignDriverId(designDriverId);
                    resultData.add(result);
                }
            }
            i++;
        }
        //return ServerResponse.createSuccess("查询成功", resultData);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    //给Goods添加App端需要处理的类型分类
    private Boolean[] addAppType(Goods goods) {
        Boolean[] appType = new Boolean[]{true, true, true, true, true, true, false, false};
        /*
         * 数组索引
         * 0-是否可修改，goods下有 司机未接单 和 司机接单但未拉货的 可以修改,收货物流不可修改订单,超过72小时订单过期要作废不可修改
         * 1-是否可废除，goods下有 司机未接单 和 司机接单但未拉货/未进场的 可以废除
         * 2-是否可分享给微信司机用户，goods下是否有可用订单 以及 字段isWeChat 区分,超过72小时订单过期不可分享
         * 3-是否来自友商分享，goods字段shareGid 或者 shareCid 区分
         * 4-是否可分享给友商，非微信司机用户的goods，订单没有全部被分享且有司机未接单的，超过72小时订单过期不可分享
         * 5-是否可退回，     goods来自他人分享 且 下面有 司机未接单的
         * 6-暂停接单
         * 7-恢复接单
         */
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", goods.getGid());
        oQuery.filter("carNum", null);
        oQuery.filter("delete", false);
        List<Order> orders = orderService.list(oQuery); //goods下司机未接的订单,
        Query<Order> ocQuery = orderService.createQuery();
        ocQuery.or(ocQuery.criteria("outChecking").greaterThan(0),
                ocQuery.criteria("inChecking").greaterThan(0));
        List<Order> checkingOrders = orderService.list(ocQuery); //goods下一检过的订单,goods下司机未运输的订单
        if (goods.getMold() == 1 || (orders.size() <= 0 && checkingOrders.size() == goods.getTotal()) || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[0] = false;   //goods下已经没有可用订单了
        if (orders.size() <= 0 && checkingOrders.size() == goods.getTotal())
            appType[1] = false;
        if (goods.getpType() == 5) {
            appType[0] = false;
            appType[1] = false;
        }
        //if (orders.size() <= 0 || StrUtil.isEmpty(goods.getIsWeChat()) || goods.getIsWeChat() == 0) appType[2] = false;
        if ((orders.size() <= 0 && checkingOrders.size() == goods.getTotal()) || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[2] = false;

        //平台微信下单开关关闭的时候 下过的单也不允许微信分享了
        Query<SysSwitch> sysSwitchQuery = sysSwitchService.createQuery();
        sysSwitchQuery.filter("code", 0);
        sysSwitchQuery.filter("type", 3);
        SysSwitch sysSwitch = sysSwitchService.get(sysSwitchQuery);   //0-客商的开关
        if (sysSwitch != null && StrUtil.isNotEmpty(sysSwitch.getShare()) && sysSwitch.getShare() == 1)
            appType[2] = false;

        if (StrUtil.isEmpty(goods.getShareGid())) appType[3] = false;
        if (orders.size() <= 0 || (StrUtil.isNotEmpty(goods.getIsWeChat()) && goods.getIsWeChat() == 1) || goods.getShare() == 2 || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[4] = false;
        if (orders.size() <= 0 || StrUtil.isEmpty(goods.getShareGid())) appType[5] = false;

        if (goods.getStatus() == 0 && orders.size() > 0) appType[6] = true;
        if (goods.getStatus() == 1 && orders.size() > 0) appType[7] = true;

        return appType;
    }

    /**
     * 查询客商下的货运信息，并按照创建时间降序排序
     * param: String cid;
     * return: GoodsInfoData
     */
    @Override
    public ServerResponse<EGridResult> goodsListData2(Long goodsTime, String pointName, Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        assert cid != null;

        //TODO:1.查询当前客商的货运信息
        Query<Goods> gQuery = this.createQuery();
        //gQuery.filter("cid", cid);
        gQuery.or(
                gQuery.criteria("cid").equal(cid),
                gQuery.criteria("shareCid").equal(cid)
        );
        if (!StrUtil.isEmpty(goodsTime)) {
            Date startTime = IdUnit.weeHours1(goodsTime, "00:00:00", 0);
            Date endTime = IdUnit.weeHours1(goodsTime, "00:00:00", 1);
            gQuery.filter("createTime >=", startTime).filter("createTime <=", endTime);
        }
        if (!StrUtil.isEmpty(pointName)) {
            gQuery.or(
                    gQuery.criteria("beginPoint").contains(pointName),
                    gQuery.criteria("endPoint").contains(pointName)
            );
        }
        gQuery.order(Sort.descending("objectId"));

        if (StrUtil.isEmpty(type)) type = 4;
        switch (type) {
            case 0:
                gQuery.criteria("orderNum0").greaterThan(0);
                break;
            case 1:
                gQuery.criteria("orderNum1").greaterThan(0);
                break;
            case 2:
                gQuery.criteria("orderNum2").greaterThan(0);
                break;
            default:
                break;
        }
        EGridResult<Goods> goodsEGridResult = this.findPage(page, rows, gQuery);

        Query<SysSwitch> sysSwitchQuery = sysSwitchService.createQuery();
        sysSwitchQuery.filter("code", 0);
        sysSwitchQuery.filter("type", 3);
        SysSwitch sysSwitch = sysSwitchService.get(sysSwitchQuery);   //0-客商的开关
        List<GoodsInfoData> resultData = new ArrayList<>();
        for (Goods g : goodsEGridResult.getRows()) {
            GoodsInfoData result = new GoodsInfoData();
            BeanUtils.copyProperties(g, result);

            result.setOutNetWeight(g.getOutGrossWeight(), g.getOutTareWeight());
            result.setInNetWeight(g.getInGrossWeight(), g.getInTareWeight());

            switch (type) {
                case 0:
                    result.setAppType(addAppType0(cid, g, sysSwitch));
                    result.setIsHistory(0);
                case 1:
                    result.setAppType(addAppType1(cid, g, sysSwitch));
                    result.setIsHistory(1);
                case 2:
                    result.setAppType(addAppType2(cid, g));
                    result.setIsHistory(2);
                default:
                    result.setAppType(addAppType1(cid, g, sysSwitch));
                    if (g.getTotal() == g.getOrderNum0() + g.getDelNum()) {
                        result.setIsHistory(0);
                    } else if (g.getTotal() == g.getOrderNum2() + g.getDelNum()) {
                        result.setIsHistory(2);
                    } else {
                        result.setIsHistory(1);
                    }
                    break;
            }

            if (StrUtil.isNotEmpty(g.getCarNum())) result.setDesignCarNum(g.getCarNum());
            if (StrUtil.isNotEmpty(g.getDriverId())) result.setDesignDriverId(g.getDriverId());
            result.setReleaseTime(g.getCreateTime());

            //区分订单是客商自己的、分享给友商的、来自客商分享的三种情况（分别由数字0、1、2表示）
            result.setCusName(g.getCustomerUser().getName());
            if (StrUtil.isEmpty(g.getShareCid())) {
                result.setShareType("0");
            } else if (cid.equals(g.getShareCid())) {
                result.setShareType("2");
                CustomerUser shareCus = customerUserService.getByPK(g.getShareCid());
                result.setShareCusName(shareCus.getName());
            } else {
                result.setShareType("1");
                CustomerUser shareCus = customerUserService.getByPK(g.getShareCid());
                result.setShareCusName(shareCus.getName());
            }

            resultData.add(result);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(goodsEGridResult.getTotal(), resultData));
    }

    private Boolean[] addAppType0(String cid, Goods goods, SysSwitch sysSwitch) { //0-未接单类型
        Boolean[] appType = new Boolean[]{true, true, true, false, true, false, false, false, true};
        /*
         * 数组索引
         * 8-是否可以修改运往地
         */
        if (StrUtil.isNotEmpty(goods.getShareCid()) || goods.getpType() == 5) appType[0] = false;
//        if (StrUtil.isNotEmpty(goods.getShareCid())) appType[1] = false;
        //友商也可以删除订单
        if (StrUtil.isNotEmpty(goods.getShareCid()) && !cid.equals(goods.getShareCid())) appType[1] = false;

        if ((cid.equals(goods.getCid()) && StrUtil.isNotEmpty(goods.getShareCid())) || (new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000))
            appType[2] = false;
        if (appType[2] && sysSwitch != null && StrUtil.isNotEmpty(sysSwitch.getShare()) && sysSwitch.getShare() == 1)
            appType[2] = false;

        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (StrUtil.isNotEmpty(goods.getShareCid()) || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[4] = false;
        if (cid.equals(goods.getShareCid()) && goods.getTotal() == goods.getDelNum() + goods.getOrderNum0())
            appType[5] = true;
        if (goods.getStatus() == 0) appType[6] = true;
        if (goods.getStatus() == 1) appType[7] = true;
        return appType;
    }

    private Boolean[] addAppType1(String cid, Goods goods, SysSwitch sysSwitch) { //1-接单未完成类型
        Boolean[] appType = new Boolean[]{false, false, false, false, false, false, false, false, false};
        //有可使用订单，按addAppType0处理
        if (goods.getOrderNum0() > 0) return addAppType0(cid, goods, sysSwitch);

        //没有可使用订单
        if (goods.getMold() != 1 && goods.getOrderNum1() > goods.getOrderNum3() && StrUtil.isEmpty(goods.getShareCid())) {
            appType[0] = true;
//            appType[1] = true;
        }
        //友商也可以删除订单
        if (goods.getMold() != 1 && goods.getOrderNum1() > goods.getOrderNum3() && (StrUtil.isEmpty(goods.getShareCid()) || cid.equals(goods.getShareCid())))
            appType[1] = true;

        if (goods.getpType() == 5) appType[0] = false;
        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (goods.getStatus() == 0) appType[6] = true;
        if (goods.getStatus() == 1) appType[7] = true;
        if (goods.getTotal() != goods.getOrderNum2() + goods.getTotal()) appType[8] = true;
        return appType;
    }

    private Boolean[] addAppType2(String cid, Goods goods) { //2-订单完成类型
        Boolean[] appType = new Boolean[]{false, false, false, true, false, false, false, false, false};
        if (cid.equals(goods.getShareCid())) appType[3] = true;

        return appType;
    }

    @Override
    public ServerResponse<EGridResult> goodsListData3(Long goodsTime, String pointName, Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        assert cid != null;

        //TODO:1.查询当前客商的货运信息
        Query<Goods> gQuery = this.createQuery();
        //gQuery.filter("cid", cid);
        gQuery.or(
                gQuery.criteria("cid").equal(cid),
                gQuery.criteria("shareCid").equal(cid)
        );
        if (!StrUtil.isEmpty(goodsTime)) {
            Date startTime = IdUnit.weeHours1(goodsTime, "00:00:00", 0);
            Date endTime = IdUnit.weeHours1(goodsTime, "00:00:00", 1);
            gQuery.filter("createTime >=", startTime).filter("createTime <=", endTime);
        }
        if (!StrUtil.isEmpty(pointName)) {
            gQuery.or(
                    gQuery.criteria("beginPoint").contains(pointName),
                    gQuery.criteria("endPoint").contains(pointName)
            );
        }
        gQuery.order(Sort.descending("objectId"));

        if (StrUtil.isEmpty(type)) type = 4;
        switch (type) {
            case 0:
                gQuery.criteria("orderNum0").greaterThan(0);
                break;
            case 1:
                gQuery.criteria("orderNum1").greaterThan(0);
                break;
            case 2:
                gQuery.criteria("orderNum2").greaterThan(0);
                break;
            default:
                break;
        }
//        Long total = this.findPage(page, rows, gQuery).getTotal();
//        if (total <= 0) return ServerResponse.createSuccess("查询成功");
        Iterator<GoodsPipeline> iterator = goodsPipelineIterator(cid, gQuery, page, rows);

        Query<SysSwitch> sysSwitchQuery = sysSwitchService.createQuery();
        sysSwitchQuery.filter("code", 0);
        sysSwitchQuery.filter("type", 3);
        SysSwitch sysSwitch = sysSwitchService.get(sysSwitchQuery);   //0-客商的开关
        List<GoodsInfoData> resultData = new ArrayList<>();
        while (iterator.hasNext()) {
            GoodsPipeline g = iterator.next();
            GoodsInfoData result = new GoodsInfoData();
            BeanUtils.copyProperties(g, result);

            result.setOutNetWeight(g.getOutGrossWeight(), g.getOutTareWeight());
            result.setInNetWeight(g.getInGrossWeight(), g.getInTareWeight());

            switch (type) {
                case 0:
                    result.setAppType(addAppType0(cid, g, sysSwitch));
                    result.setIsHistory(0);
                case 1:
                    result.setAppType(addAppType1(cid, g, sysSwitch));
                    result.setIsHistory(1);
                case 2:
                    result.setAppType(addAppType2(cid, g));
                    result.setIsHistory(2);
                default:
                    result.setAppType(addAppType1(cid, g, sysSwitch));
                    if (g.getTotal() == g.getOrderNum0() + g.getDelNum()) {
                        result.setIsHistory(0);
                    } else if (g.getTotal() == g.getOrderNum2() + g.getDelNum()) {
                        result.setIsHistory(2);
                    } else {
                        result.setIsHistory(1);
                    }
                    break;
            }

            if (StrUtil.isNotEmpty(g.getCarNum())) result.setDesignCarNum(g.getCarNum());
            if (StrUtil.isNotEmpty(g.getDriverId())) result.setDesignDriverId(g.getDriverId());
            result.setReleaseTime(g.getCreateTime());

            //区分订单是客商自己的、分享给友商的、来自客商分享的三种情况（分别由数字0、1、2表示）
            result.setShareType(g.getShareCid012());
            result.setCusName(g.getCusUser().get(0).getName());
            if (StrUtil.isNotEmpty(g.getShareCid()))
                result.setShareCusName(g.getShareCustomerUser().get(0).getName());

            result.setProductID(g.getOutVarietyCode());
            resultData.add(result);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(1, resultData));
    }

    private Iterator<GoodsPipeline> goodsPipelineIterator(String cid, Query<Goods> query, Integer page, Integer rows) {
        AggregationPipeline pipeline = this.createAggregation();
        pipeline.match(query);

        pipeline.group(
                id(grouping("gid")),
                grouping("gid", first("gid")),
                grouping("cid", first("cid")),
                grouping("tradeName", first("tradeName")),
                grouping("beginPoint", first("beginPoint")),
                grouping("endPoint", first("endPoint")),
                grouping("total", first("total")),
                grouping("weight", first("weight")),
                grouping("price", first("price")),
                grouping("distance", first("distance")),
                grouping("tolls", first("tolls")),
                grouping("pType", first("pType")),
                grouping("mold", first("mold")),
                grouping("outUnitCode", first("outUnitCode")),
                grouping("outUnitName", first("outUnitName")),
                grouping("inUnitCode", first("inUnitCode")),
                grouping("inUnitName", first("inUnitName")),
                grouping("outBizContractCode", first("outBizContractCode")),
                grouping("outBizContractName", first("outBizContractName")),
                grouping("inBizContractCode", first("inBizContractCode")),
                grouping("inBizContractName", first("inBizContractName")),
                grouping("outVariety", first("outVariety")),
                grouping("inVariety", first("inVariety")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("inSubName", first("inSubName")),
                grouping("outDefaultArea", first("outDefaultArea")),
                grouping("outAreaName", first("outAreaName")),
                grouping("inDefaultArea", first("inDefaultArea")),
                grouping("inAreaName", first("inAreaName")),
                grouping("outMin", first("outMin")),
                grouping("outMax", first("outMax")),
                grouping("inMin", first("inMin")),
                grouping("inMax", first("inMax")),
                grouping("status", first("status")),
                grouping("delNum", first("delNum")),
                grouping("orderNum0", first("orderNum0")),
                grouping("orderNum1", first("orderNum1")),
                grouping("orderNum2", first("orderNum2")),
                grouping("orderNum3", first("orderNum3")),
                grouping("outGrossWeight", first("outGrossWeight")),
                grouping("outTareWeight", first("outTareWeight")),
                grouping("inGrossWeight", first("inGrossWeight")),
                grouping("inTareWeight", first("inTareWeight")),
                grouping("carNum", first("carNum")),
                grouping("driverId", first("driverId")),
                grouping("isPay", first("isPay")),
                grouping("payCid", first("payCid")),
                grouping("groupNo", first("groupNo")),
                grouping("isWeChat", first("isWeChat")),
                grouping("share", first("share")),
                grouping("shareCid", first("shareCid")),
                grouping("customerUser", first("customerUser")),
                grouping("feesOut", first("feesOut")),
                grouping("outUnitPayId", first("outUnitPayId")),
                grouping("feesIn", first("feesIn")),
                grouping("inUnitPayId", first("inUnitPayId")),
                grouping("fees1Out", first("fees1Out")),
                grouping("fees1In", first("fees1In")),
                grouping("fees2Out", first("fees2Out")),
                grouping("fees2In", first("fees2In")),
                grouping("fees3", first("fees3")),
                grouping("fee5Out", first("fee5Out")),
                grouping("fee5In", first("fee5In")),
                grouping("fee5OutId", first("fee5OutId")),
                grouping("fee5InId", first("fee5InId")),
                grouping("fee5OutType", first("fee5OutType")),
                grouping("fee5InType", first("fee5InType")),
                grouping("longitude", first("longitude")),
                grouping("latitude", first("latitude")),
                grouping("spell", first("spell")),
                grouping("place", first("place")),
                grouping("outTradingUnit", first("outTradingUnit")),
                grouping("outTradingUnitName", first("outTradingUnitName")),
                grouping("inTradingUnit", first("inTradingUnit")),
                grouping("inTradingUnitName", first("inTradingUnitName")),
                grouping("outCusDesc", first("outCusDesc")),
                grouping("inCusDesc", first("inCusDesc")),
                grouping("partSurplusNum", first("partSurplusNum")),
                grouping("createTime", first("createTime")),
                grouping("updateTime", first("updateTime")),
                grouping("mold2", first("mold2")),
                grouping("beginDistrictCode", first("beginDistrictCode")),
                grouping("endDistrictCode", first("endDistrictCode")),
                grouping("outVarietyCode", first("outVarietyCode")),
                grouping("remark1", first("remark1")),
                grouping("remark2", first("remark2")),
                grouping("remark3", first("remark3")),
                grouping("remark4", first("remark4")),
                grouping("shareCid012",
                        Projection.expression(
                                "$first",
                                new BasicDBObject("$cond", Arrays.asList(
                                        new BasicDBObject("$eq", Arrays.asList("$shareCid", null)), "0",
                                        new BasicDBObject("$cond", Arrays.asList(
                                                new BasicDBObject("$gt", Arrays.asList("$shareCid", "")),
                                                new BasicDBObject("$cond", Arrays.asList(new BasicDBObject("$eq", Arrays.asList("$shareCid", cid)), "2", "1")),
                                                "0"
                                        ))
                                ))
                        )
                )
        );
        pipeline.sort(Sort.descending("createTime"));

        Projection cidProjection = Projection.projection(
                "cid",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$cid").append("to", "objectId")
                )
        );
        Projection shareCidProjection = Projection.projection(
                "shareCid",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$shareCid").append("to", "objectId").append("onError", "").append("onNull", "")
                )
        );
        pipeline.project(
                Projection.projection("gid"),
                Projection.projection("cid"),
                Projection.projection("tradeName"),
                Projection.projection("beginPoint"),
                Projection.projection("endPoint"),
                Projection.projection("total"),
                Projection.projection("weight"),
                Projection.projection("price"),
                Projection.projection("distance"),
                Projection.projection("tolls"),
                Projection.projection("pType"),
                Projection.projection("mold"),
                Projection.projection("outUnitCode"),
                Projection.projection("outUnitName"),
                Projection.projection("inUnitCode"),
                Projection.projection("inUnitName"),
                Projection.projection("outBizContractCode"),
                Projection.projection("outBizContractName"),
                Projection.projection("inBizContractCode"),
                Projection.projection("inBizContractName"),
                Projection.projection("outVariety"),
                Projection.projection("inVariety"),
                Projection.projection("outDefaultDownUnit"),
                Projection.projection("outSubName"),
                Projection.projection("inDefaultDownUnit"),
                Projection.projection("inSubName"),
                Projection.projection("outDefaultArea"),
                Projection.projection("outAreaName"),
                Projection.projection("inDefaultArea"),
                Projection.projection("inAreaName"),
                Projection.projection("outMin"),
                Projection.projection("outMax"),
                Projection.projection("inMin"),
                Projection.projection("inMax"),
                Projection.projection("status"),
                Projection.projection("delNum"),
                Projection.projection("orderNum0"),
                Projection.projection("orderNum1"),
                Projection.projection("orderNum2"),
                Projection.projection("orderNum3"),
                Projection.projection("outGrossWeight"),
                Projection.projection("outTareWeight"),
                Projection.projection("inGrossWeight"),
                Projection.projection("inTareWeight"),
                Projection.projection("carNum"),
                Projection.projection("driverId"),
                Projection.projection("isPay"),
                Projection.projection("payCid"),
                Projection.projection("groupNo"),
                Projection.projection("isWeChat"),
                Projection.projection("share"),
                Projection.projection("feesOut"),
                Projection.projection("outUnitPayId"),
                Projection.projection("feesIn"),
                Projection.projection("inUnitPayId"),
                Projection.projection("fees1Out"),
                Projection.projection("fees1In"),
                Projection.projection("fees2Out"),
                Projection.projection("fees2In"),
                Projection.projection("fees3"),
                Projection.projection("fee5Out"),
                Projection.projection("fee5In"),
                Projection.projection("fee5OutId"),
                Projection.projection("fee5InId"),
                Projection.projection("fee5OutType"),
                Projection.projection("fee5InType"),
                Projection.projection("longitude"),
                Projection.projection("latitude"),
                Projection.projection("spell"),
                Projection.projection("place"),
                Projection.projection("outTradingUnit"),
                Projection.projection("outTradingUnitName"),
                Projection.projection("inTradingUnit"),
                Projection.projection("inTradingUnitName"),
                Projection.projection("outCusDesc"),
                Projection.projection("inCusDesc"),
                Projection.projection("partSurplusNum"),
                Projection.projection("createTime"),
                Projection.projection("updateTime"),
                Projection.projection("shareCid012"),
                Projection.projection("mold2"),
                Projection.projection("beginDistrictCode"),
                Projection.projection("endDistrictCode"),
                Projection.projection("remark1"),
                Projection.projection("remark2"),
                Projection.projection("remark3"),
                Projection.projection("remark4"),
                cidProjection,
                shareCidProjection,
                Projection.projection("outVarietyCode")
        );
        pipeline.lookup("t_customer", "cid", "_id", "cusUser");
        pipeline.lookup("t_customer", "shareCid", "_id", "shareCustomerUser");
        pipeline.skip((page - 1) * rows);
        pipeline.limit(rows);

        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Iterator<GoodsPipeline> iterator = pipeline.aggregate(GoodsPipeline.class, aggregationOptions);

        //Iterator<GoodsPipeline> iterator = pipeline.aggregate(GoodsPipeline.class);

        return iterator;
    }

    private Boolean[] addAppType0(String cid, GoodsPipeline goods, SysSwitch sysSwitch) { //0-未接单类型
        Boolean[] appType = new Boolean[]{true, true, true, false, true, false, false, false, true};
        /*
         * 数组索引
         * 8-是否可以修改运往地
         */
        if (StrUtil.isNotEmpty(goods.getShareCid()) || goods.getpType() == 5) appType[0] = false;
//        if (StrUtil.isNotEmpty(goods.getShareCid())) appType[1] = false;
        //友商也可以删除订单
        if (StrUtil.isNotEmpty(goods.getShareCid()) && !cid.equals(goods.getShareCid())) appType[1] = false;

        if ((cid.equals(goods.getCid()) && StrUtil.isNotEmpty(goods.getShareCid())) || (new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000))
            appType[2] = false;
        if (appType[2] && sysSwitch != null && StrUtil.isNotEmpty(sysSwitch.getShare()) && sysSwitch.getShare() == 1)
            appType[2] = false;

        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (StrUtil.isNotEmpty(goods.getShareCid()) || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[4] = false;
        if (cid.equals(goods.getShareCid()) && goods.getTotal() == goods.getDelNum() + goods.getOrderNum0())
            appType[5] = true;
        if (goods.getStatus() == 0) appType[6] = true;
        if (goods.getStatus() == 1) appType[7] = true;
        return appType;
    }

    private Boolean[] addAppType1(String cid, GoodsPipeline goods, SysSwitch sysSwitch) { //1-接单未完成类型
        Boolean[] appType = new Boolean[]{false, false, false, false, false, false, false, false, false};
        //有可使用订单，按addAppType0处理
        if (goods.getOrderNum0() > 0) return addAppType0(cid, goods, sysSwitch);

        //没有可使用订单
        if (goods.getMold() != 1 && goods.getOrderNum1() > goods.getOrderNum3() && StrUtil.isEmpty(goods.getShareCid())) {
            appType[0] = true;
//            appType[1] = true;
        }
        //友商也可以删除订单
        if (goods.getMold() != 1 && goods.getOrderNum1() > goods.getOrderNum3() && (StrUtil.isEmpty(goods.getShareCid()) || cid.equals(goods.getShareCid())))
            appType[1] = true;

        if (goods.getpType() == 5) appType[0] = false;
        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (goods.getStatus() == 0) appType[6] = true;
        if (goods.getStatus() == 1) appType[7] = true;
        if (goods.getTotal() != goods.getOrderNum2() + goods.getTotal()) appType[8] = true;
        return appType;
    }

    private Boolean[] addAppType2(String cid, GoodsPipeline goods) { //2-订单完成类型
        Boolean[] appType = new Boolean[]{false, false, false, true, false, false, false, false, false};
        if (cid.equals(goods.getShareCid())) appType[3] = true;

        return appType;
    }

    @Override
    public ServerResponse<EGridResult> goodsListData4(Long goodsTime, String pointName, Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        assert cid != null;

        //TODO:1.查询当前客商的货运信息
        Query<Goods> gQuery = this.createQuery();
        gQuery.or(
                gQuery.criteria("cid").equal(cid),
                gQuery.criteria("shareCid").equal(cid)
        );
        if (!StrUtil.isEmpty(goodsTime)) {
            Date startTime = IdUnit.weeHours1(goodsTime, "00:00:00", 0);
            Date endTime = IdUnit.weeHours1(goodsTime, "00:00:00", 1);
            gQuery.filter("createTime >=", startTime).filter("createTime <=", endTime);
        }
        if (!StrUtil.isEmpty(pointName)) {
            gQuery.or(
                    gQuery.criteria("beginPoint").contains(pointName),
                    gQuery.criteria("endPoint").contains(pointName)
            );
        }
        gQuery.order(Sort.descending("objectId"));

        if (StrUtil.isEmpty(type)) type = 4;
        switch (type) {
            case 0:
                gQuery.criteria("orderNum0").greaterThan(0);
                break;
            case 1:
                gQuery.criteria("orderNum1").greaterThan(0);
                break;
            case 2:
                gQuery.criteria("orderNum2").greaterThan(0);
                break;
            default:
                break;
        }
        Iterator<GoodsPipeline> iterator = goodsPipelineIterator4(cid, gQuery, page, rows);

        Query<SysSwitch> sysSwitchQuery = sysSwitchService.createQuery();
        sysSwitchQuery.filter("code", 0);
        sysSwitchQuery.filter("type", 3);
        SysSwitch sysSwitch = sysSwitchService.get(sysSwitchQuery);   //0-客商的开关
        List<GoodsInfoData> resultData = new ArrayList<>();
        while (iterator.hasNext()) {
            GoodsPipeline g = iterator.next();
            GoodsInfoData result = new GoodsInfoData();
            BeanUtils.copyProperties(g, result);

            result.setOutNetWeight(g.getOutGrossWeight(), g.getOutTareWeight());
            result.setInNetWeight(g.getInGrossWeight(), g.getInTareWeight());

            SysUnit inSubUnit = null;
            if (g.getMold() == 1) inSubUnit = g.getInSubUnit().get(0);

            switch (type) {
                case 0:
                    result.setAppType(addAppType0_4(cid, g, sysSwitch, inSubUnit));
                    result.setIsHistory(0);
                case 1:
                    result.setAppType(addAppType1_4(cid, g, sysSwitch, inSubUnit));
                    result.setIsHistory(1);
                case 2:
                    result.setAppType(addAppType2_4(cid, g));
                    result.setIsHistory(2);
                default:
                    result.setAppType(addAppType1_4(cid, g, sysSwitch, inSubUnit));
                    if (g.getTotal() == g.getOrderNum0() + g.getDelNum()) {
                        result.setIsHistory(0);
                    } else if (g.getTotal() == g.getOrderNum2() + g.getDelNum()) {
                        result.setIsHistory(2);
                    } else {
                        result.setIsHistory(1);
                    }
                    break;
            }

            if (StrUtil.isNotEmpty(g.getCarNum())) result.setDesignCarNum(g.getCarNum());
            if (StrUtil.isNotEmpty(g.getDriverId())) result.setDesignDriverId(g.getDriverId());
            result.setReleaseTime(g.getCreateTime());

            //区分订单是客商自己的、分享给友商的、来自客商分享的三种情况（分别由数字0、1、2表示）
            result.setShareType(g.getShareCid012());
            result.setCusName(g.getCusUser().get(0).getName());
            if (StrUtil.isNotEmpty(g.getShareCid()))
                result.setShareCusName(g.getShareCustomerUser().get(0).getName());

            result.setProductID(g.getOutVarietyCode());
            resultData.add(result);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(1, resultData));
    }

    private Iterator<GoodsPipeline> goodsPipelineIterator4(String cid, Query<Goods> query, Integer page, Integer rows) {
        AggregationPipeline pipeline = this.createAggregation();
        pipeline.match(query);

        pipeline.group(
                id(grouping("gid")),
                grouping("gid", first("gid")),
                grouping("cid", first("cid")),
                grouping("tradeName", first("tradeName")),
                grouping("beginPoint", first("beginPoint")),
                grouping("endPoint", first("endPoint")),
                grouping("total", first("total")),
                grouping("weight", first("weight")),
                grouping("price", first("price")),
                grouping("distance", first("distance")),
                grouping("tolls", first("tolls")),
                grouping("pType", first("pType")),
                grouping("mold", first("mold")),
                grouping("outUnitCode", first("outUnitCode")),
                grouping("outUnitName", first("outUnitName")),
                grouping("inUnitCode", first("inUnitCode")),
                grouping("inUnitName", first("inUnitName")),
                grouping("outBizContractCode", first("outBizContractCode")),
                grouping("outBizContractName", first("outBizContractName")),
                grouping("inBizContractCode", first("inBizContractCode")),
                grouping("inBizContractName", first("inBizContractName")),
                grouping("outVariety", first("outVariety")),
                grouping("inVariety", first("inVariety")),
                grouping("outDefaultDownUnit", first("outDefaultDownUnit")),
                grouping("outSubName", first("outSubName")),
                grouping("inDefaultDownUnit", first("inDefaultDownUnit")),
                grouping("inSubName", first("inSubName")),
                grouping("outDefaultArea", first("outDefaultArea")),
                grouping("outAreaName", first("outAreaName")),
                grouping("inDefaultArea", first("inDefaultArea")),
                grouping("inAreaName", first("inAreaName")),
                grouping("outMin", first("outMin")),
                grouping("outMax", first("outMax")),
                grouping("inMin", first("inMin")),
                grouping("inMax", first("inMax")),
                grouping("status", first("status")),
                grouping("delNum", first("delNum")),
                grouping("orderNum0", first("orderNum0")),
                grouping("orderNum1", first("orderNum1")),
                grouping("orderNum2", first("orderNum2")),
                grouping("orderNum3", first("orderNum3")),
                grouping("outGrossWeight", first("outGrossWeight")),
                grouping("outTareWeight", first("outTareWeight")),
                grouping("inGrossWeight", first("inGrossWeight")),
                grouping("inTareWeight", first("inTareWeight")),
                grouping("carNum", first("carNum")),
                grouping("driverId", first("driverId")),
                grouping("isPay", first("isPay")),
                grouping("payCid", first("payCid")),
                grouping("groupNo", first("groupNo")),
                grouping("isWeChat", first("isWeChat")),
                grouping("share", first("share")),
                grouping("shareCid", first("shareCid")),
                grouping("customerUser", first("customerUser")),
                grouping("feesOut", first("feesOut")),
                grouping("outUnitPayId", first("outUnitPayId")),
                grouping("feesIn", first("feesIn")),
                grouping("inUnitPayId", first("inUnitPayId")),
                grouping("fees1Out", first("fees1Out")),
                grouping("fees1In", first("fees1In")),
                grouping("fees2Out", first("fees2Out")),
                grouping("fees2In", first("fees2In")),
                grouping("fees3", first("fees3")),
                grouping("fee5Out", first("fee5Out")),
                grouping("fee5In", first("fee5In")),
                grouping("fee5OutId", first("fee5OutId")),
                grouping("fee5InId", first("fee5InId")),
                grouping("fee5OutType", first("fee5OutType")),
                grouping("fee5InType", first("fee5InType")),
                grouping("longitude", first("longitude")),
                grouping("latitude", first("latitude")),
                grouping("spell", first("spell")),
                grouping("place", first("place")),
                grouping("outTradingUnit", first("outTradingUnit")),
                grouping("outTradingUnitName", first("outTradingUnitName")),
                grouping("inTradingUnit", first("inTradingUnit")),
                grouping("inTradingUnitName", first("inTradingUnitName")),
                grouping("outCusDesc", first("outCusDesc")),
                grouping("inCusDesc", first("inCusDesc")),
                grouping("partSurplusNum", first("partSurplusNum")),
                grouping("createTime", first("createTime")),
                grouping("updateTime", first("updateTime")),
                grouping("mold2", first("mold2")),
                grouping("beginDistrictCode", first("beginDistrictCode")),
                grouping("endDistrictCode", first("endDistrictCode")),
                grouping("outVarietyCode", first("outVarietyCode")),
                grouping("inNetWeightOne", first("inNetWeightOne")),
                grouping("loadPound", first("loadPound")),
                grouping("loadTime", first("loadTime")),
                grouping("remark1", first("remark1")),
                grouping("remark2", first("remark2")),
                grouping("remark3", first("remark3")),
                grouping("remark4", first("remark4")),
                grouping("shareCid012",
                        Projection.expression(
                                "$first",
                                new BasicDBObject("$cond", Arrays.asList(
                                        new BasicDBObject("$eq", Arrays.asList("$shareCid", null)), "0",
                                        new BasicDBObject("$cond", Arrays.asList(
                                                new BasicDBObject("$gt", Arrays.asList("$shareCid", "")),
                                                new BasicDBObject("$cond", Arrays.asList(new BasicDBObject("$eq", Arrays.asList("$shareCid", cid)), "2", "1")),
                                                "0"
                                        ))
                                ))
                        )
                )
        );
        pipeline.sort(Sort.descending("createTime"));

        Projection cidProjection = Projection.projection(
                "cid",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$cid").append("to", "objectId")
                )
        );
        Projection shareCidProjection = Projection.projection(
                "shareCid",
                Projection.expression(
                        "$convert",
                        new BasicDBObject("input", "$shareCid").append("to", "objectId").append("onError", "").append("onNull", "")
                )
        );
        pipeline.project(
                Projection.projection("gid"),
                Projection.projection("cid"),
                Projection.projection("tradeName"),
                Projection.projection("beginPoint"),
                Projection.projection("endPoint"),
                Projection.projection("total"),
                Projection.projection("weight"),
                Projection.projection("price"),
                Projection.projection("distance"),
                Projection.projection("tolls"),
                Projection.projection("pType"),
                Projection.projection("mold"),
                Projection.projection("outUnitCode"),
                Projection.projection("outUnitName"),
                Projection.projection("inUnitCode"),
                Projection.projection("inUnitName"),
                Projection.projection("outBizContractCode"),
                Projection.projection("outBizContractName"),
                Projection.projection("inBizContractCode"),
                Projection.projection("inBizContractName"),
                Projection.projection("outVariety"),
                Projection.projection("inVariety"),
                Projection.projection("outDefaultDownUnit"),
                Projection.projection("outSubName"),
                Projection.projection("inDefaultDownUnit"),
                Projection.projection("inSubName"),
                Projection.projection("outDefaultArea"),
                Projection.projection("outAreaName"),
                Projection.projection("inDefaultArea"),
                Projection.projection("inAreaName"),
                Projection.projection("outMin"),
                Projection.projection("outMax"),
                Projection.projection("inMin"),
                Projection.projection("inMax"),
                Projection.projection("status"),
                Projection.projection("delNum"),
                Projection.projection("orderNum0"),
                Projection.projection("orderNum1"),
                Projection.projection("orderNum2"),
                Projection.projection("orderNum3"),
                Projection.projection("outGrossWeight"),
                Projection.projection("outTareWeight"),
                Projection.projection("inGrossWeight"),
                Projection.projection("inTareWeight"),
                Projection.projection("carNum"),
                Projection.projection("driverId"),
                Projection.projection("isPay"),
                Projection.projection("payCid"),
                Projection.projection("groupNo"),
                Projection.projection("isWeChat"),
                Projection.projection("share"),
                Projection.projection("feesOut"),
                Projection.projection("outUnitPayId"),
                Projection.projection("feesIn"),
                Projection.projection("inUnitPayId"),
                Projection.projection("fees1Out"),
                Projection.projection("fees1In"),
                Projection.projection("fees2Out"),
                Projection.projection("fees2In"),
                Projection.projection("fees3"),
                Projection.projection("fee5Out"),
                Projection.projection("fee5In"),
                Projection.projection("fee5OutId"),
                Projection.projection("fee5InId"),
                Projection.projection("fee5OutType"),
                Projection.projection("fee5InType"),
                Projection.projection("longitude"),
                Projection.projection("latitude"),
                Projection.projection("spell"),
                Projection.projection("place"),
                Projection.projection("outTradingUnit"),
                Projection.projection("outTradingUnitName"),
                Projection.projection("inTradingUnit"),
                Projection.projection("inTradingUnitName"),
                Projection.projection("outCusDesc"),
                Projection.projection("inCusDesc"),
                Projection.projection("partSurplusNum"),
                Projection.projection("createTime"),
                Projection.projection("updateTime"),
                Projection.projection("shareCid012"),
                Projection.projection("mold2"),
                Projection.projection("beginDistrictCode"),
                Projection.projection("endDistrictCode"),
                cidProjection,
                shareCidProjection,
                Projection.projection("outVarietyCode"),
                Projection.projection("inNetWeightOne"),
                Projection.projection("loadPound"),
                Projection.projection("loadTime"),
                Projection.projection("remark1"),
                Projection.projection("remark2"),
                Projection.projection("remark3"),
                Projection.projection("remark4")
        );
        pipeline.lookup("t_customer", "cid", "_id", "cusUser");
        pipeline.lookup("t_customer", "shareCid", "_id", "shareCustomerUser");
        pipeline.lookup("sys_unit", "inDefaultDownUnit", "code", "inSubUnit");
        pipeline.skip((page - 1) * rows);
        pipeline.limit(rows);

        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Iterator<GoodsPipeline> iterator = pipeline.aggregate(GoodsPipeline.class, aggregationOptions);

        return iterator;
    }

    private Boolean[] addAppType0_4(String cid, GoodsPipeline goods, SysSwitch sysSwitch, SysUnit inSubUnit) { //0-未接单类型
        Boolean[] appType = new Boolean[]{true, true, true, false, true, false, false, false, true, false};
        /*
         * 数组索引
         * 8-是否可以修改运往地
         * 9-是否需要录入对方净重
         */
        if (StrUtil.isNotEmpty(goods.getShareCid()) || goods.getpType() == 5) appType[0] = false;
        //友商也可以删除订单
        if (StrUtil.isNotEmpty(goods.getShareCid()) && !cid.equals(goods.getShareCid())) appType[1] = false;

        if ((cid.equals(goods.getCid()) && StrUtil.isNotEmpty(goods.getShareCid())) || (new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000))
            appType[2] = false;
        if (appType[2] && sysSwitch != null && StrUtil.isNotEmpty(sysSwitch.getShare()) && sysSwitch.getShare() == 1)
            appType[2] = false;

        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (StrUtil.isNotEmpty(goods.getShareCid()) || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[4] = false;
        if (cid.equals(goods.getShareCid()) && goods.getTotal() == goods.getDelNum() + goods.getOrderNum0())
            appType[5] = true;
        if (goods.getStatus() == 0) appType[6] = true;
        if (goods.getStatus() == 1) appType[7] = true;
        if (inSubUnit != null && StrUtil.isNotEmpty(inSubUnit.getInNeedNetWeight()) && inSubUnit.getInNeedNetWeight() == 1)
            appType[9] = true;
        return appType;
    }

    private Boolean[] addAppType1_4(String cid, GoodsPipeline goods, SysSwitch sysSwitch, SysUnit inSubUnit) { //1-接单未完成类型
        Boolean[] appType = new Boolean[]{false, false, false, false, false, false, false, false, false, false};
        //有可使用订单，按addAppType0处理
        if (goods.getOrderNum0() > 0) return addAppType0_4(cid, goods, sysSwitch, inSubUnit);

        //没有可使用订单
        if (goods.getMold() != 1 && goods.getOrderNum1() > goods.getOrderNum3() && StrUtil.isEmpty(goods.getShareCid())) {
            appType[0] = true;
        }
        //友商也可以删除订单
        if (goods.getMold() != 1 && goods.getOrderNum1() > goods.getOrderNum3() && (StrUtil.isEmpty(goods.getShareCid()) || cid.equals(goods.getShareCid())))
            appType[1] = true;

        if (goods.getpType() == 5) appType[0] = false;
        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (goods.getStatus() == 0) appType[6] = true;
        if (goods.getStatus() == 1) appType[7] = true;
        if (goods.getTotal() != goods.getOrderNum2() + goods.getTotal()) appType[8] = true;
        if (inSubUnit != null && StrUtil.isNotEmpty(inSubUnit.getInNeedNetWeight()) && inSubUnit.getInNeedNetWeight() == 1)
            appType[9] = true;
        return appType;
    }

    private Boolean[] addAppType2_4(String cid, GoodsPipeline goods) { //2-订单完成类型
        Boolean[] appType = new Boolean[]{false, false, false, true, false, false, false, false, false, false};
        if (cid.equals(goods.getShareCid())) appType[3] = true;

        return appType;
    }

    /**
     * 删除货运信息，达到批量废除所有订单 接口
     * 1.货运信息下没有被司机接的单直接废除，
     * 2.货运信息下已经被司机接单的要推送给司机，司机同意才能废除。
     */
    @Override
    public ServerResponse<String> delGoods(String gid, String[] oids) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        if (StrUtil.isNotEmpty(goods.getShareGid())) return ServerResponse.createError("货运信息属于友商分享而来，无废除权限");
        if (goods.getShare() == 2) return ServerResponse.createError("货运信息下订单全部分享给友商，请先回收在废除");
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        if (StrUtil.isNotEmpty(oids)) oQuery.filter("oid in", oids);
        oQuery.filter("locked", false);//司机接单支付的过程不可废除
        oQuery.filter("delete", false);//已经废除的订单不用再废除了
        oQuery.filter("share", 0);//分享给友商的订单，不直接废除，回收后再废除

        List<Order> orderList = orderService.list(oQuery);
        if (ObjectUtil.isEmpty(orderList) || orderList.size() <= 0)
            return ServerResponse.createSuccess("货运信息下无订单可废除");

        int delNum = 0;
        List<String> pushOids = new ArrayList<>();
        List<String> carNums = new ArrayList<>();  //需要司机统一撤单的 司机的车牌号
        List<String> delOids = new ArrayList<>();
        List<Order> delOrders = new ArrayList<>();
        for (Order o : orderList) {
            if (StrUtil.isNotEmpty(o.getIsCheck()) && o.getIsCheck() == 0) continue;     //已经处于废除审核的订单不用再提交废除了
            if (StrUtil.isEmpty(o.getCarNum())) {    //司机未接的订单
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                continue;
            }

            OrderTaking ot = orderTakingService.get("oid", o.getOid());
            if (ot.getFinishTag() > 0) continue;    //订单状态 为 运输中或已完成 ，不可以删除
            //订单有司机车牌号，但是司机还没有接受的，直接删除
            if (ot.getFinishTag() == 0 && ot.getDriverIsAgree() == 0) {
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                continue;
            }
            //车辆已经进场的订单，不能删除
            if ((StrUtil.isNotEmpty(o.getOutChecking()) && o.getOutChecking() > 0) || (StrUtil.isNotEmpty(o.getInChecking()) && o.getInChecking() > 0))
                continue;
            //司机接过的订单，不在运输/未进场 的订单，加入到推送订单编号集合中。
            pushOids.add(o.getOid());
            carNums.add(o.getCarNum());
        }

        if (delOids.size() > 0) {
            boolean isUpOrder;
            Query<SysSwitch> query = sysSwitchService.createQuery();
            query.filter("code", 0); //客商
            query.filter("type", 4); //订单退款
            SysSwitch sysSwitch = sysSwitchService.get(query);
            if (StrUtil.isEmpty(sysSwitch) || (StrUtil.isEmpty(sysSwitch.getCheck()) || sysSwitch.getCheck() == 0)) {  //废除订单，退款需要审核
                isUpOrder = delGoodsRefundPay(delOids, delOrders, false, goods.getCid(), goods);
            } else {     //废除订单，退款不需要审核
                isUpOrder = delGoodsRefundPay(delOids, delOrders, true, goods.getCid(), goods);
            }
            if (!isUpOrder) return ServerResponse.createError("稍后重试");
        }
        if (pushOids.size() > 0) {
            if (goods.getIsWeChat() == 0) { //为0 是给司机app下单的，废除时会推送信息给司机app请求废除
                //推送请求废除的消息给司机，由司机执行废除订单
                orderRecordService.deleteOrder(pushOids);
            } else {
                Query<OrderTaking> otQuery = orderTakingService.createQuery();
                otQuery.filter("oid in", pushOids.toArray());
                UpdateOperations<OrderTaking> updateOperations = orderTakingService.createUpdateOperations();
                updateOperations.set("delete", 0);    //订单废除标记  0：订单请求废除
                orderTakingService.update(otQuery, updateOperations);
            }
        }

        int stillNum;
        if (StrUtil.isNotEmpty(oids)) {
            stillNum = oids.length - delNum;
        } else {
            stillNum = goods.getTotal() - (goods.getDelNum() == null ? 0 : goods.getDelNum()) - delNum;
        }
        //修改货运信息
        UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
        updateOperations.set("delNum", (goods.getDelNum() == null ? 0 : goods.getDelNum()) + delNum);
        this.update(this.createQuery().filter("_id", goods.getObjectId()), updateOperations);
        return ServerResponse.createSuccess("货运信息下未运输的订单废除成功,共废除" + delNum + "车,剩余" + stillNum + "车未能（或已）废除," + "其中" + pushOids.size() + "车需要司机同意废除,司机们的车牌号为：" + carNums);
    }

    @Override
    public ServerResponse<String> delGoods2(String gid, String[] oids) {
        String cid = ShiroUtils.getUserId();
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        if (cid.equals(goods.getShareCid())) return ServerResponse.createError("货运信息属于友商分享而来，无废除权限");
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("货运信息已分享给友商，请先回收在废除");
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        if (StrUtil.isNotEmpty(oids)) oQuery.filter("oid in", oids);
        oQuery.filter("locked", false);//司机接单支付的过程不可废除
        oQuery.filter("delete", false);//已经废除的订单不用再废除了
        oQuery.filter("share", 0);//分享给友商的订单，不直接废除，回收后再废除

        List<Order> orderList = orderService.list(oQuery);
        if (ObjectUtil.isEmpty(orderList) || orderList.size() <= 0)
            return ServerResponse.createSuccess("货运信息下无订单可废除");

        int delNum = 0;
        int orderNum0 = 0;
        int orderNum1 = 0;
        List<String> pushOids = new ArrayList<>();
        List<String> carNums = new ArrayList<>();  //需要司机统一撤单的 司机的车牌号
        List<String> delOids = new ArrayList<>();
        List<Order> delOrders = new ArrayList<>();
        List<String> dvrIds = new ArrayList<>();
        for (Order o : orderList) {
            if (StrUtil.isNotEmpty(o.getIsCheck()) && o.getIsCheck() == 0) continue;     //已经处于废除审核的订单不用再提交废除了
            if (StrUtil.isEmpty(o.getCarNum())) {    //司机未接的订单
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                orderNum0++;
                continue;
            }

            OrderTaking ot = orderTakingService.get("oid", o.getOid());
            if (ot.getFinishTag() > 0) continue;    //订单状态 为 运输中或已完成 ，不可以删除
            //订单有司机车牌号，但是司机还没有接受的，直接删除
            if (ot.getFinishTag() == 0 && ot.getDriverIsAgree() == 0) {
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                orderNum1++;
                dvrIds.add(ot.getDid());
                continue;
            }
            //车辆已经进场的订单，不能删除
            if ((StrUtil.isNotEmpty(o.getOutChecking()) && o.getOutChecking() > 0) || (StrUtil.isNotEmpty(o.getInChecking()) && o.getInChecking() > 0))
                continue;
            //司机接过的订单，不在运输/未进场 的订单，加入到推送订单编号集合中。
            pushOids.add(o.getOid());
            carNums.add(o.getCarNum());
        }

        if (delOids.size() > 0) {
            boolean isUpOrder;
            Query<SysSwitch> query = sysSwitchService.createQuery();
            query.filter("code", 0); //客商
            query.filter("type", 4); //订单退款
            SysSwitch sysSwitch = sysSwitchService.get(query);
            if (StrUtil.isEmpty(sysSwitch) || (StrUtil.isEmpty(sysSwitch.getCheck()) || sysSwitch.getCheck() == 0)) {  //废除订单，退款需要审核
                isUpOrder = delGoodsRefundPay(delOids, delOrders, false, goods.getCid(), goods);
            } else {     //废除订单，退款不需要审核
                isUpOrder = delGoodsRefundPay(delOids, delOrders, true, goods.getCid(), goods);
            }
            if (!isUpOrder) return ServerResponse.createError("稍后重试");
        }
        if (pushOids.size() > 0) {
            if (goods.getIsWeChat() == 0) { //为0 是给司机app下单的，废除时会推送信息给司机app请求废除
                //推送请求废除的消息给司机，由司机执行废除订单
                orderRecordService.deleteOrder(pushOids);
            } else {
                Query<OrderTaking> otQuery = orderTakingService.createQuery();
                otQuery.filter("oid in", pushOids.toArray());
                UpdateOperations<OrderTaking> updateOperations = orderTakingService.createUpdateOperations();
                updateOperations.set("delete", 0);    //订单废除标记  0：订单请求废除
                orderTakingService.update(otQuery, updateOperations);
            }
        }

        int stillNum;
        if (StrUtil.isNotEmpty(oids)) {
            stillNum = oids.length - delNum;
        } else {
            stillNum = goods.getTotal() - (goods.getDelNum() == null ? 0 : goods.getDelNum()) - delNum;
        }
        //修改货运信息
        Query<Goods> gQuery = this.createQuery();
        gQuery.filter("_id", goods.getObjectId());
        gQuery.filter("updateTime", goods.getUpdateTime());
        UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
        updateOperations.inc("delNum", delNum);
        updateOperations.inc("orderNum0", -orderNum0);
        updateOperations.inc("orderNum1", -orderNum1);
        this.update(gQuery, updateOperations);

        //修改司机是否有运输中订单字段haveOrder
        driverInfoService.updateHaveOrder(dvrIds, 0);
        return ServerResponse.createSuccess("货运信息下未运输的订单废除成功,共废除" + delNum + "车,剩余" + stillNum + "车未能（或已）废除," + "其中" + pushOids.size() + "车需要司机同意废除,司机们的车牌号为：" + carNums);
    }

    @Override
    public ServerResponse<String> delGoods3(String gid, String[] oids) {
        String cid = ShiroUtils.getUserId();
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        if (cid.equals(goods.getShareCid())) return ServerResponse.createError("货运信息属于友商分享而来，无废除权限");
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("货运信息已分享给友商，请先回收在废除");
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        if (StrUtil.isNotEmpty(oids)) oQuery.filter("oid in", oids);
        oQuery.filter("locked", false);//司机接单支付的过程不可废除
        oQuery.filter("delete", false);//已经废除的订单不用再废除了
        oQuery.filter("share", 0);//分享给友商的订单，不直接废除，回收后再废除

        List<Order> orderList = orderService.list(oQuery);
        if (ObjectUtil.isEmpty(orderList) || orderList.size() <= 0)
            return ServerResponse.createSuccess("货运信息下无订单可废除");

        int delNum = 0;
        int orderNum0 = 0;
        int orderNum1 = 0;
        List<String> pushOids = new ArrayList<>();
        List<String> delOids = new ArrayList<>();
        List<Order> delOrders = new ArrayList<>();
        List<ObjectId> dvrIds = new ArrayList<>();
        List<DriverInfo> driverInfos = new ArrayList<>();
        for (Order o : orderList) {
            if (StrUtil.isNotEmpty(o.getIsCheck()) && o.getIsCheck() == 0) continue;     //已经处于废除审核的订单不用再提交废除了
            if (StrUtil.isEmpty(o.getCarNum())) {    //司机未接的订单
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                orderNum0++;
                continue;
            }
            if (o.getTranStatus() > 1) continue;  //已入场车辆，已完成订单，已废除订单 不能废除

            //司机未进场订单，直接删除，并给司机添加通知信息pushInfo
            OrderTaking ot = orderTakingService.get("oid", o.getOid());
            DriverInfo driverInfo = driverInfoService.getByPK(ot.getDid());
            delOids.add(o.getOid());
            delOrders.add(o);
            delNum++;
            orderNum1++;
            dvrIds.add(new ObjectId(ot.getDid()));
            driverInfos.add(driverInfo);
            pushOids.add(o.getOid());
        }

        if (delOids.size() > 0) {
            boolean isUpOrder;
            Query<SysSwitch> query = sysSwitchService.createQuery();
            query.filter("code", 0); //客商
            query.filter("type", 4); //订单退款
            SysSwitch sysSwitch = sysSwitchService.get(query);
            if (StrUtil.isEmpty(sysSwitch) || (StrUtil.isEmpty(sysSwitch.getCheck()) || sysSwitch.getCheck() == 0)) {  //废除订单，退款需要审核
                isUpOrder = delGoodsRefundPay2(delOids, delOrders, false, goods.getCid(), goods, delNum, orderNum0, orderNum1, dvrIds);
            } else {     //废除订单，退款不需要审核
                isUpOrder = delGoodsRefundPay2(delOids, delOrders, true, goods.getCid(), goods, delNum, orderNum0, orderNum1, dvrIds);
            }
            if (!isUpOrder) return ServerResponse.createError("撤单失败");
        }
        if (pushOids.size() > 0)
            pushInfoService.addAndSendPushInfos("撤销订单", "客商已经废除订单", 6, driverInfos, pushOids);

        return ServerResponse.createSuccess("货运信息下未运输的订单废除成功,共废除" + delNum + "车");
    }

    @Override
    public ServerResponse<String> delGoods4(String gid, String[] oids) {
        String cid = ShiroUtils.getUserId();
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        //if (cid.equals(goods.getShareCid())) return ServerResponse.createError("货运信息属于友商分享而来，无废除权限");
        //if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("货运信息已分享给友商，请先回收在废除");
        if (StrUtil.isNotEmpty(goods.getShareCid()) && !cid.equals(goods.getShareCid()))
            return ServerResponse.createError("货运信息已分享给友商，请先回收在废除");
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        if (StrUtil.isNotEmpty(oids)) oQuery.filter("oid in", oids);
        oQuery.filter("locked", false);//司机接单支付的过程不可废除
        oQuery.filter("delete", false);//已经废除的订单不用再废除了
        oQuery.filter("share", 0);//分享给友商的订单，不直接废除，回收后再废除

        List<Order> orderList = orderService.list(oQuery);
        if (ObjectUtil.isEmpty(orderList) || orderList.size() <= 0)
            return ServerResponse.createSuccess("货运信息下无订单可废除");

        int delNum = 0;
        int orderNum0 = 0;
        int orderNum1 = 0;
        List<String> pushOids = new ArrayList<>();
        List<String> delOids = new ArrayList<>();
        List<Order> delOrders = new ArrayList<>();
        List<ObjectId> dvrIds = new ArrayList<>();
        List<DriverInfo> driverInfos = new ArrayList<>();
        for (Order o : orderList) {
            if (StrUtil.isNotEmpty(o.getIsCheck()) && o.getIsCheck() == 0) continue;     //已经处于废除审核的订单不用再提交废除了
            if (StrUtil.isEmpty(o.getCarNum())) {    //司机未接的订单
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                orderNum0++;
                continue;
            }
            if (o.getTranStatus() > 1) continue;  //已入场车辆，已完成订单，已废除订单 不能废除

            //司机未进场订单，直接删除，并给司机添加通知信息pushInfo
            OrderTaking ot = orderTakingService.get("oid", o.getOid());
            DriverInfo driverInfo = driverInfoService.getByPK(ot.getDid());
            delOids.add(o.getOid());
            delOrders.add(o);
            delNum++;
            orderNum1++;
            dvrIds.add(new ObjectId(ot.getDid()));
            driverInfos.add(driverInfo);
            pushOids.add(o.getOid());
        }

        if (delOids.size() > 0) {
            SysUnit[] sysUnits = searchSysUnitInGoods(goods);
            Query<SysSwitch> query = sysSwitchService.createQuery();
            query.filter("code", 3); //订单
            SysSwitch sysSwitch = sysSwitchService.get(query);
            boolean pfIsRefund = sysSwitch == null || StrUtil.isEmpty(sysSwitch.getIsRefund()) || sysSwitch.getIsRefund() == 0;
            boolean sysIsRefund = true; //true-表示撤单退款
            if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsDelOrderBackFee()) && sysUnits[0].getIsDelOrderBackFee() == 1)
                sysIsRefund = false;    //false-表示撤单不退款
            if (StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsDelOrderBackFee()) && sysUnits[2].getIsDelOrderBackFee() == 1)
                sysIsRefund = false;    //false-表示撤单不退款
            boolean isUpOrder = delGoodsRefundPay3(delOids, delOrders, pfIsRefund, pfIsRefund, sysUnits, sysIsRefund, sysIsRefund, goods, delNum, orderNum0, orderNum1, dvrIds);
            if (!isUpOrder) return ServerResponse.createError("撤单失败");
        }
        if (pushOids.size() > 0)
            pushInfoService.addAndSendPushInfos("撤销订单", "客商已经废除订单", 6, driverInfos, pushOids);

        return ServerResponse.createSuccess("货运信息下未运输的订单废除成功,共废除" + delNum + "车");
    }

    @Override
    public ServerResponse<String> delGoods5(String gid, String[] oids) {
        String cid = ShiroUtils.getUserId();
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        if (cid.equals(goods.getShareCid())) return ServerResponse.createError("货运信息属于友商分享而来，无废除权限");
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("货运信息已分享给友商，请先回收在废除");
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        if (StrUtil.isNotEmpty(oids)) oQuery.filter("oid in", oids);
        oQuery.filter("locked", false);//司机接单支付的过程不可废除
        oQuery.filter("delete", false);//已经废除的订单不用再废除了
        oQuery.filter("share", 0);//分享给友商的订单，不直接废除，回收后再废除

        List<Order> orderList = orderService.list(oQuery);
        if (ObjectUtil.isEmpty(orderList) || orderList.size() <= 0)
            return ServerResponse.createSuccess("货运信息下无订单可废除");

        int delNum = 0;
        int orderNum0 = 0;
        int orderNum1 = 0;
        List<String> pushOids = new ArrayList<>();
        List<String> delOids = new ArrayList<>();
        List<Order> delOrders = new ArrayList<>();
        List<ObjectId> dvrIds = new ArrayList<>();
        List<DriverInfo> driverInfos = new ArrayList<>();
        for (Order o : orderList) {
            if (StrUtil.isNotEmpty(o.getIsCheck()) && o.getIsCheck() == 0) continue;     //已经处于废除审核的订单不用再提交废除了
            if (StrUtil.isEmpty(o.getCarNum())) {    //司机未接的订单
                delOids.add(o.getOid());
                delOrders.add(o);
                delNum++;
                orderNum0++;
                continue;
            }
            if (o.getTranStatus() > 1) continue;  //已入场车辆，已完成订单，已废除订单 不能废除

            //司机未进场订单，直接删除，并给司机添加通知信息pushInfo
            OrderTaking ot = orderTakingService.get("oid", o.getOid());
            DriverInfo driverInfo = driverInfoService.getByPK(ot.getDid());
            delOids.add(o.getOid());
            delOrders.add(o);
            delNum++;
            orderNum1++;
            dvrIds.add(new ObjectId(ot.getDid()));
            driverInfos.add(driverInfo);
            pushOids.add(o.getOid());
        }

        if (delOids.size() > 0) {
            SysUnit[] sysUnits = searchSysUnitInGoods(goods);
            /*
            2024-05-24 修改，订单作废，平台费用退费开关 跟单位走
            Query<SysSwitch> query = sysSwitchService.createQuery();
            query.filter("code", 3); //订单
            SysSwitch sysSwitch = sysSwitchService.get(query);
            boolean pfIsRefund = sysSwitch == null || StrUtil.isEmpty(sysSwitch.getIsRefund()) || sysSwitch.getIsRefund() == 0;*/
            boolean sysIsRefund = true; //true-表示撤单退款
            if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsDelOrderBackFee()) && sysUnits[0].getIsDelOrderBackFee() == 1)
                sysIsRefund = false;    //false-表示撤单不退款
            if (StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsDelOrderBackFee()) && sysUnits[2].getIsDelOrderBackFee() == 1)
                sysIsRefund = false;    //false-表示撤单不退款
            boolean pfIsRefund = sysIsRefund;
            boolean isUpOrder = delGoodsRefundPay4(delOids, delOrders, pfIsRefund, pfIsRefund, sysUnits, sysIsRefund, sysIsRefund, goods, delNum, orderNum0, orderNum1, dvrIds);
            if (!isUpOrder) return ServerResponse.createError("撤单失败");
        }
        if (pushOids.size() > 0)
            pushInfoService.addAndSendPushInfos("撤销订单", "客商已经废除订单", 6, driverInfos, pushOids);

        return ServerResponse.createSuccess("货运信息下未运输的订单废除成功,共废除" + delNum + "车");
    }

    /**
     * 客商废除没有司机接单的空订单，后台开关为：直接退钱，不审核 时
     * 1.退单位代付的钱；
     * 2.退客商代付的钱；
     * 3.修改order delete。
     * 4.修改goods各车数
     * 5.driverInfo接单字段
     */
    @Transactional
    public boolean delGoodsRefundPay4(List<String> delOids, List<Order> delOrders, boolean pfIsRefundOut, boolean pfIsRefundIn, SysUnit[] sysUnits, boolean sysIsRefundOut, boolean sysIsRefundIn, Goods goods, int delNum, int orderNum0, int orderNum1, List<ObjectId> dvrIds) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();
        MongoCollection<Document> gCollection = this.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();
            //修改要废除的order delete为true
            Map<String, Object> oMap = new HashMap<>();
            oMap.put("updateTime", time.getTime());
            oMap.put("delete", true);
            oMap.put("delType", 0); //客商撤单
            oMap.put("isCheck", 3); // 无需审核
            oMap.put("tranStatus", 6);
            oMap.put("time6", time);//订单作废时间
            Map<String, Object> oUpMap = new HashMap<>();
            oUpMap.put("$set", oMap);
            UpdateResult upResult = orderCollection.updateMany(clientSession, in("oid", delOids), Document.parse(JSONObject.toJSONString(oUpMap)));
            if (upResult.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return false;
            }
            //删除orderTaking
            otCollection.deleteMany(clientSession, in("oid", delOids));

            //订单退款不需要审核，平台会配置撤单是否退费、收发货单位会配置撤单是否退费，
            List<Document> dvrAccountDocs = new ArrayList<>();
            List<Document> cusAccountDocs = new ArrayList<>();
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            List<Document> pfAccountDocs = new ArrayList<>();
            List<Document> thirdPartyAccountDocs = new ArrayList<>();
            for (Order order : delOrders) {
                //存在三方分账的情况时，不退费
                if ((StrUtil.isNotEmpty(order.getOutFee5()) && order.getOutFee5() > 0) || (StrUtil.isNotEmpty(order.getInFee5()) && order.getInFee5() > 0))
                    continue;
                int dvrFee = 0; //订单中需要退还司机的总金额
                //平台信息费 退回，同时平台账户金额核减
                if (pfIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer1())) { //平台收取发货单位的信息费，符合条件则退回
                    String outPayer1 = order.getOutPayer1();
                    int outFee0 = order.getOutFee0();
                    switch (order.getOutPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer1, new BigDecimal(outFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 12, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + outFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 13, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(outPayer1, null, 3, order.getOid(), outFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                if (pfIsRefundIn && StrUtil.isNotEmpty(order.getInPayer1())) { //平台收取收货单位的信息费，符合条件则退回
                    String inPayer1 = order.getInPayer1();
                    int inFee0 = order.getInFee0();
                    switch (order.getInPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer1, new BigDecimal(inFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + inFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(inPayer1, null, 3, order.getOid(), inFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                //单位收取的代扣费 退回，同时单位账户金额核减
                if (sysIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer2())) {   //发货单位收取的代扣费，符合条件则退回
                    String outPayer2 = order.getOutPayer2();
                    int outFee1 = order.getOutFee1();
                    int outFee2 = order.getOutFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer2, new BigDecimal(outFee1 + outFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + outFee1 + outFee2;
                            break;
                        default:
                            break;
                    }

                    if (outFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), null, 5, order.getOid(), -outFee1, time));
                    if (outFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), null, 5, order.getOid(), -outFee2, time));
                }
                if (sysIsRefundIn && StrUtil.isNotEmpty(order.getInPayer2())) {   //收货单位收取的代扣费，符合条件则退回
                    String inPayer2 = order.getInPayer2();
                    int inFee1 = order.getInFee1();
                    int inFee2 = order.getInFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer2, new BigDecimal(inFee1 + inFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + inFee1 + inFee2;
                            break;
                        default:
                            break;
                    }

                    if (inFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), null, 5, order.getOid(), -inFee1, time));
                    if (inFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), null, 5, order.getOid(), -inFee2, time));
                }
                //客商或友商收取的信息费和第三方费用 退回司机,同时客商账户和第三方账户金额核减
                if (StrUtil.isNotEmpty(order.getOutPayer3()) || StrUtil.isNotEmpty(order.getInPayer3())) {
                    //String did = order.getOutPayer3();
                    int cusFee = order.getOutFee3() + order.getOutFee4();
                    int outFee5 = order.getOutFee5();
                    int inFee5 = order.getInFee5();
                    //dvrAccountDocs.add(driverAccountService.createDriAccountDoc(did, null, new BigDecimal(dvrFee + cusFee + outFee5 + inFee5), 3, order.getOid(), time));
                    dvrFee = dvrFee + cusFee + outFee5 + inFee5;
                    if (cusFee > 0) {
                        String cid = goods.getShare() == 2 ? goods.getShareCid() : goods.getCid();
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(-cusFee), 9, order.getOid(), time));
                    }
                    if (outFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), null, -outFee5, 1, order.getOid(), time));
                    //                                                                                      (goods.getFee5OutId(), null, -outFee5, 1, order.getOid(), time));
                    if (inFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[1].getThirdPartyName(), null, -inFee5, 1, order.getOid(), time));
                    //                                                                                      (goods.getFee5InId(), null, -inFee5, 1, order.getOid(), time));
                }
                if (dvrFee > 0)
                    dvrAccountDocs.add(driverAccountService.createDriAccountDoc(order.getDid(), null, new BigDecimal(dvrFee), 3, order.getOid(), time));

            }
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
            if (thirdPartyAccountDocs.size() > 0) thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", delNum);
            param.put("orderNum0", -orderNum0);
            param.put("orderNum1", -orderNum1);
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(updMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", paramMap);
            driverCollection.updateMany(clientSession, in("_id", dvrIds), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("delGoodsRefundPay4" + e.getMessage());
            clientSession.abortTransaction();
            return false;
        } finally {
            clientSession.close();
        }
        return true;
    }

    /**
     * 客商废除没有司机接单的空订单，后台开关为：直接退钱，不审核 时
     * 1.退单位代付的钱；
     * 2.退客商代付的钱；
     * 3.修改order delete。
     * 4.修改goods各车数
     * 5.driverInfo接单字段
     */
    @Transactional
    public boolean delGoodsRefundPay3(List<String> delOids, List<Order> delOrders, boolean pfIsRefundOut, boolean pfIsRefundIn, SysUnit[] sysUnits, boolean sysIsRefundOut, boolean sysIsRefundIn, Goods goods, int delNum, int orderNum0, int orderNum1, List<ObjectId> dvrIds) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> thirdPartyCollection = thirdPartyAccountService.getCollection();
        MongoCollection<Document> gCollection = this.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();
            //修改要废除的order delete为true
            Map<String, Object> oMap = new HashMap<>();
            oMap.put("updateTime", time.getTime());
            oMap.put("delete", true);
            oMap.put("delType", 0); //客商撤单
            oMap.put("isCheck", 3); // 无需审核
            oMap.put("tranStatus", 6);
            oMap.put("time6", time);//订单作废时间
            Map<String, Object> oUpMap = new HashMap<>();
            oUpMap.put("$set", oMap);
            UpdateResult upResult = orderCollection.updateMany(clientSession, in("oid", delOids), Document.parse(JSONObject.toJSONString(oUpMap)));
            if (upResult.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return false;
            }
            //删除orderTaking
            otCollection.deleteMany(clientSession, in("oid", delOids));

            //订单退款不需要审核，平台会配置撤单是否退费、收发货单位会配置撤单是否退费，
            List<Document> dvrAccountDocs = new ArrayList<>();
            List<Document> cusAccountDocs = new ArrayList<>();
            List<Document> sysUnitAccountDocs = new ArrayList<>();
            List<Document> pfAccountDocs = new ArrayList<>();
            List<Document> thirdPartyAccountDocs = new ArrayList<>();
            for (Order order : delOrders) {
                //存在三方分账的情况时，不退费
                if ((StrUtil.isNotEmpty(order.getOutFee5()) && order.getOutFee5() > 0) || (StrUtil.isNotEmpty(order.getInFee5()) && order.getInFee5() > 0))
                    continue;

                int dvrFee = 0; //订单中需要退还司机的总金额
                //平台信息费 退回，同时平台账户金额核减
                if (pfIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer1())) { //平台收取发货单位的信息费，符合条件则退回
                    String outPayer1 = order.getOutPayer1();
                    int outFee0 = order.getOutFee0();
                    switch (order.getOutPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer1, new BigDecimal(outFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 12, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + outFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 13, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(outPayer1, null, 3, order.getOid(), outFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(outPayer1, new BigDecimal(-outFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                if (pfIsRefundIn && StrUtil.isNotEmpty(order.getInPayer1())) { //平台收取收货单位的信息费，符合条件则退回
                    String inPayer1 = order.getInPayer1();
                    int inFee0 = order.getInFee0();
                    switch (order.getInPayerType1()) {
                        case "CU":  //客商代付了 平台信息费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer1, new BigDecimal(inFee0), 5, order.getOid(), time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 平台信息费，则费用退回司机账户
                            dvrFee = dvrFee + inFee0;
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        case "SU":  //单位代付了 平台信息费，则费用退回单位账户
                            sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(inPayer1, null, 3, order.getOid(), inFee0, time));
                            pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(inPayer1, new BigDecimal(-inFee0), 11, order.getOid(), time));
                            break;
                        default:
                            break;
                    }
                }
                //单位收取的代扣费 退回，同时单位账户金额核减
                if (sysIsRefundOut && StrUtil.isNotEmpty(order.getOutPayer2())) {   //发货单位收取的代扣费，符合条件则退回
                    String outPayer2 = order.getOutPayer2();
                    int outFee1 = order.getOutFee1();
                    int outFee2 = order.getOutFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(outPayer2, new BigDecimal(outFee1 + outFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + outFee1 + outFee2;
                            break;
                        default:
                            break;
                    }

                    if (outFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[0].getObjectId().toHexString(), null, 5, order.getOid(), -outFee1, time));
                    if (outFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[1].getObjectId().toHexString(), null, 5, order.getOid(), -outFee2, time));
                }
                if (sysIsRefundIn && StrUtil.isNotEmpty(order.getInPayer2())) {   //收货单位收取的代扣费，符合条件则退回
                    String inPayer2 = order.getInPayer2();
                    int inFee1 = order.getInFee1();
                    int inFee2 = order.getInFee2();
                    switch (order.getOutPayerType2()) {
                        case "CU":  //客商代付了 单位代扣费，则费用退回客商账户
                            cusAccountDocs.add(customerAccountService.createCusAccountDoc(inPayer2, new BigDecimal(inFee1 + inFee2), 5, order.getOid(), time));
                            break;
                        case "DU":  //司机支付了 单位代扣费，则费用退回司机账户
                            dvrFee = dvrFee + inFee1 + inFee2;
                            break;
                        default:
                            break;
                    }

                    if (inFee1 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[2].getObjectId().toHexString(), null, 5, order.getOid(), -inFee1, time));
                    if (inFee2 > 0)
                        sysUnitAccountDocs.add(sysUnitAccountService.createSysUnitAccountDocs(sysUnits[3].getObjectId().toHexString(), null, 5, order.getOid(), -inFee2, time));
                }
                //客商或友商收取的信息费和第三方费用 退回司机,同时客商账户和第三方账户金额核减
                if (StrUtil.isNotEmpty(order.getOutPayer3()) || StrUtil.isNotEmpty(order.getInPayer3())) {
                    //String did = order.getOutPayer3();
                    int cusFee = order.getOutFee3() + order.getOutFee4();
                    int outFee5 = order.getOutFee5();
                    int inFee5 = order.getInFee5();
                    //dvrAccountDocs.add(driverAccountService.createDriAccountDoc(did, null, new BigDecimal(dvrFee + cusFee + outFee5 + inFee5), 3, order.getOid(), time));
                    dvrFee = dvrFee + cusFee + outFee5 + inFee5;
                    if (cusFee > 0) {
                        String cid = goods.getShare() == 2 ? goods.getShareCid() : goods.getCid();
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(-cusFee), 9, order.getOid(), time));
                    }
                    if (outFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getOutUnitCode(), goods.getFee5OutId(), sysUnits[0].getThirdPartyName(), null, -outFee5, 1, order.getOid(), time));
                    //                                                                                          (goods.getFee5OutId(), null, -outFee5, 1, order.getOid(), time));
                    if (inFee5 > 0)
                        thirdPartyAccountDocs.add(thirdPartyAccountService.createAccountDoc(goods.getInUnitCode(), goods.getFee5InId(), sysUnits[2].getThirdPartyName(), null, -inFee5, 1, order.getOid(), time));
                    //                                                                                          (goods.getFee5InId(), null, -inFee5, 1, order.getOid(), time));
                }
                if (dvrFee > 0)
                    dvrAccountDocs.add(driverAccountService.createDriAccountDoc(order.getDid(), null, new BigDecimal(dvrFee), 3, order.getOid(), time));

            }
            if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
            if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
            if (dvrAccountDocs.size() > 0) dAccountCollection.insertMany(clientSession, dvrAccountDocs);
            if (thirdPartyAccountDocs.size() > 0) thirdPartyCollection.insertMany(clientSession, thirdPartyAccountDocs);

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", delNum);
            param.put("orderNum0", -orderNum0);
            param.put("orderNum1", -orderNum1);
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(updMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", paramMap);
            driverCollection.updateMany(clientSession, in("_id", dvrIds), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("delGoodsRefundPay3" + e.getMessage());
            clientSession.abortTransaction();
            return false;
        } finally {
            clientSession.close();
        }
        return true;
    }

    /**
     * 客商废除没有司机接单的空订单，后台开关为：直接退钱，不审核 时
     * 1.退单位代付的钱；
     * 2.退客商代付的钱；
     * 3.修改order delete。
     * 4.修改goods各车数
     * 5.driverInfo接单字段
     */
    @Transactional
    public boolean delGoodsRefundPay2(List<String> delOids, List<Order> delOrders, boolean switchCheck, String cid, Goods goods, int delNum, int orderNum0, int orderNum1, List<ObjectId> dvrIds) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();
        MongoCollection<Document> gCollection = this.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();
            //修改要废除的order delete为true
            Map<String, Object> oMap = new HashMap<>();
            oMap.put("updateTime", time.getTime());
            oMap.put("delete", true);
            oMap.put("delType", 0); //客商撤单
            if (switchCheck) {
                oMap.put("isCheck", 3); // 无需审核
            } else {
                oMap.put("isCheck", 0); // 未审核
            }
            oMap.put("tranStatus", 6);
            Map<String, Object> oUpMap = new HashMap<>();
            oUpMap.put("$set", oMap);
            UpdateResult upResult = orderCollection.updateMany(clientSession, in("oid", delOids), Document.parse(JSONObject.toJSONString(oUpMap)));
            if (upResult.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return false;
            }
            //删除orderTaking
            otCollection.deleteMany(clientSession, in("oid", delOids));

            if (switchCheck) {  //不需要审核的情况下，直接退钱
                int[] types = new int[]{3, 5};  //3-单位代付退回，5-单位代扣退出
                SysUnit[] sysUnits = searchSysUnitInGoods(goods);
                List<Document> cusAccountDocs = new ArrayList<>();
                List<Document> sysUnitAccountDocs = new ArrayList<>();
                List<Document> pfAccountDocs = new ArrayList<>();
                for (Order order : delOrders) {
                    int cusFees = order.getFees() + order.getFees1Out() + order.getFees2Out() + order.getFees1In() + order.getFees2In();
                    if (cusFees > 0)
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(cusFees), 5, order.getOid(), time));

                    String[] outUids = new String[3];
                    if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
                        outUids = new String[]{order.getPayerIdOut(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
                    String[] inUids = new String[3];
                    if (StrUtil.isNotEmpty(goods.getInUnitCode()))
                        inUids = new String[]{order.getPayerIdIn(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};
                    String[] outUserIds = new String[]{order.getPayerIdOut(), order.getPayerId1Out(), order.getPayerId2Out()};
                    String[] inUserIds = new String[]{order.getPayerIdIn(), order.getPayerId1In(), order.getPayerId2In()};
                    int[] outFees = new int[]{order.getFeesOut(), -order.getFees1Out(), -order.getFees2Out()};
                    int[] inFees = new int[]{order.getFeesIn(), -order.getFees1In(), -order.getFees2In()};
                    sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, order.getOid(), outFees, time);
                    sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, order.getOid(), inFees, time);

                    if (order.getFees() > 0)
                        pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerId(), new BigDecimal(-order.getFees()), 11, order.getOid(), time));
                    if (order.getFeesOut() > 0)
                        pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-order.getFeesOut()), 12, order.getOid(), time));
                    if (order.getFeesIn() > 0)
                        pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-order.getFeesIn()), 12, order.getOid(), time));
                }
                if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
                if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);

                //司机接单为入场的，废除是要退回司机账户钱
            }

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("delNum", delNum);
            param.put("orderNum0", -orderNum0);
            param.put("orderNum1", -orderNum1);
            Map<String, Object> updMap = new HashMap<>();
            updMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(updMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("haveOrder", 0);
            Map<String, Object> dvrUpMap = new HashMap<>();
            dvrUpMap.put("$set", paramMap);
            driverCollection.updateMany(clientSession, in("_id", dvrIds), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            logger.error("delGoodsRefundPay2" + e.getMessage());
            clientSession.abortTransaction();
            return false;
        } finally {
            clientSession.close();
        }
        return true;
    }

    /**
     * 客商废除没有司机接单的空订单，后台开关为：直接退钱，不审核 时
     * 1.退单位代付的钱；
     * 2.退客商代付的钱；
     * 3.修改order delete。
     */
    @Transactional
    public boolean delGoodsRefundPay(List<String> delOids, List<Order> delOrders, boolean switchCheck, String cid, Goods goods) {
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        MongoCollection<Document> otCollection = orderTakingService.getCollection();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> unitAccountCollection = sysUnitAccountService.getCollection();
        MongoCollection<Document> pfAccountCollection = platFormAccountService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();
            //修改要废除的order delete为true
            Map<String, Object> oMap = new HashMap<>();
            oMap.put("updateTime", time.getTime());
            oMap.put("delete", true);
            oMap.put("delType", 0); //客商撤单
            if (switchCheck) {
                oMap.put("isCheck", 3); // 无需审核
            } else {
                oMap.put("isCheck", 0); // 未审核
            }
            Map<String, Object> oUpMap = new HashMap<>();
            oUpMap.put("$set", oMap);
            UpdateResult upResult = orderCollection.updateMany(clientSession, in("oid", delOids), Document.parse(JSONObject.toJSONString(oUpMap)));
            if (upResult.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return false;
            }
            //删除orderTaking
            otCollection.deleteMany(clientSession, in("oid", delOids));

            if (switchCheck) {  //不需要审核的情况下，直接退钱
                int[] types = new int[]{3, 5};  //3-单位代付退回，5-单位代扣退出
                SysUnit[] sysUnits = searchSysUnitInGoods(goods);
                List<Document> cusAccountDocs = new ArrayList<>();
                List<Document> sysUnitAccountDocs = new ArrayList<>();
                List<Document> pfAccountDocs = new ArrayList<>();
                for (Order order : delOrders) {
                    int cusFees = order.getFees() + order.getFees1Out() + order.getFees2Out() + order.getFees1In() + order.getFees2In();
                    if (cusFees > 0)
                        cusAccountDocs.add(customerAccountService.createCusAccountDoc(cid, new BigDecimal(cusFees), 5, order.getOid(), time));

                    String[] outUids = new String[3];
                    if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
                        outUids = new String[]{order.getPayerIdOut(), sysUnits[0].getObjectId().toString(), sysUnits[1].getObjectId().toString()};
                    String[] inUids = new String[3];
                    if (StrUtil.isNotEmpty(goods.getInUnitCode()))
                        inUids = new String[]{order.getPayerIdIn(), sysUnits[2].getObjectId().toString(), sysUnits[3].getObjectId().toString()};
                    String[] outUserIds = new String[]{order.getPayerIdOut(), order.getPayerId1Out(), order.getPayerId2Out()};
                    String[] inUserIds = new String[]{order.getPayerIdIn(), order.getPayerId1In(), order.getPayerId2In()};
                    int[] outFees = new int[]{order.getFeesOut(), -order.getFees1Out(), -order.getFees2Out()};
                    int[] inFees = new int[]{order.getFeesIn(), -order.getFees1In(), -order.getFees2In()};
                    sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, outUids, outUserIds, types, order.getOid(), outFees, time);
                    sysUnitAccountService.createSysUnitAccountDocs(sysUnitAccountDocs, inUids, inUserIds, types, order.getOid(), inFees, time);

                    if (order.getFees() > 0)
                        pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerId(), new BigDecimal(-order.getFees()), 11, order.getOid(), time));
                    if (order.getFeesOut() > 0)
                        pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdOut(), new BigDecimal(-order.getFeesOut()), 12, order.getOid(), time));
                    if (order.getFeesIn() > 0)
                        pfAccountDocs.add(platFormAccountService.createPlatFormAccountDoc(order.getPayerIdIn(), new BigDecimal(-order.getFeesIn()), 12, order.getOid(), time));
                }
                if (cusAccountDocs.size() > 0) cusAccountCollection.insertMany(clientSession, cusAccountDocs);
                if (sysUnitAccountDocs.size() > 0) unitAccountCollection.insertMany(clientSession, sysUnitAccountDocs);
                if (pfAccountDocs.size() > 0) pfAccountCollection.insertMany(clientSession, pfAccountDocs);
            }

            clientSession.commitTransaction();
        } catch (Exception e) {
            e.printStackTrace();
            clientSession.abortTransaction();
            return false;
        } finally {
            clientSession.close();
        }
        return true;
    }

    @Override
    public ServerResponse<Object> addGoodsPL(Goods goods, String longitude, String latitude, String groupNo, Integer isPay) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);

        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.Fa_Bu_Add_Goods.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");

        } else {

            try {
                goods.setCid(cid);
                goods.setStatus(0);
                goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

                //todo:收发货物流模式下，收发货的商品必须保持一致
                /*if (goods.getMold() == 2 && !goods.getOutVariety().equals(goods.getInVariety())) {
                    return ServerResponse.createError("收发货商品不一致，不符合运输规则!");
                }*/
                //2022年1月19号，可能收发单位对物料名称定义不同

                //todo:校验司机组编号是否正确
                DriverGroup dg = driverGroupService.getByPK(groupNo);
                if (ObjectUtil.isEmpty(dg) || ObjectUtil.isEmpty(dg.getDvrId()) || dg.getDvrId().length <= 0) {
                    return ServerResponse.createError("指定司机组有误，请重新选择司机组");
                }

                //todo:过滤司机组中黑名单的司机
                Query<DriverPool> dpQuery = driverPoolService.createQuery();
                dpQuery.filter("cid", cid);
                dpQuery.filter("did in", dg.getDvrId());
                dpQuery.criteria("whiteOrBlack").notEqual(0);
                List<DriverPool> driverPools = dpQuery.find().toList();
                List<ObjectId> objectIds = new ArrayList<>();
                for (DriverPool dp : driverPools) {
                    objectIds.add(new ObjectId(dp.getDid()));
                }
                Query<DriverInfo> diQuery = driverInfoService.createQuery();
                diQuery.filter("objectId in ", objectIds);
                Integer[] state = {1, 4};//用戶状态 0：未审核不可用 1：通过审核 2：冻结 3:审核未通过 4：未审核但可用
                diQuery.filter("state in ", state);
                List<DriverInfo> dInfos = diQuery.find().toList();
                if (dInfos.size() <= 0) {
                    return ServerResponse.createError("指定司机组司机全未通过审核，请重新选择司机组");
                }

                //todo:校验参数不能为空
                String cMsg = checkGoodsParam(goods);
                if (!StrUtil.isEmpty(cMsg)) {
                    return ServerResponse.createError(cMsg);
                }

                //todo:排除反复添加同一条货运信息
                String oGid = checkGoods(cid, goods);
                if (!StrUtil.isEmpty(oGid)) {
                    return ServerResponse.createError("已存在货运信息");
                }

                //todo: 校验票号outMin 和 inMin
                String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
                if (StrUtil.isNotEmpty(billCodeStr)) {
                    return ServerResponse.createError(billCodeStr);
                }

                //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
                String totalStr = checkTotal(goods);
                if (!StrUtil.isEmpty(totalStr)) {
                    return ServerResponse.createError("车数大于企业可用票号数量");
                }

                //todo:查询将要添加的货运信息 企业要求代扣的情况
                Map<String, Object> payMap = sysUnitTotalFees(goods);
                if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg"))) {
                    return ServerResponse.createError((String) payMap.get("msg"));
                }
                //todo:客商选择代付的情况，判断客商账户余额
                BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee");
                BigDecimal fee = (BigDecimal) payMap.get("sysUnitFee");
                if (isPay != 0) {
                    goods.setFees3(0);  //客商代付时，客商需要收取的信息费设置为0 即 自己需要收取的钱不用从自己账户扣除
                    BigDecimal availableFee = customerAccountService.getAvailableFee(cid);
                    if (availableFee.compareTo(payFee) < 0) {
                        return ServerResponse.result(7, "账户金额不足，请先充值");
                    }
                } else {
                    fee = new BigDecimal(0);
                }

                SysUnit[] sysOutUnits = (SysUnit[]) payMap.get("sysOutUnits");
                SysUnit[] sysInUnits = (SysUnit[]) payMap.get("sysInUnits");
                int[] outFees = (int[]) payMap.get("outFees");
                int[] inFees = (int[]) payMap.get("inFees");
                goods.setTotal(goods.getTotal() == null ? 1 : goods.getTotal() == 0 ? 1 : goods.getTotal());
                //todo:请求企业系统下单
                Map<String, String> msg = synOrderTooCompany(cid, goods, "", sysOutUnits[1], sysInUnits[1], false);
                if (ObjectUtil.isNotEmpty(msg)) {
                    //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
                    String outSynMsg = msg.get("outSynMsg");
                    String inSynMsg = msg.get("inSynMsg");
                    if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                        goods.setInMin(null);
                        addGoodsAndOrder(null, goods, true, 2, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
                    }
                    if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                        goods.setOutMin(null);
                        addGoodsAndOrder(null, goods, true, 2, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
                    }
                    return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
                }

                //todo:货运信息添加,订单号添加,并返回添加结果goods
                Goods result = addGoodsAndOrder(null, goods, false, 2, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
                if (result == null) {
                    return ServerResponse.createError("发布失败");
                }

                //todo:将发布的货运信息编号和选择的司机组中的司机添加到中间表
                List<String> dids = new ArrayList<>();
                for (DriverInfo di : dInfos) {
                    dids.add(di.getObjectId().toString());
                }
                GidAndDid gidAndDid = new GidAndDid();
                gidAndDid.setGid(result.getGid());
                gidAndDid.setDid(dids);
                gidAndDid.setCid(cid);
                gidAndDidService.save(gidAndDid);

                //todo:将信息推送给司机
                String mes = cUser.getMobile() + "发布" + result.getTradeName() + "从" + result.getBeginPoint() + "运输到" + result.getEndPoint();
                pushInfoService.addAndSendPushInfos("抢单", mes, 0, dInfos, null);//保存推送消息

                return ServerResponse.createSuccess("发布成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));

            } finally {
                lockedService.unLock(cid, SysConstants.LockType.Fa_Bu_Add_Goods.getType());
            }
        }
    }

    // 货运信息修改-1
    @Override
    public ServerResponse<List<String>> updateGoods(String gid, String outVariety, String inVariety) {
        return orderService.updateOrders(gid, null, outVariety, inVariety);
    }

    @Override
    public ServerResponse<List<String>> updateGoodsPlace(String gid, String spell, String place) {
        return orderService.updateOrdersPlace(gid, null, spell, place);
    }

    @Override
    public ServerResponse<List<String>> updateGoods2(String gid, String outVariety, String inVariety) {
        return orderService.updateOrders2(gid, null, outVariety, inVariety, null, null);
    }

    @Override
    public ServerResponse<List<String>> updateGoodsPlace2(String gid, String spell, String place) {
        return orderService.updateOrders2(gid, null, null, null, spell, place);
    }

    //调用地图测量距离接口，获得 设定距离范围内的司机列表
    private List<DriverInfo> queryDriverInCircle(String longitude, String latitude, Integer carNum) {
        //经纬度小数点不超过6位
        List<DriverInfo> driverInfo = new ArrayList<>();
        double radius = 0.0;
        Integer limit = carNum * 2;
        String destination = longitude + "," + latitude;

        ServerResponse<List<DriverLogistics>> serverData = driverLogisticsService.selectDriverByLogistics(longitude, latitude, radius, limit);
        List<DriverLogistics> dlList = serverData.getData();
        if (dlList == null || dlList.size() <= 0) return null;

        String url = "https://restapi.amap.com/v3/distance?output=json&key=209c9214120f1670b9053b298afdc619";
        int frequency = 0;  // 将mongodb空间索引 按照半径过滤后的司机按100进行分组（因为高德距离测量接口，一次最大允许100个值）
        while (frequency < (dlList.size() / 100 + 1)) {
            List<DriverLogistics> dlList1 = dlList.subList(frequency * 100, (frequency + 1) * 100);
            String origins = "";
            for (DriverLogistics dl : dlList1) {
                origins = origins.concat(dl.getLongitude());
                origins = origins.concat(",");
                origins = origins.concat(dl.getLatitude());
                origins = origins.concat("%7C");    //url中 |符号 转义 %7C
            }
            frequency++;
            origins = origins.substring(0, origins.length() - 3);
            url += "&origins=" + origins + "&destination=" + destination;

            try {
                String str = httpAPIService.doGet(url);

                JSONObject jsonObject = JSON.parseObject(str);
                if (jsonObject.get("status").equals(0)) continue;

                JSONArray results = jsonObject.getJSONArray("results");
                for (int i = 0; i < 100; i++) {
                    JSONObject result = (JSONObject) results.get(i);
                    if ((Double) result.get("distance") <= radius * 1000)  //origin_id  是从1开始计数的
                        driverInfo.add(dlList1.get((Integer) result.get("origin_id") - 1).getDriverInfo());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return driverInfo;
    }

    @Override
    public ServerResponse<Object> addWeChatGoods(Goods goods, Integer type, String designCarNum, String designDriverId, String longitude, String latitude, Integer isPay) {
        //微信下单，需要得到平台允许
        Query<SysSwitch> query = sysSwitchService.createQuery();
        query.filter("code", 0); //客商
        query.filter("type", 3); //下单
        SysSwitch sysSwitch = sysSwitchService.get(query);
        if (StrUtil.isNotEmpty(sysSwitch) && StrUtil.isNotEmpty(sysSwitch.getShare()) && sysSwitch.getShare() == 1)
            return ServerResponse.createError("平台现不允许给微信下单！");

        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);
        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.WeChat_Goods.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {

            try {
                goods.setCid(cid);
                goods.setFees3(new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue());

                //todo:收发货物流模式下，收发货的商品必须保持一致
                if (goods.getMold() == 2 && !goods.getOutVariety().equals(goods.getInVariety())) {
                    return ServerResponse.createError("收发货商品不一致，不符合运输规则!");
                }

                //todo:校验参数不能为空
                String cMsg = checkGoodsParam(goods);
                if (!StrUtil.isEmpty(cMsg)) {
                    return ServerResponse.createError(cMsg);
                }

                //todo:排除反复添加同一条货运信息
                String oGid = checkGoods(cid, goods);
                if (!StrUtil.isEmpty(oGid)) {
                    return ServerResponse.createError("已存在货运信息");
                }

                //todo: 校验票号outMin 和 inMin
                String billCodeStr = checkBillCode(goods.getOutUnitCode(), goods.getOutMin(), goods.getInUnitCode(), goods.getInMin());
                if (StrUtil.isNotEmpty(billCodeStr)) {
                    return ServerResponse.createError(billCodeStr);
                }

                //todo:校验车数是否小于发货和收货企业之间可用票号的最小值
                String totalStr = checkTotal(goods);
                if (!StrUtil.isEmpty(totalStr)) {
                    return ServerResponse.createError("车数大于企业可用票号数量");
                }

                //todo:指定车牌号时，对所指定的司机微信小程序账号校验
                if (StrUtil.isNotEmpty(designCarNum)) return ServerResponse.createError("现不允许给微信指派车牌号下单");
        /*DriverInfo driverInfo = null;
        if (StrUtil.isNotEmpty(designCarNum)) {
            goods.setTotal(1); //指定车牌号，则货运信息的车数只能是 1 车

            //0.判断车辆是否通过审核
            Car car = carDao.get("carNum", designCarNum);
            if (car.getVerify() == 0 || car.getVerify() == 2)return ServerResponse.createError("车辆审核不可用，请更换指定司机及车牌号");
            if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

            //1.根据车牌查询司机信息,若司机信息存在，则还要校验司机是否在客商的黑名单中
            driverInfo = driverInfoService.get("carNum", designCarNum);
            if (driverInfo != null && driverInfo.getState() == 1) {  //指定的车牌号司机注册了app，并通过审核
                Query<DriverPool> dpQuery = driverPoolService.createQuery();
                dpQuery.filter("cid", cid);
                dpQuery.filter("did", driverInfo.getObjectId().toString());
                DriverPool dp = driverPoolService.get(dpQuery);
                if (dp != null && StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
                    lockedService.unLock(cid, SysConstants.LockType.WeChat_Goods.getType());
                    return ServerResponse.createError("指定车牌号司机在黑名单中");
                }
            }
            //2.指定的车牌号在小程序司机信息表中不存在，则不能指派该订单
            WechatDriverInfo wDriver = wechatDriverInfoService.get("carNum", designCarNum);
            if (wDriver == null) {
                lockedService.unLock(cid, SysConstants.LockType.WeChat_Goods.getType());
                return ServerResponse.createError("指定车牌号的司机需要有使用过小程序接单经验");
            }
            //3.判断司机是否有未完成订单
            if (orderTakingService.searchDriverOrder(designCarNum, 2).size() > 0) {
                lockedService.unLock(cid, SysConstants.LockType.WeChat_Goods.getType());
                return ServerResponse.createError("车辆：" + designCarNum + "有未完成订单,不能接单");
            }
            if (driverInfo == null) {
                driverInfo = new DriverInfo();
                driverInfo.setObjectId(wDriver.getObjectId());
                driverInfo.setCarNum(wDriver.getCarNum());
            }
        }*/

                //todo:查询将要添加的货运信息 企业要求代扣的情况
                Map<String, Object> payMap = sysUnitTotalFees(goods);
                if (payMap != null && StrUtil.isNotEmpty(payMap.get("msg"))) {
                    return ServerResponse.createError((String) payMap.get("msg"));
                }
                //todo:客商选择代付的情况，判断客商账户余额
                BigDecimal payFee = (BigDecimal) payMap.get("sysUnitTotalFee");
                BigDecimal fee = (BigDecimal) payMap.get("sysUnitFee");
                if (isPay != 0) {
                    goods.setFees3(0);  //客商代付时，客商需要收取的信息费设置为0 即 自己需要收取的钱不用从自己账户扣除
                    BigDecimal availableFee = customerAccountService.getAvailableFee(cid);
                    if (availableFee.compareTo(payFee) < 0) {
                        return ServerResponse.result(7, "账户金额不足，请先充值");
                    }
                }

                SysUnit[] sysOutUnits = (SysUnit[]) payMap.get("sysOutUnits");
                SysUnit[] sysInUnits = (SysUnit[]) payMap.get("sysInUnits");
                int[] outFees = (int[]) payMap.get("outFees");
                int[] inFees = (int[]) payMap.get("inFees");
                goods.setTotal(goods.getTotal() == null || goods.getTotal() == 0 ? 1 : goods.getTotal());
                goods.setStatus(0);
                //todo:请求企业系统下单
                Map<String, String> msg = synOrderTooCompany(cid, goods, StrUtil.isEmpty(designCarNum) ? "" : designCarNum, sysOutUnits[1], sysInUnits[1], false);
                if (ObjectUtil.isNotEmpty(msg)) {
                    //收发货物流模式下，下单失败的时候，应当添加货运信息，并且把下单成功的单位最大最小号记录下来，设置为删除状态。
                    String inSynMsg = msg.get("inSynMsg");
                    String outSynMsg = msg.get("outSynMsg");
                    if (goods.getMold() == 2 && !StrUtil.isEmpty(outSynMsg) && StrUtil.isEmpty(inSynMsg)) {
                        goods.setOutMin(null);
                        addGoodsAndOrder(null, goods, true, 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
                    }
                    if (goods.getMold() == 2 && StrUtil.isEmpty(outSynMsg) && !StrUtil.isEmpty(inSynMsg)) {
                        goods.setInMin(null);
                        addGoodsAndOrder(null, goods, true, 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
                    }
                    return ServerResponse.createError(outSynMsg == null ? inSynMsg : inSynMsg == null ? outSynMsg : outSynMsg + inSynMsg);
                }

                Goods result;
        /*if (!StrUtil.isEmpty(designCarNum)) {
            //todo:手工下单，有指定车牌号的情况(一车一单)，省略客商和司机间的扫码步骤，直接添加 order 和 orderTaking
            result = addGoodsAndOrder(designCarNum, goods, false, 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
            if (result == null) {
                lockedService.unLock(cid, SysConstants.LockType.WeChat_Goods.getType());
                return ServerResponse.createError("手工下单失败");
            }
        } else {*/
                //todo:扫码下单，货运信息添加,订单号添加,并返回添加结果goods
                result = addGoodsAndOrder(null, goods, false, 0, isPay, fee, outFees, inFees, sysOutUnits, sysInUnits, null, longitude, latitude);
                if (result == null) {
                    return ServerResponse.createError("添加失败");
                }
                //}

                return ServerResponse.createSuccess("添加成功", JSON.parse("{\"gid\":\"" + result.getGid() + "\"}"));

            } finally {
                lockedService.unLock(cid, SysConstants.LockType.WeChat_Goods.getType());
            }
        }
    }

    @Override
    @Transactional
    public ServerResponse<String> goodsToFriend(String gid, String cid, String[] oids, Integer total) {
        CustomerUser newCU = customerUserService.getByPK(cid);
        if (newCU == null) return ServerResponse.createError("参数错误");

        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        //货运信息只能分享一次
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("货运信息来自他人分享，只能分享一次");
        //超过72小时订单 ，过期
        if (new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.filter("share", 0);
        oQuery.filter("carNum", null);
        oQuery.filter("delete", false);
        oQuery.filter("status", 0);
        List<Order> checkOrder = oQuery.find().toList();    //货运信息下可以分享的订单
        if (StrUtil.isEmpty(checkOrder) || (StrUtil.isNotEmpty(oids) && oids.length > checkOrder.size()) || (StrUtil.isNotEmpty(total) && total > checkOrder.size()))
            return ServerResponse.createError("能分享的订单数量不足，请新添加货运信息再分享");

        String shareCid = goods.getCid();
        String shareGid = goods.getGid();
        List<Order> orders; //客商想要分享的订单
        if (ObjectUtil.isNotEmpty(oids)) {
            Query<Order> query = orderService.createQuery();
            query.filter("oid in", oids);
            orders = query.find().toList();
        } else if (StrUtil.isNotEmpty(total)) {
            orders = checkOrder.subList(0, total);
        } else {
            orders = new ArrayList<>(checkOrder);
        }

        int share;  //要分享的order和 能分享的order 数量一致，则share值为2，否则值为1
        if (orders.size() == checkOrder.size()) {
            share = 2;
        } else {
            share = 1;
        }

        List<Object> orderObjectIds = new ArrayList<>();
        for (Order order : orders) {
            orderObjectIds.add(order.getObjectId());
        }

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> orderCollection = orderService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();
            //1.修改现有货单Goods 和 订单order 的状态
            Document gFilter = new Document("gid", gid).append("updateTime", goods.getUpdateTime());
            Map<String, Object> gMap = new HashMap<>();
            gMap.put("share", share);
            gMap.put("updateTime", time.getTime());
            Map<String, Object> gUpMap = new HashMap<>();
            gUpMap.put("$set", gMap);
            goodsCollection.updateOne(clientSession, gFilter, Document.parse(JSONObject.toJSONString(gUpMap)));

            Map<String, Object> oMap = new HashMap<>();
            Map<String, Object> oUpMap = new HashMap<>();
            oMap.put("updateTime", time.getTime());
            oMap.put("share", 1);
            oUpMap.put("$set", oMap);
            orderCollection.updateMany(clientSession, in("_id", orderObjectIds), Document.parse(JSONObject.toJSONString(oUpMap)));

            //2.给被分享的好友客商添加货单goods 和 订单order 数据
            String newGid = Utils.getUUID();
            goods.setObjectId(null);
            goods.setGid(newGid);
            goods.setCid(cid);
            goods.setShareCid(shareCid);
            goods.setShareGid(shareGid);
            goods.setUpdateTime(time.getTime());
            goods.setTotal(orders.size());
            goods.setShare(0);
            goods.setDelNum(null);
            goods.setCustomerUser(null);
            goods.setFees3(new BigDecimal(StrUtil.isEmpty(newCU.getFee()) ? "0" : newCU.getFee()).multiply(new BigDecimal(100)).intValue());
            Document gDoc = Document.parse(JSONObject.toJSONString(goods));
            gDoc.append("createTime", time);
            gDoc.append("customerUserID", newCU.getObjectId());
            goodsCollection.insertOne(clientSession, gDoc);

            List<Document> list = new ArrayList<>();
            for (Order order : orders) {
                String shareOid = order.getOid();
                order.setObjectId(null);
                order.setUpdateTime(time.getTime());
                order.setGid(newGid);
                order.setOid(Utils.getUUID());
                order.setShareCid(shareCid);
                order.setShareGid(shareGid);
                order.setShareOid(shareOid);
                Document oDoc = Document.parse(JSONObject.toJSONString(order));
                oDoc.append("createTime", time);
                list.add(oDoc);
            }
            orderCollection.insertMany(clientSession, list);

            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("分享出错，稍后重试");
        } finally {
            clientSession.close();
        }

        return ServerResponse.createSuccess("分享成功");
    }

    @Override
    public ServerResponse<String> regainFriendGoods(String gid, String[] oids) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        if (StrUtil.isEmpty(goods.getShare()) || goods.getShare() == 0)
            return ServerResponse.createError("参数错误，货运信息未分享，不可回收");

        Query<Order> query = orderService.createQuery();
        //if (StrUtil.isNotEmpty(oids)) {
        if (ObjectUtil.isNotEmpty(oids)) {
            query.filter("oid in", oids);
        } else {
            query.filter("gid", gid);
        }
        query.filter("share", 1);    //只有分享出去的订单可以回收
        List<Order> orders = query.find().toList();

        //1.循环找到分享的order
        List<String> oidList = new ArrayList<>();
        for (Order order : orders) {
            oidList.add(order.getOid());
        }
        List<String> oidTemps = new ArrayList<>(oidList);
        while (oidTemps.size() > 0) {
            Query<Order> oQuery = orderService.createQuery();
            oQuery.filter("shareOid in", oidTemps.toArray());
            List<Order> orderTemps = oQuery.find().toList();
            oidTemps.clear();
            for (Order orderTemp : orderTemps) {
                oidList.add(orderTemp.getOid());
                oidTemps.add(orderTemp.getOid());
            }
        }
        //1.1 删除 没被分享、没被司机接单、没被客商代付费用的
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("oid in", oidList.toArray());
        oQuery.filter("share", 0);
        oQuery.filter("carNum", null);
        oQuery.filter("delete", false);
        oQuery.filter("status", 0);
        oQuery.filter("fees", 0);
        List<Order> checkList = orderService.list(oQuery);
        if (checkList.size() <= 0) return ServerResponse.createError("分享的订单已全部被使用，不能回收");
        orderService.delete(oQuery);

        //2.循环找到分享的goods
        List<String> gids = new ArrayList<>();
        List<String> gidTemps = new ArrayList<>();
        gids.add(goods.getGid());
        gidTemps.add(goods.getGid());
        while (gidTemps.size() > 0) {
            Query<Goods> gQuery = this.createQuery();
            gQuery.filter("shareGid in", gidTemps.toArray());
            List<Goods> goodsTemps = gQuery.find().toList();
            gidTemps.clear();
            for (Goods goodsTemp : goodsTemps) {
                gids.add(goodsTemp.getGid());
                gidTemps.add(goodsTemp.getGid());
            }
        }
        gids.remove(0);
        //处理处于分享中间的的goods和 order 3.反向遍历，从叶子开始向上删除或修改goods 和 order
        ListIterator<String> li;// 获得ListIterator对象
        for (li = gids.listIterator(); li.hasNext(); ) {// 将游标定位到列表结尾
            li.next();
        }
        li.previous();
        for (; li.hasPrevious(); ) {// 逆序输出列表中的元素
            String temGid = li.previous();
            Query<Goods> gQuery = this.createQuery().filter("shareGid", temGid);
            List<Order> tempOrderList = orderService.list(orderService.createQuery().filter("shareGid", temGid));
            if (tempOrderList.size() <= 0) {    //分享出去的order全部收回
                this.delete("shareGid", temGid);    //删除分享的goods
                orderService.delete(orderService.createQuery().filter("gid", temGid));    //删除被分享的order
            } else {        //分享出去的订单只有部分收回
                UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
                updateOperations.set("share", 1);
                updateOperations.set("total", tempOrderList.size());
                this.update(gQuery, updateOperations);     //修改分享的goods
                List<String> oidTempList = new ArrayList<>();
                for (Order o : tempOrderList) {
                    oidTempList.add(o.getShareOid());
                }
                orderService.delete(orderService.createQuery().filter("oid nin", oidTempList));       //删除已经回收的order
            }
        }
        //处理自己分享出去的goods和order
        List<Order> tempOrderList = orderService.list(orderService.createQuery().filter("shareGid", gid)); //已经被使用，没有回收的订单
        Query<Order> orderQuery = orderService.createQuery();
        int share;
        if (tempOrderList.size() <= 0) {    //分享出去的order全部收回
            this.delete("shareGid", gid);
            share = 0;
            orderQuery.filter("gid", gid);
            orderQuery.filter("share", 1);    //修改的是当前货运信息下被分享出去的订单
        } else {        //分享出去的订单只有部分收回
            Query<Goods> gQuery = this.createQuery().filter("shareGid", gid);
            UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
            updateOperations.set("total", tempOrderList.size());
            updateOperations.set("share", 1);
            this.update(gQuery, updateOperations);     //修改分享的goods
            share = 1;
            List<String> oidTempList1 = new ArrayList<>();
            for (Order o : tempOrderList) {
                oidTempList1.add(o.getShareOid());
            }
            orderQuery.filter("oid nin", oidTempList1);    //只修改回收回来的订单
        }
        orderService.update(orderQuery, orderService.createUpdateOperations().set("share", 0));    //修改被分享的order
        Query<Goods> gQuery = this.createQuery().filter("gid", gid);
        UpdateOperations<Goods> updateOperations = this.createUpdateOperations().set("share", share);
        this.update(gQuery, updateOperations);    //修改被分享的goods

        return ServerResponse.createSuccess("订单回收成功，有" + tempOrderList.size() + "车订单已经使用不能回收");
    }

    @Override
    @Transactional
    public ServerResponse<String> backFriendGoods(String gid, String[] oids) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        if (StrUtil.isEmpty(goods.getShare()) || goods.getShare() == 2)
            return ServerResponse.createError("参数错误，货运信息被分享出去，先回收再退回");
        Query<Order> query = orderService.createQuery();
        query.filter("gid", goods.getShareGid());
        query.filter("share", 1);
        List<Order> list = orderService.list(query);   //原货运信息分享出去的订单总数
        //没有被再次分享，没有被司机接单，才可以退回
        Query<Order> oQuery = orderService.createQuery();
        //if (StrUtil.isNotEmpty(oids)) {
        if (ObjectUtil.isNotEmpty(oids)) {
            oQuery.filter("oid in", oids);
        } else {
            oQuery.filter("gid", gid);
        }
        oQuery.filter("share", 0);
        oQuery.or(
                oQuery.criteria("carNum").equal(""),
                oQuery.criteria("carNum").equal(null)
        );
        List<Order> orders = oQuery.find().toList();
        if (orders.size() <= 0) return ServerResponse.createError("订单已经全部使用，无订单可退回");
        List<Object> oidList = new ArrayList<>();
        List<Object> shareOids = new ArrayList<>();
        for (Order order : orders) {
            oidList.add(order.getOid());
            shareOids.add(order.getShareOid());
        }

        Query<Goods> gQuery = this.createQuery().filter("gid", gid);
        if (orders.size() < goods.getTotal()) {   //不是全部order退回
            this.update(gQuery, this.createUpdateOperations().set("total", goods.getTotal() - orders.size()));
        } else {                    //全部order退回
            this.delete(gQuery);
        }
        orderService.delete(orderService.createQuery().filter("oid in", oidList.toArray()));

        //修改好友客商被分享的货运信息和订单 的状态
        Query<Goods> shareGQuery = this.createQuery().filter("gid", goods.getShareGid());
        UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
        if (list.size() == orders.size()) {    //原货运信息全部被退回
            updateOperations.set("share", 0);
        } else {     //原货运信息部分被退回
            updateOperations.set("share", 1);
        }
        this.update(shareGQuery, updateOperations);
        Query<Order> query1 = orderService.createQuery().filter("oid in", shareOids.toArray());
        UpdateOperations<Order> updateOperations1 = orderService.createUpdateOperations().set("share", 0);
        orderService.update(query1, updateOperations1);

        return ServerResponse.createSuccess("分享回退成功");
    }

    @Override
    @Transactional
    public ServerResponse<String> goodsToFriend2(String gid, String cid, String[] oids, Integer total) {
        CustomerUser newCU = customerUserService.getByPK(cid);
        if (newCU == null) return ServerResponse.createError("参数错误");

        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        //货运信息只能分享一次
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("货运信息来自他人分享，只能分享一次");

        //超过72小时订单 ，过期
        if (new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.or(
                oQuery.criteria("carNum").equal(null),
                oQuery.criteria("carNum").equal("")
        );
        oQuery.filter("delete", false);
        oQuery.filter("status", 0);
        List<Order> checkOrder = oQuery.find().toList();    //货运信息下可以分享的订单
        if (StrUtil.isEmpty(checkOrder) || checkOrder.size() == 0 || goods.getTotal() - goods.getDelNum() > checkOrder.size())
            return ServerResponse.createError("有订单已经使用，不可分享");

        //订单分享给友商时，计费 友商收费项目
        boolean isForbidNewCuFee = false;
        if (goods.getFees3() <= 0) isForbidNewCuFee = false;
        SysUnit[] sysUnits = searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        int fee3 = isForbidNewCuFee ? 0 : new BigDecimal(StrUtil.isEmpty(newCU.getFee()) ? "0" : newCU.getFee()).multiply(new BigDecimal(100)).intValue();

        //分享货运信息
        UpdateOperations<Goods> up = this.createUpdateOperations();
        up.set("shareCid", cid);
        up.set("share", 2);
        if (fee3 > 0) up.set("fees3", fee3);
        this.update(this.createQuery().filter("gid", gid), up);
        //分享的订单标记修改
        UpdateOperations<Order> oUp = orderService.createUpdateOperations();
        oUp.set("shareCid", cid);
        oUp.set("share", 1);
        if (fee3 > 0) oUp.set("outFee4", fee3);
        orderService.update(oQuery, oUp);

        return ServerResponse.createSuccess("分享成功");
    }

    @Override
    public ServerResponse<String> regainFriendGoods2(String gid, String[] oids) {
        String cid = ShiroUtils.getUserId();
        CustomerUser cUser = customerUserService.getByPK(cid);
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        if (StrUtil.isEmpty(goods.getShareCid())) return ServerResponse.createError("参数错误，货运信息未分享，不可回收");

        //查询友商是否使用了订单
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.or(
                oQuery.and(
                        oQuery.criteria("carNum").notEqual(null),
                        oQuery.criteria("carNum").notEqual("")
                ),
                oQuery.criteria("delete").equal(true),
                oQuery.criteria("status").notEqual(0)
        );

        List<Order> usedOrder = oQuery.find().toList();    //友商用过的orders
        if (StrUtil.isNotEmpty(usedOrder) && usedOrder.size() > 0) return ServerResponse.createError("友商已使用过订单，不可回收");

        //订单从友商回收时，计费 客商和友商收费项目
        boolean isForbidNewCuFee = false;
        SysUnit[] sysUnits = searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        int fee3 = isForbidNewCuFee ? 0 : new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue();

        //回收货运信息
        UpdateOperations<Goods> up = this.createUpdateOperations();
        up.set("shareCid", "");
        up.set("share", 0);
        if (fee3 > 0) up.set("fee3", fee3);
        this.update(this.createQuery().filter("gid", gid), up);
        //回收分享的订单标记修改
        Query<Order> query = orderService.createQuery();
        query.criteria("gid").equal(gid);
        query.or(
                query.criteria("shareCid").notEqual(null),
                query.criteria("shareCid").notEqual("")
        );
        query.filter("delete", false);
        query.criteria("tranStatus").lessThanOrEq(0);
        UpdateOperations<Order> oUp = orderService.createUpdateOperations();
        oUp.set("shareCid", "");
        oUp.set("share", 0);
        if (fee3 > 0) oUp.set("outFee3", fee3);
        oUp.set("outFee4", 0);
        orderService.update(query, oUp);

        return ServerResponse.createSuccess("回收成功！");
    }

    @Override
    @Transactional
    public ServerResponse<String> backFriendGoods2(String gid, String[] oids) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        //查询友商是否使用了订单
        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.or(
                oQuery.and(
                        oQuery.criteria("carNum").notEqual(null),
                        oQuery.criteria("carNum").notEqual("")
                ),
                oQuery.criteria("delete").equal(true),
                oQuery.criteria("status").notEqual(0)
        );

        List<Order> usedOrder = oQuery.find().toList();    //使用过的orders
        if (StrUtil.isNotEmpty(usedOrder) && usedOrder.size() > 0) return ServerResponse.createError("已有订单使用，不可退回");

        //订单退回客商时，计费 客商和友商收费项目
        CustomerUser cUser = customerUserService.getByPK(goods.getCid());
        boolean isForbidNewCuFee = false;
        SysUnit[] sysUnits = searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;
        int fee3 = isForbidNewCuFee ? 0 : new BigDecimal(StrUtil.isEmpty(cUser.getFee()) ? "0" : cUser.getFee()).multiply(new BigDecimal(100)).intValue();

        //退回货运信息
        UpdateOperations<Goods> up = this.createUpdateOperations();
        up.set("shareCid", "");
        up.set("share", 0);
        if (fee3 > 0) up.set("fees3", fee3);
        this.update(this.createQuery().filter("gid", gid), up);
        //退回分享的订单标记修改
        Query<Order> query = orderService.createQuery();
        query.criteria("gid").equal(gid);
        query.or(
                query.criteria("shareCid").notEqual(null),
                query.criteria("shareCid").notEqual("")
        );
        query.filter("delete", false);
        query.criteria("tranStatus").lessThanOrEq(0);
        UpdateOperations<Order> oUp = orderService.createUpdateOperations();
        oUp.set("shareCid", "");
        oUp.set("share", 0);
        if (fee3 > 0) oUp.set("outFee3", fee3);
        oUp.set("outFee4", 0);
        orderService.update(query, oUp);

        return ServerResponse.createSuccess("分享回退成功");
    }

    //查询 货运信息下包含的单位信息
    @Override
    public SysUnit[] searchSysUnitInGoods(Goods goods) {
        SysUnit[] sysUnits = new SysUnit[4];
        if (StrUtil.isNotEmpty(goods.getOutUnitCode()))
            sysUnits[0] = sysUnitService.get("code", goods.getOutUnitCode());
        if (StrUtil.isNotEmpty(goods.getOutDefaultDownUnit()))
            sysUnits[1] = sysUnitService.get("code", goods.getOutDefaultDownUnit());
        if (StrUtil.isNotEmpty(goods.getInUnitCode()))
            sysUnits[2] = sysUnitService.get("code", goods.getInUnitCode());
        if (StrUtil.isNotEmpty(goods.getInDefaultDownUnit()))
            sysUnits[3] = sysUnitService.get("code", goods.getInDefaultDownUnit());
        return sysUnits;
    }

    @Override
    public ServerResponse<GoodsInUnitData> searchGoodsByOutUnit(String outUnitCode, String outBizContractCode, String outVariety, String outDefaultDownUnit, String outDefaultArea) {
        String cid = ShiroUtils.getUserId();

        Query<Goods> gQuery = this.createQuery();
        gQuery.filter("cid", cid);
        gQuery.filter("mold", 2);
        gQuery.filter("outUnitCode", outUnitCode);
        gQuery.filter("outBizContractCode", outBizContractCode);
        gQuery.filter("outVariety", outVariety);
        gQuery.filter("outDefaultDownUnit", outDefaultDownUnit);
        gQuery.filter("outDefaultArea", outDefaultArea);
        gQuery.order(Sort.descending("createTime"));
        List<Goods> goodsList = gQuery.find().toList();
        if (goodsList.size() <= 0) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());
        //return ServerResponse.createError("无对应发货单位的历史下单记录");

        Goods g = goodsList.get(0);

        Query<UnitInfo> inUnitInfoQuer = unitInfoService.createQuery();
        inUnitInfoQuer.filter("cid", cid);
        inUnitInfoQuer.filter("unitCode", g.getInUnitCode());
        UnitInfo inUnitInfo = unitInfoService.get(inUnitInfoQuer);
        if (inUnitInfo == null) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());
        //return ServerResponse.createError("客商在" + g.getInUnitName() + "需要重新认证");

        /*String bodyStr = "{\"min\":314,\"data\":[{\"bizcontractcode\":\"TO0110010482\",\"bizcontractname\":\"10.02\"}],\"max\":2800}";
        ServerResponse<Object> resBizs = ServerResponse.createSuccess("查询成功", JSON.parse(bodyStr));*/
        ServerResponse<Object> resBizs = contractService.getBizContractCode(g.getInUnitCode(), 0);
        if (resBizs.getStatus() != 0) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());
        String bizJsonStr = String.valueOf(resBizs.getData());
        JSONObject object = JSONObject.parseObject(bizJsonStr);
        int inMin = (int) object.get("min");
        int inMax = (int) object.get("max");
        if (inMax - inMin <= 0) return ServerResponse.createError("票号不足");
        JSONArray bizArr = object.getJSONArray("data");
        boolean bizhave = false;
        for (int i = 0; i < bizArr.size(); i++) {
            JSONObject o = (JSONObject) bizArr.get(i);
            if (o.get("bizcontractcode").equals(g.getInBizContractCode())) {
                bizhave = true;
                break;
            }
        }
        if (!bizhave) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());
        //return ServerResponse.createError("同类订单，收货单位已经没有可以使用的合同");

        GoodsInUnitData goodsInUnitData = new GoodsInUnitData(g.getInUnitName(), g.getInUnitCode(), g.getInBizContractName(), g.getInBizContractCode(), g.getInVariety(), g.getInSubName(), g.getInDefaultDownUnit(), g.getInAreaName(), g.getInDefaultArea(), inMin, inMax);
        return ServerResponse.createSuccess("查询成功", goodsInUnitData);
    }

    @Override
    public ServerResponse<GoodsInUnitData> searchGoodsByOutUnit2(String outUnitCode, String outBizContractCode, String outVariety, String outDefaultDownUnit, String outDefaultArea) {
        String cid = ShiroUtils.getUserId();

        Query<Goods> gQuery = this.createQuery();
        gQuery.filter("cid", cid);
        gQuery.filter("mold", 2);
        gQuery.filter("outUnitCode", outUnitCode);
        gQuery.filter("outBizContractCode", outBizContractCode);
        gQuery.filter("outVariety", outVariety);
        gQuery.filter("outDefaultDownUnit", outDefaultDownUnit);
        gQuery.filter("outDefaultArea", outDefaultArea);
        gQuery.criteria("pType").lessThan(5);   //去除企业下单
        gQuery.order(Sort.descending("createTime"));
        List<Goods> goodsList = gQuery.find().toList();
        if (goodsList.size() <= 0) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());

        Goods g = goodsList.get(0); //取日期最近的一条货运信息

        Query<UnitInfo> inUnitInfoQuer = unitInfoService.createQuery();
        inUnitInfoQuer.filter("cid", cid);
        inUnitInfoQuer.filter("unitCode", g.getInUnitCode());
        UnitInfo inUnitInfo = unitInfoService.get(inUnitInfoQuer);
        if (inUnitInfo == null) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());

        ServerResponse<Object> resBizs = contractService.getBizContractCode(g.getInUnitCode(), 0);
        if (resBizs.getStatus() != 0) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());
        String bizJsonStr = String.valueOf(resBizs.getData());
        JSONObject object = JSONObject.parseObject(bizJsonStr);
        int inMin = (int) object.get("min");
        int inMax = (int) object.get("max");
        if (inMax - inMin <= 0) return ServerResponse.createError("票号不足");
        JSONArray bizArr = object.getJSONArray("data");
        boolean bizhave = false;
        for (int i = 0; i < bizArr.size(); i++) {
            JSONObject o = (JSONObject) bizArr.get(i);
            if (o.get("bizcontractcode").equals(g.getInBizContractCode())) {
                bizhave = true;
                break;
            }
        }
        if (!bizhave) return ServerResponse.createSuccess("查询成功", new GoodsInUnitData());

        GoodsInUnitData goodsInUnitData = new GoodsInUnitData(g.getInUnitName(), g.getInUnitCode(), g.getInBizContractName(), g.getInBizContractCode(), g.getInVariety(), g.getInSubName(), g.getInDefaultDownUnit(), g.getInAreaName(), g.getInDefaultArea(), inMin, inMax);
        goodsInUnitData.setInTradingUnit(g.getInTradingUnit());
        goodsInUnitData.setInTradingUnitName(g.getInTradingUnitName());
        return ServerResponse.createSuccess("查询成功", goodsInUnitData);
    }

    @Override
    public ServerResponse<GoodsVerbInfoData> searchVerbInformation(Goods goods) {
        String cid = ShiroUtils.getUserId();

        Query<Goods> gQuery = this.createQuery();
        gQuery.filter("cid", cid);
        gQuery.filter("mold", goods.getMold());
        switch (goods.getMold()) {
            case 0:
                gQuery.filter("outUnitCode", goods.getOutUnitCode());
                gQuery.filter("outBizContractCode", goods.getOutBizContractCode());
                gQuery.filter("outVariety", goods.getOutVariety());
                gQuery.filter("outDefaultDownUnit", goods.getOutDefaultDownUnit());
                gQuery.filter("outDefaultArea", goods.getOutDefaultArea());
                break;
            case 1:
                gQuery.filter("inUnitCode", goods.getInUnitCode());
                gQuery.filter("inBizContractCode", goods.getInBizContractCode());
                gQuery.filter("inVariety", goods.getInVariety());
                gQuery.filter("inDefaultDownUnit", goods.getInDefaultDownUnit());
                gQuery.filter("inDefaultArea", goods.getInDefaultArea());
                break;
            case 2:
                gQuery.filter("outUnitCode", goods.getOutUnitCode());
                gQuery.filter("outBizContractCode", goods.getOutBizContractCode());
                gQuery.filter("outVariety", goods.getOutVariety());
                gQuery.filter("outDefaultDownUnit", goods.getOutDefaultDownUnit());
                gQuery.filter("outDefaultArea", goods.getOutDefaultArea());
                gQuery.filter("inUnitCode", goods.getInUnitCode());
                gQuery.filter("inBizContractCode", goods.getInBizContractCode());
                gQuery.filter("inVariety", goods.getInVariety());
                gQuery.filter("inDefaultDownUnit", goods.getInDefaultDownUnit());
                gQuery.filter("inDefaultArea", goods.getInDefaultArea());
                break;
            default:
                break;
        }

        gQuery.order(Sort.descending("createTime"));
        List<Goods> goodsList = gQuery.find().toList();
        if (goodsList.size() <= 0) return ServerResponse.createSuccess("查询成功", new GoodsVerbInfoData());

        Goods g = goodsList.get(0);
        GoodsVerbInfoData result = new GoodsVerbInfoData();
        /*result.setBeginPoint(g.getBeginPoint());
        result.setEndPoint(g.getEndPoint());
        result.setDistance(g.getDistance());
        result.setPrice(g.getPrice());
        result.setTolls(g.getTolls());
        result.setTotal(g.getTotal());*/
        BeanUtils.copyProperties(g, result);

        return ServerResponse.createSuccess("查询成功", result);
    }

    /**
     * 查询一级单位 和 二级单位 是否代付 和 是否需要代扣，以及代扣金额
     */
    private Map<String, Object> sysUnitTotalFees(Goods goods) {
        Map<String, Object> resultPay = new HashMap<>(); //客商代付的最后结果
        int fee = 0;       //客商需要代付的每一单金额，单位为 分
        BigDecimal totalFee;       //客商需要代付的总金额，单位为 分
        int[] outFees = new int[]{0, 0, 0}; //平台收取发货单位信息费，发货一级、二级单位要求代扣金额
        int[] inFees = new int[]{0, 0, 0}; //平台收取收货单位信息费，收货一级、二级单位要求代扣金额
        SysUnit[] sysUnits = searchSysUnitInGoods(goods);
        SysUnit[] sysOutUnits = new SysUnit[]{null, sysUnits[0], sysUnits[1]}; //第一个是愿意代付的发货单位,后两个是发货一级二级单位
        SysUnit[] sysInUnits = new SysUnit[]{null, sysUnits[2], sysUnits[3]}; //第一个是愿意代付的收货单位,后两个是收货一级二级单位
        String[] unitName = new String[2];
        //TODO:1.查询发货单位信息
        String outUnitCode = goods.getOutUnitCode();
        if (!StrUtil.isEmpty(outUnitCode)) {
            unitName[0] = goods.getOutUnitName();
            unitName[1] = goods.getOutSubName();
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("unitCode", outUnitCode.substring(4, 10));
            requestMap.put("bizContractCode", goods.getOutBizContractCode());
            requestMap.put("variety", goods.getOutVariety());
            requestMap.put("defaultDownUnit", goods.getOutDefaultDownUnit());
            requestMap.put("defaultArea", goods.getOutDefaultArea());
            resultPay = searchUnitAndFees(sysOutUnits, outFees, requestMap, unitName);
            if (StrUtil.isNotEmpty(resultPay.get("msg"))) return resultPay;
            goods.setFeesOut(-outFees[0]);
            goods.setFees1Out(outFees[1]);
            goods.setFees2Out(outFees[2]);
            fee += (outFees[0] == 0 ? sysOutUnits[1].getStandardDeduct() : 0) + outFees[1] + outFees[2];
        }
        //TODO:3.查询收货单位信息
        String inUnitCode = goods.getInUnitCode();
        if (!StrUtil.isEmpty(inUnitCode)) {
            unitName[0] = goods.getInUnitName();
            unitName[1] = goods.getInSubName();
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("unitCode", inUnitCode.substring(4, 10));
            requestMap.put("bizContractCode", goods.getInBizContractCode());
            requestMap.put("variety", goods.getInVariety());
            requestMap.put("defaultDownUnit", goods.getInDefaultDownUnit());
            requestMap.put("defaultArea", goods.getInDefaultArea());
            resultPay = searchUnitAndFees(sysInUnits, inFees, requestMap, unitName);
            if (StrUtil.isNotEmpty(resultPay.get("msg"))) return resultPay;
            goods.setFeesIn(-inFees[0]);
            goods.setFees1In(inFees[1]);
            goods.setFees2In(inFees[2]);
            fee += (inFees[0] == 0 ? sysInUnits[1].getStandardDeduct() : 0) + inFees[1] + inFees[2];
        }
        totalFee = new BigDecimal(fee).multiply(new BigDecimal(goods.getTotal())); //支付的费用 乘以 车数

        resultPay.put("sysUnitTotalFee", totalFee); //客商需要代付的总金额
        resultPay.put("sysUnitFee", new BigDecimal(fee));           //客商需要代付的每单金额
        resultPay.put("sysOutUnits", sysOutUnits);    //货运信息中涉及到的发货单位
        resultPay.put("sysInUnits", sysInUnits);    //货运信息中涉及到的收货单位
        resultPay.put("outFees", outFees);    //货运信息中涉及到的单位
        resultPay.put("inFees", inFees);    //货运信息中涉及到的单位
        return resultPay;
    }

    /**
     * 查询一级单位 和 二级单位 是否代付 和 是否需要代扣，以及代扣金额
     * 查询 平台收取一级单位的信息费
     * 查询 一级单位 是否设置了禁止客商收费
     */
    @Override
    public Map<String, Object> sysUnitTotalFees2(Goods goods) {
        Map<String, Object> resultPay = new HashMap<>(); //查询的最后结果
        int fee = 0;            //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        BigDecimal totalFee;    //平台信息费、一二级单位代扣费 合计的总金额，单位为 分
        SysUnit[] sysUnits = searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货货单位

        //TODO:1.校验发货单位账户余额
        if (!StrUtil.isEmpty(goods.getOutUnitCode())) {
            SysUnit unit = sysUnits[0];
            SysUnit subUnit = sysUnits[1];

            int standardDeduct = unit.getStandardDeduct();
            goods.setFeesOut(standardDeduct);
            goods.setFee5Out(StrUtil.isNotEmpty(unit.getThirdPartyLedger()) ? unit.getThirdPartyLedger() : 0);
            if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2())) {
                goods.setFee5OutId(unit.getThirdPartyAccount2());
                goods.setFee5OutType(0);
            } else if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) {
                goods.setFee5OutId(unit.getThirdPartyAccount());
                goods.setFee5OutType(1);
            }
            //当一级单位选择代付时，判断一级单位余额是否足够支付 平台需收取当信息费
            if (unit.getIsReplacePay() == 1) {
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(unit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) < 0) {
                    resultPay.put("msg", unit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setOutUnitPayId(unit.getObjectId().toHexString());
            } else if (subUnit.getIsReplacePay() == 1) { //当一级单位不代付，且二级单位选择代付时，判断二级单位余额是否足够支付 平台需收取当信息费
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(subUnit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) < 0) {
                    resultPay.put("msg", subUnit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setOutUnitPayId(subUnit.getObjectId().toHexString());
            }
            //当一级单位设置 禁止客商收费 时，则客商计费设为0
            if (StrUtil.isNotEmpty(unit.getIsForbidCusFee()) && unit.getIsForbidCusFee() == 1) goods.setFees3(0);

            //计费
            int fUnitFee = StrUtil.isEmpty(unit.getfUnitFee()) ? 0 : unit.getfUnitFee();
            int sUnitFee = StrUtil.isEmpty(subUnit.getsUnitFee()) ? 0 : subUnit.getsUnitFee();
            goods.setFees1Out(fUnitFee);
            goods.setFees2Out(sUnitFee);
            fee += standardDeduct + fUnitFee + sUnitFee;
        }
        //TODO:3.校验收货单位账户余额
        if (!StrUtil.isEmpty(goods.getInUnitCode())) {
            SysUnit unit = sysUnits[2];
            SysUnit subUnit = sysUnits[3];

            int standardDeduct = unit.getStandardDeductIn();
            goods.setFeesIn(standardDeduct);
            goods.setFee5In(StrUtil.isNotEmpty(unit.getThirdPartyLedger()) ? unit.getThirdPartyLedger() : 0);
            if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2())) {
                goods.setFee5InId(unit.getThirdPartyAccount2());
                goods.setFee5InType(0);
            } else if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) {
                goods.setFee5InId(unit.getThirdPartyAccount());
                goods.setFee5InType(1);
            }
//            if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) goods.setFee5InId(unit.getThirdPartyAccount());
            //当一级单位选择代付时，判断一级单位余额是否足够支付 平台需收取当信息费
            if (unit.getIsReplacePay() == 1) {
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(unit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) <= 0) {
                    resultPay.put("msg", unit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setInUnitPayId(unit.getObjectId().toHexString());
            } else if (subUnit.getIsReplacePay() == 1) { //当一级单位不代付，且二级单位选择代付时，判断二级单位余额是否足够支付 平台需收取当信息费
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(subUnit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) <= 0) {
                    resultPay.put("msg", subUnit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setInUnitPayId(subUnit.getObjectId().toHexString());
            }
            //当一级单位设置 禁止客商收费 时，则客商计费设为0
            if (StrUtil.isNotEmpty(unit.getIsForbidCusFee()) && unit.getIsForbidCusFee() == 1) goods.setFees3(0);

            //计费
            int fUnitFee = StrUtil.isEmpty(unit.getfUnitFeeIn()) ? 0 : unit.getfUnitFeeIn();
            int sUnitFee = StrUtil.isEmpty(subUnit.getsUnitFeeIn()) ? 0 : subUnit.getsUnitFeeIn();
            goods.setFees1In(fUnitFee);
            goods.setFees2In(sUnitFee);
            fee += standardDeduct + fUnitFee + sUnitFee;
        }
        totalFee = new BigDecimal(fee).multiply(new BigDecimal(goods.getTotal())); //支付的费用 乘以 车数

        resultPay.put("sysUnitTotalFee", totalFee);
        resultPay.put("sysUnitFee", new BigDecimal(fee));
        resultPay.put("sysUnits", sysUnits);
        return resultPay;
    }

    /**
     * 查询一级单位 和 二级单位 是否代付 和 是否需要代扣，以及代扣金额
     * 查询 平台收取一级单位的信息费
     * 查询 一级单位 是否设置了禁止客商收费
     * 查询 交易价格 自定义收费金额
     */
    @Override
    public Map<String, Object> sysUnitTotalFees3(Goods goods) {
        Map<String, Object> resultPay = new HashMap<>(); //查询的最后结果
        int fee = 0;            //平台信息费、一二级单位代扣费 合计的每一单金额，单位为 分
        BigDecimal totalFee;    //平台信息费、一二级单位代扣费 合计的总金额，单位为 分
        SysUnit[] sysUnits = searchSysUnitInGoods(goods);   //货运信息中涉及到的收发货货单位

        //TODO:1.校验发货单位账户余额
        if (!StrUtil.isEmpty(goods.getOutUnitCode())) {
            SysUnit unit = sysUnits[0];
            SysUnit subUnit = sysUnits[1];

            // 平台收费先查询是否自定义收费，否则按统一收费
            int standardDeduct = unit.getStandardDeduct();
            Query<TradePrice> tradePriceQuery = tradePriceService.createQuery();
            tradePriceQuery.criteria("bizContractCode").equal(goods.getOutBizContractCode());
            tradePriceQuery.criteria("defaultDownUnit").equal(goods.getOutDefaultDownUnit());
            tradePriceQuery.criteria("defaultArea").equal(goods.getOutDefaultArea());
            tradePriceQuery.criteria("variety").equal(goods.getOutVariety());
            TradePrice tradePrice = tradePriceService.get(tradePriceQuery);
            if (tradePrice!=null) standardDeduct = tradePrice.getFee();

            goods.setFeesOut(standardDeduct);
            goods.setFee5Out(StrUtil.isNotEmpty(unit.getThirdPartyLedger()) ? unit.getThirdPartyLedger() : 0);
            if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2())) {
                goods.setFee5OutId(unit.getThirdPartyAccount2());
                goods.setFee5OutType(0);
            } else if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) {
                goods.setFee5OutId(unit.getThirdPartyAccount());
                goods.setFee5OutType(1);
            }
            //当一级单位选择代付时，判断一级单位余额是否足够支付 平台需收取当信息费
            if (unit.getIsReplacePay() == 1) {
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(unit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) < 0) {
                    resultPay.put("msg", unit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setOutUnitPayId(unit.getObjectId().toHexString());
            } else if (subUnit.getIsReplacePay() == 1) { //当一级单位不代付，且二级单位选择代付时，判断二级单位余额是否足够支付 平台需收取当信息费
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(subUnit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) < 0) {
                    resultPay.put("msg", subUnit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setOutUnitPayId(subUnit.getObjectId().toHexString());
            }
            //当一级单位设置 禁止客商收费 时，则客商计费设为0
            if (StrUtil.isNotEmpty(unit.getIsForbidCusFee()) && unit.getIsForbidCusFee() == 1) goods.setFees3(0);

            //计费
            int fUnitFee = StrUtil.isEmpty(unit.getfUnitFee()) ? 0 : unit.getfUnitFee();
            int sUnitFee = StrUtil.isEmpty(subUnit.getsUnitFee()) ? 0 : subUnit.getsUnitFee();
            goods.setFees1Out(fUnitFee);
            goods.setFees2Out(sUnitFee);
            fee += standardDeduct + fUnitFee + sUnitFee;
        }
        //TODO:3.校验收货单位账户余额
        if (!StrUtil.isEmpty(goods.getInUnitCode())) {
            SysUnit unit = sysUnits[2];
            SysUnit subUnit = sysUnits[3];

            int standardDeduct = unit.getStandardDeductIn();
            Query<TradePrice> tradePriceQuery = tradePriceService.createQuery();
            tradePriceQuery.criteria("bizContractCode").equal(goods.getInBizContractCode());
            tradePriceQuery.criteria("defaultDownUnit").equal(goods.getInDefaultDownUnit());
            tradePriceQuery.criteria("defaultArea").equal(goods.getInDefaultArea());
            tradePriceQuery.criteria("variety").equal(goods.getInVariety());
            TradePrice tradePrice = tradePriceService.get(tradePriceQuery);
            if (tradePrice!=null) standardDeduct = tradePrice.getFee();

            goods.setFeesIn(standardDeduct);
            goods.setFee5In(StrUtil.isNotEmpty(unit.getThirdPartyLedger()) ? unit.getThirdPartyLedger() : 0);
            if (StrUtil.isNotEmpty(unit.getThirdPartyAccount2())) {
                goods.setFee5InId(unit.getThirdPartyAccount2());
                goods.setFee5InType(0);
            } else if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) {
                goods.setFee5InId(unit.getThirdPartyAccount());
                goods.setFee5InType(1);
            }
//            if (StrUtil.isNotEmpty(unit.getThirdPartyAccount())) goods.setFee5InId(unit.getThirdPartyAccount());
            //当一级单位选择代付时，判断一级单位余额是否足够支付 平台需收取当信息费
            if (unit.getIsReplacePay() == 1) {
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(unit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) <= 0) {
                    resultPay.put("msg", unit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setInUnitPayId(unit.getObjectId().toHexString());
            } else if (subUnit.getIsReplacePay() == 1) { //当一级单位不代付，且二级单位选择代付时，判断二级单位余额是否足够支付 平台需收取当信息费
                BigDecimal unitAccount = sysUnitAccountService.getAvailableFee(subUnit.getObjectId().toString());
                if (unitAccount.compareTo(new BigDecimal(standardDeduct * goods.getTotal())) <= 0) {
                    resultPay.put("msg", subUnit.getName() + " 在平台账户余额不足,不能代付");
                    return resultPay;
                }
                standardDeduct = 0; //当单位选择代付时，则平台收取的信息费 不计入 客商代付金额 计算中
                goods.setInUnitPayId(subUnit.getObjectId().toHexString());
            }
            //当一级单位设置 禁止客商收费 时，则客商计费设为0
            if (StrUtil.isNotEmpty(unit.getIsForbidCusFee()) && unit.getIsForbidCusFee() == 1) goods.setFees3(0);

            //计费
            int fUnitFee = StrUtil.isEmpty(unit.getfUnitFeeIn()) ? 0 : unit.getfUnitFeeIn();
            int sUnitFee = StrUtil.isEmpty(subUnit.getsUnitFeeIn()) ? 0 : subUnit.getsUnitFeeIn();
            goods.setFees1In(fUnitFee);
            goods.setFees2In(sUnitFee);
            fee += standardDeduct + fUnitFee + sUnitFee;
        }
        totalFee = new BigDecimal(fee).multiply(new BigDecimal(goods.getTotal())); //支付的费用 乘以 车数

        resultPay.put("sysUnitTotalFee", totalFee);
        resultPay.put("sysUnitFee", new BigDecimal(fee));
        resultPay.put("sysUnits", sysUnits);
        return resultPay;
    }

    private Map<String, Object> searchUnitAndFees(SysUnit[] sysUnits, int[] fees, Map<String, Object> requestMap, String[] unitName) {
        Map<String, Object> resultMap = new HashMap<>();
        fees[0] = -sysUnits[1].getStandardDeduct();
        if (sysUnits[1].getIsReplacePay() == 1 && sysUnitAccountService.getAvailableFee(sysUnits[1].getObjectId().toString()).compareTo(new BigDecimal(fees[0])) <= 0) {
            resultMap.put("msg", unitName[0] + " 在平台账户余额不足");
            return resultMap;
        }
        if (sysUnits[2] == null) {
            resultMap.put("msg", "二级单位：" + unitName[1] + "在平台信息不完善或账户余额不足，暂不支持下单");
            return resultMap;
        }
        if (sysUnits[1].getIsReplacePay() == 0 && sysUnits[2].getIsReplacePay() == 1 && sysUnitAccountService.getAvailableFee(sysUnits[2].getObjectId().toString()).compareTo(new BigDecimal(fees[0])) <= 0) {
            resultMap.put("msg", unitName[1] + "在平台账户余额不足");
            return resultMap;
        }
        if (sysUnits[1].getIsReplacePay() == 1) {
            sysUnits[0] = sysUnits[1];
        } else if (sysUnits[2].getIsReplacePay() == 1) {
            sysUnits[0] = sysUnits[2];
        } else {
            sysUnits[0] = null;
        }

        //请求企业系统，查询单位要求代扣的金额
        /*Map<String, Object> queryMsg = companyOrderService.queryCompanyCost(sysUnits[1].getIp(), requestMap);
        if (StrUtil.isNotEmpty(queryMsg.get("msg"))) {
            resultMap.put("msg", unitName[0] + queryMsg.get("msg"));
            return resultMap;
        }*/
        fees[1] = 0;//(int) queryMsg.get("fee1");    //发货一级单位要求代扣
        fees[2] = 0;//(int) queryMsg.get("fee2");    //发货二级单位要求代扣
        return resultMap;
    }

    //订单order，在企业或客商代付的情况下，添加费用信息
    private void addOrderFees(Goods goods, Order order, String cid, SysUnit[] sysOutUnits, SysUnit[] sysInUnits, boolean outIsReplacePay, boolean inIsReplacePay, Integer isPay) {
        order.setGid(goods.getGid());
        order.setStatus(0);
        order.setTradeName(goods.getTradeName());
        order.setBeginPoint(goods.getBeginPoint());
        order.setEndPoint(goods.getEndPoint());
        order.setWeight(goods.getWeight());
        order.setPrice(goods.getPrice());
        order.setDistance(goods.getDistance());
        order.setTolls(goods.getTolls());
        order.setMold(goods.getMold());
        order.setOutUnitCode(goods.getOutUnitCode());
        order.setOutUnitName(goods.getOutUnitName());
        order.setInUnitCode(goods.getInUnitCode());
        order.setInUnitName(goods.getInUnitName());
        order.setOutBizContractCode(goods.getOutBizContractCode());
        order.setOutBizContractName(goods.getOutBizContractName());
        order.setInBizContractCode(goods.getInBizContractCode());
        order.setInBizContractName(goods.getInBizContractName());
        order.setOutVariety(goods.getOutVariety());
        order.setInVariety(goods.getInVariety());
        order.setOutDefaultDownUnit(goods.getOutDefaultDownUnit());
        order.setOutSubName(goods.getOutSubName());
        order.setInDefaultDownUnit(goods.getInDefaultDownUnit());
        order.setInSubName(goods.getInSubName());
        order.setOutDefaultArea(goods.getOutDefaultArea());
        order.setOutAreaName(goods.getOutAreaName());
        order.setInDefaultArea(goods.getInDefaultArea());
        order.setInAreaName(goods.getInAreaName());
        order.setIsWeChat(goods.getIsWeChat());
        order.setShare(0);
        if (outIsReplacePay) {
            order.setFeesOut(goods.getFeesOut());
            order.setPayerIdOut(sysOutUnits[0].getObjectId().toString());
        }
        if (inIsReplacePay) {
            order.setFeesIn(goods.getFeesIn());
            order.setPayerIdIn(sysInUnits[0].getObjectId().toString());
        }
        if (isPay == 1) {
            if (!outIsReplacePay) { //发货单位的信息费 发货企业不代付
                order.setFees(order.getFees() + goods.getFeesOut());
                order.setPayerId(cid);
            }
            if (!inIsReplacePay) {  //收货单位的信息费 收货企业不代付
                order.setFees(order.getFees() + goods.getFeesIn());
                order.setPayerId(cid);
            }
            order.setFees1Out(goods.getFees1Out());
            order.setPayerId1Out(cid);
            order.setFees1In(goods.getFees1In());
            order.setPayerId1In(cid);
            order.setFees2Out(goods.getFees2Out());
            order.setPayerId2Out(cid);
            order.setFees2In(goods.getFees2In());
            order.setPayerId2In(cid);
        }
    }

    //订单order，在企业或客商代付的情况下，添加费用等信息(计费)
    private void addOrderFees2(Goods goods, Order order) {
        order.setGid(goods.getGid());
        order.setStatus(0);
        order.setTradeName(goods.getTradeName());
        order.setBeginPoint(goods.getBeginPoint());
        order.setEndPoint(goods.getEndPoint());
        order.setWeight(goods.getWeight());
        order.setPrice(goods.getPrice());
        order.setDistance(goods.getDistance());
        order.setTolls(goods.getTolls());
        order.setMold(goods.getMold());
        order.setOutUnitCode(goods.getOutUnitCode());
        order.setOutUnitName(goods.getOutUnitName());
        order.setInUnitCode(goods.getInUnitCode());
        order.setInUnitName(goods.getInUnitName());
        order.setOutBizContractCode(goods.getOutBizContractCode());
        order.setOutBizContractName(goods.getOutBizContractName());
        order.setInBizContractCode(goods.getInBizContractCode());
        order.setInBizContractName(goods.getInBizContractName());
        order.setOutVariety(goods.getOutVariety());
        order.setInVariety(goods.getInVariety());
        order.setOutDefaultDownUnit(goods.getOutDefaultDownUnit());
        order.setOutSubName(goods.getOutSubName());
        order.setInDefaultDownUnit(goods.getInDefaultDownUnit());
        order.setInSubName(goods.getInSubName());
        order.setOutDefaultArea(goods.getOutDefaultArea());
        order.setOutAreaName(goods.getOutAreaName());
        order.setInDefaultArea(goods.getInDefaultArea());
        order.setInAreaName(goods.getInAreaName());
        order.setIsWeChat(goods.getIsWeChat());
        order.setShare(0);

        //订单计费，计为0的表示不收费
        order.setOutFee0(goods.getFeesOut());
        order.setInFee0(goods.getFeesIn());
        order.setOutFee1(goods.getFees1Out());
        order.setInFee1(goods.getFees1In());
        order.setOutFee2(goods.getFees2Out());
        order.setInFee2(goods.getFees2In());
        //客商收费，分成两个字段，实际存的值相同，即司机付费时只付一个就行。
        order.setOutFee3(goods.getFees3());
        order.setInFee3(0);
        //友商和客商只能有一人收费
        order.setOutFee4(0);
        order.setInFee4(0);
        order.setOutFee5(StrUtil.isNotEmpty(goods.getFee5Out()) ? goods.getFee5Out() : 0);    //第三方费用
        order.setInFee5(StrUtil.isNotEmpty(goods.getFee5In()) ? goods.getFee5In() : 0);
        order.setOutFee6(0);    //司机服务费
        order.setInFee6(0);
        order.setOutFee7(0);    //司机特权费
        order.setInFee7(0);
        order.setOutCount6(0);  //司机服务次数
        order.setInCount6(0);
        order.setOutCount7(0);  //司机特权次数
        order.setInCount7(0);
    }

    @Override
    public ServerResponse<List<String>> updateGoodsStatus(String gid, Integer status) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("货运信息不存在");
        if (status.equals(goods.getStatus())) return ServerResponse.createSuccess("成功");

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.filter("carNum", null);
        oQuery.filter("delete", false);
        List<Order> orders = orderService.list(oQuery); //goods下司机未接的订单
        String str = status == 0 ? "恢复" : "暂停";
        if (orders.size() <= 0) return ServerResponse.createError("货运信息下已无空余订单可" + str);

        UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
        updateOperations.set("status", status);
        Query<Goods> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("updateTime", goods.getUpdateTime());
        Goods result = this.findAndModify(query, updateOperations);
        if (result == null) return ServerResponse.createError("稍后重试");

        return ServerResponse.createSuccess(str + "接单，成功");
    }

    @Override
    public ServerResponse<List<String>> updateGoodsStatus2(String gid, Integer status) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("货运信息不存在");
        if (status.equals(goods.getStatus())) return ServerResponse.createSuccess("成功");

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.filter("carNum", null);
        oQuery.filter("delete", false);
        List<Order> orders = orderService.list(oQuery); //goods下司机未接的订单
        String str = status == 0 ? "恢复" : "暂停";
        if (orders.size() <= 0) return ServerResponse.createError("货运信息下已无空余订单可" + str);

        UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
        updateOperations.set("status", status);
        Query<Goods> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("updateTime", goods.getUpdateTime());
        Goods result = this.findAndModify(query, updateOperations);
        if (result == null) return ServerResponse.createError("稍后重试");

        return ServerResponse.createSuccess(str + "接单，成功");
    }

    @Override
    @Transactional
    public ServerResponse<WXsDvrGoods> shareToWXsDvrWithTotal(String gid, Integer total) {
        String cid = ShiroUtils.getUserId();
        CustomerUser user = customerUserService.getByPK(cid);

        if (StrUtil.isEmpty(total) || total <= 0) return ServerResponse.createError("要分享车数须大于0");
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("运单号错误");
        //校验车数是否足够
        if (goods.getPartSurplusNum() <= 0) return ServerResponse.createError("货运信息下已无可分配订单");
        if (goods.getPartSurplusNum() - total < 0)
            return ServerResponse.createError("可分配车数仅有" + goods.getPartSurplusNum() + "辆");

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> goodsCollection = this.getCollection();
        MongoCollection<Document> wxDvrGoodsCollection = wXsDvrGoodsService.getCollection();
        try {
            clientSession.startTransaction();
            Date time = new Date();

            int partSurplusNum = goods.getPartSurplusNum() - total;
            Document goodsFilter = new Document("gid", gid);
            Map<String, Object> upMap = new HashMap<>();
            upMap.put("updateTime", time.getTime());
            upMap.put("partSurplusNum", partSurplusNum);
            Map<String, Object> params = new HashMap<>();
            params.put("$set", upMap);
            goodsCollection.updateOne(clientSession, goodsFilter, BsonDocument.parse(JSON.toJSONString(params)));

            WXsDvrGoods wXsDvrGoods = new WXsDvrGoods();
            wXsDvrGoods.setDvrGoodsId(Utils.getUUID());
            wXsDvrGoods.setGid(gid);
            wXsDvrGoods.setMold(goods.getMold());
            wXsDvrGoods.setBeginPoint(goods.getBeginPoint());
            wXsDvrGoods.setEndPoint(goods.getEndPoint());
            wXsDvrGoods.setTotal(goods.getTotal());
            wXsDvrGoods.setPrice(goods.getPrice());
            wXsDvrGoods.setOutVariety(goods.getOutVariety());
            wXsDvrGoods.setInVariety(goods.getInVariety());
            wXsDvrGoods.setOutBizContractName(goods.getOutBizContractName());
            wXsDvrGoods.setInBizContractName(goods.getInBizContractName());
            wXsDvrGoods.setPartNum(total);
            wXsDvrGoods.setUsedNum(0);
            if (cid.equals(goods.getCid())) {
                wXsDvrGoods.setCustomerUser(user);
            } else {
                wXsDvrGoods.setShareCusUser(user);
            }

            Document document = Document.parse(JSONObject.toJSONString(wXsDvrGoods));
            document.append("createTime", new Date());
            wxDvrGoodsCollection.insertOne(clientSession, document);
            clientSession.commitTransaction();

            return ServerResponse.createSuccess(wXsDvrGoods);
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("分配车数失败！");
        } finally {
            clientSession.close();
        }
    }

    @Override
    public ServerResponse<List<WXsDvrGoods>> getShareToWXsDvrGoodsList(String gid) {
        Query<WXsDvrGoods> query = wXsDvrGoodsService.createQuery();
        query.criteria("gid").equal(gid);
        List<WXsDvrGoods> list = wXsDvrGoodsService.list(query);
        return ServerResponse.createSuccess(list);
    }

    @Override
    public ServerResponse<List<Boolean>> checkInSubUnitNeedNetWeight(String subCode) {
        List<Boolean> resultList = new ArrayList<>();

        SysUnit inSubUnit = sysUnitService.get("code", subCode);
        if (inSubUnit == null) return ServerResponse.createError("参数错误");
        //1.判断是否需要客商录入 对方净重值
        if (StrUtil.isNotEmpty(inSubUnit.getInNeedNetWeight()) && inSubUnit.getInNeedNetWeight() == 1) {
            resultList.add(true);
        } else {
            resultList.add(false);
        }
        //2.判断是否需要客商录入 装货单号
        if (StrUtil.isNotEmpty(inSubUnit.getInNeedLoadPound()) && inSubUnit.getInNeedLoadPound() == 1) {
            resultList.add(true);
        } else {
            resultList.add(false);
        }
        //3.判断是否需要客商录入 装货时间
        if (StrUtil.isNotEmpty(inSubUnit.getInNeedLoadTime()) && inSubUnit.getInNeedLoadTime() == 1) {
            resultList.add(true);
        } else {
            resultList.add(false);
        }
        //4.判断是否需要客商上传 装货单照片
        if (StrUtil.isNotEmpty(inSubUnit.getInLoadPoundPho()) && inSubUnit.getInLoadPoundPho() == 1) {
            resultList.add(true);
        } else {
            resultList.add(false);
        }

        return ServerResponse.createSuccess(resultList);
    }

    @Override
    public ServerResponse<List<String>> updateGoodsInNetWeight(String gid, Double inNetWeight) {
        Goods goods = this.get("gid", gid);
        if (goods == null) return ServerResponse.createError("货运信息不存在");
        if (goods.getMold() != 1) return ServerResponse.createError("仅收货业务支持修改对方的净重");
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("已分享，先回收再录入净重");

        Query<Order> oQuery = orderService.createQuery();
        oQuery.filter("gid", gid);
        oQuery.filter("inChecking", 0);
        oQuery.filter("delete", false);
        List<Order> orders = orderService.list(oQuery); //goods下司机未入场未作废的订单
        if (orders.size() <= 0) return ServerResponse.createError("货运信息下未作废订单，接单司机全入场，无可修改订单");

        UpdateOperations<Order> orderUpdateOperations = orderService.createUpdateOperations();
        if (StrUtil.isNotEmpty(inNetWeight)) {
            orderUpdateOperations.set("inNetWeight", inNetWeight);
            orderUpdateOperations.set("inNetWeightPerson", "CU");
        }
        Query<Order> orderQuery = orderService.createQuery();
        orderQuery.filter("gid", gid);
        orderQuery.filter("inChecking", 0);
        orderQuery.filter("delete", false);
        UpdateResults orderUpdateResult = orderService.update(orderQuery, orderUpdateOperations);
        if (orderUpdateResult == null) return ServerResponse.createError("稍后重试");

        if (orderUpdateResult.getUpdatedCount() == goods.getTotal()) {
            if (StrUtil.isNotEmpty(inNetWeight)){
                UpdateOperations<Goods> updateOperations = this.createUpdateOperations();
                updateOperations.set("inNetWeightOne", inNetWeight);
                Query<Goods> query = this.createQuery();
                query.filter("gid", gid);
                this.update(query, updateOperations);
            }
            return ServerResponse.createSuccess("修改成功");
        } else {
            return ServerResponse.createSuccess("未入场订单修改成功");
        }

    }

    @Override
    @Async("busTaskExecutor")
    public void sendSms_ali(Order order, DriverInfo driverInfo, Integer type) {
        Query<DriverAccount> query = driverAccountService.createQuery();
        query.criteria("oid").equal(order.getOid());
        query.criteria("type").equal(2);
        DriverAccount account = driverAccountService.get(query);
        if (account != null) {
            String carNum = order.getCarNum();
            if (type == 0) {
                String subName = order.getOutSubName() != null ? order.getOutSubName() : order.getInSubName();
                String variety = order.getOutVariety() != null ? order.getOutVariety() : order.getInVariety();
                SMSService.sendNoticeSMSJieDan(driverInfo.getMobile(), carNum, subName, variety);
            } else if (type == 1) {
                SMSService.sendNoticeSMSXieHuo(driverInfo.getMobile(), carNum, order.getInSubName(), order.getInVariety());
            } else if (type == 2) {
                SMSService.sendNoticeSMSZhuangHuo(driverInfo.getMobile(), carNum, order.getOutSubName(), order.getInVariety());
            }
        }
    }

    /*@Override
    @Async("busTaskExecutor")
    public void sendSms_HuaWei(Order order, DriverInfo driverInfo, Integer type) {
        Query<DriverAccount> query = driverAccountService.createQuery();
        query.criteria("oid").equal(order.getOid());
        query.criteria("type").equal(2);
        DriverAccount account = driverAccountService.get(query);
        if (account != null) {
            List<String> templateParasList = new ArrayList<>();
            if (type == 0) {
                templateParasList.add(order.getCarNum());
                templateParasList.add(order.getOutSubName() != null ? order.getOutSubName() : order.getInSubName());
                templateParasList.add(order.getOutVariety() != null ? order.getOutVariety() : order.getInVariety());
                HuaWeiSMSService.sendSmsJieDan(driverInfo.getMobile(), JSONArray.parseArray(JSON.toJSONString(templateParasList)).toJSONString());
            } else if (type == 1) {
                templateParasList.add(order.getCarNum());
                templateParasList.add(order.getInSubName());
                templateParasList.add(order.getInVariety());
                templateParasList.add(order.getInGrossWeight());
                templateParasList.add(order.getInTareWeight());
                HuaWeiSMSService.sendSmsXieHuo(driverInfo.getMobile(), JSONArray.parseArray(JSON.toJSONString(templateParasList)).toJSONString());
            } else if (type == 2) {
                templateParasList.add(order.getCarNum());
                templateParasList.add(order.getOutSubName());
                templateParasList.add(order.getOutVariety());
                templateParasList.add(order.getOutGrossWeight());
                templateParasList.add(order.getOutTareWeight());
                HuaWeiSMSService.sendSmsZhuangHuo(driverInfo.getMobile(), JSONArray.parseArray(JSON.toJSONString(templateParasList)).toJSONString());
            }
        }
    }*/

    //    private final String[] displayNbr = {"+************", "+************", "+************", "+************"};
//    private static int displayNbrNo = 0;
    private final String displayNbr = "+************";

    @Override
    @Async("busTaskExecutor")
    public void sendCall_HuaWei(String oid, String carNum, String subName, DriverInfo driverInfo) {
        Query<DriverAccount> query = driverAccountService.createQuery();
        query.criteria("oid").equal(oid);
        query.criteria("type").equal(2);
        DriverAccount account = driverAccountService.get(query);
        if (account != null) {
            List<String> templateParaList = new ArrayList<>();
            templateParaList.add(carNum);
            templateParaList.add(subName);
            JSONArray templateParas = JSONArray.parseArray(JSON.toJSONString(templateParaList));
            Map<String, Object> playContentInfo = new HashMap<>();
            playContentInfo.put("templateId", "d6d938bd1e174334ad336a630f7b5ada");       // 语音模版id
            playContentInfo.put("templateParas", templateParas);
            List<Map<String, Object>> playInfoList = new ArrayList<>();     //PlayContentInfo
            playInfoList.add(playContentInfo);
            VoiceNotify voiceNotify = new VoiceNotify();
            try {
                voiceNotify.callNotifyAPI(displayNbr, driverInfo.getMobile(), playInfoList);
                Map<String, String> resultMap = voiceNotify.getResponsebody();
                voiceNotifyRecordService.saveVoiceNotify(carNum, subName, driverInfo.getMobile(), resultMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Scheduled(cron = "0 */1 * * * ?")  //放开注释，程序启动后开启定时任务,每5分钟执行一次
    public void voiceNotifyRetry() {
        Query<VoiceNotifyRecord> queryWrapper = voiceNotifyRecordService.createQuery();
        queryWrapper.criteria("resultCode").equal("1020165");
        queryWrapper.criteria("createTime").lessThanOrEq(IdUnit.weeHours3(null, -1));
        List<VoiceNotifyRecord> list = voiceNotifyRecordService.list(queryWrapper);
        for (VoiceNotifyRecord record : list) {
            List<String> templateParaList = new ArrayList<>();
            templateParaList.add(record.getPlateNumber());
            templateParaList.add(record.getUnitName());
            JSONArray templateParas = JSONArray.parseArray(JSON.toJSONString(templateParaList));
            Map<String, Object> playContentInfo = new HashMap<>();
            playContentInfo.put("templateId", "d6d938bd1e174334ad336a630f7b5ada");       // 语音模版id
            playContentInfo.put("templateParas", templateParas);
            List<Map<String, Object>> playInfoList = new ArrayList<>();     //PlayContentInfo
            playInfoList.add(playContentInfo);
            VoiceNotify voiceNotify = new VoiceNotify();
            try {
                voiceNotify.callNotifyAPI(displayNbr, record.getMobile(), playInfoList);
                Map<String, String> resultMap = voiceNotify.getResponsebody();
                if ("0".equals(resultMap.get("resultcode"))) voiceNotifyRecordService.delete(record.getObjectId());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public String queryUnitGoodsTotal(String outUnitCode, String inUnitCode, Integer total) {
        if (StrUtil.isNotEmpty(outUnitCode)) {
            SysUnit outUnit = sysUnitService.get("code", outUnitCode);
            int outTotal = StrUtil.isEmpty(outUnit.getGoodsTotal()) ? 100: outUnit.getGoodsTotal();
            if (outTotal < total) return outUnit.getName() + "可下单车数不能超过" + outTotal + "车";
        }
        if (StrUtil.isNotEmpty(inUnitCode) && !inUnitCode.equals(outUnitCode)) {
            SysUnit inUnit = sysUnitService.get("code", inUnitCode);
            int inTotal = StrUtil.isEmpty(inUnit.getGoodsTotal()) ? 100: inUnit.getGoodsTotal();
            if (inTotal < total) return inUnit.getName() + "可下单车数不能超过" + inTotal + "车";
        }
        return "";
    }
}