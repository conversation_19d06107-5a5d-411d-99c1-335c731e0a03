package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.httpclient.HttpResult;
import com.erdos.coal.httpclient.service.impl.HttpAPIService;
import com.erdos.coal.park.api.business.entity.UnitInfo;
import com.erdos.coal.park.api.business.service.ICompanyOrderService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.customer.dao.IOrderDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.pojo.OrderInfoData;
import com.erdos.coal.park.api.customer.pojo.OrderStatistics;
import com.erdos.coal.park.api.customer.pojo.OrderStatistics2;
import com.erdos.coal.park.api.customer.service.*;
import com.erdos.coal.park.api.driver.dao.ICarDao;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IOrderRecordService;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.park.web.sys.service.IThirdPartyAccountService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.UpdateResult;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import dev.morphia.query.UpdateResults;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static dev.morphia.aggregation.Group.*;

@Service("orderService")
public class OrderServiceImpl extends BaseMongoServiceImpl<Order, IOrderDao> implements IOrderService {
    @Resource
    private MongoClient client;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IDriverPoolService driverPoolService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    @Lazy
    private IGoodsService goodsService;
    @Resource
    private ICompanyOrderService companyOrderService;
    @Resource
    private IUnitInfoService unitInfoService;
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    @Lazy
    private IOrderRecordService orderRecordService;
    @Resource
    private IDriverToCarDao driverToCarDao;
    @Resource
    private ICarDao carDao;
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;
    @Resource
    private HttpAPIService httpAPIService;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    @Lazy
    private IPushInfoService pushInfoService;

    /**
     * 扫码 接口
     * param: String mobile
     * param: String carNum
     * param: String gid
     * return: "接单成功" 或者 "车数已满，不能继续接单"
     */
    @Override
    public ServerResponse<Order> sweepCode(String mobile, String carNum, String gid, String longitude, String latitude, String fee) {

        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
        if (ObjectUtil.isEmpty(driverInfo))
            return ServerResponse.createError("手机号为：【" + mobile + "】的司机不存在");
        // if (StrUtil.isEmpty(driverInfo.getCarNum())) return ServerResponse.createError("司机车牌号为空，请先前往设置");
        // if (!driverInfo.getCarNum().equals(carNum)) return ServerResponse.createError("司机车牌号不一致");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
//        if (orderTakingService.searchDriverOrder(carNum, 2).size() > 0)
        if (orderTakingService.searchById(driverInfo.getObjectId().toString(), 2).size() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("运单号错误！");
        //if (g.getIsWeChat() == 1) return ServerResponse.createError("请使用微信小程序扫码接单");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("carNum", null);
        query.filter("delete", false);
        query.filter("share", 0);  //未分享给友商的可接单
        Order order = this.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");
        //当mold为1时，即收货物流模式，经纬度参数不能为空
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //TODO:2.判断司机是否存在司机池中，无则添加
        //暂时屏蔽司机池添加司机
        String did = driverInfo.getObjectId().toString();
        /*String cid = g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        //TODO:3.扫码接单
        String oid = order.getOid();
        int updFeeResult = orderRecordService.updateFee(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        }

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);
        return ServerResponse.createSuccess("接单成功", result);
    }

    @Override
    public ServerResponse<Order> sweepCode2(String mobile, String carNum, String gid, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
        if (ObjectUtil.isEmpty(driverInfo))
            return ServerResponse.createError("手机号为：【" + mobile + "】的司机不存在");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("运单号错误！");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("tranStatus", 0);
        /*query.filter("carNum", null);
        query.filter("delete", false);*/
        Order order = this.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        //TODO:2.判断司机是否存在司机池中，无则添加
        String did = driverInfo.getObjectId().toString();
        //友商优先使用订单
        //暂时屏蔽司机池添加司机
        /*String cid = StrUtil.isNotEmpty(g.getShareCid()) ? g.getShareCid() : g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        //TODO:3.扫码接单
        String oid = order.getOid();
        int updFeeResult = orderRecordService.updateFee2(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        }

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);
        return ServerResponse.createSuccess("接单成功", result);
    }

    @Override
    public ServerResponse<Order> sweepCode3(String mobile, String carNum, String gid, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
        if (ObjectUtil.isEmpty(driverInfo))
            return ServerResponse.createError("手机号为：【" + mobile + "】的司机不存在");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("运单号错误！");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("tranStatus", 0);
        Order order = this.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        //TODO:2.判断司机是否存在司机池中，无则添加
        String did = driverInfo.getObjectId().toString();
        //友商优先使用订单
        //暂时屏蔽司机池添加司机
        /*String cid = StrUtil.isNotEmpty(g.getShareCid()) ? g.getShareCid() : g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee4().toString() : fee;
        } else {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee3().toString() : fee;
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //TODO:3.扫码接单
        String oid = order.getOid();
        int updFeeResult = orderRecordService.updateFee3(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        } else if (updFeeResult == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (updFeeResult == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (updFeeResult == -7) {
            return ServerResponse.createError("客商余额不足");
        }

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);
        return ServerResponse.createSuccess("接单成功", result);
    }

    @Override
    public ServerResponse<Order> sweepCode4(String mobile, String carNum, String gid, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
        if (ObjectUtil.isEmpty(driverInfo))
            return ServerResponse.createError("手机号为：【" + mobile + "】的司机不存在");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("运单号错误！");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("tranStatus", 0);
        Order order = this.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单
//        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)

        // 判断订单是否超过可接单时间
        int hour = orderRecordService.queryOrderHour(order);
        if (new Date().getTime() - order.getUpdateTime() > hour * 60 * 60 * 1000)
            return ServerResponse.createError("超过" + hour + "小时，订单过期");

        //TODO:2.判断司机是否存在司机池中，无则添加
        String did = driverInfo.getObjectId().toString();
        //友商优先使用订单
        //暂时屏蔽司机池添加司机
        /*String cid = StrUtil.isNotEmpty(g.getShareCid()) ? g.getShareCid() : g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee4().toString() : fee;
        } else {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee3().toString() : fee;
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = orderRecordService.newOrderFee2(did, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        // 单位设置delivery时，请求企业端，必须企业端返回成功，则可成功接单
        Map<String, Object> deliveryMap = orderRecordService.delivery(sysUnits, order, driverInfo, null, carNum);
        boolean su = (boolean) deliveryMap.get("su");
        String deliveryMsg = (String) deliveryMap.get("msg");
        if (!su) return ServerResponse.createError(deliveryMsg);

        //TODO:3.扫码接单
        String oid = order.getOid();
        int updFeeResult = orderRecordService.updateFee4(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        } else if (updFeeResult == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (updFeeResult == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (updFeeResult == -7) {
            return ServerResponse.createError("客商余额不足");
        }

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);

        //异步三方转账
        //thirdPartyAccountService.partyWXChange(oid);
        // 司机完成接单，异步发送短信给司机(有收费的司机提供)
        // goodsService.sendSms_HuaWei(order, driverInfo, 0);
        // 订单有发货方、订单outVarietyCode不为空、发货方二级单位配置了智慧能源授权码， 则提交数据到智慧能源
        if (StrUtil.isNotEmpty(order.getOutDefaultDownUnit()) && StrUtil.isNotEmpty(order.getOutVarietyCode())) {
            SysUnit outSubUnit = sysUnits[1];
            String accessCode = outSubUnit.getSmartEnergyAccessCode();
            if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();
            if (StrUtil.isNotEmpty(accessCode) && StrUtil.isNotEmpty(carNum))
                supervisoryElectVoucherContPosition(order, driverInfo, carNum, accessCode);
        }
//        return ServerResponse.createSuccess("操作成功");

        if (StrUtil.isNotEmpty(deliveryMsg)) {
            return ServerResponse.createSuccess(deliveryMsg);
        } else {
            return ServerResponse.createSuccess("操作成功");
        }
    }

    @Override
    public ServerResponse<Order> sweepCode6(String mobile, String carNum, String gid, String longitude, String latitude, String fee) {
        if (StrUtil.isEmpty(latitude) || StrUtil.isEmpty(longitude))
            return ServerResponse.createError("允许获取当前位置信息才可以接单");

        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.get("mobile", mobile);
        if (ObjectUtil.isEmpty(driverInfo))
            return ServerResponse.createError("手机号为：【" + mobile + "】的司机不存在");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("未通过审核，不能接单");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverInfo.getObjectId().toHexString());
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机还未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请等待审核通过");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机有未完成订单,不能接单");

        //TODO:1.查询当前货运信息下所有的 未被接单且未删除的一条订单
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("运单号错误！");
        //禁止接单的货运信息不可接单
        if (g.getStatus() == 1) return ServerResponse.createError("客商已禁止接单");
        Query<Order> query = this.createQuery();
        query.filter("gid", gid);
        query.filter("tranStatus", 0);
        Order order = this.get(query);
        if (order == null) return ServerResponse.createError("车数已满，不能继续接单");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");//订单状态0 为可用状态 才可以接单

//        if (new Date().getTime() - order.getUpdateTime() > 72 * 60 * 60 * 1000)
        // 判断订单是否超过可接单时间
        int hour = orderRecordService.queryOrderHour(order);
        if (new Date().getTime() - order.getUpdateTime() > hour * 60 * 60 * 1000)
            return ServerResponse.createError("超过" + hour + "小时，订单过期");

        //TODO:2.判断司机是否存在司机池中，无则添加
        String did = driverInfo.getObjectId().toString();
        //友商优先使用订单
        //暂时屏蔽司机池添加司机
        /*String cid = StrUtil.isNotEmpty(g.getShareCid()) ? g.getShareCid() : g.getCid();
        Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", did);
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setDid(did);
            dp.setCid(cid);
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,接单失败");
        }*/

        if (StrUtil.isNotEmpty(g.getShareCid())) {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee4().toString() : fee;
        } else {
            fee = StrUtil.isEmpty(fee) ? order.getOutFee3().toString() : fee;
        }
        //todo:判断 是否禁止客商收费
        boolean isForbidNewCuFee = false;   //false-不禁止客商收费
        SysUnit[] sysUnits = goodsService.searchSysUnitInGoods(g);   //货运信息中涉及到的收发货单位
        if (StrUtil.isNotEmpty(sysUnits[0]) && StrUtil.isNotEmpty(sysUnits[0].getIsForbidCusFee()) && sysUnits[0].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        if (!isForbidNewCuFee && StrUtil.isNotEmpty(sysUnits[2]) && StrUtil.isNotEmpty(sysUnits[2].getIsForbidCusFee()) && sysUnits[2].getIsForbidCusFee() == 1)
            isForbidNewCuFee = true;        //true-禁止客商收费
        fee = isForbidNewCuFee ? "0" : fee;

        //todo: 判断各方账户余额，并收费
        Date date = new Date();
        Map<String, Object> resultMap = orderRecordService.newOrderFee2(did, g, order, date, StrUtil.isEmpty(fee) ? 0 : Integer.valueOf(fee));
        if (StrUtil.isNotEmpty(resultMap.get("msg1"))) return ServerResponse.createError("发货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg2"))) return ServerResponse.createError("收货端余额不足");
        if (StrUtil.isNotEmpty(resultMap.get("msg3"))) return ServerResponse.createError("司机账户金额不足，请充值！");
        if (StrUtil.isNotEmpty(resultMap.get("msg4"))) return ServerResponse.createError("客商余额不足");
        List<Document> pfAccountDocs = (List<Document>) resultMap.get("pfAccountDocs");
        List<Document> sysUnitAccountDocs = (List<Document>) resultMap.get("sysUnitAccountDocs");
        List<Document> cusAccountDocs = (List<Document>) resultMap.get("cusAccountDocs");
        List<Document> dvrAccountDocs = (List<Document>) resultMap.get("dvrAccountDocs");
        List<Document> thirdPartyAccountDocs = (List<Document>) resultMap.get("thirdPartyAccountDocs");

        // 单位设置delivery时，请求企业端，必须企业端返回成功，则可成功接单
        Map<String, Object> deliveryMap = orderRecordService.delivery(sysUnits, order, driverInfo, null, carNum);
        boolean su = (boolean) deliveryMap.get("su");
        String deliveryMsg = (String) deliveryMap.get("msg");
        if (!su) return ServerResponse.createError(deliveryMsg);

        //TODO:3.扫码接单
        String oid = order.getOid();
        int updFeeResult = orderRecordService.updateFee4(did, oid, carNum, 0, fee, SysConstants.UserType.UT_DRIVER.toString(), longitude, latitude, date, pfAccountDocs, sysUnitAccountDocs, cusAccountDocs, dvrAccountDocs, thirdPartyAccountDocs);
        if (updFeeResult == -1) {
            return ServerResponse.createError("司机账户金额不足，请充值！");
        } else if (updFeeResult == -2) {
            return ServerResponse.createError("接单失败");
        } else if (updFeeResult == -5) {
            return ServerResponse.createError("发货端余额不足");
        } else if (updFeeResult == -6) {
            return ServerResponse.createError("收货端余额不足");
        } else if (updFeeResult == -7) {
            return ServerResponse.createError("客商余额不足");
        }

        //TODO:4.要返回的订单信息 即（订单号 ，车牌号）
        Order result = new Order();
        result.setOid(oid);
        result.setCarNum(carNum);
        result.setGid(gid);

        //异步三方转账
        //thirdPartyAccountService.partyWXChange(oid);
        // 司机完成接单，异步发送短信给司机(有收费的司机提供)
        // goodsService.sendSms_HuaWei(order, driverInfo, 0);
        // 订单有发货方、订单outVarietyCode不为空、发货方二级单位配置了智慧能源授权码， 则提交数据到智慧能源
        if (StrUtil.isNotEmpty(order.getOutDefaultDownUnit()) && StrUtil.isNotEmpty(order.getOutVarietyCode())) {
            SysUnit outSubUnit = sysUnits[1];
            String accessCode = outSubUnit.getSmartEnergyAccessCode();
            if (StrUtil.isEmpty(carNum)) carNum = driverInfo.getCarNum();
            if (StrUtil.isNotEmpty(accessCode) && StrUtil.isNotEmpty(carNum))
                supervisoryElectVoucherContPosition(order, driverInfo, carNum, accessCode);
        }

        if (StrUtil.isNotEmpty(deliveryMsg)) {
            return ServerResponse.createSuccess(deliveryMsg);
        } else {
            return ServerResponse.createSuccess("操作成功");
        }
    }

    /**
     * param: String gid
     * return:总车数 和 剩余车数
     */
    @Override
    public ServerResponse<Object> twoDimensionCode(String gid) {
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        if (g.getShare() == 1) return ServerResponse.createError("货运信息已经被分享给友商，不可用");
        if (new Date().getTime() - g.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");
        if (g.getStatus() == 1) return ServerResponse.createError("货运信息现处于禁止接单状态");
        Query<OrderTaking> otQuery = orderTakingService.createQuery();
        otQuery.filter("gid", gid);
        List<OrderTaking> orderTakings = orderTakingService.list(otQuery);

        //查询是否有废除的车数，废除的车数不计算到总车数和剩余车数中
        Query<Order> oQuery = this.createQuery();
        oQuery.filter("gid", gid);
        oQuery.filter("delete", false);
        List<Order> orderList = list(oQuery);
        String str;
        if (ObjectUtil.isEmpty(orderList)) {
            str = "{\"total\":\"" + 0 + "\",\"remainOrderNum\":\"" + 0 + "\"}";
        } else {
            int remainOrderNum = orderList.size() - orderTakings.size() < 0 ? 0 : orderList.size() - orderTakings.size();
            str = "{\"total\":\"" + orderList.size() + "\",\"remainOrderNum\":\"" + remainOrderNum + "\"}";
        }
        return ServerResponse.createSuccess("货运信息总订单车数和剩余订单车数", JSON.parse(str));
    }

    @Override
    public ServerResponse<Object> twoDimensionCode2(String gid) {
        String cid = ShiroUtils.getUserId();
        Goods g = goodsService.get("gid", gid);
        if (g == null) return ServerResponse.createError("编号为【" + gid + "】的货运信息不存在");
        if (cid.equals(g.getCid()) && StrUtil.isNotEmpty(g.getShareCid()))
            return ServerResponse.createError("货运信息已经被分享给友商，不可用");
        if (new Date().getTime() - g.getUpdateTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");
        if (g.getStatus() == 1) return ServerResponse.createError("货运信息现处于禁止接单状态");

        String str = "{\"total\":\"" + g.getTotal() + "\",\"remainOrderNum\":\"" + g.getOrderNum0() + "\"}";
        return ServerResponse.createSuccess("货运信息总订单车数和剩余订单车数", JSON.parse(str));
    }

    /**
     * 查询订单接口
     * param: String cid
     * param: Long orderTime
     * param: String endPoint
     * param: Integer finishTag
     * return : OrderInfoData 包含信息有-时间 商品名称 起点 终点 单号 车牌号 单价 总价
     */
    @Override
    //public ServerResponse<List<OrderInfoData>> searchOrder(Long orderTime, String endPoint, String[] finishTagArr, Integer isHand) {
    public ServerResponse<EGridResult> searchOrder(Long orderTime, String endPoint, String[] finishTagArr, Integer isHand, Integer page, Integer rows) {
        //查询未完成订单 时 finishTag参数为 "0,1"
        if (finishTagArr.length > 2 || finishTagArr.length <= 0)
            return ServerResponse.createError("参数错误！");
        Integer[] finishTag = new Integer[2];
        finishTag[0] = Integer.valueOf(finishTagArr[0]);
        if (finishTagArr.length > 1) finishTag[1] = Integer.valueOf(finishTagArr[1]);

        String cid = ShiroUtils.getUserId();

        //TODO:1.查询符合条件的货运信息编号集合。
        Query<Goods> queryG = goodsService.createQuery();
        queryG.filter("cid", cid);
        if (!StrUtil.isEmpty(orderTime)) {
            queryG.filter("createTime >= ", IdUnit.weeHours1(orderTime, "00:00:00", 0));
        }
        List<Goods> goods = queryG.find().toList();
        List<String> gids = new ArrayList<>();
        for (Goods g : goods) {
            gids.add(g.getGid());
        }

        //TODO:2.建立查询条件,查询订单
        Query<Order> orderQuery = this.createQuery();
        orderQuery.filter("gid in", gids.toArray());
        if (StrUtil.isNotEmpty(isHand)) {
            orderQuery.filter("isHand", isHand);
        }
        if (StrUtil.isNotEmpty(endPoint)) {
            orderQuery.criteria("endPoint").contains(endPoint);
        }
        Long startTime = orderTime != null ? IdUnit.weeHours(orderTime, "00:00:00", 0) : null;
        Long endTime = orderTime != null ? IdUnit.weeHours(orderTime, "00:00:00", 1) : null;
        if (startTime != null && startTime == -1) {
            return ServerResponse.createError("时间参数格式不符合秒或毫秒级时间戳长度");
        }
        if (finishTagArr.length == 2) {     //未完成订单
            List<OrderTaking> orderTakings = orderTakingService.searchByUpdateTime(cid, finishTag[0], startTime, endTime, isHand, null, null).getRows();//查询当天的接单记录
            List<OrderTaking> temp2 = orderTakingService.searchByUpdateTime(cid, finishTag[1], startTime, endTime, isHand, null, null).getRows();//查询当天的接单记录
            orderTakings.addAll(temp2);
            List<String> oidList = new ArrayList<>();
            for (OrderTaking ot : orderTakings) {
                oidList.add(ot.getOid());
            }
            orderQuery.filter("oid in ", oidList);
        } else if (finishTag[0] == 2) {     //历史订单
            List<OrderTaking> orderTakings = orderTakingService.searchByUpdateTime(cid, finishTag[0], startTime, endTime, isHand, null, null).getRows();//查询当天的接单记录
            List<String> oidList = new ArrayList<>();
            for (OrderTaking ot : orderTakings) {
                oidList.add(ot.getOid());
            }
            orderQuery.filter("oid in ", oidList);
        } else if (finishTag[0] == 3) {     //未接单
            orderQuery.or(
                    orderQuery.criteria("carNum").equal(""),
                    orderQuery.criteria("carNum").equal(null)
            );
            orderQuery.filter("delete", false);
        } else if (finishTag[0] == 4) {     //废除的订单
            orderQuery.filter("delete", true);
        }

        //TODO:3执行查询
        //List<Order> orderList = orderQuery.find().toList();
        EGridResult<Order> eGridResult = findPage(page, rows, orderQuery);
        List<Order> orderList = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        //TODO:4.处理 要返回的数据 OrderInfoData
        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orderList) {
            OrderInfoData infoData = new OrderInfoData();   //包装 要返回的数据对象
            BeanUtils.copyProperties(order, infoData);

            resultData.add(infoData);
        }

        //return ServerResponse.createSuccess("查询成功", resultData);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    @Override
    public ServerResponse<EGridResult> searchOrder2(Long orderTime, String endPoint, String[] finishTagArr, Integer isHand, Integer page, Integer rows) {
        //查询未完成订单 时 finishTag参数为 "0,1"
        if (finishTagArr.length > 2 || finishTagArr.length <= 0) return ServerResponse.createError("参数错误！");
        Integer[] finishTag = new Integer[2];
        finishTag[0] = Integer.valueOf(finishTagArr[0]);
        if (finishTagArr.length > 1) finishTag[1] = Integer.valueOf(finishTagArr[1]);

        String cid = ShiroUtils.getUserId();
        Query<Order> query = this.createQuery();
        query.criteria("gid").notEqual(null);
        query.or(
                query.criteria("cid").equal(cid),
                query.criteria("shareCid").equal(cid)
        );
        if (StrUtil.isNotEmpty(orderTime)) {
            query.filter("createTime >= ", IdUnit.weeHours1(orderTime, "00:00:00", 0));
            query.filter("createTime < ", IdUnit.weeHours1(orderTime, "00:00:00", 1));
        }
        if (StrUtil.isNotEmpty(endPoint)) query.criteria("endPoint").contains(endPoint);
        if (finishTag[0] == 3) {            //查询未接单的订单
            query.filter("tranStatus", 0);
        } else if (finishTag[0] == 2) {     //查询已完成订单
            query.filter("tranStatus", 5);
        } else if (finishTag[0] == 4) {     //查询已废除订单
            query.filter("tranStatus >=", 6);
        } else if (ObjectUtil.isNotEmpty(finishTag[1])) {     //查询未完成订单
            query.and(
                    query.criteria("tranStatus").greaterThan(0),
                    query.criteria("tranStatus").lessThan(5)
            );
        }
        EGridResult<Order> eGridResult = this.findPage(page, rows, query);
        List<Order> orderList = eGridResult.getRows();

        //TODO:4.处理 要返回的数据 OrderInfoData
        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orderList) {
            OrderInfoData infoData = new OrderInfoData();   //包装 要返回的数据对象
            BeanUtils.copyProperties(order, infoData);

            resultData.add(infoData);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(eGridResult.getTotal(), resultData));
    }

    @Override
    //public ServerResponse<List<OrderInfoData>> searchGidOrder(String gid) {
    public ServerResponse<EGridResult> searchGidOrder(String gid, Integer page, Integer rows) {
        Goods goods = goodsService.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误！");

        Query<Order> query = this.createQuery();
        query.filter("gid", gid);

        //List<Order> orders = query.find().toList();
        EGridResult<Order> eGridResult = findPage(page, rows, query);
        List<Order> orders = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orders) {
            //TODO:处理要返回的数据包装到OrderInfoData对象中
            OrderInfoData result = new OrderInfoData();
            BeanUtils.copyProperties(order, result);
            addAppType(goods, order, result);
            resultData.add(result);
        }

        //return ServerResponse.createSuccess("查询成功", resultData);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    //给order添加App端需要处理的类型分类
    private void addAppType(Goods goods, Order order, OrderInfoData orderInfoData) {
        Boolean[] appType = new Boolean[]{true, true, true, true, true, true, false, false, true};
        /*
         * 数组索引
         *0-是否可修改，司机未接单 和 司机未完成订单的 和 客商没推送司机废除订单的 和 不是来自客商分享的订单 可以修改，收货物流模式不可修改订单 , 超过72小时订单过期不可修改
         *1-是否可废除，司机未接单 和 司机未完成订单的 和 不是来自客商分享的订单 可以废除,司机支付锁定的过程不可以废除 , 通过了一检的订单不可废除
         *2-是否可分享给微信司机用户，司机未接单 以及goods字段isWeChat 区分，超过72小时订单过期不可分享
         *3-是否来自友商分享，字段shareGid 或者 shareCid 区分
         *4-是否可分享给友商，非微信司机用户的goods，订单没有被分享且司机未接单的 ,超过72小时订单过期不可分享
         *5-是否可退回，     来自他人分享 且 司机未接单
         *6-禁止运输, 未删除且未完成的订单，可运输状态时，显示"禁止运输按钮"
         *7-恢复运输, 未删除且未完成的订单，禁止运输状态时，显示"恢复运输按钮"
         *8-是否可以修改运往地
         */
        OrderTaking ot = orderTakingService.get("oid", order.getOid());
        if (goods.getMold() == 1 || order.getDelete() || (ot != null && (ot.getFinishTag() != 0 || (StrUtil.isNotEmpty(ot.getDelete()) && ot.getDelete() == 0))) || StrUtil.isNotEmpty(order.getShareOid())
                || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000) {
            appType[0] = false;
            appType[8] = false;
        }
        if (order.getDelete() || order.isLocked() || (ot != null && ot.getFinishTag() != 0) || StrUtil.isNotEmpty(order.getShareOid()) || (ot != null && StrUtil.isNotEmpty(ot.getDelete()))
                || (StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0))
            appType[1] = false;
        if (goods.getpType() == 5) appType[0] = false;

        if (order.getDelete() || order.isLocked() || (ot != null && ot.getFinishTag() != 0) || StrUtil.isNotEmpty(order.getShareOid()) || (ot != null && StrUtil.isNotEmpty(ot.getDelete()))
                || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000) {
            appType[2] = false;
        }
        /*if (StrUtil.isNotEmpty(order.getCarNum()) || StrUtil.isEmpty(goods.getIsWeChat()) || goods.getIsWeChat() == 0)
            appType[2] = false;*/
        if (StrUtil.isEmpty(order.getShareOid())) appType[3] = false;
        if (StrUtil.isNotEmpty(order.getCarNum()) || (StrUtil.isNotEmpty(goods.getIsWeChat()) && goods.getIsWeChat() == 1) || order.getShare() == 1 || new Date().getTime() - goods.getUpdateTime() > 72 * 60 * 60 * 1000)
            appType[4] = false;
        if (StrUtil.isNotEmpty(order.getCarNum()) || StrUtil.isEmpty(order.getShareOid())) appType[5] = false;

        if (!order.getDelete() && (ot == null || ot.getFinishTag() != 2) && (StrUtil.isEmpty(order.getIsTransport()) || order.getIsTransport() == 0))
            appType[6] = true;
        if (!order.getDelete() && (ot == null || ot.getFinishTag() != 2) && StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            appType[7] = true;

        orderInfoData.setAppType(appType);

        //添加订单的运输状态
        if (ot == null) {
            if (order.getDelete()) {
                orderInfoData.setYsStatus("3");
            } else {
                orderInfoData.setYsStatus("-1");
            }
        } else {
            orderInfoData.setYsStatus(ot.getDriverIsAgree() == 2 ? "4" : ot.getFinishTag().toString());
        }
    }

    @Override
    public ServerResponse<EGridResult> searchGidOrder2(String gid, Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        Goods goods = goodsService.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误！");

        Query<Order> query = this.createQuery();
        query.filter("gid", gid);

        if (StrUtil.isNotEmpty(type)) {
            switch (type) {
                case 0:
                    query.criteria("tranStatus").equal(0);
                    break;
                case 1:
                    query.criteria("tranStatus").greaterThan(0);
                    break;
                case 2:
                    query.criteria("tranStatus").equal(5);
                    break;
                default:
                    break;
            }
        }
        query.order(Sort.ascending("objectId"));

        EGridResult<Order> eGridResult = findPage(page, rows, query);
        List<Order> orders = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orders) {
            //TODO:处理要返回的数据包装到OrderInfoData对象中
            OrderInfoData result = new OrderInfoData();
            BeanUtils.copyProperties(order, result);
            addAppType2(cid, goods, order, result);
            resultData.add(result);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    private void addAppType2(String cid, Goods goods, Order order, OrderInfoData orderInfoData) {
        Boolean[] appType = new Boolean[]{false, false, false, false, false, false, false, false, false};
        /*
         * 数组索引
         *0-是否可修改，司机未接单 和 司机未入场 和 不是来自客商分享的订单 可以修改，收货物流模式不可修改订单 , 超过72小时订单过期不可修改
         *1-是否可废除，司机未接单 和 司机未完成订单的 和 不是来自客商分享的订单 可以废除,司机支付锁定的过程不可以废除 , 通过了一检的订单不可废除
         *2-false 是否可分享给微信司机用户，司机未接单 以及goods字段isWeChat 区分，超过72小时订单过期不可分享
         *3-false 是否来自友商分享，字段shareGid 或者 shareCid 区分
         *4-false 是否可分享给友商，非微信司机用户的goods，订单没有被分享且司机未接单的 ,超过72小时订单过期不可分享
         *5-false 是否可退回，     来自他人分享 且 司机未接单
         *6-禁止运输, 未删除且未完成的订单，可运输状态时，显示"禁止运输按钮"
         *7-恢复运输, 未删除且未完成的订单，禁止运输状态时，显示"恢复运输按钮"
         *8-是否可以修改运往地
         */
        //if (order.getTranStatus() == 0 || (order.getMold() != 1 && order.getOutChecking() <= 0)) {
        //收货物流模式，接单了的，也可以允许撤单
        if (order.getTranStatus() == 0 || order.getOutChecking() <= 0 || order.getInChecking() <= 0) {
            appType[0] = true;
            appType[1] = true;
            appType[8] = true;
        }
        if (goods.getpType() == 5) appType[0] = false;
        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (!order.getDelete() && order.getTranStatus() < 5 && (StrUtil.isEmpty(order.getIsTransport()) || order.getIsTransport() == 0))
            appType[6] = true;
        if (!order.getDelete() && order.getTranStatus() < 5 && StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            appType[7] = true;
        orderInfoData.setAppType(appType);
    }

    @Override
    public ServerResponse<EGridResult> searchGidOrder3(String gid, Integer type, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();
        Goods goods = goodsService.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误！");

        SysUnit inSubUnit = null;
        if (goods.getMold() == 1) inSubUnit = sysUnitService.get("code", goods.getInDefaultDownUnit());

        Query<Order> query = this.createQuery();
        query.filter("gid", gid);

        if (StrUtil.isNotEmpty(type)) {
            switch (type) {
                case 0:
                    query.criteria("tranStatus").equal(0);
                    break;
                case 1:
                    query.criteria("tranStatus").greaterThan(0);
                    break;
                case 2:
                    query.criteria("tranStatus").equal(5);
                    break;
                default:
                    break;
            }
        }
        query.order(Sort.ascending("objectId"));

        EGridResult<Order> eGridResult = findPage(page, rows, query);
        List<Order> orders = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        List<OrderInfoData> resultData = new ArrayList<>();
        for (Order order : orders) {
            //TODO:处理要返回的数据包装到OrderInfoData对象中
            OrderInfoData result = new OrderInfoData();
            BeanUtils.copyProperties(order, result);
            addAppType3(cid, goods, order, result, inSubUnit);
            if (StrUtil.isNotEmpty(order.getLoadPoundPho()))
                result.setLoadPoundPho(photoFileService.publicReadUrl(order.getLoadPoundPho()));
            resultData.add(result);
        }

        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    private void addAppType3(String cid, Goods goods, Order order, OrderInfoData orderInfoData, SysUnit inSubUnit) {
        Boolean[] appType = new Boolean[]{false, false, false, false, false, false, false, false, false, false, false, false, false};
        /*
         * 数组索引
         *0-是否可修改，司机未接单 和 司机未入场 和 不是来自客商分享的订单 可以修改，收货物流模式不可修改订单 , 超过72小时订单过期不可修改
         *1-是否可废除，司机未接单 和 司机未完成订单的 和 不是来自客商分享的订单 可以废除,司机支付锁定的过程不可以废除 , 通过了一检的订单不可废除
         *2-false 是否可分享给微信司机用户，司机未接单 以及goods字段isWeChat 区分，超过72小时订单过期不可分享
         *3-false 是否来自友商分享，字段shareGid 或者 shareCid 区分
         *4-false 是否可分享给友商，非微信司机用户的goods，订单没有被分享且司机未接单的 ,超过72小时订单过期不可分享
         *5-false 是否可退回，     来自他人分享 且 司机未接单
         *6-禁止运输, 未删除且未完成的订单，可运输状态时，显示"禁止运输按钮"
         *7-恢复运输, 未删除且未完成的订单，禁止运输状态时，显示"恢复运输按钮"
         *8-是否可以修改运往地
         *9-是否需要录入对方净重
         *10-是否需要录入装货单号
         *11-是否需要录入装货时间
         *12-是否需要上装货的照片
         */
        //if (order.getTranStatus() == 0 || (order.getMold() != 1 && order.getOutChecking() <= 0)) {
        //收货物流模式，接单了的，也可以允许撤单
        if (order.getTranStatus() == 0 || order.getOutChecking() <= 0 || order.getInChecking() <= 0) {
            appType[0] = true;
            appType[1] = true;
            appType[8] = true;
        }
        if (goods.getpType() == 5) appType[0] = false;
        if (cid.equals(goods.getShareCid())) appType[3] = true;
        if (!order.getDelete() && order.getTranStatus() < 5 && (StrUtil.isEmpty(order.getIsTransport()) || order.getIsTransport() == 0))
            appType[6] = true;
        if (!order.getDelete() && order.getTranStatus() < 5 && StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            appType[7] = true;
        if (inSubUnit != null && StrUtil.isNotEmpty(inSubUnit.getInNeedNetWeight()) && inSubUnit.getInNeedNetWeight() == 1 && order.getInChecking() == 0)
            appType[9] = true;
        if (inSubUnit != null && StrUtil.isNotEmpty(inSubUnit.getInNeedLoadPound()) && inSubUnit.getInNeedLoadPound() == 1 && order.getInChecking() == 0)
            appType[10] = true;
        if (inSubUnit != null && StrUtil.isNotEmpty(inSubUnit.getInNeedLoadTime()) && inSubUnit.getInNeedLoadTime() == 1 && order.getInChecking() == 0)
            appType[11] = true;
        if (inSubUnit != null && StrUtil.isNotEmpty(inSubUnit.getInLoadPoundPho()) && inSubUnit.getInLoadPoundPho() == 1 && order.getInChecking() == 0)
            appType[12] = true;
        orderInfoData.setAppType(appType);
    }

    @Override
    public List<Order> searchOrderByGid(String gid, Integer type) {
        /*
         * 要查询的货运信息类型（0-未接单，1-未完成订单，2-历史订单）
         * */
        List<Order> result;
        if (type == 0) {
            Query<Order> query = this.createQuery();
            query.filter("gid", gid);
            query.filter("carNum", null);
            query.filter("delete", false);
            result = this.list(query);//货运信息下没有被司机接单的订单

            //有车牌号，但是司机未同意的被归为未接单列表
            Query<OrderTaking> otQuery = orderTakingService.createQuery();
            otQuery.filter("gid", gid);
            otQuery.filter("driverIsAgree", 0);
            List<OrderTaking> orderTakings = orderTakingService.list(otQuery);
            List<String> oids = new ArrayList<>();
            for (OrderTaking ot : orderTakings) {
                oids.add(ot.getOid());
            }
            Query<Order> oQuery = this.createQuery();
            oQuery.filter("oid in", oids.toArray());
            result.addAll(this.list(oQuery));
        } else {
            Query<OrderTaking> otQuery = orderTakingService.createQuery();
            otQuery.filter("gid", gid);
            if (type == 1) {    //未完成订单
                otQuery.or(
                        otQuery.criteria("finishTag").equal(0),
                        otQuery.criteria("finishTag").equal(1)
                );
            } else if (type == 2) { //完成订单
                otQuery.filter("finishTag", 2);
            }
            List<OrderTaking> orderTakings = orderTakingService.list(otQuery);
            List<String> oids = new ArrayList<>();
            for (OrderTaking ot : orderTakings) {
                oids.add(ot.getOid());
            }
            Query<Order> oQuery = this.createQuery().filter("oid in", oids.toArray());
            result = this.list(oQuery);
        }
        return result;
    }

    @Override
    public ServerResponse<List<String>> updateOrders(String gid, String[] oids, String outVariety, String inVariety) {
        //todo:查询需要修改订单信息 以及对应的那条货运信息
        Goods goods = goodsService.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        if (StrUtil.isNotEmpty(goods.getShareGid())) return ServerResponse.createError("货运信息属于友商分享而来，无修改权限");

        if (StrUtil.isEmpty(outVariety) && StrUtil.isEmpty(inVariety))
            return ServerResponse.createError("要修改的商品名称参数不能为空");
        //todo:收发货单位的商品名称修改必须保持一致
        /*if (goods.getMold() == 2 && !outVariety.equals(inVariety))
            return ServerResponse.createError("收发货商品不一致，不符合运输规则!");*/
        //2022年1月19号，可能收发单位对物料名称定义不同

        if (new Date().getTime() - goods.getCreateTime().getTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        /*
         * 1.找到要修改的订单，首先将订单置为不可用状态，防止被接单
         * 2.将所有要修改订单对应的企业系统批号整理规范后，一次请求发货企业系统进行修改
         * 3.接收修改结果
         * 3.1.如果还有收货企业，根据结果，强制收货企业修改
         * 3.2.平台修改订单信息，同时将订单状态置为正常状态
         * */
        List<Order> orders;
        if (StrUtil.isNotEmpty(oids) && oids.length > 0) {
            Query<Order> query = this.createQuery();
            query.filter("oid in ", oids);
            orders = query.find().toList();
        } else {
            Query<Order> query = this.createQuery().filter("gid", gid);
            orders = this.list(query);// 货运信息下全部订单集合
        }
        if (orders.size() <= 0) return ServerResponse.createError("货运信息无订单");

        List<String> updateOid = new ArrayList<>(); //可修改订单号集合
        List<Order> updateOrder = new ArrayList<>();//可修改订单集合
        for (Order o : orders) {
            //被分享的订单不可修改
            if (o.getShare() == 1) continue;
            //要修改的商品名称和原来相同，则不用修改
            if (o.getMold() == 0 && outVariety.equals(o.getOutVariety())) continue;
            if (o.getMold() == 1 && inVariety.equals(o.getInVariety())) continue;
            if (o.getMold() == 2 && outVariety.equals(o.getOutVariety()) && inVariety.equals(o.getInVariety()))
                continue;
            //废除后的订单不可修改
            if (o.isDelete()) continue;
            //未被司机接单的，可直接修改
            if (StrUtil.isEmpty(o.getCarNum())) {
                updateOid.add(o.getOid());
                updateOrder.add(o);
                continue;
            }
            //司机结果的单，若通过了一检，则不可修改
            if ((StrUtil.isNotEmpty(o.getOutChecking()) && o.getOutChecking() > 0) || (StrUtil.isNotEmpty(o.getInChecking()) && o.getInChecking() > 0))
                continue;
            //司机接过的单，必须是未运输的才能修改
            //OrderTaking ot = orderTakingService.get("oid", o.getOid());
            //if (ot != null && ot.getFinishTag() == 0) {
            updateOid.add(o.getOid());
            updateOrder.add(o);
            //}
        }
        if (updateOid.size() <= 0) return ServerResponse.createError("货运信息下无订单可修改");

        Query<Order> upQuery = this.createQuery().filter("oid in", updateOid.toArray());
        this.update(upQuery, this.createUpdateOperations().set("status", 1)); //要修改的订单置为不可用状态

        List<String> updateSuccessOid = synUpdateOrder(goodsForm(goods, outVariety, inVariety, null), updateOrder, false);   //企业系统修改成功订单号集合
        if (ObjectUtil.isEmpty(updateSuccessOid)) {
            this.update(upQuery, this.createUpdateOperations().set("status", 0));//恢复订单的可用状态
            return ServerResponse.createError("修改订单数量为零", updateOid);
        }

        Query<Order> oQuery = this.createQuery().filter("oid in", updateSuccessOid.toArray());
        //todo:开始修改
        UpdateOperations<Order> oUpdateOperations = this.createUpdateOperations();
        if (StrUtil.isNotEmpty(outVariety)) oUpdateOperations.set("outVariety", outVariety);
        if (StrUtil.isNotEmpty(inVariety)) oUpdateOperations.set("inVariety", inVariety);
        oUpdateOperations.set("status", 0);
        this.update(oQuery, oUpdateOperations);

        Query<Order> query = this.createQuery();
        query.filter("gid", gid);
        query.order(Sort.ascending("_id"));
        Order order = this.list(query).get(0);
        UpdateOperations<Goods> gUpdateOperations = goodsService.createUpdateOperations();
        gUpdateOperations.set("outVariety", order.getOutVariety() == null ? "" : order.getOutVariety());
        gUpdateOperations.set("inVariety", order.getInVariety() == null ? "" : order.getInVariety());
        goodsService.update(goodsService.createQuery().filter("gid", gid), gUpdateOperations);

        //todo:不论修改成功或失败，都将订单状态还原为可用
        this.update(upQuery, this.createUpdateOperations().set("status", 0));

        if (updateOid.size() > updateSuccessOid.size()) {
            HashSet<String> allOid = new HashSet<>(updateOid);
            HashSet<String> succOid = new HashSet<>(updateSuccessOid);
            allOid.removeAll(succOid); //所有请求修改的订单号，减去 修改成功的订单号，为修改失败的订单号
            List<String> list = new ArrayList<>(allOid);
            return ServerResponse.createSuccess("修改成功，成功修改的车数为： " + updateSuccessOid.size() + "车，有" + (orders.size() - updateOid.size()) + "车已装货（或废除）不能修改,修改失败的车数为：" + allOid.size() + "车", list);
        }

        return ServerResponse.createSuccess("修改成功，成功修改的车数为： " + updateSuccessOid.size() + "车，有" + (orders.size() - updateOid.size()) + "车已装货（或废除）不能修改");
    }

    @Override
    public ServerResponse<List<String>> updateOrders2(String gid, String[] oids, String outVariety, String inVariety, String spell, String place) {
        String cid = ShiroUtils.getUserId();
        //todo:查询需要修改订单信息 以及对应的那条货运信息
        Goods goods = goodsService.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        if (cid.equals(goods.getShareCid())) return ServerResponse.createError("货运信息属于友商分享而来，无修改权限");
        if (StrUtil.isNotEmpty(goods.getShareCid())) return ServerResponse.createError("已分享，先回收再修改");
        if (goods.getpType() == 5) ServerResponse.createError("订单来自企业网单，客商不可修改物料");

        if ((StrUtil.isEmpty(outVariety) && StrUtil.isEmpty(inVariety)) && (StrUtil.isEmpty(spell) && StrUtil.isEmpty(place)))
            return ServerResponse.createError("要修改的参数不能为空");

        //2022年4月17号，存在"客商和司机的订单约定为未来的某天执行运输"，到时可能存在修改需求
        if (goods.getOrderNum0() > 0 && new Date().getTime() - goods.getCreateTime().getTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        /*
         * 1.找到要修改的订单，首先将订单置为不可用状态，防止被接单
         * 2.将所有要修改订单对应的企业系统批号整理规范后，一次请求发货企业系统进行修改
         * 3.接收修改结果
         * 3.1.如果还有收货企业，根据结果，强制收货企业修改
         * 3.2.平台修改订单信息，同时将订单状态置为正常状态
         * */
        Query<Order> query = this.createQuery();
        query.criteria("tranStatus").lessThanOrEq(1);   //未接单 或接单未进场的可修改
        query.criteria("delete").equal(false);              //未删除的订单可修改
        query.criteria("status").equal(0);                  //订单为可用（即不是司机正在接单等的订单）
        if (StrUtil.isNotEmpty(oids) && oids.length > 0) {
            query.filter("oid in ", oids);
        } else {
            query.filter("gid", gid);
        }
        List<Order> orders = this.list(query);
        if (orders.size() <= 0) return ServerResponse.createError("货运信息无可修改订单");

        List<String> updateOid = new ArrayList<>(); //可修改订单号集合
        List<Order> updateOrder = new ArrayList<>();//可修改订单集合
        for (Order o : orders) {
            //要修改的商品名称和原来相同，则不用修改
            if (o.getMold() == 0 && StrUtil.isNotEmpty(outVariety) && outVariety.equals(o.getOutVariety()))
                continue;
            if (o.getMold() == 1 && StrUtil.isNotEmpty(inVariety) && inVariety.equals(o.getInVariety()))
                continue;
            if (o.getMold() == 2 && StrUtil.isNotEmpty(outVariety) && StrUtil.isNotEmpty(inVariety) && outVariety.equals(o.getOutVariety()) && inVariety.equals(o.getInVariety()))
                continue;
            if (StrUtil.isNotEmpty(spell) && spell.equals(o.getSpell())) continue;

            //未被司机接单的，可直接修改
            if (StrUtil.isEmpty(o.getCarNum())) {
                updateOid.add(o.getOid());
                updateOrder.add(o);
                continue;
            }
            //司机接过的单，若通过了一检，则不可修改
            if ((StrUtil.isNotEmpty(o.getOutChecking()) && o.getOutChecking() > 0) || (StrUtil.isNotEmpty(o.getInChecking()) && o.getInChecking() > 0))
                continue;
            //司机接过的单，必须是未运输的才能修改
            updateOid.add(o.getOid());
            updateOrder.add(o);
        }
        if (updateOid.size() <= 0) return ServerResponse.createError("货运信息下无订单可修改");

        Query<Order> upQuery = this.createQuery().filter("oid in", updateOid.toArray());
        this.update(upQuery, this.createUpdateOperations().set("status", 1)); //要修改的订单置为不可用状态

        List<String> updateSuccessOid = synUpdateOrder(goodsForm(goods, outVariety, inVariety, null), updateOrder, true);   //企业系统修改成功订单号集合
        if (ObjectUtil.isEmpty(updateSuccessOid)) {
            this.update(upQuery, this.createUpdateOperations().set("status", 0));//恢复订单的可用状态
            return ServerResponse.createError("修改订单数量为零", updateOid);
        }

        Query<Order> oQuery = this.createQuery().filter("oid in", updateSuccessOid.toArray());
        //todo:开始修改
        UpdateOperations<Order> oUpdateOperations = this.createUpdateOperations();
        if (StrUtil.isNotEmpty(outVariety)) oUpdateOperations.set("outVariety", outVariety);
        if (StrUtil.isNotEmpty(inVariety)) oUpdateOperations.set("inVariety", inVariety);
        if (StrUtil.isNotEmpty(spell)) {
            oUpdateOperations.set("spell", spell);
            oUpdateOperations.set("place", place);
        }
        oUpdateOperations.set("status", 0);
        this.update(oQuery, oUpdateOperations);

        Query<Order> ogQuery = this.createQuery();
        ogQuery.filter("gid", gid);
        ogQuery.order(Sort.ascending("_id"));
        Order order = this.list(ogQuery).get(0);
        UpdateOperations<Goods> gUpdateOperations = goodsService.createUpdateOperations();
        gUpdateOperations.set("outVariety", order.getOutVariety() == null ? "" : order.getOutVariety());
        gUpdateOperations.set("inVariety", order.getInVariety() == null ? "" : order.getInVariety());
        if (StrUtil.isNotEmpty(order.getSpell())) {
            gUpdateOperations.set("spell", order.getSpell());
            gUpdateOperations.set("place", order.getPlace());
        }
        goodsService.update(goodsService.createQuery().filter("gid", gid), gUpdateOperations);

        //todo:不论修改成功或失败，都将订单状态还原为可用
        this.update(upQuery, this.createUpdateOperations().set("status", 0));

        if (updateOid.size() > updateSuccessOid.size()) {
            HashSet<String> allOid = new HashSet<>(updateOid);
            HashSet<String> succOid = new HashSet<>(updateSuccessOid);
            allOid.removeAll(succOid); //所有请求修改的订单号，减去 修改成功的订单号，为修改失败的订单号
            List<String> list = new ArrayList<>(allOid);
            return ServerResponse.createSuccess("修改成功，成功修改的车数为： " + updateSuccessOid.size() + "车，有" + (orders.size() - updateOid.size()) + "车已装货（或废除）不能修改,修改失败的车数为：" + allOid.size() + "车", list);
        }

        return ServerResponse.createSuccess("修改成功，成功修改的车数为： " + updateSuccessOid.size() + "车，有" + (orders.size() - updateOid.size()) + "车已装货（或废除）不能修改");
    }

    @Override
    public ServerResponse<List<String>> updateOrdersPlace(String gid, String[] oids, String spell, String place) {
        //todo:查询需要修改订单信息 以及对应的那条货运信息
        Goods goods = goodsService.get("gid", gid);
        if (goods == null) return ServerResponse.createError("参数错误");
        if (StrUtil.isNotEmpty(goods.getShareGid())) return ServerResponse.createError("货运信息属于友商分享而来，无修改权限");
        if (new Date().getTime() - goods.getCreateTime().getTime() > 72 * 60 * 60 * 1000)
            return ServerResponse.createError("超过72小时，订单过期");

        /*
         * 1.找到要修改的订单，首先将订单置为不可用状态，防止被接单
         * 2.将所有要修改订单对应的企业系统批号整理规范后，一次请求发货企业系统进行修改
         * 3.接收修改结果
         * 3.1.如果还有收货企业，根据结果，强制收货企业修改
         * 3.2.平台修改订单信息，同时将订单状态置为正常状态
         * */
        Query<Order> query = this.createQuery();
        query.filter("share", 0);    //被分享的不可修改
        query.criteria("delete").equal(false);
        query.or(query.criteria("carNum").equal(null),
                query.criteria("carNum").equal(""));
        if (goods.getMold() != 1) {
            query.criteria("outChecking").lessThanOrEq(0);
        } else {
            query.criteria("inChecking").lessThanOrEq(0);
        }
        if (StrUtil.isNotEmpty(oids) && oids.length > 0) {
            query.filter("oid in ", oids);
        } else {
            query.filter("gid", gid);// 货运信息下全部订单集合
        }
        List<Order> orders = this.list(query);  //可修改订单集合
        if (orders.size() <= 0) return ServerResponse.createError("货运信息无可修改订单");

        List<String> updateOid = new ArrayList<>(); //可修改订单号集合
        for (Order o : orders) {
            updateOid.add(o.getOid());
        }
        Query<Order> upQuery = this.createQuery().filter("oid in", updateOid.toArray());
        this.update(upQuery, this.createUpdateOperations().set("status", 1)); //要修改的订单置为不可用状态

        List<String> updateSuccessOid = synUpdateOrder(goodsForm(goods, null, null, place), orders, false);   //企业系统修改成功订单号集合
        if (ObjectUtil.isEmpty(updateSuccessOid)) {
            this.update(upQuery, this.createUpdateOperations().set("status", 0));//恢复订单的可用状态
            return ServerResponse.createError("修改订单数量为零", updateOid);
        }

        //todo:开始修改
        UpdateOperations<Order> oUpdateOperations = this.createUpdateOperations();
        oUpdateOperations.set("spell", spell);
        oUpdateOperations.set("place", place);
        oUpdateOperations.set("status", 0);
        this.update(this.createQuery().filter("oid in", updateSuccessOid.toArray()), oUpdateOperations);

        Order order = this.list(this.createQuery().filter("gid", gid)).get(0);
        if (StrUtil.isNotEmpty(order.getSpell()) && order.getSpell().equals(goods.getSpell())) {
            UpdateOperations<Goods> gUpdateOperations = goodsService.createUpdateOperations();
            gUpdateOperations.set("spell", order.getSpell());
            gUpdateOperations.set("place", order.getPlace());
            goodsService.update(goodsService.createQuery().filter("gid", gid), gUpdateOperations);
        }

        //todo:不论修改成功或失败，都将订单状态还原为可用
        this.update(upQuery, this.createUpdateOperations().set("status", 0));

        if (updateOid.size() > updateSuccessOid.size()) {
            HashSet<String> allOid = new HashSet<>(updateOid);
            HashSet<String> succOid = new HashSet<>(updateSuccessOid);
            allOid.removeAll(succOid); //所有请求修改的订单号，减去 修改成功的订单号，为修改失败的订单号
            List<String> list = new ArrayList<>(allOid);
            return ServerResponse.createSuccess("修改成功，成功修改的车数为： " + updateSuccessOid.size() + "车，有" + (orders.size() - updateOid.size()) + "车已装货（或废除）不能修改,修改失败的车数为：" + allOid.size() + "车", list);
        }

        return ServerResponse.createSuccess("修改成功，成功修改的车数为： " + updateSuccessOid.size() + "车，有" + (orders.size() - updateOid.size()) + "车已装货（或废除）不能修改");
    }

    /**
     * 将需要修改的信息封装到Goods对象中
     */
    private Goods goodsForm(Goods goods, String outVariety, String inVariety, String place) {
        Goods result = new Goods();
        result.setCid(goods.getCid());
        result.setGid(goods.getGid());
        if (StrUtil.isNotEmpty(outVariety)) {
            result.setOutUnitCode(goods.getOutUnitCode());
            result.setOutBizContractCode(goods.getOutBizContractCode());
            result.setOutVariety(outVariety);
            result.setOutDefaultDownUnit(goods.getOutDefaultDownUnit());
            result.setOutDefaultArea(goods.getOutDefaultArea());
        }
        if (StrUtil.isNotEmpty(inVariety)) {
            result.setInUnitCode(goods.getInUnitCode());
            result.setInBizContractCode(goods.getInBizContractCode());
            result.setInVariety(inVariety);
            result.setInDefaultDownUnit(goods.getInDefaultDownUnit());
            result.setInDefaultArea(goods.getInDefaultArea());
        }
        if (StrUtil.isNotEmpty(place)) {
            result.setPlace(place);
        }
        return result;
    }

    /**
     * 请求企业系统修改订单，（企业系统返回修改成功的订单批次）
     * 返回修改成功订单编号
     */
    private List<String> synUpdateOrder(Goods goods, List<Order> updateOrder, boolean place) {
        //1.找到要修改的订单的票号
        String outMinMax = "";
        String inMinMax = "";
        for (Order order : updateOrder) {
            if (order.getOutMinMax() != null) {
                outMinMax = outMinMax.concat(order.getOutMinMax());
                outMinMax = outMinMax.concat(",");
            }
            if (order.getInMinMax() != null) {
                inMinMax = inMinMax.concat(order.getInMinMax());
                inMinMax = inMinMax.concat(",");
            }
        }
        String[] outCode = outMinMax.split(",");
        String[] inCode = inMinMax.split(",");

        //2.向企业系统请求修改订单
        String outUnitCode = goods.getOutUnitCode();
        String inUnitCode = goods.getInUnitCode();
        CompletableFuture outSynMsg = null;
        CompletableFuture inSynMsg = null;
        if (!StrUtil.isEmpty(outUnitCode)) {
            String url = sysUnitService.get("code", outUnitCode).getIp();
            Query<UnitInfo> query = unitInfoService.createQuery();
            query.filter("cid", goods.getCid());
            query.filter("unitCode", outUnitCode);
            String outUserCode = unitInfoService.get(query).getUserCode();
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("userCode", outUserCode);
            requestMap.put("bizType", 1);
            requestMap.put("codeValues", chuLiPiaoHao(outCode));
            requestMap.put("bizContractCode", goods.getOutBizContractCode());
            requestMap.put("variety", goods.getOutVariety());
            requestMap.put("defaultDownUnit", goods.getOutDefaultDownUnit());
            requestMap.put("defaultArea", goods.getOutDefaultArea());
            if (place && StrUtil.isNotEmpty(goods.getPlace())) requestMap.put("place", goods.getPlace());
            outSynMsg = companyOrderService.synOrderMsg(url, requestMap, 3);
        }

        if (!StrUtil.isEmpty(inUnitCode)) {
            List<String> ruKuPiaoHao;
            if (StrUtil.isEmpty(outUnitCode)) {
                ruKuPiaoHao = chuLiPiaoHao(inCode);
            } else {
                try {
                    List<String> outCodeList = (List<String>) outSynMsg.get();     // codeStr - resultList内容是修改成功的订单
                    if (StrUtil.isEmpty(outCodeList)) return null;//都没有修改成功

                    ruKuPiaoHao = inMinMax(outCodeList, goods.getGid());
                } catch (Exception e) {
                    logger.warn("修改企业[" + goods.getOutUnitCode() + "]单位订单接口返回接口错误，企业单位修改订单失败！" +
                            "订单物流模式是：" + goods.getMold() + "***-***" +
                            "要修改的订单票号（billCode）为：out-[" + outMinMax + "]; in-[" + inMinMax + "].");
                    return null;    //(List<String>) outSynMsg.get()强转失败，说明，都没有修改成功
                }
            }

            String url = sysUnitService.get("code", inUnitCode).getIp();
            Query<UnitInfo> query = unitInfoService.createQuery();
            query.filter("unitCode", inUnitCode);
            query.filter("cid", goods.getCid());
            String inUserCode = unitInfoService.get(query).getUserCode();
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("userCode", inUserCode);
            requestMap.put("bizType", 0);
            requestMap.put("codeValues", ruKuPiaoHao);
            requestMap.put("bizContractCode", goods.getInBizContractCode());
            requestMap.put("variety", goods.getInVariety());
            requestMap.put("defaultDownUnit", goods.getInDefaultDownUnit());
            requestMap.put("defaultArea", goods.getInDefaultArea());
            if (place && StrUtil.isNotEmpty(goods.getPlace())) requestMap.put("place", goods.getPlace());
            inSynMsg = companyOrderService.synOrderMsg(url, requestMap, 3);
        }
        /* 请求修改的结果分析
         * 1.只修改发货方信息，根据outSynMsg找到修改失败的订单，进行提示
         * 2.只修改收货方信息，根据inSynMsg找到修改失败的订单，进行提示
         * 3.修改双方的信息，根据inSynMsg找到修改失败的订单，进行提示
         * */
        List<String> result = null;
        try {
            if (StrUtil.isNotEmpty(inUnitCode)) {
                List<String> inCodeList = (List<String>) inSynMsg.get();
                result = updateSuccessOid(inCodeList, goods.getGid(), "inMinMax");
            } else if (StrUtil.isNotEmpty(outUnitCode)) {
                List<String> outCodeList = (List<String>) outSynMsg.get();
                result = updateSuccessOid(outCodeList, goods.getGid(), "outMinMax");
            }
        } catch (Exception e) {
            logger.warn("调用修改企业单位订单接口返回接口错误，企业单位修改订单失败！" +
                    "订单物流模式是：" + goods.getMold() + "***-***" +
                    "要修改的订单票号（billCode）为：out-[" + outMinMax + "]; in-[" + inMinMax + "].");
            return null;    //(List<String>) inSynMsg.get() 或者 (List<String>) outSynMsg.get()强转失败，说明，都没有修改成功
        }
        if (StrUtil.isEmpty(result)) return null;    //全部都修改失败

        return result;
    }

    private List<String> chuLiPiaoHao(String[] code) {
        List<String> list = new ArrayList<>();
        //code按数字升序排序
        for (int i = code.length; i > 0; i--) {
            for (int j = 0; j < i - 1; j++) {
                if (Integer.valueOf(code[j]) > Integer.valueOf(code[j + 1])) {
                    String temp = code[j];
                    code[j] = code[j + 1];
                    code[j + 1] = temp;
                }
            }
        }
        //code按连续区间 取 开始和结束票号
        String startCode = code[0];
        String endCode = code[0];
        int acc = 1;
        for (int i = 1; i < code.length; i++) {
            if (String.valueOf(Integer.valueOf(startCode) + acc).equals(code[i])) {
                endCode = code[i];
                acc++;
            } else {
                list.add(startCode + "-" + endCode);
                startCode = code[i];
                endCode = code[i];
                acc = 1;
            }
        }
        list.add(startCode + "-" + endCode);
        return list;
    }

    private List<String> inMinMax(List<String> successCode, String gid) {
        List<String> result;
        List<Order> orders = new ArrayList<>();
        for (String codeArr : successCode) {
            if (StrUtil.isEmpty(codeArr)) continue;
            String[] code = codeArr.split("-");
            Query<Order> query = this.createQuery();
            query.filter("gid", gid);
            query.filter("outMinMax >= ", code[0]);
            query.filter("outMinMax <= ", code[1]);
            orders.addAll(query.find().toList());
        }
        if (ObjectUtil.isEmpty(orders)) return null;
        String inMinMax = "";
        for (Order order : orders) {
            if (order.getInMinMax() != null) {
                inMinMax = inMinMax.concat(order.getInMinMax());
                inMinMax = inMinMax.concat(",");
            }
        }
        result = chuLiPiaoHao(inMinMax.split(","));

        return result;
    }

    private List<String> updateSuccessOid(List<String> successCode, String gid, String MinMax) {
        List<String> result = new ArrayList<>();
        List<Order> orders = new ArrayList<>();
        for (String codeArr : successCode) {
            String[] code = codeArr.split("-");
            Query<Order> query = this.createQuery();
            query.filter("gid", gid);
            query.filter(MinMax + " >= ", code[0]);
            query.filter(MinMax + " <= ", code[1]);
            orders.addAll(query.find().toList());
        }
        if (StrUtil.isEmpty(orders)) return null;
        for (Order order : orders) {
            result.add(order.getOid());
        }
        return result;
    }

    @Override
    @Transactional
    //public ServerResponse<String> appointOrderCarNum(String oid, String carNum) {
    public ServerResponse<String> appointOrderCarNum(String oid, String carNum, String driverId) {
        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.getByPK(driverId);
        if (ObjectUtil.isEmpty(driverInfo)) return ServerResponse.createError("指定的司机不存在");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("司机未通过审核，不能派单给该司机");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverId);
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车号参数错误，车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请更换指定的司机及车牌号");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:验证 订单号是否真确
        Order order = this.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");
        //if (order.getIsWeChat() == 1) return ServerResponse.createError("不能给微信订单指派车牌号");
        if (StrUtil.isNotEmpty(order.getCarNum())) return ServerResponse.createError("订单已有司机，不可指派车牌号");
        if (order.getDelete()) return ServerResponse.createError("订单已废除，不可指派车牌号");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");
        if (order.getShare() == 1) return ServerResponse.createError("订单已经分享给友商，不能再指派司机");
        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单已禁止运输");
        Goods goods = goodsService.get("gid", order.getGid());
        if (goods.getStatus() == 1) return ServerResponse.createError("订单被禁止接单");

        //todo:检查司机是否有 未完成的订单
        //if (orderTakingService.searchDriverOrder(carNum, 2).size() > 0)
        // return ServerResponse.createError("车辆：" + carNum + "有未完成订单,不能再次派单");
        if (orderTakingService.searchById(driverId, 2).size() > 0) {
            return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能再次派单");
        }

        String cid = ShiroUtils.getUserId();
        assert cid != null;
        //TODO:2.判断司机是否存在司机池中，无则添加
        //暂时屏蔽司机池添加司机
        /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", driverInfo.getObjectId().toString());
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setCid(cid);
            dp.setDid(driverInfo.getObjectId().toString());
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,派单失败");
        }*/

        //TODO:3.订单order指派车牌号
        //下订单  /* 添加updateTime作为要修改条件,解决并发操作过程数据脏读问题 */
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = this.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();
            //1.修改order
            Document filter = new Document("oid", oid);
            filter.append("updateTime", order.getUpdateTime());
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            map.put("carNum", carNum);
            map.put("updateTime", time.getTime());
            upMap.put("$set", map);
            UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(upMap)));
            if (a.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("指派车牌号派单失败！");
            }
            //2.添加orderTaking ,生成接单信息
            orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(oid, goods, driverInfo, 0, 1, 0, time));
            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("指派车牌号失败");
        } finally {
            clientSession.close();
        }

        return ServerResponse.createSuccess("订单指派车牌号成功");
    }

    @Override
    public ServerResponse<String> appointOrderCarNum2(String oid, String carNum, String driverId) {
        String cid = ShiroUtils.getUserId();
        //todo:验证 是否存在该手机号的司机
        DriverInfo driverInfo = driverInfoService.getByPK(driverId);
        if (ObjectUtil.isEmpty(driverInfo)) return ServerResponse.createError("指定的司机不存在");
        if (StrUtil.isNotEmpty(driverInfo.getState()) && (driverInfo.getState() != 1 && driverInfo.getState() != 4))
            return ServerResponse.createError("司机未通过审核，不能派单给该司机");

        //todo.判断车辆是否通过审核
        //查询司机的车辆关联列表（delete = 1为司机取消关联的车辆，不查询）
        Query<DriverToCar> dctQuery = driverToCarDao.createQuery();
        dctQuery.filter("driverId", driverId);
        dctQuery.filter("carNum", carNum);
        dctQuery.filter("delete", 0);
        DriverToCar driverToCar = driverToCarDao.get(dctQuery);
        if (driverToCar == null) return ServerResponse.createError("司机未关联车辆" + carNum);
        Car car = carDao.getByPK(driverToCar.getCarId());
        if (car == null) return ServerResponse.createError("车号参数错误，车辆不存在");
        if (car.getVerify() == 0 || car.getVerify() == 2) return ServerResponse.createError("车辆审核不可用，请更换指定的司机及车牌号");
        if (car.getVerify() == 4) return ServerResponse.createError("车辆停用");

        //todo:验证 订单号是否真确
        Order order = this.get("oid", oid);
        if (order == null) return ServerResponse.createError("订单号错误");
        if (StrUtil.isNotEmpty(order.getCarNum())) return ServerResponse.createError("订单已有司机，不可指派车牌号");
        if (order.getDelete()) return ServerResponse.createError("订单已废除，不可指派车牌号");
        if (order.getStatus() == 1) return ServerResponse.createError("订单暂时不可用，请稍后重试");
        if (StrUtil.isNotEmpty(order.getIsTransport()) && order.getIsTransport() == 1)
            return ServerResponse.createError("订单已禁止运输");

        Goods goods = goodsService.get("gid", order.getGid());
        if (goods.getStatus() == 1) return ServerResponse.createError("订单被禁止接单");
        if (cid.equals(goods.getCid()) && StrUtil.isNotEmpty(goods.getShareCid()))
            return ServerResponse.createError("订单已经分享给友商，不能再指派司机");

        //todo:检查司机是否有 未完成的订单
        if (driverInfo.getHaveOrder() > 0)
            return ServerResponse.createError("司机：" + driverInfo.getMobile() + "有未完成订单,不能再次派单");

        //TODO:2.判断司机是否存在司机池中，无则添加
        //暂时屏蔽司机池添加司机
        /*Query<DriverPool> dpQuery = driverPoolService.createQuery();
        dpQuery.filter("cid", cid);
        dpQuery.filter("did", driverInfo.getObjectId().toString());
        DriverPool dp = driverPoolService.get(dpQuery);
        if (dp == null) {
            dp = new DriverPool();
            dp.setCid(cid);
            dp.setDid(driverInfo.getObjectId().toString());
            dp.setDriverInfo(driverInfo);
            dp.setCustomerUser(customerUserService.get(new ObjectId(cid)));
            driverPoolService.save(dp);
        } else if (StrUtil.isNotEmpty(dp.getWhiteOrBlack()) && dp.getWhiteOrBlack() == 0) {   //司机在黑名中
            return ServerResponse.createError("司机在黑名单中,派单失败");
        }*/

        //TODO:3.订单order指派车牌号
        //下订单  /* 添加updateTime作为要修改条件,解决并发操作过程数据脏读问题 */
        ClientSession clientSession = client.startSession();
        MongoCollection<Document> orderCollection = this.getCollection();
        MongoCollection<Document> orderTakingCollection = orderTakingService.getCollection();
        MongoCollection<Document> gCollection = goodsService.getCollection();
        MongoCollection<Document> driverCollection = driverInfoService.getCollection();

        try {
            clientSession.startTransaction();
            Date time = new Date();
            //1.修改order
            Document filter = new Document("oid", oid);
            filter.append("updateTime", order.getUpdateTime());
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> upMap = new HashMap<>();
            map.put("carNum", carNum);
            map.put("did", driverId);
            map.put("updateTime", time.getTime());
            map.put("tranStatus", 1);
            upMap.put("$set", map);
            UpdateResult a = orderCollection.updateOne(clientSession, filter, Document.parse(JSONObject.toJSONString(upMap)));
            if (a.getModifiedCount() <= 0) {
                clientSession.abortTransaction();
                return ServerResponse.createError("指派车牌号派单失败！");
            }
            //2.添加orderTaking ,生成接单信息
            orderTakingCollection.insertOne(clientSession, orderTakingService.createOTDoc(order, driverInfo, 0, 1, 0, time));

            //修改货运信息
            Map<String, Object> param = new HashMap<>();
            param.put("orderNum0", -1);
            param.put("orderNum1", 1);
            Map<String, Object> upgMap = new HashMap<>();
            upgMap.put("$inc", param);
            gCollection.updateOne(clientSession, new Document("gid", goods.getGid()), Document.parse(JSONObject.toJSONString(upgMap)));

            //修改司机是否有运输中订单字段haveOrder
            Map<String, Object> dvrUpMap = new HashMap<>();
            Map<String, Object> dvrMap = new HashMap<>();
            dvrMap.put("haveOrder", 1);
            dvrUpMap.put("$set", dvrMap);
            driverCollection.updateOne(clientSession, new Document("_id", new ObjectId(driverId)), Document.parse(JSONObject.toJSONString(dvrUpMap)));

            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            logger.error("appointOrderCarNum2" + e.getMessage());
            return ServerResponse.createError("指派车牌号失败");
        } finally {
            clientSession.close();
        }

        //todo:手工下单成功后要推送信息给司机
        CustomerUser cUser = customerUserService.getByPK(cid);
        if (StrUtil.isNotEmpty(goods.getBeginPoint()) && StrUtil.isNotEmpty(goods.getEndPoint())) {     //面议没有起点和终点，不需要推送
            String mes = cUser.getMobile() + "发布" + goods.getTradeName() + "从" + goods.getBeginPoint() + "运输到" + goods.getEndPoint();
            pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
        } else {
            String mes = cUser.getMobile() + "发布的面议单";
            pushInfoService.addAndSendPushInfo("派单", mes, driverInfo.getObjectId().toString(), 1, driverInfo.getDeviceId());
        }

        return ServerResponse.createSuccess("订单指派车牌号成功");
    }

    @Override
    public ServerResponse<List<DriverToCar>> searchDvrByCarNumOrMobile(String mobile, String carNum) {
        List<DriverToCar> driverToCars;
        if (StrUtil.isNotEmpty(mobile)) {
            Query<DriverToCar> query = driverToCarDao.createQuery();
            query.filter("mobile", mobile);
            query.filter("delete", 0);  //司机成功关联的车辆 可 被用于客商指定下单
            driverToCars = driverToCarDao.list(query);
        } else if (StrUtil.isNotEmpty(carNum)) {
            Query<DriverToCar> query = driverToCarDao.createQuery();
            query.filter("carNum", carNum);
            query.filter("delete", 0);  //司机成功关联的车辆 可 被用于客商指定下单
            driverToCars = driverToCarDao.list(query);
        } else {
            return ServerResponse.createError("参数错误");
        }
        List<DriverToCar> result = new ArrayList<>();
        for (DriverToCar dtc : driverToCars) {
            DriverInfo driverInfo = driverInfoService.getByPK(dtc.getDriverId());
            //司机未上传个人信息未审核的，不显示
            if (StrUtil.isEmpty(driverInfo.getState()) || driverInfo.getState() == -1) continue;
            //司机审核未通过的，不显示
            if (driverInfo.getState() == 0 || driverInfo.getState() == 2 || driverInfo.getState() == 3) continue;
            Car car = carDao.getByPK(dtc.getCarId());
            //车辆审核未通过的，不显示
            if (car.getVerify() == 0 || car.getVerify() == 2 || car.getVerify() == 4) continue;

            //if (StrUtil.isEmpty(dtc.getMobile()))
            //  dtc.setMobile(driverInfo.getMobile() == null ? "" : driverInfo.getMobile());
            dtc.setMobile(driverInfo.getMobile() == null ? dtc.getMobile() : driverInfo.getMobile());
            if (StrUtil.isEmpty(dtc.getCarNum())) dtc.setCarNum(car.getCarNum() == null ? "" : car.getCarNum());
            if (StrUtil.isEmpty(dtc.getName()))
                dtc.setName(driverInfo.getName() == null ? "" : driverInfo.getName());

            result.add(dtc);
        }
        return ServerResponse.createSuccess("查询成功", result);
    }

    @Override
    public Document createPayDoc(Order order, String payerId, int cusPayeeFee, String carNum, String driverId, Date date, String transactionId, String outTradeNo) {
        Goods goods = goodsService.get("gid", order.getGid());
        Map<String, Object> orderConMap = new HashMap<>();

        //平台需要收取 信息费(发货和收货单位的信息费) ，以及支付费用的人id
        if (goods.getFeesOut() > 0 && order.getFeesOut() == 0) {
            orderConMap.put("feesOut", goods.getFeesOut());
            orderConMap.put("payerIdOut", payerId);
        }
        if (goods.getFeesIn() > 0 && order.getFeesIn() == 0) {
            orderConMap.put("geesIn", goods.getFeesIn());
            orderConMap.put("payerIdIn", payerId);
        }
        //平台代替 收、发货一级和二级单位 要求收取的费用 ，以及支付费用的人id
        if (goods.getFees1In() > 0 && order.getFees1In() == 0) {
            orderConMap.put("fees1In", goods.getFees1In());
            orderConMap.put("payerId1In", payerId);
        }
        if (goods.getFees1Out() > 0 && order.getFees1Out() == 0) {
            orderConMap.put("fees1Out", goods.getFees1Out());
            orderConMap.put("payerId1Out", payerId);
        }
        if (goods.getFees2In() > 0 && order.getFees2In() == 0) {
            orderConMap.put("fees2In", goods.getFees2In());
            orderConMap.put("payerId2In", payerId);
        }
        if (goods.getFees2Out() > 0 && order.getFees2Out() == 0) {
            orderConMap.put("fees2Out", goods.getFees2Out());
            orderConMap.put("payerId2Out", payerId);
        }
        //平台代替 客商或友商 要求收取的费用 ，以及支付费用的人id
        if (cusPayeeFee > 0) {
            orderConMap.put("shareFees", cusPayeeFee);
            orderConMap.put("driverPayerId", payerId);
        }

        String userType = SysConstants.UserType.UT_WECHAT.toString();
        DriverInfo driverInfo = driverInfoService.getByPK(driverId);
        if (driverInfo != null) userType = SysConstants.UserType.UT_DRIVER.toString();
        int[] tempFee = orderRecordService.getPTFee(goods, driverId, userType);
        int ptFees = tempFee[0] + tempFee[1];//司机接单应付平台费用
        int balanceFees = ptFees - order.getFeesOut() - order.getFeesIn() - order.getFees();//客商或单位代付平台金额不足，司机补
        //司机实际支付的费用，以及司机id
        if (balanceFees > 0) {
            orderConMap.put("balanceFees", balanceFees);
            orderConMap.put("driverPayerId", payerId);
        }
        orderConMap.put("updateTime", date.getTime());
        if (StrUtil.isNotEmpty(carNum)) orderConMap.put("carNum", carNum);
        if (StrUtil.isNotEmpty(transactionId)) orderConMap.put("transactionId", transactionId);//微信订单号
        if (StrUtil.isNotEmpty(outTradeNo)) orderConMap.put("outTradeNo", outTradeNo);//商户订单号
        orderConMap.put("locked", false);   //放开订单锁

        Map<String, Object> updMap = new HashMap<>();
        updMap.put("$set", orderConMap);

        return Document.parse(JSONObject.toJSONString(updMap));
    }

    @Override
    public ServerResponse<EGridResult> updateOrderIsTransport(String[] oids, Integer isTransport) {

        Query<Order> query1 = this.createQuery();
        query1.filter("oid in", oids);
        query1.filter("delete", false);
        List<Order> orders = this.list(query1);

        int sum = 0;
        for (Order order : orders) {
            OrderTaking ot = orderTakingService.get("oid", order.getOid());
            if (ot == null || ot.getFinishTag() < 2) {
                UpdateOperations<Order> updateOperations = this.createUpdateOperations();
                updateOperations.set("isTransport", isTransport);
                Query<Order> query = this.createQuery();
                query.filter("oid", order.getOid());
                query.filter("updateTime", order.getUpdateTime());
                this.update(query, updateOperations);
                sum++;
            }
        }

        String str = isTransport == 0 ? "恢复" : "禁止";
        return ServerResponse.createSuccess(sum + "个订单，" + str + "运输，成功");
    }

    @Override
    public ServerResponse<List<OrderStatistics>> statisticsOrder(Long startTime, Long endTime) {
        String cid = ShiroUtils.getUserId();

        /*startTime = IdUnit.weeHours(startTime, "00:00:00", 0);
        if (StrUtil.isNotEmpty(endTime))
            endTime = IdUnit.weeHours(endTime, "00:00:00", 1);*/

        /*List<Goods> goods = goodsService.list(goodsService.createQuery().filter("cid", cid));
        List<String> gids = new ArrayList<>();
        for (Goods g : goods) {
            gids.add(g.getGid());
        }*/
        Date startTime1 = new Date(startTime);

        Query<Order> query = this.createQuery();
        query.criteria("createTime").greaterThanOrEq(startTime1);
        if (StrUtil.isNotEmpty(endTime)) {
            Date endTime1 = new Date(endTime);
            query.criteria("createTime").lessThan(endTime1);
        }
        /*query.criteria("updateTime").greaterThanOrEq(startTime);
        if (StrUtil.isNotEmpty(endTime)) query.criteria("updateTime").lessThan(endTime);*/
        query.or(
                query.criteria("cid").equal(cid),
                query.criteria("shareCid").equal(cid)
        );
        query.criteria("gid").notEqual(null);

        AggregationPipeline pipeline = this.createAggregation();
        pipeline.match(query);
        pipeline.group(
                id(grouping("mold"), grouping("tranStatus")),

                grouping("cid", first("cid")),
                grouping("mold", first("mold")),
                grouping("tranStatus", first("tranStatus")),
                grouping("numberOfCars", push("mold")),
                grouping("outGrossWeight", push("outGrossWeight")),
                grouping("outTareWeight", push("outTareWeight")),
                grouping("inGrossWeight", push("inGrossWeight")),
                grouping("inTareWeight", push("inTareWeight"))
        );

        Integer numberOfCars0_0 = 0;  //下单车数
        Integer numberOfCars1_0 = 0;  //下单车数
        Integer numberOfCars2_0 = 0;  //下单车数
        Integer numberOfCars0_1 = 0;  //接单车数
        Integer numberOfCars1_1 = 0;  //接单车数
        Integer numberOfCars2_1 = 0;  //接单车数
        Integer numberOfCars0_2 = 0;  //入场车数
        Integer numberOfCars1_2 = 0;  //入场车数
        Integer numberOfCars2_2 = 0;  //入场车数
        Integer numberOfCars0_3 = 0;  //撤单车数
        Integer numberOfCars1_3 = 0;  //撤单车数
        Integer numberOfCars2_3 = 0;  //撤单车数
        BigDecimal outNetWeight0 = new BigDecimal(0.00);    //发货完成净重
        BigDecimal outNetWeight1 = new BigDecimal(0.00);    //发货完成净重
        BigDecimal outNetWeight2 = new BigDecimal(0.00);    //发货完成净重
        BigDecimal inNetWeight0 = new BigDecimal(0.00);     //收货完成净重
        BigDecimal inNetWeight1 = new BigDecimal(0.00);     //收货完成净重
        BigDecimal inNetWeight2 = new BigDecimal(0.00);     //收货完成净重
        Integer numberOfCars0_4 = 0;  //发货完成车数
        Integer numberOfCars1_4 = 0;  //发货完成车数
        Integer numberOfCars2_4 = 0;  //发货完成车数
        Iterator<OrderStatistics> iterator = pipeline.aggregate(OrderStatistics.class);
        while (iterator.hasNext()) {
            OrderStatistics os = iterator.next();

            //按物流模式类型统计
            if (os.getMold() == 0) {
                numberOfCars0_0 = numberOfCars0_0 + os.getNumberOfCars().size();
                switch (os.getTranStatus()) {
                    case 1:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        break;
                    case 2:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_2 = numberOfCars0_2 + os.getNumberOfCars().size();
                        break;
                    case 3:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_2 = numberOfCars0_2 + os.getNumberOfCars().size();
                        break;
                    case 4:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_2 = numberOfCars0_2 + os.getNumberOfCars().size();
                        break;
                    case 5:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_4 = numberOfCars0_4 + os.getNumberOfCars().size();
                        if (os.getMold() == 0) {
                            outNetWeight0 = outNetWeight0.add(os.getOutNetWeight());
                        } else if (os.getMold() == 1) {
                            inNetWeight0 = inNetWeight0.add(os.getInNetWeight());
                        } else {
                            outNetWeight0 = outNetWeight0.add(os.getOutNetWeight());
                            inNetWeight0 = inNetWeight0.add(os.getInNetWeight());
                        }
                        break;
                    case 6:
                        numberOfCars0_3 = numberOfCars0_3 + os.getNumberOfCars().size();
                        break;
                    case 7:
                        numberOfCars0_3 = numberOfCars0_3 + os.getNumberOfCars().size();
                        break;
                    default:
                        break;
                }
            } else if (os.getMold() == 1) {
                numberOfCars1_0 = numberOfCars1_0 + os.getNumberOfCars().size();
                switch (os.getTranStatus()) {
                    case 1:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        break;
                    case 2:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_2 = numberOfCars1_2 + os.getNumberOfCars().size();
                        break;
                    case 3:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_2 = numberOfCars1_2 + os.getNumberOfCars().size();
                        break;
                    case 4:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_2 = numberOfCars1_2 + os.getNumberOfCars().size();
                        break;
                    case 5:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_4 = numberOfCars1_4 + os.getNumberOfCars().size();
                        if (os.getMold() == 0) {
                            outNetWeight1 = outNetWeight1.add(os.getOutNetWeight());
                        } else if (os.getMold() == 1) {
                            inNetWeight1 = inNetWeight1.add(os.getInNetWeight());
                        } else {
                            outNetWeight1 = outNetWeight1.add(os.getOutNetWeight());
                            inNetWeight1 = inNetWeight1.add(os.getInNetWeight());
                        }
                        break;
                    case 6:
                        numberOfCars1_3 = numberOfCars1_3 + os.getNumberOfCars().size();
                        break;
                    case 7:
                        numberOfCars1_3 = numberOfCars1_3 + os.getNumberOfCars().size();
                        break;
                    default:
                        break;
                }
            } else if (os.getMold() == 2) {
                numberOfCars2_0 = numberOfCars2_0 + os.getNumberOfCars().size();
                switch (os.getTranStatus()) {
                    case 1:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        break;
                    case 2:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_2 = numberOfCars2_2 + os.getNumberOfCars().size();
                        break;
                    case 3:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_2 = numberOfCars2_2 + os.getNumberOfCars().size();
                        break;
                    case 4:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_2 = numberOfCars2_2 + os.getNumberOfCars().size();
                        break;
                    case 5:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_4 = numberOfCars2_4 + os.getNumberOfCars().size();
                        if (os.getMold() == 0) {
                            outNetWeight2 = outNetWeight2.add(os.getOutNetWeight());
                        } else if (os.getMold() == 1) {
                            inNetWeight2 = inNetWeight2.add(os.getInNetWeight());
                        } else {
                            outNetWeight2 = outNetWeight2.add(os.getOutNetWeight());
                            inNetWeight2 = inNetWeight2.add(os.getInNetWeight());
                        }
                        break;
                    case 6:
                        numberOfCars2_3 = numberOfCars2_3 + os.getNumberOfCars().size();
                        break;
                    case 7:
                        numberOfCars2_3 = numberOfCars2_3 + os.getNumberOfCars().size();
                        break;
                    default:
                        break;
                }
            }
        }
        OrderStatistics result0 = new OrderStatistics(0, numberOfCars0_0, numberOfCars0_1, numberOfCars0_2, numberOfCars0_3, outNetWeight0, inNetWeight0, numberOfCars0_4);
        OrderStatistics result1 = new OrderStatistics(1, numberOfCars1_0, numberOfCars1_1, numberOfCars1_2, numberOfCars1_3, outNetWeight0, inNetWeight0, numberOfCars1_4);
        OrderStatistics result2 = new OrderStatistics(2, numberOfCars2_0, numberOfCars2_1, numberOfCars2_2, numberOfCars2_3, outNetWeight0, inNetWeight0, numberOfCars2_4);
        List<OrderStatistics> result = new ArrayList<>();
        result.add(result0);
        result.add(result1);
        result.add(result2);
        return ServerResponse.createSuccess("查询成功", result);
    }

    @Override
    public ServerResponse<List<OrderStatistics>> statisticsOrder2(Long startTime, Long endTime) {
        String cid = ShiroUtils.getUserId();

        Date startTime1 = new Date(startTime);

        Query<Order> query = this.createQuery();
        query.criteria("createTime").greaterThanOrEq(startTime1);
        if (StrUtil.isNotEmpty(endTime)) {
            Date endTime1 = new Date(endTime);
            query.criteria("createTime").lessThan(endTime1);
        }
        query.or(
                query.criteria("cid").equal(cid),
                query.criteria("shareCid").equal(cid)
        );
        query.criteria("gid").notEqual(null);

        AggregationPipeline pipeline = this.createAggregation();
        pipeline.match(query);
        pipeline.group(
                id(grouping("mold"), grouping("tranStatus")),

                grouping("cid", first("cid")),
                grouping("mold", first("mold")),
                grouping("tranStatus", first("tranStatus")),
                grouping("numberOfCars", push("mold")),
                grouping("outGrossWeight7", sum("outGrossWeight7")),
                grouping("outTareWeight7", sum("outTareWeight7")),
                grouping("inGrossWeight7", sum("inGrossWeight7")),
                grouping("inTareWeight7", sum("inTareWeight7"))
        );

        Integer numberOfCars0_0 = 0;  //下单车数
        Integer numberOfCars1_0 = 0;  //下单车数
        Integer numberOfCars2_0 = 0;  //下单车数
        Integer numberOfCars0_1 = 0;  //接单车数
        Integer numberOfCars1_1 = 0;  //接单车数
        Integer numberOfCars2_1 = 0;  //接单车数
        Integer numberOfCars0_2 = 0;  //入场车数
        Integer numberOfCars1_2 = 0;  //入场车数
        Integer numberOfCars2_2 = 0;  //入场车数
        Integer numberOfCars0_3 = 0;  //撤单车数
        Integer numberOfCars1_3 = 0;  //撤单车数
        Integer numberOfCars2_3 = 0;  //撤单车数
        BigDecimal outNetWeight0 = new BigDecimal(0.00);    //发货完成净重
        BigDecimal outNetWeight1 = new BigDecimal(0.00);    //发货完成净重
        BigDecimal outNetWeight2 = new BigDecimal(0.00);    //发货完成净重
        BigDecimal inNetWeight0 = new BigDecimal(0.00);     //收货完成净重
        BigDecimal inNetWeight1 = new BigDecimal(0.00);     //收货完成净重
        BigDecimal inNetWeight2 = new BigDecimal(0.00);     //收货完成净重
        Integer numberOfCars0_4 = 0;  //发货完成车数
        Integer numberOfCars1_4 = 0;  //发货完成车数
        Integer numberOfCars2_4 = 0;  //发货完成车数
        Iterator<OrderStatistics2> iterator = pipeline.aggregate(OrderStatistics2.class);
        while (iterator.hasNext()) {
            OrderStatistics2 os = iterator.next();

            //按物流模式类型统计
            if (os.getMold() == 0) {
                numberOfCars0_0 = numberOfCars0_0 + os.getNumberOfCars().size();
                switch (os.getTranStatus()) {
                    case 1:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        break;
                    case 2:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_2 = numberOfCars0_2 + os.getNumberOfCars().size();
                        break;
                    case 3:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_2 = numberOfCars0_2 + os.getNumberOfCars().size();
                        break;
                    case 4:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_2 = numberOfCars0_2 + os.getNumberOfCars().size();
                        break;
                    case 5:
                        numberOfCars0_1 = numberOfCars0_1 + os.getNumberOfCars().size();
                        numberOfCars0_4 = numberOfCars0_4 + os.getNumberOfCars().size();
                        if (os.getMold() == 0) {
                            outNetWeight0 = outNetWeight0.add(os.getOutNetWeight());
                        } else if (os.getMold() == 1) {
                            inNetWeight0 = inNetWeight0.add(os.getInNetWeight());
                        } else {
                            outNetWeight0 = outNetWeight0.add(os.getOutNetWeight());
                            inNetWeight0 = inNetWeight0.add(os.getInNetWeight());
                        }
                        break;
                    case 6:
                        numberOfCars0_3 = numberOfCars0_3 + os.getNumberOfCars().size();
                        break;
                    case 7:
                        numberOfCars0_3 = numberOfCars0_3 + os.getNumberOfCars().size();
                        break;
                    default:
                        break;
                }
            } else if (os.getMold() == 1) {
                numberOfCars1_0 = numberOfCars1_0 + os.getNumberOfCars().size();
                switch (os.getTranStatus()) {
                    case 1:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        break;
                    case 2:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_2 = numberOfCars1_2 + os.getNumberOfCars().size();
                        break;
                    case 3:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_2 = numberOfCars1_2 + os.getNumberOfCars().size();
                        break;
                    case 4:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_2 = numberOfCars1_2 + os.getNumberOfCars().size();
                        break;
                    case 5:
                        numberOfCars1_1 = numberOfCars1_1 + os.getNumberOfCars().size();
                        numberOfCars1_4 = numberOfCars1_4 + os.getNumberOfCars().size();
                        if (os.getMold() == 0) {
                            outNetWeight1 = outNetWeight1.add(os.getOutNetWeight());
                        } else if (os.getMold() == 1) {
                            inNetWeight1 = inNetWeight1.add(os.getInNetWeight());
                        } else {
                            outNetWeight1 = outNetWeight1.add(os.getOutNetWeight());
                            inNetWeight1 = inNetWeight1.add(os.getInNetWeight());
                        }
                        break;
                    case 6:
                        numberOfCars1_3 = numberOfCars1_3 + os.getNumberOfCars().size();
                        break;
                    case 7:
                        numberOfCars1_3 = numberOfCars1_3 + os.getNumberOfCars().size();
                        break;
                    default:
                        break;
                }
            } else if (os.getMold() == 2) {
                numberOfCars2_0 = numberOfCars2_0 + os.getNumberOfCars().size();
                switch (os.getTranStatus()) {
                    case 1:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        break;
                    case 2:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_2 = numberOfCars2_2 + os.getNumberOfCars().size();
                        break;
                    case 3:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_2 = numberOfCars2_2 + os.getNumberOfCars().size();
                        break;
                    case 4:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_2 = numberOfCars2_2 + os.getNumberOfCars().size();
                        break;
                    case 5:
                        numberOfCars2_1 = numberOfCars2_1 + os.getNumberOfCars().size();
                        numberOfCars2_4 = numberOfCars2_4 + os.getNumberOfCars().size();
                        if (os.getMold() == 0) {
                            outNetWeight2 = outNetWeight2.add(os.getOutNetWeight());
                        } else if (os.getMold() == 1) {
                            inNetWeight2 = inNetWeight2.add(os.getInNetWeight());
                        } else {
                            outNetWeight2 = outNetWeight2.add(os.getOutNetWeight());
                            inNetWeight2 = inNetWeight2.add(os.getInNetWeight());
                        }
                        break;
                    case 6:
                        numberOfCars2_3 = numberOfCars2_3 + os.getNumberOfCars().size();
                        break;
                    case 7:
                        numberOfCars2_3 = numberOfCars2_3 + os.getNumberOfCars().size();
                        break;
                    default:
                        break;
                }
            }
        }
        OrderStatistics result0 = new OrderStatistics(0, numberOfCars0_0, numberOfCars0_1, numberOfCars0_2, numberOfCars0_3, outNetWeight0, inNetWeight0, numberOfCars0_4);
        OrderStatistics result1 = new OrderStatistics(1, numberOfCars1_0, numberOfCars1_1, numberOfCars1_2, numberOfCars1_3, outNetWeight0, inNetWeight0, numberOfCars1_4);
        OrderStatistics result2 = new OrderStatistics(2, numberOfCars2_0, numberOfCars2_1, numberOfCars2_2, numberOfCars2_3, outNetWeight0, inNetWeight0, numberOfCars2_4);
        List<OrderStatistics> result = new ArrayList<>();
        result.add(result0);
        result.add(result1);
        result.add(result2);
        return ServerResponse.createSuccess("查询成功", result);
    }

    @Override
    public ServerResponse<List<String>> updateOrdersInNetWeight(String oid, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime) {
        Query<Order> query1 = this.createQuery();
        query1.filter("oid", oid);
        Order order = this.get(query1);
        if (order.getMold() != 1) return ServerResponse.createError("仅收货业务支持修改对方的净重");
        if (StrUtil.isNotEmpty(order.getShare()) && order.getShare() == 1)
            return ServerResponse.createError("已分享，先回收再录入净重");

        if (order.getInChecking() == 0) {
            UpdateOperations<Order> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(inNetWeight)) {
                updateOperations.set("inNetWeight", inNetWeight);
                updateOperations.set("inNetWeightPerson", "CU");
            }
            if (StrUtil.isNotEmpty(loadPound)) {
                updateOperations.set("loadPound", loadPound);
                updateOperations.set("loadPoundPerson", "CU");
            }
            if (StrUtil.isNotEmpty(loadTime)) {
                updateOperations.set("loadTime", loadTime);
                updateOperations.set("loadTimePerson", "CU");
            }
            if (StrUtil.isNotEmpty(loadPoundPho)) {
                updateOperations.set("loadPoundPho", loadPoundPho);
                updateOperations.set("loadPoundPhoPerson", "CU");
            }
            Query<Order> query = this.createQuery();
            query.filter("oid", order.getOid());
            query.filter("inChecking", 0);
            query.filter("updateTime", order.getUpdateTime());
            UpdateResults result = this.update(query, updateOperations);
            if (result != null && result.getUpdatedCount() > 0) {
                return ServerResponse.createSuccess("订单修改成功");
            } else {
                return ServerResponse.createError("订单修改失败");
            }
        } else {
            return ServerResponse.createError("车辆已入场，修改失败");
        }
    }

    @Override
    @Async("busTaskExecutor")
    public void supervisoryElectVoucherContPosition(Order order, DriverInfo driverInfo, String carNum, String accessCode) {
        // 正式环境地址
        String url = "http://************:8866/track/supervisory_elect_voucher_cont_position";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("AccessCode", accessCode);
        paramMap.put("ContractCode", order.getOutBizContractCode());
        paramMap.put("VoucherNo", order.getOid());
        paramMap.put("VehiclePlate", carNum);
        paramMap.put("CustomerName", order.getOutTradingUnitName());
        paramMap.put("ProductName", order.getOutVariety());
        paramMap.put("ProductID", order.getOutVarietyCode());
        paramMap.put("DriverName", driverInfo.getName());
        paramMap.put("DriverPhone", driverInfo.getMobile());
        paramMap.put("Longitude", "109.968418");
        paramMap.put("Latitude", "39.821256");
        paramMap.put("CipcherText", order.getOid());

        try {
            String jsonStr = JSONObject.toJSONString(paramMap);
            HttpResult result = httpAPIService.sendPost(url, jsonStr);
            Integer code = result.getCode();
            String body = result.getBody();

            if (200 == code) {
                JSONObject object = JSONObject.parseObject(body);
                boolean ok = object.getBoolean("ok");
                if (ok) {
                    // 返回成功，则order的 CipcherText 字段添加值。
                    String cipcherText = object.getString("result");
                    Query<Order> query = this.createQuery();
                    query.criteria("oid").equal(order.getOid());
                    UpdateOperations<Order> updateOperations = this.createUpdateOperations();
                    updateOperations.set("cipcherText", cipcherText);
                    this.update(query, updateOperations);
                } else {
                    logger.error("supervisoryElectVoucherContPosition失败: " + object.getString("result"));
                }
            } else {
                logger.error("supervisoryElectVoucherContPosition失败: [code:" + code + " ,body:" + body);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Override
    @Async("busTaskExecutor")   // 异步执行和智慧能源系统的交互
    public void coalDeliveryNoteStatusUpdate(Order order, Integer checking) {
        SysUnit outSubUnit = sysUnitService.get("code", order.getOutDefaultDownUnit());
        String accessCode = outSubUnit.getSmartEnergyAccessCode();
        if (StrUtil.isNotEmpty(accessCode)) {
            String status = null;
            if (order.getOutChecking() == 0 && checking == 1) {
                status = "3";
            } else if (order.getOutChecking() != 3 && checking == 3) {
                status = "4";
            }
            if (StrUtil.isNotEmpty(status)) {
                // 正式环境地址
                String url = "http://************:8866/track/coal_delivery_note_status_update";

                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("AccessCode", accessCode);
                paramMap.put("ContractCode", order.getOutBizContractCode());
                paramMap.put("VoucherNo", order.getOid());
                paramMap.put("VehiclePlate", order.getCarNum());
                paramMap.put("QrcodeText", order.getCipcherText());
                paramMap.put("Status", status);

                try {
                    String jsonStr = JSONObject.toJSONString(paramMap);
                    HttpResult result = httpAPIService.sendPost(url, jsonStr);
                    Integer code = result.getCode();
                    String body = result.getBody();

                    // 返回失败，则日志打印失败原因
                    if (200 == code) {
                        JSONObject object = JSONObject.parseObject(body);
                        boolean ok = object.getBoolean("ok");
                        if (!ok) logger.error("coalDeliveryNoteStatusUpdate失败: " + object.getString("result"));
                    } else {
                        logger.error("coalDeliveryNoteStatusUpdate失败: [code:" + code + " ,body:" + body);
                    }
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
