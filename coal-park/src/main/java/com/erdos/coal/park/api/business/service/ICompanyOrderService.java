package com.erdos.coal.park.api.business.service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface ICompanyOrderService {
    //同步订单信息到企业系统
    //String synOrderMsg(String url, Map<String, Object> map, int type);
    CompletableFuture synOrderMsg(String url, Map<String, Object> map, int type);

    //查询企业系统 要求 代扣的 金额 ---- 查询企业收费
    Map<String, Object> queryCompanyCost(String url, Map<String, Object> map);

    //按订单billCode查询企业系统订单信息
    Map<String, Object> queryCompanyDelivery(String url, Map<String, Object> map);

    Map<String, Object> queryCompanyDeliveryPound(String url, Map<String, Object> map);

//    Map<String, Object> queryCompanyRevokeDeliveryOrder(String url, Map<String, Object> map);
    CompletableFuture queryCompanyRevokeDeliveryOrder(String url, Map<String, Object> map);
}
