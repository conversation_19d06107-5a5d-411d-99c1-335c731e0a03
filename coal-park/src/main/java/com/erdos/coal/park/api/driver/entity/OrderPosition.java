package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.driver.pojo.Position;
import dev.morphia.annotations.*;
import dev.morphia.geo.Geometry;
import dev.morphia.utils.IndexType;


@Entity(value = "t_order_position", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("oid")}, options = @IndexOptions(unique = true)),
        @Index(fields = {@Field(value = "geometry_out", type = IndexType.GEO2DSPHERE)}),
        @Index(fields = {@Field(value = "geometry_in", type = IndexType.GEO2DSPHERE)}),
        @Index(fields = {@Field(value = "geometry_c1_out", type = IndexType.GEO2DSPHERE)}),
        @Index(fields = {@Field(value = "geometry_c3_out", type = IndexType.GEO2DSPHERE)}),
        @Index(fields = {@Field(value = "geometry_c1_in", type = IndexType.GEO2DSPHERE)}),
        @Index(fields = {@Field(value = "geometry_c3_in", type = IndexType.GEO2DSPHERE)})
})
public class OrderPosition extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "_oid", unique = true, background = true))
    private String oid;     //订单号
    private String outBillCode; //票号
    private String inBillCode;  //票号
    private String carNum;

    //司机发货单位签到时记录车辆的GPS
    private Position position_out;
    private Geometry geometry_out;//点（Point）
    //司机收货单位签到时记录车辆的GPS
    private Position position_in;
    private Geometry geometry_in;//点（Point）
    //司机发货单位checking=1 时记录车辆的GPS
    private Position position_c1_out;
    private Geometry geometry_c1_out;
    //司机发货单位checking=3 时记录车辆的GPS
    private Position position_c3_out;
    private Geometry geometry_c3_out;
    //司机收货单位checking=1 时记录车辆的GPS
    private Position position_c1_in;
    private Geometry geometry_c1_in;
    //司机收货单位checking=3 时记录车辆的GPS
    private Position position_c3_in;
    private Geometry geometry_c3_in;

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getOutBillCode() {
        return outBillCode;
    }

    public void setOutBillCode(String outBillCode) {
        this.outBillCode = outBillCode;
    }

    public String getInBillCode() {
        return inBillCode;
    }

    public void setInBillCode(String inBillCode) {
        this.inBillCode = inBillCode;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Position getPosition_out() {
        return position_out;
    }

    public void setPosition_out(Position position_out) {
        this.position_out = position_out;
    }

    public Geometry getGeometry_out() {
        return geometry_out;
    }

    public void setGeometry_out(Geometry geometry_out) {
        this.geometry_out = geometry_out;
    }

    public Position getPosition_in() {
        return position_in;
    }

    public void setPosition_in(Position position_in) {
        this.position_in = position_in;
    }

    public Geometry getGeometry_in() {
        return geometry_in;
    }

    public void setGeometry_in(Geometry geometry_in) {
        this.geometry_in = geometry_in;
    }

    public Position getPosition_c1_out() {
        return position_c1_out;
    }

    public void setPosition_c1_out(Position position_c1_out) {
        this.position_c1_out = position_c1_out;
    }

    public Geometry getGeometry_c1_out() {
        return geometry_c1_out;
    }

    public void setGeometry_c1_out(Geometry geometry_c1_out) {
        this.geometry_c1_out = geometry_c1_out;
    }

    public Position getPosition_c3_out() {
        return position_c3_out;
    }

    public void setPosition_c3_out(Position position_c3_out) {
        this.position_c3_out = position_c3_out;
    }

    public Geometry getGeometry_c3_out() {
        return geometry_c3_out;
    }

    public void setGeometry_c3_out(Geometry geometry_c3_out) {
        this.geometry_c3_out = geometry_c3_out;
    }

    public Position getPosition_c1_in() {
        return position_c1_in;
    }

    public void setPosition_c1_in(Position position_c1_in) {
        this.position_c1_in = position_c1_in;
    }

    public Geometry getGeometry_c1_in() {
        return geometry_c1_in;
    }

    public void setGeometry_c1_in(Geometry geometry_c1_in) {
        this.geometry_c1_in = geometry_c1_in;
    }

    public Position getPosition_c3_in() {
        return position_c3_in;
    }

    public void setPosition_c3_in(Position position_c3_in) {
        this.position_c3_in = position_c3_in;
    }

    public Geometry getGeometry_c3_in() {
        return geometry_c3_in;
    }

    public void setGeometry_c3_in(Geometry geometry_c3_in) {
        this.geometry_c3_in = geometry_c3_in;
    }
}
