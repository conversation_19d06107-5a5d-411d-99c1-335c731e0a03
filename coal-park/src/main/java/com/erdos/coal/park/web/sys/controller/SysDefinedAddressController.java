package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.sys.pojo.DefinedAddressData;
import com.erdos.coal.park.web.sys.service.ISysDefinedAddressService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/sys/definedAddress")
public class SysDefinedAddressController extends BaseController {

    @Resource
    private ISysDefinedAddressService definedAddressService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(definedAddressService.loadGrid(page, rows));
    }

    @PostMapping("/add_address")
    public ServerResponse addHandler(@RequestBody DefinedAddressData address) throws GlobalException {
        return definedAddressService.addAddress(address);
    }

    @PostMapping("/edit_address")
    public ServerResponse editHandler(@RequestBody DefinedAddressData address) throws GlobalException {
        return definedAddressService.editAddress(address);
    }

    @PostMapping("/del_address")
    public ServerResponse delHandler(@RequestBody DefinedAddressData address) throws GlobalException {
        return definedAddressService.delAddress(address);
    }
}
