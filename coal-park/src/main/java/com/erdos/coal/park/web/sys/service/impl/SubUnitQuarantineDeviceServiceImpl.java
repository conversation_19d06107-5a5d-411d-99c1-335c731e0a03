package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.sys.dao.ISubUnitQuarantineDeviceDao;
import com.erdos.coal.park.web.sys.entity.SubUnitQuarantineDevice;
import com.erdos.coal.park.web.sys.service.ISubUnitQuarantineDeviceService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Service("subUnitQuarantineDeviceService")
public class SubUnitQuarantineDeviceServiceImpl extends BaseMongoServiceImpl<SubUnitQuarantineDevice, ISubUnitQuarantineDeviceDao> implements ISubUnitQuarantineDeviceService {
    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult loadQuarantineDeviceGrid(Integer page, Integer rows) {
        Query<SubUnitQuarantineDevice> query = this.createQuery();

        if (StrUtil.isNotEmpty(request.getParameter("subCode")))
            query.criteria("subCode").startsWithIgnoreCase(request.getParameter("subCode"));
        if (StrUtil.isNotEmpty(request.getParameter("subName")))
            query.criteria("subName").startsWithIgnoreCase(request.getParameter("subName"));
        if (StrUtil.isNotEmpty(request.getParameter("deviceNo")))
            query.criteria("deviceNo").startsWithIgnoreCase(request.getParameter("deviceNo"));

        query.order(Sort.ascending("subCode"), Sort.ascending("deviceNo"));

        return findPage(page, rows, query);
    }

    @Override
    public ServerResponse<String> deleteQuarantineDevice() {
        String id = request.getParameter("id");
        this.findAndDelete(this.createQuery().filter("id", id));
        return ServerResponse.createSuccess("删除成功");
    }

    @Override
    public ServerResponse<String> addQuarantineDevice(SubUnitQuarantineDevice device) {
        device.setId(Utils.getUUID());
        if (StrUtil.isEmpty(device.getDeviceNo())) return ServerResponse.createError("设备号不能为空");
        if (StrUtil.isEmpty(device.getSubCode())) return ServerResponse.createError("请选择关联的二级单位");

        Query<SubUnitQuarantineDevice> query = this.createQuery();
        query.filter("deviceNo", device.getDeviceNo());
        SubUnitQuarantineDevice device1 = this.get(query);
        if (device1 != null) return ServerResponse.createError("设备号已存在");

        this.save(device);
        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> editQuarantineDevice(SubUnitQuarantineDevice device) {
        Query<SubUnitQuarantineDevice> query = this.createQuery();
        query.filter("id", device.getId());

        SubUnitQuarantineDevice oldDevice = this.get(query);
        if (oldDevice == null) return ServerResponse.createError("终端设备不存在");

        UpdateOperations<SubUnitQuarantineDevice> updateOperations = this.createUpdateOperations();
        if (StrUtil.isNotEmpty(device.getDeviceNo()) && !device.getDeviceNo().equals(oldDevice.getDeviceNo())) {
            Query<SubUnitQuarantineDevice> noQuery = this.createQuery();
            noQuery.filter("deviceNo", device.getDeviceNo());
            SubUnitQuarantineDevice device1 = this.get(noQuery);
            if (device1 != null) return ServerResponse.createError("设备号已存在");

            updateOperations.set("deviceNo", device.getDeviceNo());
        }
        if (StrUtil.isNotEmpty(device.getSubCode()) && !device.getSubCode().equals(oldDevice.getSubCode()))
            updateOperations.set("subCode", device.getSubCode());
        if (StrUtil.isNotEmpty(device.getSubName()) && !device.getSubName().equals(oldDevice.getSubName()))
            updateOperations.set("subName", device.getSubName());
        if (StrUtil.isNotEmpty(device.getLocalDes()) && !device.getLocalDes().equals(oldDevice.getLocalDes()))
            updateOperations.set("localDes", device.getLocalDes());

        this.update(query, updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }
}
