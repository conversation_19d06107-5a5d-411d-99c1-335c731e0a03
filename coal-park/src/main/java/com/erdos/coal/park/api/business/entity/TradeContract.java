package com.erdos.coal.park.api.business.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.business.pojo.VehicleTypeCost;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

import java.util.List;

@Entity(value = "t_app_trade_contract", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("bizUnitCode")}),
        @Index(fields = {@Field("bizContractCode")})
})
public class TradeContract extends BaseMongoInfo {
    private boolean allowLimit;          //额度限定
    private boolean balanceCtrl;         //余额控制
    private String bizContractCode;     //业务合同编号
    private String bizContractName;     //业务合同名称
    private Integer bizModule;           //业务模型
    private Integer bizType;            //业务类型
    private String bizUnitCode;         //业务单位编号
    private String contractnum;         //合同号
    private Integer contractType;        //合同类型
    private String effeDate;            //生效日期
    private boolean kindChange;          //品种变换
    private Double limitAllow;          //限定额度
    private Double limitWeight;         //限定计量
    private Integer moneyType;           //结款类型
    private Double overdraft;           //透支额
    private Integer performState;        //履行状态
    private String subCode;             //二级单位编码
    private String termDate;            //终止日期
    private boolean ticketType;          //票管类型
    private Integer tradeType;           //交易性质
    private Double triggerBalance;      //触发余额
    private boolean triggerSwitch;       //触发开关
    private String unitCode;            //单位编码
    private String voucherEarlyProcess; //凭证预期处理
    private String voucherExpire;       //凭证有效日期
    private boolean weightLimit;         //计量限定

    private List<VehicleTypeCost> vehicleTypeCostList;

    private boolean vehicleFee;     //是否启用车型收费

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizContractCode() {
        return bizContractCode;
    }

    public void setBizContractCode(String bizContractCode) {
        this.bizContractCode = bizContractCode;
    }

    public String getBizContractName() {
        return bizContractName;
    }

    public void setBizContractName(String bizContractName) {
        this.bizContractName = bizContractName;
    }

    public String getBizUnitCode() {
        return bizUnitCode;
    }

    public void setBizUnitCode(String bizUnitCode) {
        this.bizUnitCode = bizUnitCode;
    }

    public String getContractnum() {
        return contractnum;
    }

    public void setContractnum(String contractnum) {
        this.contractnum = contractnum;
    }

    public Integer getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(Integer moneyType) {
        this.moneyType = moneyType;
    }

    public Integer getBizModule() {
        return bizModule;
    }

    public void setBizModule(Integer bizModule) {
        this.bizModule = bizModule;
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public Integer getPerformState() {
        return performState;
    }

    public void setPerformState(Integer performState) {
        this.performState = performState;
    }

    public Double getLimitAllow() {
        return limitAllow;
    }

    public void setLimitAllow(Double limitAllow) {
        this.limitAllow = limitAllow;
    }

    public Double getLimitWeight() {
        return limitWeight;
    }

    public void setLimitWeight(Double limitWeight) {
        this.limitWeight = limitWeight;
    }

    public Double getOverdraft() {
        return overdraft;
    }

    public void setOverdraft(Double overdraft) {
        this.overdraft = overdraft;
    }

    public Double getTriggerBalance() {
        return triggerBalance;
    }

    public void setTriggerBalance(Double triggerBalance) {
        this.triggerBalance = triggerBalance;
    }

    public String getEffeDate() {
        return effeDate;
    }

    public void setEffeDate(String effeDate) {
        this.effeDate = effeDate;
    }

    public String getTermDate() {
        return termDate;
    }

    public void setTermDate(String termDate) {
        this.termDate = termDate;
    }

    public String getVoucherExpire() {
        return voucherExpire;
    }

    public void setVoucherExpire(String voucherExpire) {
        this.voucherExpire = voucherExpire;
    }

    public String getVoucherEarlyProcess() {
        return voucherEarlyProcess;
    }

    public void setVoucherEarlyProcess(String voucherEarlyProcess) {
        this.voucherEarlyProcess = voucherEarlyProcess;
    }

    public boolean isTriggerSwitch() {
        return triggerSwitch;
    }

    public boolean getTriggerSwitch() {
        return triggerSwitch;
    }

    public void setTriggerSwitch(boolean triggerSwitch) {
        this.triggerSwitch = triggerSwitch;
    }

    public boolean isBalanceCtrl() {
        return balanceCtrl;
    }

    public boolean getBalanceCtrl() {
        return balanceCtrl;
    }

    public void setBalanceCtrl(boolean balanceCtrl) {
        this.balanceCtrl = balanceCtrl;
    }

    public boolean isAllowLimit() {
        return allowLimit;
    }

    public boolean getAllowLimit() {
        return allowLimit;
    }

    public void setAllowLimit(boolean allowLimit) {
        this.allowLimit = allowLimit;
    }

    public boolean isWeightLimit() {
        return weightLimit;
    }

    public boolean getWeightLimit() {
        return weightLimit;
    }

    public void setWeightLimit(boolean weightLimit) {
        this.weightLimit = weightLimit;
    }

    public boolean isTicketType() {
        return ticketType;
    }

    public boolean getTicketType() {
        return ticketType;
    }

    public void setTicketType(boolean ticketType) {
        this.ticketType = ticketType;
    }

    public boolean isKindChange() {
        return kindChange;
    }

    public boolean getKindChange() {
        return kindChange;
    }

    public void setKindChange(boolean kindChange) {
        this.kindChange = kindChange;
    }

    public List<VehicleTypeCost> getVehicleTypeCostList() {
        return vehicleTypeCostList;
    }

    public void setVehicleTypeCostList(List<VehicleTypeCost> vehicleTypeCostList) {
        this.vehicleTypeCostList = vehicleTypeCostList;
    }

    public boolean isVehicleFee() {
        return vehicleFee;
    }

    public void setVehicleFee(boolean vehicleFee) {
        this.vehicleFee = vehicleFee;
    }
}
