package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.List;

@Entity(value = "t_gid_did", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("gid")})
})
public class GidAndDid extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "_gid", background = true))
    private String gid;
    private List<String> did;
    private String cid;

    public String getGid() {
        return gid;
    }

    public List<String> getDid() {
        return did;
    }

    public void setDid(List<String> did) {
        this.did = did;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

}
