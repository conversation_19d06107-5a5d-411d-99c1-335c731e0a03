package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.domain.Participant;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.customer.dao.IRefundAliResultDao;
import com.erdos.coal.park.api.customer.dao.IRefundPreDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.RefundAliResult;
import com.erdos.coal.park.api.customer.entity.RefundPre;
import com.erdos.coal.park.api.customer.service.ICustomerAccountService;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IRefundPreService;
import com.erdos.coal.park.api.driver.entity.DriverAccount;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.WxPrepaid;
import com.erdos.coal.park.api.driver.entity.WxResult;
import com.erdos.coal.park.api.driver.service.IDriverAccountService;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IWxPrepaidService;
import com.erdos.coal.park.api.driver.service.IWxResultService;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.transaction.alipay.bean.AlipayConfig;
import com.erdos.coal.transaction.alipay.service.AliPayService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.transaction.wxpay.service.*;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("refundPreService")
public class RefundPreServiceImpl extends BaseMongoServiceImpl<RefundPre, IRefundPreDao> implements IRefundPreService {
    @Resource
    private MongoClient client;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private ICustomerAccountService customerAccountService;
    @Resource
    private IDriverAccountService driverAccountService;
    @Resource
    private WXPayCustomerConfig customerConfig;
    @Resource
    private WXPayDriverConfig driverConfig;
    @Resource
    private WXPayConstants wxPayConstants;
    @Resource
    private IWxPrepaidService wxPrepaidService;
    @Resource
    private IWxResultService wxResultService;
    @Resource
    private IHttpAPIService httpAPIService;

    @Resource
    private AlipayConfig alipayConfig;
    @Resource
    private AliPayService aliPayService;
    @Resource
    private IRefundAliResultDao refundAliResultDao;
    @Resource
    private WXPayWechatConfig wxPayWechatConfig;
    @Resource
    private WXPayWechatCusConfig wxPayWechatCusConfig;

    @Override
    public ServerResponse<String> getWxNickname() {
        String type = ShiroUtils.getUserType();
        String id = ShiroUtils.getUserId();
        String nickname;
        // if (SysConstants.UserType.UT_DRIVER.toString().equals(type)) {
        if (UserType.DU.toString().equals(type)) {
            nickname = driverInfoService.getByPK(id).getNickname();
        } else {
            nickname = customerUserService.getByPK(id).getNickname();
        }
        return ServerResponse.createSuccess("查询成功", nickname);
    }

    @Override
    public ServerResponse<String> bindWxOpenid(String code, String wxName) {
        String type = ShiroUtils.getUserType();
        String id = ShiroUtils.getUserId();
        assert id != null;

        //TODO 1.获取openid和access_token
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=";
        if (UserType.DU.toString().equals(type)) {
            url += WXPayConstants.dri_app_id + "&secret=" + WXPayConstants.dri_secret;
        } else {
            url += WXPayConstants.cus_app_id + "&secret=" + WXPayConstants.cus_secret;
        }
        url += "&code="
                + code
                + "&grant_type=authorization_code";//提现获取openId
        JSONObject info = null;
        try {
            info = JSON.parseObject(httpAPIService.doGet(url));
            logger.info("bindWxOpenid，绑定微信Openid，请求微信接口返回的结果：", info);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assert info != null;
        String openid = info.getString("openid");
        String access_token = info.getString("access_token");
        if (StrUtil.isEmpty(openid) || StrUtil.isEmpty(access_token))
            return ServerResponse.createError(info.getString("errcode") + info.getString("errmsg"));

        //TODO 2.获取微信昵称，并保存
        try {
            url = "https://api.weixin.qq.com/sns/userinfo?access_token=" + access_token + "&openid=" + openid + "&lang=zh_CN";
            info = JSON.parseObject(httpAPIService.doGet(url));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String nickname = info.getString("nickname");
        if (StrUtil.isEmpty(nickname))
            return ServerResponse.createError(info.getString("errcode") + info.getString("errmsg"));

        if (UserType.DU.toString().equals(type)) {
            Query<DriverInfo> query = driverInfoService.createQuery().filter("_id", new ObjectId(id));
            UpdateOperations<DriverInfo> updateOperations = driverInfoService.createUpdateOperations();
            updateOperations.set("openid", openid);
            updateOperations.set("nickname", nickname);
            updateOperations.set("wxName", wxName);
            driverInfoService.update(query, updateOperations);
        } else {
            Query<CustomerUser> query = customerUserService.createQuery().filter("_id", new ObjectId(id));
            UpdateOperations<CustomerUser> updateOperations = customerUserService.createUpdateOperations();
            updateOperations.set("openid", openid);
            updateOperations.set("nickname", nickname);
            updateOperations.set("wxName", wxName);
            customerUserService.update(query, updateOperations);
        }
        return ServerResponse.createSuccess("获取成功", nickname);
    }

    @Override
    @Transactional
    public ServerResponse<String> wxRefundApply(double amount) {
        if (amount > 5000) return ServerResponse.createError("单日单次提现金额不能超过5000元");
        String userId = ShiroUtils.getUserId();
        String userType = ShiroUtils.getUserType();
        assert userId != null;

        RefundPre refundPre = new RefundPre();

        BigDecimal bigDecimal;
        if (UserType.DU.toString().equals(userType)) {
            bigDecimal = driverAccountService.getAvailableFee(userId);

            DriverInfo driverInfo = driverInfoService.getByPK(userId);
            refundPre.setAccountNumber(driverInfo.getOpenid());
            refundPre.setNickname(driverInfo.getNickname());
            refundPre.setRemark("司机账户余额提现到微信零钱");
            refundPre.setName(driverInfo.getWxName());

        } else {
            bigDecimal = customerAccountService.getAvailableFee(userId);

            CustomerUser customerUser = customerUserService.getByPK(userId);
            refundPre.setAccountNumber(customerUser.getOpenid());
            refundPre.setNickname(customerUser.getNickname());
            refundPre.setRemark("客商账户余额提现到微信零钱");
            refundPre.setName(customerUser.getWxName());
        }

        BigDecimal fee = new BigDecimal(String.valueOf(amount)).multiply(new BigDecimal(100));
        if (fee.compareTo(bigDecimal) > 0)
            return ServerResponse.createError("提现金额应小于等于账户余额");

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> driAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> refundCollection = this.getCollection();

        Date date = new Date();
        long timeStart = date.getTime();
        String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
        try {
            clientSession.startTransaction();
            //TODO 1：扣除余额
            if (SysConstants.UserType.UT_CUSTOMER.toString().equals(userType)) {
                Document cAccountDoc = customerAccountService.createCusAccountDoc(userId, fee.negate(), 7, out_trade_no, null, date);
                cusAccountCollection.insertOne(clientSession, cAccountDoc);
            } else {
                DriverAccount dAccount = new DriverAccount();
                dAccount.setDid(userId);
                dAccount.setTotalFee(fee.negate());
                dAccount.setType(4);
                dAccount.setOutTradeNo(out_trade_no);
                dAccount.setUpdateTime(date.getTime());
                Document driDocument = Document.parse(JSONObject.toJSONString(dAccount));
                driDocument.append("createTime", date);

                driAccountCollection.insertOne(clientSession, driDocument);
            }

            refundPre.setUserId(userId);
            refundPre.setAmount(fee);//(fee.intValue());//提现金额
            refundPre.setTransferNo(out_trade_no);//订单号
            refundPre.setRefundType(1);//微信
            refundPre.setCheck(0);
            refundPre.setUpdateTime(date.getTime());
            Document refundDocument = Document.parse(JSONObject.toJSONString(refundPre));
            refundDocument.append("createTime", date);
            if (UserType.DU.toString().equals(userType)) {
                refundDocument.append("driverInfoID", new ObjectId(userId));
            } else {
                refundDocument.append("customerUserID", new ObjectId(userId));
            }
            //TODO 2：保存提现申请
            refundCollection.insertOne(clientSession, refundDocument);
            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("提现申请提交失败");
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("提现申请已提交");
    }

    @Override
    public ServerResponse<String> wxRefundApply(double amount, String openid, String name) {
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-ddHH:mm");
        String currentTime = sdf.format(now);

        if (amount > 5000) return ServerResponse.createError("单日单次提现金额不能超过5000元");
        String userId = ShiroUtils.getUserId();
        String userType = ShiroUtils.getUserType();
        assert userId != null;

        RefundPre refundPre = new RefundPre();

        BigDecimal bigDecimal;
        if (UserType.DU.toString().equals(userType)) {
            bigDecimal = driverAccountService.getAvailableFee(userId);
            refundPre.setAccountNumber(openid);
            refundPre.setRemark("司机账户余额提现到微信零钱");
            refundPre.setName(name);
        } else {
            bigDecimal = customerAccountService.getAvailableFee(userId);
            refundPre.setAccountNumber(openid);
            refundPre.setRemark("客商账户余额提现到微信零钱");
            refundPre.setName(name);
        }
        refundPre.setUserMin(userId+currentTime);

        BigDecimal fee = new BigDecimal(String.valueOf(amount)).multiply(new BigDecimal(100));
        if (fee.compareTo(bigDecimal) > 0)
            return ServerResponse.createError("提现金额应小于等于账户余额");

        ClientSession clientSession = client.startSession();
        MongoCollection<Document> cusAccountCollection = customerAccountService.getCollection();
        MongoCollection<Document> driAccountCollection = driverAccountService.getCollection();
        MongoCollection<Document> refundCollection = this.getCollection();

        Date date = new Date();
        long timeStart = date.getTime();
        String out_trade_no = timeStart + WXPayUtil.generateNonceStr(19);
        try {
            clientSession.startTransaction();
            //TODO 1：扣除余额
            if (SysConstants.UserType.UT_CUSTOMER.toString().equals(userType)) {
                Document cAccountDoc = customerAccountService.createCusAccountDoc(userId, fee.negate(), 7, out_trade_no, null, date);
                cusAccountCollection.insertOne(clientSession, cAccountDoc);
            } else {
                DriverAccount dAccount = new DriverAccount();
                dAccount.setDid(userId);
                dAccount.setTotalFee(fee.negate());
                dAccount.setType(4);
                dAccount.setOutTradeNo(out_trade_no);
                dAccount.setUpdateTime(date.getTime());
                Document driDocument = Document.parse(JSONObject.toJSONString(dAccount));
                driDocument.append("createTime", date);

                driAccountCollection.insertOne(clientSession, driDocument);
            }

            refundPre.setUserId(userId);
            refundPre.setAmount(fee);//(fee.intValue());//提现金额
            refundPre.setTransferNo(out_trade_no);//订单号
            refundPre.setRefundType(1);//微信
            refundPre.setCheck(0);
            refundPre.setUpdateTime(date.getTime());
            Document refundDocument = Document.parse(JSONObject.toJSONString(refundPre));
            refundDocument.append("createTime", date);
            if (UserType.DU.toString().equals(userType)) {
                refundDocument.append("driverInfoID", new ObjectId(userId));
            } else {
                refundDocument.append("customerUserID", new ObjectId(userId));
            }
            //TODO 2：保存提现申请
            refundCollection.insertOne(clientSession, refundDocument);
            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            return ServerResponse.createError("提现申请提交失败");
        } finally {
            clientSession.close();
        }
        return ServerResponse.createSuccess("提现申请已提交");
    }

    @Override
    public Map<String, String> wxRefundPass(String transferNo, RefundPre refundPre) throws Exception {
        if (refundPre != null && refundPre.getRefundType() == 1 && !"SUCCESS".equals(refundPre.getResultCode())) {
            WXPay wxpay = new WXPay(wxPayWechatCusConfig, wxPayConstants.notifyUrl, true, false);

            //1.0 拼凑企业支付需要的参数
            String appid = wxPayWechatCusConfig.getAppID();  //微信公众号的appid
            String mch_id = wxPayWechatCusConfig.getMchID(); //商户号
            if (refundPre.getDriverInfo() != null) {
                wxpay = new WXPay(wxPayWechatConfig, wxPayConstants.notifyUrl, true, false);
                appid = wxPayWechatConfig.getAppID();
                mch_id = wxPayWechatConfig.getMchID();
            }

            String nonce_str = WXPayUtil.generateNonceStr(32); //生成随机数
            InetAddress address = InetAddress.getLocalHost();
            //2.0 生成map集合
            Map<String, String> packageParams = new HashMap<>();
            packageParams.put("mch_appid", appid);         //微信公众号的appid
            packageParams.put("mchid", mch_id);       //商户号
            packageParams.put("nonce_str", nonce_str);  //随机生成后数字，保证安全性
            packageParams.put("partner_trade_no", refundPre.getTransferNo()); //生成商户订单号
            packageParams.put("openid", refundPre.getAccountNumber());            // 支付给用户openid
            packageParams.put("check_name", "FORCE_CHECK");    //是否验证真实姓名呢
            packageParams.put("re_user_name", refundPre.getName());//收款用户姓名
            packageParams.put("amount", String.valueOf(refundPre.getAmount()));            //企业付款金额，单位为分
            packageParams.put("desc", refundPre.getRemark());                   //企业付款操作说明信息。必填。
            packageParams.put("spbill_create_ip", address.getHostAddress()); //调用接口的机器Ip地址
            String sign;
            if (refundPre.getCustomerUser() != null) {
                sign = WXPayUtil.generateSignature(packageParams, wxPayWechatCusConfig.getKey(), WXPayConstants.SignType.MD5);
            } else {
                sign = WXPayUtil.generateSignature(packageParams, wxPayWechatConfig.getKey(), WXPayConstants.SignType.MD5);
            }
            packageParams.put("sign", sign);

            Map<String, String> result = wxpay.transfers(packageParams);
            return this.saveWxResult(packageParams, result);
        } else {
            Map<String, String> map = new HashMap<>();
            map.put("resultCode", "FAIL");
            return map;
        }
    }

    @Override
    public ServerResponse<List<RefundPreData>> queryRefund() {
        String id = ShiroUtils.getUserId();
        Query<RefundPre> query = this.createQuery();
        query.filter("userId", id);
        query.order(Sort.descending("createTime"));
        List<RefundPre> refundPreList = this.list(query);

        List<RefundPreData> list = new ArrayList<>();
        for (RefundPre refundPre : refundPreList) {
            RefundPreData refundPreData = new RefundPreData();
            BeanUtils.copyProperties(refundPre, refundPreData);
            if (refundPre.getDriverInfo() != null) {
                refundPreData.setUserType(0);
                refundPreData.setMobile(refundPre.getDriverInfo().getMobile());
            } else {
                refundPreData.setUserType(1);
                refundPreData.setMobile(refundPre.getCustomerUser().getMobile());
            }
            refundPreData.setAmount(refundPre.getAmount().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue());//refundPre.getAmount() * 1.0 / 100);
            list.add(refundPreData);
        }
        return ServerResponse.createSuccess("查询成功", list);
    }

    private void saveWxPrepaid(String tradeNo, Map<String, String> params, boolean pay) {
        WxPrepaid prepaid = wxPrepaidService.get("outTradeNo", tradeNo);
        if (prepaid == null) prepaid = new WxPrepaid();
        prepaid.setPay(pay);
        prepaid.setType(3);//提现
        prepaid.setTotalFee(Integer.parseInt(params.get("amount")));
        prepaid.setAppid(params.get("mch_appid"));
        prepaid.setMchId(params.get("mchid"));
        prepaid.setNonceStr(params.get("nonce_str"));
        prepaid.setOutTradeNo(tradeNo);
        prepaid.setOpenid(params.get("openid"));
        prepaid.setCheckName(params.get("check_name"));
        prepaid.setReUserName(params.get("re_user_name"));
        prepaid.setDesc(params.get("desc"));
        prepaid.setSign(params.get("sign"));
        prepaid.setSpbillCreateIp(params.get("spbill_create_ip"));
        prepaid.setUpdateTime(new Date().getTime());
        wxPrepaidService.save(prepaid);
    }

    private Map<String, String> saveWxResult(Map<String, String> params, Map<String, String> result) {
        Map<String, String> map = new HashMap<>();

        String tradeNo = params.get("partner_trade_no");
        //TODO 1.提现成功的直接返回，否则将提现参数和结果存入WxPrepaid,WxResult
        WxResult wxResult = wxResultService.get("outTradeNo", tradeNo);
        if (wxResult != null && "SUCCESS".equals(wxResult.getResultCode())) {
            map.put("resultCode", "SUCCESS");
            return map;
        }

        boolean pay = false;
        //支付结果表添加或修改数据
        if (wxResult == null) wxResult = new WxResult();
        wxResult.setTotalFee(Integer.parseInt(params.get("amount")));
        wxResult.setReturnCode(result.get("return_code"));
        wxResult.setOutTradeNo(tradeNo);
        if ("SUCCESS".equals(result.get("return_code"))) { //以下字段在return_code为SUCCESS的时候有返回
            wxResult.setMchAppid(result.get("mch_appid"));
            wxResult.setMchid(result.get("mchid"));
            wxResult.setDeviceInfo(result.get("device_info"));
            wxResult.setNonceStr(result.get("nonce_str"));
            wxResult.setResultCode(result.get("result_code"));
            wxResult.setErrCode(result.get("err_code"));
            wxResult.setErrCodeDes(result.get("err_code_des"));

            map.put("errCode", result.get("err_code"));
            map.put("errCodeDes", result.get("err_code_des"));
            map.put("resultCode", result.get("result_code"));
            if ("SUCCESS".equals(result.get("result_code"))) { //企业付款成功扣除customerAccount 和 driverAccount 账户余额
                logger.info(result.get("partner_trade_no") + "partner_trade_no");
                // wxResult.setOutTradeNo(result.get("partner_trade_no"));//商户订单号
                wxResult.setPaymentNo(result.get("payment_no"));//微信付款订单号
                wxResult.setPaymentTime(result.get("payment_time"));//付款成功时间
                pay = true;
            }
        } else {
            wxResult.setReturnMsg(result.get("return_msg"));
        }
        wxResult.setUpdateTime(new Date().getTime());
        wxResultService.save(wxResult);

        saveWxPrepaid(tradeNo, params, pay); //预支付表添加或修改数据
        logger.info(map.toString());
        return map;
    }

    @Override
    public ServerResponse<String> PreBindingAliPay(Integer cusOrDri) {
        if (cusOrDri != 0 && cusOrDri != 1) return ServerResponse.createError("参数错误！");
        /*url拼接规则：https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=APPID&scope=SCOPE&redirect_uri=ENCODED_URL*/
        String url = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?";
        if (cusOrDri == 0) {    //0-客商app请求授权
            url += "app_id=" + alipayConfig.APP_ID_CUSTOMER;
        } else {                //1-司机app请求授权
            url += "app_id=" + alipayConfig.APP_ID_DRIVER;
        }
        url += "&scope=auth_user";  //获取用户信息、网站支付宝登录
        if (cusOrDri == 0) {
            url += "&redirect_uri=" + alipayConfig.REDIRECT_URI_CUS;
        } else {
            url += "&redirect_uri=" + alipayConfig.REDIRECT_URI_DRI;
        }
        return ServerResponse.createSuccess("成功获取拼接url", url);
    }

    @Override
    public ServerResponse<String> BindingAliPay(String appId, String authCode) {
        int cusOrDri;
        if (alipayConfig.APP_ID_CUSTOMER.equals(appId)) {
            cusOrDri = 0;
        } else if (alipayConfig.APP_ID_DRIVER.equals(appId)) {
            cusOrDri = 1;
        } else {
            return ServerResponse.createError("参数错误");
        }
        AlipaySystemOauthTokenResponse oauthTokenResponse = aliPayService.getOauthToken(cusOrDri, authCode);
        if (oauthTokenResponse == null) return ServerResponse.createError("授权失败");

        String appUserId = ShiroUtils.getUserId();
        assert appUserId != null;
        switch (cusOrDri) {
            case 0:
                Query<CustomerUser> cQuery = customerUserService.createQuery();
                cQuery.filter("_id", new ObjectId(appUserId));
                UpdateOperations<CustomerUser> cUpdateOperations = customerUserService.createUpdateOperations();
                cUpdateOperations.set("payeeType", "ALIPAY_USERID"); //支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成。
                cUpdateOperations.set("payeeAccount", oauthTokenResponse.getUserId());
                customerUserService.update(cQuery, cUpdateOperations);
                break;
            case 1:
                Query<DriverInfo> dQuery = driverInfoService.createQuery();
                dQuery.filter("_id", new ObjectId(appUserId));
                UpdateOperations<DriverInfo> dUpdateOperations = driverInfoService.createUpdateOperations();
                dUpdateOperations.set("payeeType", "ALIPAY_USERID"); //支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成。
                dUpdateOperations.set("payeeAccount", oauthTokenResponse.getUserId());
                driverInfoService.update(dQuery, dUpdateOperations);
                break;
            default:
                break;
        }
        return ServerResponse.createSuccess("授权成功");
    }

    /**
     * 以下金额比较时 用 分 为单位
     * 校验提现金额 小于app账户上限 和 支付宝单次、单日、单月提现上限
     * 单笔限额：转账给个人支付宝账户，单笔最高 5 万元；转账给企业支付宝账户，单笔最高 10 万元。
     * 日限额：初始额度为 200 万元，即每日最高可转200万元。
     * 月限额：初始额度为 3100 万元，即每月最高可转3100万元
     */
    private String checkAmountApiPay(String appUserId, SysConstants.UserType ut, String amount) {
        BigDecimal appAccountTotalFee = new BigDecimal(0);
        BigDecimal askRefundAmount = new BigDecimal(amount).multiply(new BigDecimal(100));
        switch (ut) {
            case UT_CUSTOMER:
                appAccountTotalFee = customerAccountService.getAvailableFee(appUserId);
                break;
            case UT_DRIVER:
                appAccountTotalFee = driverAccountService.getAvailableFee(appUserId);
                break;
            default:
                break;
        }
        if (askRefundAmount.compareTo(appAccountTotalFee) > 0) return "申请提现金额过大，账户余额不足";   //申请提现金额大于账户总金额
        if (askRefundAmount.compareTo(new BigDecimal("5000000")) > 0) return "单笔提现最高5万";
        Date today = IdUnit.weeHours1(null, "00:00:00", 0);
        Date tomorrow = IdUnit.weeHours1(IdUnit.getTomorrow().getTime(), "00:00:00", 0);
        if (askRefundAmount.add(sumAmount(appUserId, today, tomorrow, null)).compareTo(new BigDecimal(*********)) > 0)
            return "每日最高200万";
        Date monthFirstDay = IdUnit.weeHours1(IdUnit.getMonthFirstDay().getTime(), "00:00:00", 0);
        Date nextMonthFirstDay = IdUnit.weeHours1(IdUnit.getNextMonthFirstDay().getTime(), "00:00:00", 0);
        if (askRefundAmount.add(sumAmount(appUserId, monthFirstDay, nextMonthFirstDay, null)).compareTo(new BigDecimal("**********")) > 0)
            return "每月最高3100万";
        return null;
    }

    private BigDecimal sumAmount(String appUserId, Date startTime, Date endTime, Integer check) {
        Query<RefundPre> refundPreQuery = this.createQuery();
        refundPreQuery.field("userId").equal(appUserId);
        if (StrUtil.isNotEmpty(startTime)) refundPreQuery.field("createTime").greaterThanOrEq(startTime); // 大于等于
        if (StrUtil.isNotEmpty(endTime)) refundPreQuery.field("createTime").lessThan(endTime); // 小于
        if (StrUtil.isNotEmpty(check)) {
            refundPreQuery.field("check").equal(check);
        } else {
            refundPreQuery.field("check").notEqual(2);
        }
        List<RefundPre> preList = refundPreQuery.find().toList();
        BigDecimal totalAmount = new BigDecimal(0);
        for (RefundPre pre : preList) {
            totalAmount = totalAmount.add(pre.getAmount());
        }
        return totalAmount;
    }

    @Override
    @Transactional
    public ServerResponse<String> RefundAliPayPre(String aliPayLoginId, String name, String amount) {
        if (StrUtil.isNotEmpty(aliPayLoginId) && StrUtil.isEmpty(name)) return ServerResponse.createError("参数错误，缺少姓名");
        if (amount.indexOf(".") > 0 && (amount.substring(amount.indexOf(".") + 1)).length() > 2)
            return ServerResponse.createError("金额错误");

        String appUserId = ShiroUtils.getUserId();
        String appUserType = ShiroUtils.getUserType();   //UT_CUSTOMER("CU")--客商用户,UT_DRIVER("DU")--司机用户
        assert appUserType != null;
        SysConstants.UserType ut = SysConstants.UserType.createEnum(appUserType);
        if (ut == null)
            throw new GlobalException(ResponseCode.ERR_USER_TYPE);

        //todo:校验提现金额 小于app账户上限 和 支付宝单次、单日、单月提现上限
        String msg = checkAmountApiPay(appUserId, ut, amount);
        if (StrUtil.isNotEmpty(msg)) return ServerResponse.createError(msg);

        RefundPre refundPre = new RefundPre();
        refundPre.setUserId(appUserId);
        refundPre.setRefundType(0); //提现方式（0-支付宝/1-微信）
        refundPre.setTransferNo(System.currentTimeMillis() + WXPayUtil.generateNonceStr(19));    //13+19 = 32位
        if (StrUtil.isNotEmpty(aliPayLoginId)) {
            refundPre.setAccountNumber(aliPayLoginId);
            refundPre.setName(name);
        }
        refundPre.setTitle("提现");
        refundPre.setRemark("申请提现到支付宝余额");
        refundPre.setCheck(0);
        ClientSession clientSession = client.startSession();
        try {
            clientSession.startTransaction();
            MongoCollection<Document> refundPreCollection = this.getCollection();
            MongoCollection<Document> cAccountCollection = customerAccountService.getCollection();
            MongoCollection<Document> dAccountCollection = driverAccountService.getCollection();

            Date time = new Date();
            switch (ut) {
                case UT_CUSTOMER:
                    CustomerUser cUser = customerUserService.getByPK(appUserId);
                    if (cUser == null) throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);
                    if (StrUtil.isEmpty(aliPayLoginId) && StrUtil.isEmpty(cUser.getPayeeAccount()))
                        return ServerResponse.createError("请输入提现真实账户名称和真实用户姓名 或 完善信息界面进行支付宝授权");

                    Document document_c = Document.parse(JSONObject.toJSONString(refundPre));
                    document_c.append("customerUserID", cUser.getObjectId());
                    document_c.append("amount", new BigDecimal(amount).multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP));
                    document_c.append("createTime", time);
                    document_c.append("updateTime", time.getTime());
                    refundPreCollection.insertOne(clientSession, document_c);

                    BigDecimal fee = new BigDecimal("-" + amount).multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
                    Document cAccountDoc = customerAccountService.createCusAccountDoc(appUserId, fee, 8, refundPre.getTransferNo(), null, time);
                    cAccountCollection.insertOne(clientSession, cAccountDoc);
                    break;
                case UT_DRIVER:
                    DriverInfo dUser = driverInfoService.getByPK(appUserId);
                    if (dUser == null) throw new GlobalException(ResponseCode.ERR_USER_NOT_EXISTS);
                    if (StrUtil.isEmpty(aliPayLoginId) && StrUtil.isEmpty(dUser.getPayeeAccount()))
                        return ServerResponse.createError("请输入提现真实账户名称和真实用户姓名 或 完善信息界面进行支付宝授权");

                    Document document_d = Document.parse(JSONObject.toJSONString(refundPre));
                    document_d.append("driverInfoID", dUser.getObjectId());
                    document_d.append("createTime", time);
                    document_d.append("updateTime", time.getTime());
                    refundPreCollection.insertOne(clientSession, document_d);

                    DriverAccount driverAccount = new DriverAccount();
                    driverAccount.setType(4);   //提现
                    driverAccount.setDid(appUserId);
                    driverAccount.setUpdateTime(time.getTime());
                    driverAccount.setOutTradeNo(refundPre.getTransferNo());
                    Document accountDoc = Document.parse(JSONObject.toJSONString(driverAccount));
                    accountDoc.append("createTime", time);
                    accountDoc.append("totalFee", new BigDecimal("-" + amount).setScale(0, BigDecimal.ROUND_HALF_UP));//微信 单位为分
                    dAccountCollection.insertOne(clientSession, accountDoc);
                    break;
                default:
                    break;
            }
            clientSession.commitTransaction();
        } catch (Exception e) {
            clientSession.abortTransaction();
            e.printStackTrace();
            return ServerResponse.createError("提现申请失败，联系管理员");
        } finally {
            clientSession.close();
        }

        return ServerResponse.createSuccess("提现申请成功");
    }

    //成功SUCCESS; 失败FAIL
    @Override
    public Map<String, String> aliPayRefundPass(String transferNo) {
        Map<String, String> result = new HashMap<>();
        result.put("resultCode", "FAIL");

        RefundAliResult queryRefundAliResult = refundAliResultDao.get("outBizNo", transferNo);
        if (queryRefundAliResult != null && queryRefundAliResult.getStatus().equals("SUCCESS")) {
            result.put("resultCode", queryRefundAliResult.getStatus());
            return result;
        }

        RefundPre per = this.get("transferNo", transferNo);
        //errCode不为空，说明之前一起向支付宝提起过转账申请，转账没有成功，则不可以再重试了
        //提现申请的金额已经退回到用app账户中，需要提现可重新提出申请
        if (per != null && StrUtil.isNotEmpty(per.getErrCode())) {
            result.put("errCode", per.getErrCode());
            result.put("errCodeDes", per.getErrCodeDes());
            return result;
        }
        if (per != null && per.getRefundType() == 0) {
            //默认使用 支付宝登录号 给账户转账
            Participant payee_info = new Participant();
            payee_info.setIdentity(per.getAccountNumber());
            payee_info.setIdentityType("ALIPAY_LOGON_ID");
            payee_info.setName(per.getName());

            int cusOrDri;
            if (StrUtil.isNotEmpty(per.getCustomerUser())) {
                cusOrDri = 0;
                if (StrUtil.isEmpty(payee_info.getIdentity())) {
                    CustomerUser cUser = per.getCustomerUser();
                    payee_info.setIdentity(cUser.getPayeeAccount());
                    payee_info.setIdentityType(cUser.getPayeeType());
                }
            } else if (StrUtil.isNotEmpty(per.getDriverInfo())) {
                cusOrDri = 1;
                if (StrUtil.isEmpty(payee_info.getIdentity())) {
                    DriverInfo dUser = per.getDriverInfo();
                    payee_info.setIdentity(dUser.getPayeeAccount());
                    payee_info.setIdentityType(dUser.getPayeeType());
                }
            } else {
                result.put("errCode", "");
                result.put("errCodeDes", "仅支持客商或司机用户类型提现");
                return result;
            }
            if (StrUtil.isEmpty(payee_info.getIdentity())) {
                result.put("errCode", "");
                result.put("errCodeDes", "要提现的账户信息不全，请用户完善信息后重试");
                return result;
            }

            String amount = per.getAmount().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).toPlainString();
            AlipayFundTransUniTransferResponse response = aliPayService.transfer(per.getTransferNo(), amount, per.getTitle(), payee_info, per.getRemark());
            if (StrUtil.isEmpty(response)) {
                result.put("errCode", "");
                result.put("errCodeDes", "网络异常，稍后重试");
                return result;
            }
            if ("10000".equals(response.getCode())) {
                RefundAliResult refundAliResult = new RefundAliResult();
                BeanUtils.copyProperties(refundAliResult, response);
                refundAliResult.setPayDate(response.getTransDate());//(new Date().toString());
                refundAliResultDao.save(refundAliResult);

                result.put("resultCode", "SUCCESS");
            } else {
                result.put("errCode", response.getSubCode());
                result.put("errCodeDes", response.getSubMsg());
                //支付宝转账失败，申请提现的金额退回到用户app账户中
                refundFailThenBackToUserAccount(cusOrDri, per.getTransferNo());
            }
        }
        return result;
    }

    private void refundFailThenBackToUserAccount(int cusOrDri, String tradeNo) {
        if (cusOrDri == 0) {
            customerAccountService.delete("outTradeNo", tradeNo);
        } else if (cusOrDri == 1) {
            driverAccountService.delete("outTradeNo", tradeNo);
        }

    }
}
