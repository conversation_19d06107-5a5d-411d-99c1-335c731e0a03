package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;
import com.erdos.coal.park.api.driver.pojo.CarData;
import com.erdos.coal.park.api.driver.service.IWechatDriverInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

//"司机微信小程序管理接口列表"
@RestController
@RequestMapping("/api/dvr/wx")
public class WechatDriverInfoController extends BaseController {

    @Resource
    private IWechatDriverInfoService wechatDriverInfoService;

    @InvokeLog(description = "微信小程序上传照片 接口") //日志
    @PostMapping(value = "/upload_pho")
    public ServerResponse<String> uploadPhoHandler(
            @RequestParam(value = "uploadPho") MultipartFile uploadPho, //"上传照片"
            @RequestParam(value = "type") String type                   //"照片类型--行驶证照片(第一页):drivingPho1,行驶证照片(第二页):drivingPho2,驾驶证照片:driverPho,车主身份证照片正面:carIdentityPhoBef,车主身份证照片反面:carIdentityPhoBack,司机身份证照片正面:driIdentityPhoBef,司机身份证照片反面:driIdentityPhoBack,司机和车的合影照片:driverCarPho"
                                                                        // 2024年9月4号 + 收货业务，二级单位需要录入对方净重时，司机录入净重需上传(煤单照片:inNetWeightPho)
                                                                        // 2024年10月28号 + 收货业务，二级单位需要录入装货单号时，司机录入装货单号(装货单照片:loadPoundPho)
    ) throws GlobalException {
        if ("inNetWeightPho".equals(type) || "loadPoundPho".equals(type)){
            return wechatDriverInfoService.uploadPhoPublicRead(uploadPho, type);
        } else {
            return wechatDriverInfoService.uploadPho(uploadPho, type);
        }
    }

    @InvokeLog(description = "微信小程序完善司机信息 接口") //日志
    @PostMapping(value = "/update_dvr_info")
    public ServerResponse<String> updateDriverInfoHandler(
            @RequestParam(value = "driverPho", required = false) String driverPho,     //"驾驶证照片"
            @RequestParam(value = "driverPho2", required = false) String driverPho2,   //"驾驶证照片副页"
            @RequestParam(value = "driIdentityPhoBef", required = false) String driIdentityPhoBef,     //"司机身份证照片正面"
            @RequestParam(value = "driIdentityPhoBack", required = false) String driIdentityPhoBack,   //"司机身份证照片反面"
            @RequestParam(value = "driverCarPho", required = false) String driverCarPho,   //"司机和车的合影照片"
            @RequestParam(value = "roadQCPho", required = false) String roadQCPho,     //"道路从业资格证照片"
            @RequestParam(value = "bankCardPho", required = false) String bankCardPho  //"银行卡照片"
    ) throws GlobalException {
        //return wechatDriverInfoService.updateDriverInfo(carNum, drivingPho, driverPho, carIdentityPhoBef, carIdentityPhoBack, driIdentityPhoBef, driIdentityPhoBack, driverCarPho);
        //真微信小程序司机
        //return wechatDriverInfoService.updateDriverInfo(driverPho, driverPho2, driIdentityPhoBef, driIdentityPhoBack, driverCarPho, roadQCPho, bankCardPho);
        //假微信小程序司机，实际是app司机
        return wechatDriverInfoService.updateDriverInfo2(driverPho, driverPho2, driIdentityPhoBef, driIdentityPhoBack, driverCarPho, roadQCPho, bankCardPho);
    }

    @InvokeLog(description = "微信小程序保存司机车辆信息 接口") //日志
    @PostMapping(value = "/save_dvr_car")
    public ServerResponse<String> saveDvrCarHandler(
            @RequestParam(value = "carNum") String carNum,         //"车牌号"
            @RequestParam(value = "carInfoId") String carInfoId,   //"车型"
            @RequestParam(value = "drivingPho1", required = false) String drivingPho1, //"行驶证照片（正页）"
            @RequestParam(value = "drivingPho2", required = false) String drivingPho2, //"行驶证照片（副页正面）"
            @RequestParam(value = "drivingPho3", required = false) String drivingPho3, //"行驶证照片（副页反面）"
            @RequestParam(value = "roadTCPho", required = false) String roadTCPho,     //"车辆道路运输证"
            @RequestParam(value = "carIdentityPhoBef", required = false) String carIdentityPhoBef,     //"车主身份证正（单位证件）"
            @RequestParam(value = "carIdentityPhoBack", required = false) String carIdentityPhoBack,   //"车主身份证反（单位证件）"
            @RequestParam(value = "driverCarPho", required = false) String driverCarPho    //"司机和车的合影照片"
    ) throws GlobalException {
        //真微信小程序司机
        //return wechatDriverInfoService.saveDvrCar(carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack, driverCarPho);
        //假微信小程序司机，实际是app 司机
        return wechatDriverInfoService.saveDvrCar2(carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack, driverCarPho);
    }

    @InvokeLog(description = "微信小程序保存司机车辆信息2 接口") //日志
    @PostMapping(value = "/save_dvr_car2")  // 保存车辆信息是增加录入皮重信息
    public ServerResponse<String> saveDvrCar2Handler(
            @RequestParam(value = "carNum") String carNum,         //"车牌号"
            @RequestParam(value = "carInfoId") String carInfoId,   //"车型"
            @RequestParam(value = "tareWeight") Double tareWeight,   //"皮重"
            @RequestParam(value = "drivingPho1", required = false) String drivingPho1, //"行驶证照片（正页）"
            @RequestParam(value = "drivingPho2", required = false) String drivingPho2, //"行驶证照片（副页正面）"
            @RequestParam(value = "drivingPho3", required = false) String drivingPho3, //"行驶证照片（副页反面）"
            @RequestParam(value = "roadTCPho", required = false) String roadTCPho,     //"车辆道路运输证"
            @RequestParam(value = "carIdentityPhoBef", required = false) String carIdentityPhoBef,     //"车主身份证正（单位证件）"
            @RequestParam(value = "carIdentityPhoBack", required = false) String carIdentityPhoBack,   //"车主身份证反（单位证件）"
            @RequestParam(value = "driverCarPho", required = false) String driverCarPho    //"司机和车的合影照片"
    ) throws GlobalException {
        return wechatDriverInfoService.saveDvrCar3(carNum, carInfoId, tareWeight, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack, driverCarPho);
    }
}
