package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.park.web.sys.entity.PlatFormAccount;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.Date;

public interface IPlatFormAccountService extends IBaseMongoService<PlatFormAccount> {
    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
    Document createPlatFormAccountDoc(String payerId, BigDecimal fee, Integer type, String oid, Date time);

    Document createPlatFormAccountDoc(String payerId, BigDecimal fee, Integer type, Date time, String preferentialRefundNo);
}
