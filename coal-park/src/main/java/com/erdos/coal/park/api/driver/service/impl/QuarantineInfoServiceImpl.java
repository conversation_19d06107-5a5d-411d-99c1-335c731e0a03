package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.ocr.IRecognitionHandler;
import com.erdos.coal.ocr.entity.HealthCode;
import com.erdos.coal.ocr.entity.TravelCard;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.IQuarantineInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.QuarantineInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import com.erdos.coal.park.api.driver.service.IQuarantineInfoService;
import com.erdos.coal.park.api.manage.service.IPhotoFileService;
import com.erdos.coal.park.web.sys.entity.SubUnitQuarantineDevice;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.service.ISubUnitQuarantineDeviceService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("quarantineInfoService")
public class QuarantineInfoServiceImpl extends BaseMongoServiceImpl<QuarantineInfo, IQuarantineInfoDao> implements IQuarantineInfoService {
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private IRecognitionHandler<HealthCode> healthCodeService;
    @Resource
    private IRecognitionHandler<TravelCard> travelCardService;
    @Resource
    private IPhotoFileService photoFileService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private ISubUnitQuarantineDeviceService subUnitQuarantineDeviceService;
    @Resource
    private IOrderService orderService;

    @Override
    public QuarantineInfo searchDvrLastQuarantine(String did, String defaultDownUnit) {
        Query<QuarantineInfo> query = this.createQuery();
        query.filter("did", did);
        if (StrUtil.isNotEmpty(defaultDownUnit)) query.filter("defaultDownUnit", defaultDownUnit);
        query.order(Sort.descending("createTime"));
        List<QuarantineInfo> list = this.list(query);
        if (list.size() > 0) return list.get(0);
        return null;
    }

    @Override
    public boolean checkDvrIsQuarantine(String did, DriverInfo driverInfo, String defaultDownUnit) {
        QuarantineInfo quarantineInfo = searchDvrLastQuarantine(did, null);

        SysUnit sysUnit = sysUnitService.get("code", defaultDownUnit);
        if (sysUnit != null && StrUtil.isNotEmpty(sysUnit.getIsQuarantine()) && sysUnit.getIsQuarantine() == 1) {
            if (quarantineInfo == null) return true;
            if (quarantineInfo.getAudit() != 1 && quarantineInfo.getAudit() != 3) return true;
            int validityTime = 24;   //防疫申报 内容有效期默认为24小时。
            if (StrUtil.isNotEmpty(sysUnit.getValidityTime())) validityTime = sysUnit.getValidityTime();
            if (new Date().getTime() - quarantineInfo.getHealthCodeTime().getTime() > validityTime * 60 * 60 * 1000)
                return true;

            //需要添加防疫申报信息，且已申报的防疫信息在有效期内
            if (StrUtil.isNotEmpty(defaultDownUnit) && !defaultDownUnit.equals(quarantineInfo.getDefaultDownUnit())) {
                quarantineInfo.setId(Utils.getUUID());
                quarantineInfo.setDefaultDownUnit(defaultDownUnit);
                this.save(quarantineInfo);
            }
        }
        return false;
    }

    @Override
    public QuarantineInfo imageRecognition(DriverInfo driverInfo, String healthCodePho, String travelCardPho, String temperaturePro, String temperature) throws IOException {
        /* 以下信息图片未识别出
        private String temperaturePro;  //体温照片
        private String travelColor;     //行程卡颜色
        private boolean inHighRiskArea;     //是否到达高风险区
        private boolean passHighRiskArea;   //是否路过高风险区
        private boolean inMiddleRiskArea;   //是否到达中风险区
        private boolean passMiddleRiskArea; //是否路过中风险区
        private Integer audit;      //审核结果 0-未审核，1-自动审核通过，2-自动审核未通过，3-人工审核通过，4-人工审核未通过
        private String auditDes;    //审核结果描述：自动审核通过，自动审核未通过等待人工审核，审核通过，"人工审核未通的原因描述"
        */
        QuarantineInfo result = new QuarantineInfo();
        result.setId(Utils.getUUID());

        //String msg;
        //识别健康码照片
        /*long start1 = System.currentTimeMillis();
        HealthCode healthCode = healthCodeService.getInfo(ImageIO.read(photoFileService.signUrl2(healthCodePho)));
        logger.info("共耗时：" + (System.currentTimeMillis() - start1));
        logger.info(healthCode.toString());*/
        //检查照片信息是否属于本人
        //msg = checkDvrIdentity(driverInfo, healthCode.getName(), healthCode.getIdno(), null);
        //添加健康码照片信息到QuarantineInfo对象
        result.setHealthCodePho(healthCodePho);
        //result.setHealthColor(healthCode.isGreenCode() ? "GREEN" : "NOTGREEN");
        /*SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date d = sdf.parse(healthCode.getTime());
            result.setHealthCodeTime(d);
            Date d2 = sdf2.parse(healthCode.getAcidTime());
            result.setNucleicAcidTime(d2);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        result.setNucleicAcidDes(healthCode.getAcidResult());
        result.setVaccinationInfo(healthCode.getVaccination());*/

        //识别行程码照片
        /*long start2 = System.currentTimeMillis();
        TravelCard travelCard = travelCardService.getInfo(ImageIO.read(photoFileService.signUrl2(travelCardPho)));
        logger.info(travelCard.toString());
        logger.info("共耗时：" + (System.currentTimeMillis() - start2));*/
        //检查照片信息是否属于本人
        //msg = msg + checkDvrIdentity(driverInfo, null, null, travelCard.getMobileString());
        //添加行程码照片信息到QuarantineInfo对象
        result.setTravelCardPho(travelCardPho);
        /*SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
        String timeString = travelCard.getTimeString();
        try {
            Date d3 = sdf3.parse(timeString.substring(timeString.indexOf(":") + 1));
            result.setTravelCardTime(d3);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        result.setTravelArea(travelCard.getTravelString());*/

        result.setTemperature(temperature);

        //if (StrUtil.isNotEmpty(msg)) result.setMsg(msg);

        return result;
    }

    @Override
    public String checkPho(MultipartFile uploadPho, String type, DriverInfo driverInfo) throws IOException {
        String msg = "";
        if (type.equals("healthCodePho")) {
            HealthCode healthCode = healthCodeService.getInfo(ImageIO.read(uploadPho.getInputStream()));
            msg = checkDvrIdentity(driverInfo, healthCode.getName(), healthCode.getIdno(), null);
        }
        if (type.equals("travelCardPho")) {
            TravelCard travelCard = travelCardService.getInfo(ImageIO.read(uploadPho.getInputStream()));
            String mobile = travelCard.getMobileString().substring(0, 11);
            msg = checkDvrIdentity(driverInfo, null, null, mobile);
        }

        return msg;
    }

    private String checkDvrIdentity(DriverInfo driverInfo, String name, String idCard, String mobile) {
        if (StrUtil.isNotEmpty(name) && StrUtil.isNotEmpty(driverInfo.getName())) {
            String dvrName = driverInfo.getName();
            if (name.length() != dvrName.length()) return "健康码姓名不一致";
            if (!name.substring(0, 1).equals(dvrName.substring(0, 1))) return "健康码姓名不一致";
            if (name.length() > 2 && !name.substring(name.length() - 1).equals(dvrName.substring(dvrName.length() - 1)))
                return "健康码姓名不一致";
        }

        if (StrUtil.isNotEmpty(idCard) && StrUtil.isNotEmpty(driverInfo.getIdentity())) {
            String dvrIdCard = driverInfo.getIdentity();
            if (idCard.length() < 18) return "健康码身份证错误";
            if (!idCard.substring(0, 2).equals(dvrIdCard.substring(0, 2))) return "健康码身份证不一致";
            if (!idCard.substring(16).equals(dvrIdCard.substring(16))) return "健康码身份证不一致";
        }

        if (StrUtil.isNotEmpty(mobile) && StrUtil.isNotEmpty(driverInfo.getMobile())) {
            String dvrMobile = driverInfo.getMobile();
            if (!mobile.substring(0, 3).equals(dvrMobile.substring(0, 3))) return "行程卡手机号不一致";
            if (!mobile.substring(7).equals(dvrMobile.substring(7))) return "行程卡手机号不一致";
        }

        return "";
    }

    @Override
    public String auditQuarantineInfo(QuarantineInfo quarantineInfo) {
        if (StrUtil.isNotEmpty(quarantineInfo.getMsg())) {
            quarantineInfo.setAudit(2);
            quarantineInfo.setAuditDes(quarantineInfo.getMsg());
            return quarantineInfo.getMsg();
        }

        if (!quarantineInfo.getHealthColor().equals("GREEN")) {
            quarantineInfo.setAudit(2);
            quarantineInfo.setAuditDes("健康码不是绿码");
            return "健康码不是绿码";
        }

        SysUnit sysUnit = sysUnitService.get("code", quarantineInfo.getDefaultDownUnit());
        quarantineInfo.setSubName(sysUnit.getName());
        int validityTime = 24;   //防疫申报 内容有效期默认为24小时。
        if (StrUtil.isNotEmpty(sysUnit.getValidityTime())) validityTime = sysUnit.getValidityTime();
        if (new Date().getTime() - quarantineInfo.getHealthCodeTime().getTime() > validityTime * 60 * 60 * 1000) {
            quarantineInfo.setAudit(2);
            quarantineInfo.setAuditDes("健康码过期");
            return "健康码过期";
        }

        if (Double.valueOf(quarantineInfo.getTemperature()) - 37.5 >= 0) {
            quarantineInfo.setAudit(2);
            quarantineInfo.setAuditDes("体温异常");
            return "体温异常";
        }

        quarantineInfo.setAudit(1);
        return null;
    }

    @Override
    public ServerResponse<String> synQuarantine(String sxId, String addr, String deviceNo, String dvrName, String dvrIdCardNum, String healthCodeLevel, String nucleate, Double temperature) {
        Query<SubUnitQuarantineDevice> unitQuery = subUnitQuarantineDeviceService.createQuery();
        unitQuery.or(
                unitQuery.criteria("deviceNo").equal(sxId),
                unitQuery.criteria("deviceNo").equal(addr),
                unitQuery.criteria("deviceNo").equal(deviceNo));
        SubUnitQuarantineDevice device = subUnitQuarantineDeviceService.get(unitQuery);
        String subCode = device.getSubCode();

        Query<DriverInfo> dvrQuery = driverInfoService.createQuery();
        dvrQuery.filter("name", dvrName);
        dvrQuery.filter("identity", dvrIdCardNum);
        DriverInfo driverInfo = driverInfoService.get(dvrQuery);
        if (driverInfo == null) return ServerResponse.createError("司机姓名或身份证号错误");

        List<OrderTaking> orderTakings = orderTakingService.searchById(driverInfo.getObjectId().toHexString(), 2);
        if (orderTakings == null || orderTakings.size() == 0) return ServerResponse.createError("司机用户暂无订单");
        OrderTaking ot = orderTakings.get(0);
        Order order = orderService.get("oid", ot.getOid());

        if (!healthCodeLevel.equals("1")) return ServerResponse.createError("健康码异常");
        if (temperature > 37.5) return ServerResponse.createError("体温异常");

        QuarantineInfo quarantineInfo = new QuarantineInfo();
        quarantineInfo.setId(Utils.getUUID());
        quarantineInfo.setDid(driverInfo.getObjectId().toHexString());
        quarantineInfo.setDriverInfo(driverInfo);
        quarantineInfo.setDefaultDownUnit(subCode);
        quarantineInfo.setOid(ot.getOid());
        quarantineInfo.setHealthColor("GREEN");
        if (StrUtil.isNotEmpty(nucleate)) quarantineInfo.setNucleicAcidDes(nucleate);
        quarantineInfo.setTemperature(String.valueOf(temperature));
        quarantineInfo.setAudit(1);
        quarantineInfo.setAuditDes("自动审核通过");

        this.save(quarantineInfo);

        //添加推送信息
        Map<String, String> map = pushInfoService.weChatSendDvr(driverInfo.getPhoneId(), driverInfo.getMobile(), driverInfo.getName(), "防疫申报审核", quarantineInfo.getDefaultDownUnit() + ":自动审核通过");
        pushInfoService.addPushInfo("疫情防控", "司机订单：" + quarantineInfo.getOid() + "防疫申报审核结果(" + quarantineInfo.getDefaultDownUnit() + ")自动审核通过", quarantineInfo.getDid(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));

        return ServerResponse.createSuccess("成功");
    }

    @Override
    public ServerResponse<String> synQuarantine2(String sxId, String addr, String deviceNo, String dvrName, String dvrIdCardNum, String healthCodeLevel, String nucleate, Double temperature) {
        Query<SubUnitQuarantineDevice> unitQuery = subUnitQuarantineDeviceService.createQuery();
        unitQuery.or(
                unitQuery.criteria("deviceNo").equal(sxId),
                unitQuery.criteria("deviceNo").equal(addr),
                unitQuery.criteria("deviceNo").equal(deviceNo));
        SubUnitQuarantineDevice device = subUnitQuarantineDeviceService.get(unitQuery);
        if (device == null) return ServerResponse.createError("请先在平台二级单位下配置该设备");
        String subCode = device.getSubCode();

        Query<DriverInfo> dvrQuery = driverInfoService.createQuery();
        dvrQuery.filter("name", dvrName);
        dvrQuery.filter("identity", dvrIdCardNum);
        /*DriverInfo driverInfo = driverInfoService.get(dvrQuery);
        if (driverInfo == null) return ServerResponse.createError("司机姓名或身份证号错误");*/
        //2022年7月15号
        dvrQuery.criteria("state").equal(1);    //司机状态为可用
        dvrQuery.criteria("haveOrder").equal(1);//司机当前有接单

        DriverInfo driverInfo = null;
        String oid = "";
        List<DriverInfo> dvrList = driverInfoService.list(dvrQuery);
        if (dvrList.size() <= 0) return ServerResponse.createError("司机账户不存在，或不可用，或未实名认证");
        for (DriverInfo dvr : dvrList) {

            Query<OrderTaking> otQuery = orderTakingService.createQuery();
            otQuery.field("did").equal(dvr.getObjectId().toHexString());
            otQuery.field("finishTag").notEqual(2);
            otQuery.field("driverIsAgree").notEqual(2);
            otQuery.order(Sort.descending("createTime"));
            OrderTaking taking = orderTakingService.get(otQuery);

            if (taking != null) {
                driverInfo = dvr;
                oid = taking.getOid();
                break;
            }
        }


        if (driverInfo == null) return ServerResponse.createError("司机用户暂无订单");

        QuarantineInfo quarantineInfo = new QuarantineInfo();
        quarantineInfo.setId(Utils.getUUID());
        quarantineInfo.setDid(driverInfo.getObjectId().toHexString());
        quarantineInfo.setDriverInfo(driverInfo);
        quarantineInfo.setDefaultDownUnit(subCode);
        quarantineInfo.setOid(oid);
        quarantineInfo.setHealthColor("GREEN");
        if (StrUtil.isNotEmpty(nucleate)) quarantineInfo.setNucleicAcidDes(nucleate);
        quarantineInfo.setTemperature(String.valueOf(temperature));
        quarantineInfo.setAudit(1);
        quarantineInfo.setAuditDes("自动审核通过");
        this.save(quarantineInfo);

        //添加完成 即 修改orderTaking防疫申报按钮
        Order order = orderService.get("oid", oid);
        int isQuarantine = 0;
        if (subCode.equals(order.getOutDefaultDownUnit())) isQuarantine = 1;
        if (subCode.equals(order.getInDefaultDownUnit())) isQuarantine = 2;
        UpdateOperations<OrderTaking> otUpdateOperations = orderTakingService.createUpdateOperations();
        otUpdateOperations.set("isQuarantine", isQuarantine);
        Query<OrderTaking> query = orderTakingService.createQuery().filter("oid", order.getOid());
        orderTakingService.update(query, otUpdateOperations);

        //添加推送信息
        Map<String, String> map = pushInfoService.weChatSendDvr(driverInfo.getPhoneId(), driverInfo.getMobile(), driverInfo.getName(), "防疫申报审核", quarantineInfo.getDefaultDownUnit() + ":自动审核通过");
        pushInfoService.addPushInfo("疫情防控", "司机订单：" + quarantineInfo.getOid() + "防疫申报审核结果(" + quarantineInfo.getDefaultDownUnit() + ")自动审核通过", quarantineInfo.getDid(), 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));

        return ServerResponse.createSuccess("成功");
    }
}
