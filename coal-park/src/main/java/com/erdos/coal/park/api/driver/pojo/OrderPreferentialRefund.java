package com.erdos.coal.park.api.driver.pojo;

import java.io.Serializable;
import java.util.List;

public class OrderPreferentialRefund implements Serializable {
    private String outDefaultDownUnit;
    private String did;
    private String carNum;

    private Integer count;                          //车数
    private Double outFee0;     //平台收费（分）
    private Double outFee5;     //第三方收费（分）

    private List<Integer> outFee0s;
    private List<Integer> outFee5s;
    private List<String> counts;
    private String noTime;      //日期 yyyy-MM-dd

    public String getOutDefaultDownUnit() {
        return outDefaultDownUnit;
    }

    public void setOutDefaultDownUnit(String outDefaultDownUnit) {
        this.outDefaultDownUnit = outDefaultDownUnit;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Integer getCount() {
        return counts.size();
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getOutFee0() {
        return outFee0;
    }

    public void setOutFee0(Double outFee0) {
        this.outFee0 = outFee0;
    }

    public Double getOutFee5() {
        return outFee5;
    }

    public void setOutFee5(Double outFee5) {
        this.outFee5 = outFee5;
    }

    public List<Integer> getOutFee0s() {
        return outFee0s;
    }

    public void setOutFee0s(List<Integer> outFee0s) {
        this.outFee0s = outFee0s;
    }

    public List<Integer> getOutFee5s() {
        return outFee5s;
    }

    public void setOutFee5s(List<Integer> outFee5s) {
        this.outFee5s = outFee5s;
    }

    public List<String> getCounts() {
        return counts;
    }

    public void setCounts(List<String> counts) {
        this.counts = counts;
    }

    public String getNoTime() {
        return noTime;
    }

    public void setNoTime(String noTime) {
        this.noTime = noTime;
    }
}
