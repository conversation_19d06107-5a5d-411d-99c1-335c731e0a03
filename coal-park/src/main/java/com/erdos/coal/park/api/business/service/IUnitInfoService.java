package com.erdos.coal.park.api.business.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.entity.UnitInfo;

public interface IUnitInfoService extends IBaseMongoService<UnitInfo> {

    //企业绑定客商 接口
    ServerResponse<String> boundCustomer(String uName, String telCode, String unitCode, String userCode);

    //废除绑定的客商 接口
    ServerResponse<String> cancelBound(String userCode);

    //企业推送添加二级单位
    ServerResponse<String> addSecondUnit(String pCode, String subUnitList);

    //企业推送修改二级单位
    ServerResponse<String> editSecondUnit(String code, String name);

    //企业推送删除二级单位
    ServerResponse<String> delSecondUnit(String code);

    //业务系统登录
    ServerResponse<AccessToken> bizUserLogin(String username, String password);
}
