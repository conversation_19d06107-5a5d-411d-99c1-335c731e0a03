package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DriverAccountData implements Serializable {
    private String id;
    private String name;
    private String mobile;
    private BigDecimal availableFee = new BigDecimal("0");
    private Date createTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getAvailableFee() {
        return availableFee;
    }

    public void setAvailableFee(BigDecimal availableFee) {
        this.availableFee = availableFee;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
