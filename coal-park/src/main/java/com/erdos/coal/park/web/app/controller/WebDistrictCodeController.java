package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.entity.DistrictCode;
import com.erdos.coal.park.web.app.service.IDistrictCodeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/district")
public class WebDistrictCodeController extends BaseController {
    @Resource
    private IDistrictCodeService districtCodeService;

    @PostMapping("/list_code")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return districtCodeService.districtCodeList(page, rows);
    }

    @PostMapping("/up_code")
    public ServerResponse upCodeHandler(@RequestBody MultipartFile file) throws GlobalException {
        return districtCodeService.upDistrictCode(file);
    }

    @PostMapping("/add_code")
    public ServerResponse addCodeHandler(@RequestBody DistrictCode districtCode) throws GlobalException {
        return districtCodeService.addDistrictCode(districtCode);
    }

    @PostMapping("/edit_code")
    public ServerResponse editCodeHandler(@RequestBody DistrictCode districtCode) throws GlobalException {
        return districtCodeService.editDistrictCode(districtCode);
    }
}
