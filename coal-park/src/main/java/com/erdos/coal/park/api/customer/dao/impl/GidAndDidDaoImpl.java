package com.erdos.coal.park.api.customer.dao.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.park.api.customer.dao.IGidAndDidDao;
import com.erdos.coal.park.api.customer.entity.GidAndDid;
import org.springframework.stereotype.Repository;

@Repository("gidAndCidDao")
public class GidAndDidDaoImpl extends BaseMongoDAOImpl<GidAndDid> implements IGidAndDidDao {
}
