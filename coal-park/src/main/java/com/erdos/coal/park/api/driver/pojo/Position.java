package com.erdos.coal.park.api.driver.pojo;

import dev.morphia.geo.Geometry;

import java.io.Serializable;

public class Position  implements Serializable {
    private String longitude;   //经度
    private String latitude;    //纬度
    private String speed;  //车速 公里每小时
    private String baiduLong;
    private String baiduLat;
    private String googleLong;
    private String googleLat;

    private String msg;

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getBaiduLong() {
        return baiduLong;
    }

    public void setBaiduLong(String baiduLong) {
        this.baiduLong = baiduLong;
    }

    public String getBaiduLat() {
        return baiduLat;
    }

    public void setBaiduLat(String baiduLat) {
        this.baiduLat = baiduLat;
    }

    public String getGoogleLong() {
        return googleLong;
    }

    public void setGoogleLong(String googleLong) {
        this.googleLong = googleLong;
    }

    public String getGoogleLat() {
        return googleLat;
    }

    public void setGoogleLat(String googleLat) {
        this.googleLat = googleLat;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
