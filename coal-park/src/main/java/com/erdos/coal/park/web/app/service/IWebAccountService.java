package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;

public interface IWebAccountService extends IBaseMongoService<SysUnitAccount> {

    // 报表   平台入账，客商代扣，一级单位代扣，二级单位代扣
    ServerResponse<EGridResult> report(Integer page, Integer rows);

    // 客商，一级单位，二级单位 代扣、代付 货运信息（oid）明细
    ServerResponse<EGridResult> detail(Integer page, Integer rows);

    //订单所有金额类型的明细
    ServerResponse<EGridResult> orderDetail(Integer page, Integer rows);

    // 报表   客商所有流水，司机所有流水，平台记录的所有微信支付记录
    ServerResponse<EGridResult> report2(Integer page, Integer rows);

    // 报表   消费明细，一级单位代扣，三方分账
    ServerResponse<EGridResult> report3(Integer page, Integer rows);

    // 统计   一级单位、客商（三方）一天的总金额和总笔数等
    ServerResponse<EGridResult> statisticsThirdPartyAccount(Integer page, Integer rows);

    // 统计   按二级单位、日期 统计车数、平台金额、分账金额
    ServerResponse<EGridResult> report4(Integer page, Integer rows);

    // 统计   按二级单位、日期 统计短盘退费总车数、总金额
    ServerResponse<EGridResult> preferentialRefundRecordList(Integer page, Integer rows);
}
