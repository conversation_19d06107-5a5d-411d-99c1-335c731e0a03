package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.manage.dao.ISMSDao;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.web.app.service.IWebMessageService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service("webMessageService")
public class WebMessageServiceImpl extends BaseMongoServiceImpl<SMS, ISMSDao> implements IWebMessageService {
    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult smsLoadGrid(Integer page, Integer rows) {
        Query<SMS> query = this.createQuery();
        if (!StrUtil.isEmpty(request.getParameter("mobile"))) {
            query.filter("mobile", request.getParameter("mobile"));
        }
        query.or(
                query.criteria("code").startsWith("1"),
                query.criteria("code").startsWith("2"),
                query.criteria("code").startsWith("3"),
                query.criteria("code").startsWith("4"),
                query.criteria("code").startsWith("5"),
                query.criteria("code").startsWith("6"),
                query.criteria("code").startsWith("7"),
                query.criteria("code").startsWith("8"),
                query.criteria("code").startsWith("9"),
                query.criteria("code").startsWith("0"));
//        query.criteria("mobile").notEqual("15947496200");
        query.order(Sort.descending("updateTime"));
        return this.findPage(page, rows, query);
    }

}
