package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import org.bson.Document;

import java.util.Date;
import java.util.List;

public interface IOrderTakingService extends IBaseMongoService<OrderTaking> {
    //车牌号查询司机是否有未完成(finishTag!=2)的订单
    List<OrderTaking> searchDriverOrder(String carNum, int finishTag);

    //下单时间和订单状态 综合条件 查询orderTaking记录
    //List<OrderTaking> searchByUpdateTime(String cid, Integer finishTag, Long startTime, Long endTime, Integer isHand);
    EGridResult<OrderTaking> searchByUpdateTime(String cid, Integer finishTag, Long startTime, Long endTime, Integer isHand, Integer page, Integer rows);

    //objectId查询司机是否有未完成(finishTag!=2)的订单
    List<OrderTaking> searchById(String did, int i);

    /**
     * 生成Document （事务方式添加数据 需要的Document）
     */
    Document createOTDoc(String oid, Goods goods, DriverInfo driverInfo, Integer finishTag, Integer isHand, Integer driverIsAgree, Date time);
    Document createOTDoc(Order order, DriverInfo driverInfo, Integer finishTag, Integer isHand, Integer driverIsAgree, Date time);
    Document createOTDoc(Order order, String did, String dvrMobile, String dvrCarNum, Integer finishTag, Integer isHand, Integer driverIsAgree, Date time);
}
