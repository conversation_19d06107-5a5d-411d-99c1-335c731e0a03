package com.erdos.coal.park.web.app.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

public class CustomerAccountData implements Serializable {
    private String id;
    private String name;
    private String mobile;
    private BigDecimal totalFee = new BigDecimal("0");   //用户在平台账户 可用总金额
    /*private BigDecimal aliFee = new BigDecimal("0");   //支付宝充值的总金额
    private BigDecimal weChatFee = new BigDecimal("0");   //微信充值的总金额
    private BigDecimal fee = new BigDecimal("0");   //订单撤销返回的总金额
    private BigDecimal payGidFee = new BigDecimal("0");   //代支付货运信息费，从账户扣除的总金额
    private BigDecimal payGidFeeAli = new BigDecimal("0");   //代支付货运信息费，从支付宝支付的总金额
    private BigDecimal payGidFeeWeChat = new BigDecimal("0");   //代支付货运信息费，从微信支付的总金额*/

    private Integer type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
