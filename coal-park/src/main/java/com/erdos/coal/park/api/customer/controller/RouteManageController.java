package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.IRouteService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP线路地址管理接口列表"
@RestController
@RequestMapping("/api/cus/route")
public class RouteManageController extends BaseController {
    @Resource
    private IRouteService routeService;

    @InvokeLog(description = "地图调用查询总里程和过路费 接口", printReturn = false) //日志
    @PostMapping(value = "/map")
    public ServerResponse<Object> mapHandler(
            @RequestParam(value = "origin") String origin,             //"出发地坐标(经度，纬度)"
            @RequestParam(value = "destination") String destination    //"目的地坐标(经度，纬度)"
    ) throws GlobalException {
        return routeService.testMapRoadLine(origin, destination);
    }

    @InvokeLog(description = "添加自定义线路 接口") //日志
    @PostMapping(value = "/add_route")
    public ServerResponse<Object> addRouteHandler(
            @RequestParam(value = "beginPoint") String beginPoint,     //"出发地"
            @RequestParam(value = "endPoint") String endPoint,         //"目的地"
            @RequestParam(value = "distance") Long distance,           //"总里程"
            @RequestParam(value = "price") Double price,               //"单价"
            @RequestParam(value = "tolls", required = false) Double tolls//"过路费预估"
    ) throws GlobalException {
        return routeService.addRoute(beginPoint, endPoint, price, distance, tolls);
    }

    @InvokeLog(description = "查询自定义线路 接口", printReturn = false) //日志
    @PostMapping(value = "/search_route")
    public ServerResponse<EGridResult> searchRouteHandler(
            @RequestParam(value = "beginPoint", required = false) String beginPoint,   //"出发地"
            @RequestParam(value = "endPoint", required = false) String endPoint,       //"目的地"
            @RequestParam(value = "page", required = false) Integer page,              //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows               //"每页多少条"
    ) throws GlobalException {
        return routeService.searchRoute(beginPoint, endPoint, page, rows);
    }

    @InvokeLog(description = "删除自定义线路 接口") //日志
    @PostMapping(value = "/del_route")
    public ServerResponse<String> delRouteHandler(
            @RequestParam(value = "routeId") String routeId    //"自定义路线编号"
    ) throws GlobalException {
        return routeService.delRoute(routeId);
    }
}
