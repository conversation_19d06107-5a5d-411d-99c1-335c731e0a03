package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.QuarantineInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface IQuarantineInfoService extends IBaseMongoService<QuarantineInfo> {

    //查询司机最新的一条防疫申报信息
    QuarantineInfo searchDvrLastQuarantine(String did, String defaultDownUnit);

    //检查司机需要提交防疫申报
    boolean checkDvrIsQuarantine(String did, DriverInfo driverInfo, String defaultDownUnit);

    //识别防疫申报图片信息
    QuarantineInfo imageRecognition(DriverInfo driverInfo, String healthCodePho, String travelCardPho, String temperaturePro, String temperature) throws IOException;

    //图片上传时校验
    String checkPho(MultipartFile uploadPho, String type, DriverInfo driverInfo) throws IOException;

    //自动审核防疫信息
    String auditQuarantineInfo(QuarantineInfo quarantineInfo);

    //企业终端上传司机防疫识别信息
    ServerResponse<String> synQuarantine(String sxId, String addr, String deviceNo, String dvrName, String dvrIdCardNum, String healthCodeLevel, String nucleate, Double temperature);
    ServerResponse<String> synQuarantine2(String sxId, String addr, String deviceNo, String dvrName, String dvrIdCardNum, String healthCodeLevel, String nucleate, Double temperature);
}
