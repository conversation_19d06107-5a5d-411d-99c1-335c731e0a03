package com.erdos.coal.park.api.manage.entity;


import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.Indexes;

@Entity(value = "t_sms_limit", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("mobile")})
})
public class SMSLimit extends BaseMongoInfo {
    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
