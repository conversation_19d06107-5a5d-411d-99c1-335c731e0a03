package com.erdos.coal.park.web.sys.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.AccessLimit;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.manage.service.IAppLogService;
import com.erdos.coal.park.web.sys.service.ISampleService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/test/sample")
public class SampleController extends BaseController {

    @Resource
    private ISampleService sampleService;

    @Resource
    private IAppLogService appLogService;

    @RequestMapping("/slow")
    public ServerResponse slow() {
        return ServerResponse.createSuccess(sampleService.slow(400_000, 10));
    }

    @RequestMapping("/fast")
    public ServerResponse fast() {
        return ServerResponse.createSuccess(sampleService.fast(400_000, 10));
    }

    // -----------------------------------------------------------------------------------------------------------------

    /**
     * 获取所有接口列表信息
     *
     * @return html 页面
     */
    @AccessLimit(key = "sample_api_list", perSecond = 2, timeOut = 1)
    @GetMapping(value = "/api_list", produces = "text/html;charset=utf-8")
    public String getApiListHtmlString() {
        return sampleService.getApiListHtmlString();
    }

    /**
     * 获取1小时内接口请求情况
     *
     * @return html 页面
     */
    @AccessLimit(key = "sample_request_list_hour", perSecond = 2, timeOut = 1)
    @GetMapping(value = "/hour_list", produces = "text/html;charset=utf-8")
    public String getRequestListWithinAnHour() {
        return sampleService.getRequestListWithinAnHour();
    }

    /**
     * 获取1天内接口请求情况
     *
     * @return html 页面
     */
    @AccessLimit(key = "sample_request_list_day", perSecond = 2, timeOut = 1)
    @GetMapping(value = "/day_list", produces = "text/html;charset=utf-8")
    public String getRequestListWithinAnDay() {
        return sampleService.getRequestListWithinAnDay();
    }

    /**
     * 获取昨天一天内接口请求情况
     *
     * @return html 页面
     */
    @AccessLimit(key = "sample_request_list_yesterday", perSecond = 2, timeOut = 1)
    @GetMapping(value = "/yesterday_list", produces = "text/html;charset=utf-8")
    public String getRequestListWithinAnYesterday() {
        return sampleService.getRequestListWithinAnYesterday();
    }

    /**
     * 获取今天接口请求情况
     *
     * @return html 页面
     */
    @AccessLimit(key = "sample_request_list_today", perSecond = 2, timeOut = 1)
    @GetMapping(value = "/today_list", produces = "text/html;charset=utf-8")
    public String getRequestListWithinAnToday() {
        return sampleService.getRequestListWithinAnToday();
    }
}
