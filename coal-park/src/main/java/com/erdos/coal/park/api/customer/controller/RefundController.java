package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IRefundPreService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.web.app.pojo.RefundPreData;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//"APP提现接口列表"
@RestController
@RequestMapping("/api/pay/refund")
public class RefundController extends BaseController {
    @Resource
    private IRefundPreService refundPreService;
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private ICustomerUserService customerUserService;

    @InvokeLog(description = "客商app绑定支付宝账户 接口", printReturn = false) //日志
    @PostMapping(value = "/pre_binding_ali_pay_cus")
    public ServerResponse<String> PreBindingAliPayCusHandler() throws GlobalException {
        return refundPreService.PreBindingAliPay(0);
    }

    @InvokeLog(description = "查询微信用户昵称 接口", printReturn = false) //日志
    @PostMapping(value = "/get_wx_nickname")
    public ServerResponse<String> getWxNicknameHandler() throws GlobalException {
        return refundPreService.getWxNickname();
    }

    @InvokeLog(description = "绑定微信号(openid,昵称) 接口", printReturn = false) //日志
    @PostMapping(value = "/bind_wx_openid")
    public ServerResponse<String> bindWxOpenidHandler(
            @RequestParam(value = "code") String code,     //"登录时获取的 code"
            @RequestParam(value = "wxName") String wxName  //"微信账号真实姓名"
    ) throws GlobalException {
        return refundPreService.bindWxOpenid(code, wxName);
    }

    @InvokeLog(description = "企业付款到零钱申请 接口", printReturn = false) //日志
    @PostMapping(value = "/wx_refund_apply")
    public ServerResponse<String> wxRefundApplyHandler(
            @RequestParam(value = "amount") double amount,     //"提现金额"
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "name", required = false) String name //"微信账号真实姓名"
    ) throws GlobalException {

        if (StrUtil.isEmpty(openid) || openid.equals("null")) {
            String userType = ShiroUtils.getUserType();
            if (userType.equals("DU")) {
                DriverInfo driverInfo = driverInfoService.getByPK(ShiroUtils.getUserId());
                openid = driverInfo.getPhoneId();
                name = driverInfo.getName();
            } else if (userType.equals("CU")) {
                CustomerUser customerUser = customerUserService.getByPK(ShiroUtils.getUserId());
                openid = customerUser.getPhoneId();
                name = customerUser.getName();
            }
        }
        return refundPreService.wxRefundApply(amount, openid, name);

//        return refundPreService.wxRefundApply(amount);
    }

    @InvokeLog(description = "企业付款到零钱进度查询 接口", printReturn = false) //日志
    @PostMapping(value = "/query_refund")
    public ServerResponse<List<RefundPreData>> queryRefundHandler() throws GlobalException {
        return refundPreService.queryRefund();
    }

    @InvokeLog(description = "司机app绑定支付宝账户 接口", printReturn = false) //日志
    @PostMapping(value = "/pre_binding_ali_pay_dri")
    public ServerResponse<String> PreBindingAliPayDriHandler() throws GlobalException {
        return refundPreService.PreBindingAliPay(1);
    }

    @InvokeLog(description = "支付宝auth_code授权app获取userId 接口", printReturn = false) //日志
    @PostMapping(value = "/binding_ali_pay_dri")
    public ServerResponse<String> BindingAliPayDriHandler(
            @RequestParam(value = "appId") String appId,       //"开发者应用在支付宝的appId"
            @RequestParam(value = "authCode") String authCode  //"临时授权码"
    ) throws GlobalException {
        return refundPreService.BindingAliPay(appId, authCode);
    }

    @InvokeLog(description = "客商提出支付宝提现申请 接口", printReturn = false) //日志
    @PostMapping(value = "/refund_aliPay_cus")
    public ServerResponse<String> RefundAliPayCusHandler(
            @RequestParam(value = "aliPayLoginId", required = false) String aliPayLoginId, //"支付宝登录号（邮箱或手机号）"
            @RequestParam(value = "name", required = false) String name,                   //"真实姓名"
            @RequestParam(value = "amount") String amount                                  //"提现金额"
    ) throws GlobalException {
        return refundPreService.RefundAliPayPre(aliPayLoginId, name, amount);
    }

    @InvokeLog(description = "司机提出支付宝提现申请 接口", printReturn = false) //日志
    @PostMapping(value = "/refund_aliPay_dri")
    public ServerResponse<String> RefundAliPayDriHandler(
            @RequestParam(value = "aliPayLoginId", required = false) String aliPayLoginId, //"支付宝登录号（邮箱或手机号）"
            @RequestParam(value = "name", required = false) String name,                   //"真实姓名"
            @RequestParam(value = "amount") String amount                                  //"提现金额"
    ) throws GlobalException {
        return refundPreService.RefundAliPayPre(aliPayLoginId, name, amount);
    }
}
