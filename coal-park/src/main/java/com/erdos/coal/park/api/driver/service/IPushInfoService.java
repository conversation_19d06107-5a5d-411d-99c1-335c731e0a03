package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.PushInfo;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;

import java.util.List;
import java.util.Map;

public interface IPushInfoService extends IBaseMongoService<PushInfo> {
    ServerResponse<List<PushInfo>> pushList(Integer type);

    ServerResponse<String> pushEdit(String id);

    void addPushInfo(String title, String body, String did, Integer type, String errCode, String errMsg, String msGid);

    void addAndSendPushInfo(String title, String body, String did, Integer type, String deviceId);

    void addAndSendPushInfos(String title, String body, Integer type, List<DriverInfo> dInfos, List<String> oids);

    //根据订单号查询客商申请撤销订单
    ServerResponse<DriverOrderData> pushQueryDelete(String oid);

    //微信消息推送---司机审核结果通知 (司机防疫申报审核结果通知)
    Map<String, String> weChatSendDvr(String openId, String mobile, String name, String phrase, String thing);

    //微信消息推送---司机关联车辆审核结果通知
    Map<String, String> weChatSendCar(String openId, String mobile, String name, String phrase, String thing, String carNum, String did);

    //微信消息推送---司机进场叫号
    Map<String, String> weChatSend4081(String openId, String name, String date, String thing);

    //微信消息推送---车辆叫号进场
    void weChatSend36090(String openId, String name, String date, String thing, String msg, String did);

    //微信消息推送---排队入场通知
    void weChatSend14370(String openId, String oid, String carNum, String data, String thing, String did);

    //微信消息推送---邀约结果通知
    void weChatSend13662(String openId, String cName, String thing, String did, String groupNo);
}
