package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.CidRelation;
import com.erdos.coal.park.api.customer.pojo.CidRelationData;

import java.util.List;

public interface ICidRelationService extends IBaseMongoService<CidRelation> {
    ServerResponse<String> addCidRelation(String mobile);

    ServerResponse<String> delCidRelation(String id);

    //ServerResponse<List<CidRelationData>> listCidRelation();
    ServerResponse<EGridResult> listCidRelation(Integer page, Integer rows);
}
