package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.sys.dao.ISysUserDao;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.park.web.sys.service.ISysUserService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("sysUserService")
public class SysUserServiceImpl extends BaseMongoServiceImpl<SysUser, ISysUserDao> implements ISysUserService {
    @Override
    public EGridResult loadUserGrid(Integer page, Integer rows) {

        Query<SysUser> query = this.createQuery();

        query.and(
                query.criteria("enable").equal(1) // 可用
                // query.criteria("id").notEqual("1") //去掉管理员
        );

        query.order(Sort.ascending("order"));

        return this.findPage(page, rows, query);
    }

    boolean checkSysUser(SysUser user) {
        if (user == null) return false;

        if (user.getName() == null) return false;
        if (user.getLoginName() == null) return false;
        if (!user.getLoginName().equals("admin"))
            return StrUtil.isPhone(user.getLoginName());
        // ...

        return true;
    }

    @Override
    public ServerResponse addUser(SysUser sysUser) {
        if (checkSysUser(sysUser)) {
            sysUser.setId(Utils.getUUID());
            sysUser.setType(1);
            if (sysUser.getRoles() == null) sysUser.setRoles(new String[]{});
            /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
            String str = passwordEncoder.encode(StrUtil.isEmpty(sysUser.getPassword()) ? "123456" : sysUser.getPassword());
            sysUser.setPassword(str);*/

            String str = StrUtil.isEmail(sysUser.getPassword()) ? "123456" : sysUser.getPassword();
            sysUser.setPassword(Utils.md5(str));
            this.save(sysUser);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse editUser(SysUser sysUser) {

        if (checkSysUser(sysUser) || sysUser.getId() != null || sysUser.getUpdateTime() != null) {

            if (sysUser.getRoles() == null) sysUser.setRoles(new String[]{});

            // condition
            Query<SysUser> condition = this.createQuery();
            condition.filter("id", sysUser.getId());
            condition.filter("updateTime", sysUser.getUpdateTime());

            //value
            UpdateOperations<SysUser> value = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(sysUser.getName())) value.set("name", sysUser.getName());
            if (StrUtil.isNotEmpty(sysUser.getLoginName())) value.set("loginName", sysUser.getLoginName());
            if (StrUtil.isNotEmpty(sysUser.getEmail())) value.set("email", sysUser.getEmail());
            if (StrUtil.isNotEmpty(sysUser.getRoles())) value.set("roles", sysUser.getRoles());
            if (StrUtil.isNotEmpty(sysUser.getPassword())) value.set("password", Utils.md5(sysUser.getPassword()));

            this.update(condition, value);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse deleteUser(SysUser sysUser) {
        String id = sysUser.getId();
        SysUser has = this.get("id", id);
        if (has != null) {
            this.delete(has.getObjectId());
            return ServerResponse.createSuccess();
        }
        return ServerResponse.createError();
    }

    @Override
    public SysUser findUserByName(String name) {
        return this.get(this.createQuery().filter("id", name));
    }

    @Override
    public List<String> findPermissions(String name) {
        //返回当前用户权限列表
        List<String> permissions = new ArrayList<>();
        permissions.add("sys:menu:view");
        permissions.add("sys:menu:add");
        permissions.add("sys:menu:edit");
        permissions.add("sys:menu:delete");
        return permissions;
    }

}

















