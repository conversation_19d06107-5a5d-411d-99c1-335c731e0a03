package com.erdos.coal.park.api.manage.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.park.api.manage.entity.Locked;
import com.erdos.coal.park.api.manage.service.ILockedService;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateResults;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service("lockedService")
public class LockedServiceImpl extends BaseMongoDAOImpl<Locked> implements ILockedService {

    @Override
    public CompletableFuture<String> test1(String code, int tid) {

        String result;

//        try {
//            Thread.sleep(1);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }

        //synchronized (Integer.toHexString(tid)){

        Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("code", code);

        Locked locked = this.get(mapParam);
        if (locked == null) {
            locked = new Locked();
            locked.setUserId(code);
            locked.setLockTime(new Date());
            locked.setTest("当前线程=" + tid);
            this.save(locked);
            result = "已添加线程:" + tid;
        } else {
            result = "正在处理中, threadID:" + tid;
        }

        //}

        return CompletableFuture.completedFuture(result);
    }

    @Override
    public boolean getLock(String code, int type, int threadID) {
        Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("userId", code);
        mapParam.put("type", type);

        UpdateResults result = this.update(mapParam, mapParam);
        boolean exists = result.getUpdatedExisting();
        return !exists;

        //TRUE:   UpdateResults{wr=WriteResult{n=1, updateOfExisting=false, upsertedId=5d3801c159a9476cd12737a7}}

        //FALSE:  UpdateResults{wr=WriteResult{n=1, updateOfExisting=true, upsertedId=null}}
    }

    @Override
    public void unLock(String code, int type) {
        Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("userId", code);
        mapParam.put("type", type);

        this.delete(mapParam);
    }

    @Override
    public boolean getLock(String code, int type) {
        Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("userId", code);
        mapParam.put("type", type);

        UpdateResults result = this.update(mapParam, mapParam);
        return !result.getUpdatedExisting();

        //TRUE:   UpdateResults{wr=WriteResult{n=1, updateOfExisting=false, upsertedId=5d3801c159a9476cd12737a7}}
        //FALSE:  UpdateResults{wr=WriteResult{n=1, updateOfExisting=true, upsertedId=null}}
    }

    @Override
    public boolean orderQuarantineLock(String oid, int type) {
        Query<Locked> query = this.createQuery();
        query.filter("userId", oid);
        query.filter("type", type);
        Locked locked = this.get(query);

        if (locked != null) return false;

        locked = new Locked();
        locked.setUserId(oid);
        locked.setType(type);
        locked.setLockTime(new Date());
        String test = JWT.create().withExpiresAt(new Date()).sign(Algorithm.HMAC256("secret12345a"));
        locked.setTest(test);
        this.save(locked);

        return true;
    }

    @Override
    public void orderQuarantineUnLock(String oid, int type) {
        Query<Locked> query = this.createQuery();
        query.filter("userId", oid);
        query.filter("type", type);
        this.delete(query);
    }
}
