package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;
import dev.morphia.annotations.*;

import java.util.List;

@Entity(value = "sys_switch", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("id")}, options = @IndexOptions(unique = true))
})
public class SysSwitch extends BaseMongoInfo {

    //@Indexed(options = @IndexOptions(name = "_idx_unit_id", unique = true, background = true))
    private String id;
    private Integer code;       //0-客商，1-司机，2-车辆，3-订单 , 4-司机接单支付方式（零钱扣款，微信直接付款[配合分账使用的])
    private Integer check;      //用户账号是否需要审核才能使用    0-需要审核1-无需审核

    //0-code为客商时
    private Integer share;      //是否允许分享给微信小程序   0-允许1-不允许

    //1-code为司机时
    private Integer type;       //类型   code=1：0-app,1-微信小程序,2-订单退款； code=0：3-下单，4-订单退款
    private Integer photo;      //上传照片数量    0（无需上传）3（2张行驶证，车主身份证）8（全部照片）
    private Integer refundType; //司机退款类型    0-客商撤单司机同意，1-司机主动撤单

    //2-code为车辆时
    private List<CheckBoxData> dvrPhotoTypes;   //需要上传司机照片类型数量
    private List<CheckBoxData> carPhotoTypes;   //需要上传车辆照片类型数量
    private Integer needGPS;                    //车辆注册时是否需要GPS 0或空-不需要，1-需要

    //3-code为订单时
    private Integer isRefund;   //撤单平台是否退费，0-退费，1-不退费
    private Integer smsFee;     //短信通知费
    private Integer phoneFee;   //电话呼叫费
    private Integer queuingFee; //排队查询费
    private Integer queuingNum; //排队查询次数

    //4-code为司机接单支付时
    private Integer payType;    //司机接单支付方式 null、0-余额扣费，1-微信直接付款[是为了配合分账使用]

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public Integer getCheck() {
        return check;
    }

    public void setCheck(Integer check) {
        this.check = check;
    }

    public Integer getPhoto() {
        return photo;
    }

    public void setPhoto(Integer photo) {
        this.photo = photo;
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    public List<CheckBoxData> getDvrPhotoTypes() {
        return dvrPhotoTypes;
    }

    public void setDvrPhotoTypes(List<CheckBoxData> dvrPhotoTypes) {
        this.dvrPhotoTypes = dvrPhotoTypes;
    }

    public List<CheckBoxData> getCarPhotoTypes() {
        return carPhotoTypes;
    }

    public void setCarPhotoTypes(List<CheckBoxData> carPhotoTypes) {
        this.carPhotoTypes = carPhotoTypes;
    }

    public Integer getNeedGPS() {
        return needGPS;
    }

    public void setNeedGPS(Integer needGPS) {
        this.needGPS = needGPS;
    }

    public Integer getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Integer isRefund) {
        this.isRefund = isRefund;
    }

    public Integer getSmsFee() {
        return smsFee;
    }

    public void setSmsFee(Integer smsFee) {
        this.smsFee = smsFee;
    }

    public Integer getPhoneFee() {
        return phoneFee;
    }

    public void setPhoneFee(Integer phoneFee) {
        this.phoneFee = phoneFee;
    }

    public Integer getQueuingFee() {
        return queuingFee;
    }

    public void setQueuingFee(Integer queuingFee) {
        this.queuingFee = queuingFee;
    }

    public Integer getQueuingNum() {
        return queuingNum;
    }

    public void setQueuingNum(Integer queuingNum) {
        this.queuingNum = queuingNum;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }
}
