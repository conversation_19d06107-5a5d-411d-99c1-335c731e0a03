package com.erdos.coal.park.web.sys.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.math.BigDecimal;

@Entity(value = "t_third_party_account", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("unitCode")}),
        @Index(fields = {@Field("id")}),
        @Index(fields = {@Field("payerId")}),
        @Index(fields = {@Field("oid")}),
        @Index(fields = {@Field("outTradeNo")}),
        @Index(fields = {@Field("preferentialRefundNo")}, options = @IndexOptions(unique = true, partialFilter = "{preferentialRefundNo:{$exists:true}}"))
})
public class ThirdPartyAccount extends BaseMongoInfo {
    private String unitCode;        //编辑三方账号的单位
    private String id;              //三方账号
    private String name;              //三方账号人姓名
    private String payerId;
    private Integer type;           //0-司机接单所得，1-订单废除退款,2-提现扣除余额, 3-优惠返款给司机
    private BigDecimal totalFee;    //金额（分）
    private String oid;

    private String outTradeNo;  //商户订单号
    private Integer pay;    //转账是否成功，null-未转账，0-失败，1-成功

    private String preferentialRefundNo;    //优惠返款唯一订单号 由二级单位编号+日期+司机id+carNum组成

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public Integer getPay() {
        return pay;
    }

    public void setPay(Integer pay) {
        this.pay = pay;
    }

    public String getPreferentialRefundNo() {
        return preferentialRefundNo;
    }

    public void setPreferentialRefundNo(String preferentialRefundNo) {
        this.preferentialRefundNo = preferentialRefundNo;
    }
}
