package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.web.app.dao.ICusHDDKSDao;
import com.erdos.coal.park.web.app.entity.CusHDDKS;
import com.erdos.coal.park.web.app.service.ICusHDDKSService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Service("cusHDDKSService")
public class CusHDDKSServiceImpl extends BaseMongoServiceImpl<CusHDDKS, ICusHDDKSDao> implements ICusHDDKSService {
    @Resource
    private HttpServletRequest request;
    @Resource
    private ICusHDDKSDao cusHDDKSDao;
    @Resource
    private ICustomerUserService customerUserService;

    @Override
    public ServerResponse<EGridResult> loadGrid(Integer page, Integer rows) {
        String mobile = request.getParameter("mobile");

        Query<CusHDDKS> query = cusHDDKSDao.createQuery();
        if (StrUtil.isNotEmpty(mobile)) {
            CustomerUser customerUser = customerUserService.get("mobile", mobile);
            if (customerUser == null) return ServerResponse.createSuccess(new EGridResult());
            query.criteria("cid").contains(customerUser.getObjectId().toHexString());
        }

        EGridResult<CusHDDKS> result = cusHDDKSDao.findPage(page, rows, query);
        if (StrUtil.isNotEmpty(mobile)) {
            List<CusHDDKS> list = result.getRows();
            for (CusHDDKS cusHDDKS : list) {
                CustomerUser customerUser = customerUserService.getByPK(cusHDDKS.getCid());
                if (!cusHDDKS.getMobile().equals(customerUser.getMobile()) || (StrUtil.isNotEmpty(cusHDDKS.getName()) && !cusHDDKS.getName().equals(customerUser.getName()))) {
                    Query<CusHDDKS> upQuery = cusHDDKSDao.createQuery();
                    upQuery.criteria("id").equal(cusHDDKS.getId());
                    UpdateOperations<CusHDDKS> updateOperations = cusHDDKSDao.createUpdateOperations();
                    updateOperations.set("mobile", customerUser.getMobile());
                    if (StrUtil.isNotEmpty(customerUser.getName()))
                        updateOperations.set("name", customerUser.getName());
                    cusHDDKSDao.update(upQuery, updateOperations);
                }
            }

        }
        return ServerResponse.createSuccess(result);
    }

    @Override
    public ServerResponse<String> addCusHDDKS(CusHDDKS cusHDDKS) {
        String mobile = cusHDDKS.getMobile();
        CustomerUser customerUser = customerUserService.get("mobile", mobile);
        if (customerUser == null) return ServerResponse.createError("手机号错误，客商不存在");

        CusHDDKS cusHDDKS1 = cusHDDKSDao.get("cid", customerUser.getObjectId().toHexString());
        if (cusHDDKS1 != null)
            return ServerResponse.createError("[" + cusHDDKS.getMobile() + "]客商已存在key&secret，不可重复添加");

        cusHDDKS.setId(Utils.getUUID());
        cusHDDKS.setCid(customerUser.getObjectId().toHexString());
        if (StrUtil.isNotEmpty(customerUser.getName())) cusHDDKS.setName(customerUser.getName());
        cusHDDKSDao.save(cusHDDKS);
        return ServerResponse.createSuccess("添加成功");
    }

    @Override
    public ServerResponse<String> editCusHDDKS(CusHDDKS cusHDDKS) {
        CusHDDKS oldCusHDDKS = cusHDDKSDao.get("id", cusHDDKS.getId());

        Query<CusHDDKS> query = cusHDDKSDao.createQuery();
        query.criteria("id").equal(cusHDDKS.getId());

        UpdateOperations<CusHDDKS> updateOperations = cusHDDKSDao.createUpdateOperations();
        if (StrUtil.isNotEmpty(cusHDDKS.getAppKey()) && !cusHDDKS.getAppKey().equals(oldCusHDDKS.getAppKey()))
            updateOperations.set("appKey", cusHDDKS.getAppKey());
        if (StrUtil.isNotEmpty(cusHDDKS.getAppSecret()) && !cusHDDKS.getAppSecret().equals(oldCusHDDKS.getAppSecret()))
            updateOperations.set("appSecret", cusHDDKS.getAppSecret());

        cusHDDKSDao.update(query, updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }

    @Override
    public ServerResponse<String> delCusHDDKS(CusHDDKS cusHDDKS) {
        Query<CusHDDKS> query = cusHDDKSDao.createQuery();
        query.criteria("id").equal(cusHDDKS.getId());
        cusHDDKSDao.delete(query);
        return ServerResponse.createSuccess("删除成功");
    }
}
