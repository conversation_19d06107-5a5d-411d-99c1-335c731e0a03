package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.sys.entity.SysRole;

import java.util.HashMap;
import java.util.List;

public interface ISysRoleService extends IBaseMongoService<SysRole> {

    EGridResult loadGrid(Integer page, Integer rows);

    EGridResult loadTreeGrid(Integer page, Integer rows);

    ServerResponse addRole(SysRole sysRole);

    ServerResponse editRole(SysRole sysRole);

    ServerResponse deleteRole(SysRole sysRole);

    //角色下拉列表
    List<HashMap<String, Object>> roleSel();
}