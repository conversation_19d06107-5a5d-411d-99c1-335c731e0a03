package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.web.app.service.IWebCustomerService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/web/app/customer")
public class WebCustomerController extends BaseController {

    @Resource
    private IWebCustomerService webCustomerService;

    @PostMapping("/list")
    public ServerResponse<EGridResult> listHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webCustomerService.loadGrid(page, rows));
    }

    @PostMapping("/save")
    //public ServerResponse<String> saveHandler(String inserted, String updated, String deleted) {
    //return webCustomerService.saveState(inserted, updated, deleted);
    public ServerResponse<String> saveHandler(@RequestBody CustomerUser cUser) throws GlobalException {
        return webCustomerService.editUserState(cUser);
    }

    @PostMapping("/account")
    public ServerResponse<EGridResult> accountHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webCustomerService.loadAccountGrid(page, rows));
    }

    @PostMapping("/account/edit")
    public ServerResponse<EGridResult> accEditHandler(Integer page, Integer rows) {
        return ServerResponse.createSuccess(webCustomerService.accEdit(page, rows));
    }

    @PostMapping("/account/getAvailableFee")
    public ServerResponse<BigDecimal> getAvailableFee() {
        return ServerResponse.createSuccess(webCustomerService.getAvailableFee());
    }

    @PostMapping("/to_third_party")
    public ServerResponse<String> toThirdPartyHandler(@RequestBody CustomerUser cUser) throws GlobalException {
        return webCustomerService.toThirdParty(cUser);
    }

    @PostMapping("/search_by_mobile")
    public ServerResponse<List<CustomerUser>> searchByMobileHandler() throws GlobalException {
        return webCustomerService.searchByMobile();
    }
}
