package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.app.service.IWebAccountService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/account")
public class WebAccountController extends BaseController {
    @Resource
    private IWebAccountService webAccountService;

    @PostMapping("/report")
    public ServerResponse<EGridResult> report(Integer page, Integer rows) {
        return webAccountService.report(page, rows);
    }

    @PostMapping("/detail")
    public ServerResponse<EGridResult> detail(Integer page, Integer rows) {
        return webAccountService.detail(page, rows);
    }

    @PostMapping("/order_detail")
    public ServerResponse<EGridResult> orderDetail(Integer page, Integer rows) {
        return webAccountService.orderDetail(page, rows);
    }

    @PostMapping("/report2")
    public ServerResponse<EGridResult> report2(Integer page, Integer rows) {
        return webAccountService.report2(page, rows);
    }

    @PostMapping("/report3")
    public ServerResponse<EGridResult> report3(Integer page, Integer rows) {
        return webAccountService.report3(page, rows);
    }

    @PostMapping("/statistics_third_party_account")
    public ServerResponse<EGridResult> statisticsThirdPartyAccount(Integer page, Integer rows) {
        return webAccountService.statisticsThirdPartyAccount(page, rows);
    }

    @PostMapping("/report4")
    public ServerResponse<EGridResult> report4(Integer page, Integer rows) {
        return webAccountService.report4(page, rows);
    }

    @PostMapping("/preferentialRefundRecordList")
    public ServerResponse<EGridResult> PreferentialRefundRecordList(Integer page, Integer rows) {
        return webAccountService.preferentialRefundRecordList(page, rows);
    }
}
