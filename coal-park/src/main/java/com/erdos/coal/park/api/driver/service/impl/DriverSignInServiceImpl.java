package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.driver.dao.IDriverSignInDao;
import com.erdos.coal.park.api.driver.entity.DriverSignIn;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.park.api.driver.service.IDriverSignInService;
import org.springframework.stereotype.Service;

@Service("driverSignInService")
public class DriverSignInServiceImpl extends BaseMongoServiceImpl<DriverSignIn, IDriverSignInDao> implements IDriverSignInService {
}
