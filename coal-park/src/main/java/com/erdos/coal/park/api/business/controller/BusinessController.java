package com.erdos.coal.park.api.business.controller;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.service.IReceiveCarMsgService;
import com.erdos.coal.park.api.business.service.IUnitInfoService;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by LIGX on 2018/12/27.
 * 业务接口
 */
@RestController
@RequestMapping("/api/bus")
public class BusinessController extends BaseController {
    @Resource
    private ISMSService ismsService;
    @Resource
    private IReceiveCarMsgService receiveCarMsgService;
    @Resource
    private IUnitInfoService unitInfoService;

    //登录 -------------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "业务系统登录接口") //日志
    @PostMapping(value = "/biz_login")
    public ServerResponse<AccessToken> loginHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String username = (String) data.get("username");  //用户名
        String password = (String) data.get("password");  //密码
        return unitInfoService.bizUserLogin(username, password);
    }

    //登出 -------------------------------------------------------------------------------------------------------------
    @InvokeLog(description = "业务系统登出接口") //日志
    @PostMapping(value = "/biz_login_out")
    public ServerResponse<AccessToken> loginOutHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        //登出系统，清除SecurityContextHolder内容
        return null;//loginManageHandler.bizUserLogin(username, password);
    }

    @InvokeLog(description = "企业系统发送短信验证码 接口") //日志
    @PostMapping(value = "/biz_send_sms")
    public ServerResponse<String> boundCustomerSendSMSHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");

        String telCode = (String) data.get("telCode");  //手机号
        String type = (String) data.get("type");  //类型--根据这个类型，选择后台设定好的短信签名和磨边编号

        if (StrUtil.isEmpty(telCode) || StrUtil.isEmpty(type)) {
            return ServerResponse.createError("参数错误");
        }
        String signName = (String) data.get("signName");    //短信签名
        String tempCode = (String) data.get("tempCode");    //模板编号

        return ismsService.sendSMS(telCode);
    }

    @InvokeLog(description = "企业系统推送车牌号 接口")   //日志
    @PostMapping(value = "/biz_push_by_carNum")
    public ServerResponse<String> pushByCarNumHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String list = (String) data.get("listStr");
        return receiveCarMsgService.pushByCarNum(list);
    }

    @InvokeLog(description = "企业系统推送订单号-装卸 接口")   //日志
    @PostMapping(value = "/biz_push_by_billcode")
    public ServerResponse<String> pushByBillCodeHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String list = (String) data.get("listStr");
        return receiveCarMsgService.pushByBillCode(list);
    }
   /* @InvokeLog(description = "企业系统推送线下订单 接口")   //日志
    @PostMapping(value = "/add_offline_goods")
    public ServerResponse<String> addOfflineGoodsHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        *//*if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String list = (String) data.get("listStr");
        return receiveCarMsgService.pushByCarNum(list);*//*
        return null;
    }*/

    @InvokeLog(description = "企业确认司机订单车型 接口")   //日志
    @PostMapping(value = "/update_order_vehicleName")
    public ServerResponse<String> updateOrderVehicleNameHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String billCode = data.getString("billCode");
        String vehicleCode = data.getString("vehicleCode");

        return receiveCarMsgService.updateOrderVehicleName(billCode, vehicleCode);
    }

    @InvokeLog(description = "企业修改订单备注4 接口")   //日志
    @PostMapping(value = "/update_order_remark4")
    public ServerResponse<String> updateOrderRemark4Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签错误！");
        String billCode = data.getString("billCode");
        String remark4 = data.getString("remark4");

        return receiveCarMsgService.updateOrderRemark4(billCode, remark4);
    }
}