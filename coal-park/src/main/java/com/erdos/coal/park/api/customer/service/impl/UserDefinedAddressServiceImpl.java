package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.customer.dao.IUserDefinedAddressDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.UserDefinedAddress;
import com.erdos.coal.park.api.customer.service.ICustomerUserService;
import com.erdos.coal.park.api.customer.service.IUserDefinedAddressService;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.park.web.app.entity.DistrictCode;
import com.erdos.coal.park.web.app.service.IDistrictCodeService;
import com.erdos.coal.utils.StrUtil;
import com.erdos.coal.utils.SysConstants;
import dev.morphia.geo.GeoJson;
import dev.morphia.geo.Point;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("userDefinedAddressService")
public class UserDefinedAddressServiceImpl extends BaseMongoServiceImpl<UserDefinedAddress, IUserDefinedAddressDao> implements IUserDefinedAddressService {
    @Resource
    private ILockedService lockedService;
    @Resource
    private ICustomerUserService customerUserService;
    @Resource
    private IDistrictCodeService districtCodeService;

    //自定义地址 按照名称判断是否重复
    private String checkByName(String addName, String fullName, String cid) {
        if (!StrUtil.isEmpty(addName)) {
            Query<UserDefinedAddress> query1 = this.createQuery();
            query1.filter("cid", cid);
            query1.filter("addName", addName);
            UserDefinedAddress uAdd = this.get(query1);
            if (uAdd != null)
                return "自定义名称已存在，请重新命名";
        }
        Query<UserDefinedAddress> query2 = this.createQuery();
        query2.filter("cid", cid);
        query2.filter("fullName", fullName);
        UserDefinedAddress uFul = this.get(query2);
        if (uFul != null)
            return fullName + "已经添加过自定义地址列表";

        return null;
    }

    /**
     * 添加用户自定义地址接口
     * param：String addName, 自定义地址简称
     * param：String fullName, 自定义地址全称
     * param：Integer type，自定义地址类型 0-出发地，1-目的地
     * return：caId，新添加的自定义地址编号
     */
    @Override
    public ServerResponse<Object> addUserDefinedAddress(String addName, String fullName, Double[] location, String province, String city, String district) {
        String cid = ShiroUtils.getUserId();

        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(cid, SysConstants.LockType.Add_Defined_Address.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {

            try {
                String cStr = checkByName(addName, fullName, cid);
                if (!StrUtil.isEmpty(cStr)) {
                    return ServerResponse.createError(cStr);
                }

                UserDefinedAddress uDefAdd = new UserDefinedAddress();
                uDefAdd.setCid(cid);
                if (StrUtil.isEmpty(addName)) {
                    uDefAdd.setAddName(fullName);
                } else {
                    uDefAdd.setAddName(addName);
                }
                uDefAdd.setFullName(fullName);
                Point point = GeoJson.point(location[1], location[0]);
                uDefAdd.setGeometry(point);

                //自定义地址添加区划编码
                if (StrUtil.isNotEmpty(district)) {
                    DistrictCode districtCode = districtCodeService.get("name", district);
                    if (districtCode != null) {
                        uDefAdd.setCode(districtCode.getCode());
                    } else {
                        logger.warn("【" + province + "-" + city + "-" + district + "】查询区划编码为空");
                    }
                }

                UserDefinedAddress result = this.save(uDefAdd);

                String str = "{\"caId\":\"" + result.getObjectId().toString() + "\"}";
                return ServerResponse.createSuccess("添加成功", JSON.parse(str));
            } finally {
                lockedService.unLock(cid, SysConstants.LockType.Add_Defined_Address.getType());
            }
        }
    }

    /**
     * 删除自定义地址接口
     * param：String caId,自定义地址编号
     */
    @Override
    public ServerResponse<String> delUserDefinedAddress(String caId) {
        UserDefinedAddress uDefAdd = this.getByPK(caId);
        if (uDefAdd != null) {
            if (!StrUtil.isEmpty(uDefAdd.getUnitCode())) return ServerResponse.createError("该地址为后台维护地址不可删除！");
            this.delete(uDefAdd.getObjectId());
        }

        return ServerResponse.createSuccess("编号为：【" + caId + "】的自定义地址删除成功");
    }

    /**
     * 查询自定义地址接口
     */
    @Override
    //public ServerResponse<List<UserDefinedAddress>> searchUserDefinedAddress(String unitCode, String addName) {
    public ServerResponse<EGridResult> searchUserDefinedAddress(String unitCode, String addName, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        unitCode = StrUtil.isEmpty(unitCode) ? "" : unitCode;
        addName = StrUtil.isEmpty(addName) ? "" : addName;

        Query<UserDefinedAddress> query = this.createQuery();
        query.or(
                query.and(
                        query.criteria("cid").equal(cid),
                        query.criteria("addName").contains(addName)
                ),
                query.and(
                        query.criteria("cid").equal(null),
                        query.criteria("unitCode").equal(unitCode)
                )
        );
        //List<UserDefinedAddress> list = query.find().toList();

        EGridResult<UserDefinedAddress> eGridResult = findPage(page, rows, query);
        List<UserDefinedAddress> list = eGridResult.getRows();
        List<UserDefinedAddress> resList = new ArrayList<>();
        for (UserDefinedAddress uad : list) {
            UserDefinedAddress temp = new UserDefinedAddress();
            BeanUtils.copyProperties(uad, temp);
            temp.setId(uad.getObjectId().toString());
            resList.add(temp);
        }

        //return ServerResponse.createSuccess("查询成功", list);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(eGridResult.getTotal(), resList));
    }

    @Override
    public ServerResponse<String> addUserDefinedAddressWithMobile(String mobile, String addName, String fullName, Double[] location) {
        CustomerUser cUser = customerUserService.get("mobile", mobile);
        if (Utils.isEmpty(cUser)) return ServerResponse.createError("手机号错误，添加自定义地址失败");
        String cid = cUser.getObjectId().toString();

        //防抖动重复提交加锁
        boolean lock = lockedService.getLock(mobile, SysConstants.LockType.Add_Defined_Address.getType());
        if (!lock) {
            return ServerResponse.createError("重复提交");
        } else {
            try {
                if (StrUtil.isNotEmpty(addName)) {
                    Query<UserDefinedAddress> query = this.createQuery();
                    query.filter("cid", cid);
                    query.filter("addName", addName);
                    UserDefinedAddress checkAddName = this.get(query);
                    if (!Utils.isEmpty(checkAddName)) {
                        return ServerResponse.createError(addName + "重复添加");
                    }
                }
                Query<UserDefinedAddress> query = this.createQuery();
                query.filter("cid", cid);
                query.filter("fullName", fullName);
                UserDefinedAddress checkFullName = this.get(query);
                if (Utils.isEmpty(checkFullName)) { //判断微信小程序用户上传是点是否已经存在，存在则修改坐标，不存在则添加记录
                    UpdateOperations<UserDefinedAddress> updateOperations = this.createUpdateOperations();
                    Point point = GeoJson.point(location[1], location[0]);
                    updateOperations.set("geometry", point);
                    this.update(this.createQuery().filter("_id", checkFullName.getObjectId()), updateOperations);
                } else {
                    UserDefinedAddress uDefAdd = new UserDefinedAddress();
                    uDefAdd.setCid(cid);
                    if (StrUtil.isEmpty(addName)) {
                        uDefAdd.setAddName(fullName);
                    } else {
                        uDefAdd.setAddName(addName);
                    }
                    uDefAdd.setFullName(fullName);
                    Point point = GeoJson.point(location[1], location[0]);
                    uDefAdd.setGeometry(point);
                    this.save(uDefAdd);
                }

                return ServerResponse.createSuccess("添加成功");
            } finally {
                lockedService.unLock(mobile, SysConstants.LockType.Add_Defined_Address.getType());
            }
        }
    }
}
