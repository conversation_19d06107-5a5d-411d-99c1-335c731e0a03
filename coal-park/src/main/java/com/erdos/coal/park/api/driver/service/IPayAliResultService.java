package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.PayAliPrepaid;
import com.erdos.coal.park.api.driver.entity.PayAliResult;

import java.util.Map;

public interface IPayAliResultService extends IBaseMongoService<PayAliResult> {
    //支付宝支付结果，检查参数验签是否正确，以及参数是否合法
    PayAliPrepaid rsaCheckV1(Map<String, String> params);

    //12.接收支付宝异步发送的支付结果通知
    String checkNotify(Map<String, String> params, String out_trade_no, String tradeStatus, PayAliPrepaid prepaid);

    //查询支付结果
    ServerResponse<String> driverSearchAliPayResult(String outTradeNo, String tradeNo);

    //处理支付结果，平台记录支付成功信息，司机账户添加金额
    ServerResponse<String> processAliPayInformation(String userCode, String outTradeNo, String tradeNo, PayAliPrepaid prepaid, Integer cusOrDri);

    //9.同步支付结果返回客商服务端，验签，解析支付结果
    ServerResponse<String> checkPayResult(String jsonParam);
}
