package com.erdos.coal.park.api.manage.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.util.Date;

@Entity(value = "t_sms", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("mobile")}, options = @IndexOptions(unique = true)),
        @Index(fields = {@Field("sendTime")}, options = @IndexOptions(expireAfterSeconds = 60))
})
public class SMS extends BaseMongoInfo {

    /*@Indexed(options = @IndexOptions(name = "_idx_ccc", unique = true, background = true))
    private Integer ccc;*/

    //@Indexed(options = @IndexOptions(name = "_idx_mobile", unique = true, background = true))
    private String mobile; //手机号

    //@Indexed(options = @IndexOptions(name = "_idx_sendTime", expireAfterSeconds = 60))
    private Date sendTime; //发送时间
    private String code; //验证码
    private int sendStatus; //发送状态
    
    /*public Integer getCcc() {
        return ccc;
    }

    public void setCcc(Integer ccc) {
        this.ccc = ccc;
    }*/

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(int sendStatus) {
        this.sendStatus = sendStatus;
    }
}
