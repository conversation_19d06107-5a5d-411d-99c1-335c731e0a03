package com.erdos.coal.park.api.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.customer.dao.IOrderTakingDao;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import dev.morphia.query.Query;
import org.bson.Document;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("orderTakingService")
public class OrderTakingServiceImpl extends BaseMongoServiceImpl<OrderTaking, IOrderTakingDao> implements IOrderTakingService {
    @Override
    public List<OrderTaking> searchDriverOrder(String carNum, int finishTag) {
        Query<OrderTaking> query = this.createQuery();
        query.field("carNum").equal(carNum);
        query.field("finishTag").notEqual(finishTag);
        query.field("driverIsAgree").notEqual(2);

        return query.find().toList();
    }

    @Override
    //public List<OrderTaking> searchByUpdateTime(String cid, Integer finishTag, Long startTime, Long endTime, Integer isHand) {
    public EGridResult<OrderTaking> searchByUpdateTime(String cid, Integer finishTag, Long startTime, Long endTime, Integer isHand, Integer page, Integer rows) {
        Query<OrderTaking> query = this.createQuery();
        query.filter("cid", cid).filter("finishTag", finishTag);
        if (isHand != null && isHand == 1) {
            query.filter("isHand", isHand);
        }
        if (startTime != null) {
            query.filter("updateTime >= ", startTime);
        }
        if (endTime != null) {
            query.filter("updateTime <= ", endTime);
        }

        //return query.find().toList();
        return this.findPage(page, rows, query);
    }

    @Override
    public List<OrderTaking> searchById(String did, int finishTag) {
        Query<OrderTaking> query = this.createQuery();
        query.field("did").equal(did);
        query.field("finishTag").notEqual(finishTag);
        query.field("driverIsAgree").notEqual(2);

        return query.find().toList();
    }

    @Override
    public Document createOTDoc(String oid, Goods goods, DriverInfo driverInfo, Integer finishTag, Integer isHand, Integer driverIsAgree, Date time) {
        OrderTaking ot = new OrderTaking();
        ot.setOid(oid);
        ot.setCid(goods.getCid());
        ot.setDid(driverInfo.getObjectId().toString());
        ot.setMobile(driverInfo.getMobile());
        ot.setCarNum(driverInfo.getCarNum());
        ot.setFinishTag(finishTag);
        ot.setGid(goods.getGid());
        ot.setUpdateTime(time.getTime());
        ot.setIsHand(isHand);
        ot.setDriverIsAgree(driverIsAgree);
        Document otDoc = Document.parse(JSONObject.toJSONString(ot));
        otDoc.append("createTime", time);
        return otDoc;
    }

    @Override
    public Document createOTDoc(Order order, DriverInfo driverInfo, Integer finishTag, Integer isHand, Integer driverIsAgree, Date time) {
        OrderTaking ot = new OrderTaking();
        ot.setOid(order.getOid());
        ot.setCid(order.getCid());
        ot.setGid(order.getGid());
        ot.setDid(driverInfo.getObjectId().toString());
        ot.setMobile(driverInfo.getMobile());
        ot.setCarNum(driverInfo.getCarNum());
        ot.setFinishTag(finishTag);
        ot.setUpdateTime(time.getTime());
        ot.setIsHand(isHand);
        ot.setDriverIsAgree(driverIsAgree);
        Document otDoc = Document.parse(JSONObject.toJSONString(ot));
        otDoc.append("createTime", time);
        return otDoc;
    }

    @Override
    public Document createOTDoc(Order order, String did, String dvrMobile, String dvrCarNum, Integer finishTag, Integer isHand, Integer driverIsAgree, Date time) {
        OrderTaking ot = new OrderTaking();
        ot.setOid(order.getOid());
        ot.setCid(order.getCid());
        ot.setGid(order.getGid());
        ot.setDid(did);
        ot.setCarNum(dvrCarNum);
        ot.setMobile(dvrMobile);
        ot.setFinishTag(finishTag);
        ot.setUpdateTime(time.getTime());
        ot.setDriverIsAgree(driverIsAgree);
        ot.setIsHand(isHand);
        Document otDoc = Document.parse(JSONObject.toJSONString(ot));
        otDoc.append("createTime", time);
        return otDoc;
    }
}
