package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.web.app.entity.CusHDDKS;

public interface ICusHDDKSService extends IBaseMongoService<CusHDDKS> {
    //查询list
    ServerResponse<EGridResult> loadGrid(Integer page, Integer rows);

    //添加客商对货达达的key和secret
    ServerResponse<String> addCusHDDKS(CusHDDKS cusHDDKS);

    //修改key和secret
    ServerResponse<String> editCusHDDKS(CusHDDKS cusHDDKS);

    //删除客商对货达的货主信息
    ServerResponse<String> delCusHDDKS(CusHDDKS cusHDDKS);
}
