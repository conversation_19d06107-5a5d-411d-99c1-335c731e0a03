package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

import java.math.BigDecimal;

@Entity(value = "t_driver_account", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("did")}),
        @Index(fields = {@Field("type")}),
        @Index(fields = {@Field("preferentialRefundNo")}, options = @IndexOptions(unique = true, partialFilter = "{preferentialRefundNo:{$exists:true}}"))
})
//司机账户
public class DriverAccount extends BaseMongoInfo {
    private String did;     //司机id
    private String cdid;    //充值人（司机或客商id)或扣款人
    private BigDecimal totalFee = new BigDecimal("0");   //金额(分)
    private Integer type;   // 0：微信充值 1：支付宝充值 2：接单扣款 3：订单退款 4：提现到微信 5：提现到支付宝 6: 优惠返款
    // 8:司机接单直接微信付款
    private String outTradeNo;  //商户订单号
    private String transactionId;//微信订单号/支付宝订单号
    private String oid; //扣款订单号

    private String preferentialRefundNo;    //优惠返款唯一订单号 由二级单位编号+日期+司机id+carNum组成

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getCdid() {
        return cdid;
    }

    public void setCdid(String cdid) {
        this.cdid = cdid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getPreferentialRefundNo() {
        return preferentialRefundNo;
    }

    public void setPreferentialRefundNo(String preferentialRefundNo) {
        this.preferentialRefundNo = preferentialRefundNo;
    }
}
