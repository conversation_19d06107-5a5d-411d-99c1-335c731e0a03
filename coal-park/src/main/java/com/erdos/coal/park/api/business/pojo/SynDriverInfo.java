package com.erdos.coal.park.api.business.pojo;

import org.bson.types.ObjectId;

import java.io.Serializable;

public class SynDriverInfo implements Serializable {
    private ObjectId objectId;

    private String objId;
    private String mobile;      //电话号码
    private Integer state = 0;        //用戶状态 0：未审核不可用 1：通过审核 2：冻结 3:审核未通过 4：未审核但可用
    private String phoneId;
    private String deviceId;
    private Double capacity = 0.0;    //载重（kg） 识别行驶证照片
    private String stateStr;          //用戶审核不可用原因
    private String initialOpenid;   //用户初始注册时的微信openid
    private String driIdentityPhoBef;       //司机身份证照片正面
    private String identity;    //身份证号
    private String name;        //姓名
    private String carNum;      //关联车牌
    private Integer haveOrder = 0;  //司机当前是否有在运输的订单 0-无，1-有
    private String axlesNumber; //车轴数
    private String carType; //车型
    private String drivingPho1;      //行驶证照片（第一页）
    private String drivingPho2;      //行驶证照片（第二页）
    private String drivingPho3;      //行驶证照片（第三页）
    private String driverPho;       //驾驶证照片（正页）
    private String driverPho2;       //驾驶证照片（副页）
    private String driIdentityPhoBack;      //司机身份证照片背面
    private String roadQCPho;    //道路从业资格证照片
    private String bankCardPho;      //司机银行卡照片
    private String driverCarPho;    //司机和车的合影照片

    public void setObjectId(ObjectId objectId) {
        this.objectId = objectId;
    }

    public String getObjId() {
        return objectId.toHexString();
    }

    public void setObjId(String objId) {
        this.objId = objectId.toHexString();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(String phoneId) {
        this.phoneId = phoneId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getStateStr() {
        return stateStr;
    }

    public void setStateStr(String stateStr) {
        this.stateStr = stateStr;
    }

    public String getInitialOpenid() {
        return initialOpenid;
    }

    public void setInitialOpenid(String initialOpenid) {
        this.initialOpenid = initialOpenid;
    }

    public String getDriIdentityPhoBef() {
        return driIdentityPhoBef;
    }

    public void setDriIdentityPhoBef(String driIdentityPhoBef) {
        this.driIdentityPhoBef = driIdentityPhoBef;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public Integer getHaveOrder() {
        return haveOrder;
    }

    public void setHaveOrder(Integer haveOrder) {
        this.haveOrder = haveOrder;
    }

    public String getAxlesNumber() {
        return axlesNumber;
    }

    public void setAxlesNumber(String axlesNumber) {
        this.axlesNumber = axlesNumber;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getDrivingPho1() {
        return drivingPho1;
    }

    public void setDrivingPho1(String drivingPho1) {
        this.drivingPho1 = drivingPho1;
    }

    public String getDrivingPho2() {
        return drivingPho2;
    }

    public void setDrivingPho2(String drivingPho2) {
        this.drivingPho2 = drivingPho2;
    }

    public String getDrivingPho3() {
        return drivingPho3;
    }

    public void setDrivingPho3(String drivingPho3) {
        this.drivingPho3 = drivingPho3;
    }

    public String getDriverPho() {
        return driverPho;
    }

    public void setDriverPho(String driverPho) {
        this.driverPho = driverPho;
    }

    public String getDriverPho2() {
        return driverPho2;
    }

    public void setDriverPho2(String driverPho2) {
        this.driverPho2 = driverPho2;
    }

    public String getDriIdentityPhoBack() {
        return driIdentityPhoBack;
    }

    public void setDriIdentityPhoBack(String driIdentityPhoBack) {
        this.driIdentityPhoBack = driIdentityPhoBack;
    }

    public String getRoadQCPho() {
        return roadQCPho;
    }

    public void setRoadQCPho(String roadQCPho) {
        this.roadQCPho = roadQCPho;
    }

    public String getBankCardPho() {
        return bankCardPho;
    }

    public void setBankCardPho(String bankCardPho) {
        this.bankCardPho = bankCardPho;
    }

    public String getDriverCarPho() {
        return driverCarPho;
    }

    public void setDriverCarPho(String driverCarPho) {
        this.driverCarPho = driverCarPho;
    }
}
