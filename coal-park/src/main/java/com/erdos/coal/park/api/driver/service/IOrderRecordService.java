package com.erdos.coal.park.api.driver.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.api.customer.entity.Goods;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.pojo.DriverInfoData;
import com.erdos.coal.park.api.customer.pojo.GoodsInfoData;
import com.erdos.coal.park.api.driver.entity.Car;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.OrderRecord;
import com.erdos.coal.park.api.driver.entity.WxPrepaid;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.pojo.HandlingCostPojo;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import org.bson.Document;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IOrderRecordService extends IBaseMongoService<OrderRecord> {
    //扫码接单-扫码 接口
    //ServerResponse sweepCode(String gid,String carNum,String mobile);

    //扫码接单-二维码 接口
    ServerResponse<DriverInfoData> twoDimensionCode(String carNum);

    //订单信息查询  接口
    ServerResponse<List<DriverOrderData>> orderQuery(int[] finishTag, Long finishTime, String point, String tradePwd);

    // 订单信息查询  接口 分页
    ServerResponse<EGridResult> orderQuery(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows);
    ServerResponse<EGridResult> orderQuery2(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows);
    //加了did后的查询方法，已全库司机测试过。用时100ms内
    ServerResponse<EGridResult> orderQuery3(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows);
    ServerResponse<EGridResult> orderQuery4(int[] finishTag, Long finishTime, String point, String tradePwd, Integer page, Integer rows);

    //找货    接口
//    ServerResponse<List<Goods>> lookGoods(String point);
    ServerResponse<List<GoodsInfoData>> lookGoods(String point);
    ServerResponse<List<GoodsInfoData>> lookGoods2(String point);

    //抢单    接口
    ServerResponse<String> grab(String gid, String carNum, String longitude, String latitude);

    //判断是否设置交易密码    接口
    ServerResponse<String> isTradePwd();

    ServerResponse<String> checkTradePwd(String pwd);

    //司机已接单，客商申请撤销订单
    void deleteOrder(List<String> oids);

    // 返回该司机接单 应付平台信息费（分）
    int[] getPTFee(Goods goods, String did, String type);

    // 返回司机接单 应付总金额（分）
    int totalFee(Goods goods, Order order, String userId, String type, String fee);
    int totalFee3(Goods goods, Order order, String userId, String type, String fee);

    //查询司机应付费用
    ServerResponse<Double> searchFee(String oid, String gid, String fee);
    ServerResponse<Map<String,Object>> searchFee2(String oid, String gid, String fee);

    //查询司机应付 或 已付金额
    Double searchFee(String gid, String oid);

    // 指定车牌号，司机接单或拒绝
    ServerResponse<Order> designCarNum(int driverIsAgree, String oid, String carNum, String longitude, String latitude);
    ServerResponse<Order> designCarNum2(int driverIsAgree, String oid, String carNum, String longitude, String latitude);
    ServerResponse<Order> designCarNum3(int driverIsAgree, String oid, String carNum, String longitude, String latitude);
    ServerResponse<Order> designCarNum4(int driverIsAgree, String oid, String carNum, String longitude, String latitude);
    ServerResponse<Order> designCarNum5(int driverIsAgree, String oid, String carNum, String longitude, String latitude);

    ServerResponse<Order> designCarNum6(int driverIsAgree, String oid, String carNum, String longitude, String latitude);

    Map<String, Object> delivery(SysUnit[] sysUnits, Order order, DriverInfo driverInfo, Car car, String carNum);

    // 司机接单 更新order，orderTaking，driverAccount，customerAccount，unitAccount，orderLogistics
    int updateFee(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude);
    int updateFee2(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude);
    Map<String, Object> newOrderFee2(String did, Goods goods, Order order, Date date, Integer fee);
    int updateFee3(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude);
    int updateFee4(String userId, String oid, String carNum, int driverIsAgree, String fee, String type, String longitude, String latitude, Date date, List<Document> pfAccountDocs, List<Document> sysUnitAccountDocs, List<Document> cusAccountDocs, List<Document> dvrAccountDocs, List<Document> thirdPartyAccountDocs);

    // 司机微信完成支付后，分配订单给司机，完成司机接单业务
    int distributionOrder(WxPrepaid prepaid);

    Map<String, Object> getUnitFees(int driverFee, int cusFees, String driverPayerId, String cusPayerId, int fees, String payerId);

    // 司机撤单
    ServerResponse<String> saveDeleteOrder(String oid);
    ServerResponse<String> saveDeleteOrder2(String oid);
    ServerResponse<String> saveDeleteOrder3(String oid);
    ServerResponse<String> saveDeleteOrder4(String oid);

    //司机提交二级单位的预约
    ServerResponse<String> submitAppointment(String oid, Long startTime, Long endTime);

    //司机查询预约后 排队情况
    ServerResponse<Object> searchQueue(String oid);

    //司机修改订单车牌号
    ServerResponse<String> updateOrderCarNum(String oid, String carNum);

    //二级单位司机签到
    ServerResponse<String> punchClock(String oid, String imei, String longitude, String latitude);
    ServerResponse<String> punchClock2(String oid, String imei, String longitude, String latitude);
    ServerResponse<String> punchClock3(String oid, String imei, String longitude, String latitude);

    //上传防疫申报照片
    ServerResponse<String> uploadQuarantinePho(MultipartFile uploadPho, String type);

    //司机提交防疫申报信息
    ServerResponse<String> addQuarantineInfo(String oid, String healthCodePho, String travelCardPho, String temperaturePro, String temperature);
    //司机提交防疫申报信息
    ServerResponse<String> addQuarantineInfo2(String oid, String healthCodePho, String travelCardPho, String nucleicAcidPho, String vaccinationPho, String touchPro, String temperaturePro, String temperature);

    //司机抢号
    ServerResponse<String> grabNumber(String oid);

    //司机查询名下的状态（tranStatus3,5）的一条订单
    ServerResponse<DriverOrderData> get35TranStatusOrder();
    ServerResponse<DriverOrderData> get35TranStatusOrder2();
    //加了did后的查询方法，已全库司机测试过。用时100ms内
    ServerResponse<DriverOrderData> get35TranStatusOrder3();

    //订单有三方分账的情况，则司机微信支付接单
    ServerResponse<Map<String, String>> orderRequestPayWithProfitSharing(String gid, String oid, String dvrGoodsId, String openid, String carNum, String longitude, String latitude, String fee);

    //司机微信支付完成，解锁订单
    void unLockOrder(String oid);

    //司机在微信链接接单
    ServerResponse<Order> wxLinkOrder(String carNum, String dvrGoodsId, String longitude, String latitude, String fee);
    ServerResponse<Order> wxLinkOrder2(String carNum, String dvrGoodsId, String longitude, String latitude, String fee);

    ServerResponse<List<CompanyUnit>> searchSubUnit(String unitName);

    ServerResponse<String> addQuarantineInfoBefore(String defaultDownUnit, String healthCodePho, String travelCardPho, String nucleicAcidPho, String vaccinationPho, String touchPro, String temperaturePro, String temperature);

    //****************************hdd*********************
    //查询订单车辆轨迹
    ServerResponse<String> getHistoricalTrack(String oid);

    //查询订单车辆过磅图片
    ServerResponse<Map<String, String>> getPhotoByOid(String oid);

    // 按oid查询二级单位求助热线
    ServerResponse<String> getHotLineByOid(String oid);

    // 检查订单是否需要司机录入对方净重
    ServerResponse<Map<String, Object>> checkInNetWeight(String oid);

    // 司机录入对方净重
    ServerResponse<String> updateInNetWeight(String oid, Double inNetWeight, String loadPound, String loadPoundPho, Long loadTime);

    // 检查订单是否超过一级单位设置的接单时间限制
    int queryOrderHour(Order order);

    // 查询订单装卸费支付结果
    ServerResponse<HandlingCostPojo> searchCostPay(String oid, String bizContractCode);

}
