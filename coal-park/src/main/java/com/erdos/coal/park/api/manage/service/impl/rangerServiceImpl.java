package com.erdos.coal.park.api.manage.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.manage.dao.IRangerDao;
import com.erdos.coal.park.api.manage.entity.Ranger;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.service.IRangerService;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("rangerService")
public class rangerServiceImpl extends BaseMongoServiceImpl<Ranger, IRangerDao> implements IRangerService {
    @Resource
    private ISMSService smsService;

    @Override
    public ServerResponse<AccessToken> regAndLogin(String username, String password, String code) {
        String md5PW = Utils.md5(password);

        Query<Ranger> query = this.createQuery();
        query.filter("username", username);
        query.filter("password", md5PW);
        Ranger ranger = this.get(query);

        if (ranger == null) {
            if (StrUtil.isEmpty(code)) return ServerResponse.createError("请先获取验证码！");
            SMS sms = smsService.get("mobile", username);
            if (sms == null || !code.equals(sms.getCode())) return ServerResponse.createError("验证码错误！");
            ranger = new Ranger();
            ranger.setUsername(username);
            ranger.setPassword(md5PW);
            this.save(ranger);
        }

        String token = JwtUtil.sign(UserType.PD, username, password);
        return ServerResponse.createSuccess("登录成功", new AccessToken(token));
    }
}
