package com.erdos.coal.park.web.app.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.park.api.customer.dao.ICustomerUserDao;
import com.erdos.coal.park.api.customer.dao.IDriverPoolDao;
import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.web.app.pojo.DriverPoolData;
import com.erdos.coal.park.web.app.service.IWebDriverPoolService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service("webDriverPoolService")
public class WebDriverPoolServiceImpl extends BaseMongoServiceImpl<DriverPool, IDriverPoolDao> implements IWebDriverPoolService {

    @Resource
    private ICustomerUserDao customerUserDao;
    @Resource
    private HttpServletRequest request;

    @Override
    public EGridResult driPoolList(Integer page, Integer rows) {

        Query<CustomerUser> query = customerUserDao.createQuery();
        List<String> cids = new ArrayList<>();
        if (!StrUtil.isEmpty(request.getParameter("name")))
            query.filter("name", request.getParameter("name"));
        if (!StrUtil.isEmpty(request.getParameter("mobile")))
            query.filter("mobile", request.getParameter("mobile"));

        List<CustomerUser> userList = customerUserDao.list(query);
        for (CustomerUser customerUser : userList) {
            cids.add(customerUser.getObjectId().toHexString());
        }

        Query<DriverPool> dpQuery = this.createQuery();
        /*if (userList.size() == 1) {
            dpQuery.filter("cid", userList.get(0).getObjectId().toString());
        } else if (userList.size() == 0) { //查不到无记录显示
            dpQuery.filter("cid", "");
        }*/
        dpQuery.criteria("cid").in(cids);
        dpQuery.order(Sort.ascending("cid"));

//        List<DriverPool> driverPoolList = this.list(dpQuery);
        EGridResult<DriverPool> result = this.findPage(page, rows, dpQuery);
        List<DriverPool> driverPoolList = result.getRows();

        List<DriverPoolData> driverPoolDataList = new ArrayList<>();
        for (DriverPool driverPool : driverPoolList) {
            DriverPoolData driPoolData = new DriverPoolData();

            driPoolData.setWhiteOrBlack(driverPool.getWhiteOrBlack());

            CustomerUser user = driverPool.getCustomerUser();
            if (ObjectUtil.isNotNull(user)) {
                driPoolData.setCusMobile(user.getMobile());
                driPoolData.setCusName(user.getName());
            }

            DriverInfo driverInfo = driverPool.getDriverInfo();
            if (ObjectUtil.isNotNull(driverInfo)) {
                driPoolData.setDriMobile(driverInfo.getMobile());
                driPoolData.setDriName(driverInfo.getName());
            }

            /*CustomerUser user = customerUserDao.getByPK(driverPool.getCid());
            driPoolData.setCusName(user.getName());
            driPoolData.setCusMobile(user.getMobile());

            DriverInfo driver = driverInfoDao.getByPK(driverPool.getDid());
            driPoolData.setDriMobile(driver.getMobile());
            driPoolData.setDriName(driver.getName());*/

            driverPoolDataList.add(driPoolData);
        }

        return new EGridResult(result.getTotal(), driverPoolDataList);
        /*int size = driverPoolDataList.size();
        int pageCount = size / rows;
        int fromIndex = rows * (page - 1);
        int toIndex = fromIndex + rows;
        if (toIndex >= size) {
            toIndex = size;
        }
        if (page > pageCount + 1) {
            fromIndex = 0;
            toIndex = 0;
        }
        return new EGridResult(driverPoolDataList.size(), driverPoolDataList.subList(fromIndex, toIndex));*/
    }
}
