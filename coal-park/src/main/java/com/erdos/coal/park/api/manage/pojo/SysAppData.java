package com.erdos.coal.park.api.manage.pojo;

import java.io.Serializable;

public class SysAppData implements Serializable {
    private String version;//版本号
    private String url;//下载地址
    private Integer isUpdate;//是否强制更新
    private String content;//该版本更新内容描述

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getIsUpdate() {
        return isUpdate;
    }

    public void setIsUpdate(Integer isUpdate) {
        this.isUpdate = isUpdate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
