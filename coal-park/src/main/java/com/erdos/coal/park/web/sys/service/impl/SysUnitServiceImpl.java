package com.erdos.coal.park.web.sys.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.map.service.MapService;
import com.erdos.coal.park.api.business.pojo.CompanyUnit;
import com.erdos.coal.park.web.sys.dao.ISysUnitDao;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;
import com.erdos.coal.park.web.sys.pojo.CheckBoxData;
import com.erdos.coal.park.web.sys.pojo.PwdData;
import com.erdos.coal.park.web.sys.pojo.SubUnitGeo;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import com.erdos.coal.park.web.sys.service.ISysUnitAccountService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

@Service("sysUnitService")
public class SysUnitServiceImpl extends BaseMongoServiceImpl<SysUnit, ISysUnitDao> implements ISysUnitService {
    @Resource
    private ISysUnitAccountService sysUnitAccountService;
    @Resource
    private HttpServletRequest request;

    public EGridResult loadGrid(Integer page, Integer rows, Integer type) {
        Query<SysUnit> query = this.createQuery();

        if (!StrUtil.isEmpty(request.getParameter("code"))) {
            query.criteria("code").equalIgnoreCase(request.getParameter("code"));
        }
        if (!StrUtil.isEmpty(request.getParameter("name"))) {
            query.criteria("name").equalIgnoreCase(request.getParameter("name"));
        }

        Object[] pCode = {"", null};
        if (type == 0) {
            query.filter("pCode in", pCode);
        } else {
            query.filter("pCode nin", pCode);
        }
        EGridResult eGrid = this.findPage(page, rows, query);

        List<SysUnit> sysUnitList = (List<SysUnit>) eGrid.getRows();
        List<UnitData> list = new ArrayList<>();
        for (SysUnit unit : sysUnitList) {
            UnitData data = new UnitData();
            BeanUtils.copyProperties(unit, data);
            data.setStandardDeduct(unit.getStandardDeduct() / 100.0);
            data.setStandardDeductIn(unit.getStandardDeductIn() / 100.0);
            data.setCarDeduct(unit.getCarDeduct() / 100.0);
            data.setWechatDeduct(unit.getWechatDeduct() / 100.0);
            BigDecimal availableFee = sysUnitAccountService.getAvailableFee(unit.getObjectId().toString());
            availableFee = availableFee.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            data.setAvailableFee(availableFee);
            if (StrUtil.isNotEmpty(unit.getfUnitFee()) && unit.getfUnitFee() > 0)
                data.setfUnitFee(unit.getfUnitFee() / 100.0);
            if (StrUtil.isNotEmpty(unit.getfUnitFeeIn()) && unit.getfUnitFeeIn() > 0)
                data.setfUnitFeeIn(unit.getfUnitFeeIn() / 100.0);
            if (StrUtil.isNotEmpty(unit.getsUnitFee()) && unit.getsUnitFee() > 0)
                data.setsUnitFee(unit.getsUnitFee() / 100.0);
            if (StrUtil.isNotEmpty(unit.getsUnitFeeIn()) && unit.getsUnitFeeIn() > 0)
                data.setsUnitFeeIn(unit.getsUnitFeeIn() / 100.0);
            if (StrUtil.isNotEmpty(unit.getThirdPartyLedger()) && unit.getThirdPartyLedger() > 0)
                data.setThirdPartyLedger(unit.getThirdPartyLedger() / 100.0);
            if (StrUtil.isNotEmpty(unit.getPreferentialPFPrice()) && unit.getPreferentialPFPrice() > 0)
                data.setPreferentialPFPrice(unit.getPreferentialPFPrice() / 100.0);
            if (StrUtil.isNotEmpty(unit.getPreferentialTPPrice()) && unit.getPreferentialTPPrice() > 0)
                data.setPreferentialTPPrice(unit.getPreferentialTPPrice() / 100.0);

            //电子围栏数据
            // if (StrUtil.isEmpty(data.getGid())) data.setGeoCenter("109.780594, 39.609548");
            if (StrUtil.isEmpty(data.getSubUnitGeoList()) || data.getSubUnitGeoList().size() == 0) {
                List<SubUnitGeo> geoList = new ArrayList<>();
                SubUnitGeo geo = new SubUnitGeo();
                geo.setGeoCenter("109.780594,39.609548");
                geoList.add(geo);
                data.setSubUnitGeoList(geoList);
            }

            list.add(data);
        }

        EGridResult eGridResult = new EGridResult();
        eGridResult.setRows(list);
        eGridResult.setTotal(eGrid.getTotal());
        return eGridResult;
    }

    /**
     * 根据unitCode的list 批量查询
     */
    @Override
    public List<SysUnit> searchByUnitCodes(List<String> unitCodes) {
        List<Pattern> strList = new ArrayList<>();
        unitCodes.forEach(item -> strList.add(Pattern.compile(item + "$")));
        Query<SysUnit> query = this.createQuery();
        //query.filter("code in ", unitCodes);
        query.filter("code in ", strList);
        return query.find().toList();
    }

    @Override
    public ServerResponse<String> deleteUnit(UnitData unit) {
        String id = unit.getId();
        SysUnit has = this.get("id", id);
        if (has != null) {
            Query<SysUnitAccount> query = sysUnitAccountService.createQuery();
            query.filter("uid", has.getObjectId().toString());
            query.order(Sort.descending("createTime"));
            List<SysUnitAccount> accountList = sysUnitAccountService.list(query);
            if (accountList.size() > 0) return ServerResponse.createError("该单位有交易信息，不能删除");

            Query<SysUnit> unitQuery = this.createQuery().filter("pCode", unit.getCode());
            List<SysUnit> unitList = this.list(unitQuery);
            if (unitList.size() > 0) return ServerResponse.createError("该单位下有二级单位，不能删除");

            this.delete("id", id);
            return ServerResponse.createSuccess();
        }
        return ServerResponse.createError();
    }

    @Override
    public ServerResponse<String> addUnit(UnitData unit) {
        if (checkUnit(unit)) {
            if (StrUtil.isNotEmpty(unit.getDuanPanNum()) && unit.getDuanPanNum() < 2)
                return ServerResponse.createError("计划任务短盘车数须大于等于2");
            if (StrUtil.isNotEmpty(unit.getShouFeiNum()) && unit.getDuanPanNum() < unit.getShouFeiNum())
                return ServerResponse.createError("计划任务收费车数须大于短盘车数");

            unit.setId(Utils.getUUID());
            if (ObjectUtil.isNotNull(unit.getWechatDeduct()))
                unit.setWechatDeduct(unit.getWechatDeduct() * 100);
            if (ObjectUtil.isNotNull(unit.getStandardDeduct()))
                unit.setStandardDeduct(unit.getStandardDeduct() * 100);
            if (ObjectUtil.isNotNull(unit.getStandardDeductIn()))
                unit.setStandardDeductIn(unit.getStandardDeductIn() * 100);
            if (ObjectUtil.isNotNull(unit.getCarDeduct()))
                unit.setCarDeduct(unit.getCarDeduct() * 100);
            if (StrUtil.isNotEmpty(unit.getPassword())) {
                /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
                String pwd = passwordEncoder.encode(unit.getPassword());
                unit.setPassword(pwd);*/
                unit.setPassword(Utils.md5(unit.getPassword()));
            }
            if (StrUtil.isNotEmpty(unit.getfUnitFee())) unit.setfUnitFee(unit.getfUnitFee() * 100);
            if (StrUtil.isNotEmpty(unit.getfUnitFeeIn())) unit.setfUnitFeeIn(unit.getfUnitFeeIn() * 100);
            if (StrUtil.isNotEmpty(unit.getsUnitFee())) unit.setsUnitFee(unit.getsUnitFeeIn() * 100);
            if (StrUtil.isNotEmpty(unit.getsUnitFeeIn())) unit.setsUnitFeeIn(unit.getsUnitFeeIn() * 100);

            SysUnit sysUnit = new SysUnit();
            BeanUtils.copyProperties(unit, sysUnit);
            sysUnit.setUpdateTime(new Date().getTime());
            this.save(sysUnit);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse<String> addUnit2(UnitData unit) {
        if (StrUtil.isNotEmpty(unit.getGoodsTotal()) && unit.getGoodsTotal()>300)
            return ServerResponse.createError("客商下单车数不可超过300");

        if (checkUnit(unit)) {
            unit.setId(Utils.getUUID());
            if (StrUtil.isNotEmpty(unit.getPassword())) {
                /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
                String pwd = passwordEncoder.encode(unit.getPassword());
                unit.setPassword(pwd);*/
                unit.setPassword(Utils.md5(unit.getPassword()));
            }

            SysUnit sysUnit = new SysUnit();
            BeanUtils.copyProperties(unit, sysUnit);
            //页面屏蔽的费用等字段，设置默认值
            sysUnit.setWechatDeduct(0);
            sysUnit.setStandardDeduct(0);
            sysUnit.setStandardDeductIn(0);
            sysUnit.setCarDeduct(0);
            sysUnit.setShare(0);
            sysUnit.setIsReplacePay(0);
            sysUnit.setIsForbidCusFee(1);
            sysUnit.setIsDelOrderBackFee(1);
            sysUnit.setUpdateTime(new Date().getTime());
            this.save(sysUnit);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    private String checkPreferentialRefundType(UnitData unit) {
        // 当 duanPanNum >= 2时，才符合参与计划任务优惠退款标准
        if (StrUtil.isEmpty(unit.getPreferentialRefundType())) {
            return null;
        } else if (unit.getPreferentialRefundType() == 1) {      // 按次返还
            if (StrUtil.isEmpty(unit.getDuanPanNum()) || unit.getDuanPanNum() < 2) return "计划任务短盘车数须大于等于2";
            if (StrUtil.isEmpty(unit.getShouFeiNum()) || unit.getShouFeiNum() > unit.getDuanPanNum() || unit.getShouFeiNum() < 0)
                return "计划任务收费车数须小于或等于短盘车数,且不可为负数";
        } else if (unit.getPreferentialRefundType() == 2) {     // 按单价返还
            if (StrUtil.isEmpty(unit.getDuanPanNum()) || unit.getDuanPanNum() < 2) return "计划任务短盘车数须大于等于2";
            if (StrUtil.isEmpty(unit.getPreferentialPFPrice()) || unit.getPreferentialPFPrice() < 0) return "优惠单价不可为负数";
            if (StrUtil.isEmpty(unit.getPreferentialTPPrice()) || unit.getPreferentialTPPrice() < 0) return "优惠单价不可为负数";
        } else if (unit.getPreferentialRefundType() == 3) {
            if (StrUtil.isEmpty(unit.getDuanPanNum()) || unit.getDuanPanNum() < 2) return "计划任务短盘车数须大于等于2";
            if (StrUtil.isEmpty(unit.getShouFeiNum()) || unit.getShouFeiNum() > unit.getDuanPanNum() || unit.getShouFeiNum() < 0)
                return "计划任务收费车数须小于或等于短盘车数,且不可为负数";
            if (StrUtil.isEmpty(unit.getPreferentialPFPrice()) || unit.getPreferentialPFPrice() < 0) return "优惠单价不可为负数";
            if (StrUtil.isEmpty(unit.getPreferentialTPPrice()) || unit.getPreferentialTPPrice() < 0) return "优惠单价不可为负数";
        }
        return null;
    }

    @Override
    public ServerResponse<String> editUnit(UnitData unit) {
        if (checkUnit(unit) && unit.getId() != null) {
            String checkPRTMsg = checkPreferentialRefundType(unit);
            if (StrUtil.isNotEmpty(checkPRTMsg)) return ServerResponse.createError(checkPRTMsg);

            Query<SysUnit> query = this.createQuery();
            query.filter("id", unit.getId());
            query.filter("updateTime", unit.getUpdateTime());

            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(unit.getCode()))
                updateOperations.set("code", unit.getCode());
            if (StrUtil.isNotEmpty(unit.getName()))
                updateOperations.set("name", unit.getName());
            if (StrUtil.isNotEmpty(unit.getIp()))
                updateOperations.set("ip", unit.getIp());
            if (StrUtil.isNotEmpty(unit.getVariety()))
                updateOperations.set("variety", unit.getVariety());
            if (ObjectUtil.isNotNull(unit.getStandardDeduct()))
                updateOperations.set("standardDeduct", unit.getStandardDeduct() * 100);
            if (ObjectUtil.isNotNull(unit.getStandardDeductIn()))
                updateOperations.set("standardDeductIn", unit.getStandardDeductIn() * 100);
            if (ObjectUtil.isNotNull(unit.getWechatTimes()))
                updateOperations.set("wechatTimes", unit.getWechatTimes());
            if (ObjectUtil.isNotNull(unit.getWechatDeduct()))
                updateOperations.set("wechatDeduct", unit.getWechatDeduct() * 100);
            if (ObjectUtil.isNotNull(unit.getCarsTime()))
                updateOperations.set("carsTime", unit.getCarsTime());
            if (ObjectUtil.isNotNull(unit.getCarDeduct()))
                updateOperations.set("carDeduct", unit.getCarDeduct() * 100);
            if (ObjectUtil.isNotEmpty(unit.getShare()))
                updateOperations.set("share", unit.getShare());
            if (ObjectUtil.isNotNull(unit.getIsReplacePay()))
                updateOperations.set("isReplacePay", unit.getIsReplacePay());
            if (StrUtil.isNotEmpty(unit.getLoginName()))
                updateOperations.set("loginName", unit.getLoginName());
            /*if (StrUtil.isNotEmpty(unit.getPassword()))
                updateOperations.set("password", Utils.md5(unit.getPassword()));*/

            if (StrUtil.isNotEmpty(unit.getIsAppointment()))
                updateOperations.set("isAppointment", unit.getIsAppointment());
            if (StrUtil.isNotEmpty(unit.getIsPunchClock()))
                updateOperations.set("isPunchClock", unit.getIsPunchClock());
            if (StrUtil.isNotEmpty(unit.getIsQuarantine()))
                updateOperations.set("isQuarantine", unit.getIsQuarantine());
            if (StrUtil.isNotEmpty(unit.getQuarantineType()))
                updateOperations.set("quarantineType", unit.getQuarantineType());
            if (StrUtil.isNotEmpty(unit.getQuarantineInfo()))
                updateOperations.set("quarantineInfo", unit.getQuarantineInfo());
            if (StrUtil.isNotEmpty(unit.getRegionId()))
                updateOperations.set("regionId", unit.getRegionId());
            if (StrUtil.isNotEmpty(unit.getIsGrabNumber()))
                updateOperations.set("isGrabNumber", unit.getIsGrabNumber());
            if (StrUtil.isNotEmpty(unit.getQuaVerify()))
                updateOperations.set("quaVerify", unit.getQuaVerify());
            if (StrUtil.isNotEmpty(unit.getValidityTime()))
                updateOperations.set("validityTime", unit.getValidityTime());

            if (StrUtil.isNotEmpty(unit.getfUnitFee()))
                updateOperations.set("fUnitFee", unit.getfUnitFee() * 100);
            if (StrUtil.isNotEmpty(unit.getfUnitFeeIn()))
                updateOperations.set("fUnitFeeIn", unit.getfUnitFeeIn() * 100);
            if (StrUtil.isNotEmpty(unit.getsUnitFee()))
                updateOperations.set("sUnitFee", unit.getsUnitFee() * 100);
            if (StrUtil.isNotEmpty(unit.getsUnitFeeIn()))
                updateOperations.set("sUnitFeeIn", unit.getsUnitFeeIn() * 100);
            if (StrUtil.isNotEmpty(unit.getIsDelOrderBackFee()))
                updateOperations.set("isDelOrderBackFee", unit.getIsDelOrderBackFee());
            if (StrUtil.isNotEmpty(unit.getIsForbidCusFee()))
                updateOperations.set("isForbidCusFee", unit.getIsForbidCusFee());

            if (StrUtil.isNotEmpty(unit.getPreferentialRefundType()))
                updateOperations.set("preferentialRefundType", unit.getPreferentialRefundType());
            if (StrUtil.isNotEmpty(unit.getDuanPanNum())) updateOperations.set("duanPanNum", unit.getDuanPanNum());
            if (StrUtil.isNotEmpty(unit.getShouFeiNum())) updateOperations.set("shouFeiNum", unit.getShouFeiNum());
            if (StrUtil.isNotEmpty(unit.getPreferentialPFPrice())) updateOperations.set("preferentialPFPrice", unit.getPreferentialPFPrice() * 100);
            if (StrUtil.isNotEmpty(unit.getPreferentialTPPrice())) updateOperations.set("preferentialTPPrice", unit.getPreferentialTPPrice() * 100);

            if (StrUtil.isNotEmpty(unit.getIsCall())) updateOperations.set("isCall", unit.getIsCall());
            if (StrUtil.isNotEmpty(unit.getSmartEnergyAccessCode())){
                updateOperations.set("smartEnergyAccessCode", unit.getSmartEnergyAccessCode());
            }else{
                updateOperations.set("smartEnergyAccessCode", "");
            }
            if (StrUtil.isNotEmpty(unit.getInNeedNetWeight())) updateOperations.set("inNeedNetWeight", unit.getInNeedNetWeight());
            if (StrUtil.isNotEmpty(unit.getInNeedLoadPound())) updateOperations.set("inNeedLoadPound", unit.getInNeedLoadPound());
            if (StrUtil.isNotEmpty(unit.getInNeedLoadTime())) updateOperations.set("inNeedLoadTime", unit.getInNeedLoadTime());
            if (StrUtil.isNotEmpty(unit.getInLoadPoundPho())) updateOperations.set("inLoadPoundPho", unit.getInLoadPoundPho());

            this.update(query, updateOperations);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse<String> editUnit2(UnitData unit) {
        if (checkUnit(unit) && unit.getId() != null) {
            Query<SysUnit> query = this.createQuery();
            query.filter("id", unit.getId());
            query.filter("updateTime", unit.getUpdateTime());

            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(unit.getCode())) updateOperations.set("code", unit.getCode());
            if (StrUtil.isNotEmpty(unit.getName())) updateOperations.set("name", unit.getName());
            if (StrUtil.isNotEmpty(unit.getIp())) updateOperations.set("ip", unit.getIp());
            if (StrUtil.isNotEmpty(unit.getVariety())) updateOperations.set("variety", unit.getVariety());
            if (StrUtil.isNotEmpty(unit.getLoginName())) updateOperations.set("loginName", unit.getLoginName());
            /*if (StrUtil.isNotEmpty(unit.getPassword())) updateOperations.set("password", Utils.md5(unit.getPassword()));*/
            if (StrUtil.isNotEmpty(unit.getDelivery())) updateOperations.set("delivery", unit.getDelivery());
            if (StrUtil.isNotEmpty(unit.getGoodsTotal())){
                if (unit.getGoodsTotal()>300) return ServerResponse.createError("客商下单车数不可超过300");
                updateOperations.set("goodsTotal", unit.getGoodsTotal());
            }
            if (StrUtil.isNotEmpty(unit.getDvrOrderHour())) updateOperations.set("dvrOrderHour", unit.getDvrOrderHour());

            this.update(query, updateOperations);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public ServerResponse<String> saveGeofenceUnit(UnitData sysUnit) {
        String gid = sysUnit.getGid();
        String geoCenter = sysUnit.getGeoCenter();
        String geoRadius = sysUnit.getGeoRadius();
        String unitCode = sysUnit.getCode();

        if (StrUtil.isEmpty(geoRadius)) {
            geoRadius = "100";
        } else {
            try {
                int radius = Integer.valueOf(geoRadius);
                if (radius < 0 || radius > 5000) return ServerResponse.createError("围栏半径为0-5000");
            } catch (Exception e) {
                return ServerResponse.createError("围栏半径请输入正确的数字");
            }
        }

        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        updateOperations.set("geoCenter", geoCenter);
        updateOperations.set("geoRadius", geoRadius);

        if (StrUtil.isEmpty(gid)) {   //gid为空，则添加电子围栏
//            gid = MapService.addGeoFence(sysUnit.getName(), geoCenter, geoRadius);
            gid = MapService.addFenceBaiDu(sysUnit.getName(), geoCenter, geoRadius);
            if (StrUtil.isNotEmpty(gid)) {
                updateOperations.set("gid", gid);
                updateOperations.set("startUsing", true);   // 电子围栏创建初状态
            } else {
                return ServerResponse.createError("创建失败");
            }
        } else {  // gid不为空，则修改电子围栏
            SysUnit geofenceUnit = this.get("code", unitCode);
            if (!geoCenter.equals(geofenceUnit.getGeoCenter()) || !geoRadius.equals(geofenceUnit.getGeoRadius())) {
//                int r = MapService.updataOrStopAndStartGeoFence(gid, null, geoCenter, geoRadius, null);
                int r = MapService.updataCircleFenceBaidu(gid, null, geoCenter, geoRadius);
                if (r != 0) return ServerResponse.createError("修改失败");
            }
        }

        Query<SysUnit> query = this.createQuery().filter("code", unitCode);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public ServerResponse<String> saveGeofenceUnit2(UnitData sysUnit) {
        String gid = sysUnit.getGid();
        String geoCenter = sysUnit.getGeoCenter();
        String geoRadius = sysUnit.getGeoRadius();
        String unitCode = sysUnit.getCode();

        if (StrUtil.isEmpty(geoRadius)) {
            geoRadius = "500000";
        } else {
            try {
                int radius = Integer.valueOf(geoRadius);
                if (radius < 0 || radius > 500000) return ServerResponse.createError("围栏半径为0-500000");
            } catch (Exception e) {
                return ServerResponse.createError("围栏半径请输入正确的数字");
            }
        }

        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        updateOperations.set("geoCenter", geoCenter);
        updateOperations.set("geoRadius", geoRadius);

        if (StrUtil.isEmpty(gid)) {   //gid为空，则添加电子围栏
            gid = Utils.getUUID();
            updateOperations.set("gid", gid);
            updateOperations.set("startUsing", true);   // 电子围栏创建初状态
        }  // gid不为空，则修改电子围栏

        Query<SysUnit> query = this.createQuery().filter("code", unitCode);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public ServerResponse<String> saveGeofenceUnit3(UnitData sysUnit) {
        String gid = sysUnit.getGid();
        String geoCenter = sysUnit.getGeoCenter();
        String geoRadius = sysUnit.getGeoRadius();
        String unitCode = sysUnit.getCode();

        if (StrUtil.isEmpty(geoRadius)) {
            geoRadius = "500000";
        } else {
            try {
                int radius = Integer.valueOf(geoRadius);
                if (radius < 0 || radius > 500000) return ServerResponse.createError("围栏半径为0-500000");
            } catch (Exception e) {
                return ServerResponse.createError("围栏半径请输入正确的数字");
            }
        }

        SysUnit subUnit = this.get("code", unitCode);
        List<SubUnitGeo> geoList = subUnit.getSubUnitGeoList();

        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        if (StrUtil.isEmpty(gid)) {   //gid为空，则添加电子围栏
            SubUnitGeo subunitGeo = new SubUnitGeo();
            subunitGeo.setGid(Utils.getUUID());
            subunitGeo.setGeoCenter(geoCenter);
            subunitGeo.setGeoRadius(geoRadius);
            subunitGeo.setStartUsing(true);        // 电子围栏创建初状态

            if (geoList == null) geoList = new ArrayList<>();
            geoList.add(subunitGeo);

            updateOperations.set("subUnitGeoList", geoList);
        } else {                      // gid不为空，则修改电子围栏
            List<SubUnitGeo> newGeoList = new ArrayList<>();
            for (SubUnitGeo geo: geoList){
                if (gid.equals(geo.getGid())){
                    geo.setGeoCenter(geoCenter);
                    geo.setGeoRadius(geoRadius);
                    newGeoList.add(geo);
                } else {
                    newGeoList.add(geo);
                }
            }

            updateOperations.set("subUnitGeoList", newGeoList);
        }

        Query<SysUnit> query = this.createQuery().filter("code", unitCode);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public ServerResponse<String> onOrOffGeofenceUnit() {
        String gid = request.getParameter("gid");
        String unitCode = request.getParameter("unitCode");
        Boolean startUsing = Boolean.valueOf(request.getParameter("startUsing"));

        SysUnit geofenceUnit = this.get("code", unitCode);
        if (!geofenceUnit.getStartUsing().equals(startUsing)) { //状态不同，进行修改
            /*int r = MapService.updataOrStopAndStartGeoFence(gid, null, null, null, startUsing);
            if (r != 0 && startUsing) return ServerResponse.createError("启用失败");
            if (r != 0 && !startUsing) return ServerResponse.createError("关闭失败");

            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            updateOperations.set("startUsing", startUsing);
            Query<SysUnit> query = this.createQuery().filter("code", unitCode);
            this.update(query, updateOperations);*/
        }

        if (startUsing) {
            return ServerResponse.createSuccess("启用成功");
        } else {
            return ServerResponse.createSuccess("关闭成功");
        }
    }

    @Override
    public ServerResponse<String> onOrOffGeofenceUnit2() {
        String unitCode = request.getParameter("unitCode");
        Boolean startUsing = Boolean.valueOf(request.getParameter("startUsing"));

        SysUnit geofenceUnit = this.get("code", unitCode);
        if (!geofenceUnit.getStartUsing().equals(startUsing)) { //状态不同，进行修改
            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            updateOperations.set("startUsing", startUsing);
            Query<SysUnit> query = this.createQuery().filter("code", unitCode);
            this.update(query, updateOperations);
        }

        return ServerResponse.createSuccess("启用成功");
    }

    @Override
    public ServerResponse<String> delGeofenceUnit() {
        String gid = request.getParameter("gid");
        String unitCode = request.getParameter("unitCode");

//        int r = MapService.delGeoFence(gid);
        int r = MapService.delFenceBaiDu(gid);
        if (r == 0) {
            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            updateOperations.set("startUsing", "");
            updateOperations.set("gid", "");
            updateOperations.set("geoCenter", "");
            updateOperations.set("geoRadius", "");
            Query<SysUnit> query = this.createQuery().filter("code", unitCode);
            this.update(query, updateOperations);

            return ServerResponse.createSuccess("删除成功");
        } else {
            return ServerResponse.createError("删除失败");
        }
    }

    @Override
    public ServerResponse<String> delGeofenceUnit2() {
        String unitCode = request.getParameter("unitCode");

        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        updateOperations.set("startUsing", "");
        updateOperations.set("gid", "");
        updateOperations.set("geoCenter", "");
        updateOperations.set("geoRadius", "");
        Query<SysUnit> query = this.createQuery().filter("code", unitCode);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("删除成功");
    }

    @Override
    public ServerResponse<String> delGeofenceUnit3() {
        String unitCode = request.getParameter("unitCode");
        String gid = request.getParameter("gid");
        SysUnit subUnit = this.get("code", unitCode);
        List<SubUnitGeo> subUnitGeoList = subUnit.getSubUnitGeoList();
        List<SubUnitGeo> newSubUnitGeoList = new ArrayList<>();
        for (SubUnitGeo geo: subUnitGeoList){
            if (geo.getGid().equals(gid))continue;
            newSubUnitGeoList.add(geo);
        }

        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        updateOperations.set("subUnitGeoList", newSubUnitGeoList);
        Query<SysUnit> query = this.createQuery().filter("code", unitCode);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("删除成功");
    }

    @Override
    public ServerResponse<List<CheckBoxData>> getQuarantineInfo() {
        List<CheckBoxData> result = new ArrayList<>();

        CheckBoxData healthCodePho = new CheckBoxData("healthCodePho", "健康码");
        result.add(healthCodePho);
        CheckBoxData travelCardPho = new CheckBoxData("travelCardPho", "行程卡");
        result.add(travelCardPho);
        // 以下两项合并在健康码中 内蒙的
        CheckBoxData nucleicAcidPho = new CheckBoxData("nucleicAcidPho", "核酸检测");
        result.add(nucleicAcidPho);
        CheckBoxData vaccinationPho = new CheckBoxData("vaccinationPho", "疫苗接种");
        result.add(vaccinationPho);
        CheckBoxData temperaturePro = new CheckBoxData("temperaturePro", "体温照片");
        result.add(temperaturePro);
        CheckBoxData touchPro = new CheckBoxData("touchPro", "是否同行或密接照片");
        result.add(touchPro);
        CheckBoxData temperature = new CheckBoxData("temperature", "体温数值");
        result.add(temperature);

        return ServerResponse.createSuccess(result);
    }

    @Override
    public Map<String, Object> getPQInfoG(String unitCode) {
        Map<String, Object> result = new HashMap<>();

        SysUnit sysUnit = this.get("code", unitCode);
        if (sysUnit != null) {
            if (StrUtil.isNotEmpty(sysUnit.getIsPunchClock())) result.put("isPunchClock", sysUnit.getIsPunchClock());
            if (StrUtil.isNotEmpty(sysUnit.getIsQuarantine())) result.put("isQuarantine", sysUnit.getIsQuarantine());
            if (ObjectUtil.isNotEmpty(sysUnit.getQuarantineInfo()))
                result.put("quarantineInfo", sysUnit.getQuarantineInfo());
            if (StrUtil.isNotEmpty(sysUnit.getIsGrabNumber())) result.put("isGrabNumber", sysUnit.getIsGrabNumber());
        }

        return result;
    }

    Boolean checkUnit(UnitData unit) {
        if (unit == null) return false;
        if (unit.getCode() == null) return false;
//        if (unit.getIp() == null) return false;

        return true;
    }

    @Override
    public ServerResponse<List<CompanyUnit>> searchUnit(String unitCode) {
        Query<SysUnit> query = this.createQuery();
        if (StrUtil.isNotEmpty(unitCode)) {
            query.filter("pCode", unitCode);
        } else {
            query.or(
                    query.criteria("pCode").equal(null),
                    query.criteria("pCode").equal("")
            );
        }
        List<SysUnit> list = this.list(query);
        List<CompanyUnit> resultList = new ArrayList<>();
        for (SysUnit sysUnit : list) {
            CompanyUnit cu = new CompanyUnit();
            cu.setUnitCode(sysUnit.getCode());
            cu.setUnitName(sysUnit.getName());
            resultList.add(cu);
        }
        return ServerResponse.createSuccess(resultList);
    }

    @Override
    public ServerResponse<String> saveUnitGeoCenter(String subCode, String longitude, String latitude, String radius) {
        SysUnit geofenceUnit = this.get("code", subCode);
        UnitData unitData = new UnitData();
        unitData.setCode(subCode);
        unitData.setName(geofenceUnit.getName());
        if (StrUtil.isNotEmpty(geofenceUnit.getGid())) unitData.setGid(geofenceUnit.getGid());
        unitData.setGeoCenter(longitude + "," + latitude);
//        if (StrUtil.isEmpty(radius)) radius = "100";   //默认围栏半径为100米
        if (StrUtil.isEmpty(radius)) radius = "50000";   //默认围栏半径为50公里
        unitData.setGeoRadius(radius);

//        return saveGeofenceUnit(unitData);
        return saveGeofenceUnit2(unitData);
    }

    @Override
    public ServerResponse<String> saveUnitGeoCenter2(String subCode, String longitude, String latitude, String radius) {
        SysUnit geofenceUnit = this.get("code", subCode);
        // 判断经纬度是否超界
        boolean isIn = IdUnit.isInArea(Double.valueOf(longitude), Double.valueOf(latitude));
        if (isIn) return ServerResponse.createError("位置错误");

        // 判断新的经纬度坐标是否存在有效围栏内
        List<SubUnitGeo> geoList = geofenceUnit.getSubUnitGeoList();
        if (geoList == null) geoList = new ArrayList<>();
        for (SubUnitGeo geo: geoList){
            String geoCenter = geo.getGeoCenter();
            String[] geoCenterArr = geoCenter.split(",");
            double s = IdUnit.getDistance(Double.valueOf(longitude), Double.valueOf(latitude), Double.valueOf(geoCenterArr[0]), Double.valueOf(geoCenterArr[1]));
            if (s <= Double.valueOf(geo.getGeoRadius())) {
                /*if (geo.getStartUsing()) {    //判断当前围栏是否启用
                } else {
                }*/
                return ServerResponse.createError("当前位置在已有围栏内，添加失败");
            }
        }

        SubUnitGeo subunitGeo = new SubUnitGeo();
        subunitGeo.setGid(Utils.getUUID());
        subunitGeo.setGeoCenter(longitude + "," + latitude);
        if (StrUtil.isEmpty(radius)) radius = "50000";   //默认围栏半径为50公里
        subunitGeo.setGeoRadius(radius);
        subunitGeo.setStartUsing(true);        // 电子围栏创建初状态
        geoList.add(subunitGeo);

        Query<SysUnit> query = this.createQuery().filter("code", subCode);
        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        updateOperations.set("subUnitGeoList", geoList);
        this.update(query, updateOperations);

        return ServerResponse.createSuccess("保存成功");
    }

    @Override
    public ServerResponse<List<SysUnit>> searchSubUnitByName(String name) {
        Query<SysUnit> query = this.createQuery();
        query.and(
                query.criteria("pCode").notEqual(null),
                query.criteria("pCode").notEqual("")
        );
        if (StrUtil.isNotEmpty(name)) query.criteria("name").startsWithIgnoreCase(name);

        List<SysUnit> list = this.list(query);
        return ServerResponse.createSuccess("查询成功", list);
    }

    @Override
    public List<SysUnit> getSysUnitListByType(Integer type, String pcode) {
        if (ObjectUtil.isEmpty(type)) return null;

        Query<SysUnit> query = this.createQuery();
        if (type == 0) { //一级单位
            query.or(
                    query.criteria("pCode").equal(null),
                    query.criteria("pCode").equal("")
            );
        } else if (type == 1) { //二级单位
            query.and(
                    query.criteria("pCode").notEqual(null),
                    query.criteria("pCode").notEqual(""),
                    query.criteria("pCode").equal(pcode)
            );
        } else {
            return null;
        }

        List<SysUnit> list = this.list(query);
        return list;
    }


    @Override
    public ServerResponse<String> editUNitPwd(PwdData pwdData) {
        SysUnit unit = this.get("id", pwdData.getId());
        String md5OldPwd = Utils.md5(pwdData.getOldPwd());
        if (StrUtil.isEmpty(pwdData.getLoginName()) || pwdData.getLoginName().equals(unit.getLoginName()))
            return ServerResponse.createError("请输入新的唯一登录名");
        SysUnit checkLoginName = this.get("loginName", pwdData.getLoginName());
        if (checkLoginName != null) return ServerResponse.createError("用户名:" + pwdData.getLoginName() + " 已存在");
        if (md5OldPwd.equals(unit.getPassword())) {
            if (!pwdData.getNewPwd().equals(pwdData.getNewPwd2())) return ServerResponse.createError("新密码和确认密码不一致");
            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            updateOperations.set("loginName", pwdData.getLoginName());
            updateOperations.set("password", Utils.md5(pwdData.getNewPwd()));
            this.update(this.createQuery().filter("id", unit.getId()), updateOperations);
            return ServerResponse.createSuccess("修改成功");
        } else {
            return ServerResponse.createError("原密码错误");
        }
    }

    @Override
    public ServerResponse<String> saveSubUnitExcavateArea(UnitData sysUnit) {
        Query<SysUnit> query = this.createQuery();
        query.criteria("id").equal(sysUnit.getId());
        UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitExcavateArea()) && sysUnit.getSubUnitExcavateArea().size() > 0)
            updateOperations.set("subUnitExcavateArea", sysUnit.getSubUnitExcavateArea());
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitExitPoint()) && sysUnit.getSubUnitExitPoint().size() > 0)
            updateOperations.set("subUnitExitPoint", sysUnit.getSubUnitExitPoint());
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitEntryPoint()) && sysUnit.getSubUnitEntryPoint().size() > 0)
            updateOperations.set("subUnitEntryPoint", sysUnit.getSubUnitEntryPoint());
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitEmptyPoundPoint()) && sysUnit.getSubUnitEmptyPoundPoint().size() > 0)
            updateOperations.set("subUnitEmptyPoundPoint", sysUnit.getSubUnitEmptyPoundPoint());
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitHeavyPoundPoint()) && sysUnit.getSubUnitHeavyPoundPoint().size() > 0)
            updateOperations.set("subUnitHeavyPoundPoint", sysUnit.getSubUnitHeavyPoundPoint());
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitLoadTruckPoint()) && sysUnit.getSubUnitLoadTruckPoint().size() > 0)
            updateOperations.set("subUnitLoadTruckPoint", sysUnit.getSubUnitLoadTruckPoint());
        if (StrUtil.isNotEmpty(sysUnit.getSubUnitUnloadTruckPoint()) && sysUnit.getSubUnitUnloadTruckPoint().size() > 0)
            updateOperations.set("subUnitUnloadTruckPoint", sysUnit.getSubUnitUnloadTruckPoint());

        this.update(query, updateOperations);
        System.out.println(sysUnit.getSubUnitExcavateArea());
        return ServerResponse.createSuccess("修改成功");

    }

    @Override
    public ServerResponse<String> editUnitHandlingCost(UnitData unit) {
        if (checkUnit(unit) && unit.getId() != null) {
            // 若handlingCost值为1，则mchId和key必填
            if(unit.getHandlingCost() == 1 && (StrUtil.isEmpty(unit.getMchId()) || StrUtil.isEmpty(unit.getKey())))
                return ServerResponse.createError("商户号和key缺少则无法成功转账");

            Query<SysUnit> query = this.createQuery();
            query.filter("id", unit.getId());
            query.filter("updateTime", unit.getUpdateTime());

            UpdateOperations<SysUnit> updateOperations = this.createUpdateOperations();
            if (StrUtil.isNotEmpty(unit.getHandlingCost())) updateOperations.set("handlingCost", unit.getHandlingCost());
            if (StrUtil.isNotEmpty(unit.getMchId())){
                updateOperations.set("mchId", unit.getMchId());
            } else {
                updateOperations.set("mchId", "");
            }
            if (StrUtil.isNotEmpty(unit.getKey())){
                updateOperations.set("key", unit.getKey());
            } else {
                updateOperations.set("key", "");
            }

            this.update(query, updateOperations);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查未通过");
        }
    }

    @Override
    public SysUnit findUserByName(String name) {
        return this.get(this.createQuery().filter("loginName", name));
    }

    @Override
    public List<String> findPermissions(String name) {
        //返回当前用户权限列表
        List<String> permissions = new ArrayList<>();
        permissions.add("sys:menu:add");
        permissions.add("sys:menu:edit");
        permissions.add("sys:menu:delete");
        return permissions;
    }
}
