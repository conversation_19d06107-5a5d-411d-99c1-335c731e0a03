package com.erdos.coal.park.api.manage.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.erdos.coal.alibaba.sms.service.SMSService;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.api.manage.dao.ISMSDao;
import com.erdos.coal.park.api.manage.dao.ISMSResultDao;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.entity.SMSResult;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.utils.StrUtil;
import com.huawei.service.HuaWeiSMSService;
import dev.morphia.query.UpdateOperations;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("smsService")
public class SMSServiceImpl extends BaseMongoServiceImpl<SMS, ISMSDao> implements ISMSService {
    @Resource
    private ISMSResultDao ismsResultDao;

    @Override
    public ServerResponse<String> sendSMS(String mobile) {
        //TODO: 手机号合法性校验
        if (!StrUtil.isPhone(mobile)) return ServerResponse.createError("参数错误！");

        Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("mobile", mobile);

        //TODO: 记录短信验证码与对应手机号及过期时间(测试时指定短信验证码为 123456)

        //TODO: 1, 判断手机号是否已经注册
        SMS sms = this.get(mapParam);
        //String code = "123456";
        String code = String.valueOf(Math.random()).substring(2, 8);//6位数随机码
        if (mobile.equals("13245678900") || mobile.equals("15711411367")) code = "123456";
        if (sms == null) {
            //TODO: 未注册 进行短信发送
            //do send sms...

            sms = new SMS();
            sms.setMobile(mobile);
            sms.setCode(code);
            sms.setSendStatus(1);
            sms.setSendTime(new Date());
            this.save(sms);

            if (!mobile.equals("13245678900") && !mobile.equals("15711411367")) {
                SendSmsResponse sendSmsResponse = SMSService.sendSMS(mobile, code);
                if (StrUtil.isEmpty(sendSmsResponse.getCode()) || !"OK".equals(sendSmsResponse.getCode())) {
                    SMSResult smsResult = new SMSResult(mobile);
                    BeanUtils.copyProperties(sendSmsResponse, smsResult);
                    ismsResultDao.save(smsResult);
//                    return ServerResponse.createError("短信发送失败，1小时后重试或联系管理员");
                }
                /*List<String> templateParasList = new ArrayList<>();
                templateParasList.add(code);
                String res =HuaWeiSMSService.sendSmsYanZhengMa(mobile, JSONArray.parseArray(JSON.toJSONString(templateParasList)).toJSONString());
                if (StrUtil.isNotEmpty(res)){
                    SMSResult smsResult = new SMSResult(mobile);
                    smsResult.setMessage(res);
                    ismsResultDao.save(smsResult);
                }*/
            }
        } else {
            //在列表中，判断时间间隔

            //如果没有超过1分钟禁止重复发送
            Date mTime = Utils.getAfterDateTime(sms.getSendTime(), 0, 0, 0, 0, 1, 0);
            if (mTime.getTime() >= (new Date()).getTime()) {
                return ServerResponse.createError("频繁发送");
            }

            //do send sms...

            UpdateOperations<SMS> updateOperations = this.createUpdateOperations();
            updateOperations.set("sendTime", new Date());
            this.update(this.createQuery().filter("_id", sms.getObjectId()), updateOperations);
        }

        ServerResponse<String> serverResponse = new ServerResponse<>();
        serverResponse.setStatus(ResponseCode.SUCCESS.getCode());
        serverResponse.setMsg("发送成功");
        serverResponse.setData(code);
        return serverResponse;
    }

    public static void main(String[] args) {
        List<String> templateParasList = new ArrayList<>();
        templateParasList.add("123123");
        String res =HuaWeiSMSService.sendSmsYanZhengMa("15357039213", JSONArray.parseArray(JSON.toJSONString(templateParasList)).toJSONString());
        System.out.println(res);
        // SMSService.sendSMS("15000000000", "123456");
    }

}