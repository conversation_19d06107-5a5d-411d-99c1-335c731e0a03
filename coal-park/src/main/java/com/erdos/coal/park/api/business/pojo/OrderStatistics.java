package com.erdos.coal.park.api.business.pojo;

import com.erdos.coal.park.api.customer.entity.CustomerUser;
import com.erdos.coal.park.api.driver.entity.DriverInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class OrderStatistics implements Serializable {
    private List<DriverInfo> driverInfo;
    private String dvrName;
    private String dvrMobile;

    private String carNum;  //车牌号
    private String outSubName;//发货单位 默认二级单位名称,销售单位
    private String place;   //运往地名称
    private Date time2;     //发货入场时间
    private Date time3;     //发货出场时间

    public String getDvrName() {
        return driverInfo.get(0).getName();
    }

    public void setDvrName(String dvrName) {
        this.dvrName = dvrName;
    }

    public String getDvrMobile() {
        return driverInfo.get(0).getMobile();
    }

    public void setDvrMobile(String dvrMobile) {
        this.dvrMobile = dvrMobile;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public Date getTime2() {
        return time2;
    }

    public void setTime2(Date time2) {
        this.time2 = time2;
    }

    public Date getTime3() {
        return time3;
    }

    public void setTime3(Date time3) {
        this.time3 = time3;
    }
}
