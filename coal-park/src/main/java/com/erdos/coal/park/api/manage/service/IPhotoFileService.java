package com.erdos.coal.park.api.manage.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Map;

public interface IPhotoFileService {

    //===============================================
    //0.图片存储 "/root/uploadsv2" ,返回文件名称，存储到数据
    String readPhoto0(String photoFileName, String dbTable);

    //返回文件path
    String uploadPhoto0(String uid, MultipartFile photoFile);

    //返回文件fileName
    String uploadPhotoByUid(String uid, String dbTable, MultipartFile photoFile);

    //返回文件fileName
    String uploadPhotoByDB(String uid, String dbTable, String photoFileName);

    //返回文件fileName集合
    Map<String, String> uploadPhotosByUid(String userId, String dbTable, Map<String, MultipartFile> photoFilesMap);

    //返回文件fileName集合
    Map<String, String> uploadPhotosByDB(String userId, String dbTable, Map<String, String> photoFileNamesMap);

    //===============================================

    //1.图片存储 FastDFS
    //返回文件path
    String uploadPhoto(MultipartFile photoFile);

    //返回文件path集合
    Map<String, String> uploadPhotos(Map<String, MultipartFile> photoFilesMap);

    //===============================================
    //2.图片上传阿里oss
    //上传图片
    String putObject(String key, InputStream inputStream);

    String putObject(String key, File file);

    void putObject(String dbPath, String tableName);

    //生成图片访问URL
    String signUrl(String objectName);

    //获取已经上传图片的InputStream
    byte[] getBytes(String objectName);

    boolean isOSSObjectNUll(String objectName);

    URL signUrl2(String objectName);

    String putObjectPublicRead(String key, InputStream inputStream);

    //生成图片访问URL（public_read）
    String publicReadUrl(String objectName);
}
