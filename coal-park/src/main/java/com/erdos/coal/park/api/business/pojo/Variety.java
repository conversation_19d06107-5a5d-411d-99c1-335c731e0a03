package com.erdos.coal.park.api.business.pojo;

import java.io.Serializable;
import java.util.List;

public class Variety implements Serializable {
    private String code;                    //编号
    private String name;                    //名称
    private List<Variety> child;      //子节点集
    private String parentCode;              //父节点编号
    private Integer dept;                   //所在树节点深度
    private String grandParentCode;                   //父节点的父节点编号

    private String placeInput;      //二级单位录入地点
    private String productID;       //品种code-智慧能源需要的 合同备案中的煤种ID

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Variety> getChild() {
        return child;
    }

    public void setChild(List<Variety> child) {
        this.child = child;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getDept() {
        return dept;
    }

    public void setDept(Integer dept) {
        this.dept = dept;
    }

    public String getGrandParentCode() {
        return grandParentCode;
    }

    public void setGrandParentCode(String grandParentCode) {
        this.grandParentCode = grandParentCode;
    }

    public String getPlaceInput() {
        return placeInput;
    }

    public void setPlaceInput(String placeInput) {
        this.placeInput = placeInput;
    }

    public String getProductID() {
        return productID;
    }

    public void setProductID(String productID) {
        this.productID = productID;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Variety variety = (Variety) o;

        return code.equals(variety.code) && dept.equals(variety.dept);
    }

    @Override
    public int hashCode() {
        int result = code.hashCode();
        result = 31 * result + name.hashCode();
        return result;
    }
}