package com.erdos.coal.park.api.customer.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.entity.DriverGroup;
import com.erdos.coal.park.api.customer.service.IDriverGroupService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

//"客商APP司机组管理接口列表"
@RestController
@RequestMapping("/api/cus/driver")
public class DriverGroupController extends BaseController {
    @Resource
    private IDriverGroupService driverGroupService;

    @InvokeLog(description = "添加新司机组 接口") //日志
    @PostMapping(value = "/add_driver_group")
    public ServerResponse<DriverGroup> addDriverGroupHandler(
            @RequestParam(value = "groupName") String groupName,        //"司机组名称"
            @RequestParam(value = "mobiles") String[] mobiles           //"司机手机号"
    ) throws GlobalException {
        return driverGroupService.addDriverGroup(groupName, mobiles);
    }

    @InvokeLog(description = "查询司机组 接口", printReturn = false) //日志
    @PostMapping(value = "/driver_group_list_data")
    public ServerResponse<EGridResult> DriverGroupListDataHandler(
            @RequestParam(value = "groupNo", required = false) String groupNo,      //"司机组编号"
            @RequestParam(value = "page", required = false) Integer page,           //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows            //"每页多少条"
    ) throws GlobalException {
        return driverGroupService.driverGroupListData(groupNo, page, rows);
    }

    @InvokeLog(description = "删除司机组 接口") //日志
    @PostMapping(value = "/del_driver_group")
    public ServerResponse<String> delDriverGroupHandler(
            @RequestParam(value = "groupNo") String groupNo    //"司机组编号"
    ) throws GlobalException {
        return driverGroupService.delDriverGroup(groupNo);
    }

    @InvokeLog(description = "修改司机组名称 接口") //日志
    @PostMapping(value = "/edit_driver_group")
    public ServerResponse<String> editDriverGroupHandler(
            @RequestParam(value = "groupNo") String groupNo,        //"司机组编号"
            @RequestParam(value = "groupName") String groupName     //"司机组名称"
    ) throws GlobalException {
        return driverGroupService.editDriverGroup(groupNo, groupName);
    }

    @InvokeLog(description = "添加新司机组,或司机组添加司机 接口") //日志
    @PostMapping(value = "/add_driver_group2")
    public ServerResponse<String> addDriverGroup2Handler(
            @RequestParam(value = "groupNo", required = false) String groupNo,        //"司机组编号"
            @RequestParam(value = "groupName", required = false) String groupName,       //"司机组名称"
            @RequestParam(value = "mobiles", required = false) String[] mobiles           //"司机手机号"
    ) throws GlobalException {
        return driverGroupService.addDriverGroup2(groupNo, groupName, mobiles);
    }

    @InvokeLog(description = "移除司机组中司机 接口") //日志
    @PostMapping(value = "/del_driver")
    public ServerResponse<String> delDriverHandler(
            @RequestParam(value = "groupNo") String groupNo,        //"司机组编号"
            @RequestParam(value = "mobiles") String[] mobiles           //"司机手机号"
    ) throws GlobalException {
        return driverGroupService.delDriver(groupNo, mobiles);
    }

    @InvokeLog(description = "查询司机组 接口", printReturn = false) //日志
    @PostMapping(value = "/driver_group_list_data2")
    public ServerResponse<EGridResult> DriverGroupListData2Handler(
            @RequestParam(value = "groupNo", required = false) String groupNo,      //"司机组编号"
            @RequestParam(value = "page", required = false) Integer page,           //"第几页"
            @RequestParam(value = "rows", required = false) Integer rows            //"每页多少条"
    ) throws GlobalException {
        return driverGroupService.driverGroupListData2(groupNo, page, rows);
    }

}
