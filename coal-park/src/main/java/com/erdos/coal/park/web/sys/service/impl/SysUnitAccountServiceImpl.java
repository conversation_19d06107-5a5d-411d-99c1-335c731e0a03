package com.erdos.coal.park.web.sys.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.web.sys.dao.ISysUnitAccountDao;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.park.web.sys.entity.SysUnitAccount;
import com.erdos.coal.park.web.sys.pojo.UnitData;
import com.erdos.coal.park.web.sys.service.ISysUnitAccountService;
import com.erdos.coal.park.web.sys.service.ISysUnitService;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.bson.Document;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("sysUnitAccountService")
public class SysUnitAccountServiceImpl extends BaseMongoServiceImpl<SysUnitAccount, ISysUnitAccountDao> implements ISysUnitAccountService {
    @Resource
    private ISysUnitService sysUnitService;
    @Resource
    private HttpServletRequest request;

    @Override
    public BigDecimal getAvailableFee(String uid) {
        Query<SysUnitAccount> query = this.createQuery().filter("uid", uid);
        query.order(Sort.descending("createTime"));
        List<SysUnitAccount> accountList = this.list(query);
        BigDecimal availableFee = new BigDecimal("0");
        for (SysUnitAccount account : accountList) {
            availableFee = availableFee.add(account.getTotalFee());//单位为分
        }
        return availableFee;
    }

    @Override
    public ServerResponse<String> addUnitAccount(UnitData data) {
        String userId = ShiroUtils.getUserId();
        SysUnit unit = sysUnitService.get("code", data.getCode());
        if (unit != null) {
            SysUnitAccount sysUnitAccount = new SysUnitAccount();
            sysUnitAccount.setUid(unit.getObjectId().toString());
            sysUnitAccount.setTotalFee(data.getAvailableFee().multiply(BigDecimal.valueOf(100)));
            sysUnitAccount.setUserId(userId);
            sysUnitAccount.setType(4);  // web页面充值
            this.save(sysUnitAccount);
            return ServerResponse.createSuccess();
        } else {
            return ServerResponse.createError("参数检查错误");
        }
    }

    @Override
    public ServerResponse<EGridResult> detailsAccount(Integer page, Integer rows) {
        String id = request.getParameter("id");
        SysUnit unit = sysUnitService.get("id", id);
        if (unit != null) {
            Query<SysUnitAccount> query = this.createQuery();
            query.filter("uid", unit.getObjectId().toString());
            query.order(Sort.descending("createTime"));
            List<SysUnitAccount> accountList = this.list(query);

            List<SysUnitAccount> list = new ArrayList<>();
            for (SysUnitAccount account : accountList) {
                SysUnitAccount unitAccount = new SysUnitAccount();
                unitAccount.setTotalFee(account.getTotalFee().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
                unitAccount.setCreateTime(account.getCreateTime());
                list.add(unitAccount);
            }

            int size = list.size();
            int pageCount = size / rows;
            int fromIndex = rows * (page - 1);
            int toIndex = fromIndex + rows;
            if (toIndex >= size) {
                toIndex = size;
            }
            if (page > pageCount + 1) {
                fromIndex = 0;
                toIndex = 0;
            }
            EGridResult result = new EGridResult(list.size(), list.subList(fromIndex, toIndex));
            return ServerResponse.createSuccess(result);
        }

        return ServerResponse.createError();
    }

    @Override
    public ServerResponse<BigDecimal> responseAvailableFee() {
        String id = request.getParameter("id");
        SysUnit unit = sysUnitService.get("id", id);
        if (unit != null) {
            BigDecimal availableFee = getAvailableFee(unit.getObjectId().toString());
            return ServerResponse.createSuccess(availableFee.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));//转换为元
        }
        return null;
    }

    @Override
    public void createSysUnitAccountDocs(List<Document> sysUnitAccountDocs, String[] uids, String[] userIds, int[] types, String oid, int[] fees, Date time) {
        //单位代付相关
        if (fees[0] != 0) {
            SysUnitAccount uAccount = new SysUnitAccount();
            uAccount.setUid(uids[0]);
            uAccount.setUserId(userIds[0]);
            uAccount.setType(types[0]);
            uAccount.setOid(oid);
            uAccount.setUpdateTime(time.getTime());
            Document uAccountDoc = Document.parse(JSONObject.toJSONString(uAccount));
            uAccountDoc.append("totalFee", new BigDecimal(fees[0]));
            uAccountDoc.append("createTime", time);
            sysUnitAccountDocs.add(uAccountDoc);
        }
        //单位代扣相关
        for (int i = 1; i < 3; i++) {
            if (fees[i] == 0) continue;
            SysUnitAccount uAccount = new SysUnitAccount();
            uAccount.setUid(uids[i]);
            uAccount.setUserId(userIds[i]);
            uAccount.setType(types[1]);
            uAccount.setOid(oid);
            uAccount.setUpdateTime(time.getTime());
            Document uAccountDoc = Document.parse(JSONObject.toJSONString(uAccount));
            uAccountDoc.append("totalFee", new BigDecimal(fees[i]));
            uAccountDoc.append("createTime", time);
            sysUnitAccountDocs.add(uAccountDoc);
        }
    }

    @Override
    public Document createSysUnitAccountDocs(String uid, String userId, int type, String oid, int fee, Date time) {
        SysUnitAccount uAccount = new SysUnitAccount();
        uAccount.setUid(uid);
        if (userId != null) uAccount.setUserId(userId);
        uAccount.setType(type);
        uAccount.setOid(oid);
        uAccount.setUpdateTime(time.getTime());
        Document uAccountDoc = Document.parse(JSONObject.toJSONString(uAccount));
        uAccountDoc.append("totalFee", new BigDecimal(fee));
        uAccountDoc.append("createTime", time);
        return uAccountDoc;
    }
}
