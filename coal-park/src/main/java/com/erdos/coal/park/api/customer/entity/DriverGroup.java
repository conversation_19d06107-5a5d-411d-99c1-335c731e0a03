package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import dev.morphia.annotations.*;

import java.util.List;

@Entity(value = "t_driver_group", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("cid")})
})
public class DriverGroup extends BaseMongoInfo {
    //@Indexed(options = @IndexOptions(name = "_cid", background = true))
    private String cid;         //客商编号
    private String groupNo;     //司机组编号
    private String groupName;   //司机组名称
    private String[] dvrId;    //司机id

    @Reference(value = "customerUserID", lazy = true, idOnly = true, ignoreMissing = true)
    private CustomerUser customerUser;

    @Reference(idOnly = true, lazy = true, ignoreMissing = true)
    private List<DriverInfo> driverInfos; //司机id

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String[] getDvrId() {
        return dvrId;
    }

    public void setDvrId(String[] dvrId) {
        this.dvrId = dvrId;
    }

    public CustomerUser getCustomerUser() {
        return customerUser;
    }

    public void setCustomerUser(CustomerUser customerUser) {
        this.customerUser = customerUser;
    }

    public List<DriverInfo> getDriverInfos() {
        return driverInfos;
    }

    public void setDriverInfos(List<DriverInfo> driverInfos) {
        this.driverInfos = driverInfos;
    }
}
