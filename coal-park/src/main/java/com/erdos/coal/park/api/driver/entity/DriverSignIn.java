package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

import java.util.List;

@Entity(value = "t_driver_signin",noClassnameStored = true)
public class DriverSignIn extends BaseMongoInfo {

    private String driverId;            //签到司机编号
    private String driverName;          //签到司机姓名
    private String mobile;              //签到司机手机号
    private String oid;                 //签到订单编号
    private String defaultDownUnit;     //签到二级单位编号
    private String subName;             //签到二级单位名称
    private String geoFenceId;          //签到二级单位地理围栏编号
    private String geoFenceName;        //签到二级单位地理围栏名称
    private Integer type;               //签到类型 0-二级单位签到
    private List<String> geofenceIds;
    private List<String> geofenceNames;
    private String locations;

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getDefaultDownUnit() {
        return defaultDownUnit;
    }

    public void setDefaultDownUnit(String defaultDownUnit) {
        this.defaultDownUnit = defaultDownUnit;
    }

    public String getGeoFenceId() {
        return geoFenceId;
    }

    public void setGeoFenceId(String geoFenceId) {
        this.geoFenceId = geoFenceId;
    }

    public String getGeoFenceName() {
        return geoFenceName;
    }

    public void setGeoFenceName(String geoFenceName) {
        this.geoFenceName = geoFenceName;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<String> getGeofenceIds() {
        return geofenceIds;
    }

    public void setGeofenceIds(List<String> geofenceIds) {
        this.geofenceIds = geofenceIds;
    }

    public List<String> getGeofenceNames() {
        return geofenceNames;
    }

    public void setGeofenceNames(List<String> geofenceNames) {
        this.geofenceNames = geofenceNames;
    }

    public String getLocations() {
        return locations;
    }

    public void setLocations(String locations) {
        this.locations = locations;
    }
}
