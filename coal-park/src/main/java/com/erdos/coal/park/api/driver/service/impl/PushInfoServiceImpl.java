package com.erdos.coal.park.api.driver.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.httpclient.HttpResult;
import com.erdos.coal.httpclient.service.IHttpAPIService;
import com.erdos.coal.park.api.customer.entity.Order;
import com.erdos.coal.park.api.customer.entity.OrderTaking;
import com.erdos.coal.park.api.customer.service.IOrderService;
import com.erdos.coal.park.api.customer.service.IOrderTakingService;
import com.erdos.coal.park.api.driver.dao.IPushInfoDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.PushInfo;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import com.erdos.coal.park.api.manage.entity.Locked;
import com.erdos.coal.park.api.manage.service.ILockedService;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("pushInfoService")
public class PushInfoServiceImpl extends BaseMongoServiceImpl<PushInfo, IPushInfoDao> implements IPushInfoService {

    @Resource
    private IOrderTakingService orderTakingService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IHttpAPIService httpAPIService;
    @Resource
    private ILockedService lockedService;

    @Override
    public ServerResponse<List<PushInfo>> pushList(Integer type) {
        String did = ShiroUtils.getUserId();
        Query<PushInfo> query = this.createQuery();
        query.filter("did", did);
        query.filter("type", type);
        query.order(Sort.descending("createTime"));
        List<PushInfo> pushInfoList = query.find().toList();
        return ServerResponse.createSuccess("查询成功", pushInfoList);
    }

    @Override
    public ServerResponse<String> pushEdit(String id) {
        UpdateOperations<PushInfo> updateOperations = this.createUpdateOperations();
        updateOperations.set("read", true);
        this.update(this.createQuery().filter("_id", new ObjectId(id)), updateOperations);
        return ServerResponse.createSuccess();
    }

    @Override
    public void addPushInfo(String title, String body, String did, Integer type, String errCode, String errMsg, String msGid) {
        PushInfo pushInfo = new PushInfo();
        pushInfo.setTitle(title);
        pushInfo.setBody(body);
        pushInfo.setDid(did);
        pushInfo.setRead(false);
        pushInfo.setType(type);
        pushInfo.setErrCode(errCode);
        pushInfo.setErrMsg(errMsg);
        pushInfo.setMsGid(msGid);
        save(pushInfo);
    }

    /**
     * //标题，内容，司机id，是否已读，类型1:派单    2:抢单成功  3：到达目的地   4：扣款及账户金额   5：账户充值     6：撤单
     */
    @Override
    @Async("busTaskExecutor")   //异步执行司机推送信息添加和向司机发送推送信息
    public void addAndSendPushInfo(String title, String body, String did, Integer type, String deviceId) {
        PushInfo pushInfo = new PushInfo();
        pushInfo.setTitle(title);
        pushInfo.setBody(body);
        pushInfo.setDid(did);
        pushInfo.setRead(false);
        pushInfo.setType(type);
        save(pushInfo);

        //deviceId 实际存储的是openid，不能推送了，所以注释推送功能
        /*try {
            PushService.advancedPush(title, body, "DEVICE", deviceId, true);
        } catch (Exception e) {
            e.printStackTrace();
        }*/
    }

    @Override
    @Async("busTaskExecutor")   //异步执行司机批量推送信息添加和向司机发送推送信息
    public void addAndSendPushInfos(String title, String body, Integer type, List<DriverInfo> dInfos, List<String> oids) {
        List<PushInfo> pushInfos = new ArrayList<>();
        int j = 0;

        for (DriverInfo di : dInfos) {
            PushInfo pushInfo = new PushInfo();
            pushInfo.setTitle(title);
            pushInfo.setBody(body);
            pushInfo.setDid(di.getObjectId().toString());
            pushInfo.setType(type);
            pushInfo.setRead(false);

            if (oids != null && oids.size() > j) {//list 为有序存储
                pushInfo.setOid(oids.get(j));
                j++;
            }

            pushInfos.add(pushInfo);
        }
        save(pushInfos);//保存推送消息

        //deviceId 实际存储的是openid，不能推送了，所以注释推送功能
        /*try {
            for (int i = 0; i < dInfos.size(); i += 100) {
                // 取出100条司机的deviceId 生成格式为555650137806,537368309003,537366789549,537253431467
                String alias = dInfos.stream().skip(i).limit(100).map(DriverInfo::getDeviceId).collect(Collectors.joining(","));
                PushService.advancedPush(title, body, "DEVICE", alias, true);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }*/
    }

    @Override
    public ServerResponse<DriverOrderData> pushQueryDelete(String oid) {
        Order order = orderService.get("oid", oid);
        OrderTaking taking = orderTakingService.get("oid", oid);

        if (order == null || taking == null) return ServerResponse.createError("订单编号错误！");

        DriverOrderData driverOrderData = new DriverOrderData();
        //if (taking.getFinishTag() == 0) {       //运输后的订单，不再允许司机同意撤销订单
        if ((StrUtil.isNotEmpty(order.getOutChecking()) && order.getOutChecking() > 0) || (StrUtil.isNotEmpty(order.getInChecking()) && order.getInChecking() > 0)) {       //运输后的订单，不再允许司机同意撤销订单
            driverOrderData.setCarNum(order.getCarNum());
            driverOrderData.setBeginPoint(order.getBeginPoint());
            driverOrderData.setPrice(order.getPrice());
            driverOrderData.setEndPoint(order.getEndPoint());
            driverOrderData.setTradeName(order.getTradeName());
            driverOrderData.setDistance(order.getDistance());
            driverOrderData.setTolls(order.getTolls());
            driverOrderData.setMold(order.getMold());
            driverOrderData.setOid(oid);
            driverOrderData.setOrderDate(taking.getUpdateTime());
            driverOrderData.setCreateDate(taking.getCreateTime());
            driverOrderData.setFinishTag(taking.getFinishTag());
            driverOrderData.setDelete(taking.getDelete());//客商是否撤单
        }
        return ServerResponse.createSuccess("查询成功", driverOrderData);
    }

    private String getAccessToken() {
        String url1 = "https://api.weixin.qq.com/cgi-bin/token";
        Map<String, Object> paramMap1 = new HashMap<>();
        paramMap1.put("grant_type", "client_credential");
        paramMap1.put("appid", WXPayConstants.wechat_appid);
        paramMap1.put("secret", WXPayConstants.wechat_secret);
        String access_token = null;
        Integer expires_in = null;
        try {
            String result = httpAPIService.doGet(url1, paramMap1);

            JSONObject jsonObject = JSON.parseObject(result);
            access_token = jsonObject.getString("access_token");
            expires_in = jsonObject.getInteger("expires_in");
        } catch (Exception e) {
            e.printStackTrace();
        }

        //保存access_token到数据库
        Locked locked = lockedService.get("userId", "we_chat_small_pro_dvr");
        if (locked == null) {
            locked = new Locked();
            locked.setUserId("we_chat_small_pro_dvr");
            locked.setTest(access_token);
            locked.setType(expires_in);
            lockedService.save(locked);
        } else {
            Query<Locked> query = lockedService.createQuery();
            query.filter("userId", "we_chat_small_pro_dvr");
            UpdateOperations<Locked> up = lockedService.createUpdateOperations();
            up.set("test", access_token);
            up.set("type", expires_in);
            lockedService.update(query, up);
        }

        return access_token;
    }

    private String searchAccessToken() {
        Locked locked = lockedService.get("userId", "we_chat_small_pro_dvr");

        if (locked == null) return getAccessToken();

        //判断token是否快要到期，更新token
        int exTime = locked.getType();
        if (System.currentTimeMillis() - locked.getUpdateTime() >= exTime * 1000 - 180 * 1000)
            return getAccessToken();

        return locked.getTest();
    }

    private Map<String, String> weChatSend(String openId, String tid, String objStr) {
        Map<String, String> resMap = new HashMap<>();
        String url8 = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + searchAccessToken();

        JSONObject object = JSONObject.parseObject(objStr);

        Map<String, Object> paramMap8 = new HashMap<>();
        paramMap8.put("touser", openId);
        paramMap8.put("template_id", tid);
        paramMap8.put("page", "pages/transportService/transportService");
        paramMap8.put("data", object);
        paramMap8.put("miniprogram_state", "formal");
        paramMap8.put("lang", "zh_CN");
        try {
            String jsonStr = JSONObject.toJSONString(paramMap8);
            HttpResult result = httpAPIService.sendPost(url8, jsonStr);
            String data = result.getBody();
            JSONObject obj = JSONObject.parseObject(data);
            String errCode = obj.getString("errcode");
            String errMsg = obj.getString("errmsg");
            String msgid = obj.getString("msgid");

            resMap.put("errCode", errCode);
            resMap.put("errMsg", errMsg);
            resMap.put("msgid", msgid);
        } catch (Exception e) {
            resMap.put("errMag", "程序异常");
            e.printStackTrace();
        }

        return resMap;
    }

    @Override
    public Map<String, String> weChatSendDvr(String openId, String mobile, String name, String phrase, String thing) {
        //认证结果通知
        String tid = "StJAqCkGPvy2KX8sNM_K8D2R1HBBuSy664kL8dJKQS4";

        String objStr = "{\"character_string1\":{\"value\":\"" + mobile + "\"},\"name2\":{\"value\":\"" + name + "\"},\"phrase4\":{\"value\":\"" + phrase + "\"},\"thing5\":{\"value\":\"" + thing + "\"}}";

        return weChatSend(openId, tid, objStr);
    }

    @Override
    @Async("busTaskExecutor")
    public Map<String, String> weChatSendCar(String openId, String mobile, String name, String phrase, String thing, String carNum, String did) {
        //认证结果通知
        String tid = "StJAqCkGPvy2KX8sNM_K8ACAwn3yxqjCI7XxnA914eo";

        String objStr = "{\"character_string1\":{\"value\":\"" + mobile + "\"},\"name2\":{\"value\":\"" + name + "\"},\"phrase4\":{\"value\":\"" + phrase + "\"},\"thing5\":{\"value\":\"" + thing + "\"},\"car_number7\":{\"value\":\"" + carNum + "\"}}";

        Map<String, String> map = weChatSend(openId, tid, objStr);
        //保存推送信息
        this.addPushInfo("车辆审核", "车辆：" + carNum + "审核结果" + thing, did, 7, map.get("errCode"), map.get("errMsg"), map.get("msGid"));
        return map;
    }

    @Override
    public Map<String, String> weChatSend4081(String openId, String name, String date, String thing) {
        //用户呼叫通知
//        String tid = "Qex1KHY4YTRerOBxlIsJ2-r7Q_pgHzsKda-4HqzH8k8";
        String tid = "Qex1KHY4YTRerOBxlIsJ25f6mZobpbXAyh00ZTIHvUI";

        String objStr = "{\"phrase1\":{\"value\":\"进场叫号\"},\"name2\":{\"value\":\"" + name + "\"},\"date3\":{\"value\":\"" + date + "\"},\"thing4\":{\"value\":\"" + thing + "\"}}";

        return weChatSend(openId, tid, objStr);
    }

    @Override
    @Async("busTaskExecutor")
    public void weChatSend36090(String openId, String name, String date, String thing, String msg, String did) {
        //车辆入场通知
        String tid = "EyluzkbNMoeOyOZzsKfR9GN_jPa633_atiK0kp0AdvQ";

        String objStr = "{\"thing2\":{\"value\":\"" + name + "\"},\"time4\":{\"value\":\"" + date + "\"},\"thing5\":{\"value\":\"" + thing + "\"}}";

        Map<String, String> resMap = weChatSend(openId, tid, objStr);
        this.addPushInfo(name + "叫号", msg, did, 8, resMap.get("errCode"), resMap.get("errMsg"), resMap.get("msGid"));
    }

    //wBFcbbGvRuTIzGqIgqpcC3DrcPe4yap3-aANzAOVy4s
    @Override
    @Async("busTaskExecutor")
    public void weChatSend14370(String openId, String oid, String carNum, String data, String thing, String did) {
        //排队入场通知-通知装卸
        String tid = "wBFcbbGvRuTIzGqIgqpcC3DrcPe4yap3-aANzAOVy4s";

        String objStr = "{\"character_string1\":{\"value\":\"" + oid + "\"},\"car_number2\":{\"value\":\"" + carNum + "\"},\"time4\":{\"value\":\"" + data + "\"},\"thing6\":{\"value\":\"" + thing + "\"}}";

        Map<String, String> resMap = weChatSend(openId, tid, objStr);
        this.addPushInfo("排队入场通知", thing, did, 9, resMap.get("errCode"), resMap.get("errMsg"), resMap.get("msGid"));
    }

    @Override
    @Async("busTaskExecutor")
    public void weChatSend13662(String openId, String cName, String thing, String did, String groupNo) {
        //排队入场通知-通知装卸
        String tid = "SJ8NSC_BTFdzlBXt_GPk1FHrOo_KFGwisbJaVywpRFg";

        String objStr = "{\"thing1\":{\"value\":\"" + cName + "\"},\"thing4\":{\"value\":\"" + thing + "\"}}";

        Map<String, String> resMap = weChatSend(openId, tid, objStr);
        this.addPushInfo10("邀约通知", thing, did, 10, groupNo, resMap.get("errCode"), resMap.get("errMsg"), resMap.get("msGid"));
    }

    private void addPushInfo10(String title, String body, String did, Integer type, String groupNo, String errCode, String errMsg, String msGid) {
        PushInfo pushInfo = new PushInfo();
        pushInfo.setTitle(title);
        pushInfo.setBody(body);
        pushInfo.setDid(did);
        pushInfo.setRead(false);
        pushInfo.setType(type);
        pushInfo.setGroupNo(groupNo);
        pushInfo.setErrCode(errCode);
        pushInfo.setErrMsg(errMsg);
        pushInfo.setMsGid(msGid);
        save(pushInfo);
    }
}
