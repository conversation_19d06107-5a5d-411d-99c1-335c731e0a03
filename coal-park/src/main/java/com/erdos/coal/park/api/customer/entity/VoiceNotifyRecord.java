package com.erdos.coal.park.api.customer.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.*;

@Entity(value = "t_voice_notify_record", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("resultCode")})
})
public class VoiceNotifyRecord extends BaseMongoInfo {
    private String mobile;          //司机手机号
    private String resultCode;      //语音通知接口返回码
    private String plateNumber;     //车牌号
    private String unitName;        //单位名称

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
