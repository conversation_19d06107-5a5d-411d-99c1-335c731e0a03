package com.erdos.coal.park.api.customer.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.customer.dao.IDriverPoolDao;
import com.erdos.coal.park.api.customer.entity.DriverPool;
import com.erdos.coal.park.api.customer.pojo.DriverInfoData;
import com.erdos.coal.park.api.customer.service.IDriverPoolService;
import com.erdos.coal.park.api.driver.dao.IDriverToCarDao;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.entity.DriverToCar;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import dev.morphia.query.Query;
import dev.morphia.query.UpdateOperations;
import org.bson.types.ObjectId;
import org.omg.CORBA.INTERNAL;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("driverPoolService")
public class DriverPoolServiceImpl extends BaseMongoServiceImpl<DriverPool, IDriverPoolDao> implements IDriverPoolService {
    @Resource
    private IDriverInfoService driverInfoService;
    @Resource
    private IDriverToCarDao driverToCarDao;

    /**
     * 查询客商的司机池中所有司机
     * param:String cid
     * return：List<DriverInfoData>,包括“name,mobile,carNum”
     */
    @Override
    //public ServerResponse<List<DriverInfoData>> driverPoolListData(String carNum, Integer whiteOrBlack) {
    public ServerResponse<EGridResult> driverPoolListData(String carNum, Integer whiteOrBlack, Integer page, Integer rows) {
        String cid = ShiroUtils.getUserId();

        Query<DriverPool> query = this.createQuery();

        if (!StrUtil.isEmpty(carNum)) {
            Query<DriverToCar> driverToCarQuery = driverToCarDao.createQuery();
            driverToCarQuery.filter("carNum", carNum);
            List<DriverToCar> driverToCars = driverToCarDao.list(driverToCarQuery);

            List<String> dids = new ArrayList<>();
            for (DriverToCar dc : driverToCars) {
                dids.add(dc.getDriverId());
            }
            query.filter("did in", dids.toArray());
            /*DriverInfo driver;
            driver = driverInfoService.get("carNum", carNum);//先按车牌号查询
            if (ObjectUtil.isEmpty(driver)) {
                driver = driverInfoService.get("name", carNum);//车牌号查询不到，按姓名查询
            }
            if (ObjectUtil.isNotEmpty(driver)) {
                String did = driver.getObjectId().toString();
                query.filter("did", did);
            } else {
                return ServerResponse.createSuccess("没有该司机");
            }*/
        }

        query.filter("cid", cid);
        if (ObjectUtil.isNotNull(whiteOrBlack) && whiteOrBlack == 0) {
            query.filter("whiteOrBlack", 0);
        } else if (ObjectUtil.isNotNull(whiteOrBlack) && whiteOrBlack == 1) {
            query.filter("whiteOrBlack", 1);
        } else {
            query.criteria("whiteOrBlack").notEqual(0);
        }
        //List<DriverPool> dpList = query.find().toList();
        EGridResult<DriverPool> eGridResult = findPage(page, rows, query);
        List<DriverPool> dpList = eGridResult.getRows();
        Long total = eGridResult.getTotal();

        //转pojo
        List<DriverInfoData> resultData = new ArrayList<>();
        for (DriverPool dp : dpList) {
            //TODO:用司机id 查询 司机姓名和车牌号和手机号
            DriverInfo driverInfo = dp.getDriverInfo();
            if (driverInfo == null) continue;
            DriverInfoData driverInfoData = new DriverInfoData();
            driverInfoData.setId(dp.getObjectId().toString());
            driverInfoData.setWhiteOrBlack(dp.getWhiteOrBlack());
            driverInfoData.setName(driverInfo.getName());
            driverInfoData.setMobile(driverInfo.getMobile());

            String carNumsStr = "";
            Query<DriverToCar> dcQuery = driverToCarDao.createQuery();
            dcQuery.filter("driverId", driverInfo.getObjectId().toHexString());
            List<DriverToCar> carNums = driverToCarDao.list(dcQuery);
            for (DriverToCar c : carNums) {
                carNumsStr = c.getCarNum() + "," + carNumsStr;
            }
            driverInfoData.setCarNum(carNumsStr);
            // driverInfoData.setCarNum(driverInfo.getCarNum());

            resultData.add(driverInfoData);
        }

        //TODO:返回查询结果
        //return ServerResponse.createSuccess("查询成功", resultData);
        return ServerResponse.createSuccess("查询成功", new EGridResult<>(total, resultData));
    }

    /**
     * 按照手机号，从客商的司机池中移除司机
     * param:String mobile
     * param:String id 司机在客商司机池中的编号
     */
    @Override
    public ServerResponse<String> driverPoolDelDvr(String mobile, String id) {
        this.delete(id);
        return ServerResponse.createSuccess("手机号为:【 " + mobile + " 】的司机移除成功");
    }

    /**
     * 批量更新黑白名单
     */
    public ServerResponse<String> whiteOrBlackAdd(String[] ids, Integer whiteOrBlack) {
        List<ObjectId> objectIds = new ArrayList<>();
        for (String id : ids) {
            objectIds.add(new ObjectId(id));
        }
        Query<DriverPool> query = this.createQuery().filter("_id in", objectIds.toArray());
        UpdateOperations<DriverPool> updateOperations = this.createUpdateOperations();
        if (whiteOrBlack == null) {
            updateOperations.unset("whiteOrBlack");
        } else {
            updateOperations.set("whiteOrBlack", whiteOrBlack);
        }
        this.update(query, updateOperations);
        return ServerResponse.createSuccess("修改成功");
    }
}
