package com.erdos.coal.park.api.driver.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import dev.morphia.annotations.Entity;

@Entity(value = "t_driver_to_car", noClassnameStored = true)
public class DriverToCar extends BaseMongoInfo {
    private String driverId;
    private String mobile;  //司机手机号
    private String name;    //司机姓名
    private String carId;
    private String carNum;
    private String driverCarPho;    //司机和车辆的合影
    private Integer delete; //0-司机关联车辆,1-司机取消关联车辆,2-平台限制司机使用车辆

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public String getCarId() {
        return carId;
    }

    public void setCarId(String carId) {
        this.carId = carId;
    }

    public String getDriverCarPho() {
        return driverCarPho;
    }

    public void setDriverCarPho(String driverCarPho) {
        this.driverCarPho = driverCarPho;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    public String getMobile() {
        return mobile;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
