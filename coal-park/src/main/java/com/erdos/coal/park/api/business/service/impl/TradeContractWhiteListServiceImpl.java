package com.erdos.coal.park.api.business.service.impl;

import com.erdos.coal.alibaba.sms.bean.SmsResult;
import com.erdos.coal.alibaba.sms.service.SMSService;
import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.park.api.business.dao.ITradeContractDao;
import com.erdos.coal.park.api.business.dao.ITradeContractWhiteListDao;
import com.erdos.coal.park.api.business.entity.TradeContract;
import com.erdos.coal.park.api.business.entity.TradeContractWhiteList;
import com.erdos.coal.park.api.business.entity.TradePrice;
import com.erdos.coal.park.api.business.service.ITradeContractWhiteListService;
import com.erdos.coal.park.api.manage.dao.ISMSResultDao;
import com.erdos.coal.park.api.manage.entity.SMSResult;
import com.erdos.coal.park.web.sys.dao.ISysUnitDao;
import com.erdos.coal.park.web.sys.entity.SysUnit;
import com.erdos.coal.utils.StrUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("tradeContractWhiteListService")
public class TradeContractWhiteListServiceImpl extends BaseMongoServiceImpl<TradeContractWhiteList, ITradeContractWhiteListDao> implements ITradeContractWhiteListService {
    @Resource
    private ISysUnitDao sysUnitDao;
    @Resource
    private ITradeContractDao tradeContractDao;
    @Resource
    private ISMSResultDao ismsResultDao;

    @Override
    @Async("busTaskExecutor")   //异步执行和业务系统的交互
    public void sendTradeSms(List<TradePrice> addList, Map<Integer, TradePrice> editMap) {
        Map<String, String> map = new HashMap<>();

        List<String> bizContractCodeList = new ArrayList<>();

        // 遍历新增的 交易价格 数据集合
        for (TradePrice tradePrice : addList) {
            if (bizContractCodeList.size() == 0 || !bizContractCodeList.contains(tradePrice.getBizContractCode())) {
                TradeContractWhiteList contractWhiteList = this.get("bizContractCode", tradePrice.getBizContractCode());
                if (contractWhiteList != null) {
                    SysUnit sysUnit = sysUnitDao.get("code", tradePrice.getDefaultDownUnit());
                    map.put(contractWhiteList.getBizContractName(), sysUnit.getName());
                } else {
                    TradeContractWhiteList varietyWhiteList = this.get("variety", tradePrice.getVariety());
                    if (varietyWhiteList != null) {
                        SysUnit sysUnit = sysUnitDao.get("code", tradePrice.getDefaultDownUnit());
                        TradeContract tradeContract = tradeContractDao.get("bizContractCode", tradePrice.getBizContractCode());
                        map.put(tradeContract.getBizContractName(), sysUnit.getName());
                    }
                }
            }
        }
        // 遍历修改的 交易价格 数据集合
        for (Map.Entry<Integer, TradePrice> entry : editMap.entrySet()) {
            TradePrice tradePrice = entry.getValue();
            if (bizContractCodeList.size() == 0 || !bizContractCodeList.contains(tradePrice.getBizContractCode())) {
                TradeContractWhiteList contractWhiteList = this.get("bizContractCode", tradePrice.getBizContractCode());
                if (contractWhiteList != null) {
                    SysUnit sysUnit = sysUnitDao.get("code", tradePrice.getDefaultDownUnit());
                    map.put(contractWhiteList.getBizContractName(), sysUnit.getName());
                } else {
                    TradeContractWhiteList varietyWhiteList = this.get("variety", tradePrice.getVariety());
                    if (varietyWhiteList != null) {
                        SysUnit sysUnit = sysUnitDao.get("code", tradePrice.getDefaultDownUnit());
                        TradeContract tradeContract = tradeContractDao.get("bizContractCode", tradePrice.getBizContractCode());
                        map.put(tradeContract.getBizContractName(), sysUnit.getName());
                    }
                }
            }
        }

        for (Map.Entry<String, String> entry : map.entrySet()) {
            SmsResult sendResult = SMSService.sendTradeNoticeSMS(entry.getValue(), entry.getKey() + "合同");

            if (StrUtil.isEmpty(sendResult.getCode()) || !"OK".equals(sendResult.getCode())) {
                SMSResult smsResult = new SMSResult();
                BeanUtils.copyProperties(sendResult, smsResult);
                ismsResultDao.save(smsResult);
            }

            SmsResult sendResultYD = SMSService.sendTradeNoticeSMSYD(entry.getValue(), entry.getKey() + "合同");

            if (StrUtil.isEmpty(sendResultYD.getCode()) || !"OK".equals(sendResultYD.getCode())) {
                SMSResult smsResult = new SMSResult();
                BeanUtils.copyProperties(sendResultYD, smsResult);
                ismsResultDao.save(smsResult);
            }
        }
    }
}
