package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.service.IWebDriverGroupService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/customer/driverGroup")
public class WebDriverGroupController extends BaseController {

    @Resource
    private IWebDriverGroupService webDriverGroupService;

    @PostMapping("/group")
    public ServerResponse<EGridResult> customerHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webDriverGroupService.group(page, rows));
    }

    @PostMapping("/driver")
    public ServerResponse<EGridResult> driverHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webDriverGroupService.driver(page, rows));
    }
}
