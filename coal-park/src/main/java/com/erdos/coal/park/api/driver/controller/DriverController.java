package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.pojo.CarData;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by LIGX on 2018/12/27.
 * 司机端接口
 */
//"司机APP业务接口列表"
@RestController
@RequestMapping("/api/dvr")
public class DriverController extends BaseController {

    @Resource
    private IDriverInfoService driverInfoService;

    @InvokeLog(description = "完善司机信息 接口") //日志
    @PostMapping(value = "/update_dvr_info")
    public ServerResponse<String> updateDriverInfoHandler(
            @RequestParam(value = "driverPho", required = false) MultipartFile driverPho,      //"驾驶证照片"
            @RequestParam(value = "driverPho2", required = false) MultipartFile driverPho2,    //"驾驶证照片副页"
            @RequestParam(value = "driIdentityPhoBef", required = false) MultipartFile driIdentityPhoBef,//"司机身份证照片正面"
            @RequestParam(value = "driIdentityPhoBack", required = false) MultipartFile driIdentityPhoBack,//"司机身份证照片反面"
            @RequestParam(value = "driverCarPho", required = false) MultipartFile driverCarPho,    //"司机和车的合影照片"
            @RequestParam(value = "roadQCPho", required = false) MultipartFile roadQCPho,      //"道路从业资格证照片"
            @RequestParam(value = "bankCardPho", required = false) MultipartFile bankCardPho   //"银行卡照片"
    ) throws GlobalException {
        return ServerResponse.createError("因小程序只能单张上传照片，接口停用，功能实现请使用 /api/dvr/wx/upload_pho 和 /api/dvr/wx/update_dvr_info");
        //return driverInfoService.updateDvrInfo(driverPho, driverPho2, driIdentityPhoBef, driIdentityPhoBack, driverCarPho, roadQCPho, bankCardPho);
    }

    @InvokeLog(description = "查询司机信息 接口", printReturn = false) //日志
    @PostMapping(value = "/selectOne_dvr_info")
    public ServerResponse<DriverInfo> selectOneDriverInfoHandler() throws GlobalException {
        return driverInfoService.selectOne();
    }

    @InvokeLog(description = "获取总开关", printReturn = false)   //日志
    @PostMapping(value = "/get_dvr_switch")
    public ServerResponse<Map<String, String>> getSwitchHandler() throws GlobalException {
        return driverInfoService.getSwitch();
    }

    @InvokeLog(description = "查询车辆需上传照片数量 接口", printReturn = false)   //日志
    @PostMapping(value = "/get_car_switch")
    public ServerResponse<Map<String, String>> getCarSwitchHandler() throws GlobalException {
        return driverInfoService.getCarSwitch();
    }

    @InvokeLog(description = "保存司机车辆信息 接口") //日志
    @PostMapping(value = "/save_driver_car")
    public ServerResponse<String> saveDriverCarHandler(
            @RequestParam(value = "carNum") String carNum,             //"车牌号"
            @RequestParam(value = "carInfoId") String carInfoId,       //"车型"
            @RequestParam(value = "drivingPho1", required = false) MultipartFile drivingPho1,//"行驶证照片（正页）"
            @RequestParam(value = "drivingPho2", required = false) MultipartFile drivingPho2,//"行驶证照片（副页正面）"
            @RequestParam(value = "drivingPho3", required = false) MultipartFile drivingPho3,//"行驶证照片（副页反面）"
            @RequestParam(value = "roadTCPho", required = false) MultipartFile roadTCPho,    //"车辆道路运输证"
            @RequestParam(value = "carIdentityPhoBef", required = false) MultipartFile carIdentityPhoBef,  //"车主身份证正（单位证件）"
            @RequestParam(value = "carIdentityPhoBack", required = false) MultipartFile carIdentityPhoBack,//"车主身份证反（单位证件）"
            @RequestParam(value = "driverCarPho", required = false) MultipartFile driverCarPho //"司机和车的合影照片"
    ) throws GlobalException {
        return ServerResponse.createError("因小程序只能单张上传照片，接口停用，功能实现请使用 /api/dvr/wx/upload_pho 和 /api/dvr/wx/save_dvr_car");
        //return driverInfoService.saveDriverCar(carNum, carInfoId, drivingPho1, drivingPho2, drivingPho3, roadTCPho, carIdentityPhoBef, carIdentityPhoBack, driverCarPho);
    }

    @InvokeLog(description = "查询司机关联车辆信息 接口") //日志
    @PostMapping(value = "/get_driver_cars")
    public ServerResponse<List<CarData>> getDriverCarsHandler() throws GlobalException {
        return driverInfoService.getDriverCars();
    }

    @InvokeLog(description = "司机取消车辆关联 接口") //日志
    @PostMapping(value = "/cancel_to_car")
    public ServerResponse<String> cancelToCarHandler(
            @RequestParam(value = "id") String id  //"关联车辆列表编号"
    ) throws GlobalException {
        return driverInfoService.cancelToCar(id);
    }

    @InvokeLog(description = "司机确定正在使用车辆车牌 接口") //日志
    @PostMapping(value = "/chose_car")
    public ServerResponse<String> choseCarHandler(
            @RequestParam(value = "carNum") String carNum  //"车牌号"
    ) throws GlobalException {
        return driverInfoService.choseCar(carNum);
    }

    @InvokeLog(description = "司机确定正在使用车辆车牌 接口") //日志
    @PostMapping(value = "/chose_car2")
    public ServerResponse<String> choseCar2Handler(
            @RequestParam(value = "carNum") String carNum  //"车牌号"
    ) throws GlobalException {
        return driverInfoService.choseCar2(carNum);
    }

    @InvokeLog(description = "清空设置的正在使用车牌号 接口") //日志
    @PostMapping(value = "/clean_carnum")
    public ServerResponse<String> cleanCarNumHandler() throws GlobalException {
        return driverInfoService.cleanCarNum();
    }

    @InvokeLog(description = "智运通司机查询车牌列表接口") //日志
    @PostMapping(value = "/zyt_search_carNums")
    public ServerResponse<List<String>> zytSearchCarNumsHandler() throws GlobalException {
        return driverInfoService.zytSearchDvrCar();
    }

    @InvokeLog(description = "智运通司机查询状态接口") //日志
    @PostMapping(value = "/zyt_search_status")
    public ServerResponse<List<String>> zytSearchStatusHandler() throws GlobalException {
        return driverInfoService.zytSearchDvrCar();
    }

    @InvokeLog(description = "司机补充修改关联车辆皮重信息 接口") //日志
    @PostMapping(value = "/update_tare_weight")
    public ServerResponse<String> updateTareWeightHandler(
            @RequestParam(value = "id") String id,  //"关联车辆列表编号"
            @RequestParam(value = "tareWeight") Double tareWeight  //"皮重"
    ) throws GlobalException {
        return driverInfoService.updateTareWeight(id, tareWeight);
    }
}