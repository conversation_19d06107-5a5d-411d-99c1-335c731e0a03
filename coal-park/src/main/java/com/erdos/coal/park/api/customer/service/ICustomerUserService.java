package com.erdos.coal.park.api.customer.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.CustomerUser;

import java.util.List;

public interface ICustomerUserService extends IBaseMongoService<CustomerUser> {

    //查询接口
    ServerResponse<CustomerUser> getCustomerUser();

    //注册接口
    ServerResponse<AccessToken> customReg(String phoneId, String mobile, String password, String code);

    ServerResponse<AccessToken> customReg2(String phoneId, String mobile, String password, String code);

    //登录接口
    ServerResponse<AccessToken> customLogin(String phoneId, String mobile, String password, String code);

    ServerResponse<AccessToken> customLogin2(String phoneId, String mobile, String password, String code);

    // 检查司机手机号是否更换微信登录
    ServerResponse<Boolean> checkPoneId(String mobile, String phoneId);

    //1.按手机号 或 用户名查询用户
    CustomerUser getCUByMobile(String mobile, String password);

    //修改密码接口
    ServerResponse<AccessToken> editPwd(String oldPwd, String newPwd, String confirmPwd);

    //短信验证码重置密码接口
    ServerResponse<AccessToken> resetPwd(String username, String code, String newPwd, String confirmPwd);

    //查询交易密码
    ServerResponse<Integer> checkBarPwd();

    //设置交易密码
    ServerResponse<String> setBarPwd(String barPwd, String confirmPwd);

    //重置交易密码
    ServerResponse<String> updateBarPwd(String oldBarPwd, String newBarPwd, String confirmPwd);

    //短信验证码修改交易密码
    ServerResponse<String> smsUpBarPwd(String smsCode, String barPwd, String confirmPwd);

    //验证交易密码是否正确
    ServerResponse<String> checkBarPwd(String barPwd);

    //设置默认金额
    ServerResponse<String> setCustomerFee(double fee);

    //查询默认金额
    ServerResponse<String> getCustomerFee();

    //修改手机号
    ServerResponse<String> updateMobile(String mobile, String code);

    //保存客商姓名
    ServerResponse<String> updateName(String name);

    //返回当前客商用户权限列表
    List<String> findPermissions(String name);

}
