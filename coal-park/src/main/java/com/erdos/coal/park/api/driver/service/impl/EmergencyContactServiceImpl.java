package com.erdos.coal.park.api.driver.service.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoServiceImpl;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.park.api.driver.dao.IEmergencyContactDao;
import com.erdos.coal.park.api.driver.entity.EmergencyContact;
import com.erdos.coal.park.api.driver.service.IEmergencyContactService;
import com.erdos.coal.park.api.manage.entity.SMS;
import com.erdos.coal.park.api.manage.service.ISMSService;
import com.erdos.coal.utils.ObjectUtil;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("emergencyContactService")
public class EmergencyContactServiceImpl extends BaseMongoServiceImpl<EmergencyContact, IEmergencyContactDao> implements IEmergencyContactService {

    @Resource
    private ISMSService smsService;

    public ServerResponse<String> saveEmergencyContact(String name, String mobile, String code) {
        String did = ShiroUtils.getUserId();
        //TODO: 1, 验证码校验
        SMS sms = smsService.get("mobile", mobile);
        if (sms == null) {
            return ServerResponse.createError("请先获取验证码");
        }
        if (!sms.getCode().equals(code)) {
            return ServerResponse.createError("验证码错误");
        }

        //TODO: 2, 保存应急联系人
        EmergencyContact emeCon = this.get("did", did);
        if (ObjectUtil.isEmpty(emeCon)) {
            EmergencyContact emergencyContact = new EmergencyContact();
            emergencyContact.setName(name);
            emergencyContact.setMobile(mobile);
            emergencyContact.setDid(did);
            this.save(emergencyContact);
        } else {
            UpdateOperations<EmergencyContact> updateOperations = this.createUpdateOperations();
            updateOperations.set("mobile", mobile);
            updateOperations.set("name", name);
            this.update(this.createQuery().filter("_id", emeCon.getObjectId()), updateOperations);
        }

        return ServerResponse.createSuccess("设置成功");

    }

}
