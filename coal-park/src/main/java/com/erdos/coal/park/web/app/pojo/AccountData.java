package com.erdos.coal.park.web.app.pojo;

import java.math.BigDecimal;

public class AccountData {
    private String id; // 单位编码或客商编码 或司机编码
    private String mobile;  // 客商手机号 或司机手机号
    private String name;    // 单位名称 或客商姓名或司机姓名
    private BigDecimal totalFee = new BigDecimal("0");   //金额(元)
    private String createTime;    // 创建日期
    private String oid;     // 订单号
    private String gid;     // 货运信息编号
    private Integer type;   // 0.代付货运信息费扣款 1.多代付的订单退款 2.已代付的订单退款 3.平台代客商/单位 代扣 司机/客商的服务费（接单时需要支付给客商/单位 的钱）4.退回司机/客商信息费
    private Integer cusType;
    private Integer dvrType;
    private Integer wxResultType;   //0-客商自己充值，1-客商给司机充值，2-司机充值，3-司机微信支付接单（订单含三方费用）,4-司机接单失败退款（订单含三方费用）,5-客商提现，6司机提现，7-三方提现，8-分账给三方,9-司机接单时直接微信零钱转三方

    private String transactionId;   //微信支付订单号,微信平台唯一单号
    private String outTradeNo;      //商户订单号,商户平台支付唯一单号
    private String outRefundNo;     //商户退款单号,商户平台退款唯一单号
    private String paymentNo;       //商户付款单号,商户平台企业转零钱唯一单号
    private String outOrderNo;      //商户分账单号，商户平台分账唯一单号
    private String resultCode;  //业务结果
    private String errCodeDes;  //错误代码描述

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getCusType() {
        return cusType;
    }

    public void setCusType(Integer cusType) {
        this.cusType = cusType;
    }

    public Integer getDvrType() {
        return dvrType;
    }

    public void setDvrType(Integer dvrType) {
        this.dvrType = dvrType;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getPaymentNo() {
        return paymentNo;
    }

    public void setPaymentNo(String paymentNo) {
        this.paymentNo = paymentNo;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public Integer getWxResultType() {
        return wxResultType;
    }

    public void setWxResultType(Integer wxResultType) {
        this.wxResultType = wxResultType;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes;
    }
}
