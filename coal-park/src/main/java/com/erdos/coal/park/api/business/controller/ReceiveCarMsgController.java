package com.erdos.coal.park.api.business.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.business.pojo.BizOrderData;
import com.erdos.coal.park.api.business.service.IReceiveCarMsgService;
import com.erdos.coal.park.api.driver.entity.QuarantineInfo;
import com.erdos.coal.park.api.driver.pojo.CoalTicket;
import com.erdos.coal.park.api.driver.service.IQuarantineInfoService;
import com.erdos.coal.utils.IdUnit;
import com.erdos.coal.utils.ObjectUtil;
import com.erdos.coal.utils.StrUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//"企业扫描司机订单二维码并请求平台相关信息"
@RestController
@RequestMapping("/api/bus/receive_car_msg")
public class ReceiveCarMsgController extends BaseController {
    @Resource
    private IReceiveCarMsgService receiveCarMsgService;
    @Resource
    private IQuarantineInfoService quarantineInfoService;

    @InvokeLog(description = "扫描司机二维码到平台查询订单信息 接口") //日志
    @PostMapping(value = "/search_same_order")
    public ServerResponse<BizOrderData> searchSameOrderHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");

        String oid = (String) data.get("oid");          //订单编号
        String unitCode = (String) data.get("unitCode");    //单位编码
        String subCode = (String) data.get("subCode");    //二级单位编码

        return receiveCarMsgService.searchOrder(oid, unitCode, subCode, false);
    }

    @InvokeLog(description = "扫描司机二维码到平台查询订单信息 接口") //日志
    @PostMapping(value = "/search_order")
    public ServerResponse<BizOrderData> searchOrderHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        /*if (e.equals("e"))
            throw new GlobalException("-1", "globalException: e is null");
        return ServerResponse.createSuccess("api main");*/

        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        //if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");
        String md5Oid = (String) data.get("oid");          //订单编号
        String unitCode = (String) data.get("unitCode");    //单位编码
        String subCode = (String) data.get("subCode");    //二级单位编码

        return receiveCarMsgService.searchOrder(md5Oid, unitCode, subCode, true);
    }

    @InvokeLog(description = "扫描司机二维码到平台修改订单检票状态 接口") //日志
    @PostMapping(value = "/update_order")
    public ServerResponse<Object> updateOrderHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkUpdateOrderCheckingSign(data)) return ServerResponse.createError("验签失败！");

        Map<String, Object> id = new HashMap<>();
        id.put("minInId", data.get("minInId"));
        id.put("maxInId", data.get("maxInId"));
        id.put("minOutId", data.get("minOutId"));
        id.put("maxOutId", data.get("maxOutId"));

        JSONArray inArray = data.getJSONArray("inData");
        JSONArray outArray = data.getJSONArray("outData");

        return receiveCarMsgService.checkingOrder(id, inArray, outArray);
    }

    @InvokeLog(description = "扫描司机二维码到平台修改订单(一单)检票状态 接口") //日志
    @PostMapping(value = "/update_one_order2")
    public ServerResponse<String> updateOneOrder2Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        return ServerResponse.createError("接口停用！");

        /*if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String billCode = String.valueOf(data.get("billCode"));
        Integer bizType = (Integer) data.get("bizType");
        Integer checking = (Integer) data.get("checking");
        String grossWeight = String.valueOf(data.get("grossWeight"));
        String tareWeight = String.valueOf(data.get("tareWeight"));

        return receiveCarMsgService.checkingOneOrder2(billCode, bizType, checking, grossWeight, tareWeight);*/
    }

    @InvokeLog(description = "扫描司机二维码到平台修改订单(一单)检票状态 接口") //日志
    @PostMapping(value = "/update_one_order3")
    public ServerResponse<String> updateOneOrder3Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        return receiveCarMsgService.checkingOneOrder3(data);
    }

    @InvokeLog(description = "订单   取消预约 接口") //日志
    @PostMapping(value = "/cancel_appointment")
    public ServerResponse<String> cancelAppointmentHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String billCode = String.valueOf(data.get("billCode"));
        return receiveCarMsgService.cancelAppointmentOrCheckin(billCode, 0);
    }

    @InvokeLog(description = "订单   取消签到 接口") //日志
    @PostMapping(value = "/cancel_checkin")
    public ServerResponse<String> cancelCheckinHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String billCode = String.valueOf(data.get("billCode"));
        return receiveCarMsgService.cancelAppointmentOrCheckin(billCode, 1);
    }

    @InvokeLog(description = "订单 查询防疫申报信息 接口") //日志
    @PostMapping(value = "/search_quarantine")
    public ServerResponse<List<QuarantineInfo>> searchQuarantineHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
         /*if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String subCode = String.valueOf(data.get("subCode"));
        Integer bizType = (Integer) data.get("bizType");        //0入库，1出库
        Integer page = data.getInteger("page");            //"第几页"
        Integer rows = data.getInteger("rows");            //"每页多少条"

        return receiveCarMsgService.searchQuarantineInfosBySubCode(subCode, bizType, page, rows);*/
    }

    @InvokeLog(description = "订单 查询防疫申报信息 接口") //日志
    @PostMapping(value = "/search_quarantine2")
//    public ServerResponse<Map<String,Object>> searchQuarantineHandler(
    public ServerResponse<EGridResult> searchQuarantine2Handler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        return ServerResponse.createError("接口停用");
        /*if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String subCode = String.valueOf(data.get("subCode"));
        Integer bizType = (Integer) data.get("bizType");        //0入库，1出库
        Integer page = data.getInteger("page");            //"第几页"
        Integer rows = data.getInteger("rows");            //"每页多少条"

        String mobile = data.getString("mobile");

        String date = data.getString("date");
        Integer quaType = data.getInteger("quaType");   //审核结果 0-未审核，1-自动审核通过，2-自动审核未通过，3-人工审核通过，4-人工审核未通过
        String unitCode = data.getString("unitCode");
        return receiveCarMsgService.searchQuarantineInfos2BySubCode(unitCode, subCode, bizType, page, rows, mobile, date, quaType);*/
    }

    @InvokeLog(description = "订单 修改防疫申报审核 接口") //日志
    @PostMapping(value = "/audit_quarantine")
    public ServerResponse<String> auditQuarantineHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String id = String.valueOf(data.get("id"));
        Integer audit = (Integer) data.get("audit");
        String auditDes = String.valueOf(data.get("auditDes"));

//        return receiveCarMsgService.auditQuarantine(id, audit, auditDes);
        return receiveCarMsgService.auditQuarantine2(id, audit, auditDes);
    }

    @InvokeLog(description = "企业 提交终端识别后的防疫信息 接口") //日志
    @PostMapping(value = "/syn_quarantine")
    public ServerResponse<String> synQuarantineHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
//        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        //终端设备自带信息
        //Integer credentialType = data.getInteger("credentialType");             //凭证类型
        //Integer dangerType = data.getInteger("dangerType");                     //
        //String deviceName = data.getString("deviceName");                       //
        String deviceId = data.getString("deviceId");                           //设备编号
        String deviceNo = data.getString("deviceNo");                           //设备序号
        //身份证信息
        //String address = data.getJSONObject("ext").getString("address");        //身份证家庭住址
        //String birthDate = data.getJSONObject("ext").getString("birthDate");    //身份证生日
        //String gender = data.getJSONObject("ext").getString("gender");          //身份证性别
        //String issuer = data.getJSONObject("ext").getString("issuer");          //身份证签发机关
        //String idCardName = data.getJSONObject("ext").getString("name");        //姓名
        //String nation = data.getJSONObject("ext").getString("nation");          //身份证民族
        //String scope = data.getJSONObject("ext").getString("scope");            //身份证有效期限
        //终端设备查询识别到到防疫信息
        //boolean hasOcclusion = data.getBoolean("hasOcclusion");                 //
        String healthCodeLevel = data.getString("healthCodeLevel");             //健康码等级
        //String idcard = data.getString("idcard");                               //
        String idcardNum = data.getString("idcardNum");                         //身份证号
        //Integer jkbStatus = data.getInteger("jkbStatus");                       //
        //String mobile = data.getString("mobile");                               //手机号
        String name = data.getString("name");                                   //姓名
        String nucleate = data.getString("nucleate");                           //核酸检测 时间、地点、结果 信息
        //Integer openMode = data.getInteger("openMode");                         //
        //String recordTime = data.getString("recordTime");                       //
        //String skmStatus = data.getString("skmStatus");                         //
        //Integer staffId = data.getInteger("staffId");                           //员工工号
        Double temperature = data.getDouble("temperature");                     //体温
        //String trip = data.getString("trip");
        //终端设备设置到二级单位信息
        String id = data.getJSONObject("xs").getString("id");
        //String subName = data.getJSONObject("xs").getString("name");
        String addr = data.getJSONObject("xs").getString("addr");
        //String gate = data.getJSONObject("xs").getString("gate");
        
        /*json.put("rtnCode",0);
            json.put("errorMsg","接收成功");
        }else{
            json.put("rtnCode",1);
            json.put("errorMsg","接收失败");
        */
        if (!healthCodeLevel.equals("1")) return ServerResponse.createError("健康码异常");
        if (temperature > 37.5) return ServerResponse.createError("体温异常");

//        return quarantineInfoService.synQuarantine(id, addr, deviceNo, name, idcardNum, healthCodeLevel, nucleate, temperature);
        return quarantineInfoService.synQuarantine2(id, addr, deviceNo, name, idcardNum, healthCodeLevel, nucleate, temperature);
    }

    @InvokeLog(description = "企业 提交电子煤票信息 接口") //日志
    @PostMapping(value = "/put_coal_ticket")
    public ServerResponse<String> putCoalTicketHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");

        CoalTicket coalTicket = new CoalTicket();

        String billCode = data.getString("billCode");             //订单编号
        coalTicket.setTicketNo(data.getString("ticketNo"));       //煤票编号
        coalTicket.setTicketType(data.getString("ticketType"));   //煤票类型
        coalTicket.setOutSubName(data.getString("outSubName"));   //煤票发货单位
        coalTicket.setInSubName(data.getString("inSubName"));     //煤票收货单位
        coalTicket.setArea(data.getString("area"));               //煤票运往地区
        coalTicket.setPlace(data.getString("place"));             //煤票运往地点
        coalTicket.setoPlace(data.getString("oPlace"));           //煤票运往关联地点
        coalTicket.setCoalType(data.getString("coalType"));       //煤票煤炭种类
        coalTicket.setProductName(data.getString("productName")); //煤票煤炭品种
        coalTicket.setVariety(data.getString("variety"));         //煤票内部品种
        coalTicket.setCarNum(data.getString("carNum"));           //煤票车号
        coalTicket.setGrossWeight(data.getString("grossWeight")); //煤票毛重
        coalTicket.setTareWeight(data.getString("tareWeight"));   //煤票皮重
        coalTicket.setNetWeight(data.getString("netWeight"));     //煤票净重
        coalTicket.setPrice(data.getString("price"));             //煤票单价
        coalTicket.setHeatValue(data.getString("heatValue"));     //煤票热值
        coalTicket.setPurpose(data.getString("purpose"));         //煤票用途
        coalTicket.setCleanWarehouse(data.getString("cleanWarehouse"));//煤票清库
        coalTicket.setSupplyGua(data.getString("supplyGua"));     //煤票保供
        coalTicket.setTicketSign(data.getString("ticketSign"));   //煤票签名
        coalTicket.setBarCode(data.getString("barCode"));         //煤票条形码图（暂不处理）
        coalTicket.setDimBarCode(data.getString("dimBarCode"));   //煤票二维码图（暂不处理）
        coalTicket.setBillingTime(data.getString("billingTime")); //煤票开票时间
        coalTicket.setBillingClerk(data.getString("billingClerk"));//煤票开票员

        return receiveCarMsgService.putCoalTicket(billCode, coalTicket);
    }

    @InvokeLog(description = "订单销售信息统计 接口") //日志
    @PostMapping(value = "/order_statistics")
    public ServerResponse<EGridResult> orderStatisticsHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data)) return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String unitCode = data.getString("unitCode");
        String subCode = data.getString("subCode");
        Integer page = data.getInteger("page");            //"第几页"
        Integer rows = data.getInteger("rows");            //"每页多少条"
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        return receiveCarMsgService.orderStatistics(unitCode, subCode, startTime, endTime, page, rows);
    }

    @InvokeLog(description = "司机身份证号查询当前订单billCode 接口") //日志
    @PostMapping(value = "/search_bill_code_by_identity3")
    public ServerResponse<String> searchBillCodeByIdentityHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");

        String identity = (String) data.get("identity");  //身份证号
        String unitCode = (String) data.get("unitCode");  //一级单位编码
        String subCode = (String) data.get("subCode");    //二级单位编码

        return receiveCarMsgService.searchBillCodeByIdentity(identity, unitCode, subCode);
    }

    @InvokeLog(description = "企业按deviceCode修改deviceId 接口") //日志
    @PostMapping(value = "/update_device_id")
    public ServerResponse<String> updateDeviceIdHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");

        String deviceCode = (String) data.get("deviceCode");  //设备编码

        return receiveCarMsgService.udpateDeviceIdByDeviceCode(deviceCode);
    }

    @InvokeLog(description = "订单离场 接口") //日志
    @PostMapping(value = "/update_order_exit")
    public ServerResponse<String> updateOrderExitHandler(
            @RequestBody JSONObject data
    ) throws GlobalException {
        if (ObjectUtil.isEmpty(data))
            return ServerResponse.createError("参数为空");
        if (!IdUnit.checkSign(data)) return ServerResponse.createError("验签失败！");

        String billCode = data.getString("billCode");
        Integer bizType = data.getInteger("bizType");
        if (StrUtil.isEmpty(billCode)) return ServerResponse.createError("参数billCode不能为空");
        if (StrUtil.isEmpty(bizType)) return ServerResponse.createError("参数bizType不能为空");
        return receiveCarMsgService.updateOrderExit(billCode, bizType);
    }
}
