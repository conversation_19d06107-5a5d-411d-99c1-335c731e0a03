package com.erdos.coal.park.web;

import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.config.OcrConfig;
import com.erdos.coal.ocr.IRecognitionHandler;
import com.erdos.coal.ocr.entity.HealthCode;
import com.erdos.coal.ocr.entity.IDCard;
import com.erdos.coal.ocr.entity.TravelCard;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryPoolMXBean;
import java.util.ArrayList;
import java.util.List;

@Service
public class TestService {

    private Logger logger = LoggerFactory.getLogger(TestService.class);

    @Value("${spring.application.name}")
    private String appName;

    @Resource
    private OcrConfig ocrConfig;

    @Resource
    private ServletWebServerApplicationContext applicationContext;

    @Resource
    private IRecognitionHandler<IDCard> idCardService;
    @Resource
    private IRecognitionHandler<HealthCode> healthCodeService;
    @Resource
    private IRecognitionHandler<TravelCard> travelCardService;

    public IDCard ocrIDCardTest(InputStream src) {
        long start = System.currentTimeMillis();
        logger.info("开始");

        try {
            IDCard idCard = idCardService.getInfo(ImageIO.read(src));

            logger.info(idCard.toString());
            logger.info("共耗时：" + (System.currentTimeMillis() - start));

            return idCard;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public HealthCode ocrHealthCodeTest(InputStream src) {
        long start = System.currentTimeMillis();
        logger.info("开始");

        try {
            HealthCode healthCode = healthCodeService.getInfo(ImageIO.read(src));

            logger.info(healthCode.toString());
            logger.info("共耗时：" + (System.currentTimeMillis() - start));

            return healthCode;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public TravelCard ocrTravelCardTest(InputStream src) {
        long start = System.currentTimeMillis();
        logger.info("开始");

        try {
            TravelCard travelCard = travelCardService.getInfo(ImageIO.read(src));

            logger.info(travelCard.toString());
            logger.info("共耗时：" + (System.currentTimeMillis() - start));

            return travelCard;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Async("busTaskExecutor")
    public void doAsyncTest() {
        try {
            //for (int i = 0; i < 1000; i++) {
            try {
                BufferedImage src = ImageIO.read(new File(ocrConfig.testFilePath + "healthcode.jpg"));
                HealthCode healthCode = healthCodeService.getInfo(src);

                logger.info(healthCode.toString());

            } catch (Exception e) {
                e.printStackTrace();
            }
            //}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String forOptimization() {
        JSONObject json = new JSONObject();
        try {
            json.put("name", appName); // COAL-PARK1

            JSONObject tmtObj = new JSONObject();
            // org.apache.tomcat.util.threads.ThreadPoolExecutor@3c77ed34[Running, pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 2]
            TomcatWebServer webServer = (TomcatWebServer) applicationContext.getWebServer();
            ThreadPoolExecutor executor = (ThreadPoolExecutor) webServer.getTomcat().getConnector().getProtocolHandler().getExecutor();
            tmtObj.put("poolSize", executor.getPoolSize());
            tmtObj.put("activeThreads", executor.getActiveCount());
            tmtObj.put("queuedTasks", executor.getQueue().size());
            tmtObj.put("completedTasks", executor.getCompletedTaskCount());
            json.put("thread", tmtObj);

            List<String> list = new ArrayList<>();
            for (MemoryPoolMXBean memoryMXBean : ManagementFactory.getMemoryPoolMXBeans()) {
                list.add(memoryMXBean.getName() + " : " + memoryMXBean.getUsage().getUsed() / 1024 / 1024 + " mb");
            }
            json.put("memory", list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return json.toJSONString();
    }
}
