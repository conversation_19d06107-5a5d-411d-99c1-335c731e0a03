package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.customer.entity.CustomerUser;

import java.math.BigDecimal;
import java.util.List;

public interface IWebCustomerService extends IBaseMongoService<CustomerUser> {
    //客商信息列表
    EGridResult loadGrid(Integer page, Integer rows);

    // 修改客商信息（状态state 是否停用）
    ServerResponse<String> editUserState(CustomerUser cUser);

    //客商账户信息列表
    EGridResult loadAccountGrid(Integer page, Integer rows);

    //客商账户详情
    EGridResult accEdit(Integer page, Integer rows);

    //账户剩余金额
    BigDecimal getAvailableFee();

    //设置用户账号为三方账户
    ServerResponse<String> toThirdParty(CustomerUser cUser);

    //手机号模糊查询用户
    ServerResponse<List<CustomerUser>> searchByMobile();
}
