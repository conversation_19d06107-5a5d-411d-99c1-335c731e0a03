package com.erdos.coal.park.api.driver.pojo;

import java.io.Serializable;
import java.util.Date;

//电子磅单
public class PoundList implements Serializable {
    //电子磅单
    private String outSubName;   //供货单位
    private String billCode;     //票号
    private String carNum;       //车号
    //private String variety;      //煤种
    // 发货 或 收货 单位 checking到3-检票时，会有毛重和皮重参数要记录到订单中
    //private String grossWeight;  //毛重
    //private String tareWeight;   //皮重
    private String netWeight;    //净重
    private String inSubName;    //收货单位
    private Date otherTime;      //检票时间


    //
    //分 发货 和 收货
    //业务票号 公司名称 交易单位 交易单位别名 交易单位显示方式 交易合同 交易合同别名 交易合同显示方式 品种 品种别名 品种显示方式 入场时间 出场时间 入场重量 毛重 皮重 毛重x 皮重x 重量显示方式 过磅员
    private String busCode;//业务票号
    private String companyName;// 公司名称
    private String tradingUnit;// 交易单位
    private String tUnit;// 交易单位别名
    private String tdDisplayMold;// 交易单位显示方式
    private String bizContractCode;// 交易合同
    private String bizContractName;// 交易合同别名
    private String bizCDisplayMold;// 交易合同显示方式
    private String variety;// 品种
    private String varietyName;// 品种别名
    private String vDisplayMold;// 品种显示方式
    private String entranceTime; // 入场时间
    private String exitTime;// 出场时间
    private String InitialWeight;// 入场重量
    private String grossWeight;// 毛重
    private String tareWeight;// 皮重
    private String grossWeightX;// 毛重x
    private String tareWeightX;// 皮重x
    private String weightDisplayMold;// 重量显示方式
    private String weigher;// 过磅员


    public PoundList() {
    }

    public PoundList(String outSubName, String billCode, String carNum, String variety, String grossWeight, String tareWeight, String inSubName, Date otherTime) {
        this.outSubName = outSubName;
        this.billCode = billCode;
        this.carNum = carNum;
        this.variety = variety;
        this.grossWeight = grossWeight;
        this.tareWeight = tareWeight;
        this.netWeight = String.valueOf(Double.valueOf(grossWeight) - Double.valueOf(tareWeight));
        this.inSubName = inSubName;
        this.otherTime = otherTime;
    }

    public PoundList(String outSubName, String billCode, String carNum, String variety, String grossWeight, String tareWeight, String inSubName, Date otherTime, String bizContractName) {
        this.outSubName = outSubName;
        this.billCode = billCode;
        this.carNum = carNum;
        this.variety = variety;
        this.grossWeight = grossWeight;
        this.tareWeight = tareWeight;
        this.netWeight = String.valueOf(Double.valueOf(grossWeight) - Double.valueOf(tareWeight));
        this.inSubName = inSubName;
        this.otherTime = otherTime;
        this.bizContractName = bizContractName;
    }

    public String getOutSubName() {
        return outSubName;
    }

    public void setOutSubName(String outSubName) {
        this.outSubName = outSubName;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getCarNum() {
        return carNum;
    }

    public void setCarNum(String carNum) {
        this.carNum = carNum;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getTareWeight() {
        return tareWeight;
    }

    public void setTareWeight(String tareWeight) {
        this.tareWeight = tareWeight;
    }

    public String getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(String netWeight) {
        this.netWeight = netWeight;
    }

    public String getInSubName() {
        return inSubName;
    }

    public void setInSubName(String inSubName) {
        this.inSubName = inSubName;
    }

    public Date getOtherTime() {
        return otherTime;
    }

    public void setOtherTime(Date otherTime) {
        this.otherTime = otherTime;
    }

    public String getBizContractName() {
        return bizContractName;
    }

    public void setBizContractName(String bizContractName) {
        this.bizContractName = bizContractName;
    }
}
