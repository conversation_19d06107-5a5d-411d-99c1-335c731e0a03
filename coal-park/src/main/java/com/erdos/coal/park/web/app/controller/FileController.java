package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.park.api.driver.entity.DriverInfo;
import com.erdos.coal.park.api.driver.service.IDriverInfoService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/web/file")
public class FileController extends BaseController {

    @Resource
    private IDriverInfoService driverInfoService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public List<DriverInfo> getList() {
        return driverInfoService.list();
    }
}


