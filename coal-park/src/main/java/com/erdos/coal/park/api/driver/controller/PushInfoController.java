package com.erdos.coal.park.api.driver.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.api.customer.service.IDriverGroupPrequelService;
import com.erdos.coal.park.api.driver.entity.PushInfo;
import com.erdos.coal.park.api.driver.pojo.DriverOrderData;
import com.erdos.coal.park.api.driver.service.IOrderRecordService;
import com.erdos.coal.park.api.driver.service.IPushInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by LIGX on 2018/12/27.
 * 司机端接口
 */
//"司机APP消息接口"
@RestController
@RequestMapping("/api/dvr/push")
public class PushInfoController extends BaseController {

    @Resource
    private IPushInfoService pushInfoService;
    @Resource
    private IOrderRecordService orderRecordService;
    @Resource
    private IDriverGroupPrequelService driverGroupPrequelService;

    @InvokeLog(description = "查询司机消息 接口", printReturn = false) //日志
    @PostMapping(value = "/push_list")
    public ServerResponse<List<PushInfo>> pushListHandler(
            @RequestParam(value = "type") Integer type //"消息类型 0:待抢订单 1:派单 2:抢单成功 3：到达目的地 4：扣款及账户金额 5：账户充值 6：撤单"
    ) throws GlobalException {
        //return ServerResponse.createError("接口停用");
        return pushInfoService.pushList(type);
    }

    @InvokeLog(description = "修改司机消息为已读 接口")//日志
    @PostMapping(value = "/push_edit")
    public ServerResponse<String> pushEditHandler(
            @RequestParam(value = "id") String id  //"消息id"
    ) throws GlobalException {
        return pushInfoService.pushEdit(id);
    }

    @InvokeLog(description = "司机查看撤单信息 接口", printReturn = false)//日志
    @PostMapping(value = "/push_query_delete")
    public ServerResponse<DriverOrderData> pushQueryDelete(
            @RequestParam(value = "oid") String oid    //"撤销订单id"
    ) throws GlobalException {
        return pushInfoService.pushQueryDelete(oid);
    }

    @InvokeLog(description = "司机同意撤销订单 接口")//日志
    @PostMapping(value = "/save_delete_order")
    public ServerResponse<String> saveDeleteOrderHandler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用！");
        //return orderRecordService.saveDeleteOrder(oid);
    }

    @InvokeLog(description = "司机同意撤销订单 接口")//日志
    @PostMapping(value = "/save_delete_order2")
    public ServerResponse<String> saveDeleteOrder2Handler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用！");
//        return orderRecordService.saveDeleteOrder2(oid);
    }

    @InvokeLog(description = "司机同意撤销订单 接口")//日志
    @PostMapping(value = "/save_delete_order3")
    public ServerResponse<String> saveDeleteOrder3Handler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return ServerResponse.createError("接口停用！");
//        return orderRecordService.saveDeleteOrder3(oid);
    }

    @InvokeLog(description = "司机同意撤销订单 接口")//日志
    @PostMapping(value = "/save_delete_order4")
    public ServerResponse<String> saveDeleteOrder4Handler(
            @RequestParam(value = "oid") String oid    //"订单编号"
    ) throws GlobalException {
        return orderRecordService.saveDeleteOrder4(oid);
    }

    @InvokeLog(description = "司机同意或拒绝加入客商的司机组 接口")//日志
    @PostMapping(value = "/save_driver_group_invite")
    public ServerResponse<String> saveDvrGroupInviteHandler(
            @RequestParam(value = "groupNo") String groupNo,    //"订单编号"
            @RequestParam(value = "manner") Integer manner       //"司机接受或拒绝，0-接受，1-拒绝"
    ) throws GlobalException {
        return driverGroupPrequelService.saveDvrGroupInvite(groupNo, manner);
    }
}
