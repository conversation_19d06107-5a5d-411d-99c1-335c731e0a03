package com.erdos.coal.park.web.app.controller;

import com.erdos.coal.base.BaseController;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.exception.GlobalException;
import com.erdos.coal.park.web.app.service.IWebOrderService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/web/app/rush")
public class WebRushController extends BaseController {

    @Resource
    private IWebOrderService webOrderService;

    @PostMapping("/listGoods")
    public ServerResponse<EGridResult> listGoodsHandler(Integer page, Integer rows) throws GlobalException {
        return ServerResponse.createSuccess(webOrderService.listGoods(page, rows, "1"));
    }

    @RequestMapping(value = "/option/list", produces = {"application/json;charset=UTF-8"})
    public String optionListHandler() {

        return "{\"total\":5,\"rows\":[\n" +
                "\t{\"id\":\"1\",\"mobile\":\"15712345678\",\"name\":\"煤块\",\"start\":\"呼和浩特\",\"end\":\"乌海市\",\"publish\":\"0\"},\n" +
                "\t{\"id\":\"2\",\"mobile\":\"13412345678\",\"name\":\"乙醇\",\"start\":\"锡林郭勒盟\",\"end\":\"通辽市\",\"publish\":\"0\"},\n" +
                "\t{\"id\":\"3\",\"mobile\":\"15612345678\",\"name\":\"化学材料\",\"start\":\"鄂尔多斯\",\"end\":\"呼和浩特\",\"publish\":\"1\"},\n" +
                "\t{\"id\":\"4\",\"mobile\":\"18312345678\",\"name\":\"石油\",\"start\":\"巴彦淖尔\",\"end\":\"乌兰察布\",\"publish\":\"0\"},\n" +
                "\t{\"id\":\"5\",\"mobile\":\"13812345678\",\"name\":\"煤\",\"start\":\"呼和浩特\",\"end\":\"兴安盟\",\"publish\":\"1\"}\n" +
                "]}";
    }
}
