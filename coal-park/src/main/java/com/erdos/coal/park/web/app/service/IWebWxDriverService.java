package com.erdos.coal.park.web.app.service;

import com.erdos.coal.core.base.mongo.IBaseMongoService;
import com.erdos.coal.core.common.EGridResult;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.park.api.driver.entity.WechatDriverInfo;

public interface IWebWxDriverService extends IBaseMongoService<WechatDriverInfo> {
    EGridResult loadGrid(Integer page, Integer rows);

    ServerResponse saveCheck(WechatDriverInfo wechatDriverInfo);

    ServerResponse editWxDriver();

    //司机关联车辆列表
    EGridResult loadDvrToCarGrid(Integer page, Integer rows);

    //修改司机关联车辆的状态
    ServerResponse<String> updateDvrToCarDel();
}
