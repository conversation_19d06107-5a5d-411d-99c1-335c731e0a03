package com.erdos.coal.park.web.sys.service;

import com.erdos.coal.core.common.AccessToken;
import com.erdos.coal.core.common.ServerResponse;

import java.util.Map;

public interface ISysService {

    //登录系统
    Map<String, Object> login(String uname, String pwd);

    //登录
    ServerResponse<AccessToken> login2(String username, String password);
    ServerResponse<AccessToken> login2(String username, String password, String smsCode, String imgCode);

    //登录系统
    void logout();

}