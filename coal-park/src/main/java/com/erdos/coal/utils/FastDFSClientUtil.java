package com.erdos.coal.utils;

import com.github.tobato.fastdfs.domain.fdfs.MetaData;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.domain.fdfs.ThumbImageConfig;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class FastDFSClientUtil {
    @Autowired
    private static FastFileStorageClient fastFileStorageClient;

    @Autowired
    private static ThumbImageConfig thumbImageConfig;

    //文件图片上传
    public static Map<String, Object> uploadFiled(byte[] bytes, long fileSize, String extension, boolean saveThumb) {
        Map<String, Object> result = new HashMap<>();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        if (saveThumb) {    //是否保存缩略图
            StorePath storePath = fastFileStorageClient.uploadImageAndCrtThumbImage(byteArrayInputStream, fileSize, extension, createMetaData());
            result.put("img", storePath.getFullPath());
            String thumb = thumbImageConfig.getThumbImagePath(storePath.getFullPath());
            result.put("thumb", thumb);
        } else {
            StorePath storePath = fastFileStorageClient.uploadFile(byteArrayInputStream, fileSize, extension, createMetaData());
            result.put("img", storePath);
        }
        System.out.println(result);
        return result;
    }

    private static Set<MetaData> createMetaData() {
        Set<MetaData> metaDataSet = new HashSet<>();
        metaDataSet.add(new MetaData("Author", "lucifer"));
        //metaDataSet.add(new MetaData("CreateDate","2021-06-01"));
        return metaDataSet;
    }
}
