package com.erdos.coal.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.utils.Utils;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

public final class IdUnit {
    //private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    private IdUnit() {
    }

    /**
     * 生成唯一的id编号
     * 规则：yyyyMMddHHmmssSSS + 一位字母符号（mark）+ 6位随机数  =共24位数
     */
    public static String createId(String mark) {
        //改成uuid
        return Utils.getUUID();
        /*Date now = new Date();
        String partOne = sdf.format(now);
        String partTwo = String.valueOf(Math.random()).substring(2, 8);   //0-1 随机数 转为字符串，截取2-8位
        return partOne + mark + partTwo;*/
    }

    /**
     * 将不确定时分秒的时间戳统一转换成 days天 前/后 的凌晨("00:00:00","59:59:59")的时间戳
     * 1.时间戳转换成时间，
     * 2.设置成凌晨的时间，
     * 3.再 转换成时间戳
     * 默认为系统当前的时间
     */
    public static long weeHours(Long timeStamp, String HHmmss, int days) {
        if (timeStamp == null) timeStamp = System.currentTimeMillis();
        try {
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (timeStamp.toString().length() == 10) {
                timeStamp = Long.valueOf(timeStamp.toString() + "000");
            } else if (timeStamp.toString().length() < 10 || timeStamp.toString().length() > 13) {
                return -1;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = new Date(timeStamp);

            //当前日期 前推/后移 days天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            Date d = calendar.getTime();
            String tempTime = simpleDateFormat.format(d);

            //String tempTime = simpleDateFormat.format(date1);
            tempTime = tempTime.substring(0, 10) + " " + HHmmss;
            timeStamp = simpleDateFormat.parse(tempTime).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return timeStamp;
    }

    /**
     * 将不确定时分秒的时间戳统一转换成 days天 前/后 凌晨("00:00:00","59:59:59")的时间
     * 1.时间戳转换成时间，
     * 2.设置成凌晨的时间
     * 默认为系统当前的时间
     */
    public static Date weeHours1(Long timeStamp, String HHmmss, int days) {
        if (timeStamp == null) timeStamp = System.currentTimeMillis();

        Date result = null;
        try {
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (timeStamp.toString().length() == 10) {
                timeStamp = Long.valueOf(timeStamp.toString() + "000");
            } else if (timeStamp.toString().length() < 10 || timeStamp.toString().length() > 13) {
                return null;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = new Date(timeStamp);

            //当前日期 前推/后移 days天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            Date d = calendar.getTime();
            String tempTime = simpleDateFormat.format(d);

            //String tempTime = simpleDateFormat.format(date1);
            tempTime = tempTime.substring(0, 10) + " " + HHmmss;
            result = simpleDateFormat.parse(tempTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static Date weeHours1(Long timeStamp, int days) {
        if (timeStamp == null) timeStamp = System.currentTimeMillis();

        Date result = null;
        try {
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (timeStamp.toString().length() == 10) {
                timeStamp = Long.valueOf(timeStamp.toString() + "000");
            } else if (timeStamp.toString().length() < 10 || timeStamp.toString().length() > 13) {
                return null;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = new Date(timeStamp);

            //当前日期 前推/后移 days天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            Date d = calendar.getTime();
            String tempTime = simpleDateFormat.format(d);

            result = simpleDateFormat.parse(tempTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 将不确定时分秒的时间戳统一转换成 days天 前/后 的凌晨("00:00:00")的时间戳
     * 1.时间戳转换成时间，
     * 2.设置成凌晨的时间，
     * 3.再 转换成时间戳
     * 默认为系统当前的时间
     */
    public static Date weeHours(String date, int days) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date date1 = null;
        try {
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (date.length() == 10) {
                date1 = sdf.parse(date + " 00:00:00");
            } else if (date.length() == 19) {
                date1 = sdf.parse(date);
            } else if (date.length() < 10 || date.length() > 19) {
                return null;
            }

            //当前日期 前推/后移 days天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            Date d = calendar.getTime();
            return d;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date weeHours2(String date, int hours) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date date1 = null;
        try {
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (StrUtil.isEmpty(date)) {
                date1 = new Date();
            } else if (date.length() == 10) {
                date1 = sdf.parse(date + " 00:00:00");
            } else if (date.length() == 19) {
                date1 = sdf.parse(date);
            } else if (date.length() < 10 || date.length() > 19) {
                return null;
            }

            //当前日期 前推/后移 hours小时
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            calendar.add(Calendar.HOUR_OF_DAY, hours);
            Date d = calendar.getTime();
            return d;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date weeHours3(String date, int minutes) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date date1 = null;
        try {
            //判断时间戳长度 10为精确到秒，13为精确到毫秒
            if (StrUtil.isEmpty(date)) {
                date1 = new Date();
            } else if (date.length() == 10) {
                date1 = sdf.parse(date + " 00:00:00");
            } else if (date.length() == 19) {
                date1 = sdf.parse(date);
            } else if (date.length() < 10 || date.length() > 19) {
                return null;
            }

            //当前日期 前推/后移 hours小时
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            calendar.add(Calendar.MINUTE, minutes);
            Date d = calendar.getTime();
            return d;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static final String ACCESSKEY_SECRET = "testSecret";
    protected static final Logger logger = LoggerFactory.getLogger(IdUnit.class);

    /*
    所有的参数，这里使用TreeMap， 好处在于天然有序，默认是字母顺序
    Map<String, String> params = new TreeMap<>();
    */
    public static String getSign(Map<String, Object> params) throws Exception {
        StringBuilder builder = new StringBuilder();
        StringBuilder builder1 = new StringBuilder();

        List<Map.Entry<String, Object>> items = new ArrayList<>(params.entrySet());
        Collections.sort(items, (o1, o2) -> {
            // TODO Auto-generated method stub
            return (o1.getKey().compareTo(o2.getKey()));
        });

        int size = 1;
        for (Map.Entry<String, Object> entry : items) {
            String entValue;
            if (entry.getValue() instanceof List) {
                List<String> list = (List<String>) entry.getValue();
                entValue = String.join(",", list);
            } else {
                entValue = String.valueOf(entry.getValue());
            }
            //对每个参数和值进行encode，对字符进行转义
            String key = URLEncoder.encode(entry.getKey(), "UTF-8");
            String value = URLEncoder.encode(entValue, "UTF-8");

            builder.append(key).append("=").append(value);
            builder1.append(entry.getKey()).append("=").append(entValue);
            if (size != params.size()) {
                builder.append("&");
                builder1.append("&");
            }
            size++;
        }
        String stringToSign = URLEncoder.encode(builder.toString(), "UTF-8");

        byte[] bytes = HmacSHA1Encrypt(stringToSign, ACCESSKEY_SECRET);
        /*BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(bytes);*/
        return Base64.encodeBase64String(bytes);
    }

    private static byte[] HmacSHA1Encrypt(String encryptText, String encryptKey) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKey secretKey = new SecretKeySpec(encryptKey.getBytes(), "HmacSHA1");
        mac.init(secretKey);
        byte[] text = encryptText.getBytes();
        return mac.doFinal(text);
    }

    public static boolean checkSign(JSONObject data) {
        return true;
        /*HashMap<String, Object> paramMap = new HashMap<>();
        //循环转换
        for (Object o : data.entrySet()) {
            Map.Entry<String, Object> entry = (Map.Entry<String, Object>) o;
            if (!entry.getKey().equals("sign")) paramMap.put(entry.getKey(), entry.getValue());
        }

        String sign = (String) data.get("sign");

        String newSign;
        try {
            newSign = getSign(paramMap);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return !StrUtil.isEmpty(newSign) && newSign.equals(sign);*/
    }

    public static boolean checkUpdateOrderCheckingSign(JSONObject data) {
        String minInId = String.valueOf(data.get("minInId"));
        String minOutId = String.valueOf(data.get("minOutId"));
        String maxInId = String.valueOf(data.get("maxInId"));
        String maxOutId = String.valueOf(data.get("maxOutId"));
        JSONArray inArray = data.getJSONArray("inData");
        JSONArray outArray = data.getJSONArray("outData");
        List<String> inBillCodes = new ArrayList<>();
        List<String> outBillCodes = new ArrayList<>();
        for (Object o : inArray) {
            String jsonStr = JSONObject.toJSONString(o);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            inBillCodes.add(String.valueOf(jsonObject.get("billCode")));//订单企业系统票号
        }
        for (Object o : outArray) {
            String jsonStr = JSONObject.toJSONString(o);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            outBillCodes.add(String.valueOf(jsonObject.get("billCode")));//订单企业系统票号
        }
        if (inBillCodes.size() > 0 && (!inBillCodes.contains(minInId) || !inBillCodes.contains(maxInId))) return false;
        if (outBillCodes.size() > 0 && (!outBillCodes.contains(minOutId) || !outBillCodes.contains(maxOutId)))
            return false;

        return true;
        /*HashMap<String, Object> paramMap = new HashMap<>();
        //循环转换
        for (Object o : data.entrySet()) {
            Map.Entry<String, Object> entry = (Map.Entry<String, Object>) o;
            if (!entry.getKey().equals("sign") && !entry.getKey().equals("inData") && !entry.getKey().equals("outData"))
                paramMap.put(entry.getKey(), entry.getValue());
        }
        String sign = (String) data.get("sign");

        String newSign;
        try {
            newSign = getSign(paramMap);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return !StrUtil.isEmpty(newSign) && newSign.equals(sign);*/
    }

    /**
     * 时间戳转换成日期格式字符串
     */
    public static String timeStamp2Date(Long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sd = sdf.format(new Date(timeStamp));      // 时间戳转换成时间
        return sd;
    }

    /**
     * 日期转换成日期格式字符串
     */
    public static String date2DateString(Date date) {
        if (date == null) date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sd = sdf.format(date);      // 时间戳转换成时间
        return sd;
    }

    public static Date getTomorrow() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getMonthFirstDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getNextMonthFirstDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        calendar.set(Calendar.MONTH, 1);
        return calendar.getTime();
    }


    private static final Double EARTH_RADIUS = 6371393.0;    //地球半径：6371.393km
    /**
     * 计算两个经纬度坐标之间的距离
     * 返回值：距离，单位 米（m）
     * */
    public static Double getDistance(Double startLng, Double startLat, Double endLng, Double endLat) {
        double lng1 = Math.toRadians(startLng);
        double lat1 = Math.toRadians(startLat);
        double lng2 = Math.toRadians(endLng);
        double lat2 = Math.toRadians(endLat);

        double a = lat1 - lat2;
        double b = lng1 - lng2;

        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;

        return s;
    }

    public static boolean isInArea(Double lng, Double lat) {
        double E = 135.09;
        double W = 73.55;
        double S = 3.85;
        double N = 53.55;

        if (lng > E || lng < W) return true;
        if (lat > N || lat < S) return true;
        return false;
    }
}
