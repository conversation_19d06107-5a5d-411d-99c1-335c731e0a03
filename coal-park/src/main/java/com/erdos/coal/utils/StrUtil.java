package com.erdos.coal.utils;

import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串相关工具类
 */
public final class StrUtil {

    private StrUtil() {
    }

    public static boolean isEmpty(Object obj) {
        return obj == null || String.valueOf(obj).trim().equals("");
    }

    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }

    public static boolean isBlank(String str) {
        int strLen;
        if (str != null && (strLen = str.length()) != 0) {
            for (int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return true;
        }
    }

    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 用户名是否符合规范   ^[\u4E00-\u9FA5A-Za-z0-9]{6,16}$，支持中文、大小写字母、数字随意组合，默认长度6~16字符
     */
    public static boolean isValidUsername(String username) {
        return isValidUsername(username, 6, 20);
    }

    /**
     * 用户名是否符合规范   ^[\u4E00-\u9FA5A-Za-z0-9]{6,16}$
     */
    public static boolean isValidUsername(String username, Integer min, Integer max) {
        if (isBlank(username)) {
            return false;
        }
        String reg = "^[\u4E00-\u9FA5A-Za-z0-9]{" + min + "," + max + "}$";

        return username.matches(reg);
    }

    //TODO: 使用时验证一下以下两个正则; isMobileNum, isPhone

    /**
     * 是否有效手机号码1
     */
    public static boolean isMobileNum(String mobileNum) {
        if (null == mobileNum) {
            return false;
        }
        //String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$";

        return mobileNum.matches("^((13[0-9])|(14[4,7])|(15[^4,\\D])|(17[0-9])|(18[0-9]))(\\d{8})$");
    }

    /**
     * 是否有效手机号码2
     */
    public static boolean isPhone(String phone) {
        //中国电信号段 133、149、153、173、177、180、181、189、199
        //中国联通号段 130、131、132、145、155、156、166、175、176、185、186
        //中国移动号段 134(0-8)、135、136、137、138、139、147、150、151、152、157、158、159、178、182、183、184、187、188、198
        //其他号段
        //14号段以前为上网卡专属号段，如中国联通的是145，中国移动的是147等等。
        //虚拟运营商
        //电信：1700、1701、1702
        //移动：1703、1705、1706
        //联通：1704、1707、1708、1709、171
        //卫星通信：1349
//        String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$";
        String regex = "^1\\d{10}$";
        if (phone.length() != 11) {
            return false;
        } else {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(phone);
            boolean isMatch = m.matches();
            return isMatch;
        }
    }

    /**
     * 是否有效邮箱
     */
    public static boolean isEmail(String email) {
        if (null == email) {
            return false;
        }

        return email.matches("^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$");
    }

    /**
     * 是否是QQ邮箱
     */
    public static boolean isQQEmail(String email) {
        if (null == email)
            return false;

        return email.matches("^[\\s\\S]*@qq.com$");
    }

    /**
     * 是否为16-22位银行账号
     */
    public static boolean isBankAccount(String bankAccount) {
        if (null == bankAccount) {
            return false;
        }

        return bankAccount.matches("^\\d{16,22}$");
    }

    /**
     * 是否是纯数字，不含空格
     */
    public static boolean isNumeric(String str) {
        if (isBlank(str)) {
            return false;
        }
        int sz = str.length();
        for (int i = 0; i < sz; i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否数值类型，整数或小数
     */
    public static boolean isNumericalValue(String str) {
        if (null == str) {
            return false;
        }
        return str.matches("^[+-]?(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d)+)?$");
    }

    /**
     * 是否整数(^[+-]?(([1-9]{1}\\d*)|([0]{1}))$)
     */
    public static boolean isNumericInt(String str) {
        if (str == null) {
            return false;
        }
        return str.matches("(^[+-]?([0-9]|([1-9][0-9]*))$)");
    }

    /**
     * 是否正整数
     */
    public static boolean isNumericPositiveInt(String number) {
        if (null == number) {
            return false;
        }
        return number.matches("^[+-]?(([1-9]{1}\\d*)|([0]{1}))$");
    }

    /**
     * 判断是否是正整数数或者一位小数
     */
    public static boolean isOneDouble(String str) {
        if (str == null) {
            return false;
        }
        return str.matches("^(\\d+\\.\\d{1,1}|\\d+)$");
    }

    /**
     * 判断是否是正整数数或者一位小数
     */
    public static boolean isTwoDouble(String str) {
        if (str == null) {
            return false;
        }
        return str.matches("^(\\d+\\.\\d{1,2}|\\d+)$");
    }

    /**
     * 判断给定字符串是否小于给定的值(min)
     */
    public static boolean isNumLess(String str, float min) {
        if (str == null) {
            return false;
        }
        if (!isNumeric(str)) {
            return false;
        }
        float val = Float.parseFloat(str);
        return (val < min);
    }

    /**
     * 判断给定的字符串大于说给定的值
     */
    public static boolean isNumMore(String str, float max) {
        if (str == null) {
            return false;
        }
        if (!isNumeric(str)) {
            return false;
        }
        float val = Float.parseFloat(str);
        return (val > max);
    }

    /**
     * 是否小数
     */
    public static boolean isNumericDouble(String str) {
        if (str == null) {
            return false;
        }
        return str.matches("^[+-]?(([1-9]\\d*\\.?\\d+)|(0{1}\\.\\d+)|0{1})");
    }

    /**
     * 是否是16进制颜色值
     */
    public static boolean isColor(String str) {
        if (str == null) {
            return false;
        }
        return str.matches("(^([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$)");
    }

    /**
     * 判断是否是Boolean值
     */
    public static boolean isBoolean(String str) {
        if (str == null) {
            return false;
        }
        return str.equals("true") || str.equals("false");
    }

    /**
     * 判断是否是日期,格式：yyyy-MM-dd
     */
    public static boolean isDate(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            format.parse(str);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 判断是否是日期,格式：yyyy-MM-dd hh:mm:ss
     */
    public static boolean isDateyyyyMMddhhmmss(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        try {
            format.parse(str);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    // 今天0点0分0秒 - 23点59分59秒的毫秒数
    public static long[] time() {
        long current = System.currentTimeMillis();//当前时间毫秒数
        long zero = current / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();//今天零点零分零秒的毫秒数
        long twelve = zero + 24 * 60 * 60 * 1000 - 1;//今天23点59分59秒的毫秒数
        long[] time = {zero, twelve};
        return time;
    }

    /**
     * 使用map中的值替换str的指定内容
     * 假设:date  supervisor设置SEO规则,date。其中date和supervisor是要被替换的内容，则map中key就为date,supervisor。
     */
    public static String replaceByMap(String str, Map<String, String> map) {
        String result = str;
        if (map == null || map.isEmpty()) {
            return result;
        }
        for (String _key : map.keySet()) {
            result = result.replace(_key, map.get(_key));
        }
        return result;
    }

    /**
     * 计算一个颜色值的0.7透明度
     * 颜色值为ffffff/fff两种形式
     */
    public static String colorByAlpha(String color) {
        if (color.length() == 3) {
            color = "000" + color;
        }
        String r = color.substring(0, 2);
        String g = color.substring(2, 4);
        String b = color.substring(4);
        return Integer.toHexString(Integer.parseInt(r, 16) * 7 / 10 + 255 * 3 / 10)
                + Integer.toHexString(Integer.parseInt(g, 16) * 7 / 10 + 255 * 3 / 10)
                + Integer.toHexString(Integer.parseInt(b, 16) * 7 / 10 + 255 * 3 / 10);
    }

    /**
     * 脱敏处理
     */
    public static String asterisk(String str, int start, int end, int count) {
        StringBuilder result = new StringBuilder();
        int length = str.length();
        if (start <= length) {
            result.append(str, 0, start);
        } else {
            result.append(str, 0, length);
        }
        for (int i = 0; i < count; i++) {
            result.append("*");
        }
        if (end <= length) {
            result.append(str, length - end, length);
        } else {
            result.append(str, 0, length);
        }
        return result.toString();
    }

    public static Boolean isSixNum(String pwd) {
        Pattern pattern = Pattern.compile("\\d{6}");
        boolean matches = pattern.matcher(pwd).matches();
        return matches;
    }

    // Map<String,String[]> 转 Map<String,String>
    public static Map<String, String> convertMap(Map<String, String[]> paraMap) {
        Map<String, String> returnMap = new HashMap<>();
        // 转换为Entry
        Set<Map.Entry<String, String[]>> entries = paraMap.entrySet();
        //遍历
        for (Map.Entry<String, String[]> entry : entries) {
            String key = entry.getKey();
            StringBuffer value = new StringBuffer();
            String[] val = entry.getValue();
            if (val != null && val.length > 0) {
                for (String v : val) {
                    value.append(v);
                }
            }
            returnMap.put(key, value.toString());
        }
        return returnMap;
    }

    /**
     * @param param  参数
     * @param encode 编码
     * @return 按key字典序排序, 只取值
     */
    public static String sortParam(Map<String, String> param, String encode) {
        String params = "";
        Map<String, String> map = param;

        try {
            List<Map.Entry<String, String>> itmes = new ArrayList<Map.Entry<String, String>>(map.entrySet());

            //对所有传入的参数按照字段名从小到大排序
            //Collections.sort(items); 默认正序
            //可通过实现Comparator接口的compare方法来完成自定义排序
            Collections.sort(itmes, new Comparator<Map.Entry<String, String>>() {
                @Override
                public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                    // TODO Auto-generated method stub
                    return (o1.getKey().toString().compareTo(o2.getKey()));
                }
            });

            //构造URL 键值对的形式
            StringBuffer sb = new StringBuffer();
            for (Map.Entry<String, String> item : itmes) {
                if (isNotBlank(item.getKey()) && !item.getKey().equals("sign")) {
                    String key = item.getKey();
                    String val = item.getValue();
                    val = URLEncoder.encode(val, encode);
//                    if (isLower) {
//                        sb.append(key.toLowerCase() + "=" + val);
//                    } else {
//                    sb.append(key + "=" + val);
//                    }
//                    sb.append("&");
                    sb.append(val);
                }
            }

            params = sb.toString();

        } catch (Exception e) {
            return "";
        }
        return params;
    }

    public static void main(String[] args) {
        //用户名是否符合规范
        isValidUsername("张三123");
        //isValidUsername("张三",1,3);
        //是否有效手机号码
        isMobileNum("***********");
        //是否是有效邮箱
        isEmail("<EMAIL>");
        //是否是QQ号码
        isQQEmail("77079859hgfg");
        //是否为16-22位银行账号
        isBankAccount("6214668888952714400111111111");
        //脱敏处理
        String account = "6214668888952714400";
        String accountStr = asterisk(account, 4, 4, account.length() - 8);
        System.out.println("银行卡脱敏处理后：" + accountStr);
    }
}