package com.erdos.coal.utils;

public class SysConstants {

    //编码规则类型 针对 billNum 表
    public enum UserType {

        UT_BIZ("BIZ"), //业务系统
        UT_SYSTEM("WEB"), //系统用户
        UT_CUSTOMER("CU"), //客商用户
        UT_DRIVER("DU"), //司机用户
        UT_WECHAT("WECHAT"),//微信小程序用户
        UT_WECHATCU("WECHATCU");//微信小程序客商用户

        private final String text;

        UserType(final String text) {
            this.text = text;
        }

        public static UserType createEnum(String value) {
            switch (value) {
                case "BIZ":
                    return UserType.UT_BIZ;
                case "WEB":
                    return UserType.UT_SYSTEM;
                case "CU":
                    return UserType.UT_CUSTOMER;
                case "DU":
                    return UserType.UT_DRIVER;
                case "WECHAT":
                    return UserType.UT_WECHAT;
                case "WECHATCU":
                    return UserType.UT_WECHATCU;
                default:
                    return null;
            }
        }

        @Override
        public String toString() {
            return text;
        }
    }

    public enum LockType {

        ALI_PAY_PREPAID(101),//支付宝_预下单_锁
        ALI_PAY_RESULT(102),//支付宝_支付结果_锁
        ACCOUNT_PAY_GOODS(103),//客商账户_代支付_锁
        Face_To_Face_Add_Goods(111),//面对面下单，添加货运信息
        Fa_Bu_Add_Goods(112),//批量发布货运信息
        WeChat_Goods(113),//分享小程序下单发布货运信息
        Add_Driver_Group(121),//添加司机组
        Add_Defined_Address(122),//添加自定义地址
        Add_Defined_Route(123),//添加自定义路线
        WX_PAY_PREPAID(201),//微信_预下单_锁
        WX_PAY_RESULT(202),//微信_支付结果_锁

        REFUND_PRE(301),//提现审核_锁

        SWEEPCODE(401);//扫码接单

        private final Integer type;

        LockType(int type) {
            this.type = type;
        }

        public Integer getType() {
            return type;
        }
    }

}
