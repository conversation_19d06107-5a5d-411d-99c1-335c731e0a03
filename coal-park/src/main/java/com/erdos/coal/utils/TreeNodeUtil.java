package com.erdos.coal.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.park.web.sys.entity.SysMenu;
import com.erdos.coal.park.web.sys.pojo.LoginMenu;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TreeNodeUtil {

    //for vue ================================================================================
    //获取父节点菜单
    public final static List<LoginMenu> getFatherLoginNode(List<LoginMenu> treesList) {
        List<LoginMenu> newTrees = new ArrayList<LoginMenu>();
        for (LoginMenu mt : treesList) {
            if (mt.getPid().equals("1")) {//如果pId为空，则该节点为父节点
                //递归获取父节点下的子节点
                mt.setChildren(getChildrenLoginNode(mt.getId(), treesList));
                newTrees.add(mt);
            }
        }
        return newTrees;
    }

    //递归获取子节点下的子节点
    private final static List<LoginMenu> getChildrenLoginNode(String pId, List<LoginMenu> treesList) {
        List<LoginMenu> newTrees = new ArrayList<LoginMenu>();
        for (LoginMenu mt : treesList) {
            if (mt.getPid().equals("1")) continue;
            if (mt.getPid().equals(pId)) {
                //递归获取子节点下的子节点，即设置树控件中的children
                mt.setChildren(getChildrenLoginNode(mt.getId(), treesList));
                //设置树控件attributes属性的数据
//                Map<String, Object> map = new HashMap<String, Object>();
//                map.put("url", mt.getUrl());
                //mt.setAttributes(map);
                newTrees.add(mt);
            }
        }
        return newTrees;
    }
    //==========================================================================

    //获取父节点菜单
    public final static List<SysMenu> getFatherNode(List<SysMenu> treesList) {
        List<SysMenu> newTrees = new ArrayList<SysMenu>();
        for (SysMenu mt : treesList) {
            if (mt.get_parentId().equals("1")) {//如果pId为空，则该节点为父节点
                //递归获取父节点下的子节点
                mt.setChildren(getChildrenNode(mt.getId(), treesList));
                newTrees.add(mt);
            }
        }
        return newTrees;
    }

    //递归获取子节点下的子节点
    private final static List<SysMenu> getChildrenNode(String pId, List<SysMenu> treesList) {
        List<SysMenu> newTrees = new ArrayList<SysMenu>();
        for (SysMenu mt : treesList) {
            if (mt.get_parentId().equals("1")) continue;
            if (mt.get_parentId().equals(pId)) {
                //递归获取子节点下的子节点，即设置树控件中的children
                mt.setChildren(getChildrenNode(mt.getId(), treesList));
                //设置树控件attributes属性的数据
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("url", mt.getUrl());
                //mt.setAttributes(map);
                newTrees.add(mt);
            }
        }
        return newTrees;
    }


    public static void main(String[] args) {
        // new ObjectId()
    }

}
