package com.erdos.coal.utils;

import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数字格式化
 */
public class NumFmtUtil {
    /**
     * 中文大写数字
     */
    private static final char[] CN_NUMBERS = {'零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'};
    /**
     * 中文大写金额单位
     */
    private static final char[] SERIES = {'分', '角', '元', '拾', '百', '仟', '万', '拾', '百', '仟', '亿'};

    /**
     * 四舍五入，由scale参数指 定精度
     *
     * @param val   原始数据
     * @param scale
     * @return
     */
    public static String round(int val, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("精确度不能小于0！");
        }
        String format = "%." + scale + "f";
        return String.format(format, val);
    }

    /**
     * 四舍五入，由scale参数指 定精度 (控制小数点位数)
     *
     * @param val   原始数据
     * @param scale
     * @return
     */
    public static String round(double val, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("精确度不能小于0！");
        }
        String format = "%." + scale + "f";
        return String.format(format, val);
    }

    /**
     * double数值格式化。该方法会进行四舍五入处理
     *
     * @param val    double数值
     * @param format 格式。   “0”表示“ 一个数字”；“#”表示“一个数字，不包括 0”
     * @return
     */
    public static String format(double val, String format) {
        DecimalFormat fmt = new DecimalFormat();
        fmt.applyPattern(format);
        return fmt.format(val);
    }

    /**
     * 金额格式化，格式：##,###.00 。该方法会对千分位进行四舍五入处理
     */
    public static String financeEN(double amount) {
        DecimalFormat fmt = new DecimalFormat();
        fmt.applyPattern("##,##0.00");
        return fmt.format(amount);
    }

    /**
     * 金额格式化，格式：0.00 。该方法会对千分位进行四舍五入处理
     */
    public static String finance(double amount) {
        DecimalFormat fmt = new DecimalFormat();
        fmt.applyPattern("0.00");
        return fmt.format(amount);
    }

    /**
     * 金额格式化。格式：中文大写
     * <br>如：100 -> 壹佰元
     *
     * @param amount
     * @return
     */
    public static String financeCN(double amount) {
        StringBuilder result = new StringBuilder();
        /** 格式金额，这里会保留两位小数，四舍五入 */
        DecimalFormat fmt = new DecimalFormat();
        fmt.applyPattern("#.00");
        String amountStr = fmt.format(amount);
        String numStr = amountStr.replace(".", "");
        int length = numStr.length();
        for (int i = 0; i < length; i++) {
            int num = Integer.parseInt(String.valueOf(numStr.charAt(i)));
            result.append(CN_NUMBERS[num]);
            int flag = 0;
            for (int j = i + 1; j < length; j++) {
                int numEnd = Integer.parseInt(String.valueOf(numStr.charAt(j)));
                if (numEnd == 0) {
                    flag += 1;
                }
            }
            result.append(SERIES[length - 1 - i]);
            if (flag == length - i - 1) {
                if (flag == 2) {
                    result.append("整");
                } else if (flag < 2) {
                    result.append("");
                } else if (flag >= 7 && flag < 10) {
                    result.append("万元整");
                } else {
                    result.append("元整");
                }
                break;
            }
        }
        return result.toString();
    }

    /**
     * 利用正则表达式判断字符串是否是数字
     *
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 利用正则表达式判断字符串是否是 小数点保留两位的数字
     *
     * @param s
     * @return
     */
    public static boolean isDouble(String s) {
        if (StrUtil.isEmpty(s)) return false;
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式

        Matcher match = pattern.matcher(s);
        boolean bo = match.matches();

        return bo;
    }
}
