package com.erdos.coal.utils;

import com.erdos.coal.park.api.manage.pojo.ImageResult;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class ImgUtil {

    // 图片路径
    private String classPath = "classpath*:sliderImage/*.*";
    // 图片的最大大小
    private static int IMAGE_MAX_WIDTH = 350;
    private static int IMAGE_MAX_HEIGHT = 250;
    // 抠图上面的半径
    private static int RADIUS = IMAGE_MAX_WIDTH / 38;
    // 抠图区域的高度
    private static int CUT_HEIGHT = IMAGE_MAX_WIDTH / 7;
    // 抠图区域的宽度
    private static int CUT_WEIGHT = IMAGE_MAX_WIDTH / 7;
    // 被扣地方填充的颜色
    private static int FLAG = 0xffffff;
    // 抠图部分凸起的方向
    private Location location;

    ImageResult imageResult = new ImageResult();
    private String ORI_IMAGE_KEY = "ORI_IMAGE_KEY";
    private String CUT_IMAGE_KEY = "CUT_IMAGE_KEY";

    private int XPOS;
    private int YPOS;

    ImageMessage imageMessage = new ImageMessage();

    /**
     * 获取抠图区的坐标原点
     */
    public void createXYPos(BufferedImage oriImage) {
        int height = oriImage.getHeight();
        int width = oriImage.getWidth();

        XPOS = new Random().nextInt(width - CUT_WEIGHT - RADIUS);
        YPOS = new Random().nextInt(height - CUT_HEIGHT - RADIUS - RADIUS) + RADIUS;

        // 确保横坐标位于2/4 - 3/4
        int div = (IMAGE_MAX_WIDTH / 4);
        if (XPOS / div == 0) {
            XPOS = XPOS + div * 2;
        } else if (XPOS / div == 1) {
            XPOS = XPOS + div;
        } else if (XPOS / div == 3) {
            XPOS = XPOS - div;
        }
    }

    /**
     * 计算抠图的相关参数
     */
    private void createImageMessage() {
        int x = 0, y = 0;
        int w = 0, h = 0;

        if (location == Location.UP) {
            x = XPOS;
            y = YPOS - RADIUS;
            w = CUT_WEIGHT;
            h = CUT_HEIGHT + RADIUS;
        } else if (location == Location.LEFT) {
            x = XPOS - RADIUS;
            y = YPOS;
            w = CUT_WEIGHT + RADIUS;
            h = CUT_HEIGHT;
        } else if(location == Location.DOWN) {
            x = XPOS;
            y = YPOS;
            w = CUT_WEIGHT;
            h = CUT_HEIGHT + RADIUS;
        } else if (location == Location.RIGHT) {
            x = XPOS;
            y = YPOS;
            w = CUT_WEIGHT + RADIUS;
            h = CUT_HEIGHT;
        }

        imageMessage.setXpos(x);
        imageMessage.setYpos(y);
        imageMessage.setCutImageHeight(h);
        imageMessage.setCutImageWidth(w);
    }

    /**
     * 检测图片大小是否符合要求
     */
    private BufferedImage checkImage(BufferedImage image) throws IOException {
        if ((image.getWidth() == IMAGE_MAX_WIDTH) || (image.getHeight() == IMAGE_MAX_HEIGHT)) {
            return image;
        } else if ((image.getWidth() < IMAGE_MAX_WIDTH) || (image.getHeight() < IMAGE_MAX_HEIGHT)) {
            throw new IllegalArgumentException("图片太小，不符合要求w*h[380*190]");
        } else {    // 压缩图片
            return compressImage(image, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT);
        }
    }

    /**
     * 获取扣完图的原图和被扣出来的图
     */
    public Map<String, BufferedImage> cutByTemplate(BufferedImage oriImage, int[][] blockData) {
        Map<String, BufferedImage> imageMap = new HashMap<>();
        BufferedImage cutImage = new BufferedImage(imageMessage.cutImageWidth, imageMessage.cutImageHeight, oriImage.getType());
        Graphics2D g2d = cutImage.createGraphics();
        // 透明化整张图
        cutImage = g2d.getDeviceConfiguration().createCompatibleImage(imageMessage.cutImageWidth, imageMessage.cutImageHeight, Transparency.BITMASK);
        g2d.dispose();
        g2d = cutImage.createGraphics();
        // 背景透明代码结束
        int xmax = imageMessage.xpos + imageMessage.cutImageWidth;
        int ymax = imageMessage.ypos + imageMessage.cutImageHeight;

        for (int x = imageMessage.xpos; x < xmax && x >= 0; x++) {
            for (int y = imageMessage.ypos; y < ymax && y >= 0; y++) {
                int oriRgb = oriImage.getRGB(x, y);
                if (blockData[x][y] == FLAG) {
                    oriImage.setRGB(x, y, FLAG);
                    // 描边 判断是否为边界，若是边界则填充为白色
                    if (blockData[x - 1][y] != FLAG || blockData[x + 1][y] != FLAG || blockData[x][y + 1] != FLAG || blockData[x][y - 1] != FLAG) {
                        g2d.setColor(color(0xffffff));
                    } else {
                        g2d.setColor(color(oriRgb));
                    }
                    g2d.setStroke(new BasicStroke(1f));
                    g2d.fillRect(x - imageMessage.xpos, y - imageMessage.ypos, 1, 1);
                }
            }
        }

        // 释放对象
        g2d.dispose();
        imageMap.put(ORI_IMAGE_KEY, oriImage);
        imageMap.put(CUT_IMAGE_KEY, cutImage);
        return imageMap;
    }

    /**
     * 获取抠图数据，被扣的像素点将使用FLAG进行标记
     */
    public int[][] getBlockData(BufferedImage oriImage) {
        int height = oriImage.getHeight();
        int width = oriImage.getWidth();
        int[][] blockData = new int[width][height];

        Location locations[] = {Location.UP, Location.LEFT, Location.DOWN, Location.RIGHT};

        // 矩形， 进行区域扫描
        for (int x = 0; x < width && x >= 0; x++) {
            for (int y = 0; y < height && y >= 0; y++) {
                blockData[x][y] = 0;
                if ((x > XPOS) && (x < (XPOS + CUT_WEIGHT)) && (y > YPOS) && (y < (YPOS + CUT_HEIGHT))) {
                    blockData[x][y] = FLAG;
                }
            }
        }

        // 圆形突出区域， 突出圆形的原点坐标(x,y)
        int xBulgeCenter = 0, yBulgeCenter = 0;
        int xConcaveCenter = 0, yConcaveCenter = 0;

        // 位于矩形的哪一边，0123--上下左右
        location = locations[new Random().nextInt(4)];
        if (location == Location.UP) {
            //上 凸起
            xBulgeCenter = XPOS + CUT_WEIGHT / 2;
            yBulgeCenter = YPOS;
            //左 凹陷
            xConcaveCenter = XPOS;
            yConcaveCenter = YPOS + CUT_HEIGHT / 2;
        } else if (location == Location.DOWN) {
            // 下 凸起
            xBulgeCenter = XPOS + CUT_WEIGHT / 2;
            yBulgeCenter = YPOS + CUT_HEIGHT;
            // 右 凹陷
            xConcaveCenter = XPOS + CUT_WEIGHT;
            yConcaveCenter = YPOS + CUT_HEIGHT / 2;
        } else if (location == Location.LEFT) {
            //左 凸起
            xBulgeCenter = XPOS;
            yBulgeCenter = YPOS + CUT_HEIGHT / 2;
            //下 凹陷
            xConcaveCenter = XPOS + CUT_WEIGHT / 2;
            yConcaveCenter = YPOS + CUT_HEIGHT;
        } else {
            //右 凸起
            xBulgeCenter = XPOS + CUT_WEIGHT;
            yBulgeCenter = YPOS + CUT_HEIGHT / 2;
            //上 凹陷
            xConcaveCenter = XPOS + CUT_WEIGHT / 2;
            yConcaveCenter = YPOS;
        }

        // 半径的平方
        int RADIUS_POW2 = RADIUS * RADIUS;

        // 凸起的部分
        for (int x = xBulgeCenter - RADIUS; x < xBulgeCenter + RADIUS && x >= 0; x++) {
            for (int y = yBulgeCenter - RADIUS; y < xBulgeCenter + RADIUS && y >= 0; y++) {
                if (Math.pow((x - xBulgeCenter), 2) + Math.pow((y - yBulgeCenter), 2) < RADIUS_POW2) {
                    blockData[x][y] = FLAG;
                }
            }
        }

        // 凹陷部分
        for (int x = xConcaveCenter - RADIUS; x < xConcaveCenter + RADIUS && x >= 0; x++) {
            for (int y = yConcaveCenter - RADIUS; y < yConcaveCenter + RADIUS && y >= 0; y++) {
                if (Math.pow((x - xConcaveCenter), 2) + Math.pow((y - yConcaveCenter), 2) <= RADIUS_POW2) {
                    blockData[x][y] = 0;
                }
            }
        }
        return blockData;
    }

    /**
     * 将图片转为base64存储
     */
    private String ImageBase64(BufferedImage bufferedImage) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "png", out);
        // 转成byte数组
        byte[] bytes = out.toByteArray();
        Base64.Encoder encoder = Base64.getEncoder();
        // 生成BASE64编码
        return encoder.encodeToString(bytes);
    }

    /**
     * 随机获取一个图片文件
     */
    private BufferedImage getRandomImage() throws IOException {
        try {
            // 使用resource获取resource文件【注意：即使打成jar包也有效】
            ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resourcePatternResolver.getResources(classPath);

            if (resources.length <= 0) {
                throw new IOException("该文件夹内没有文件");
            } else {
                int index = new Random().nextInt(resources.length);
                InputStream inputStream = resources[index].getInputStream();
                BufferedImage marioBufferedImage = ImageIO.read(inputStream);
                return marioBufferedImage;
            }
        } catch (IOException e) {
            throw new IOException("读取文件失败！");
        }
    }

    /**
     * 压缩图片
     */
    private BufferedImage compressImage(BufferedImage image, int width, int height) throws IOException {
        return Thumbnails.of(image).forceSize(width, height)
                // .width(width).height(height)
                .asBufferedImage();
    }


    private Color color(int rgb) {
        int b = (0xff & rgb);
        int g = (0xff & (rgb >> 8));
        int r = (0xff & (rgb >> 16));
        return new Color(r, g, b);
    }

    /**
     * 对外提供的接口
     */
    public ImageResult imageResult() throws IOException {
        return imageResult(getRandomImage());
    }

    public ImageResult imageResult(BufferedImage oriBufferedImage) throws IOException {
        // 检测图片大小
        oriBufferedImage = checkImage(oriBufferedImage);

        // 初始化原点坐标
        createXYPos(oriBufferedImage);

        // 获取被扣图像的标志图
        int[][] blockData = getBlockData(oriBufferedImage);

        // 计算抠图区域的信息
        createImageMessage();

        // 获取扣了图的原图和被扣部分的图
        Map<String, BufferedImage> imageMap = cutByTemplate(oriBufferedImage, blockData);
        imageResult.setOriImage(ImageBase64(imageMap.get(ORI_IMAGE_KEY)));
        imageResult.setCutImage(ImageBase64(imageMap.get(CUT_IMAGE_KEY)));

        imageResult.setXpos(imageMessage.getXpos());
        imageResult.setYpos(imageMessage.getYpos());
        imageResult.setCutImageWidth(imageMessage.getCutImageWidth());
        imageResult.setCutImageHeight(imageMessage.getCutImageHeight());

        return imageResult;
    }


    private class ImageMessage {
        int xpos;
        int ypos;
        int cutImageWidth;
        int cutImageHeight;

        public int getXpos() {
            return xpos;
        }

        public void setXpos(int xpos) {
            this.xpos = xpos;
        }

        public int getYpos() {
            return ypos;
        }

        public void setYpos(int ypos) {
            this.ypos = ypos;
        }

        public int getCutImageWidth() {
            return cutImageWidth;
        }

        public void setCutImageWidth(int cutImageWidth) {
            this.cutImageWidth = cutImageWidth;
        }

        public int getCutImageHeight() {
            return cutImageHeight;
        }

        public void setCutImageHeight(int cutImageHeight) {
            this.cutImageHeight = cutImageHeight;
        }
    }


    /**
     * 抠图部分凸起的区域
     */
    private enum Location {
        UP,
        LEFT,
        DOWN,
        RIGHT
    }
}
