package com.erdos.coal.base;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @作者: LIGX
 * @时间: 2017/12/3
 * @描述: IBaseController 实现类
 */

public abstract class BaseController {

    //protected final Log event = LogFactory.getLog(this.getClass());

    protected final Logger logger = LoggerFactory.getLogger(getClass());

//    private static final ThreadLocal<HttpServletRequest> requestContainer = new ThreadLocal<HttpServletRequest>();
//    private static final ThreadLocal<HttpServletResponse> responseContainer = new ThreadLocal<HttpServletResponse>();
//    private static final ThreadLocal<ModelMap> modelContainer = new ThreadLocal<ModelMap>();
//
//    @ModelAttribute
//    private void initResponse(HttpServletResponse response) {
//        responseContainer.set(response); //TODO: 设置response
//    }
//
//    @ModelAttribute
//    private void initRequest(HttpServletRequest request) {
//        requestContainer.set(request); //TODO: 设置request
//    }
//
//    @ModelAttribute
//    private void initModelMap(ModelMap model) {
//        modelContainer.set(model); //TODO: 设置model
//    }
//
//    protected final HttpServletResponse getResponse() {
//        return responseContainer.get(); //获取当前线程的response对象
//    }
//
//    protected final HttpServletRequest getRequest() {
//        return requestContainer.get(); //获取当前线程的request对象
//    }
//
//    protected final ModelMap getModel() {
//        return modelContainer.get(); //获取当前线程的modelMap对象
//    }

    protected HttpServletRequest request;

    protected HttpServletResponse response;

    public HttpServletRequest getRequest() {
        return request;
    }

    @Autowired
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    @Resource
    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

//    protected <T> T buildVoFromRequest(Class<T> clazz) {
//        return Utils.buildVoFromRequest(this.getRequest(), clazz);
//    }

    //返回 JSON String by Object
//    protected String getJsonString(Object obj) {
//        String mResult = "";
//        try {
//            ObjectMapper om = new ObjectMapper();
//            mResult = om.writeValueAsString(obj);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return mResult;
//    }

    //返回值
//    protected void renderData(Object obj) {
//        PrintWriter printWriter = null;
//        try {
//            //设置页面不缓存
//            response.setContentType("application/json");
//            response.setHeader("Pragma", "No-cache");
//            response.setHeader("Cache-Control", "no-cache");
//            response.setCharacterEncoding("UTF-8");
//
//            printWriter = response.getWriter();
//            String data = getJsonString(obj);
//            printWriter.print(data);
//        } catch (IOException ex) {
//            ex.printStackTrace();
//        } finally {
//            if (null != printWriter) {
//                printWriter.flush();
//                printWriter.close();
//            }
//        }
//    }
//    protected String getParameter(String param) {
//        return Utils.trim(this.getRequest().getParameter(param));
//    }
//
//    protected String[] getParameterValues(String param) {
//        return this.getRequest().getParameterValues(param);
//    }

//    protected void setSessionObject(Object obj) {
//        HttpSession session = this.getRequest().getSession();
//
//        session.setMaxInactiveInterval(maxInactiveInterval);
//
//        session.setAttribute(Constant.CURRENT_USER_KEY, obj);
//    }
//
//    protected Object getSessionObject() {
//        return this.getRequest().getSession().getAttribute(Constant.CURRENT_USER_KEY);
//    }

//    protected void removeSessionObject() {
//        this.getRequest().getSession().removeAttribute(Constant.CURRENT_USER_KEY);
//    }

//    @SuppressWarnings("unchecked")
//    protected <T> T getCurrentUser() {
//        //APP 端用户，通过拦截器时，从令牌中取到id, 然后从数据库中查出来放到request属性中的
//        return (T) this.getRequest().getAttribute(Constant.CURRENT_USER_KEY);
//    }

}
