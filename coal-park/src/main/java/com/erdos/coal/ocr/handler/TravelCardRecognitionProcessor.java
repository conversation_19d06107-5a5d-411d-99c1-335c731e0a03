package com.erdos.coal.ocr.handler;

import com.erdos.coal.config.OcrConfig;
import com.erdos.coal.ocr.IRecognitionHandler;
import com.erdos.coal.ocr.entity.TravelCard;
import com.erdos.coal.ocr.parser.travelcard.TravelCardParser;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;

/**
 * 行程卡识别处理
 */
@Component
public class TravelCardRecognitionProcessor extends TravelCardParser implements IRecognitionHandler<TravelCard> {

    public TravelCardRecognitionProcessor(OcrConfig ocrConfig) {
        // 需要先加载 opencv 库 Core.NATIVE_LIBRARY_NAME
        // System.load(ocrConfig.opencvLib);
        super(ocrConfig.languagePath);
        this.setLanguage("chi_sim");
    }

    @Override
    public TravelCard getInfo(BufferedImage src) {
        return this.doRecognition(src);
    }
}
