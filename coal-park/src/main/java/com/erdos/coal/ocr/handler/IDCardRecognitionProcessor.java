package com.erdos.coal.ocr.handler;

import com.erdos.coal.config.OcrConfig;
import com.erdos.coal.ocr.IRecognitionHandler;
import com.erdos.coal.ocr.entity.IDCard;
import com.erdos.coal.ocr.parser.idcard.IDCardParser;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;

/**
 * 身份证识别处理
 */
@Component
public class IDCardRecognitionProcessor extends IDCardParser implements IRecognitionHandler<IDCard> {

    public IDCardRecognitionProcessor(OcrConfig ocrConfig) {
        // 需要先加载 opencv 库 Core.NATIVE_LIBRARY_NAME
        // System.load(ocrConfig.opencvLib);
        super(ocrConfig.languagePath);
    }

    @Override
    public IDCard getInfo(BufferedImage src) {
        return this.doRecognition(src);
    }
}
