package com.erdos.coal.ocr.handler;

import com.erdos.coal.config.OcrConfig;
import com.erdos.coal.ocr.IRecognitionHandler;
import com.erdos.coal.ocr.entity.HealthCode;
import com.erdos.coal.ocr.parser.healthcode.HealthCodeParser;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;

/**
 * 健康码识别处理
 */
@Component
public class HealthCodeRecognitionProcessor extends HealthCodeParser implements IRecognitionHandler<HealthCode> {

    public HealthCodeRecognitionProcessor(OcrConfig ocrConfig) {
        // 需要先加载 opencv 库 Core.NATIVE_LIBRARY_NAME
        // System.load(ocrConfig.opencvLib);
        super(ocrConfig.languagePath);
    }

    @Override
    public HealthCode getInfo(BufferedImage src) {
        return this.doRecognition(src);
    }
}
