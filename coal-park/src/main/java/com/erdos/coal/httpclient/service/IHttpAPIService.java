package com.erdos.coal.httpclient.service;

import com.erdos.coal.httpclient.HttpResult;

import java.util.Map;

public interface IHttpAPIService {

    public String doGet(String url) throws Exception;

    public String doGet(String url, Map<String, Object> map) throws Exception;

    public HttpResult doPost(String url, Map<String, Object> map) throws Exception;

    public HttpResult doPost(String url) throws Exception;

    public HttpResult sendPost(String url,String jsonStr) throws Exception;
}
