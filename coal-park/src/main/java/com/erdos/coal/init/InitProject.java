package com.erdos.coal.init;

import com.erdos.coal.init.database.SystemConfig;
import com.erdos.coal.init.database.SystemMenu;
import com.erdos.coal.init.database.SystemRole;
import com.erdos.coal.init.database.SystemUser;
import com.erdos.coal.park.web.sys.service.ISysMenuService;
import com.erdos.coal.park.web.sys.service.ISysRoleService;
import com.erdos.coal.park.web.sys.service.ISysUserService;
import dev.morphia.Datastore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class InitProject implements ApplicationRunner {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource(name = "dsForCoal")
    private Datastore datastore;

    @Resource
    private ISysMenuService sysMenuService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ISysRoleService sysRoleService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("---------- 系统初始化开始... ----------");

        //TODO: 系统初始化相关操作
        //0. 初始化系统配置...
        SystemConfig.initSysConfig(datastore, mongoTemplate);

        //1, 初始化系统菜单
        SystemMenu.initSysMenu(sysMenuService);

        //2, 初始化系统用户
        SystemUser.initSysUser(sysUserService);

        //3, 初始化系统角色
        SystemRole.initSysRole(sysRoleService);

        logger.info("========== 系统初始化完成. ==========");
    }
}
