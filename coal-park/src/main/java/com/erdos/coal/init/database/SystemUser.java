package com.erdos.coal.init.database;

import com.erdos.coal.core.utils.Utils;
import com.erdos.coal.park.web.sys.entity.SysUser;
import com.erdos.coal.park.web.sys.service.ISysUserService;

import java.util.List;

public class SystemUser {

    /**
     * 说明: 如果系统菜单为空，则增加系统菜单，否则不进行处理
     */
    public static void initSysUser(ISysUserService sysUserService) {

        //1,查询
        List<SysUser> listCount = sysUserService.list();

        if (listCount.size() > 0) {
            return;
        }

        //2,增加
        SysUser adminUser = new SysUser();

        /*PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String str = passwordEncoder.encode("123456");*/
        String str = Utils.md5("123456");

        adminUser.setId("1");
        adminUser.setName("管理员");
        adminUser.setLoginName("admin");
        adminUser.setPassword(str);
        adminUser.setRoles(new String[]{"1"}); //设置角色
        adminUser.setEnable(1);
        adminUser.setType(0);
        adminUser.setOrder(-1);

        sysUserService.save(adminUser);

        SysUser sysUser = new SysUser();

        sysUser.setId(Utils.getUUID());
        sysUser.setName("用户1");
        sysUser.setLoginName("user1");
        sysUser.setPassword(str);
        sysUser.setRoles(new String[]{}); //设置角色
        sysUser.setEnable(1);
        sysUser.setType(1);
        sysUser.setOrder(0);

        sysUserService.save(sysUser);
    }
}
