package com.erdos.coal.init.database;

import com.erdos.coal.park.web.sys.entity.SysRole;
import com.erdos.coal.park.web.sys.service.ISysRoleService;

import java.util.List;

public class SystemRole {

    /**
     * 说明: 如果系统菜单为空，则增加系统菜单，否则不进行处理
     */
    public static void initSysRole(ISysRoleService sysRoleService) {

        //1,查询
        List<SysRole> listCount = sysRoleService.list();

        if (listCount.size() > 0) {
            return;
        }

        //2,增加
        SysRole sysRole = new SysRole();
        sysRole.setId("1");
        sysRole.setName("管理员角色");
        sysRole.setState(-1);
        sysRole.setOrder(-1);
        // 选中的菜单
        sysRole.setMenus(new String[]{"0.000.001", "0.000.002", "0.000.003", "0.000.004", "0.000.005", "0.000.006",
                "0.001.001", "0.001.002", "0.001.003", "0.001.004", "0.001.005",
                "0.002.001", "0.002.002", "0.002.003", "0.002.004",
                "0.003.001", "0.003.002", "0.003.003",
                "0.004.001", "0.004.002", "0.004.003", "0.004.004",
                "0.005.001", "0.005.002", "0.005.003",
                "0.006.001",
                "0.007.001", "0.007.002",
                "0.008.001"
        });
        //登录后看见的菜单
        sysRole.setFuncs(new String[]{"1", "0.000", "0.000.001", "0.000.002", "0.000.003", "0.000.004", "0.000.005", "0.000.006",
                "0.001", "0.001.001", "0.001.002", "0.001.003", "0.001.004", "0.001.005",
                "0.002", "0.002.001", "0.002.002", "0.002.003", "0.002.004",
                "0.003", "0.003.001", "0.003.002", "0.003.003",
                "0.004", "0.004.001", "0.004.002", "0.004.003", "0.004.004",
                "0.005", "0.005.001", "0.005.002", "0.005.003",
                "0.006", "0.006.001",
                "0.007", "0.007.001", "0.007.002",
                "0.008", "0.008.001"

        });

        sysRoleService.save(sysRole);
    }
}
