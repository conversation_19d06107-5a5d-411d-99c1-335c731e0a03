package com.erdos.coal.init.database;

import com.erdos.coal.park.web.sys.entity.SysMenu;
import com.erdos.coal.park.web.sys.service.ISysMenuService;

import java.util.ArrayList;
import java.util.List;

public class SystemMenu {

    /**
     * 说明: 如果系统菜单为空，则增加系统菜单，否则不进行处理
     */
    public static void initSysMenu(ISysMenuService sysMenuService) {

        //1,查询
        List<SysMenu> listCount = sysMenuService.list(sysMenuService.createQuery().filter("type", 0));//0: 系统; 1: 业务;

        if (listCount.size() > 0) {
            return;
        }

        //2,增加
        List<SysMenu> list = new ArrayList<>();
        String id = "";

        list.add(new SysMenu("1", "[菜单树]", null, null, 0));//第一级

        id = "0.008";
        list.add(new SysMenu(id, "车辆管理", 1, "1", 1));//第二级
        list.add(new SysMenu("0.008.001", "车辆审核", 1, id, "app/car/index", 1));

        id = "0.003";
        list.add(new SysMenu(id, "司机管理", 1, "1", 2));//第二级
        list.add(new SysMenu("0.003.001", "司机APP信息", 1, id, "app/driver/info/index", 1));
        list.add(new SysMenu("0.003.002", "司机APP账户", 1, id, "app/driver/account/index", 2));
        list.add(new SysMenu("0.003.003", "微信小程序信息", 1, id, "app/driver/wx/index", 3));

        id = "0.004";
        list.add(new SysMenu(id, "客商管理", 1, "1", 3));//第三级
        list.add(new SysMenu("0.004.001", "客商信息", 1, id, "app/customer/info/index", 1));
        list.add(new SysMenu("0.004.002", "司机池", 1, id, "app/customer/driverPool/index", 2));
        list.add(new SysMenu("0.004.003", "司机组", 1, id, "app/customer/driverGroup/index", 3));
        list.add(new SysMenu("0.004.004", "客商账户", 1, id, "app/customer/account/index", 4));

        id = "0.005";
        list.add(new SysMenu(id, "订单管理", 1, "1", 4));//第四级
        list.add(new SysMenu("0.005.001", "订单信息", 1, id, "app/order/info/index", 1));
        list.add(new SysMenu("0.005.002", "订单评价", 1, id, "app/order/appraise/index", 2));
        list.add(new SysMenu("0.005.003", "订单退款", 1, id, "app/order/orderCheck/index", 3));

        /*id = Utils.getUUID();
        list.add(new SysMenu(id, "结算管理", 1, "1", 5));//第一级
        list.add(new SysMenu(Utils.getUUID(), "运费支付", 1, id, "app/settlement/freight", 1));//运费支付管理
        list.add(new SysMenu(Utils.getUUID(), "运费代付", 1, id, "app/settlement/payment", 2));//运费代付管理

        id = Utils.getUUID();
        list.add(new SysMenu(id, "抢单管理", 1, "1", 6));//第一级
        list.add(new SysMenu(Utils.getUUID(), "抢单信息", 1, id, "app/grab/info/index", 1));
        list.add(new SysMenu(Utils.getUUID(), "抢单设置", 1, id, "app/grab/setting/index", 2));

        id = Utils.getUUID();
        list.add(new SysMenu(id, "投诉建议", 1, "1", 7));//第一级
        list.add(new SysMenu(Utils.getUUID(), "司机信用", 1, id, "app/complaints/driverComplaints/index", 1));
        list.add(new SysMenu(Utils.getUUID(), "客商信用", 1, id, "app/complaints/customerComplaints/index", 2));*/

        id = "0.006";
        list.add(new SysMenu(id, "物流管理", 1, "1", 7));//第七级
        list.add(new SysMenu("0.006.001", "物流信息", 1, id, "app/logistics/info/index", 1));

        id = "0.007";
        list.add(new SysMenu(id, "资金管理", 1, "1", 8));//第八级
        list.add(new SysMenu("0.007.001", "报表", 1, id, "app/account/report", 1));//报表（按天为单位）
        list.add(new SysMenu("0.007.002", "明细", 1, id, "app/account/detail", 2));//明细

        id = "0.002";
        list.add(new SysMenu(id, "消息管理", 1, "1", 9));//第九级
        list.add(new SysMenu("0.002.001", "短信信息", 1, id, "app/message/sms/index", 1));
        list.add(new SysMenu("0.002.002", "车主申诉", 1, id, "app/message/ownerAppeal/index", 2));
        list.add(new SysMenu("0.002.003", "推送记录", 1, id, "app/message/push/index", 3));
        list.add(new SysMenu("0.002.004", "提现审核", 1, id, "app/message/refundPre/index", 4));

        list.add(new SysMenu("0.001", "基础设置", 0, "1", 10));//第九级
        list.add(new SysMenu("0.001.001", "企业管理一级单位", 0, "0.001", "app/primaryConfig/unit/index", 1));//单位地址管理
        list.add(new SysMenu("0.001.005", "企业管理二级单位", 0, "0.001", "app/primaryConfig/unit/second/index", 2));//二级单位地址管理
        list.add(new SysMenu("0.001.002", "自定义地址管理", 0, "0.001", "app/primaryConfig/definedAddress/index", 3));//自定义地址管理
        list.add(new SysMenu("0.001.003", "开关设置", 0, "0.001", "app/primaryConfig/switch/index", 4));
        list.add(new SysMenu("0.001.004", "车型管理", 0, "0.001", "app/primaryConfig/carInfo/index", 5));
        list.add(new SysMenu("0.001.006", "扫码设备管理", 0, "0.001", "app/device/index", 6));

        //TODO: 系统菜单
        list.add(new SysMenu("0.000", "系统管理", 0, "1", 11));//第十一级
        list.add(new SysMenu("0.000.001", "菜单管理", 0, "0.000", "sys/menu/index", 1));//第二级
        list.add(new SysMenu("0.000.002", "角色管理", 0, "0.000", "sys/role/index", 2));//第二级
        list.add(new SysMenu("0.000.003", "用户管理", 0, "0.000", "sys/user/index", 3));//第二级
        list.add(new SysMenu("0.000.004", "APP管理", 0, "0.000", "sys/app/index", 4));//第二级
        list.add(new SysMenu("0.000.005", "日志管理", 0, "0.000", "sys/log/index", 5));//第二级
        list.add(new SysMenu("0.000.006", "系统监控", 0, "0.000", "sys/server/index", 5));//第二级

        sysMenuService.save(list);
    }
}