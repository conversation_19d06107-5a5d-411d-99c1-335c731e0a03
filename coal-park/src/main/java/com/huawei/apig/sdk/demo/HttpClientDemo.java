/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2022. All rights reserved.
 */

package com.huawei.apig.sdk.demo;

// Import the external dependencies.
import com.huawei.apig.sdk.util.Constant;
import com.huawei.apig.sdk.util.SSLCipherSuiteUtil;

import com.cloud.apigateway.sdk.utils.Client;
import com.cloud.apigateway.sdk.utils.Request;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

public class HttpClientDemo {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientDemo.class);

    public static void main(String[] args) throws Exception {
        // Create a new request.
        Request request = new Request();
        try {
            // Set the request parameters.
            // AppKey, AppSecrect, Method and Url are required parameters.
            request.setKey("apigateway_sdk_demo_key");
            request.setSecret("apigateway_sdk_demo_secret");
            request.setMethod("POST");
            request.setUrl("your url");
            request.addHeader("Content-Type", "text/plain");
            request.setBody("demo");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            return;
        }
        CloseableHttpClient client = null;
        try {
            // Sign the request.
            HttpRequestBase signedRequest = Client.sign(request, Constant.SIGNATURE_ALGORITHM_SDK_HMAC_SHA256);
            // Do not verify ssl certificate
            client = (CloseableHttpClient) SSLCipherSuiteUtil.createHttpClient(Constant.INTERNATIONAL_PROTOCOL);
            HttpResponse response = client.execute(signedRequest);
            // Print the body of the response.
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                LOGGER.info("Processing Body with name: {} and value: {}", System.getProperty("line.separator"),
                        EntityUtils.toString(resEntity, "UTF-8"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (client != null) {
                client.close();
            }
        }
    }
}
