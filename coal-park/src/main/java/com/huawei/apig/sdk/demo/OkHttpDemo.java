/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2022. All rights reserved.
 */

package com.huawei.apig.sdk.demo;

import com.huawei.apig.sdk.util.Constant;
import com.huawei.apig.sdk.util.SSLCipherSuiteUtil;

import com.cloud.apigateway.sdk.utils.Client;
import com.cloud.apigateway.sdk.utils.Request;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import okhttp3.ResponseBody;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OkHttpDemo {
    private static final Logger LOGGER = LoggerFactory.getLogger(OkHttpDemo.class);

    public static void main(String[] args) {
        // Create a new request.
        Request request = new Request();
        try {
            request.setKey("apigateway_sdk_demo_key");
            request.setSecret("apigateway_sdk_demo_secret");
            request.setMethod("GET");
            request.setUrl("your url");
            request.addHeader("Content-Type", "text/plain");
            request.setBody("demo");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            return;
        }
        try {
            // Sign the request.
            okhttp3.Request signedRequest = Client.signOkhttp(request, Constant.SIGNATURE_ALGORITHM_SDK_HMAC_SHA256);
            // creat okhttpClient.
            OkHttpClient client = SSLCipherSuiteUtil.createOkHttpClient(Constant.INTERNATIONAL_PROTOCOL);
            // Send the request.
            Response response = client.newCall(signedRequest).execute();
            // Print the status line of the response.
            LOGGER.info("status: " + response.code());
            // Print the body of the response.
            ResponseBody resEntity = response.body();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
