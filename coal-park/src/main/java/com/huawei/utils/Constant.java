/*
 * File Name: com.huawei.utils.Constant.java
 *
 * Copyright Notice:
 *      Copyright  1998-2008, Huawei Technologies Co., Ltd.  ALL Rights Reserved.
 *
 *      Warning: This computer software sourcecode is protected by copyright law
 *      and international treaties. Unauthorized reproduction or distribution
 *      of this sourcecode, or any portion of it, may result in severe civil and
 *      criminal penalties, and will be prosecuted to the maximum extent
 *      possible under the law.
 */
package com.huawei.utils;

public class Constant {

    // purchase and get : base_url,appid,secret

    // please replace the IP and Port, when you use the demo.
    public static final String BASE_URL = "https://rtccall.cn-north-1.myhuaweicloud.cn:443";//https://{domain}:{port}";

    // please replace the appId and secret, when you use the demo.
    public static final String CLICK2CALL_APPID = "*****";
    public static final String CLICK2CALL_SECRET = "*****";
    public static final String CALLNOTIFY_APPID = "LR7Zv8Z3QXaZ63D61o8PP7OD0orI";//"*****";
    public static final String CALLNOTIFY_SECRET = "KfHtP9zwVqns4FPUqyLHZgqELj30";//"*****";
    public static final String CALLVERIFY_APPID = "*****";
    public static final String CALLVERIFY_SECRET = "*****";
    public static final String TRANSPARENTLY_TRANSMITS_APPID = "*****";
    public static final String TRANSPARENTLY_TRANSMITS_SECRET = "*****";

    /*
     *IP and port of callback url.
     *please replace the IP and Port of your Application deployment environment address, when you use the demo.
     */
    public static final String CALLBACK_BASE_URL = "";//http://************:9999";

    /*
     * complete callback url
     * please replace uri, when you use the demo.
     */
    public static final String STATUS_CALLBACK_URL = CALLBACK_BASE_URL + "/status";
    public static final String FEE_CALLBACK_URL = CALLBACK_BASE_URL + "/fee";

    // *************************** The following constants do not need to be
    // modified *********************************//

    /*
     * request header
     * 1. HEADER_APP_AUTH
     * 2. HEADER_APP_AKSK
     * 3. AUTH_HEADER_VALUE
     * 4. AKSK_HEADER_FORMAT
     */
    public static final String HEADER_APP_AUTH = "Authorization";
    public static final String HEADER_APP_AKSK = "X-AKSK";
    public static final String AUTH_HEADER_VALUE = "AKSK realm=\"SDP\",profile=\"UsernameToken\",type=\"Appkey\"";
    public static final String AKSK_HEADER_FORMAT = "UsernameToken Username=\"%s\",PasswordDigest=\"%s\",Nonce=\"%s\",Created=\"%s\"";

    /*
     * Voice Call:
     * 1. VOICE_CALL_COMERCIAL
     * 2. VOICE_VERIFICATION_COMERCIAL
     * 3. CALL_NOTIFY_COMERCIAL
     * 4. VOICE_FILE_DOWNLOAD
     */
    public static final String VOICE_CALL_COMERCIAL = BASE_URL + "/rest/httpsessions/click2Call/v2.0";
    public static final String VOICE_VERIFICATION_COMERCIAL = BASE_URL + "/rest/httpsessions/callVerify/v1.0";
    public static final String CALL_NOTIFY_COMERCIAL = BASE_URL + "/rest/httpsessions/callnotify/v2.0";
    public static final String VOICE_FILE_DOWNLOAD = BASE_URL + "/rest/provision/voice/record/v1.0";
}