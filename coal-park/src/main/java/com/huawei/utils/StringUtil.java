/*
 * Copyright Notice:
 *      Copyright  1998-2008, Huawei Technologies Co., Ltd.  ALL Rights Reserved.
 *
 *      Warning: This computer software sourcecode is protected by copyright law
 *      and international treaties. Unauthorized reproduction or distribution
 *      of this sourcecode, or any portion of it, may result in severe civil and
 *      criminal penalties, and will be prosecuted to the maximum extent
 *      possible under the law.
 */
package com.huawei.utils;

//import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64; // JDK version≥1.8
import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;
import java.util.UUID;

//import org.apache.commons.codec.binary.Base64; //JDK version<1.8
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class StringUtil {

    public static boolean strIsNullOrEmpty(String s) {
        return (null == s || s.trim().length() < 1);
    }

    public static String buildAKSKHeader(String appKey, String appSecret) throws Exception {
        if (StringUtil.strIsNullOrEmpty(appKey) || StringUtil.strIsNullOrEmpty(appSecret)) {
            return null;
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        format.setTimeZone(TimeZone.getTimeZone("UTC"));
        Calendar calendar = Calendar.getInstance();
        String time = format.format(calendar.getTime());
        String stNonce = UUID.randomUUID().toString().replace("-", "").toUpperCase(Locale.ROOT);
        String str = stNonce + time;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] authBytes = mac.doFinal(str.getBytes(StandardCharsets.UTF_8));
        String passwordDigestBase64Str = encodeBase64(authBytes);
        return String.format(Constant.AKSK_HEADER_FORMAT, appKey, passwordDigestBase64Str, stNonce, time);
    }

    private static String encodeBase64(byte[] bytes) {
        if (bytes.length == 0) {
            return null;
        } else {
            return new String(org.apache.commons.codec.binary.Base64.encodeBase64(bytes), StandardCharsets.UTF_8);
        }
    }
}