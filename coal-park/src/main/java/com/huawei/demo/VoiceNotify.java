package com.huawei.demo;

import com.huawei.utils.*;

import javax.net.ssl.HttpsURLConnection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VoiceNotify {
    // 语音通知API的调用地址
    private String urlCallNotify;
    // 接口响应的消息体
    private Map<String, String> Responsebody;
    // Https实体
    private HttpsUtil httpsUtil;

    public VoiceNotify() {
        // 商用地址
        urlCallNotify = Constant.CALL_NOTIFY_COMERCIAL;
        Responsebody = new HashMap<>();
    }

    @SuppressWarnings("unchecked")
    /*
     * 该示例只仅体现必选参数,可选参数根据接口文档和实际情况配置. 该示例不体现参数校验,请根据各参数的格式要求自行实现校验功能.
     * playInfoList为最大个数为5的放音内容参数列表,每个放音内容参数以Map<String,Object>格式存储,
     * 放音内容参数的构造方法请参考getplayInfo方法.
     */
    public String callNotifyAPI(String displayNbr, String calleeNbr, List<Map<String, Object>> playInfoList) throws Exception {
        httpsUtil = new HttpsUtil();

        // 忽略证书信任问题
        httpsUtil.trustAllHttpsCertificates();
        HttpsURLConnection.setDefaultHostnameVerifier(httpsUtil.hv);

        calleeNbr = "+86" + calleeNbr;

        // 请求Headers
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(Constant.HEADER_APP_AUTH, Constant.AUTH_HEADER_VALUE);
        headerMap.put(Constant.HEADER_APP_AKSK,
                StringUtil.buildAKSKHeader(Constant.CALLNOTIFY_APPID, Constant.CALLNOTIFY_SECRET));

        // 构造消息体
        Map<String, Object> bodys = new HashMap<>();
        bodys.put("displayNbr", displayNbr);//主叫用户手机终端的来电显示号码。
        bodys.put("calleeNbr", calleeNbr);//发起呼叫时所拨打的被叫号码。
        bodys.put("playInfoList", playInfoList);//播放信息列表，最大支持5个，每个播放信息携带的参数都可以不相同。
        String jsonRequest = JsonUtil.jsonObj2Sting(bodys);

        /*
         * Content-Type为application/json且请求方法为post时, 使用doPostJsonGetStatusLine方法构造http
         * request并获取响应.
         */
        StreamClosedHttpResponse responseCallNotify = httpsUtil.doPostJsonGetStatusLine(urlCallNotify, headerMap,
                jsonRequest);

        // 响应的消息体写入Responsebody.
        Responsebody = JsonUtil.jsonString2SimpleObj(responseCallNotify.getContent(), Responsebody.getClass());
        // System.out.println(Responsebody);
        // 返回响应的status.
        return responseCallNotify.getStatusLine().toString();
    }

    /*
     * 构造playInfoList中携带的放音内容参数 使用语音文件或者v1.0版本接口的TTS文本作为放音内容
     */
    public Map<String, Object> getplayInfo(String fileorTTS) {
        Map<String, Object> body = new HashMap<String, Object>();
        // 音频文件只支持wave格式,文件名以.wav结尾
        if (fileorTTS.endsWith(".wav")) {
            body.put("notifyVoice", fileorTTS);
        } else {
            System.out.println("Only .wav file is supported.");
        }
        return body;
    }

    /*
     * 构造playInfoList中携带的放音内容参数 使用v2.0版本接口的TTS模板作为放音内容 重构getplayInfo方法
     */
    public Map<String, Object> getplayInfo(String templateId, List<String> templateParas) {
        Map<String, Object> bodys = new HashMap<String, Object>();
        bodys.put("templateId", templateId);
        bodys.put("templateParas", templateParas);
        return bodys;
    }

    // 获取整个响应消息体
    public Map<String, String> getResponsebody() {
        return this.Responsebody;
    }

    // 获取响应消息体中的单个参数
    public String getResponsePara(String ParaName) {
        return this.Responsebody.get(ParaName);
    }
}