package com.huawei.demo;

import com.huawei.utils.Constant;
import com.huawei.utils.StringUtil;

public class VoiceCallMain {
    // 接口通用返回值
    private static String status = "";
    private static String resultcode = "";
    private static String resultdesc = "";
    // 语音回呼接口返回值
    private static String sessionId = "";
    // 语音回呼业务类实体
    public static VoiceCall voiceCallAPI = new VoiceCall();
    // 获取录音文件下载地址实体
    public static GetRecordLink recordLinkAPI = new GetRecordLink();

    // 调用接口成功标识
    private static final String success = "200";

    public static void main(String args[]) throws Exception {

        // TODO 程序前端要求发起语音回呼,调用doVoiceCall方法.
        // 以下代码仅供调试使用,实际开发时请删除
        VoiceCallMain.doVoiceCall("+8653159511234", "+8613800000001", "+8653159511234", "+8613800000002");
        if (status.indexOf(success) != -1) {
            System.out.println(status);
            System.out.println(resultcode + " " + resultdesc);
            System.out.println("The session id is: " + sessionId);
        }

        // TODO 需要接收状态和话单时,请参考"呼叫状态和话单通知API"接口实现状态通知和话单的接收和解析.
        // HostingVoiceEventDemoImpl

        // TODO 需要下载录音文件时,请参照"获取录音文件下载地址API"接口获取录音文件下载地址.
        String aksk = StringUtil.buildAKSKHeader(Constant.CLICK2CALL_APPID, Constant.CLICK2CALL_SECRET);
        String code = recordLinkAPI.getRecordLinkAPI("1200_366_0_20161228102743.wav", "ostor.huawei.com", aksk);
        if (code.indexOf("301") != -1) {
            System.out.println("The record file download link is: " + recordLinkAPI.getLocation());
        } else {
            System.out.println("code: " + code);
            System.out.println("Failed: " + recordLinkAPI.getResponsebody().toString());
        }
    }

    /*
     * 前端需要发起语音回呼时,调用此方法 该示例只仅体现必选参数,可选参数根据接口文档和实际情况配置.
     */
    public static void doVoiceCall(String displayNbr, String callerNbr, String displayCalleeNbr, String calleeNbr)
            throws Exception {

        Boolean retry = false;
        // 调用语音回呼接口,直至成功
        do {
            status = voiceCallAPI.voiceCallAPI(displayNbr, callerNbr, displayCalleeNbr, calleeNbr);
            if (status.indexOf(success) != -1) {
                retry = false;
                // 调用成功,记录返回的信息.
                resultcode = voiceCallAPI.getResponsePara("resultcode");
                resultdesc = voiceCallAPI.getResponsePara("resultdesc");
                sessionId = voiceCallAPI.getResponsePara("sessionId");
            } else {
                retry = true;
                // 调用失败,获取错误码和错误描述.
                resultcode = voiceCallAPI.getResponsePara("resultcode");
                resultdesc = voiceCallAPI.getResponsePara("resultdesc");
                // 处理错误
                VoiceCallMain.processError();
            }
        } while (retry);
    }

    // 当API的返回值不是200时,处理错误.
    private static void processError() throws InterruptedException {

        // TODO 根据错误码和错误码描述处理问题
        // 以下代码仅供调试使用,实际开发时请删除
        System.out.println(status);
        System.out.println(resultcode + " " + resultdesc);
        System.exit(-1);
    }
}