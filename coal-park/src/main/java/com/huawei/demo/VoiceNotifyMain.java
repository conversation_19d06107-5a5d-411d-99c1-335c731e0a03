package com.huawei.demo;

import java.util.*;

public class VoiceNotifyMain {
    // 接口返回值
    private static String status = "";
    private static String resultcode = "";
    private static String resultdesc = "";
    // 语音通知接口返回值
    private static String sessionId = "";
    // 语音通知业务类实体
    public static VoiceNotify callNotifyAPI = new VoiceNotify();

    // 调用接口成功标识
    private static final String success = "200";

    public static void main(String args[]) throws Exception {

        // TODO 程序前端要求发起语音通知呼叫,首先使用getplayInfo构造构造playInfoList参数,然后调用doCallNotify方法.
        // 以下代码仅供调试使用,实际开发时请删除
        // 构造playInfoList参数
        List<Map<String, Object>> playInfoList = new ArrayList<Map<String, Object>>();
        // 使用音频文件作为第一段放音内容
        playInfoList.add(callNotifyAPI.getplayInfo("test.wav"));
        // 使用v2.0版本接口的语音通知模板作为第二段放音内容
        String templateId = "test";
        List<String> templateParas = new ArrayList<String>();
        templateParas.add("3");
        templateParas.add("1栋保安亭");
        playInfoList.add(callNotifyAPI.getplayInfo(templateId, templateParas));
        // 调用doCallNotify方法
        VoiceNotifyMain.doCallNotify("+8653159511234", "+8613800000001", playInfoList);
        if (status.indexOf(success) != -1) {
            System.out.println(status);
            System.out.println(resultcode + " " + resultdesc);
            System.out.println("The session id is: " + sessionId);
        }

        // TODO 需要接收状态和话单时,请参考"呼叫状态和话单通知API"接口实现状态通知和话单的接收和解析
        // HostingVoiceEventDemoImpl
    }

    /*
     * 前端需要发起语音通知呼叫时,调用此方法 该示例只仅体现必选参数,可选参数根据接口文档和实际情况配置.
     */
    public static void doCallNotify(String displayNbr, String calleeNbr, List<Map<String, Object>> playInfoList)
            throws Exception {

        Boolean retry = false;
        // 调用语音通知接口,直至成功
        do {
            status = callNotifyAPI.callNotifyAPI(displayNbr, calleeNbr, playInfoList);
            if (status.indexOf(success) != -1) {
                retry = false;
                // 调用成功,记录返回的信息.
                resultcode = callNotifyAPI.getResponsePara("resultcode");
                resultdesc = callNotifyAPI.getResponsePara("resultdesc");
                sessionId = callNotifyAPI.getResponsePara("sessionId");
            } else {
                retry = true;
                // 调用失败,获取错误码和错误描述.
                resultcode = callNotifyAPI.getResponsePara("resultcode");
                resultdesc = callNotifyAPI.getResponsePara("resultdesc");
                // 处理错误
                VoiceNotifyMain.processError();
            }
        } while (retry);
    }

    // 当API的返回值不是200时,处理错误.
    private static void processError() throws InterruptedException {

        // TODO 根据错误码和错误码描述处理问题
        // 以下代码仅供调试使用,实际开发时请删除
        System.out.println(status);
        System.out.println(resultcode + " " + resultdesc);
        System.exit(-1);
    }
}