package com.huawei.demo;

public class VoiceVerificationCodeMain {
    // 接口通用返回值
    private static String status = "";
    private static String resultcode = "";
    private static String resultdesc = "";
    // 语音验证码接口返回值
    private static String sessionId = "";

    // 语音验证码业务类实体
    public static VoiceVerificationCode voiceVerificationCodeAPI = new VoiceVerificationCode();

    // 调用接口成功标识
    private static final String success = "200";

    public static void main(String args[]) throws Exception {

        // TODO 程序前端要求发起语音验证码呼叫,调用doVoiceVerificationCode方法.
        // 以下代码仅供调试使用,实际开发时请删除
        VoiceVerificationCodeMain.doVoiceVerificationCode("+8653159511234", "+8613800000001", 2, "test.wav", "1234");
        if (status.indexOf(success) != -1) {
            System.out.println(status);
            System.out.println(resultcode + " " + resultdesc);
            System.out.println("The session id is: " + sessionId);
        }

        // TODO 需要接收状态和话单时,请参考"呼叫状态和话单通知API"接口实现状态通知和话单的接收和解析
        // HostingVoiceEventDemoImpl
    }

    /*
     * 前端需要发起语音验证码呼叫时,调用此方法 该示例只仅体现必选参数,可选参数根据接口文档和实际情况配置.
     */
    public static void doVoiceVerificationCode(String displayNbr, String calleeNbr, int languageType, String preTone,
            String verifyCode) throws Exception {

        Boolean retry = false;
        // 调用语音验证码接口,直至成功
        do {
            status = voiceVerificationCodeAPI.voiceVerificationCodeAPI(displayNbr, calleeNbr, languageType, preTone,
                    verifyCode);
            if (status.indexOf(success) != -1) {
                retry = false;
                // 调用成功,记录返回的信息.
                resultcode = voiceVerificationCodeAPI.getResponsePara("resultcode");
                resultdesc = voiceVerificationCodeAPI.getResponsePara("resultdesc");
                sessionId = voiceVerificationCodeAPI.getResponsePara("sessionId");
            } else {
                retry = true;
                // 调用失败,获取错误码和错误描述.
                resultcode = voiceVerificationCodeAPI.getResponsePara("resultcode");
                resultdesc = voiceVerificationCodeAPI.getResponsePara("resultdesc");
                // 处理错误
                VoiceVerificationCodeMain.processError();
            }
        } while (retry);
    }

    // 当API的返回值不是200时,处理错误.
    private static void processError() throws InterruptedException {

        // TODO 根据错误码和错误码描述处理问题
        // 以下代码仅供调试使用,实际开发时请删除
        System.out.println(status);
        System.out.println(resultcode + " " + resultdesc);
        System.exit(-1);
    }
}