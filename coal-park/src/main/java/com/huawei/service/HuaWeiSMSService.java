package com.huawei.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloud.apigateway.sdk.utils.Client;
import com.cloud.apigateway.sdk.utils.Request;
import com.huawei.apig.sdk.util.Constant;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class HuaWeiSMSService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HuaWeiSMSService.class);

    public static final String UTF_8 = "UTF-8";

//    private static CloseableHttpClient client =  null;

    public static void main(String[] args) throws Exception {
        // 为防止因HTTPS证书认证失败造成API调用失败,需要先忽略证书信任问题
//        client = createIgnoreSSLHttpClient();
//        sendSms();
    }

    // 验证码${1}，您正在进行身份验证，打死不要告诉别人哦！
    public static String sendSmsYanZhengMa(String receiver, String templateParas) {
        return sendSms("a4cbc68cc0004b1e95c66984a1489cf3", receiver, templateParas, "8824041828722");
    }

    // ${1}需修改信息，收款人${2}，收费${3}，验证码${4}
    public static String sendSmsThirdParty(String receiver, String templateParas) {
        return sendSms("0ca476566e67489aa164cbd4fe17246e", receiver, templateParas, "8824010210006");
    }

    // 车牌号为${plateNumber}的司机，您已接单，请至${place}完成${name}运输。
    public static void sendSmsJieDan(String receiver, String templateParas) {
        sendSms("e18627cff3ab415e916d68cfde7028b5", receiver, templateParas, "8824010210006");
    }

    // 车号${plateNumber}的司机，您已在${place}完成${name}装货，毛重${grossWeight}，皮重${tareWeight}
    public static void sendSmsZhuangHuo(String receiver, String templateParas) {
        sendSms("1d21d791077049a28ef85466afb39e6a", receiver, templateParas, "8824010210006");
    }

    // 车号${plateNumber}的司机，您已在${place}完成${name}卸货，毛重${grossWeight}，皮重${tareWeight}
    public static void sendSmsXieHuo(String receiver, String templateParas) {
        sendSms("ac6fc7979f8b4fc088af66be3ab23afd", receiver, templateParas, "8824010210006");
    }

    // templateParas:如模板内容为“您有${1}件快递请到${2}领取”时，该参数可填写为'["3","人民公园正门"]'
    private static String sendSms(String templateId, String receiver, String templateParas, String sender) {
        //必填,请参考"开发准备"获取如下数据,替换为实际值
        String url = "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1";
        //"https://smsapi.ap-southeast-1.myhuaweicloud.com:443/sms/batchSendSms/v1"; //APP接入地址+接口访问URI
        // 认证用的appKey和appSecret硬编码到代码中或者明文存储都有很大的安全风险，建议在配置文件或者环境变量中密文存放，使用时解密，确保安全；
        String appKey = "mMnoVMT2aK9BNnr021MiSy9KbBh1"; //Application Key
        String appSecret = "zhZpR3qNH8P4EJZh7j3TlOEyEmrn"; //Application Secret
        // String sender = "8824010210006";//"csms12345678"; //中国大陆短信签名通道号或全球短信通道号
        // String templateId = "8ff55eac1d0b478ab3c06c3c6a492300"; //模板ID

        //条件必填,中国大陆短信关注,当templateId指定的模板类型为通用模板时生效且必填,必须是已审核通过的,与模板类型一致的签名名称
        //全球短信不用关注该参数
        String signature = "黑金宝司机"; //签名名称

        //必填,全局号码格式(包含国家码),示例:+86151****6789,多个号码之间用英文逗号分隔
        //String receiver = "+86151****6789,+86152****7890"; //短信接收人号码
        receiver = "+86" + receiver; //短信接收人号码

        //选填,短信状态报告接收地址,推荐使用域名,为空或者不填表示不接收状态报告
        String statusCallBack = "";

        /**
         * 选填,使用无变量模板时请赋空值 String templateParas = "";
         * 单变量模板示例:模板内容为"您的验证码是${NUM_6}"时,templateParas可填写为"[\"111111\"]"
         * 双变量模板示例:模板内容为"您有${NUM_2}件快递请到${TXT_20}领取"时,templateParas可填写为"[\"3\",\"人民公园正门\"]"
         * 查看更多模板规范和变量规范:产品介绍>短信模板须知和短信变量须知
         */
        // String templateParas = "[\"111111\"]"; //模板变量，此处以单变量验证码短信为例，请客户自行生成6位验证码，并定义为字符串类型，以杜绝首位0丢失的问题（例如：002569变成了2569）。
        try {
            //请求Body,不携带签名名称时,signature请填null
            String body = buildRequestBody(sender, receiver, templateId, templateParas, statusCallBack, signature);
            if (null == body || body.isEmpty()) {
                LOGGER.warn("body is null.");
                return "body is null.";
            }

            Request request = new Request();
            request.setKey(appKey);
            request.setSecret(appSecret);
            request.setMethod("POST");
            request.setUrl(url);
            request.addHeader("Content-Type", "application/x-www-form-urlencoded");
            request.setBody(body);
            //LOGGER.info("Print the body: {}", body);

            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (x509CertChain, authType) -> true).build();
            CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE)).build();

            HttpRequestBase signedRequest = Client.sign(request, Constant.SIGNATURE_ALGORITHM_SDK_HMAC_SHA256);
            // LOGGER.info("Print the authorization: {}", Arrays.toString(signedRequest.getHeaders("Authorization")));
            Header[] requestAllHeaders = signedRequest.getAllHeaders();
            //for (Header h : requestAllHeaders) {
            //LOGGER.info("req Header with name: {} and value: {}", h.getName(), h.getValue());
            //}

            HttpResponse response = client.execute(signedRequest);

            //LOGGER.info("Print the status line of the response: {}", response.getStatusLine().toString());
            Header[] resHeaders = response.getAllHeaders();
            //for (Header h : resHeaders) {
            //LOGGER.info("Processing Header with name: {} and value: {}", h.getName(), h.getValue());
            //}
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                String a = EntityUtils.toString(resEntity, "UTF-8");
                JSONObject object = JSON.parseObject(a);
                String code = object.getString("code");
                if (!"000000".equals(code)) {   //发送短信失败，打印日志
                    LOGGER.error("================================== sendSms - huawei ================================");
                    LOGGER.error("====== templateParas : " + templateParas);
                    LOGGER.error("====== resEntity : " + a);
                    return a;
                }
                return null;
                //LOGGER.info("Processing Body with name: {} and value: {}", System.getProperty("line.separator"), EntityUtils.toString(resEntity, "UTF-8"));
            }

            return "返回结果为空";
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return e.getMessage();
        }
    }

    public static CloseableHttpClient createIgnoreSSLHttpClient() throws Exception {
        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (x509CertChain, authType) -> true).build();
        return HttpClients.custom().setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE)).build();
    }

    static String buildRequestBody(String sender, String receiver, String templateId, String templateParas, String statusCallBack, String signature) throws UnsupportedEncodingException {
        if (null == sender || null == receiver || null == templateId || sender.isEmpty() || receiver.isEmpty() || templateId.isEmpty()) {
            System.out.println("buildRequestBody(): sender, receiver or templateId is null.");
            return null;
        }

        StringBuilder body = new StringBuilder();
        appendToBody(body, "from=", sender);
        appendToBody(body, "&to=", receiver);
        appendToBody(body, "&templateId=", templateId);
        appendToBody(body, "&templateParas=", templateParas);
        appendToBody(body, "&statusCallback=", statusCallBack);
        appendToBody(body, "&signature=", signature);
        return body.toString();
    }

    private static void appendToBody(StringBuilder body, String key, String val) throws UnsupportedEncodingException {
        if (null != val && !val.isEmpty()) {
            //LOGGER.info("Print appendToBody: {}:{}", key, val);
            body.append(key).append(URLEncoder.encode(val, UTF_8));
        }
    }
}
