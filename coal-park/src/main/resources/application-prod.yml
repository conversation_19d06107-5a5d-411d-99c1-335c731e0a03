spring:
  application:
    name: COAL-PARK

  profiles:
    include:
      - tra-prod

  # 数据库 =============================================================================================================
  data:
    mongodb:
      database: coal
      uri: mongodb://coal:coalMongo#<EMAIL>:3717,dds-8vb0b1aee4fad0e42.mongodb.zhangbei.rds.aliyuncs.com:3717/coal?replicaSet=mgset-500654601

# 自定义配置
coal:
  #1.系统相关:
  session-timeout: 1800
  upload-path: /root/uploadsv2/
  log-path: /root/projectv2/logs/
  white-list:
  black-list:

ocr:
  language-path: /root/
  opencv-lib: /root/opencv/share/java/opencv4/libopencv_java410.so
  test-file-path: /root/