server:
  port: 8080
  servlet:
    session:
      tracking-modes: cookie
      cookie:
        http-only: true

management:
  endpoint:
    shutdown:
      enabled: true  #启用shutdown
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: / # 自定义管理端点的前缀(保证安全)
  server:
    port: 9090
    address: 127.0.0.1 # 不允许远程管理连接(不允许外部调用保证安全)spring:

# JWT配置
jwt:
  # 密匙KEY
  secret: JWTSecretV2
  # HeaderKEY
  tokenHeader: x-access-token-v2
  # 过期时间 单位秒 1天后过期=86400 7天后过期=604800
  # expiration: 86400
  # 业务系统用户
  expBiz: 86400
  # Web 用户
  expWeb: 86400
  # 客商用户
  expCU: 86400
  # 司机用户
  expDU: 864000
  # 微信小程序
  expWechat: 86400

  # 配置不需要认证的接口
  #antMatchers: /web/sys/user/login,/api/**
  antMatchers:
    - /webjars/**
    - /images/**
    - /websocket/**
    - /configuration/**
    - /swagger**/**
    - /we_chat/small_pro/**
    - /api/manage/**
    - /web/sys/user/login
    - /api/bus/biz_login
    - /api/bus/biz_send_sms
    - /api/test/**
    - /web/test/**
    - /web/file/**
    - /api/file/**
    - /web/log/**
    - /api/bus/receive_car_msg/syn_quarantine

  # 签名KEY
  signSecret: ht123456
  # 签名允许间隔时间
  signDiffTime: 600000
  # 不需要验签的接口
  signExcludeMatchers:
    - /api/file/**
    - /api/bus/**
    - /web/**
    - /api/manage/app/download_app
    - /api/manage/sms
    - /api/manage/sms2
    - /api/manage/get_img
    - /api/manage/check_img
    - /api/manage/dic/car_info
    - /api/manage/wxpay/notify
    - /api/manage/wxpay/notify_cost

  # 部分接口防抖动 接口访问间隔时间（毫秒）
  expTime1: 10000      # /api/cus/goods/add_goods2
  expTime2: 10000      # /api/cus/goods/del_goods2
  expTime3: 10000      # /api/cus/goods/add_goods_pl
  expTime4: 10000      # /api/cus/goods/update_Goods2
  expTime5: 10000      # /api/cus/goods/update_goods_place2
  expTime6: 10000      # /api/cus/goods/goods_to_friend2
  expTime7: 10000      # /api/cus/goods/regain_friend_goods2
  expTime8: 10000      # /api/cus/goods/back_friend_goods2
  expTime9: 10000      # /api/cus/order/update_orders2
  expTime10: 10000     # /api/cus/order/update_orders_place2
  expTime11: 10000     # /api/cus/order/appoint_order_car2
  expTime12: 10000     # /api/dvr/order/design_carNum2
  expTime13: 10000     # /api/dvr/order/sweep_code2
  expTime14: 10000     # /api/dvr/order/grab
  expTime15: 10000     # /api/dvr/order/appointment
  expTime16: 10000     # /api/dvr/order/update_order_carnum
  expTime17: 10000     # /api/dvr/order/punch_clock
  expTime18: 500     # /api/dvr/order/upload_quarantine_pho
  expTime19: 10000     # /api/dvr/order/add_quarantine
  expTime20: 10000     # /api/dvr/push/save_delete_order2

spring:
  messages:
    encoding: UTF-8
  http:
    encoding:
      force: true
      charset: UTF-8
      enabled: true
  profiles:
    active: dev