spring:
  application:
    name: COAL-PARK

  profiles:
    include:
    - tra-test

  # 数据库 =============================================================================================================
  data:
    mongodb:
      database: db_coal_test
      uri: mongodb://127.0.0.1:27017,127.0.0.1:27018,127.0.0.1:27019/?replicaSet=replset&readPreference=secondaryPreferred&connectTimeoutMS=300000&maxIdleTimeMS=30000

# 自定义配置
coal:
  #1.系统相关:
  session-timeout: 1800
  upload-path: /root/uploads/
  white-list:
  black-list: