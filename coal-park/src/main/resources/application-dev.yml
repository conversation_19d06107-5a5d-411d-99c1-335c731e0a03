spring:
  application:
    name: COAL-PARK

  profiles:
    include:
      - tra-dev

  # 数据库 =============================================================================================================
  data:
    mongodb:
      database: de_coal_dev4
      uri: mongodb://127.0.0.1:27017
      #/?replicaSet=replset&readPreference=secondaryPreferred&connectTimeoutMS=300000&maxIdleTimeMS=600000
      # uri: ****************************************************************************************************************************************************************

# 自定义配置
coal:
  #1.系统相关:
  session-timeout: 1800
  upload-path: D:\log\upload\
  log-path: D:\log\
  white-list:
  black-list:

ocr:
  language-path: D:\tessdata-main\
  opencv-lib: D:\opencv-lib\x64\opencv_java410.dll
  test-file-path: D:\Desktop\ocr\
