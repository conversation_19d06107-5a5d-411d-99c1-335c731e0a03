SpringBoot 项目结构说明
======

示例表 SQL 语句:

drop table if exists sample;
create table sample (
   id                   serial not           null,
   text                 VARCHAR(32)          null,
   content              VARCHAR(32)          null,
   constraint PK_SAMPLE primary key (id)
);
comment on table sample is '示例表';
comment on column sample.id is 'ID';
comment on column sample.text is '文本';
comment on column sample.content is '内容';

1, 环境及组件版本:
---

JDK: 1.8<br>
build.sourceEncoding: UTF-8<br>
reporting.outputEncoding: UTF-8<br>

SpringBoot                      2.0.5<br>
MySQL driver version            8.0.11<br>
PostgreSQL driver version       42.2.5<br>
MyBatis                         1.3.2<br>
    generator version           1.3.2<br>
        maven plugin version    1.3.5<br>
pagehelper version              5.1.5<br>
    springboot version          1.2.5<br>
swagger2 version                2.5.0<br>
alibaba<br>
    fastjson version            1.2.4<br>
    druid version               1.1.11<br>
logbak version                  4.10<br>
thymeleaf                       3.0.1<br>
jwt<br>

2, 测试中断言用法JUnit4(assert):
---
        /**数值匹配**/
        //测试变量是否大于指定值
        assertThat(test1.getShares(), greaterThan(50));
        //测试变量是否小于指定值
        assertThat(test1.getShares(), lessThan(100));
        //测试变量是否大于等于指定值
        assertThat(test1.getShares(), greaterThanOrEqualTo(50));
        //测试变量是否小于等于指定值
        assertThat(test1.getShares(), lessThanOrEqualTo(100));

        //测试所有条件必须成立
        assertThat(test1.getShares(), allOf(greaterThan(50),lessThan(100)));
        //测试只要有一个条件成立
        assertThat(test1.getShares(), anyOf(greaterThanOrEqualTo(50), lessThanOrEqualTo(100)));
        //测试无论什么条件成立(还没明白这个到底是什么意思)
        assertThat(test1.getShares(), anything());
        //测试变量值等于指定值
        assertThat(test1.getShares(), is(100));
        //测试变量不等于指定值
        assertThat(test1.getShares(), not(50));

        /**字符串匹配**/
        String url = "http://www.baidu.com";
        //测试变量是否包含指定字符
        assertThat(url, containsString("taobao"));
        //测试变量是否已指定字符串开头
        assertThat(url, startsWith("http://"));
        //测试变量是否以指定字符串结尾
        assertThat(url, endsWith(".com"));
        //测试变量是否等于指定字符串
        assertThat(url, equalTo("http://www.baidu.com"));
        //测试变量再忽略大小写的情况下是否等于指定字符串
        assertThat(url, equalToIgnoringCase("http://www.baidu.com"));
        //测试变量再忽略头尾任意空格的情况下是否等于指定字符串
        assertThat(url, equalToIgnoringWhiteSpace("http://www.baidu.com"));

3, 接口开发说明:
---

A, 业务推送数据接口: com.erdos.coal.park.controller.business<br>
映射路径为: /bus 开头<br>

B, 客商数据接口: com.erdos.coal.park.controller.customer<br>
映射路径为: /cus 开头<br>

C, 司机数据接口: com.erdos.coal.park.controller.driver<br>
映射路径为: /dvr 开头<br>



