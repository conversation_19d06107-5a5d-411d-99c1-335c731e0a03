<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.erdos.coal</groupId>
    <artifactId>coal-project</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <name>coal-project</name>
    <description>Spring Boot for coal project</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.3.RELEASE</version>
        <relativePath/>
        <!-- lookup parent from repository -->
    </parent>

    <modules>
        <module>coal-common/coal-core</module>
        <module>coal-common/coal-ds-mongo</module>
        <module>coal-common/coal-aliyun</module>
        <module>coal-common/coal-map</module>
        <module>coal-common/coal-ocr</module>
        <module>coal-common/coal-transaction</module>
        <module>coal-common/coal-crawler</module>

        <module>coal-park</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <coal.core.version>1.0-SNAPSHOT</coal.core.version>
        <coal.ds.version>1.0-SNAPSHOT</coal.ds.version>
        <coal.aliyun.version>1.0-SNAPSHOT</coal.aliyun.version>
        <coal.ocr.version>1.0-SNAPSHOT</coal.ocr.version>
        <coal.map.version>1.0-SNAPSHOT</coal.map.version>

        <!--<pagehelper.version>5.1.5</pagehelper.version>-->
        <!--<pagehelper.springboot.version>1.2.5</pagehelper.springboot.version>-->

        <!--ORM映射依赖, 参考: https://morphia.dev/ -->
        <!--<morphia.version>1.4.1</morphia.version>-->
        <morphia.version>1.5.8</morphia.version>

        <alibaba.fastjson.version>1.2.83</alibaba.fastjson.version>
        <alibaba.druid.version>1.1.11</alibaba.druid.version>

        <!--swagger2.version>2.9.2</swagger2.version>
        <swagger2.am.version>1.5.21</swagger2.am.version>-->

        <jjwt.version>0.9.1</jjwt.version>

        <logbak.version>4.10</logbak.version>
    </properties>

    <!-- 依赖仓库 设置从aliyun仓库下载  -->
    <repositories>
        <repository>
            <id>alimaven</id>
            <url>
                http://maven.aliyun.com/nexus/content/repositories/central/
            </url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <!--  插件依赖仓库  -->
    <pluginRepositories>
        <pluginRepository>
            <id>alimaven</id>
            <url>
                http://maven.aliyun.com/nexus/content/repositories/central/
            </url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>compile</scope>
        </dependency>

        <!--测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <!--排除 android-json-->
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--  alibaba JSON 支持 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${alibaba.fastjson.version}</version>
        </dependency>

    </dependencies>

</project>
