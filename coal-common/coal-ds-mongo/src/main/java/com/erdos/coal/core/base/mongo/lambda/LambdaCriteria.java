package com.erdos.coal.core.base.mongo.lambda;

import org.springframework.data.mongodb.core.query.Criteria;

public class LambdaCriteria<T> extends Criteria implements IMongoCriteria {

    public LambdaCriteria() {
        super();
    }

    public LambdaCriteria(SFunction<T, ?> function) {
        super(IMongoCriteria.getKey(function));
    }

//    protected LambdaCriteria(List<Criteria> criteria<PERSON>hain, String key) {
//        super(criteria<PERSON><PERSON><PERSON>, key);
//    }

    public Criteria and(SFunction<T, ?> function) {
        return super.and(IMongoCriteria.getKey(function));
    }

    public Criteria where(SFunction<T, ?> function) {
        return where(IMongoCriteria.getKey(function));
    }

}
