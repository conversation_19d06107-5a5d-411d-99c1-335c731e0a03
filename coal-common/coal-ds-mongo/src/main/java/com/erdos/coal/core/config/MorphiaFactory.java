package com.erdos.coal.core.config;

import com.mongodb.Mongo;
import com.mongodb.MongoClient;
import dev.morphia.Datastore;
import dev.morphia.Morphia;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.mongo.MongoProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.MongoTransactionManager;

@Configuration
@ConditionalOnClass(Mongo.class)
public class MorphiaFactory {

    private MongoClient mongoClient;

    private MongoProperties mongoProperties;

    @Autowired
    public MorphiaFactory(MongoClient mongoClient, MongoProperties mongoProperties) {
        this.mongoClient = mongoClient;
        this.mongoProperties = mongoProperties;
    }

    @Bean(name = "dsForCoal")
    public Datastore get() {
        Morphia morphia = new Morphia();
//        String pkg = "com.erdos.coal.park.%s.%s.entity";
//
//        morphia
//                .mapPackage(String.format(pkg, "api", "business"))
//                .mapPackage(String.format(pkg, "api", "customer"))
//                .mapPackage(String.format(pkg, "api", "driver"))
//                .mapPackage(String.format(pkg, "api", "manage"))
//
//                .mapPackage(String.format(pkg, "web", "app"))
//                .mapPackage(String.format(pkg, "web", "sys"));

        Datastore datastore = morphia.createDatastore(mongoClient, mongoProperties.getDatabase());
//        datastore.ensureIndexes();
//        datastore.ensureCaps();

        return datastore;
    }

    @Bean
    MongoTransactionManager transactionManager(MongoDbFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }
}
