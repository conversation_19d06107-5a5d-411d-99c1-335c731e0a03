package com.erdos.coal.core.base.mongo.entity;

import java.io.Serializable;

// 模拟 GeoJsonPoint
// 如果直接使用 SpringData.GeoJsonPoint，Morphia 不能正确构造而产生错误
// 因为 SpringData.GeoJsonPoint 对象没有无参构造方法
//
//    "point": {
//        "type": "Point",
//        "coordinates": [
//            116.397469,
//            39.908821
//        ]
//    }

public class GeoPoint implements Serializable {

    private String type = "Point";
    private double[] coordinates;

    public GeoPoint() {
    }

    public GeoPoint(double[] coordinates) {
        this.coordinates = coordinates;
    }

    public GeoPoint(double lon, double lat) {
        this.coordinates = new double[]{lon, lat};
    }

    public String getType() {
        return type;
    }

    public GeoPoint setType(String type) {
        this.type = type;
        return this;
    }

    public double[] getCoordinates() {
        return coordinates;
    }

    public GeoPoint setCoordinates(double[] coordinates) {
        this.coordinates = coordinates;
        return this;
    }
}
