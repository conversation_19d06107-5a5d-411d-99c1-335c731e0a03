package com.erdos.coal.core.base.mongo.lambda;

public final class ClassUtils {

    private static ClassLoader systemClassLoader;

    private ClassUtils() {
    }

    public static Class<?> toClassConfident(String name) {
        return toClassConfident(name, null);
    }

    public static Class<?> toClassConfident(String name, ClassLoader classLoader) {
        try {
            return loadClass(name, getClassLoaders(classLoader));
        } catch (ClassNotFoundException var3) {
            throw new RuntimeException("找不到指定的class！请仅在明确确定会有 class 的时候，调用该方法");
        }
    }

    private static Class<?> loadClass(String className, ClassLoader[] classLoaders) throws ClassNotFoundException {
        ClassLoader[] var2 = classLoaders;
        int var3 = classLoaders.length;

        for (int var4 = 0; var4 < var3; ++var4) {
            ClassLoader classLoader = var2[var4];
            if (classLoader != null) {
                try {
                    return Class.forName(className, true, classLoader);
                } catch (ClassNotFoundException var7) {
                }
            }
        }

        throw new ClassNotFoundException("Cannot find class: " + className);
    }

    private static ClassLoader[] getClassLoaders(ClassLoader classLoader) {
        return new ClassLoader[]{
                classLoader,
                // Resources.getDefaultClassLoader(),
                Thread.currentThread().getContextClassLoader(),
                // com.baomidou.mybatisplus.core.toolkit.ClassUtils.class.getClassLoader(),
                systemClassLoader
        };
    }

    static {
        try {
            systemClassLoader = ClassLoader.getSystemClassLoader();
        } catch (SecurityException var1) {
        }

    }
}
