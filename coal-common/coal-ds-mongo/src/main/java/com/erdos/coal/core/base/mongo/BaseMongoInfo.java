package com.erdos.coal.core.base.mongo;

import com.erdos.coal.core.annotation.Fixed;
import dev.morphia.annotations.Id;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;

public class BaseMongoInfo implements Serializable {

    public static final String ID_KEY = "_id";

    @Id
    @Fixed
    private ObjectId objectId;

    @Fixed
    private Date createTime;

    private Long updateTime;

    public ObjectId getObjectId() {
        return objectId;
    }

    public void setObjectId(ObjectId objectId) {
        this.objectId = objectId;
    }

    /*public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }*/

//    public Long getCreateTime() {
//        //5c6f56552f325e0990013d64
//        //Mongodb中 _id 中包含生成时间, 为字段中的前 8 位 16 进制
//        //转换为10进制
//        try {
//            if (id != null && !id.isEmpty() && id.length() > 8) {
//                return Long.parseLong(id.substring(0, 8), 16);
//            }
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

//    public static void main(String[] args) {
//        BaseMongoInfo bi = new BaseMongoInfo();
//        bi.id = "5c6f56552f325e0990013d64";
//    }
}
