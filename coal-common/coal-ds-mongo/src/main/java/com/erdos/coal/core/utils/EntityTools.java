package com.erdos.coal.core.utils;

import dev.morphia.annotations.Entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class EntityTools {

    public static Set<Class<?>> getEntityCollectionClass(String basePackage) {
        return ReflectHelper.getCollectionClassByAnnotation(basePackage, Entity.class);
    }

    public static List<String> getCollectionNamesByEntityCollectionClass(Set<Class<?>> classSet) {
        List<String> list = new ArrayList<>();
        for (Class<?> cls : classSet) {
            if (cls.isAnnotationPresent(Entity.class)) {
                Entity entity = cls.getAnnotation(Entity.class);
                String collName = entity.value();

                list.add(collName);
            }
        }
        return list;
    }

}
