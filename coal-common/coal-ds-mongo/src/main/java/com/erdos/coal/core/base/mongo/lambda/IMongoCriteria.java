package com.erdos.coal.core.base.mongo.lambda;

import dev.morphia.annotations.Property;

import java.io.*;
import java.lang.reflect.Field;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public interface IMongoCriteria<T> {
    /**
     * 方法名转字段名，直接copy mybatis-plus 的代码
     * 其实就是按java bean的规范，先把get、set、is前缀去掉，然后第二个字符如果不是大写，就把第一个转小写
     *
     * @param name
     * @return
     */
    static String methodToProperty(String name) {
        if (name.startsWith("is")) {
            name = name.substring(2);
        } else if (name.startsWith("get") || name.startsWith("set")) {
            name = name.substring(3);
        } else {
            throw new RuntimeException("Error parsing property name '" + name + "'.  Didn't start with 'is', 'get' or 'set'.");
        }

        if (name.length() == 1 || (name.length() > 1 && !Character.isUpperCase(name.charAt(1)))) {
            name = name.substring(0, 1).toLowerCase(Locale.ENGLISH) + name.substring(1);
        }

        return name;
    }

    /**
     * 序列化
     *
     * @param function
     * @return
     */
    static <T> byte[] serialize(SFunction<T, ?> function) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream(1024);
        try (ObjectOutputStream oos = new ObjectOutputStream(baos)) {
            oos.writeObject(function);
            oos.flush();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return baos.toByteArray();
    }

    /**
     * 反序列化
     *
     * @param bytes
     * @return
     */
    static SerializedLambda deserialize(byte[] bytes) {
        try (ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(bytes)) {
            @Override
            protected Class<?> resolveClass(ObjectStreamClass objectStreamClass) throws IOException, ClassNotFoundException {
                return objectStreamClass.getName().equals(java.lang.invoke.SerializedLambda.class.getName())
                        ? SerializedLambda.class
                        : super.resolveClass(objectStreamClass);
            }
        }) {
            return (SerializedLambda) ois.readObject();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    Map<SFunction<?, ?>, String> cache = new ConcurrentHashMap<>();

    static <T> String getKey(SFunction<T, ?> function) {
        return getCacheField(function);
    }

    static String getCacheField(SFunction<?, ?> function) {
        return cache.computeIfAbsent(function, IMongoCriteria::getPropertyName);
    }

    static <T> String getPropertyName(SFunction<T, ?> function) {
        SerializedLambda lambda = deserialize(serialize(function));
        // lambda.getImplMethodName() // getName
        // lambda.getImplMethodSignature() // java.lang.String
        String key = methodToProperty(lambda.getImplMethodName());
        // String key = Introspector.decapitalize(lambda.getImplMethodName().replace("get", ""));

        // todo: 还需判断字段别名
        // spring data mongo 为 @Field, morphia 为 @Property
        for (Field field : lambda.getImplClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(Property.class) && key.equals(field.getName())) {
                Property annotation = field.getAnnotation(Property.class);
                if (!annotation.value().isEmpty())
                    return annotation.value();
            }
        }

        return key;
    }

}
