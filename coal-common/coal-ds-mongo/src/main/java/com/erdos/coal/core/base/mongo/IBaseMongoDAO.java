package com.erdos.coal.core.base.mongo;

import com.erdos.coal.core.base.mongo.lambda.IMongoCriteria;
import com.erdos.coal.core.base.mongo.lambda.QueryWrapper;
import com.erdos.coal.core.base.mongo.lambda.SFunction;
import com.erdos.coal.core.common.EGridResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import dev.morphia.UpdateOptions;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.*;
import org.bson.Document;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.erdos.coal.core.base.mongo.BaseMongoInfo.ID_KEY;

/**
 * Created by LIGX on 2018/12/6.
 */
public interface IBaseMongoDAO<T extends BaseMongoInfo> extends IMongoCriteria<T> {

    /**
     * 向集合中添加文档
     *
     * @param obj
     * @return
     */
    T save(T obj);

    /**
     * 向集合中批量添加文档
     *
     * @param obj
     */
    void save(List<T> obj);

    // AdvancedDatastore 中的方法, 建议直接使用 Datastore, Datastore 中没有insert方法
    //T insert(T obj);

    //TODO: -------------------------------------------------------------------------------------------------

    /**
     * 修改
     *
     * @param query
     * @param updateOperations
     * @return
     */
    T findAndModify(Query<T> query, UpdateOperations<T> updateOperations);

    T findAndModify(QueryWrapper<T> wrapper, UpdateOperations<T> operations);

    /**
     * 修改
     * options.upsert 如果没有找到符合条件的数据, 是否插入
     * options.multi 是否更新多条符合条件的数据
     *
     * @param query
     * @param updateOperations
     * @param options
     * @return
     */
    UpdateResults update(Query<T> query, UpdateOperations<T> updateOperations, UpdateOptions options);

    UpdateResults update(QueryWrapper<T> wrapper, UpdateOperations<T> operations, UpdateOptions options);

    UpdateResults update(Query<T> query, UpdateOperations<T> updateOperations);

    UpdateResults update(QueryWrapper<T> wrapper, UpdateOperations<T> operations);

    @Deprecated
    default UpdateResults updateOne(Query<T> query, UpdateOperations<T> updateOperations) {
        return this.update(query, updateOperations, new UpdateOptions().multi(false));
    }

    default UpdateResults updateOne(QueryWrapper<T> wrapper, UpdateOperations<T> updateOperations) {
        return this.update(wrapper.getQuery(), updateOperations, new UpdateOptions().multi(false));
    }

    UpdateResults update(Map<String, Object> conditionFieldMap, Map<String, Object> updateFieldMap);

    //TODO: -------------------------------------------------------------------------------------------------

    /**
     * 获取集合中的某一对象
     *
     * @param query
     * @return
     */
    @Deprecated
    default T get(Query<T> query) {
        return query.first();
    }

    default T get(QueryWrapper<T> wrapper) {
        return wrapper.first();
    }

    T get(Map<String, Object> conditionFieldMap);

    /**
     * 获取集合中的某一对象
     *
     * @param oid
     * @return
     */
    default T get(ObjectId oid) {
        return this.get(this.createQuery().filter(ID_KEY, oid));
    }

    /**
     * 获取集合中的某一对象
     *
     * @param field
     * @param value
     * @return
     */
    // @Deprecated
    default T get(String field, Object value) {
        return this.get(this.createQuery().filter(field, value));
    }

    default T get(SFunction<T, ?> function, Object value) {
        return this.get(this.createQuery().filter(IMongoCriteria.getKey(function), value));
    }

    /**
     * 获取集合中的某一对象
     *
     * @param oid
     * @return
     */
    default T getByPK(ObjectId oid) {
        return this.get(this.createQuery().filter(ID_KEY, oid));
    }

    /**
     * 获取集合中的某一对象
     *
     * @param oid
     * @return
     */
    default T getByPK(String oid) {
        return this.get(this.createQuery().filter(ID_KEY, new ObjectId(oid)));
    }

    /**
     * 查询集合 指定条件的数据
     * 废弃: 使用 query.find().toList()
     *
     * @param query
     * @return
     */
    @Deprecated
    default List<T> list(Query<T> query) {
        return query.find().toList();
    }

    /**
     * 废弃: 使用 query.find(findOptions).toList()
     *
     * @param query
     * @param findOptions
     * @return
     */
    @Deprecated
    default List<T> list(Query<T> query, FindOptions findOptions) {
        return query.find(findOptions).toList();
    }

    /**
     * 查询集合所有数据
     * 废弃: 使用 query.find().toList()
     *
     * @return
     */
    @Deprecated
    default List<T> list() {
        // 是否应该限制最大值
        //FindOptions options = new FindOptions();
        //options.limit(100_000); // 限制最大值
        return this.createQueryWrapper().getQuery().find().toList();
    }

    default List<T> list(QueryWrapper<T> wrapper) {
        return wrapper.getQuery().find().toList();
    }

    default List<T> list(QueryWrapper<T> wrapper, FindOptions options) {
        return wrapper.getQuery().find(options).toList();
    }

    /**
     * 查询集合某个字段包含某些特定值
     *
     * @param field  字段
     * @param values 值列表
     * @return
     */
    default List<T> listByIn(String field, Object... values) {
        List<Object> list = new ArrayList<>();
        Collections.addAll(list, values);
        return this.createQuery().field(field).in(list).find().toList();
    }

    /**
     * 查询集合某个字段不包含某些特定值
     *
     * @param field  字段
     * @param values 值列表
     * @return
     */
    default List<T> listByNotIn(String field, Object... values) {
        List<Object> list = new ArrayList<>();
        Collections.addAll(list, values);
        return this.createQuery().field(field).notIn(list).find().toList();
    }

    @Deprecated
    List<T> list(Map<String, Object> conditionFieldMap, Sort... sorts);

    //TODO: -------------------------------------------------------------------------------------------------

    /**
     * 删除文档
     *
     * @param query
     * @return
     */
    T findAndDelete(Query<T> query);

    T findAndDelete(QueryWrapper<T> wrapper);

    /**
     * 删除文档
     *
     * @param query
     * @return
     */
    int delete(Query<T> query);

    int delete(QueryWrapper<T> wrapper);

    /**
     * 删除文档
     *
     * @param oid
     * @return
     */
    default T delete(String oid) {
        return this.findAndDelete(this.createQuery().filter(ID_KEY, new ObjectId(oid)));
    }

    /**
     * 删除文档
     *
     * @param oid
     * @return
     */
    default T delete(ObjectId oid) {
        return this.delete(oid.toString());
    }

    /**
     * 删除文档
     *
     * @param key
     * @param value
     * @return
     */
    @Deprecated
    default T delete(String key, Object value) {
        return this.findAndDelete(this.createQuery().filter(key, value));
    }

    default T delete(SFunction<T, ?> function, Object value) {
        return this.findAndDelete(this.createQuery().filter(IMongoCriteria.getKey(function), value));
    }

    int delete(Map<String, Object> conditionFieldMap);

    //TODO: -------------------------------------------------------------------------------------------------

    // T inc(String oid, String key, Integer value);

    /**
     * 分页
     */

    EGridResult<T> findPage(Integer page, Integer rows, Map<String, Object> conditionMap, Sort... sorts);

    EGridResult<T> findPage(Integer page, Integer rows, Query<T> query);

    EGridResult<T> findPage(Integer page, Integer rows, QueryWrapper<T> wrapper);

    List<T> findPageList(Integer page, Integer rows, Query<T> query);

    List<T> findPageList(Integer page, Integer rows, QueryWrapper<T> wrapper);

    /**
     * adv
     */
    Query<T> createQuery(); //查询用

    QueryWrapper<T> createQueryWrapper(); //查询用

    UpdateOperations<T> createUpdateOperations(); //更新用

    AggregationPipeline createAggregation(); //统计用

    /**
     * Transactional
     */
    MongoDatabase getDataBase();

    MongoCollection<Document> getCollection();
}
