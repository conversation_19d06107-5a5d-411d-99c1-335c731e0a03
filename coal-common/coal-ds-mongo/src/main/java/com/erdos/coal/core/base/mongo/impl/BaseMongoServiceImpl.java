package com.erdos.coal.core.base.mongo.impl;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.core.base.mongo.IBaseMongoDAO;
import com.erdos.coal.core.base.mongo.IBaseMongoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by LIGX on 2018/12/25.
 * MongoDB 操作封装
 */
public class BaseMongoServiceImpl<T extends BaseMongoInfo, M extends IBaseMongoDAO<T>> extends BaseMongoDAOImpl<T> implements IBaseMongoService<T> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
}