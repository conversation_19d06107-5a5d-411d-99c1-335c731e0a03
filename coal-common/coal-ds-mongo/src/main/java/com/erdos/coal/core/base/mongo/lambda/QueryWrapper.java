package com.erdos.coal.core.base.mongo.lambda;

import dev.morphia.query.*;
import dev.morphia.query.internal.MorphiaKeyCursor;

import java.util.List;

/**
 * morphia.Query 包装类，使用lambda表达式写法
 *
 * @param <T>
 */
// eg:
//        // 1,
//        List<Sample> list1 = this.createQueryWrapper()
//                .filter(Sample::getField1, 1)
//                .findList();
//
//        // 2,
//        QueryWrapper<Sample> wrapper = this.createQueryWrapper();
//        List<Sample> list2 = wrapper
//                .filter(Sample::getId, 1)
//                // .field(Sample::getField11).greaterThanOrEq("")
//                .and(
//                        wrapper.criteria(Sample::getField1).greaterThan(1),
//                        wrapper.criteria(Sample::getField2).lessThanOrEq(100)
//                )
//                .or(
//                        wrapper.criteria(Sample::getField3).greaterThanOrEq(1000),
//                        wrapper.criteria(Sample::getField4).lessThan(2000)
//                )
//                .findList();
//
//        // 3,
//        wrapper.field(Sample::getField1).greaterThanOrEq(20);
//        List<Sample> list3 = wrapper.findList();
//
// 当使用 wrapper.criteria、wrapper.field 时, 不能链式处理，需要先声明 wrapper.

public class QueryWrapper<T> {

    private Query<T> query;

    public QueryWrapper(Query<T> query) {
        this.query = query;
    }

    public Query<T> getQuery() {
        return query;
    }

    private String getFieldName(SFunction<T, ?> function) {
        return IMongoCriteria.getKey(function);
    }

    @Deprecated
    public QueryWrapper<T> filter(String condition, Object value) {
        query.filter(condition, value);
        return this;
    }

    public QueryWrapper<T> filter(SFunction<T, ?> field, Object value) {
        query.filter(this.getFieldName(field), value);
        return this;
    }

    @Deprecated
    public FieldEnd<? extends Query<T>> field(String field) {
        return query.field(field);
    }

    public FieldEnd<? extends Query<T>> field(SFunction<T, ?> field) {
        return query.field(this.getFieldName(field));
    }

//    public MorphiaCursor<T> find() {
//        return query.find();
//    }
//
//    public MorphiaCursor<T> find(FindOptions options) {
//        return query.find(options);
//    }

    public QueryWrapper<T> and(Criteria... criteria) {
        query.and(criteria);
        return this;
    }

    public QueryWrapper<T> or(Criteria... criteria) {
        query.or(criteria);
        return this;
    }

    public long count() {
        return query.count();
    }

    public long count(CountOptions options) {
        return query.count(options);
    }

    @Deprecated
    public QueryWrapper<T> limit(int i) {
        query.limit(i);
        return this;
    }

    public T first() {
        return query.first();
    }

    public T first(FindOptions options) {
        return query.first(options);
    }

//    public QueryWrapper<T> cloneQuery() {
//        return new QueryWrapper<>(query.cloneQuery());
//    }

    @Deprecated
    public FieldEnd<? extends CriteriaContainer> criteria(String field) {
        return query.criteria(field);
    }

    public FieldEnd<? extends CriteriaContainer> criteria(SFunction<T, ?> field) {
        return query.criteria(this.getFieldName(field));
    }

    public QueryWrapper<T> disableValidation() {
        query.disableValidation();
        return this;
    }

    public QueryWrapper<T> enableValidation() {
        query.enableValidation();
        return this;
    }

//    public Map<String, Object> explain() {
//        return query.explain();
//    }
//
//    public Map<String, Object> explain(FindOptions options) {
//        return query.explain(options);
//    }

    public QueryWrapper<T> order(Meta meta) {
        query.order(meta);
        return this;
    }

    public QueryWrapper<T> order(Sort... sort) {
        query.order(sort);
        return this;
    }

    public QueryWrapper<T> project(SFunction<T, ?> field, boolean include) {
        query.project(this.getFieldName(field), include);
        return this;
    }

    public QueryWrapper<T> project(SFunction<T, ?> field, ArraySlice arraySlice) {
        query.project(this.getFieldName(field), arraySlice);
        return this;
    }

    public QueryWrapper<T> project(Meta meta) {
        query.project(meta);
        return this;
    }

    public QueryWrapper<T> retrieveKnownFields() {
        query.retrieveKnownFields();
        return this;
    }

    public QueryWrapper<T> search(SFunction<T, ?> field) {
        query.search(this.getFieldName(field));
        return this;
    }

    public QueryWrapper<T> search(SFunction<T, ?> field, String language) {
        query.search(this.getFieldName(field), language);
        return this;
    }

    public MorphiaKeyCursor<T> keys() {
        return query.keys();
    }

    public MorphiaKeyCursor<T> keys(FindOptions options) {
        return query.keys(options);
    }

    public List<T> findList() {
        return query.find().toList();
    }

    public List<T> findList(FindOptions options) {
        return query.find(options).toList();
    }

    @Override
    public String toString() {
        return query.toString();
    }
}
