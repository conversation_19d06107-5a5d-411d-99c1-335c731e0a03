package com.erdos.coal.core.base.mongo.impl;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.core.base.mongo.IBaseMongoDAO;
import com.erdos.coal.core.base.mongo.ReflectionUtils;
import com.erdos.coal.core.base.mongo.lambda.QueryWrapper;
import com.erdos.coal.core.common.EGridResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import dev.morphia.Datastore;
import dev.morphia.UpdateOptions;
import dev.morphia.aggregation.AggregationPipeline;
import dev.morphia.query.*;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.erdos.coal.core.base.mongo.BaseMongoInfo.ID_KEY;

/**
 * Created by LIGX on 2018/12/6.
 * 2019-03-25: 去掉条件值的为空判断，允许空值查询
 */
public class BaseMongoDAOImpl<T extends BaseMongoInfo> implements IBaseMongoDAO<T> {

    @Resource(name = "dsForCoal")
    protected Datastore dataStore;

    @Resource
    protected MongoTemplate mongoTemplate;

    private Class<T> clazz;

    public BaseMongoDAOImpl() {
        this.clazz = getEntityClass();
    }

    /**
     * 获得泛型类
     */
    private Class<T> getEntityClass() {
        return ReflectionUtils.getSuperClassGenricType(getClass());
    }

    //TODO: 保存 --------------------------------------------------------------------------------------------
    @Override
    public T save(T obj) {
        if (obj == null) {
            return null;
        }
        Date time = new Date();
        if (Objects.isNull(obj.getCreateTime())) {
            obj.setCreateTime(time);
        }
        if (Objects.isNull(obj.getUpdateTime())) {
            obj.setUpdateTime(time.getTime());
        }

        // dataStore.ensureIndexes(clazz);
        dataStore.save(obj);
        return obj;
    }

    //TODO: 批量保存
    @Override
    public void save(List<T> obj) {
        if (CollectionUtils.isEmpty(obj)) {
            return;
        }

        List<T> list = new ArrayList<>();

        Date time = new Date();
        for (T t : obj) {
            if (Objects.isNull(t.getCreateTime())) {
                t.setCreateTime(time);
            }
            if (Objects.isNull(t.getUpdateTime())) {
                t.setUpdateTime(time.getTime());
            }
            list.add(t);
        }

        // dataStore.ensureIndexes(clazz);
        dataStore.save(list);
    }

    //TODO: 更新 --------------------------------------------------------------------------------------------

    //TODO: 更新单文档

    @Deprecated
    @Override
    public T findAndModify(Query<T> query, UpdateOperations<T> updateOperations) {
        //更新修改时间
        Long time = System.currentTimeMillis();
        updateOperations.set("updateTime", time);
        return dataStore.findAndModify(query, updateOperations);
    }

    @Override
    public T findAndModify(QueryWrapper<T> wrapper, UpdateOperations<T> operations) {
        //更新修改时间
        Long time = System.currentTimeMillis();
        operations.set("updateTime", time);
        return dataStore.findAndModify(wrapper.getQuery(), operations);
    }

    @Deprecated
    @Override
    public UpdateResults update(Query<T> query, UpdateOperations<T> updateOperations, UpdateOptions options) {
        //更新修改时间
        Long time = System.currentTimeMillis();
        updateOperations.set("updateTime", time);
        if (Objects.isNull(options))
            return dataStore.update(query, updateOperations);
        return dataStore.update(query, updateOperations, options);
    }

    @Override
    public UpdateResults update(QueryWrapper<T> wrapper, UpdateOperations<T> operations, UpdateOptions options) {
        //更新修改时间
        Long time = System.currentTimeMillis();
        operations.set("updateTime", time);
        return dataStore.update(wrapper.getQuery(), operations, options);
    }

    @Deprecated
    @Override
    public UpdateResults update(Query<T> query, UpdateOperations<T> updateOperations) {
        //更新修改时间
        Long time = System.currentTimeMillis();
        updateOperations.set("updateTime", time);
        return dataStore.update(query, updateOperations);
    }

    @Override
    public UpdateResults update(QueryWrapper<T> wrapper, UpdateOperations<T> operations) {
        //更新修改时间
        Long time = System.currentTimeMillis();
        operations.set("updateTime", time);
        return dataStore.update(wrapper.getQuery(), operations);
    }

    @Deprecated
    @Override
    public UpdateResults update(Map<String, Object> conditionFieldMap, Map<String, Object> updateFieldMap) {
        if (updateFieldMap == null || updateFieldMap.size() == 0) {
            return null;
        }

        //条件
        Query<T> q = dataStore.createQuery(clazz);
        for (Map.Entry<String, Object> eachObj : conditionFieldMap.entrySet()) {
            //if (eachObj.getValue() != null) {
            q = filterIDQuery(q, eachObj.getKey(), eachObj.getValue());
            //}
        }

        //值
        UpdateOperations<T> ops = this.createUpdateOperations();
        for (Map.Entry<String, Object> eachObj : updateFieldMap.entrySet()) {
            if (eachObj.getValue() != null) {
                ops.set(eachObj.getKey(), eachObj.getValue());
            } else {
                ops.unset(eachObj.getKey());//删除空值
            }
        }

        //更新修改时间
        Long time = System.currentTimeMillis();
        ops.set("updateTime", time);

        return dataStore.update(q, ops);
    }

    //TODO: 获取 --------------------------------------------------------------------------------------------

    @Deprecated
    @Override
    public T get(Map<String, Object> conditionFieldMap) {
        Query<T> query = dataStore.createQuery(clazz);

        if (conditionFieldMap != null && conditionFieldMap.size() != 0) {
            for (Map.Entry<String, Object> eachObj : conditionFieldMap.entrySet()) {
                //if (eachObj.getValue() != null) {
                query = filterIDQuery(query, eachObj.getKey(), eachObj.getValue());
                //}
            }
        }

        FindOptions findOptions = new FindOptions();
        findOptions.limit(1);//限定条数
        return query.first(findOptions);
    }

    @Deprecated
    @Override
    public List<T> list(Map<String, Object> conditionFieldMap, Sort... sorts) {
        Query<T> query = dataStore.createQuery(clazz);
        if (conditionFieldMap != null && conditionFieldMap.size() != 0) {
            for (Map.Entry<String, Object> eachObj : conditionFieldMap.entrySet()) {
                //if (eachObj.getValue() != null) {
                query = filterIDQuery(query, eachObj.getKey(), eachObj.getValue());
                //}
            }
        }

        query.order(sorts);

        return query.find().toList();
    }

    private Query<T> filterIDQuery(Query<T> query, String key, Object value) {
        //TODO: 处理 _id
        if (value instanceof ObjectId) {
            return query.filter(key, value);
        } else if (key.equals(ID_KEY) && value instanceof String) {
            return query.filter(key, new ObjectId(String.valueOf(value)));
        } else {
            return query.filter(key, value);
        }
    }

    //TODO: -------------------------------------------------------------------------------------------------

    @Deprecated
    @Override
    public T findAndDelete(Query<T> query) {
        return dataStore.findAndDelete(query);
    }

    @Override
    public T findAndDelete(QueryWrapper<T> wrapper) {
        return dataStore.findAndDelete(wrapper.getQuery());
    }

    @Deprecated
    @Override
    public int delete(Query<T> query) {
        return dataStore.delete(query).getN();
    }

    @Override
    public int delete(QueryWrapper<T> wrapper) {
        return dataStore.delete(wrapper.getQuery()).getN();
    }

    @Deprecated
    @Override
    public int delete(Map<String, Object> conditionFieldMap) {

        if (conditionFieldMap == null || conditionFieldMap.size() == 0) {
            return 0;
        }

        //条件
        Query<T> q = dataStore.createQuery(clazz);
        for (Map.Entry<String, Object> eachObj : conditionFieldMap.entrySet()) {
            //if (eachObj.getValue() != null) {
            q = filterIDQuery(q, eachObj.getKey(), eachObj.getValue());
            //}
        }

        return this.delete(q);
    }

    //TODO: -------------------------------------------------------------------------------------------------

//    @Override
//    public T inc(String oid, String key, Integer value) {
//        Query<T> q = dataStore.createQuery(clazz).filter(ID_KEY, new ObjectId(oid));
//        UpdateOperations<T> ops = this.createUpdateOperations();
//        ops.inc(key, value);
//        return dataStore.findAndModify(q, ops);
//    }

    //TODO: 排序分页
    @Deprecated
    @Override
    public EGridResult<T> findPage(Integer page, Integer rows, Map<String, Object> conditionMap, Sort... sorts) {
        Query<T> query = dataStore.createQuery(clazz);

        //TODO: 条件
        if (conditionMap != null && conditionMap.size() != 0) {
            for (Map.Entry<String, Object> eachObj : conditionMap.entrySet()) {
                //if (eachObj.getValue() != null) {
                query = filterIDQuery(query, eachObj.getKey(), eachObj.getValue());
                //}
            }
        }

        //TODO: 排序
        query.order(sorts);

//        //TODO: 总数
//        long count = query.count();
//
//        int currPage = page < 1 ? 1 : page; //最小为1
//
//        //TODO: 分页
//        FindOptions findOptions = new FindOptions();
//        findOptions.skip((currPage - 1) * rows);//起始位置
//        findOptions.limit(rows);//查询条数
//
//        //TODO: 结果
//        List<T> list = query.asList(findOptions);
//
//        //TODO: 返回
//        EGridResult result = new EGridResult();
//        result.setRows(list);
//        result.setTotal(count);

        return getEGridResult(page, rows, query);
    }

    @Deprecated
    @Override
    public EGridResult<T> findPage(Integer page, Integer rows, Query<T> query) {
        return getEGridResult(page, rows, query);
    }

    @Override
    public EGridResult<T> findPage(Integer page, Integer rows, QueryWrapper<T> wrapper) {
        return getEGridResult(page, rows, wrapper.getQuery());
    }

    @Deprecated
    @Override
    public List<T> findPageList(Integer page, Integer rows, Query<T> query) {
        try {
            int pages = page == null ? 1 : page;
            int size = rows == null ? 10 : rows;

            int currPage = Math.max(pages, 1); //最小为1

            //TODO: 分页
            FindOptions findOptions = new FindOptions();
            findOptions.skip((currPage - 1) * size);//起始位置
            findOptions.limit(size);//查询条数

            //TODO: 结果
            List<T> list = query.find(findOptions).toList();
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<T> findPageList(Integer page, Integer rows, QueryWrapper<T> wrapper) {
        return this.findPageList(page, rows, wrapper.getQuery());
    }

    private EGridResult<T> getEGridResult(Integer page, Integer rows, Query<T> query) {

        try {
            //TODO: 总数
            long count = query.count();

            int pages = page == null ? 1 : page;
            int size = rows == null ? (int) count : rows;

            int currPage = Math.max(pages, 1); //最小为1

            //TODO: 分页
            FindOptions findOptions = new FindOptions();
            findOptions.skip((currPage - 1) * size);//起始位置
            findOptions.limit(size);//查询条数

            //TODO: 结果
            List<T> list = query.find(findOptions).toList();

            return new EGridResult<>(count, list);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Query<T> createQuery() {
        return dataStore.createQuery(clazz);
    }

    @Override
    public QueryWrapper<T> createQueryWrapper() {
        return new QueryWrapper<>(this.createQuery());
    }

    @Override
    public UpdateOperations<T> createUpdateOperations() {
        return dataStore.createUpdateOperations(clazz);
    }

    @Override
    public AggregationPipeline createAggregation() {
        return dataStore.createAggregation(clazz);
    }

    @Override
    public MongoDatabase getDataBase() {
        return dataStore.getDatabase();
    }

    @Override
    public MongoCollection<Document> getCollection() {
        // mongoTemplate.getCollectionName(clazz); // springdata 方式, 需要实体类上添加 @Document 并设置名称.
        String collName = dataStore.getCollection(clazz).getName(); // morphia 方式 (默认).
        return getDataBase().getCollection(collName);
    }
}