<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>coal-project</artifactId>
        <groupId>com.erdos.coal</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>coal-ds-mongo</artifactId>
    <description>coal-ds-mongo library for erdos</description>

    <properties>
        <mongo-driver-version>3.12.10</mongo-driver-version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-core</artifactId>
            <version>${coal.core.version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- Mongodb -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver</artifactId>
            <version>${mongo-driver-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongodb-driver-core</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bson</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-core</artifactId>
            <version>${mongo-driver-version}</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
            <version>${mongo-driver-version}</version>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>org.springframework.session</groupId>-->
        <!--<artifactId>spring-session-data-mongodb</artifactId>-->
        <!--</dependency>-->

        <dependency>
            <groupId>dev.morphia.morphia</groupId>
            <artifactId>core</artifactId>
            <version>${morphia.version}</version>
        </dependency>

        <!-- 查看 LazyFeatureDependencies.class 文件 -->
        <!-- morphia 在用代理处理 lazy 的时候，会动态加载 cglib 的 class，找不到时候，LOG.warn 如下信息: -->
        <!-- dev.morphia.mapping.lazy.LazyFeatureDependencies - Lazy loading impossible due to missing dependencies. -->
        <!-- 添加如下依赖解决次问题 -->
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>2.2.2</version>
        </dependency>
        <dependency>
            <groupId>asm</groupId>
            <artifactId>asm</artifactId>
            <version>3.1</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <!--<resource>-->
            <!--<directory>src/main/resources</directory>-->
            <!--<includes>-->
            <!--<include>**/*.xml</include>-->
            <!--<include>**/*.yml</include>-->
            <!--<include>**/*.properties</include>-->
            <!--<include>**/*.html</include>-->
            <!--<include>**/*.js</include>-->
            <!--<include>**/*.css</include>-->
            <!--<include>**/*.png</include>-->
            <!--<include>**/*.gif</include>-->
            <!--<include>**/*.jpg</include>-->
            <!--</includes>-->
            <!--</resource>-->
            <!--<resource>-->
            <!--<directory>src/main/java</directory>-->
            <!--<includes>-->
            <!--<include>**/*.xml</include>-->
            <!--<include>**/*.properties</include>-->
            <!--</includes>-->
            <!--</resource>-->
            <!--<resource>-->
            <!--<targetPath>BOOT-INF/lib/</targetPath>-->
            <!--<directory>src/main/resources/libs/</directory>-->
            <!--<includes>-->
            <!--<include>**/*.jar</include>-->
            <!--</includes>-->
            <!--</resource>-->
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <includes>
                        <!--只打包 core 目录-->
                        <include>**/coal/core/**</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
