<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>coal-project</artifactId>
        <groupId>com.erdos.coal</groupId>
        <version>1.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>coal-aliyun</artifactId>
    <description>coal-aliyun for erdos.coal</description>

    <properties>
        <aliyun.sdk.version>4.0.6</aliyun.sdk.version>
        <aliyun.sdk.api.version>1.1.0</aliyun.sdk.api.version>
        <aliyun.sdk.push.version>3.10.1</aliyun.sdk.push.version>
    </properties>

    <dependencies>
        <!-- ======================================================================================================= -->
        <!--阿里短信-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.25</version> <!-- 注：如提示报错，先升级基础包版，无法解决可联系技术支持 -->
<!--            <version>[4.3.2,5.0.0)</version> &lt;!&ndash; 注：如提示报错，先升级基础包版，无法解决可联系技术支持 &ndash;&gt;-->
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>${aliyun.sdk.api.version}</version>
        </dependency>

        <!--aliyun 推送-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-push</artifactId>
            <version>${aliyun.sdk.push.version}</version>
        </dependency>

        <!-- oss 图片存储 -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>

        <!-- 语音通知 -->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-console</artifactId>
            <version>0.0.1</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>darabonba-env</artifactId>
            <version>0.1.1</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-openapi</artifactId>
            <version>0.2.2</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dyvmsapi20170525</artifactId>
            <version>2.1.2</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-util</artifactId>
            <version>0.2.13</version>
        </dependency>-->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>darabonba-number</artifactId>
            <version>0.0.2</version>
        </dependency>-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea</artifactId>
            <version>1.1.14</version>
        </dependency>

    </dependencies>

</project>