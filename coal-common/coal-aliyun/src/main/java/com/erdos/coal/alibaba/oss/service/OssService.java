package com.erdos.coal.alibaba.oss.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

public class OssService {
    // yourEndpoint填写Bucket所在地域对应的Endpoint。
    // 以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    private static final String endpoint = "oss-cn-zhangjiakou.aliyuncs.com";
    // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。
    // 强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
    private static final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";
    private static final String accessKeySecret = "******************************";
    private static final String bucketName = "platform-v2-photo";
    private static final String bucketName_PublicRead = "hjypr";
    // private static OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

    public static void putObject(String key, InputStream inputStream) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        PutObjectResult putObjectResult = ossClient.putObject(bucketName, key, inputStream);
        ossClient.shutdown();
    }

    public static void putObject(String key, File file) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        PutObjectResult putObjectResult = ossClient.putObject(bucketName, key, file);
        ossClient.shutdown();
    }

    /**
     * 生成单个以GET方法访问的签名URL
     */
    public static URL getObject1(String objectName) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        // 设置签名URL过期时间为300秒（5分钟）。2*60*60=7200(2小时)
        Date expiration = new Date(new Date().getTime() + 7200 * 1000);
        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);

        // 关闭OSSClient。
        ossClient.shutdown();
        return url;
    }

    /**
     * 获取上传对象的InputStream流
     */
    public static byte[] getBytes(String objectName) throws IOException {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        OSSObject ossObject = ossClient.getObject(bucketName, objectName);

        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc;
        while ((rc = ossObject.getObjectContent().read(buff, 0, 100)) > 0) {
            swapStream.write(buff, 0, rc);
        }

        ossClient.shutdown();
        return swapStream.toByteArray();
    }

    /**
     * 获取上传的对象
     */
    public static OSSObject getObject(String objectName) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            OSSObject ossObject = ossClient.getObject(bucketName, objectName);
            ossClient.shutdown();
            return ossObject;
        } catch (OSSException e) {
            ossClient.shutdown();
            return null; //"The specified key does not exist";
        }
    }


    public static void putObjectToPublicRead(String key, InputStream inputStream) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        PutObjectResult putObjectResult = ossClient.putObject(bucketName_PublicRead, key, inputStream);
        ossClient.shutdown();
    }

    /**
     * 生成单个以GET方法访问的签名URL（public_read）
     */
    public static String getObjectWithPublicRead(String objectName) {
        return "http://" + bucketName_PublicRead + "." + endpoint + "/" + objectName;
    }

    public static void main(String[] arg) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        // http://platform-v2-photo.oss-cn-zhangjiakou.aliyuncs.com/20220508healthCodePho6229354410ad822838d9e038.jpg?Expires=**********&OSSAccessKeyId=LTAI4FiHsKy7mgFjzMZUqKjE&Signature=bf0ZqPJp4rRAbUy8AJyTheI9wsc%3D

//        URL url = getObject1(key);
        // 查询当前oss上有哪些bucket；
//        List<Bucket> list = ossClient.listBuckets();
//        System.out.println(list);       // 两个bucket : logistics-heijinyun 和 platform-v2-photo

        // 查询bucket存储对象集合吧
//        String bucketName1 = "logistics-heijinyun";
//        ObjectListing objectListing = ossClient.listObjects(bucketName1);
//        ObjectListing objectListing2 = ossClient.listObjects(bucketName);
//        ObjectListing objectListing3 = ossClient.listObjects(bucketName_PublicRead);

        // 创建 hjypr bucket存储桶，并设置为public_read
//        ossClient.createBucket(bucketName_PublicRead);
//        SetBucketAclRequest setBucketAclRequest = new SetBucketAclRequest(bucketName_PublicRead);
//        setBucketAclRequest.setCannedACL(CannedAccessControlList.PublicRead);
//        ossClient.setBucketAcl(setBucketAclRequest);
//        BucketInfo bucketInfo = ossClient.getBucketInfo(bucketName_PublicRead);

        // hjypr存储桶测试上传图片
//        File file = new File("/Users/<USER>/Downloads/1.jpeg");
//        PutObjectResult putObjectResult = ossClient.putObject(bucketName_PublicRead, "20241119inNetWeightPho"+"6503a01363ebff236cf19591", file);
        // 20241119inNetWeightPho6503a01363ebff236cf19591

        // 删除上传的测试图片
//        ossClient.deleteObject(bucketName_PublicRead, "20241119inNetWeightPho6503a01363ebff236cf19591");

        // 释放链接
//        ossClient.shutdown();

        System.out.println();
//        String key = "";
//        ossClient.deleteObject("", key);
//        ossClient.createBucket("public_bucket_name");

//        SetBucketAclRequest setBucketAclRequest = new SetBucketAclRequest(bucketName);
//        setBucketAclRequest.setCannedACL(CannedAccessControlList.PublicRead);
//        ossClient.setBucketAcl(setBucketAclRequest);
//        BucketInfo bucketInfo = ossClient.getBucketInfo(bucketName);
//        System.out.println(bucketInfo);

        /*File file = new File("/Users/<USER>/Downloads/guobang1.png");
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        PutObjectResult putObjectResult = ossClient.putObject(bucketName, "guobang1", file);
        ossClient.shutdown();

        URL url = getObject1("guobang1");
        System.out.println(url.toString());*/

        /*File file = new File("/Users/<USER>/Downloads/123.jpeg");
        putObject("platform-v2-photo", "1", file);*/

        /*String objectName = "618b331d65c3ff69bd3a9f60driIdentityPhoBef";
        System.out.println(getObject1(objectName));*/

        // 创建OSSClient实例。
        /*OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

// ossClient.listObjects返回ObjectListing实例，包含此次listObject请求的返回结果。
        ObjectListing objectListing = ossClient.listObjects(bucketName);
// objectListing.getObjectSummaries获取所有文件的描述信息。
        for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
            if (objectSummary.getKey().contains("healthCodePho") || objectSummary.getKey().contains("travelCardPho"))
                System.out.println(" - " + objectSummary.getKey() + "  " + "(size = " + objectSummary.getSize() + ")");
        }

// 关闭OSSClient。
        ossClient.shutdown();*/

        /*URL u1 = getObject1("20220310healthCodePho61e78bfb10ad82102e8a8fd9.jpg");
        URL u2 = getObject1("20220310travelCardPho61e78bfb10ad82102e8a8fd9.jpg");*/

        /*System.out.println(u1.toString());
        System.out.println(u2.toString());*/

        /*String file = getObject1("618b331d65c3ff69bd3a9f60driIdentityPhoBef").getFile();
        System.out.println(file);*/

    }

}
