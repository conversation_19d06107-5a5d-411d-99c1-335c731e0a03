package com.erdos.coal.alibaba.push.bean;

public class PushResult {
    private String messageId;   //消息id
    private String title;   //标题
    private String body;    //内容
    private String deviceType;  //设备类型 iOS,ANDROID
    private String pushTime;    //推送时间
    private Long sentCount;     //发送数
    private Long receivedCount; //到达数
    private Long openedCount;   //打开数
    private Long deletedCount;  //删除数

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getPushTime() {
        return pushTime;
    }

    public void setPushTime(String pushTime) {
        this.pushTime = pushTime;
    }

    public Long getSentCount() {
        return sentCount;
    }

    public void setSentCount(Long sentCount) {
        this.sentCount = sentCount;
    }

    public Long getReceivedCount() {
        return receivedCount;
    }

    public void setReceivedCount(Long receivedCount) {
        this.receivedCount = receivedCount;
    }

    public Long getOpenedCount() {
        return openedCount;
    }

    public void setOpenedCount(Long openedCount) {
        this.openedCount = openedCount;
    }

    public Long getDeletedCount() {
        return deletedCount;
    }

    public void setDeletedCount(Long deletedCount) {
        this.deletedCount = deletedCount;
    }
}
