package com.erdos.coal.alibaba.sms.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.erdos.coal.alibaba.sms.bean.SmsResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SMSService {
    protected final static Logger logger = LoggerFactory.getLogger(SMSService.class);

    public static SendSmsResponse sendSMS(String phoneNum, String sendCode) {
        // 示例说明: https://help.aliyun.com/document_detail/55284.html

        // 错误码 : https://help.aliyun.com/document_detail/55323.html?spm=a2c4g.11186623.2.23.2530157b2uaUJN

        //TODO: 手机号
//        String phoneNum = "1500000000";
        //TODO: 短信签名
        String signName = "黑金宝";//"黑金宝";
        //TODO: 模板编号
        String tempCode = "SMS_173625787";//"SMS_173625787";
        //TODO: 验证码, 替换模板中的 code 形式
//        String sendCode = "1234";

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            //DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            //request.setMethod(MethodType.POST);
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("code", sendCode);
            //request.setTemplateParam("{\"name\":\"Tom\", \"code\":\"123\"}");
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            /*if (sendSmsResponse.getCode() != null && !sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                logger.info(
                        sendSmsResponse.getBizId() + "," +
                                sendSmsResponse.getCode() + "," +
                                sendSmsResponse.getMessage() + "," +
                                sendSmsResponse.getRequestId()
                );

                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());

                return sr;
            }*/
            return sendSmsResponse;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static SmsResult sendNoticeSMS(String name, String thirdPartyName, Double thirdPartyLedger, String sendCode) {
        //TODO: 手机号
        String phoneNum = "15947496200";
        //TODO: 短信签名
        String signName = "黑金宝客商";
        //TODO: 模板编号
        String tempCode = "SMS_244051189";
        //TODO: 验证码, 替换模板中的 code 形式

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("name", name);
            jo.put("name2", thirdPartyName);
            jo.put("fee", thirdPartyLedger);
            jo.put("code", sendCode);
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                /*logger.info(
                        sendSmsResponse.getBizId() + "," +
                                sendSmsResponse.getCode() + "," +
                                sendSmsResponse.getMessage() + "," +
                                sendSmsResponse.getRequestId()
                );*/

                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());

                return sr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * 针对同号码的发送频率,目前每天50条限制
     * */
    public static SmsResult sendNoticeSMSJieDan(String phoneNum, String carNum, String subName, String variety) {
        System.out.println("jiedan");
        //TODO: 短信签名
        String signName = "黑金宝司机";
        //TODO: 模板编号
        String tempCode = "SMS_464316158";

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("plateNumber", carNum);
            jo.put("place", subName);
            jo.put("name", variety);
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());
                return sr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static SmsResult sendNoticeSMSZhuangHuo(String phoneNum, String carNum, String subName, String variety) {
        //TODO: 短信签名
        String signName = "黑金宝客商";
        //TODO: 模板编号
        String tempCode = "SMS_464336143";
        //TODO: 验证码, 替换模板中的 code 形式

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("plateNumber", carNum);
            jo.put("place", subName);
            jo.put("name", variety);
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());
                return sr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static SmsResult sendNoticeSMSXieHuo(String phoneNum, String carNum, String subName, String variety) {
        //TODO: 短信签名
        String signName = "黑金宝客商";
        //TODO: 模板编号
        String tempCode = "SMS_464331115";
        //TODO: 验证码, 替换模板中的 code 形式

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("plateNumber", carNum);
            jo.put("place", subName);
            jo.put("name", variety);
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());
                return sr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static SmsResult sendTradeNoticeSMS(String subName, String contractName) {
        //TODO: 手机号
//        String phoneNum = "18047770665,15009227826";
        String phoneNum = "18047770665";
        //TODO: 短信签名
        String signName = "黑金宝客商";
        //TODO: 模板编号
        String tempCode = "SMS_479950292";
        //TODO: 验证码, 替换模板中的 code 形式

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("subName", subName);
            jo.put("contractName", contractName);
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                /*logger.info(
                        sendSmsResponse.getBizId() + "," +
                                sendSmsResponse.getCode() + "," +
                                sendSmsResponse.getMessage() + "," +
                                sendSmsResponse.getRequestId()
                );*/

                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());

                return sr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static SmsResult sendTradeNoticeSMSYD(String subName, String contractName) {
        //TODO: 手机号
        String phoneNum = "15009227826";
        //TODO: 短信签名
        String signName = "内蒙古黑金网络科技";
        //TODO: 模板编号
        String tempCode = "SMS_491300536";
        //TODO: 验证码, 替换模板中的 code 形式

        //设置超时时间-可自行调整
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化ascClient需要的几个参数
        final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
        final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）

        //TODO: 替换成你的AK
        final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";//你的accessKeyId,参考本文档步骤2"LTAI4FiHsKy7mgFjzMZUqKjE"
        final String accessKeySecret = "******************************";//你的accessKeySecret，参考本文档步骤2"******************************"
        try {
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setSysMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            // 批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,
            // 验证码类型的短信推荐使用单条调用的方式；
            // 发送国际/港澳台消息时，接收号码格式为00+国际区号+号码，如“0085200000000”
            request.setPhoneNumbers(phoneNum);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(tempCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            JSONObject jo = new JSONObject();
            jo.put("subName", subName);
            jo.put("contractName", contractName);
            request.setTemplateParam(jo.toJSONString());
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            request.setOutId("yourOutId");
            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //请求成功
                /*logger.info(
                        sendSmsResponse.getBizId() + "," +
                                sendSmsResponse.getCode() + "," +
                                sendSmsResponse.getMessage() + "," +
                                sendSmsResponse.getRequestId()
                );*/

                SmsResult sr = new SmsResult();
                sr.setBizId(sendSmsResponse.getBizId());
                sr.setCode(sendSmsResponse.getCode());
                sr.setMessage(sendSmsResponse.getMessage());
                sr.setRequestId(sendSmsResponse.getRequestId());

                return sr;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] arg) {
        sendSMS("15357039213", "123456");
    }

    /*public static void call(){
        // HttpClient Configuration
        *//*HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
                .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout time, the default is 10 seconds
                .responseTimeout(Duration.ofSeconds(10)) // Set the response timeout time, the default is 20 seconds
                .maxConnections(128) // Set the connection pool size
                .maxIdleTimeOut(Duration.ofSeconds(50)) // Set the connection pool timeout, the default is 30 seconds
                // Configure the proxy
                .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new InetSocketAddress("<your-proxy-hostname>", 9001))
                        .setCredentials("<your-proxy-username>", "<your-proxy-password>"))
                // If it is an https connection, you need to configure the certificate, or ignore the certificate(.ignoreSSL(true))
                .x509TrustManagers(new X509TrustManager[]{})
                .keyManagers(new KeyManager[]{})
                .ignoreSSL(false)
                .build();*//*

        // Configure Credentials authentication information, including ak, secret, token
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                .accessKeyId(System.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID"))
                .accessKeySecret(System.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"))
                //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-hangzhou") // Region ID
                //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Dyvmsapi
                                .setEndpointOverride("dyvmsapi.aliyuncs.com")
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();

        // Parameter settings for API request
        SingleCallByVoiceRequest singleCallByVoiceRequest = SingleCallByVoiceRequest.builder()
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<SingleCallByVoiceResponse> response = client.singleCallByVoice(singleCallByVoiceRequest);
        // Synchronously get the return value of the API request
        SingleCallByVoiceResponse resp = response.get();
        System.out.println(new Gson().toJson(resp));
        // Asynchronous processing of return values
        *//*response.thenAccept(resp -> {
            System.out.println(new Gson().toJson(resp));
        }).exceptionally(throwable -> { // Handling exceptions
            System.out.println(throwable.getMessage());
            return null;
        });*//*

        // Finally, close the client
        client.close();
    }*/
}
