package com.erdos.coal.alibaba.push.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.push.model.v20160801.*;
import com.aliyuncs.utils.ParameterHelper;
import com.erdos.coal.alibaba.push.bean.PushResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

public class PushService {
    private static final long appKey = 27862777;
    private static final String accessKeyId = "LTAI4FiHsKy7mgFjzMZUqKjE";
    private static final String accessKeySecret = "******************************";
    //目前该值固定，不用动
    private static final String regionId = "cn-hangzhou";
    private static final IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
    protected static DefaultAcsClient client = new DefaultAcsClient(profile);
    protected static final Logger logger = LoggerFactory.getLogger(PushService.class);

    /**
     * 推送高级接口
     *
     * @Param titile 标题
     * @Param message 内容
     * @Param target 推送类型
     * @Param aliass 推送账号
     * <p>
     * 参见文档 https://help.aliyun.com/document_detail/48089.html
     * //
     */
    public static void advancedPush(String title, String message, String target, String aliass, boolean store) throws Exception {

        PushRequest pushRequest = new PushRequest();
        //安全性比较高的内容建议使用HTTPS
//        pushRequest.setProtocol(ProtocolType.HTTPS);
        //内容较大的请求，使用POST请求
        //pushRequest.setMethod(MethodType.POST);
        pushRequest.setSysMethod(MethodType.POST);
        // 推送目标
        pushRequest.setAppKey(appKey);
        pushRequest.setTarget(target); //推送目标: DEVICE:按设备推送 ALIAS : 按别名推送 ACCOUNT:按帐号推送  TAG:按标签推送; ALL: 广播推送
        pushRequest.setTargetValue(aliass); //根据Target来设定，如Target=DEVICE, 则对应的值为 设备id1,设备id2. 多个值使用逗号分隔.(帐号与设备有一次最多100个的限制)
//        pushRequest.setTarget("ALL"); //推送目标: device:推送给设备; account:推送给指定帐号,tag:推送给自定义标签; all: 推送给全部
//        pushRequest.setTargetValue("ALL"); //根据Target来设定，如Target=device, 则对应的值为 设备id1,设备id2. 多个值使用逗号分隔.(帐号与设备有一次最多100个的限制)
        pushRequest.setPushType("NOTICE"); // 消息类型 MESSAGE NOTICE
        pushRequest.setDeviceType("ANDROID"); // 设备类型 ANDROID iOS ALL.

        // 推送配置
        pushRequest.setTitle(title); // 消息的标题
        pushRequest.setBody(message); // 消息的内容

        // 推送配置: Android
        pushRequest.setAndroidNotifyType("BOTH");//BOTH通知的提醒方式 "VIBRATE" : 震动 "SOUND" : 声音 "BOTH" : 声音和震动 NONE : 静音
        pushRequest.setAndroidNotificationBarType(1);//通知栏自定义样式0-100
        pushRequest.setAndroidNotificationBarPriority(1);//通知栏自定义样式0-100
        pushRequest.setAndroidOpenType("APPLICATION"); //点击通知后动作 "APPLICATION" : 打开应用 "ACTIVITY" : 打开AndroidActivity "URL" : 打开URL "NONE" : 无跳转

        /*String url = type.equals("1") ? "test://order.com/xxx" : (type.equals("0") ? "test://logistics.com/xxx" : "");//app端需要打开的页面
        pushRequest.setAndroidOpenUrl(url); //Android收到推送后打开对应的url,仅当AndroidOpenType="URL"有效*/
//        pushRequest.setAndroidActivity("com.alibaba.push2.demo.XiaoMiPushActivity"); // 设定通知打开的activity，仅当AndroidOpenType="Activity"有效
        pushRequest.setAndroidMusic("default"); // Android通知音乐
        pushRequest.setAndroidPopupActivity("com.ali.demo.MiActivity");
        pushRequest.setAndroidPopupBody(message);
        pushRequest.setAndroidPopupTitle(title);
       /* pushRequest.setAndroidXiaoMiActivity("com.ali.demo.MiActivity");//设置该参数后启动小米托管弹窗功能, 此处指定通知点击后跳转的Activity（托管弹窗的前提条件：1. 集成小米辅助通道；2. StoreOffline参数设为true）
        pushRequest.setAndroidXiaoMiNotifyTitle(title);
        pushRequest.setAndroidXiaoMiNotifyBody(message); 新版本已废弃*/
//        pushRequest.setAndroidExtParameters(extPara); //设定通知的扩展属性。eg:"{\"k1\":\"android\",\"k2\":\"v2\"}"(注意 : 该参数要以 json map 的格式传入,否则会解析出错)
        // 指定notificaitonchannel id android版本8.0以上需设置
//        pushRequest.setAndroidNotificationChannel("1");   // 加此参数，手机收不到

//        // 推送控制
//        Date pushDate = new Date(System.currentTimeMillis()); // 30秒之间的时间点, 也可以设置成你指定固定时间
//        String pushTime = ParameterHelper.getISO8601Time(pushDate);
//        pushRequest.setPushTime(pushTime); // 延后推送。可选，如果不设置表示立即推送
        String expireTime = ParameterHelper.getISO8601Time(new Date(System.currentTimeMillis() + 12 * 3600 * 1000)); // 12小时后消息失效, 不会再发送
        pushRequest.setExpireTime(expireTime);
        pushRequest.setStoreOffline(store); // 离线消息是否保存,若保存, 在推送时候，用户即使不在线，下一次上线则会收到

        PushResponse pushResponse = client.getAcsResponse(pushRequest);
        logger.info("RequestId: %s, MessageID: %s\n",
                pushResponse.getRequestId(), pushResponse.getMessageId());

    }

    /**
     * 任务维度推送统计
     * 查询: 发送数,到达数,打开数,删除数
     * 参考文档 ：https://help.aliyun.com/document_detail/48097.html
     */
    public static PushResult testQueryPushStatByMsg(String messageId) throws Exception {
        QueryPushStatByMsgRequest request = new QueryPushStatByMsgRequest();
        request.setAppKey(appKey);
        request.setMessageId(Long.parseLong(messageId));//消息推送后返回的MessageID

        QueryPushStatByMsgResponse response = client.getAcsResponse(request);
        logger.info("RequestId: %s\n", response.getRequestId());

        PushResult result = new PushResult();

        for (QueryPushStatByMsgResponse.PushStat item : response.getPushStats()) {
            logger.info("MessageId: %s , SentCount: %s, ReceivedCount: %s, OpenedCount: %s, DeletedCount: %s\n",
                    item.getMessageId(), item.getSentCount(), item.getReceivedCount(), item.getOpenedCount(), item.getDeletedCount());

            result.setMessageId(item.getMessageId());
            result.setSentCount(item.getSentCount());
            result.setOpenedCount(item.getOpenedCount());
            result.setReceivedCount(item.getReceivedCount());
            result.setDeletedCount(item.getDeletedCount());
        }
        return result;
    }

    /**
     * APP维度推送统计
     * 参考文档 ：https://help.aliyun.com/document_detail/48093.html
     */
    public static void testQueryPushStatByApp() throws Exception {

        QueryPushStatByAppRequest request = new QueryPushStatByAppRequest();
        request.setAppKey(appKey);
        request.setGranularity("DAY");//DAY: 天粒度

        Date startDate = new Date(System.currentTimeMillis() - 7 * 24 * 3600 * 1000);
        String startTime = ParameterHelper.getISO8601Time(startDate);
        Date endDate = new Date(System.currentTimeMillis());
        String endTime = ParameterHelper.getISO8601Time(endDate);

        request.setStartTime(startTime);
        request.setEndTime(endTime);

        QueryPushStatByAppResponse response = client.getAcsResponse(request);
        logger.info("RequestId: %s\n", response.getRequestId());

        for (QueryPushStatByAppResponse.AppPushStat item : response.getAppPushStats()) {
            logger.info("Time: %s , SentCount: %s, ReceivedCount: %s, OpenedCount: %s, DeletedCount: %s\n",
                    item.getTime(), item.getSentCount(), item.getReceivedCount(), item.getOpenedCount(), item.getDeletedCount());
        }
    }

    /**
     * 设备新增与留存
     * 参考文档 ：https://help.aliyun.com/document_detail/48094.html
     */
    public static void testQueryDeviceStat() throws Exception {
        QueryDeviceStatRequest request = new QueryDeviceStatRequest();
        request.setAppKey(appKey);
        request.setQueryType("TOTAL");//NEW: 新增设备查询, TOTAL: 留存设备查询
        request.setDeviceType("ANDROID");//iOS,ANDROID,ALL

        Date startDate = new Date(System.currentTimeMillis() - 7 * 24 * 3600 * 1000);
        String startTime = ParameterHelper.getISO8601Time(startDate);
        Date endDate = new Date(System.currentTimeMillis());
        String endTime = ParameterHelper.getISO8601Time(endDate);

        request.setStartTime(startTime);
        request.setEndTime(endTime);

        QueryDeviceStatResponse response = client.getAcsResponse(request);
        logger.info("RequestId: %s\n", response.getRequestId());

        for (QueryDeviceStatResponse.AppDeviceStat stat : response.getAppDeviceStats()) {
            logger.info("Time: %s, DeviceType: %s, Count: %s\n",
                    stat.getTime(), stat.getDeviceType(), stat.getCount());
        }
    }

    /**
     * 去重设备统计
     * 参考文档 ：https://help.aliyun.com/document_detail/48092.html
     */
    public static void testQueryUniqueDeviceStat(String start, String end, String granularity) throws Exception {

        QueryUniqueDeviceStatRequest request = new QueryUniqueDeviceStatRequest();
        request.setAppKey(appKey);
        request.setGranularity(granularity);//DAY: 天粒度 MONTH: 月粒度

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startDate = simpleDateFormat.parse(start + " 00:00:00");
        // Date startDate = new Date(System.currentTimeMillis() - 31 * 24 * 3600 * 1000);
        String startTime = ParameterHelper.getISO8601Time(startDate);
        Date endDate = simpleDateFormat.parse(end + " 23:59:59");
        // Date endDate = new Date(System.currentTimeMillis());
        String endTime = ParameterHelper.getISO8601Time(endDate);

        request.setStartTime(startTime);
        request.setEndTime(endTime);

        QueryUniqueDeviceStatResponse response = client.getAcsResponse(request);
        logger.info("RequestId: %s\n", response.getRequestId());

        for (QueryUniqueDeviceStatResponse.AppDeviceStat stat : response.getAppDeviceStats()) {
            logger.info("Time: %s, Count: %s\n",
                    stat.getTime(), stat.getCount());
        }
    }

    /*
     * 推送记录
     *
     */
    public static Map<String, Object> testQueryPushList(int page, int rows, String startTime, String endTime, String pushType) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();

        ListPushRecordsRequest request = new ListPushRecordsRequest();
        request.setAppKey(appKey);
        request.setPage(page);
        request.setPageSize(rows);

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date start = new Date();
        Date end = new Date();

        if (null == startTime || startTime.equals("")) {//如果开始时间为空，取前7天
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 7);
            Date today = calendar.getTime();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String result = format.format(today);
            startTime = result + "T00:00:00Z";

        } else {
            start = df.parse(startTime);
            startTime = startTime.replace(" ", "T") + "Z";

        }
        request.setStartTime(startTime);

        if (null == endTime || endTime.equals("")) {//取当前时间
            Date endDate = new Date(System.currentTimeMillis());
            endTime = ParameterHelper.getISO8601Time(endDate);
        } else {
            end = df.parse(endTime);
            endTime = endTime.replace(" ", "T") + "Z";
        }

        long l = end.getTime() - start.getTime();
        long day = l / (24 * 60 * 60 * 1000);
        map.put("error", "");
        if (day > 31) {
            map.put("error", "只能查询31天以内的记录");
            return map;
        }
        request.setEndTime(endTime);

        request.setPushType(pushType);

        ListPushRecordsResponse response = client.getAcsResponse(request);
        map.put("total", response.getTotal());

        List<PushResult> resultList = new ArrayList<>();
        for (ListPushRecordsResponse.PushMessageInfo stat : response.getPushMessageInfos()) {
            logger.info("messageId: %s, title: %s, body: %s,deviceType: %s,pushTime: %s\n",
                    stat.getMessageId(), stat.getTitle(), stat.getBody(), stat.getDeviceType(), stat.getPushTime());

            PushResult result = new PushResult();
            result.setMessageId(stat.getMessageId());
            result.setTitle(stat.getTitle());
            result.setBody(stat.getBody());
            result.setDeviceType(stat.getDeviceType());
            result.setPushTime(stat.getPushTime());
            resultList.add(result);
        }
        map.put("resultList", resultList);
        return map;
    }

    public static void main(String[] arg) {
        try {
//            PushService.advancedPush("六六", "15711111111发布煤炭从北京市朝阳区香河园街道光熙家园社区居委会光熙家园南区运输到北京市昌平区史各庄街道生命科学园东路7号中关村生命科学园", "DEVICE", "8923c0c22c4a468990a5ce4e2109b1f6", true);
//            testQueryPushStatByApp();
//            testQueryPushStatByMsg();
//            testQueryDeviceStat();
//            testQueryPushList("2019-05-15T00:00:00Z", "2019-06-5T00:00:00Z", "NOTICE");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
