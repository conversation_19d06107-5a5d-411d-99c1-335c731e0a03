# 煤炭指数爬虫模块 (Coal Crawler)

## 概述

本模块是煤炭项目的数据爬虫模块，主要用于爬取煤易宝网站(https://www.meiyibao.com/)的煤炭指数数据，包括CCI指数、CCTD指数和神华外购价格数据。

## 🚀 快速开始

### 1. 克隆项目并进入爬虫模块
```bash
cd coal-common/coal-crawler
```

### 2. 安装依赖和浏览器
```bash
mvn clean compile
```

### 3. 配置数据库
确保MongoDB服务正在运行，并在application.yml中配置连接信息。

### 4. 启动服务
```bash
mvn spring-boot:run
```

### 5. 测试爬虫
```bash
# 测试健康检查
curl http://localhost:8081/crawler/api/coal/crawler/health

# 爬取CCI指数数据
curl -X POST http://localhost:8081/crawler/api/coal/crawler/crawl/latest/CCI
```

## 功能特性

- 🚀 基于Playwright的现代化网页爬虫
- 📊 支持多种煤炭指数数据爬取（CCI、CCTD、神华外购）
- 💾 数据自动存储到MongoDB数据库
- ⏰ 支持定时任务自动爬取
- 🔄 智能数据更新检测
- 📈 图表数据和文本数据双重爬取
- 🛡️ 完善的错误处理和重试机制
- 📝 详细的爬取日志记录

## 技术栈

- **Java 8** - 完全兼容Java 8语法
- **Spring Boot 2.1.3**
- **Playwright** - 网页自动化和爬虫
- **MongoDB** - 数据存储
- **Morphia** - MongoDB ORM
- **Jackson** - JSON处理

## Java 8兼容性

本项目完全兼容Java 8，所有代码都使用Java 8语法编写：

- ✅ 使用字符串拼接代替文本块（Text Blocks）
- ✅ 使用传统的匿名类和Lambda表达式
- ✅ 兼容Java 8的日期时间API
- ✅ 所有JavaScript代码使用字符串拼接方式

## 项目结构

```
coal-crawler/
├── src/main/java/com/erdos/coal/crawler/
│   ├── controller/          # REST API控制器
│   │   └── CoalIndexCrawlerController.java
│   ├── service/            # 业务服务层
│   │   ├── CoalIndexCrawlerService.java
│   │   └── impl/
│   │       └── CoalIndexCrawlerServiceImpl.java
│   ├── dao/                # 数据访问层
│   │   ├── CoalIndexDataDao.java
│   │   ├── CoalCrawlLogDao.java
│   │   └── impl/
│   │       ├── CoalIndexDataDaoImpl.java
│   │       └── CoalCrawlLogDaoImpl.java
│   ├── entity/             # 数据实体类
│   │   ├── CoalIndexData.java
│   │   └── CoalCrawlLog.java
│   ├── dto/                # 数据传输对象
│   │   ├── CoalIndexCrawlRequest.java
│   │   ├── CoalIndexCrawlResponse.java
│   │   └── CoalIndexDataDto.java
│   ├── enums/              # 枚举类
│   │   └── IndexType.java
│   ├── core/               # 核心爬虫逻辑
│   │   └── PlaywrightCrawler.java
│   ├── config/             # 配置类
│   │   └── CrawlerConfig.java
│   └── schedule/           # 定时任务
│       └── CoalIndexCrawlerSchedule.java
└── src/test/java/          # 测试类
    └── com/erdos/coal/crawler/
        └── CoalIndexCrawlerTest.java
```

## 数据模型

### 煤炭指数数据 (CoalIndexData)
- 指数类型 (CCI/CCTD/SHENHUA)
- 数据日期
- 热值 (大卡)
- 价格 (元/吨)
- 涨跌幅 (%)
- 涨跌趋势
- 较上期变化金额

### 爬虫日志 (CoalCrawlLog)
- 指数类型
- 开始时间/结束时间
- 爬取状态
- 数据条数
- 错误信息

## API接口

### 1. 爬取数据
```http
POST /api/coal/crawler/crawl
Content-Type: application/json

{
    "indexType": "CCI",
    "forceRefresh": true,
    "timeout": 30000
}
```

### 2. 爬取最新数据
```http
POST /api/coal/crawler/crawl/latest/{indexType}
```

### 3. 爬取所有类型数据
```http
POST /api/coal/crawler/crawl/all
```

### 4. 查询数据
```http
GET /api/coal/crawler/data/{indexType}?startDate=2024-01-01&endDate=2024-01-31
```

### 5. 查询最新数据
```http
GET /api/coal/crawler/data/latest/{indexType}
```

### 6. 获取爬取历史
```http
GET /api/coal/crawler/history/{indexType}?limit=10
```

### 7. 获取数据统计
```http
GET /api/coal/crawler/statistics/{indexType}
```

## 配置说明

### application.yml配置
```yaml
coal:
  crawler:
    default-timeout: 30000      # 默认超时时间
    headless: true              # 无头模式
    page-load-wait: 2000        # 页面加载等待时间
    retry-count: 3              # 重试次数
    update-interval: 3600000    # 更新间隔(1小时)
    enable-schedule: false      # 是否启用定时任务
    schedule-cron: "0 0 */1 * * ?" # 定时任务表达式
```

## 使用方法

### 1. 环境准备
确保已安装：
- Java 8+
- MongoDB
- Maven

### 2. 安装Playwright浏览器
```bash
# 方法1: 使用Maven插件（推荐）
mvn compile

# 方法2: 手动安装
mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install chromium"
```

### 3. 启动应用
```bash
# 启动爬虫服务
mvn spring-boot:run

# 或者运行主类
java -jar target/coal-crawler-1.0-SNAPSHOT.jar
```

### 4. 测试爬虫功能

#### 4.1 使用API接口测试
```bash
# 健康检查
curl http://localhost:8081/crawler/api/coal/crawler/health

# 爬取CCI指数数据
curl -X POST http://localhost:8081/crawler/api/coal/crawler/crawl/latest/CCI

# 爬取CCTD指数数据
curl -X POST http://localhost:8081/crawler/api/coal/crawler/crawl/latest/CCTD

# 爬取神华外购数据
curl -X POST http://localhost:8081/crawler/api/coal/crawler/crawl/latest/SHENHUA
```

#### 4.2 使用Java代码测试
```java
@Autowired
private CoalIndexCrawlerService crawlerService;

// 爬取CCI指数数据
CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.CCI);
request.setForceRefresh(true);
request.setTimeout(60000L);
CoalIndexCrawlResponse response = crawlerService.crawlCoalIndexData(request);
```

#### 4.3 使用调试工具
```java
// 运行调试工具查看页面元素
public static void main(String[] args) {
    CrawlerDebugUtil.debugPageElements();
}
```

#### 4.4 运行单元测试
```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest=CoalIndexCrawlerTest#testCrawlCCIData
```

## 爬取策略

### 1. 数据源分析
- **目标网站**: https://www.meiyibao.com/
- **数据类型**: 
  - 左侧文本数据（当前价格、热值、涨跌）
  - 图表数据（历史价格趋势）

### 2. 爬取流程
1. 启动Playwright浏览器
2. 访问目标网站
3. 点击对应指数标签页
4. 等待页面加载完成
5. 提取文本数据
6. 执行JavaScript获取图表数据
7. 数据清洗和格式化
8. 保存到MongoDB数据库

### 3. 数据处理
- 自动计算涨跌幅
- 重复数据检测和更新
- 数据完整性验证
- 异常数据过滤

## 定时任务

### 1. 启用定时任务
```yaml
coal:
  crawler:
    enable-schedule: true
```

### 2. 任务类型
- **全量爬取**: 每小时执行一次
- **单独爬取**: 每30分钟检查各指数
- **健康检查**: 每5分钟检查服务状态
- **日志清理**: 每天凌晨2点清理过期日志

## 监控和日志

### 1. 日志级别
- DEBUG: 详细的爬取过程
- INFO: 关键操作信息
- WARN: 警告信息
- ERROR: 错误信息

### 2. 监控指标
- 爬取成功率
- 数据更新时间
- 响应时间
- 错误统计

## 故障排除

### 1. 常见问题
- **Java版本兼容性**: 确保使用Java 8，不支持Java 13+的文本块语法
- **Playwright安装失败**: 检查网络连接，手动安装浏览器
- **页面加载超时**: 增加timeout配置
- **数据解析失败**: 检查网站结构是否变化
- **MongoDB连接失败**: 检查数据库配置和连接
- **JavaScript执行失败**: 运行JavaScriptCompatibilityTest检查兼容性

### 2. 调试方法
- 启用DEBUG日志
- 关闭headless模式观察浏览器行为
- 使用测试类单独测试各个功能

## 扩展开发

### 1. 添加新的指数类型
1. 在IndexType枚举中添加新类型
2. 在PlaywrightCrawler中添加对应的爬取逻辑
3. 更新相关配置和文档

### 2. 自定义数据处理
1. 继承或修改CoalIndexCrawlerServiceImpl
2. 实现自定义的数据清洗逻辑
3. 添加新的验证规则

## 版本历史

- **v1.0.0**: 初始版本，支持基本的煤炭指数爬取功能

## 许可证

本项目采用MIT许可证。
