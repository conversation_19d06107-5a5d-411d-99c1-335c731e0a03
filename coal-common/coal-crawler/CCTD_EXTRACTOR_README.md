# CCTD指数提取工具 - Canvas交互版本

## 功能概述

本工具专门用于从CCTD指数网站的折线图中提取数据。通过模拟鼠标在canvas上的交互，获取不同日期对应的煤炭价格信息。

## 主要特性

### 1. Canvas折线图交互
- 自动定位 `class="coal-char"` 元素
- 查找其中的canvas元素
- 模拟鼠标在折线图上的移动和点击
- 提取tooltip或弹出信息中的数据

### 2. 多层数据提取策略
1. **Canvas交互提取** - 主要方法，通过鼠标交互获取动态数据
2. **HTML内容提取** - 备用方法，从静态HTML中提取数据
3. **页面文本提取** - 第三备用方法，从页面文本中提取数据
4. **模拟数据生成** - 测试用途，生成符合格式的模拟数据

### 3. 智能采样算法
- 在canvas的X轴上进行多点采样（默认20个采样点）
- 在不同Y坐标位置采样，以捕获多条折线的数据
- 自动过滤无效数据，确保价格在合理范围内（300-800元）

## 数据格式

提取的数据格式如下：
```
07-22: 5500kCal=643元, 5000kCal=580元, 4500kCal=514元
07-23: 5500kCal=645元, 5000kCal=582元, 4500kCal=516元
...
```

## 核心方法说明

### `extractDataFromCanvas(Page page)`
- 主要的canvas交互方法
- 查找coal-char类和canvas元素
- 获取canvas边界框并计算采样区域

### `extractDataByMouseInteraction(Page page, Locator canvas, BoundingBox bounds)`
- 执行具体的鼠标交互操作
- 在折线图上进行多点采样
- 提取每个采样点的数据

### `extractTooltipData(Page page)`
- 提取鼠标悬停时显示的tooltip数据
- 支持多种tooltip选择器
- 自动识别最新更新的文本内容

### `parseTooltipData(String tooltipData, Map result)`
- 解析tooltip中的日期、热值和价格信息
- 使用正则表达式匹配数据格式
- 自动验证数据有效性

## 使用方法

### 1. 直接运行
```java
public static void main(String[] args) {
    Map<String, Map<Integer, Integer>> data = CCTDExtractor.extractCCTDData();
    // 处理提取到的数据
}
```

### 2. 集成到其他系统
```java
// 获取CCTD指数数据
Map<String, Map<Integer, Integer>> cctdData = CCTDExtractor.extractCCTDData();

// 遍历数据
for (Map.Entry<String, Map<Integer, Integer>> entry : cctdData.entrySet()) {
    String date = entry.getKey();
    Map<Integer, Integer> prices = entry.getValue();
    
    for (Map.Entry<Integer, Integer> priceEntry : prices.entrySet()) {
        int calorific = priceEntry.getKey();  // 热值 (kCal)
        int price = priceEntry.getValue();    // 价格 (元)
        
        // 处理具体的价格数据
        System.out.println(String.format("%s: %dkCal=%d元", date, calorific, price));
    }
}
```

## 配置说明

### 浏览器配置
- 使用Chromium浏览器
- 非无头模式（便于调试）
- 2分钟超时设置
- 1920x1080分辨率

### 采样配置
- X轴采样点数：20个
- Y轴采样位置：30%, 50%, 70%
- 鼠标移动间隔：200ms
- 点击等待时间：500ms

## 注意事项

1. **网络依赖**：需要稳定的网络连接访问CCTD指数网站
2. **浏览器依赖**：需要安装Playwright的Chromium浏览器
3. **动态内容**：网站内容可能动态变化，需要适当调整等待时间
4. **反爬虫**：网站可能有反爬虫机制，建议适当控制访问频率

## 测试

运行测试类验证功能：
```bash
mvn test -Dtest=CCTDExtractorTest
```

或直接运行测试类的main方法：
```java
CCTDExtractorTest.main(new String[]{});
```

## 故障排除

1. **找不到canvas元素**
   - 检查网站结构是否变化
   - 确认CCTD标签页是否正确点击

2. **提取不到数据**
   - 增加等待时间
   - 检查tooltip选择器是否正确
   - 查看浏览器控制台错误信息

3. **数据格式异常**
   - 检查正则表达式匹配规则
   - 验证价格范围过滤条件

## 版本历史

- v1.0 - 基础HTML提取功能
- v2.0 - 新增Canvas交互功能，支持动态数据提取
