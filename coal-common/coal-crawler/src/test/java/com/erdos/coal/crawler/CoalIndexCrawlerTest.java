package com.erdos.coal.crawler;

import com.erdos.coal.crawler.dto.CoalIndexCrawlRequest;
import com.erdos.coal.crawler.dto.CoalIndexCrawlResponse;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.CoalIndexCrawlerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 煤炭指数爬虫测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CoalIndexCrawlerTest {
    
    @Autowired
    private CoalIndexCrawlerService coalIndexCrawlerService;
    
    @Test
    public void testCrawlCCIData() {
        System.out.println("=== 测试爬取CCI指数数据 ===");

        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.CCI);
        request.setForceRefresh(true);
        request.setTimeout(60000L);

        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);

        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("数据条数: " + response.getDataCount());
        System.out.println("耗时: " + response.getDuration() + "ms");

        if (response.getSuccess() && response.getDataList() != null) {
            response.getDataList().forEach(data -> {
                System.out.println("数据: " + data.getIndexType() +
                                 ", 热值: " + data.getCalorificValue() + "kCal" +
                                 ", 价格: " + data.getPrice() + "元" +
                                 ", 涨跌: " + data.getChangeAmount());
            });
        } else {
            System.out.println("错误信息: " + response.getErrorMessage());
        }
    }
    
    @Test
    public void testCrawlCCTDData() {
        System.out.println("=== 测试爬取CCTD指数数据 ===");

        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.CCTD);
        request.setForceRefresh(true);
        request.setTimeout(60000L);

        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);

        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("数据条数: " + response.getDataCount());
        System.out.println("耗时: " + response.getDuration() + "ms");

        if (response.getSuccess() && response.getDataList() != null) {
            response.getDataList().forEach(data -> {
                System.out.println("数据: " + data.getIndexType() +
                                 ", 热值: " + data.getCalorificValue() + "kCal" +
                                 ", 价格: " + data.getPrice() + "元");
            });
        } else {
            System.out.println("错误信息: " + response.getErrorMessage());
        }
    }
    
    @Test
    public void testCrawlShenhuaData() {
        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.SHENHUA);
        request.setForceRefresh(true);
        
        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);
        
        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("数据条数: " + response.getDataCount());
        System.out.println("耗时: " + response.getDuration() + "ms");
    }
    
    @Test
    public void testCrawlAllData() {
        System.out.println("=== 测试爬取所有指数数据 ===");

        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlAllLatestData();

        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("总数据条数: " + response.getDataCount());
        System.out.println("总耗时: " + response.getDuration() + "ms");

        if (response.getDataList() != null) {
            response.getDataList().forEach(data -> {
                System.out.println("指数类型: " + data.getIndexType() +
                                 ", 热值: " + data.getCalorificValue() + "kCal" +
                                 ", 价格: " + data.getPrice() + "元" +
                                 ", 涨跌: " + data.getChangeAmount());
            });
        } else {
            System.out.println("错误信息: " + response.getErrorMessage());
        }
    }

    /**
     * 测试单独的Playwright爬虫功能
     */
    @Test
    public void testPlaywrightCrawlerDirectly() {
        System.out.println("=== 直接测试Playwright爬虫 ===");

        try {
            // 这里可以直接测试PlaywrightCrawler的功能
            // 注意：需要确保MongoDB连接正常
            System.out.println("开始测试爬虫功能...");

            // 测试CCI指数
            testSingleIndexType(IndexType.CCI);

            // 测试CCTD指数
            testSingleIndexType(IndexType.CCTD);

            // 测试神华外购
            testSingleIndexType(IndexType.SHENHUA);

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void testSingleIndexType(IndexType indexType) {
        System.out.println("\n--- 测试 " + indexType + " ---");

        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(indexType);
        request.setForceRefresh(true);
        request.setTimeout(60000L);

        try {
            CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);

            System.out.println("结果: " + (response.getSuccess() ? "成功" : "失败"));
            System.out.println("数据条数: " + response.getDataCount());
            System.out.println("耗时: " + response.getDuration() + "ms");

            if (!response.getSuccess()) {
                System.out.println("错误: " + response.getErrorMessage());
            }

        } catch (Exception e) {
            System.err.println("测试" + indexType + "失败: " + e.getMessage());
        }
    }
}
