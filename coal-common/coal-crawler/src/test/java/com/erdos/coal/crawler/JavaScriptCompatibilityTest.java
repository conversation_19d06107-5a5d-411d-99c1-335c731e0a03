package com.erdos.coal.crawler;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.microsoft.playwright.*;
import org.junit.Test;

/**
 * JavaScript兼容性测试类
 * 用于测试JavaScript代码在Java 8环境下的兼容性
 */
public class JavaScriptCompatibilityTest {
    
    private static final String BASE_URL = "https://www.meiyibao.com/";
    
    @Test
    public void testJavaScriptConstants() {
        System.out.println("=== 测试JavaScript常量兼容性 ===");
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            BrowserContext context = browser.newContext();
            Page page = context.newPage();
            
            // 访问目标网站
            page.navigate(BASE_URL);
            
            // 等待页面加载
            page.waitForSelector(".coal-ing", new Page.WaitForSelectorOptions().setTimeout(30_000));
            
            // 测试各个JavaScript常量
            testCheckPageReady(page);
            testCheckEchartsAvailable(page);
            testGetPriceElements(page);
            testGetCurrentTabInfo(page);
            testGetEchartsData(page);
            
            page.close();
            context.close();
            browser.close();
            
            System.out.println("所有JavaScript常量测试完成");
            
        } catch (Exception e) {
            System.err.println("JavaScript兼容性测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void testCheckPageReady(Page page) {
        try {
            System.out.println("\n--- 测试页面就绪检查 ---");
            Object result = page.evaluate(JavaScriptConstants.CHECK_PAGE_READY);
            System.out.println("页面就绪状态: " + result);
        } catch (Exception e) {
            System.err.println("页面就绪检查失败: " + e.getMessage());
        }
    }
    
    private void testCheckEchartsAvailable(Page page) {
        try {
            System.out.println("\n--- 测试ECharts可用性检查 ---");
            Object result = page.evaluate(JavaScriptConstants.CHECK_ECHARTS_AVAILABLE);
            System.out.println("ECharts信息: " + result);
        } catch (Exception e) {
            System.err.println("ECharts可用性检查失败: " + e.getMessage());
        }
    }
    
    private void testGetPriceElements(Page page) {
        try {
            System.out.println("\n--- 测试价格元素获取 ---");
            Object result = page.evaluate(JavaScriptConstants.GET_PRICE_ELEMENTS);
            System.out.println("价格元素数据: " + result.toString().substring(0, Math.min(200, result.toString().length())) + "...");
        } catch (Exception e) {
            System.err.println("价格元素获取失败: " + e.getMessage());
        }
    }
    
    private void testGetCurrentTabInfo(Page page) {
        try {
            System.out.println("\n--- 测试当前标签页信息 ---");
            Object result = page.evaluate(JavaScriptConstants.GET_CURRENT_TAB_INFO);
            System.out.println("标签页信息: " + result);
        } catch (Exception e) {
            System.err.println("标签页信息获取失败: " + e.getMessage());
        }
    }
    
    private void testGetEchartsData(Page page) {
        try {
            System.out.println("\n--- 测试ECharts数据获取 ---");
            Object result = page.evaluate(JavaScriptConstants.GET_ECHARTS_DATA);
            System.out.println("ECharts数据: " + result);
        } catch (Exception e) {
            System.err.println("ECharts数据获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试字符串拼接的JavaScript代码
     */
    @Test
    public void testStringConcatenation() {
        System.out.println("=== 测试字符串拼接JavaScript ===");
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            Page page = browser.newPage();
            
            // 测试简单的JavaScript代码
            String simpleJs = "() => { return 'Hello from JavaScript'; }";
            Object result1 = page.evaluate(simpleJs);
            System.out.println("简单JS结果: " + result1);
            
            // 测试复杂的字符串拼接JavaScript代码
            String complexJs = "() => {" +
                    "    const data = {" +
                    "        message: 'Hello World'," +
                    "        timestamp: new Date().getTime()," +
                    "        userAgent: navigator.userAgent" +
                    "    };" +
                    "    return data;" +
                    "}";
            Object result2 = page.evaluate(complexJs);
            System.out.println("复杂JS结果: " + result2);
            
            // 测试等待元素的JavaScript代码
            String waitJs = JavaScriptConstants.waitForElement("body", 5000);
            try {
                Object result3 = page.evaluate(waitJs);
                System.out.println("等待元素结果: " + result3);
            } catch (Exception e) {
                System.out.println("等待元素测试: " + e.getMessage());
            }
            
            page.close();
            browser.close();
            
            System.out.println("字符串拼接JavaScript测试完成");
            
        } catch (Exception e) {
            System.err.println("字符串拼接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试所有JavaScript常量的语法正确性
     */
    @Test
    public void testJavaScriptSyntax() {
        System.out.println("=== 测试JavaScript语法正确性 ===");
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            Page page = browser.newPage();
            
            // 创建一个简单的HTML页面来测试JavaScript
            String html = "<html><body><div class='coal-ing'>Test</div></body></html>";
            page.setContent(html);
            
            // 测试所有JavaScript常量
            String[] jsConstants = {
                    JavaScriptConstants.GET_ECHARTS_DATA,
                    JavaScriptConstants.CHECK_PAGE_READY,
                    JavaScriptConstants.GET_PRICE_ELEMENTS,
                    JavaScriptConstants.CHECK_ECHARTS_AVAILABLE,
                    JavaScriptConstants.GET_CURRENT_TAB_INFO,
                    JavaScriptConstants.SIMULATE_CHART_HOVER
            };
            
            String[] constantNames = {
                    "GET_ECHARTS_DATA",
                    "CHECK_PAGE_READY", 
                    "GET_PRICE_ELEMENTS",
                    "CHECK_ECHARTS_AVAILABLE",
                    "GET_CURRENT_TAB_INFO",
                    "SIMULATE_CHART_HOVER"
            };
            
            for (int i = 0; i < jsConstants.length; i++) {
                try {
                    Object result = page.evaluate(jsConstants[i]);
                    System.out.println(constantNames[i] + ": ✓ 语法正确");
                } catch (Exception e) {
                    System.err.println(constantNames[i] + ": ✗ 语法错误 - " + e.getMessage());
                }
            }
            
            page.close();
            browser.close();
            
            System.out.println("JavaScript语法测试完成");
            
        } catch (Exception e) {
            System.err.println("JavaScript语法测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
