package com.erdos.coal.crawler;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.microsoft.playwright.*;
import org.junit.Test;

/**
 * JavaScript语法测试类
 * 用于验证JavaScript代码的语法正确性
 */
public class JavaScriptSyntaxTest {
    
    @Test
    public void testJavaScriptSyntax() {
        System.out.println("=== 测试JavaScript语法 ===");
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            Page page = browser.newPage();
            
            // 创建一个简单的HTML页面
            String html = "<html><body><div class='coal-ing'>Test</div></body></html>";
            page.setContent(html);
            
            // 测试每个JavaScript常量
            testJavaScriptConstant(page, "CHECK_ECHARTS_AVAILABLE", JavaScriptConstants.CHECK_ECHARTS_AVAILABLE);
            testJavaScriptConstant(page, "CHECK_PAGE_READY", JavaScriptConstants.CHECK_PAGE_READY);
            testJavaScriptConstant(page, "GET_PRICE_ELEMENTS", JavaScriptConstants.GET_PRICE_ELEMENTS);
            testJavaScriptConstant(page, "GET_CURRENT_TAB_INFO", JavaScriptConstants.GET_CURRENT_TAB_INFO);
            testJavaScriptConstant(page, "GET_ECHARTS_DATA", JavaScriptConstants.GET_ECHARTS_DATA);
            testJavaScriptConstant(page, "SIMULATE_CHART_HOVER", JavaScriptConstants.SIMULATE_CHART_HOVER);
            
            // 测试waitForElement方法
            String waitJs = JavaScriptConstants.waitForElement("body", 1000);
            testJavaScriptConstant(page, "waitForElement", waitJs);
            
            page.close();
            browser.close();
            
            System.out.println("所有JavaScript语法测试完成");
            
        } catch (Exception e) {
            System.err.println("JavaScript语法测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void testJavaScriptConstant(Page page, String name, String jsCode) {
        try {
            System.out.println("\n测试: " + name);
            System.out.println("代码长度: " + jsCode.length());
            
            // 检查代码是否以()=>开头和}结尾
            if (!jsCode.trim().startsWith("() =>") && !jsCode.trim().startsWith("()=>")) {
                System.err.println("  ✗ 代码格式错误: 不是箭头函数");
                return;
            }
            
            if (!jsCode.trim().endsWith("}")) {
                System.err.println("  ✗ 代码格式错误: 不以}结尾");
                return;
            }
            
            // 尝试执行JavaScript代码
            Object result = page.evaluate(jsCode);
            System.out.println("  ✓ 语法正确，结果: " + result);
            
        } catch (Exception e) {
            System.err.println("  ✗ 语法错误: " + e.getMessage());
            
            // 打印代码以便调试
            System.err.println("  代码内容:");
            String[] lines = jsCode.split("\\+");
            for (int i = 0; i < Math.min(lines.length, 10); i++) {
                System.err.println("    " + i + ": " + lines[i].trim());
            }
        }
    }
    
    /**
     * 测试简化的JavaScript代码
     */
    @Test
    public void testSimpleJavaScript() {
        System.out.println("=== 测试简化JavaScript ===");
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            Page page = browser.newPage();
            page.setContent("<html><body><div>Test</div></body></html>");
            
            // 测试简单的JavaScript代码
            String simpleJs = "() => { return 'Hello World'; }";
            Object result1 = page.evaluate(simpleJs);
            System.out.println("简单JS结果: " + result1);
            
            // 测试ECharts检查的简化版本
            String echartsCheckJs = "() => { return { available: typeof window.echarts !== 'undefined' }; }";
            Object result2 = page.evaluate(echartsCheckJs);
            System.out.println("ECharts检查结果: " + result2);
            
            page.close();
            browser.close();
            
        } catch (Exception e) {
            System.err.println("简化JavaScript测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
