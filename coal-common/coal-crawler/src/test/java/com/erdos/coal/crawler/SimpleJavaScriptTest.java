package com.erdos.coal.crawler;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.microsoft.playwright.*;

/**
 * 简单的JavaScript测试类
 */
public class SimpleJavaScriptTest {
    
    public static void main(String[] args) {
        System.out.println("=== 简单JavaScript测试 ===");
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            Page page = browser.newPage();
            page.setContent("<html><body><div class='coal-ing'>Test</div></body></html>");
            
            // 测试简单的JavaScript
            testSimpleJS(page);
            
            // 测试ECharts检查
            testEChartsCheck(page);
            
            // 测试页面就绪检查
            testPageReady(page);
            
            page.close();
            browser.close();
            
            System.out.println("所有测试完成");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSimpleJS(Page page) {
        try {
            System.out.println("\n1. 测试简单JavaScript...");
            String simpleJs = "() => { return 'Hello World'; }";
            Object result = page.evaluate(simpleJs);
            System.out.println("   结果: " + result);
            System.out.println("   ✓ 简单JavaScript测试通过");
        } catch (Exception e) {
            System.err.println("   ✗ 简单JavaScript测试失败: " + e.getMessage());
        }
    }
    
    private static void testEChartsCheck(Page page) {
        try {
            System.out.println("\n2. 测试ECharts检查...");
            Object result = page.evaluate(JavaScriptConstants.CHECK_ECHARTS_AVAILABLE);
            System.out.println("   结果: " + result);
            System.out.println("   ✓ ECharts检查测试通过");
        } catch (Exception e) {
            System.err.println("   ✗ ECharts检查测试失败: " + e.getMessage());
        }
    }
    
    private static void testPageReady(Page page) {
        try {
            System.out.println("\n3. 测试页面就绪检查...");
            Object result = page.evaluate(JavaScriptConstants.CHECK_PAGE_READY);
            System.out.println("   结果: " + result);
            System.out.println("   ✓ 页面就绪检查测试通过");
        } catch (Exception e) {
            System.err.println("   ✗ 页面就绪检查测试失败: " + e.getMessage());
        }
    }
}
