package com.erdos.coal.crawler.util;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * CCTD指数提取工具测试类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class CCTDExtractorTest {

    private static final Logger logger = LoggerFactory.getLogger(CCTDExtractorTest.class);

    @Test
    public void testExtractCCTDData() {
        logger.info("开始测试CCTD指数数据提取（带去重功能）...");

        try {
            // 调用提取方法
            Map<String, Map<Integer, Integer>> data = CCTDExtractor.extractCCTDData();

            // 验证结果
            if (data != null && !data.isEmpty()) {
                logger.info("成功提取到{}天的数据", data.size());

                // 验证去重效果
                Set<String> allDataPoints = new HashSet<>();
                int totalDataPoints = 0;

                for (Map.Entry<String, Map<Integer, Integer>> entry : data.entrySet()) {
                    String date = entry.getKey();
                    Map<Integer, Integer> prices = entry.getValue();

                    logger.info("日期: {}", date);
                    for (Map.Entry<Integer, Integer> priceEntry : prices.entrySet()) {
                        int calorific = priceEntry.getKey();
                        int price = priceEntry.getValue();

                        String dataPoint = date + "-" + calorific + "kCal-" + price + "元";
                        allDataPoints.add(dataPoint);
                        totalDataPoints++;

                        logger.info("  {}kCal = {}元", calorific, price);
                    }
                }

                logger.info("总数据点: {}, 去重后数据点: {}", totalDataPoints, allDataPoints.size());

                // 验证数据格式和去重效果
                for (Map.Entry<String, Map<Integer, Integer>> entry : data.entrySet()) {
                    String date = entry.getKey();
                    Map<Integer, Integer> prices = entry.getValue();

                    // 验证日期格式
                    if (!date.matches("07-\\d{2}")) {
                        logger.warn("日期格式不正确: {}", date);
                    }

                    // 验证每个日期的热值不重复
                    Set<Integer> calorificValues = new HashSet<>(prices.keySet());
                    if (calorificValues.size() != prices.size()) {
                        logger.warn("日期{}存在重复的热值数据", date);
                    }

                    // 验证价格数据
                    for (Map.Entry<Integer, Integer> priceEntry : prices.entrySet()) {
                        int calorific = priceEntry.getKey();
                        int price = priceEntry.getValue();

                        if (calorific != 5500 && calorific != 5000 && calorific != 4500) {
                            logger.warn("热值不在预期范围: {}kCal", calorific);
                        }

                        if (price < 300 || price > 800) {
                            logger.warn("价格超出预期范围: {}元", price);
                        }
                    }
                }

                // 打印示例数据格式
                logger.info("数据格式示例：");
                int count = 0;
                for (Map.Entry<String, Map<Integer, Integer>> entry : data.entrySet()) {
                    if (count >= 2) break; // 只打印前2天

                    String date = entry.getKey();
                    Map<Integer, Integer> prices = entry.getValue();

                    StringBuilder example = new StringBuilder(date + ": ");
                    List<String> priceStrings = new ArrayList<>();

                    // 按热值排序显示
                    int[] calorificOrder = {5500, 5000, 4500};
                    for (int calorific : calorificOrder) {
                        if (prices.containsKey(calorific)) {
                            priceStrings.add(calorific + "kCal对应的是" + prices.get(calorific) + "元");
                        }
                    }

                    example.append(String.join(", ", priceStrings));
                    logger.info(example.toString());
                    count++;
                }

                logger.info("数据验证完成，去重功能正常");

            } else {
                logger.warn("未提取到任何数据");
            }

        } catch (Exception e) {
            logger.error("测试过程中发生异常: {}", e.getMessage(), e);
        }

        logger.info("CCTD指数数据提取测试完成");
    }
    
    /**
     * 测试主方法
     */
    public static void main(String[] args) {
        CCTDExtractorTest test = new CCTDExtractorTest();
        test.testExtractCCTDData();
    }
}
