package com.erdos.coal.crawler.service.impl;

import com.erdos.coal.crawler.core.PlaywrightCrawler;
import com.erdos.coal.crawler.dao.CoalCrawlLogDao;
import com.erdos.coal.crawler.dao.CoalIndexDataDao;
import com.erdos.coal.crawler.dto.CoalIndexCrawlRequest;
import com.erdos.coal.crawler.dto.CoalIndexCrawlResponse;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.entity.CoalCrawlLog;
import com.erdos.coal.crawler.entity.CoalIndexData;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.CoalIndexCrawlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 煤炭指数爬虫服务实现类
 */
@Service
public class CoalIndexCrawlerServiceImpl implements CoalIndexCrawlerService {
    
    private static final Logger logger = LoggerFactory.getLogger(CoalIndexCrawlerServiceImpl.class);
    
    @Autowired
    private PlaywrightCrawler playwrightCrawler;
    
    @Autowired
    private CoalIndexDataDao coalIndexDataDao;
    
    @Autowired
    private CoalCrawlLogDao coalCrawlLogDao;
    
    @Override
    public CoalIndexCrawlResponse crawlCoalIndexData(CoalIndexCrawlRequest request) {
        Date startTime = new Date();
        CoalIndexCrawlResponse response = new CoalIndexCrawlResponse();
        response.setStartTime(startTime);
        
        // 创建爬虫日志
        CoalCrawlLog crawlLog = createCrawlLog(request.getIndexType(), startTime);
        coalCrawlLogDao.save(crawlLog);
        response.setLogId(crawlLog.getObjectId().toString());
        
        try {
            // 检查是否需要强制刷新
            if (!request.getForceRefresh() && !needUpdate(request.getIndexType())) {
                logger.info("数据较新，无需重新爬取: {}", request.getIndexType());
                response.setSuccess(true);
                response.setDataCount(0);
                response.setEndTime(new Date());
                response.setDuration(response.getEndTime().getTime() - startTime.getTime());
                return response;
            }
            
            // 执行爬虫
            List<CoalIndexDataDto> crawledData = playwrightCrawler.crawlCoalIndexData(
                    request.getIndexType(), request.getTimeout());
            
            if (CollectionUtils.isEmpty(crawledData)) {
                throw new RuntimeException("未爬取到任何数据");
            }
            
            // 数据处理和保存
            List<CoalIndexData> savedData = processAndSaveData(crawledData, request.getIndexType());
            
            // 更新响应信息
            response.setSuccess(true);
            response.setDataCount(savedData.size());
            response.setDataList(crawledData);
            response.setEndTime(new Date());
            response.setDuration(response.getEndTime().getTime() - startTime.getTime());
            
            // 更新日志状态
            coalCrawlLogDao.updateStatus(crawlLog.getObjectId().toString(), 1, null);
            
            logger.info("爬取煤炭指数数据成功: {} 条数据", savedData.size());
            
        } catch (Exception e) {
            logger.error("爬取煤炭指数数据失败: {}", e.getMessage(), e);
            
            response.setSuccess(false);
            response.setErrorMessage(e.getMessage());
            response.setDataCount(0);
            response.setEndTime(new Date());
            response.setDuration(response.getEndTime().getTime() - startTime.getTime());
            
            // 更新日志状态
            coalCrawlLogDao.updateStatus(crawlLog.getObjectId().toString(), 0, e.getMessage());
        }
        
        return response;
    }
    
    @Override
    public CoalIndexCrawlResponse crawlLatestData(IndexType indexType) {
        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(indexType);
        request.setForceRefresh(true);
        return crawlCoalIndexData(request);
    }
    
    @Override
    public CoalIndexCrawlResponse crawlAllLatestData() {
        CoalIndexCrawlResponse response = new CoalIndexCrawlResponse();
        Date startTime = new Date();
        response.setStartTime(startTime);
        
        List<CoalIndexDataDto> allData = new ArrayList<>();
        int totalCount = 0;
        boolean allSuccess = true;
        StringBuilder errorMessages = new StringBuilder();
        
        // 爬取所有类型的数据
        for (IndexType indexType : IndexType.values()) {
            try {
                CoalIndexCrawlResponse singleResponse = crawlLatestData(indexType);
                if (singleResponse.getSuccess()) {
                    totalCount += singleResponse.getDataCount();
                    if (singleResponse.getDataList() != null) {
                        allData.addAll(singleResponse.getDataList());
                    }
                } else {
                    allSuccess = false;
                    errorMessages.append(indexType).append(": ").append(singleResponse.getErrorMessage()).append("; ");
                }
            } catch (Exception e) {
                allSuccess = false;
                errorMessages.append(indexType).append(": ").append(e.getMessage()).append("; ");
                logger.error("爬取{}数据失败: {}", indexType, e.getMessage(), e);
            }
        }
        
        response.setSuccess(allSuccess);
        response.setDataCount(totalCount);
        response.setDataList(allData);
        response.setEndTime(new Date());
        response.setDuration(response.getEndTime().getTime() - startTime.getTime());
        
        if (!allSuccess) {
            response.setErrorMessage(errorMessages.toString());
        }
        
        return response;
    }
    
    @Override
    public List<CoalIndexDataDto> queryCoalIndexData(IndexType indexType, Date startDate, Date endDate) {
        List<CoalIndexData> dataList = coalIndexDataDao.findByTypeAndDateRange(indexType, startDate, endDate);
        return convertToDto(dataList);
    }
    
    @Override
    public List<CoalIndexDataDto> queryLatestData(IndexType indexType) {
        List<CoalIndexData> dataList = coalIndexDataDao.findLatestByType(indexType);
        return convertToDto(dataList);
    }
    
    @Override
    public List<CoalIndexCrawlResponse> getCrawlHistory(IndexType indexType, int limit) {
        List<CoalCrawlLog> logs = coalCrawlLogDao.findRecentByType(indexType, limit);
        return logs.stream().map(this::convertLogToResponse).collect(Collectors.toList());
    }
    
    @Override
    public CoalIndexCrawlResponse manualCrawl(IndexType indexType, Long timeout) {
        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(indexType);
        request.setForceRefresh(true);
        request.setTimeout(timeout);
        return crawlCoalIndexData(request);
    }
    
    @Override
    public boolean needUpdate(IndexType indexType) {
        try {
            // 检查最新数据的时间
            List<CoalIndexData> latestData = coalIndexDataDao.findLatestByType(indexType);
            if (CollectionUtils.isEmpty(latestData)) {
                return true; // 没有数据，需要爬取
            }
            
            // 检查数据是否超过1小时
            Date latestDate = latestData.get(0).getDataDate();
            long timeDiff = System.currentTimeMillis() - latestDate.getTime();
            return timeDiff > 3600000; // 1小时 = 3600000毫秒
            
        } catch (Exception e) {
            logger.warn("检查更新状态失败: {}", e.getMessage());
            return true; // 出错时默认需要更新
        }
    }
    
    @Override
    public Object getDataStatistics(IndexType indexType) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 统计总数据量
            long totalCount = coalIndexDataDao.countByTypeAndDateRange(indexType, null, null);
            statistics.put("totalCount", totalCount);
            
            // 最新数据
            List<CoalIndexData> latestData = coalIndexDataDao.findLatestByType(indexType);
            if (!CollectionUtils.isEmpty(latestData)) {
                statistics.put("latestData", convertToDto(latestData));
                statistics.put("lastUpdateTime", latestData.get(0).getDataDate());
            }
            
            // 爬虫执行统计
            List<CoalCrawlLog> recentLogs = coalCrawlLogDao.findRecentByType(indexType, 10);
            long successCount = recentLogs.stream().mapToLong(log -> log.getCrawlStatus() == 1 ? 1 : 0).sum();
            statistics.put("recentSuccessRate", recentLogs.isEmpty() ? 0 : (double) successCount / recentLogs.size());
            
        } catch (Exception e) {
            logger.error("获取数据统计失败: {}", e.getMessage(), e);
            statistics.put("error", e.getMessage());
        }
        
        return statistics;
    }
    
    /**
     * 处理和保存数据
     */
    private List<CoalIndexData> processAndSaveData(List<CoalIndexDataDto> dtoList, IndexType indexType) {
        List<CoalIndexData> savedData = new ArrayList<>();
        
        for (CoalIndexDataDto dto : dtoList) {
            try {
                // 检查是否已存在相同数据
                CoalIndexData existingData = coalIndexDataDao.findByTypeAndDateAndCaloric(
                        dto.getIndexType(), dto.getDataDate(), dto.getCalorificValue());
                
                CoalIndexData data;
                if (existingData != null) {
                    // 更新现有数据
                    data = existingData;
                    BeanUtils.copyProperties(dto, data, "objectId", "createTime");
                } else {
                    // 创建新数据
                    data = new CoalIndexData();
                    BeanUtils.copyProperties(dto, data);
                }
                
                // 计算涨跌幅
                calculateChangeRate(data);
                
                coalIndexDataDao.save(data);
                savedData.add(data);
                
            } catch (Exception e) {
                logger.warn("保存数据失败: {}", e.getMessage(), e);
            }
        }
        
        return savedData;
    }
    
    /**
     * 计算涨跌幅
     */
    private void calculateChangeRate(CoalIndexData currentData) {
        try {
            CoalIndexData previousData = coalIndexDataDao.findLatestByTypeAndCaloric(
                    currentData.getIndexType(), currentData.getCalorificValue());
            
            if (previousData != null && previousData.getPrice() != null && currentData.getPrice() != null) {
                BigDecimal changeAmount = currentData.getPrice().subtract(previousData.getPrice());
                BigDecimal changeRate = changeAmount.divide(previousData.getPrice(), 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"));
                
                currentData.setChangeAmount(changeAmount);
                currentData.setChangeRate(changeRate);
                currentData.setTrendDirection(changeAmount.compareTo(BigDecimal.ZERO) > 0 ? "上涨" : 
                                            changeAmount.compareTo(BigDecimal.ZERO) < 0 ? "下跌" : "持平");
            }
        } catch (Exception e) {
            logger.warn("计算涨跌幅失败: {}", e.getMessage());
        }
    }
    
    /**
     * 创建爬虫日志
     */
    private CoalCrawlLog createCrawlLog(IndexType indexType, Date startTime) {
        CoalCrawlLog log = new CoalCrawlLog();
        log.setIndexType(indexType);
        log.setStartTime(startTime);
        log.setCrawlStatus(0); // 初始状态为进行中
        log.setSourceUrl("https://www.meiyibao.com/");
        return log;
    }
    
    /**
     * 转换为DTO
     */
    private List<CoalIndexDataDto> convertToDto(List<CoalIndexData> dataList) {
        return dataList.stream().map(data -> {
            CoalIndexDataDto dto = new CoalIndexDataDto();
            BeanUtils.copyProperties(data, dto);
            return dto;
        }).collect(Collectors.toList());
    }
    
    /**
     * 转换日志为响应对象
     */
    private CoalIndexCrawlResponse convertLogToResponse(CoalCrawlLog log) {
        CoalIndexCrawlResponse response = new CoalIndexCrawlResponse();
        response.setLogId(log.getObjectId().toString());
        response.setStartTime(log.getStartTime());
        response.setEndTime(log.getEndTime());
        response.setSuccess(log.getCrawlStatus() == 1);
        response.setErrorMessage(log.getErrorMessage());
        response.setDataCount(log.getDataCount());
        
        if (log.getStartTime() != null && log.getEndTime() != null) {
            response.setDuration(log.getEndTime().getTime() - log.getStartTime().getTime());
        }
        
        return response;
    }
}
