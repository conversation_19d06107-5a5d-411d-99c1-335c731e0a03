package com.erdos.coal.crawler.constants;

/**
 * 煤炭指数相关常量
 */
public class CoalIndexConstants {
    
    /**
     * 热值常量（大卡）
     */
    public static final Integer CALORIC_4500 = 4500;
    public static final Integer CALORIC_5000 = 5000;
    public static final Integer CALORIC_5500 = 5500;
    public static final Integer CALORIC_5800 = 5800;
    
    /**
     * 价格单位
     */
    public static final String PRICE_UNIT = "元/吨";
    
    /**
     * 百分比符号
     */
    public static final String PERCENT_SYMBOL = "%";
    
    /**
     * 涨跌状态
     */
    public static final String TREND_UP = "UP";      // 上涨
    public static final String TREND_DOWN = "DOWN";  // 下跌
    public static final String TREND_FLAT = "FLAT";  // 持平
    
    /**
     * 会员类型
     */
    public static final String MEMBER_TYPE_VIP = "VIP";        // 会员
    public static final String MEMBER_TYPE_NORMAL = "NORMAL";  // 非会员
    
    /**
     * 数据状态
     */
    public static final Integer STATUS_ACTIVE = 1;    // 有效
    public static final Integer STATUS_INACTIVE = 0;  // 无效
    
    /**
     * 默认显示的热值数组
     */
    public static final Integer[] DEFAULT_CALORIC_VALUES = {
        CALORIC_4500, CALORIC_5000, CALORIC_5500, CALORIC_5800
    };
    
    private CoalIndexConstants() {
        // 工具类，禁止实例化
    }
}
