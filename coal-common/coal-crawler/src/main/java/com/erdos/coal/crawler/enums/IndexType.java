package com.erdos.coal.crawler.enums;

/**
 * 煤炭指数类型枚举
 * 用于区分不同的煤炭指数来源
 */
public enum IndexType {
    
    /**
     * 神华外购指数
     */
    SHENHUA("SHENHUA", "神华外购指数"),
    
    /**
     * CCTD指数
     */
    CCTD("CCTD", "CCTD指数"),
    
    /**
     * CCI指数
     */
    CCI("CCI", "CCI指数");
    
    private final String code;
    private final String name;
    
    IndexType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static IndexType fromCode(String code) {
        for (IndexType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown index type code: " + code);
    }
}
