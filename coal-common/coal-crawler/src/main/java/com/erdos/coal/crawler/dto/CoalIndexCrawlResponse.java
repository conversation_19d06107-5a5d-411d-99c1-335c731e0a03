package com.erdos.coal.crawler.dto;

import java.util.Date;
import java.util.List;

/**
 * 煤炭指数爬取响应DTO
 */
public class CoalIndexCrawlResponse {
    
    /**
     * 爬取是否成功
     */
    private Boolean success;
    
    /**
     * 爬取的数据条数
     */
    private Integer dataCount;
    
    /**
     * 爬取开始时间
     */
    private Date startTime;
    
    /**
     * 爬取结束时间
     */
    private Date endTime;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 爬取的数据详情
     */
    private List<CoalIndexDataDto> dataList;
    
    /**
     * 日志ID
     */
    private String logId;
    
    public CoalIndexCrawlResponse() {}
    
    public CoalIndexCrawlResponse(Boolean success, Integer dataCount) {
        this.success = success;
        this.dataCount = dataCount;
    }
    
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public Integer getDataCount() {
        return dataCount;
    }
    
    public void setDataCount(Integer dataCount) {
        this.dataCount = dataCount;
    }
    
    public Date getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public Long getDuration() {
        return duration;
    }
    
    public void setDuration(Long duration) {
        this.duration = duration;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public List<CoalIndexDataDto> getDataList() {
        return dataList;
    }
    
    public void setDataList(List<CoalIndexDataDto> dataList) {
        this.dataList = dataList;
    }
    
    public String getLogId() {
        return logId;
    }
    
    public void setLogId(String logId) {
        this.logId = logId;
    }
}
