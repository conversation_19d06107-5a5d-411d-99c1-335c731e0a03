package com.erdos.coal.crawler.controller;

import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.crawler.dto.CoalIndexCrawlRequest;
import com.erdos.coal.crawler.dto.CoalIndexCrawlResponse;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.CoalIndexCrawlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 煤炭指数爬虫控制器
 */
@RestController
@RequestMapping("/api/coal/crawler")
public class CoalIndexCrawlerController {
    
    private static final Logger logger = LoggerFactory.getLogger(CoalIndexCrawlerController.class);
    
    @Autowired
    private CoalIndexCrawlerService coalIndexCrawlerService;
    
    /**
     * 爬取煤炭指数数据
     */
    @PostMapping("/crawl")
    public ServerResponse<CoalIndexCrawlResponse> crawlCoalIndexData(@RequestBody CoalIndexCrawlRequest request) {
        try {
            logger.info("开始爬取煤炭指数数据: {}", request.getIndexType());
            
            if (request.getIndexType() == null) {
                return ServerResponse.createError("指数类型不能为空");
            }
            
            CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);
            
            if (response.getSuccess()) {
                return ServerResponse.createSuccess("爬取成功", response);
            } else {
                return ServerResponse.createError("爬取失败: " + response.getErrorMessage(), response);
            }
            
        } catch (Exception e) {
            logger.error("爬取煤炭指数数据异常: {}", e.getMessage(), e);
            return ServerResponse.createError("爬取异常: " + e.getMessage());
        }
    }
    
    /**
     * 爬取指定类型的最新数据
     */
    @PostMapping("/crawl/latest/{indexType}")
    public ServerResponse<CoalIndexCrawlResponse> crawlLatestData(@PathVariable IndexType indexType) {
        try {
            logger.info("爬取最新数据: {}", indexType);
            
            CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlLatestData(indexType);
            
            if (response.getSuccess()) {
                return ServerResponse.createSuccess("爬取成功", response);
            } else {
                return ServerResponse.createError("爬取失败: " + response.getErrorMessage(), response);
            }
            
        } catch (Exception e) {
            logger.error("爬取最新数据异常: {}", e.getMessage(), e);
            return ServerResponse.createError("爬取异常: " + e.getMessage());
        }
    }
    
    /**
     * 爬取所有类型的最新数据
     */
    @PostMapping("/crawl/all")
    public ServerResponse<CoalIndexCrawlResponse> crawlAllLatestData() {
        try {
            logger.info("爬取所有类型的最新数据");
            
            CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlAllLatestData();
            
            if (response.getSuccess()) {
                return ServerResponse.createSuccess("爬取成功", response);
            } else {
                return ServerResponse.createError("部分爬取失败: " + response.getErrorMessage(), response);
            }
            
        } catch (Exception e) {
            logger.error("爬取所有数据异常: {}", e.getMessage(), e);
            return ServerResponse.createError("爬取异常: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发数据爬取
     */
    @PostMapping("/manual/{indexType}")
    public ServerResponse<CoalIndexCrawlResponse> manualCrawl(
            @PathVariable IndexType indexType,
            @RequestParam(required = false, defaultValue = "30000") Long timeout) {
        try {
            logger.info("手动触发爬取: {}, 超时: {}ms", indexType, timeout);
            
            CoalIndexCrawlResponse response = coalIndexCrawlerService.manualCrawl(indexType, timeout);
            
            if (response.getSuccess()) {
                return ServerResponse.createSuccess("手动爬取成功", response);
            } else {
                return ServerResponse.createError("手动爬取失败: " + response.getErrorMessage(), response);
            }
            
        } catch (Exception e) {
            logger.error("手动爬取异常: {}", e.getMessage(), e);
            return ServerResponse.createError("手动爬取异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询煤炭指数数据
     */
    @GetMapping("/data/{indexType}")
    public ServerResponse<List<CoalIndexDataDto>> queryCoalIndexData(
            @PathVariable IndexType indexType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            logger.info("查询煤炭指数数据: {}, 时间范围: {} - {}", indexType, startDate, endDate);
            
            List<CoalIndexDataDto> dataList = coalIndexCrawlerService.queryCoalIndexData(indexType, startDate, endDate);
            
            return ServerResponse.createSuccess("查询成功", dataList);
            
        } catch (Exception e) {
            logger.error("查询数据异常: {}", e.getMessage(), e);
            return ServerResponse.createError("查询异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询最新的煤炭指数数据
     */
    @GetMapping("/data/latest/{indexType}")
    public ServerResponse<List<CoalIndexDataDto>> queryLatestData(@PathVariable IndexType indexType) {
        try {
            logger.info("查询最新数据: {}", indexType);
            
            List<CoalIndexDataDto> dataList = coalIndexCrawlerService.queryLatestData(indexType);
            
            return ServerResponse.createSuccess("查询成功", dataList);
            
        } catch (Exception e) {
            logger.error("查询最新数据异常: {}", e.getMessage(), e);
            return ServerResponse.createError("查询异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取爬虫执行历史
     */
    @GetMapping("/history/{indexType}")
    public ServerResponse<List<CoalIndexCrawlResponse>> getCrawlHistory(
            @PathVariable IndexType indexType,
            @RequestParam(required = false, defaultValue = "10") int limit) {
        try {
            logger.info("获取爬虫历史: {}, 限制: {}", indexType, limit);
            
            List<CoalIndexCrawlResponse> historyList = coalIndexCrawlerService.getCrawlHistory(indexType, limit);
            
            return ServerResponse.createSuccess("查询成功", historyList);
            
        } catch (Exception e) {
            logger.error("获取爬虫历史异常: {}", e.getMessage(), e);
            return ServerResponse.createError("查询异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查数据是否需要更新
     */
    @GetMapping("/check/update/{indexType}")
    public ServerResponse<Boolean> checkNeedUpdate(@PathVariable IndexType indexType) {
        try {
            logger.info("检查更新状态: {}", indexType);
            
            boolean needUpdate = coalIndexCrawlerService.needUpdate(indexType);
            
            return ServerResponse.createSuccess("检查完成", needUpdate);
            
        } catch (Exception e) {
            logger.error("检查更新状态异常: {}", e.getMessage(), e);
            return ServerResponse.createError("检查异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取数据统计信息
     */
    @GetMapping("/statistics/{indexType}")
    public ServerResponse<Object> getDataStatistics(@PathVariable IndexType indexType) {
        try {
            logger.info("获取数据统计: {}", indexType);
            
            Object statistics = coalIndexCrawlerService.getDataStatistics(indexType);
            
            return ServerResponse.createSuccess("获取成功", statistics);
            
        } catch (Exception e) {
            logger.error("获取数据统计异常: {}", e.getMessage(), e);
            return ServerResponse.createError("获取异常: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ServerResponse<String> healthCheck() {
        try {
            // 简单的健康检查，可以扩展检查数据库连接等
            return ServerResponse.createSuccess("爬虫服务运行正常", "OK");
        } catch (Exception e) {
            logger.error("健康检查异常: {}", e.getMessage(), e);
            return ServerResponse.createError("服务异常: " + e.getMessage());
        }
    }
}
