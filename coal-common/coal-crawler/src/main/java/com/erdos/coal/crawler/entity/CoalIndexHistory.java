package com.erdos.coal.crawler.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.crawler.enums.IndexType;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.IndexOptions;
import dev.morphia.annotations.Indexes;
import dev.morphia.annotations.Property;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 煤炭指数历史数据实体
 * 专门用于存储折线图展示的历史数据
 */
@Entity(value = "coal_index_history", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("indexType"), @Field("calorificValue"), @Field("dataDate")},
               options = @IndexOptions(name = "idx_history_type_caloric_date", background = true)),
        @Index(fields = {@Field("dataDate")}, 
               options = @IndexOptions(name = "idx_history_date", background = true))
})
public class CoalIndexHistory extends BaseMongoInfo {
    
    /**
     * 指数类型：SHENHUA/CCTD/CCI
     */
    @Property("indexType")
    private IndexType indexType;
    
    /**
     * 热值（大卡）
     */
    @Property("calorificValue")
    private Integer calorificValue;
    
    /**
     * 数据日期
     */
    @Property("dataDate")
    private Date dataDate;
    
    /**
     * 价格（元/吨）
     */
    @Property("price")
    private BigDecimal price;
    
    /**
     * 数据点在图表中的X轴位置（可选，用于前端图表渲染）
     */
    @Property("chartX")
    private Integer chartX;
    
    /**
     * 数据点在图表中的Y轴位置（可选，用于前端图表渲染）
     */
    @Property("chartY")
    private Integer chartY;
    
    /**
     * 数据状态：1-有效，0-无效
     */
    @Property("status")
    private Integer status = 1;
    
    /**z
     * 排序字段（用于控制图表中数据点的顺序）
     */
    @Property("sortOrder")
    private Integer sortOrder;
    
    // 构造函数
    public CoalIndexHistory() {}
    
    public CoalIndexHistory(IndexType indexType, Integer calorificValue, Date dataDate, BigDecimal price) {
        this.indexType = indexType;
        this.calorificValue = calorificValue;
        this.dataDate = dataDate;
        this.price = price;
    }
    
    // Getter和Setter方法
    public IndexType getIndexType() {
        return indexType;
    }
    
    public void setIndexType(IndexType indexType) {
        this.indexType = indexType;
    }
    
    public Integer getCalorificValue() {
        return calorificValue;
    }
    
    public void setCalorificValue(Integer calorificValue) {
        this.calorificValue = calorificValue;
    }
    
    public Date getDataDate() {
        return dataDate;
    }
    
    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Integer getChartX() {
        return chartX;
    }
    
    public void setChartX(Integer chartX) {
        this.chartX = chartX;
    }
    
    public Integer getChartY() {
        return chartY;
    }
    
    public void setChartY(Integer chartY) {
        this.chartY = chartY;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
}
