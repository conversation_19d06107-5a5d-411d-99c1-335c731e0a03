// package com.erdos.coal.crawler.util;
//
// import com.erdos.coal.crawler.constants.JavaScriptConstants;
// import com.erdos.coal.crawler.dto.CoalIndexDataDto;
// import com.erdos.coal.crawler.enums.IndexType;
// import com.microsoft.playwright.*;
// import com.microsoft.playwright.options.WaitUntilState;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
//
// import java.math.BigDecimal;
// import java.text.SimpleDateFormat;
// import java.util.*;
// import java.util.regex.Matcher;
// import java.util.regex.Pattern;
//
// /**
//  * 专门提取煤炭图表数据的工具类
//  * 专注于提取具体的日期-价格对应关系
//  *
//  * <AUTHOR>
//  * @date 2025-07-31
//  */
// public class CoalChartDataExtractor {
//
//     private static final Logger logger = LoggerFactory.getLogger(CoalChartDataExtractor.class);
//     private static final String BASE_URL = "https://www.meiyibao.com/";
//     private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");
//
//     /**
//      * 提取图表数据的主方法
//      */
//     public static Map<String, Map<String, String>> extractCoalChartData(IndexType indexType) {
//         Map<String, Map<String, String>> result = new LinkedHashMap<>();
//
//         try (Playwright playwright = Playwright.create()) {
//             Browser browser = playwright.chromium().launch(
//                     new BrowserType.LaunchOptions()
//                             .setHeadless(false) // 可视化模式便于调试
//                             .setTimeout(60000)
//             );
//
//             BrowserContext context = browser.newContext(
//                     new Browser.NewContextOptions()
//                             .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
//                             .setViewportSize(1920, 1080)
//             );
//
//             Page page = context.newPage();
//             page.setDefaultTimeout(60000);
//
//             logger.info("开始提取{}的图表数据", getIndexTypeName(indexType));
//
//             try {
//                 // 访问页面
//                 page.navigate(BASE_URL, new Page.NavigateOptions()
//                         .setWaitUntil(WaitUntilState.DOMCONTENTLOADED)
//                         .setTimeout(60000));
//
//                 logger.info("页面加载成功，等待内容渲染...");
//                 page.waitForTimeout(3000);
//
//                 // 点击对应标签页
//                 clickIndexTab(page, indexType);
//                 page.waitForTimeout(3000);
//
//                 // 提取图表数据
//                 result = extractChartDataFromDOM(page);
//
//                 // 如果DOM提取失败，尝试JavaScript方法
//                 if (result.isEmpty()) {
//                     result = extractChartDataFromJS(page);
//                 }
//
//                 // 如果还是没有数据，尝试网络拦截
//                 if (result.isEmpty()) {
//                     result = extractChartDataFromNetwork(page);
//                 }
//
//             } catch (Exception e) {
//                 logger.error("数据提取过程出错: {}", e.getMessage());
//             }
//
//             page.close();
//             context.close();
//             browser.close();
//
//         } catch (Exception e) {
//             logger.error("浏览器操作失败: {}", e.getMessage(), e);
//         }
//
//         return result;
//     }
//
//     /**
//      * 从DOM中提取图表数据
//      */
//     private static Map<String, Map<String, String>> extractChartDataFromDOM(Page page) {
//         Map<String, Map<String, String>> result = new LinkedHashMap<>();
//
//         try {
//             logger.info("尝试从DOM提取图表数据...");
//
//             // 获取页面HTML内容
//             String pageContent = page.content();
//
//             // 查找包含日期和价格的模式
//             // 模式1: 查找类似 "07-22" 和对应的价格数据
//             Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
//             Pattern pricePattern = Pattern.compile("(\\d{3,})");
//
//             Matcher dateMatcher = datePattern.matcher(pageContent);
//             Set<String> dates = new HashSet<>();
//
//             while (dateMatcher.find()) {
//                 String date = dateMatcher.group(1);
//                 // 过滤合理的日期范围（7月份）
//                 if (date.startsWith("07-")) {
//                     dates.add(date);
//                 }
//             }
//
//             logger.info("找到日期: {}", dates);
//
//             // 尝试查找特定的价格元素
//             String[] selectors = {
//                 ".coal-ing",
//                 "[class*='price']",
//                 "[class*='value']",
//                 "[data-price]",
//                 ".chart-data",
//                 ".price-item"
//             };
//
//             for (String selector : selectors) {
//                 try {
//                     Locator elements = page.locator(selector);
//                     int count = elements.count();
//
//                     if (count > 0) {
//                         logger.info("选择器 {} 找到 {} 个元素", selector, count);
//
//                         for (int i = 0; i < count; i++) {
//                             try {
//                                 String text = elements.nth(i).textContent();
//                                 if (text != null && text.trim().length() > 0) {
//                                     // 解析文本中的价格信息
//                                     parseTextForPriceData(text, result);
//                                 }
//                             } catch (Exception e) {
//                                 logger.debug("解析元素失败: {}", e.getMessage());
//                             }
//                         }
//                     }
//                 } catch (Exception e) {
//                     logger.debug("选择器 {} 失败: {}", selector, e.getMessage());
//                 }
//             }
//
//         } catch (Exception e) {
//             logger.error("DOM数据提取失败: {}", e.getMessage());
//         }
//
//         return result;
//     }
//
//     /**
//      * 从JavaScript中提取图表数据
//      */
//     private static Map<String, Map<String, String>> extractChartDataFromJS(Page page) {
//         Map<String, Map<String, String>> result = new LinkedHashMap<>();
//
//         try {
//             logger.info("尝试从JavaScript提取图表数据...");
//
//             // 使用增强版JavaScript获取数据
//             Object chartData = page.evaluate(JavaScriptConstants.GET_ALL_CHART_DATA);
//
//             if (chartData != null) {
//                 String dataStr = chartData.toString();
//                 logger.info("JavaScript返回数据长度: {}", dataStr.length());
//
//                 if (!dataStr.equals("null") && dataStr.length() > 0) {
//                     // 解析JavaScript返回的数据
//                     parseJSDataForChart(dataStr, result);
//                 }
//             }
//
//         } catch (Exception e) {
//             logger.error("JavaScript数据提取失败: {}", e.getMessage());
//         }
//
//         return result;
//     }
//
//     /**
//      * 从网络请求中提取图表数据
//      */
//     private static Map<String, Map<String, String>> extractChartDataFromNetwork(Page page) {
//         Map<String, Map<String, String>> result = new LinkedHashMap<>();
//
//         try {
//             logger.info("尝试从网络请求提取图表数据...");
//
//             // 设置网络监听器
//             List<String> networkData = new ArrayList<>();
//
//             page.onResponse(response -> {
//                 try {
//                     String url = response.url();
//                     if (isChartDataURL(url)) {
//                         String body = response.text();
//                         if (body != null && containsChartData(body)) {
//                             networkData.add(body);
//                             logger.info("捕获到图表数据: {}", url);
//                         }
//                     }
//                 } catch (Exception e) {
//                     logger.debug("网络数据处理失败: {}", e.getMessage());
//                 }
//             });
//
//             // 刷新页面触发网络请求
//             page.reload();
//             page.waitForTimeout(5000);
//
//             // 解析网络数据
//             for (String data : networkData) {
//                 parseNetworkDataForChart(data, result);
//             }
//
//         } catch (Exception e) {
//             logger.error("网络数据提取失败: {}", e.getMessage());
//         }
//
//         return result;
//     }
//
//     /**
//      * 解析文本中的价格数据
//      */
//     private static void parseTextForPriceData(String text, Map<String, Map<String, String>> result) {
//         try {
//             // 查找日期模式 07-22, 07-23 等
//             Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
//             Matcher dateMatcher = datePattern.matcher(text);
//
//             // 查找价格和热值模式
//             Pattern priceCalPattern = Pattern.compile("(\\d{3,}).*?(\\d{4,5})kCal");
//             Pattern simplePattern = Pattern.compile("(\\d{3,})元");
//
//             if (dateMatcher.find()) {
//                 String date = dateMatcher.group(1);
//
//                 if (date.startsWith("07-")) { // 只处理7月份的数据
//                     Map<String, String> dayData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
//
//                     // 尝试匹配价格和热值
//                     Matcher priceMatcher = priceCalPattern.matcher(text);
//                     while (priceMatcher.find()) {
//                         String price = priceMatcher.group(1);
//                         String calorific = priceMatcher.group(2);
//                         dayData.put(calorific + "kCal", price + "元");
//                     }
//
//                     // 如果没有匹配到热值，尝试简单价格匹配
//                     if (dayData.isEmpty()) {
//                         Matcher simpleMatcher = simplePattern.matcher(text);
//                         if (simpleMatcher.find()) {
//                             String price = simpleMatcher.group(1);
//                             dayData.put("price", price + "元");
//                         }
//                     }
//                 }
//             }
//
//         } catch (Exception e) {
//             logger.debug("解析文本失败: {}", text, e);
//         }
//     }
//
//     /**
//      * 解析JavaScript数据
//      */
//     private static void parseJSDataForChart(String dataStr, Map<String, Map<String, String>> result) {
//         try {
//             // 查找时间序列数据模式
//             Pattern timeSeriesPattern = Pattern.compile("\"(\\d{2}-\\d{2})\"[^\\d]*(\\d{3,})");
//             Matcher matcher = timeSeriesPattern.matcher(dataStr);
//
//             while (matcher.find()) {
//                 String date = matcher.group(1);
//                 String price = matcher.group(2);
//
//                 if (date.startsWith("07-")) {
//                     Map<String, String> dayData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
//                     dayData.put("price", price + "元");
//                 }
//             }
//
//         } catch (Exception e) {
//             logger.error("解析JavaScript数据失败: {}", e.getMessage());
//         }
//     }
//
//     /**
//      * 解析网络数据
//      */
//     private static void parseNetworkDataForChart(String data, Map<String, Map<String, String>> result) {
//         try {
//             // 类似JavaScript数据解析
//             parseJSDataForChart(data, result);
//
//         } catch (Exception e) {
//             logger.error("解析网络数据失败: {}", e.getMessage());
//         }
//     }
//
//     /**
//      * 点击指数标签页
//      */
//     private static void clickIndexTab(Page page, IndexType indexType) {
//         try {
//             String tabSelector;
//             switch (indexType) {
//                 case SHENHUA:
//                     tabSelector = "#tab-1";
//                     break;
//                 case CCI:
//                     tabSelector = "#tab-2";
//                     break;
//                 case CCTD:
//                     tabSelector = "#tab-3";
//                     break;
//                 default:
//                     tabSelector = "#tab-3";
//             }
//
//             logger.info("点击标签页: {}", tabSelector);
//             page.querySelector(tabSelector).click();
//
//         } catch (Exception e) {
//             logger.error("点击标签页失败: {}", e.getMessage());
//         }
//     }
//
//     /**
//      * 判断是否是图表数据URL
//      */
//     private static boolean isChartDataURL(String url) {
//         if (url == null) return false;
//         String lower = url.toLowerCase();
//         return lower.contains("chart") || lower.contains("data") ||
//                lower.contains("api") || lower.contains("ajax");
//     }
//
//     /**
//      * 判断是否包含图表数据
//      */
//     private static boolean containsChartData(String content) {
//         if (content == null) return false;
//         return content.contains("07-") && content.matches(".*\\d{3,}.*");
//     }
//
//     /**
//      * 获取指数类型名称
//      */
//     private static String getIndexTypeName(IndexType type) {
//         switch (type) {
//             case CCTD: return "CCTD指数";
//             case CCI: return "CCI指数";
//             case SHENHUA: return "神华外购";
//             default: return type.toString();
//         }
//     }
//
//     /**
//      * 格式化输出结果
//      */
//     public static void printChartData(Map<String, Map<String, String>> data, IndexType indexType) {
//         System.out.println("\n=== " + getIndexTypeName(indexType) + " 图表数据 ===");
//
//         if (data.isEmpty()) {
//             System.out.println("未提取到数据");
//             return;
//         }
//
//         for (Map.Entry<String, Map<String, String>> entry : data.entrySet()) {
//             String date = entry.getKey();
//             Map<String, String> prices = entry.getValue();
//
//             System.out.print(date + ": ");
//             List<String> priceList = new ArrayList<>();
//
//             for (Map.Entry<String, String> priceEntry : prices.entrySet()) {
//                 priceList.add(priceEntry.getKey() + "=" + priceEntry.getValue());
//             }
//
//             System.out.println(String.join(", ", priceList));
//         }
//     }
//
//     /**
//      * 测试方法
//      */
//     public static void main(String[] args) {
//         System.out.println("开始提取煤炭图表数据...");
//
//         // 提取CCTD指数数据
//         Map<String, Map<String, String>> cctdData = extractCoalChartData(IndexType.CCTD);
//         printChartData(cctdData, IndexType.CCTD);
//
//         // 提取CCI指数数据
//         Map<String, Map<String, String>> cciData = extractCoalChartData(IndexType.CCI);
//         printChartData(cciData, IndexType.CCI);
//
//         // 提取神华外购数据
//         Map<String, Map<String, String>> shenhuaData = extractCoalChartData(IndexType.SHENHUA);
//         printChartData(shenhuaData, IndexType.SHENHUA);
//
//         System.out.println("\n数据提取完成！");
//     }
// }
