package com.erdos.coal.crawler.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 爬虫配置类
 */
@Configuration
@ConfigurationProperties(prefix = "coal.crawler")
public class CrawlerConfig {
    
    /**
     * 默认超时时间（毫秒）
     */
    private Long defaultTimeout = 30000L;
    
    /**
     * 是否启用无头模式
     */
    private Boolean headless = true;
    
    /**
     * 浏览器用户代理
     */
    private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    
    /**
     * 页面加载等待时间（毫秒）
     */
    private Long pageLoadWait = 2000L;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 3;
    
    /**
     * 数据更新间隔（毫秒）
     */
    private Long updateInterval = 3600000L; // 1小时
    
    /**
     * 是否启用定时任务
     */
    private Boolean enableSchedule = false;
    
    /**
     * 定时任务cron表达式
     */
    private String scheduleCron = "0 0 */1 * * ?"; // 每小时执行一次
    
    public Long getDefaultTimeout() {
        return defaultTimeout;
    }
    
    public void setDefaultTimeout(Long defaultTimeout) {
        this.defaultTimeout = defaultTimeout;
    }
    
    public Boolean getHeadless() {
        return headless;
    }
    
    public void setHeadless(Boolean headless) {
        this.headless = headless;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public Long getPageLoadWait() {
        return pageLoadWait;
    }
    
    public void setPageLoadWait(Long pageLoadWait) {
        this.pageLoadWait = pageLoadWait;
    }
    
    public Integer getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
    
    public Long getUpdateInterval() {
        return updateInterval;
    }
    
    public void setUpdateInterval(Long updateInterval) {
        this.updateInterval = updateInterval;
    }
    
    public Boolean getEnableSchedule() {
        return enableSchedule;
    }
    
    public void setEnableSchedule(Boolean enableSchedule) {
        this.enableSchedule = enableSchedule;
    }
    
    public String getScheduleCron() {
        return scheduleCron;
    }
    
    public void setScheduleCron(String scheduleCron) {
        this.scheduleCron = scheduleCron;
    }
}
