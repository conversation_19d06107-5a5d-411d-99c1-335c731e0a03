package com.erdos.coal.crawler.core;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Playwright爬虫核心类
 */
@Component
public class PlaywrightCrawler {
    
    private static final Logger logger = LoggerFactory.getLogger(PlaywrightCrawler.class);
    
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final int DEFAULT_TIMEOUT = 30000;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 爬取煤炭指数数据
     */
    public List<CoalIndexDataDto> crawlCoalIndexData(IndexType indexType, Long timeout) {
        List<CoalIndexDataDto> resultList = new ArrayList<>();

        try (Playwright playwright = Playwright.create()) {
            // 启动Chromium浏览器
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true) // 无头模式（服务器环境必须）
            );

            // 创建浏览器上下文
            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            );

            // 阻止图片加载以提高性能
            context.route("**/*.{png,jpg,jpeg}", Route::abort);

            // 创建新页面
            Page page = context.newPage();

            // 设置页面超时
            page.setDefaultTimeout(timeout != null ? timeout.doubleValue() : DEFAULT_TIMEOUT);

            // 导航到目标URL
            page.navigate(BASE_URL);

            // 关键步骤：等待Vue动态内容加载
            // 等待特定选择器出现（推荐）
            page.waitForSelector(".coal-ing", // Vue渲染后的选择器
                    new Page.WaitForSelectorOptions().setTimeout(30_000)); // 超时30秒

            // 根据指数类型点击对应的标签
            clickIndexTab(page, indexType);

            // 等待Vue异步加载数据完成
            page.waitForTimeout(2000);

            // 爬取左侧文本数据
            List<CoalIndexDataDto> textData = crawlTextData(page, indexType);
            resultList.addAll(textData);

            // 爬取图表数据
            List<CoalIndexDataDto> chartData = crawlChartData(page, indexType);
            resultList.addAll(chartData);

            // 关闭资源
            page.close();
            context.close();
            browser.close();

        } catch (PlaywrightException e) {
            logger.error("Playwright爬取失败: {}", e.getMessage(), e);
            throw new RuntimeException("Playwright爬取失败: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("爬取煤炭指数数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("爬取数据失败: " + e.getMessage(), e);
        }

        return resultList;
    }
    
    /**
     * 点击指数类型标签
     */
    private void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabSelector = getTabSelector(indexType);

            // 使用querySelector点击对应的标签
            page.querySelector(tabSelector).click();
            logger.info("成功点击{}标签", indexType);

            // 等待Vue异步加载数据完成
            page.waitForTimeout(2000);

        } catch (Exception e) {
            logger.error("点击标签失败: {}", e.getMessage(), e);
            throw new RuntimeException("点击标签失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取标签选择器
     */
    private String getTabSelector(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "#tab-1"; // 神华外购
            case CCI:
                return "#tab-2"; // CCI指数
            case CCTD:
                return "#tab-3"; // CCTD指数
            default:
                return "#tab-2"; // 默认CCI指数
        }
    }
    
    /**
     * 爬取左侧文本数据
     */
    private List<CoalIndexDataDto> crawlTextData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 等待coal-ing元素加载完成
            page.waitForSelector(".coal-ing", new Page.WaitForSelectorOptions().setTimeout(10_000));

            // 获取所有coal-ing元素
            Locator coalIngElements = page.locator(".coal-ing");
            int count = coalIngElements.count();

            logger.info("找到{}个coal-ing元素", count);

            for (int i = 0; i < count; i++) {
                try {
                    Locator element = coalIngElements.nth(i);
                    String elementText = element.textContent();
                    logger.debug("coal-ing[{}]文本: {}", i, elementText);

                    CoalIndexDataDto data = parseTextData(elementText, indexType);
                    if (data != null) {
                        dataList.add(data);
                    }
                } catch (Exception e) {
                    logger.warn("解析coal-ing[{}]数据失败: {}", i, e.getMessage());
                }
            }

            // 如果没有找到数据，尝试其他选择器
            if (dataList.isEmpty()) {
                dataList.addAll(crawlTextDataAlternative(page, indexType));
            }

        } catch (Exception e) {
            logger.error("爬取文本数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }
    
    /**
     * 备用文本数据爬取方法
     */
    private List<CoalIndexDataDto> crawlTextDataAlternative(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 尝试其他可能的选择器
            String[] selectors = {
                ".price-card", ".index-card", ".coal-price", ".price-info",
                "[class*='price']", "[class*='coal']", "[class*='index']"
            };

            for (String selector : selectors) {
                try {
                    Locator elements = page.locator(selector);
                    if (elements.count() > 0) {
                        logger.info("使用备用选择器找到元素: {}", selector);

                        for (int i = 0; i < elements.count(); i++) {
                            String elementText = elements.nth(i).textContent();
                            logger.debug("备用元素[{}]文本: {}", i, elementText);

                            CoalIndexDataDto data = parseTextData(elementText, indexType);
                            if (data != null) {
                                dataList.add(data);
                            }
                        }

                        if (!dataList.isEmpty()) {
                            break; // 找到数据就停止尝试其他选择器
                        }
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}失败: {}", selector, e.getMessage());
                }
            }

            // 如果还是没有数据，尝试通过页面内容匹配
            if (dataList.isEmpty()) {
                dataList.addAll(parsePageContentWithRegex(page, indexType));
            }

        } catch (Exception e) {
            logger.error("备用文本数据爬取失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 通过正则表达式解析页面内容
     */
    private List<CoalIndexDataDto> parsePageContentWithRegex(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            String pageContent = page.content();

            // 正则匹配价格和热值信息
            Pattern pattern = Pattern.compile("(\\d+)元.*?(\\d+)kCal");
            Matcher matcher = pattern.matcher(pageContent);

            while (matcher.find()) {
                try {
                    BigDecimal price = new BigDecimal(matcher.group(1));
                    Integer calorificValue = Integer.parseInt(matcher.group(2));

                    CoalIndexDataDto data = new CoalIndexDataDto();
                    data.setIndexType(indexType);
                    data.setPrice(price);
                    data.setCalorificValue(calorificValue);
                    data.setDataDate(new Date());
                    data.setSourceUrl(BASE_URL);

                    dataList.add(data);
                } catch (Exception e) {
                    logger.warn("解析匹配数据失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("正则解析页面内容失败: {}", e.getMessage(), e);
        }

        return dataList;
    }
    
    /**
     * 解析文本数据
     */
    private CoalIndexDataDto parseTextData(String text, IndexType indexType) {
        try {
            // 提取价格
            Pattern pricePattern = Pattern.compile("(\\d+)元");
            Matcher priceMatcher = pricePattern.matcher(text);
            
            // 提取热值
            Pattern calorificPattern = Pattern.compile("(\\d+)kCal");
            Matcher calorificMatcher = calorificPattern.matcher(text);
            
            // 提取涨跌
            Pattern changePattern = Pattern.compile("([+\\-]?\\d+)");
            Matcher changeMatcher = changePattern.matcher(text);
            
            if (priceMatcher.find() && calorificMatcher.find()) {
                CoalIndexDataDto data = new CoalIndexDataDto();
                data.setIndexType(indexType);
                data.setPrice(new BigDecimal(priceMatcher.group(1)));
                data.setCalorificValue(Integer.parseInt(calorificMatcher.group(1)));
                data.setDataDate(new Date());
                data.setSourceUrl(BASE_URL);
                
                // 设置涨跌信息
                if (changeMatcher.find()) {
                    BigDecimal changeAmount = new BigDecimal(changeMatcher.group(1));
                    data.setChangeAmount(changeAmount);
                    data.setTrendDirection(changeAmount.compareTo(BigDecimal.ZERO) > 0 ? "上涨" : 
                                         changeAmount.compareTo(BigDecimal.ZERO) < 0 ? "下跌" : "持平");
                }
                
                return data;
            }
        } catch (Exception e) {
            logger.warn("解析文本数据失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 爬取图表数据（增强版）
     */
    private List<CoalIndexDataDto> crawlChartData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 等待图表加载
            page.waitForSelector("canvas, svg, .chart", new Page.WaitForSelectorOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(5000));

            // 使用增强版方法获取所有图表数据
            Object allChartDataObj = page.evaluate(JavaScriptConstants.GET_ALL_CHART_DATA);

            if (allChartDataObj != null) {
                dataList.addAll(parseEnhancedChartData(allChartDataObj.toString(), indexType));
            }

            // 如果没有获取到数据，尝试传统方法
            if (dataList.isEmpty()) {
                Object chartDataObj = page.evaluate(JavaScriptConstants.GET_ECHARTS_DATA);
                if (chartDataObj != null) {
                    dataList.addAll(parseChartData(chartDataObj.toString(), indexType));
                }
            }

        } catch (Exception e) {
            logger.error("爬取图表数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 解析增强版图表数据
     */
    private List<CoalIndexDataDto> parseEnhancedChartData(String allChartDataJson, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            if (allChartDataJson == null || allChartDataJson.trim().isEmpty() || allChartDataJson.equals("null")) {
                logger.warn("图表数据为空");
                return dataList;
            }

            logger.info("开始解析增强版图表数据，数据长度: {}", allChartDataJson.length());

            // 这里可以使用JSON解析库来解析复杂的数据结构
            // 为了演示，先使用简单的字符串匹配方法

            // 1. 尝试从DOM数据中提取价格信息
            dataList.addAll(extractPriceFromDomData(allChartDataJson, indexType));

            // 2. 尝试从图表数据中提取时间序列数据
            dataList.addAll(extractTimeSeriesFromChartData(allChartDataJson, indexType));

            logger.info("从增强版图表数据中解析出{}条记录", dataList.size());

        } catch (Exception e) {
            logger.error("解析增强版图表数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 从DOM数据中提取价格信息
     */
    private List<CoalIndexDataDto> extractPriceFromDomData(String dataJson, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 使用正则表达式匹配价格和热值信息
            Pattern pricePattern = Pattern.compile("(\\d+)元.*?(\\d+)kCal");
            Matcher matcher = pricePattern.matcher(dataJson);

            while (matcher.find()) {
                try {
                    BigDecimal price = new BigDecimal(matcher.group(1));
                    Integer calorificValue = Integer.parseInt(matcher.group(2));

                    CoalIndexDataDto data = new CoalIndexDataDto();
                    data.setIndexType(indexType);
                    data.setPrice(price);
                    data.setCalorificValue(calorificValue);
                    data.setDataDate(new Date());
                    data.setSourceUrl(BASE_URL);

                    dataList.add(data);
                    logger.debug("从DOM数据提取: 价格={}, 热值={}", price, calorificValue);

                } catch (Exception e) {
                    logger.warn("解析价格数据失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("从DOM数据提取价格信息失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 从图表数据中提取时间序列数据
     */
    private List<CoalIndexDataDto> extractTimeSeriesFromChartData(String dataJson, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找时间序列数据模式
            // 例如: ["07-22", 643], ["07-23", 645] 等
            Pattern timeSeriesPattern = Pattern.compile("\"(\\d{2}-\\d{2})\"[^\\d]*(\\d{3,})");
            Matcher matcher = timeSeriesPattern.matcher(dataJson);

            while (matcher.find()) {
                try {
                    String dateStr = matcher.group(1);
                    BigDecimal value = new BigDecimal(matcher.group(2));

                    // 解析日期（假设是当前年份）
                    Date dataDate = parseDate(dateStr);

                    CoalIndexDataDto data = new CoalIndexDataDto();
                    data.setIndexType(indexType);
                    data.setPrice(value);
                    data.setDataDate(dataDate);
                    data.setSourceUrl(BASE_URL);

                    dataList.add(data);
                    logger.debug("从图表数据提取: 日期={}, 价格={}", dateStr, value);

                } catch (Exception e) {
                    logger.warn("解析时间序列数据失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("从图表数据提取时间序列失败: {}", e.getMessage(), e);
        }

        return dataList;
    }



    /**
     * 解析图表数据
     */
    private List<CoalIndexDataDto> parseChartData(String chartDataJson, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            JsonNode chartData = objectMapper.readTree(chartDataJson);
            
            if (chartData.isArray() && chartData.size() > 0) {
                JsonNode firstChart = chartData.get(0);
                JsonNode series = firstChart.get("series");
                JsonNode xAxis = firstChart.get("xAxis");
                
                if (series != null && series.isArray()) {
                    for (JsonNode seriesItem : series) {
                        JsonNode data = seriesItem.get("data");
                        String name = seriesItem.get("name").asText();
                        
                        if (data != null && data.isArray()) {
                            for (int i = 0; i < data.size(); i++) {
                                try {
                                    CoalIndexDataDto dto = parseSeriesDataPoint(data.get(i), name, indexType, i, xAxis);
                                    if (dto != null) {
                                        dataList.add(dto);
                                    }
                                } catch (Exception e) {
                                    logger.warn("解析数据点失败: {}", e.getMessage());
                                }
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("解析图表JSON数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 解析单个数据点
     */
    private CoalIndexDataDto parseSeriesDataPoint(JsonNode dataPoint, String seriesName, 
                                                 IndexType indexType, int index, JsonNode xAxis) {
        try {
            CoalIndexDataDto data = new CoalIndexDataDto();
            data.setIndexType(indexType);
            data.setSourceUrl(BASE_URL);
            
            // 解析价格
            if (dataPoint.isNumber()) {
                data.setPrice(new BigDecimal(dataPoint.asText()));
            } else if (dataPoint.isArray() && dataPoint.size() >= 2) {
                data.setPrice(new BigDecimal(dataPoint.get(1).asText()));
            }
            
            // 解析热值（从系列名称中提取）
            Pattern calorificPattern = Pattern.compile("(\\d+)kCal");
            Matcher matcher = calorificPattern.matcher(seriesName);
            if (matcher.find()) {
                data.setCalorificValue(Integer.parseInt(matcher.group(1)));
            }
            
            // 解析日期（从x轴数据中获取）
            if (xAxis != null && xAxis.isArray() && xAxis.size() > 0) {
                JsonNode xAxisData = xAxis.get(0).get("data");
                if (xAxisData != null && xAxisData.isArray() && index < xAxisData.size()) {
                    String dateStr = xAxisData.get(index).asText();
                    data.setDataDate(parseDate(dateStr));
                }
            }
            
            if (data.getDataDate() == null) {
                data.setDataDate(new Date());
            }
            
            return data;
            
        } catch (Exception e) {
            logger.warn("解析数据点失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        try {
            // 尝试多种日期格式
            String[] patterns = {
                "MM-dd", "yyyy-MM-dd", "MM/dd", "yyyy/MM/dd", 
                "MM月dd日", "yyyy年MM月dd日"
            };
            
            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    Date date = sdf.parse(dateStr);
                    
                    // 如果只有月日，补充年份
                    if (pattern.equals("MM-dd") || pattern.equals("MM/dd") || pattern.equals("MM月dd日")) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(date);
                        cal.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR));
                        date = cal.getTime();
                    }
                    
                    return date;
                } catch (ParseException ignored) {
                    // 继续尝试下一个格式
                }
            }
        } catch (Exception e) {
            logger.warn("解析日期失败: {}", e.getMessage());
        }
        
        return new Date(); // 默认返回当前日期
    }
}
