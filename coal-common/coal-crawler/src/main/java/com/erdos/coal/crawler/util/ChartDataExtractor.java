package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.WaitUntilState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 专门用于提取图表数据的工具类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class ChartDataExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");
    
    /**
     * 提取指定类型的煤炭指数图表数据
     */
    public static List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        List<CoalIndexDataDto> resultList = new ArrayList<>();
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions().setHeadless(true)
            );
            
            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            );
            
            Page page = context.newPage();
            
            // 设置网络拦截器捕获AJAX请求
            setupNetworkCapture(page, resultList, indexType);
            
            // 设置页面超时
            page.setDefaultTimeout(60000);

            logger.info("正在访问网站: {}", BASE_URL);

            try {
                // 导航到目标页面，使用更宽松的等待条件
                page.navigate(BASE_URL, new Page.NavigateOptions()
                        .setWaitUntil(WaitUntilState.DOMCONTENTLOADED)
                        .setTimeout(60000));

                logger.info("页面导航成功，等待内容加载...");

                // 等待页面基础结构加载
                page.waitForTimeout(3000);

                // 点击对应的标签页
                clickIndexTab(page, indexType);

                // 等待图表数据加载
                page.waitForTimeout(5000);

                logger.info("页面加载完成，开始提取数据...");

            } catch (Exception e) {
                logger.warn("页面加载出现问题，但继续尝试数据提取: {}", e.getMessage());
            }
            
            // 1. 直接分析页面内容
            List<CoalIndexDataDto> directData = extractDataDirectly(page, indexType);
            logger.info("直接提取结果: {} 条", directData.size());
            resultList.addAll(directData);

            // 2. 提取DOM中的数据
            List<CoalIndexDataDto> domData = extractDomData(page, indexType);
            logger.info("DOM数据提取结果: {} 条", domData.size());
            resultList.addAll(domData);

            // 3. 提取JavaScript数据（如果前面没有获取到数据）
            if (resultList.isEmpty()) {
                List<CoalIndexDataDto> jsData = extractJavaScriptData(page, indexType);
                logger.info("JavaScript数据提取结果: {} 条", jsData.size());
                resultList.addAll(jsData);
            }

            // 4. 去重和过滤
            resultList = filterAndDeduplicateData(resultList);
            logger.info("过滤后数据: {} 条", resultList.size());
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("提取图表数据失败: {}", e.getMessage(), e);
        }
        
        return resultList;
    }

    /**
     * 过滤和去重数据
     */
    private static List<CoalIndexDataDto> filterAndDeduplicateData(List<CoalIndexDataDto> dataList) {
        List<CoalIndexDataDto> filteredList = new ArrayList<>();
        Set<String> seenData = new HashSet<>();

        for (CoalIndexDataDto data : dataList) {
            try {
                // 过滤条件
                if (data.getPrice() == null) continue;

                BigDecimal price = data.getPrice();

                // 价格范围过滤（煤炭价格通常在300-800元之间）
                if (price.compareTo(new BigDecimal("300")) < 0 ||
                    price.compareTo(new BigDecimal("800")) > 0) {
                    continue;
                }

                // 创建唯一标识符用于去重
                String uniqueKey = data.getIndexType() + "_" +
                                  (data.getDataDate() != null ? DATE_FORMAT.format(data.getDataDate()) : "unknown") + "_" +
                                  price.toString() + "_" +
                                  (data.getCalorificValue() != null ? data.getCalorificValue().toString() : "");

                if (!seenData.contains(uniqueKey)) {
                    seenData.add(uniqueKey);
                    filteredList.add(data);
                }

            } catch (Exception e) {
                logger.debug("过滤数据时出错: {}", e.getMessage());
            }
        }

        // 按日期排序
        filteredList.sort((a, b) -> {
            if (a.getDataDate() == null && b.getDataDate() == null) return 0;
            if (a.getDataDate() == null) return 1;
            if (b.getDataDate() == null) return -1;
            return a.getDataDate().compareTo(b.getDataDate());
        });

        return filteredList;
    }

    /**
     * 直接从页面提取数据 - 最简单直接的方法
     */
    private static List<CoalIndexDataDto> extractDataDirectly(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            logger.info("开始直接提取页面数据...");

            // 获取页面的所有文本内容
            String pageText = page.textContent("body");
            logger.info("页面文本长度: {}", pageText.length());

            // 查找包含日期和价格的行
            String[] lines = pageText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.length() > 0) {
                    // 查找包含07-XX格式日期的行
                    if (line.contains("07-")) {
                        logger.debug("发现包含日期的行: {}", line);

                        // 尝试从这行提取数据
                        List<CoalIndexDataDto> lineData = parseLineForData(line, indexType);
                        dataList.addAll(lineData);
                    }

                    // 查找包含价格信息的行（XXX元 XXXXkCal格式）
                    if (line.matches(".*\\d{3,}元.*\\d{4,5}kCal.*")) {
                        logger.debug("发现包含价格的行: {}", line);

                        CoalIndexDataDto data = parseTextData(line, indexType);
                        if (data != null) {
                            dataList.add(data);
                        }
                    }
                }
            }

            logger.info("直接提取完成，找到 {} 条记录", dataList.size());

        } catch (Exception e) {
            logger.error("直接提取数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 解析单行数据
     */
    private static List<CoalIndexDataDto> parseLineForData(String line, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找日期模式
            Pattern datePattern = Pattern.compile("(\\d{2}-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(line);

            if (dateMatcher.find()) {
                String dateStr = dateMatcher.group(1);

                // 查找该行中的所有数字（可能是价格）
                Pattern numberPattern = Pattern.compile("\\b(\\d{3,})\\b");
                Matcher numberMatcher = numberPattern.matcher(line);

                while (numberMatcher.find()) {
                    String numberStr = numberMatcher.group(1);
                    int number = Integer.parseInt(numberStr);

                    // 过滤合理的价格范围
                    if (number >= 300 && number <= 800) {
                        CoalIndexDataDto data = new CoalIndexDataDto();
                        data.setIndexType(indexType);
                        data.setPrice(new BigDecimal(number));
                        data.setDataDate(parseDate(dateStr));
                        data.setSourceUrl(BASE_URL);

                        dataList.add(data);
                        logger.debug("从行中提取数据: {} = {}元", dateStr, number);
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析行数据失败: {}", line, e);
        }

        return dataList;
    }

    /**
     * 设置网络请求捕获
     */
    private static void setupNetworkCapture(Page page, List<CoalIndexDataDto> resultList, IndexType indexType) {
        page.onResponse(response -> {
            try {
                String url = response.url();
                String contentType = response.headers().get("content-type");
                
                // 检查是否是包含数据的响应
                if (isDataResponse(url, contentType)) {
                    String body = response.text();
                    if (body != null && containsCoalData(body)) {
                        logger.info("捕获到煤炭数据响应: {}", url);
                        resultList.addAll(parseNetworkData(body, indexType));
                    }
                }
            } catch (Exception e) {
                logger.debug("处理网络响应失败: {}", e.getMessage());
            }
        });
    }
    
    /**
     * 判断是否是数据响应 - 更精确的过滤
     */
    private static boolean isDataResponse(String url, String contentType) {
        if (url == null) return false;

        String lowerUrl = url.toLowerCase();

        // 排除明显不相关的URL
        if (lowerUrl.contains("baidu.com") ||
            lowerUrl.contains("google.com") ||
            lowerUrl.contains("map") ||
            lowerUrl.contains("analytics") ||
            lowerUrl.contains("font") ||
            lowerUrl.contains("css") ||
            lowerUrl.contains(".js") ||
            lowerUrl.contains("image") ||
            lowerUrl.contains("icon") ||
            lowerUrl.contains("static")) {
            return false;
        }

        // 只关注可能包含煤炭数据的URL
        boolean isRelevantUrl = lowerUrl.contains("meiyibao.com") &&
                               (lowerUrl.contains("api") ||
                                lowerUrl.contains("data") ||
                                lowerUrl.contains("chart") ||
                                lowerUrl.contains("ajax") ||
                                lowerUrl.contains("coal") ||
                                lowerUrl.contains("price") ||
                                lowerUrl.contains("index"));

        boolean isJsonContent = contentType != null && contentType.contains("json");

        return isRelevantUrl || (isJsonContent && lowerUrl.contains("meiyibao.com"));
    }
    
    /**
     * 检查内容是否包含煤炭数据
     */
    private static boolean containsCoalData(String content) {
        if (content == null) return false;
        
        String lower = content.toLowerCase();
        return lower.contains("coal") || lower.contains("煤") ||
               lower.contains("price") || lower.contains("价格") ||
               lower.contains("5500") || lower.contains("5000") || lower.contains("4500") ||
               lower.contains("cctd") || lower.contains("cci");
    }
    
    /**
     * 解析网络数据 - 专门提取图表时间序列数据
     */
    private static List<CoalIndexDataDto> parseNetworkData(String data, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            logger.info("开始解析网络数据，数据长度: {}", data.length());

            // 专门查找图表时间序列数据模式
            // 模式1: ["07-22", 643] 格式
            Pattern timeSeriesPattern1 = Pattern.compile("\\[\"(\\d{2}-\\d{2})\",\\s*(\\d{3,})\\]");
            Matcher matcher1 = timeSeriesPattern1.matcher(data);

            while (matcher1.find()) {
                String dateStr = matcher1.group(1);
                String priceStr = matcher1.group(2);

                if (dateStr.startsWith("07-")) { // 只处理7月份数据
                    try {
                        CoalIndexDataDto dto = new CoalIndexDataDto();
                        dto.setIndexType(indexType);
                        dto.setPrice(new BigDecimal(priceStr));
                        dto.setDataDate(parseDate(dateStr));
                        dto.setSourceUrl(BASE_URL);

                        dataList.add(dto);
                        logger.debug("提取到时间序列数据: {} = {}元", dateStr, priceStr);
                    } catch (Exception e) {
                        logger.debug("解析时间序列数据失败: {} = {}", dateStr, priceStr);
                    }
                }
            }

            // 模式2: "07-22": 643 格式
            Pattern timeSeriesPattern2 = Pattern.compile("\"(\\d{2}-\\d{2})\"\\s*:\\s*(\\d{3,})");
            Matcher matcher2 = timeSeriesPattern2.matcher(data);

            while (matcher2.find()) {
                String dateStr = matcher2.group(1);
                String priceStr = matcher2.group(2);

                if (dateStr.startsWith("07-")) {
                    try {
                        CoalIndexDataDto dto = new CoalIndexDataDto();
                        dto.setIndexType(indexType);
                        dto.setPrice(new BigDecimal(priceStr));
                        dto.setDataDate(parseDate(dateStr));
                        dto.setSourceUrl(BASE_URL);

                        dataList.add(dto);
                        logger.debug("提取到键值对数据: {} = {}元", dateStr, priceStr);
                    } catch (Exception e) {
                        logger.debug("解析键值对数据失败: {} = {}", dateStr, priceStr);
                    }
                }
            }

            logger.info("从网络数据中提取到 {} 条有效记录", dataList.size());

        } catch (Exception e) {
            logger.error("解析网络数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }
    
    /**
     * 提取DOM数据 - 专门查找图表相关元素
     */
    private static List<CoalIndexDataDto> extractDomData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            logger.info("开始从DOM提取图表数据...");

            // 首先尝试获取页面HTML内容进行全局搜索
            String pageContent = page.content();

            // 查找图表数据的特征模式
            dataList.addAll(extractChartDataFromHTML(pageContent, indexType));

            // 如果没有找到数据，尝试特定选择器
            if (dataList.isEmpty()) {
                String[] chartSelectors = {
                    "canvas", // 图表画布
                    "svg", // SVG图表
                    ".chart", ".chart-container", ".chart-wrapper",
                    ".echarts", ".echarts-container",
                    "[id*='chart']", "[class*='chart']",
                    ".coal-ing", // 原有选择器
                    ".price-card", ".index-card"
                };

                for (String selector : chartSelectors) {
                    try {
                        Locator elements = page.locator(selector);
                        int count = elements.count();

                        if (count > 0) {
                            logger.info("选择器 {} 找到 {} 个元素", selector, count);

                            for (int i = 0; i < Math.min(count, 10); i++) { // 限制处理数量
                                try {
                                    String text = elements.nth(i).textContent();
                                    String html = elements.nth(i).innerHTML();

                                    // 分析元素内容
                                    dataList.addAll(analyzeElementForChartData(text, html, indexType));

                                } catch (Exception e) {
                                    logger.debug("分析元素失败: {}", e.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.debug("选择器 {} 失败: {}", selector, e.getMessage());
                    }
                }
            }

            logger.info("DOM数据提取完成，共找到 {} 条记录", dataList.size());

        } catch (Exception e) {
            logger.error("提取DOM数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 从HTML内容中提取图表数据
     */
    private static List<CoalIndexDataDto> extractChartDataFromHTML(String html, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 查找图表数据的JavaScript配置
            // 模式1: 查找ECharts配置中的数据
            Pattern echartsDataPattern = Pattern.compile("data\\s*:\\s*\\[(.*?)\\]");
            Matcher matcher = echartsDataPattern.matcher(html);

            while (matcher.find()) {
                String dataStr = matcher.group(1);
                dataList.addAll(parseChartDataString(dataStr, indexType));
            }

            // 模式2: 查找时间序列数组
            Pattern timeSeriesPattern = Pattern.compile("\\[\"(\\d{2}-\\d{2})\",\\s*(\\d{3,})\\]");
            Matcher timeSeriesMatcher = timeSeriesPattern.matcher(html);

            while (timeSeriesMatcher.find()) {
                String dateStr = timeSeriesMatcher.group(1);
                String priceStr = timeSeriesMatcher.group(2);

                if (dateStr.startsWith("07-")) {
                    try {
                        CoalIndexDataDto dto = new CoalIndexDataDto();
                        dto.setIndexType(indexType);
                        dto.setPrice(new BigDecimal(priceStr));
                        dto.setDataDate(parseDate(dateStr));
                        dto.setSourceUrl(BASE_URL);

                        dataList.add(dto);
                    } catch (Exception e) {
                        logger.debug("解析HTML时间序列失败: {} = {}", dateStr, priceStr);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("从HTML提取图表数据失败: {}", e.getMessage());
        }

        return dataList;
    }

    /**
     * 分析元素内容查找图表数据
     */
    private static List<CoalIndexDataDto> analyzeElementForChartData(String text, String html, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            // 检查是否包含图表相关关键词
            if (text != null && (text.contains("07-") || text.contains("图表") || text.contains("数据"))) {

                // 查找日期和价格模式
                Pattern dateValuePattern = Pattern.compile("(\\d{2}-\\d{2}).*?(\\d{3,})");
                Matcher matcher = dateValuePattern.matcher(text);

                while (matcher.find()) {
                    String dateStr = matcher.group(1);
                    String valueStr = matcher.group(2);

                    if (dateStr.startsWith("07-")) {
                        try {
                            CoalIndexDataDto dto = new CoalIndexDataDto();
                            dto.setIndexType(indexType);
                            dto.setPrice(new BigDecimal(valueStr));
                            dto.setDataDate(parseDate(dateStr));
                            dto.setSourceUrl(BASE_URL);

                            dataList.add(dto);
                        } catch (Exception e) {
                            logger.debug("解析元素数据失败: {} = {}", dateStr, valueStr);
                        }
                    }
                }
            }

            // 如果文本分析没有结果，尝试HTML分析
            if (dataList.isEmpty() && html != null) {
                dataList.addAll(extractChartDataFromHTML(html, indexType));
            }

        } catch (Exception e) {
            logger.debug("分析元素内容失败: {}", e.getMessage());
        }

        return dataList;
    }

    /**
     * 提取JavaScript数据 - 专门获取图表配置和数据
     */
    private static List<CoalIndexDataDto> extractJavaScriptData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();

        try {
            logger.info("开始从JavaScript提取图表数据...");

            // 1. 使用增强版JavaScript获取所有数据
            Object allChartData = page.evaluate(JavaScriptConstants.GET_ALL_CHART_DATA);

            if (allChartData != null) {
                String dataStr = allChartData.toString();
                logger.info("JavaScript返回数据长度: {}", dataStr.length());

                if (!dataStr.equals("null") && dataStr.length() > 0) {
                    dataList.addAll(parseChartDataString(dataStr, indexType));
                }
            }

            // 2. 如果没有获取到数据，尝试直接获取ECharts数据
            if (dataList.isEmpty()) {
                Object echartsData = page.evaluate(JavaScriptConstants.GET_ECHARTS_DATA);
                if (echartsData != null) {
                    String echartsStr = echartsData.toString();
                    if (!echartsStr.equals("null") && echartsStr.length() > 0) {
                        dataList.addAll(parseChartDataString(echartsStr, indexType));
                    }
                }
            }

            // 3. 尝试执行简化的JavaScript来查找图表数据
            if (dataList.isEmpty()) {
                try {
                    String simpleJS =
                        "() => {" +
                        "  try {" +
                        "    const result = [];" +
                        "    // 查找页面中的文本内容" +
                        "    const bodyText = document.body.innerText || '';" +
                        "    if (bodyText.includes('07-')) {" +
                        "      result.push({source: 'bodyText', data: bodyText});" +
                        "    }" +
                        "    return result;" +
                        "  } catch (e) {" +
                        "    return [];" +
                        "  }" +
                        "}";

                    Object simpleData = page.evaluate(simpleJS);
                    if (simpleData != null) {
                        String simpleStr = simpleData.toString();
                        if (!simpleStr.equals("null") && simpleStr.length() > 0) {
                            dataList.addAll(parseChartDataString(simpleStr, indexType));
                        }
                    }
                } catch (Exception e) {
                    logger.debug("简化JavaScript执行失败: {}", e.getMessage());
                }
            }

            logger.info("JavaScript数据提取完成，共找到 {} 条记录", dataList.size());

        } catch (Exception e) {
            logger.error("提取JavaScript数据失败: {}", e.getMessage(), e);
        }

        return dataList;
    }
    
    /**
     * 解析文本数据
     */
    private static CoalIndexDataDto parseTextData(String text, IndexType indexType) {
        try {
            // 匹配价格和热值模式：如 "652元 5500kCal"
            Pattern pattern = Pattern.compile("(\\d+)元.*?(\\d+)kCal");
            Matcher matcher = pattern.matcher(text);
            
            if (matcher.find()) {
                BigDecimal price = new BigDecimal(matcher.group(1));
                Integer calorificValue = Integer.parseInt(matcher.group(2));
                
                CoalIndexDataDto data = new CoalIndexDataDto();
                data.setIndexType(indexType);
                data.setPrice(price);
                data.setCalorificValue(calorificValue);
                data.setDataDate(new Date());
                data.setSourceUrl(BASE_URL);
                
                return data;
            }
        } catch (Exception e) {
            logger.debug("解析文本数据失败: {}", text, e);
        }
        
        return null;
    }
    
    /**
     * 解析图表数据字符串
     */
    private static List<CoalIndexDataDto> parseChartDataString(String dataStr, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 提取时间序列数据
            Pattern timeSeriesPattern = Pattern.compile("\"(\\d{2}-\\d{2})\"[^\\d]*(\\d{3,})");
            Matcher matcher = timeSeriesPattern.matcher(dataStr);
            
            while (matcher.find()) {
                try {
                    String dateStr = matcher.group(1);
                    BigDecimal price = new BigDecimal(matcher.group(2));
                    
                    CoalIndexDataDto data = new CoalIndexDataDto();
                    data.setIndexType(indexType);
                    data.setPrice(price);
                    data.setDataDate(parseDate(dateStr));
                    data.setSourceUrl(BASE_URL);
                    
                    dataList.add(data);
                } catch (Exception e) {
                    logger.debug("解析时间序列数据失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("解析图表数据字符串失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 点击指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabSelector;
            switch (indexType) {
                case SHENHUA:
                    tabSelector = "#tab-1";
                    break;
                case CCI:
                    tabSelector = "#tab-2";
                    break;
                case CCTD:
                    tabSelector = "#tab-3";
                    break;
                default:
                    tabSelector = "#tab-3"; // 默认CCTD
            }
            
            page.querySelector(tabSelector).click();
            page.waitForTimeout(2000); // 等待标签页切换
            
        } catch (Exception e) {
            logger.error("点击标签页失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 解析日期
     */
    private static Date parseDate(String dateStr) {
        try {
            String[] parts = dateStr.split("-");
            if (parts.length == 2) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, Integer.parseInt(parts[0]) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(parts[1]));
                return calendar.getTime();
            }
        } catch (Exception e) {
            logger.debug("解析日期失败: {}", dateStr);
        }
        return new Date();
    }
    
    /**
     * 格式化输出数据
     */
    private static void printFormattedData(List<CoalIndexDataDto> dataList, String indexName) {
        System.out.println("\n=== " + indexName + " 数据 ===");
        System.out.println("总条数: " + dataList.size());

        if (dataList.isEmpty()) {
            System.out.println("未提取到有效数据");
            return;
        }

        // 按日期分组
        Map<String, List<CoalIndexDataDto>> groupedByDate = new LinkedHashMap<>();

        for (CoalIndexDataDto data : dataList) {
            String dateKey = data.getDataDate() != null ?
                DATE_FORMAT.format(data.getDataDate()) : "未知日期";

            groupedByDate.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(data);
        }

        // 输出格式化结果
        for (Map.Entry<String, List<CoalIndexDataDto>> entry : groupedByDate.entrySet()) {
            String date = entry.getKey();
            List<CoalIndexDataDto> dayData = entry.getValue();

            System.out.print(date + ": ");

            List<String> priceInfo = new ArrayList<>();
            for (CoalIndexDataDto data : dayData) {
                String info = "";
                if (data.getCalorificValue() != null) {
                    info = data.getCalorificValue() + "kCal=" + data.getPrice() + "元";
                } else {
                    info = data.getPrice() + "元";
                }
                priceInfo.add(info);
            }

            System.out.println(String.join(", ", priceInfo));
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        System.out.println("=== 煤炭图表数据提取工具 ===");
        System.out.println("目标：提取7.22-7.30期间的价格数据");

        // 提取CCTD指数数据
        System.out.println("\n1. 开始提取CCTD指数数据...");
        List<CoalIndexDataDto> cctdData = extractChartData(IndexType.CCTD);
        printFormattedData(cctdData, "CCTD指数");

        // 提取CCI指数数据
        System.out.println("\n2. 开始提取CCI指数数据...");
        List<CoalIndexDataDto> cciData = extractChartData(IndexType.CCI);
        printFormattedData(cciData, "CCI指数");

        // 提取神华外购数据
        System.out.println("\n3. 开始提取神华外购数据...");
        List<CoalIndexDataDto> shenhuaData = extractChartData(IndexType.SHENHUA);
        printFormattedData(shenhuaData, "神华外购");

        System.out.println("\n=== 数据提取完成 ===");
        System.out.println("如果数据为空，请检查：");
        System.out.println("1. 网络连接是否正常");
        System.out.println("2. 网站是否可以正常访问");
        System.out.println("3. 页面结构是否发生变化");
    }
}
