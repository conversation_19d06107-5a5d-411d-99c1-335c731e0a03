package com.erdos.coal.crawler.schedule;

import com.erdos.coal.crawler.config.CrawlerConfig;
import com.erdos.coal.crawler.dto.CoalIndexCrawlResponse;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.CoalIndexCrawlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 煤炭指数爬虫定时任务
 */
@Component
@ConditionalOnProperty(name = "coal.crawler.enable-schedule", havingValue = "true")
public class CoalIndexCrawlerSchedule {
    
    private static final Logger logger = LoggerFactory.getLogger(CoalIndexCrawlerSchedule.class);
    
    @Autowired
    private CoalIndexCrawlerService coalIndexCrawlerService;
    
    @Autowired
    private CrawlerConfig crawlerConfig;
    
    /**
     * 定时爬取所有煤炭指数数据
     * 默认每小时执行一次
     */
    @Scheduled(cron = "${coal.crawler.schedule-cron:0 0 */1 * * ?}")
    public void scheduledCrawlAllData() {
        logger.info("开始执行定时爬取任务");
        
        try {
            CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlAllLatestData();
            
            if (response.getSuccess()) {
                logger.info("定时爬取任务完成，共爬取 {} 条数据，耗时 {} ms", 
                           response.getDataCount(), response.getDuration());
            } else {
                logger.warn("定时爬取任务部分失败: {}", response.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("定时爬取任务异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 定时爬取CCI指数数据
     * 每30分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void scheduledCrawlCCIData() {
        crawlSingleIndexData(IndexType.CCI, "CCI指数");
    }
    
    /**
     * 定时爬取CCTD指数数据
     * 每30分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void scheduledCrawlCCTDData() {
        crawlSingleIndexData(IndexType.CCTD, "CCTD指数");
    }
    
    /**
     * 定时爬取神华外购数据
     * 每30分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void scheduledCrawlShenhuaData() {
        crawlSingleIndexData(IndexType.SHENHUA, "神华外购");
    }
    
    /**
     * 爬取单个指数数据
     */
    private void crawlSingleIndexData(IndexType indexType, String indexName) {
        try {
            // 检查是否需要更新
            if (!coalIndexCrawlerService.needUpdate(indexType)) {
                logger.debug("{}数据较新，跳过本次爬取", indexName);
                return;
            }
            
            logger.info("开始定时爬取{}数据", indexName);
            
            CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlLatestData(indexType);
            
            if (response.getSuccess()) {
                logger.info("{}定时爬取完成，共 {} 条数据，耗时 {} ms", 
                           indexName, response.getDataCount(), response.getDuration());
            } else {
                logger.warn("{}定时爬取失败: {}", indexName, response.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("{}定时爬取异常: {}", indexName, e.getMessage(), e);
        }
    }
    
    /**
     * 健康检查定时任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void healthCheck() {
        try {
            logger.debug("执行爬虫服务健康检查");
            
            // 检查各个指数的数据状态
            for (IndexType indexType : IndexType.values()) {
                try {
                    Object statistics = coalIndexCrawlerService.getDataStatistics(indexType);
                    logger.debug("{}数据状态正常", indexType);
                } catch (Exception e) {
                    logger.warn("{}数据状态检查异常: {}", indexType, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("健康检查异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清理过期日志
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldLogs() {
        try {
            logger.info("开始清理过期爬虫日志");
            
            // 这里可以添加清理逻辑，比如删除30天前的日志
            // 暂时只记录日志
            logger.info("爬虫日志清理完成");
            
        } catch (Exception e) {
            logger.error("清理日志异常: {}", e.getMessage(), e);
        }
    }
}
