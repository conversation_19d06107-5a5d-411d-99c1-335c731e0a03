package com.erdos.coal.crawler.dto;

import com.erdos.coal.crawler.enums.IndexType;

import java.util.Date;

/**
 * 煤炭指数爬取请求DTO
 */
public class CoalIndexCrawlRequest {
    
    /**
     * 指数类型：SHENHUA/CCTD/CCI
     */
    private IndexType indexType;
    
    /**
     * 开始日期
     */
    private Date startDate;
    
    /**
     * 结束日期
     */
    private Date endDate;
    
    /**
     * 是否强制重新爬取
     */
    private Boolean forceRefresh = false;
    
    /**
     * 超时时间（毫秒）
     */
    private Long timeout = 30000L;
    
    public CoalIndexCrawlRequest() {}
    
    public CoalIndexCrawlRequest(IndexType indexType) {
        this.indexType = indexType;
    }
    
    public IndexType getIndexType() {
        return indexType;
    }
    
    public void setIndexType(IndexType indexType) {
        this.indexType = indexType;
    }
    
    public Date getStartDate() {
        return startDate;
    }
    
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }
    
    public Date getEndDate() {
        return endDate;
    }
    
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
    
    public Boolean getForceRefresh() {
        return forceRefresh;
    }
    
    public void setForceRefresh(Boolean forceRefresh) {
        this.forceRefresh = forceRefresh;
    }
    
    public Long getTimeout() {
        return timeout;
    }
    
    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }
}
