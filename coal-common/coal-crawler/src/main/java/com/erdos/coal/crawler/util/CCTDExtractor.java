package com.erdos.coal.crawler.util;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CCTD指数提取工具 - 专门针对CCTD指数的简化版本
 * 支持Canvas折线图交互和数据去重
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public class CCTDExtractor {

    private static final Logger logger = LoggerFactory.getLogger(CCTDExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");

    /**
     * 数据去重辅助类
     */
    private static class DataDeduplicator {
        private final Set<String> processedRawData = new HashSet<>();
        private final Map<String, Map<Integer, Integer>> uniqueData = new LinkedHashMap<>();

        public boolean addData(String date, int calorific, int price, String rawData) {
            // 检查原始数据是否已处理
            if (processedRawData.contains(rawData)) {
                return false;
            }

            // 检查是否为有效数据
            if (price < 300 || price > 800 ||
                (calorific != 5500 && calorific != 5000 && calorific != 4500)) {
                return false;
            }

            Map<Integer, Integer> dateData = uniqueData.computeIfAbsent(date, k -> new LinkedHashMap<>());

            // 检查是否已有相同的数据
            if (dateData.containsKey(calorific) && dateData.get(calorific).equals(price)) {
                return false;
            }

            // 添加新数据
            dateData.put(calorific, price);
            processedRawData.add(rawData);
            logger.debug("添加新数据: {} - {}kCal={}元", date, calorific, price);
            return true;
        }

        public Map<String, Map<Integer, Integer>> getUniqueData() {
            return uniqueData;
        }

        public int getProcessedCount() {
            return processedRawData.size();
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("=== CCTD指数提取工具 ===");
        System.out.println("目标：提取7.22-7.30期间的CCTD指数数据");
        
        // 提取数据
        Map<String, Map<Integer, Integer>> data = extractCCTDData();
        
        // 打印结果
        printResults(data);
    }
    
    /**
     * 提取CCTD指数数据
     */
    public static Map<String, Map<Integer, Integer>> extractCCTDData() {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();

        try (Playwright playwright = Playwright.create()) {
            // 启动浏览器，增加更多配置
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(120000) // 增加到2分钟
                            .setArgs(java.util.Arrays.asList(
                                    "--no-sandbox",
                                    "--disable-dev-shm-usage",
                                    "--disable-blink-features=AutomationControlled",
                                    "--disable-web-security"
                            ))
            );

            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                            .setViewportSize(1920, 1080)
                            .setIgnoreHTTPSErrors(true) // 忽略HTTPS错误
            );

            Page page = context.newPage();
            page.setDefaultTimeout(120000); // 2分钟超时

            System.out.println("正在访问网站: " + BASE_URL);

            try {
                // 先尝试访问页面，使用更宽松的等待条件
                page.navigate(BASE_URL, new Page.NavigateOptions().setTimeout(120000));

                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(5000);

                // 检查页面是否加载成功
                String title = page.title();
                logger.info("页面标题: {}", title);

                // 尝试点击CCTD标签页
                try {
                    logger.info("尝试点击CCTD标签页...");
                    page.querySelector("#tab-3").click();
                    page.waitForTimeout(3000);
                    logger.info("CCTD标签页点击成功");
                } catch (Exception e) {
                    logger.warn("点击CCTD标签页失败，尝试其他方法: {}", e.getMessage());
                    // 尝试其他可能的选择器
                    try {
                        page.click("text=CCTD指数");
                        page.waitForTimeout(3000);
                    } catch (Exception e2) {
                        logger.warn("备用点击方法也失败: {}", e2.getMessage());
                    }
                }

                // 尝试从canvas折线图提取数据
                result = extractDataFromCanvas(page);

                // 如果canvas提取失败，尝试传统方法
                if (result.isEmpty()) {
                    logger.info("Canvas提取失败，尝试传统HTML提取...");
                    String pageContent = page.content();
                    logger.info("获取到页面内容，长度: {}", pageContent.length());
                    result = extractDataFromHTML(pageContent);
                }

                // 如果还是没有数据，尝试直接从页面文本提取
                if (result.isEmpty()) {
                    logger.info("从HTML提取失败，尝试从页面文本提取...");
                    String pageText = page.textContent("body");
                    logger.info("页面文本长度: {}", pageText.length());
                    result = extractDataFromText(pageText);
                }

                // 如果还是没有数据，尝试模拟数据（用于测试）
                if (result.isEmpty()) {
                    logger.warn("所有提取方法都失败，生成模拟数据用于测试");
                    // result = generateMockData();
                }

            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
                // 即使页面访问失败，也生成一些模拟数据用于测试
                logger.info("生成模拟数据用于测试...");
                // result = generateMockData();
            }

            page.close();
            context.close();
            browser.close();

        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 从Canvas折线图提取数据
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromCanvas(Page page) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();

        try {
            logger.info("开始从Canvas折线图提取数据...");

            // 查找coal-char类的元素
            Locator coalCharElement = page.locator(".coal-char");
            if (coalCharElement.count() == 0) {
                logger.warn("未找到class='coal-char'的元素");
                return result;
            }

            logger.info("找到coal-char元素，数量: {}", coalCharElement.count());

            // 查找canvas元素
            Locator canvasElement = coalCharElement.locator("canvas");
            if (canvasElement.count() == 0) {
                logger.warn("在coal-char元素中未找到canvas");
                return result;
            }

            logger.info("找到canvas元素，数量: {}", canvasElement.count());

            // 获取canvas的边界框
            BoundingBox canvasBounds = canvasElement.first().boundingBox();
            if (canvasBounds == null) {
                logger.warn("无法获取canvas的边界框");
                return result;
            }

            logger.info("Canvas边界框: x={}, y={}, width={}, height={}",
                       canvasBounds.x, canvasBounds.y, canvasBounds.width, canvasBounds.height);

            // 等待图表加载完成
            page.waitForTimeout(3000);

            // 在canvas上模拟鼠标移动，尝试触发tooltip
            result = extractDataByMouseInteraction(page, canvasElement.first(), canvasBounds);

        } catch (Exception e) {
            logger.error("从Canvas提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 通过鼠标交互提取数据（使用去重辅助类）
     */
    private static Map<String, Map<Integer, Integer>> extractDataByMouseInteraction(
            Page page, Locator canvas, BoundingBox bounds) {
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("开始鼠标交互提取数据...");

            // 计算折线图区域（通常在canvas的中间部分）
            double startX = bounds.x + bounds.width * 0.1; // 左边距10%
            double endX = bounds.x + bounds.width * 0.9;   // 右边距10%
            double centerY = bounds.y + bounds.height * 0.5; // 中心Y坐标

            // 在X轴上分多个点进行采样
            int samplePoints = 25; // 适中的采样点数
            double stepX = (endX - startX) / samplePoints;

            for (int i = 0; i <= samplePoints; i++) {
                double currentX = startX + i * stepX;

                try {
                    // 移动鼠标到当前位置
                    page.mouse().move(currentX, centerY);
                    page.waitForTimeout(400); // 等待tooltip显示

                    // 点击当前位置
                    page.mouse().click(currentX, centerY);
                    page.waitForTimeout(600); // 等待数据更新

                    // 尝试提取tooltip或弹出的数据
                    String tooltipData = extractTooltipData(page);
                    if (!tooltipData.isEmpty()) {
                        logger.debug("在位置({}, {})提取到数据: {}", currentX, centerY, tooltipData);
                        parseTooltipDataWithDeduplicator(tooltipData, deduplicator);
                    }

                    // 尝试从页面文本中提取当前显示的数据
                    String currentPageText = page.textContent("body");
                    extractCurrentDisplayDataWithDeduplicator(currentPageText, deduplicator);

                } catch (Exception e) {
                    logger.debug("在位置({}, {})提取数据失败: {}", currentX, centerY, e.getMessage());
                }
            }

            logger.info("鼠标交互完成，处理了{}条原始数据，去重后提取到{}天的数据",
                       deduplicator.getProcessedCount(), deduplicator.getUniqueData().size());

        } catch (Exception e) {
            logger.error("鼠标交互提取数据失败: {}", e.getMessage(), e);
        }

        return deduplicator.getUniqueData();
    }

    /**
     * 提取tooltip数据
     */
    private static String extractTooltipData(Page page) {
        try {
            // 尝试多种可能的tooltip选择器
            String[] tooltipSelectors = {
                ".tooltip", ".chart-tooltip", ".echarts-tooltip",
                "[role='tooltip']", ".highcharts-tooltip", ".d3-tip"
            };

            for (String selector : tooltipSelectors) {
                Locator tooltip = page.locator(selector);
                if (tooltip.count() > 0 && tooltip.first().isVisible()) {
                    return tooltip.first().textContent();
                }
            }

            // 如果没有找到tooltip，尝试查找最近更新的文本
            Locator recentText = page.locator("text=/07-\\d{2}/");
            if (recentText.count() > 0) {
                return recentText.first().textContent();
            }

        } catch (Exception e) {
            logger.debug("提取tooltip失败: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 使用去重辅助类的tooltip数据解析方法
     */
    private static void parseTooltipDataWithDeduplicator(String tooltipData, DataDeduplicator deduplicator) {
        try {
            logger.debug("开始解析tooltip数据: {}", tooltipData);

            // 针对格式：07-285500kCal651（+1）5000kCal585（-）4500kCal520（-）
            // 首先提取日期
            Pattern datePattern = Pattern.compile("(07-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(tooltipData);

            if (dateMatcher.find()) {
                String date = dateMatcher.group(1);
                logger.debug("提取到日期: {}", date);

                // 移除日期部分，处理剩余的价格数据
                String priceData = tooltipData.substring(dateMatcher.end());

                // 使用改进的正则表达式匹配：数字kCal数字（涨跌幅）
                Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal(\\d{3,})(?:[（(][^)）]*[)）])?");
                Matcher priceMatcher = pricePattern.matcher(priceData);

                while (priceMatcher.find()) {
                    try {
                        int calorific = Integer.parseInt(priceMatcher.group(1));
                        int price = Integer.parseInt(priceMatcher.group(2));

                        deduplicator.addData(date, calorific, price, tooltipData);

                    } catch (Exception e) {
                        logger.debug("解析单个价格失败: {}", e.getMessage());
                    }
                }

                // 如果没有找到标准格式，尝试其他模式
                if (!priceData.isEmpty()) {
                    // 尝试更宽松的匹配模式
                    Pattern loosePricePattern = Pattern.compile("(\\d{3,})");
                    Matcher loosePriceMatcher = loosePricePattern.matcher(priceData);

                    // 按顺序匹配价格，通常是5500kCal, 5000kCal, 4500kCal的顺序
                    int[] calorificValues = {5500, 5000, 4500};
                    int index = 0;

                    while (loosePriceMatcher.find() && index < calorificValues.length) {
                        try {
                            int price = Integer.parseInt(loosePriceMatcher.group(1));
                            if (price >= 300 && price <= 800) {
                                deduplicator.addData(date, calorificValues[index], price, tooltipData + "_" + index);
                                index++;
                            }
                        } catch (Exception e) {
                            logger.debug("宽松解析失败: {}", e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析tooltip数据失败: {}", e.getMessage());
        }
    }

    /**
     * 使用去重辅助类的当前显示数据提取方法
     */
    private static void extractCurrentDisplayDataWithDeduplicator(String pageText, DataDeduplicator deduplicator) {
        try {
            // 查找当前页面中显示的价格信息
            String[] lines = pageText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.contains("07-") && (line.contains("kCal") || line.contains("元"))) {

                    // 尝试提取这一行的数据
                    Pattern fullPattern = Pattern.compile("(07-\\d{2}).*?(\\d{4,5})kCal.*?(\\d{3,})");
                    Matcher fullMatcher = fullPattern.matcher(line);

                    if (fullMatcher.find()) {
                        try {
                            String date = fullMatcher.group(1);
                            int calorific = Integer.parseInt(fullMatcher.group(2));
                            int price = Integer.parseInt(fullMatcher.group(3));

                            deduplicator.addData(date, calorific, price, line);

                        } catch (Exception e) {
                            logger.debug("解析当前显示数据失败: {}", e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("提取当前显示数据失败: {}", e.getMessage());
        }
    }

    /**
     * 改进的tooltip数据解析方法（保留用于兼容性）
     */
    private static void parseTooltipDataImproved(String tooltipData, Map<String, Map<Integer, Integer>> result) {
        try {
            logger.debug("开始解析tooltip数据: {}", tooltipData);

            // 针对新格式的数据进行解析：07-285500kCal651（+1）5000kCal585（-）4500kCal520（-）
            // 首先提取日期
            Pattern datePattern = Pattern.compile("(07-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(tooltipData);

            if (dateMatcher.find()) {
                String date = dateMatcher.group(1);
                logger.debug("提取到日期: {}", date);

                // 移除日期部分，处理剩余的价格数据
                String priceData = tooltipData.substring(dateMatcher.end());

                // 使用改进的正则表达式匹配：数字kCal数字（涨跌幅）
                Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal(\\d{3,})(?:[（(][^)）]*[)）])?");
                Matcher priceMatcher = pricePattern.matcher(priceData);

                Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());

                while (priceMatcher.find()) {
                    try {
                        int calorific = Integer.parseInt(priceMatcher.group(1));
                        int price = Integer.parseInt(priceMatcher.group(2));

                        // 验证数据合理性
                        if (price >= 300 && price <= 800 &&
                            (calorific == 5500 || calorific == 5000 || calorific == 4500)) {

                            // 避免重复添加相同的数据
                            if (!dateData.containsKey(calorific) || !dateData.get(calorific).equals(price)) {
                                dateData.put(calorific, price);
                                logger.debug("解析成功: {} - {}kCal={}元", date, calorific, price);
                            }
                        }
                    } catch (Exception e) {
                        logger.debug("解析单个价格失败: {}", e.getMessage());
                    }
                }

                // 如果没有找到标准格式，尝试其他模式
                if (dateData.isEmpty()) {
                    // 尝试更宽松的匹配模式
                    Pattern loosePricePattern = Pattern.compile("(\\d{3,})");
                    Matcher loosePriceMatcher = loosePricePattern.matcher(priceData);

                    // 按顺序匹配价格，通常是5500kCal, 5000kCal, 4500kCal的顺序
                    int[] calorificValues = {5500, 5000, 4500};
                    int index = 0;

                    while (loosePriceMatcher.find() && index < calorificValues.length) {
                        try {
                            int price = Integer.parseInt(loosePriceMatcher.group(1));
                            if (price >= 300 && price <= 800) {
                                dateData.put(calorificValues[index], price);
                                logger.debug("宽松解析: {} - {}kCal={}元", date, calorificValues[index], price);
                                index++;
                            }
                        } catch (Exception e) {
                            logger.debug("宽松解析失败: {}", e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("解析tooltip数据失败: {}", e.getMessage());
        }
    }

    /**
     * 改进的当前显示数据提取方法（带去重）
     */
    private static void extractCurrentDisplayDataImproved(String pageText,
                                                         Map<String, Map<Integer, Integer>> result,
                                                         Set<String> processedData) {
        try {
            // 查找当前页面中显示的价格信息
            String[] lines = pageText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.contains("07-") && (line.contains("kCal") || line.contains("元"))) {

                    // 检查是否已经处理过这行数据
                    if (processedData.contains(line)) {
                        continue;
                    }

                    // 尝试提取这一行的数据
                    Pattern fullPattern = Pattern.compile("(07-\\d{2}).*?(\\d{4,5})kCal.*?(\\d{3,})");
                    Matcher fullMatcher = fullPattern.matcher(line);

                    if (fullMatcher.find()) {
                        try {
                            String date = fullMatcher.group(1);
                            int calorific = Integer.parseInt(fullMatcher.group(2));
                            int price = Integer.parseInt(fullMatcher.group(3));

                            if (price >= 300 && price <= 800 &&
                                (calorific == 5500 || calorific == 5000 || calorific == 4500)) {

                                Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());

                                // 避免重复添加相同的数据
                                if (!dateData.containsKey(calorific) || !dateData.get(calorific).equals(price)) {
                                    dateData.put(calorific, price);
                                    logger.debug("从当前显示提取: {} - {}kCal={}元", date, calorific, price);
                                    processedData.add(line); // 标记为已处理
                                }
                            }
                        } catch (Exception e) {
                            logger.debug("解析当前显示数据失败: {}", e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("提取当前显示数据失败: {}", e.getMessage());
        }
    }

    /**
     * 生成模拟数据（用于测试）
     */
    private static Map<String, Map<Integer, Integer>> generateMockData() {
        Map<String, Map<Integer, Integer>> mockData = new LinkedHashMap<>();

        // 添加7.22-7.30的模拟数据
        String[] dates = {"07-22", "07-23", "07-24", "07-25", "07-26", "07-27", "07-28", "07-29", "07-30"};

        // 初始价格
        int price5500 = 643;
        int price5000 = 580;
        int price4500 = 514;

        for (String date : dates) {
            Map<Integer, Integer> dayData = new LinkedHashMap<>();
            dayData.put(5500, price5500);
            dayData.put(5000, price5000);
            dayData.put(4500, price4500);

            mockData.put(date, dayData);

            // 每天价格小幅变动
            price5500 += 2;
            price5000 += 2;
            price4500 += 2;
        }

        logger.info("生成了 {} 天的模拟数据", mockData.size());
        return mockData;
    }

    /**
     * 从HTML内容提取数据 - 改进版
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromHTML(String html) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();

        try {
            logger.info("从HTML提取数据，HTML长度: {}", html.length());

            // 先检查HTML中是否包含关键信息
            if (!html.contains("07-") && !html.contains("kCal") && !html.contains("元")) {
                logger.warn("HTML中未找到关键信息，可能页面未正确加载");
                return result;
            }

            // 多种模式尝试提取数据

            // 模式1: 查找包含完整信息的行
            Pattern fullPattern = Pattern.compile("(07-\\d{2}).*?(\\d{4,5})kCal.*?(\\d{3,})");
            Matcher fullMatcher = fullPattern.matcher(html);

            while (fullMatcher.find()) {
                try {
                    String date = fullMatcher.group(1);
                    int calorific = Integer.parseInt(fullMatcher.group(2));
                    int price = Integer.parseInt(fullMatcher.group(3));

                    if (price >= 300 && price <= 800) { // 价格范围过滤
                        Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                        dateData.put(calorific, price);
                        logger.debug("模式1提取: {} - {}kCal={}元", date, calorific, price);
                    }
                } catch (Exception e) {
                    logger.debug("模式1解析失败: {}", e.getMessage());
                }
            }

            // 模式2: 分别查找日期和价格
            Pattern datePattern = Pattern.compile("(07-\\d{2})");
            Matcher dateMatcher = datePattern.matcher(html);

            while (dateMatcher.find()) {
                String date = dateMatcher.group(1);

                // 在日期前后200字符范围内查找价格信息
                int start = Math.max(0, dateMatcher.start() - 200);
                int end = Math.min(html.length(), dateMatcher.end() + 200);
                String context = html.substring(start, end);

                // 查找热值和价格
                Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})");
                Matcher priceMatcher = pricePattern.matcher(context);

                while (priceMatcher.find()) {
                    try {
                        int calorific = Integer.parseInt(priceMatcher.group(1));
                        int price = Integer.parseInt(priceMatcher.group(2));

                        if (price >= 300 && price <= 800) {
                            Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                            dateData.put(calorific, price);
                            logger.debug("模式2提取: {} - {}kCal={}元", date, calorific, price);
                        }
                    } catch (Exception e) {
                        logger.debug("模式2解析失败: {}", e.getMessage());
                    }
                }
            }

            // 模式3: 查找常见的价格模式
            Pattern priceOnlyPattern = Pattern.compile("(\\d{3})元.*?(\\d{4,5})kCal");
            Matcher priceOnlyMatcher = priceOnlyPattern.matcher(html);

            while (priceOnlyMatcher.find()) {
                try {
                    int price = Integer.parseInt(priceOnlyMatcher.group(1));
                    int calorific = Integer.parseInt(priceOnlyMatcher.group(2));

                    if (price >= 300 && price <= 800) {
                        // 假设是最近的日期
                        String defaultDate = "07-22";
                        Map<Integer, Integer> dateData = result.computeIfAbsent(defaultDate, k -> new LinkedHashMap<>());
                        dateData.put(calorific, price);
                        logger.debug("模式3提取: {} - {}kCal={}元", defaultDate, calorific, price);
                    }
                } catch (Exception e) {
                    logger.debug("模式3解析失败: {}", e.getMessage());
                }
            }

            logger.info("HTML提取完成，找到 {} 天的数据", result.size());

        } catch (Exception e) {
            logger.error("从HTML提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }
    
    /**
     * 从页面文本提取数据 - 改进版
     */
    private static Map<String, Map<Integer, Integer>> extractDataFromText(String text) {
        Map<String, Map<Integer, Integer>> result = new LinkedHashMap<>();

        try {
            logger.info("从文本提取数据，文本长度: {}", text.length());

            // 先检查文本中是否包含关键信息
            if (!text.contains("07-") && !text.contains("kCal") && !text.contains("元")) {
                logger.warn("文本中未找到关键信息");
                return result;
            }

            // 输出文本的前500字符用于调试
            String preview = text.length() > 500 ? text.substring(0, 500) + "..." : text;
            logger.debug("文本预览: {}", preview);

            // 按行分割
            String[] lines = text.split("\n");
            logger.info("文本共 {} 行", lines.length);

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.isEmpty()) continue;

                // 查找包含日期的行
                if (line.contains("07-")) {
                    logger.debug("发现包含日期的行[{}]: {}", i, line);

                    // 提取日期
                    Pattern datePattern = Pattern.compile("(07-\\d{2})");
                    Matcher dateMatcher = datePattern.matcher(line);

                    while (dateMatcher.find()) {
                        String date = dateMatcher.group(1);

                        // 在当前行和前后几行中查找价格信息
                        String context = line;

                        // 扩展上下文（前后2行）
                        for (int j = Math.max(0, i-2); j <= Math.min(lines.length-1, i+2); j++) {
                            if (j != i) {
                                context += " " + lines[j].trim();
                            }
                        }

                        // 查找热值和价格
                        Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})");
                        Matcher priceMatcher = pricePattern.matcher(context);

                        while (priceMatcher.find()) {
                            try {
                                int calorific = Integer.parseInt(priceMatcher.group(1));
                                int price = Integer.parseInt(priceMatcher.group(2));

                                if (price >= 300 && price <= 800) {
                                    Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                                    dateData.put(calorific, price);
                                    logger.debug("从文本提取: {} - {}kCal={}元", date, calorific, price);
                                }
                            } catch (Exception e) {
                                logger.debug("解析价格失败: {}", e.getMessage());
                            }
                        }

                        // 如果没有找到完整的价格信息，尝试查找单独的数字
                        if (!result.containsKey(date)) {
                            Pattern numberPattern = Pattern.compile("\\b(\\d{3})\\b");
                            Matcher numberMatcher = numberPattern.matcher(context);

                            while (numberMatcher.find()) {
                                try {
                                    int price = Integer.parseInt(numberMatcher.group(1));

                                    if (price >= 300 && price <= 800) {
                                        Map<Integer, Integer> dateData = result.computeIfAbsent(date, k -> new LinkedHashMap<>());
                                        dateData.put(5500, price); // 默认5500kCal
                                        logger.debug("从文本提取简单价格: {} - 5500kCal={}元", date, price);
                                        break; // 只取第一个合理的价格
                                    }
                                } catch (Exception e) {
                                    logger.debug("解析简单价格失败: {}", e.getMessage());
                                }
                            }
                        }
                    }
                }

                // 查找包含热值和价格但没有日期的行
                if (line.contains("kCal") && (line.contains("元") || line.matches(".*\\d{3,}.*"))) {
                    logger.debug("发现包含价格的行[{}]: {}", i, line);

                    Pattern pricePattern = Pattern.compile("(\\d{4,5})kCal[^\\d]*(\\d{3,})");
                    Matcher priceMatcher = pricePattern.matcher(line);

                    while (priceMatcher.find()) {
                        try {
                            int calorific = Integer.parseInt(priceMatcher.group(1));
                            int price = Integer.parseInt(priceMatcher.group(2));

                            if (price >= 300 && price <= 800) {
                                // 使用默认日期
                                String defaultDate = "07-22";
                                Map<Integer, Integer> dateData = result.computeIfAbsent(defaultDate, k -> new LinkedHashMap<>());
                                dateData.put(calorific, price);
                                logger.debug("从价格行提取: {} - {}kCal={}元", defaultDate, calorific, price);
                            }
                        } catch (Exception e) {
                            logger.debug("解析价格行失败: {}", e.getMessage());
                        }
                    }
                }
            }

            logger.info("文本提取完成，找到 {} 天的数据", result.size());

        } catch (Exception e) {
            logger.error("从文本提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }
    
    /**
     * 打印结果
     */
    private static void printResults(Map<String, Map<Integer, Integer>> data) {
        System.out.println("\n=== CCTD指数数据 ===");

        if (data.isEmpty()) {
            System.out.println("未提取到数据！");
            return;
        }

        // 按日期排序
        List<String> dates = new ArrayList<>(data.keySet());
        Collections.sort(dates);

        Map<Integer, Integer> previousPrices = new HashMap<>(); // 用于计算涨跌幅

        for (String date : dates) {
            Map<Integer, Integer> prices = data.get(date);

            System.out.print(date + ": ");

            List<String> priceStrings = new ArrayList<>();

            // 按热值排序显示（5500, 5000, 4500）
            int[] calorificOrder = {5500, 5000, 4500};

            for (int calorific : calorificOrder) {
                if (prices.containsKey(calorific)) {
                    int currentPrice = prices.get(calorific);
                    String priceStr = calorific + "kCal对应的是" + currentPrice + "元";

                    // 计算涨跌幅
                    if (previousPrices.containsKey(calorific)) {
                        int previousPrice = previousPrices.get(calorific);
                        int change = currentPrice - previousPrice;
                        if (change > 0) {
                            priceStr += " 涨幅+" + change;
                        } else if (change < 0) {
                            priceStr += " 跌幅" + change;
                        } else {
                            priceStr += " 涨幅-";
                        }
                    } else {
                        priceStr += " 涨幅-"; // 第一天没有对比数据
                    }

                    priceStrings.add(priceStr);
                }
            }

            System.out.println(String.join(", ", priceStrings));

            // 更新前一天的价格用于下次计算涨跌幅
            for (Map.Entry<Integer, Integer> entry : prices.entrySet()) {
                previousPrices.put(entry.getKey(), entry.getValue());
            }
        }

        System.out.println("\n总共提取到 " + data.size() + " 天的数据");
        System.out.println("数据格式示例：07-22: 5500kCal对应的是643元 涨幅+2, 5000kCal对应的是580元 涨幅+2, 4500kCal对应的是514元 涨幅+2");
    }
}
