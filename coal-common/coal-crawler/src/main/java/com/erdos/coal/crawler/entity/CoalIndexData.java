package com.erdos.coal.crawler.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.crawler.enums.IndexType;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.IndexOptions;
import dev.morphia.annotations.Indexes;
import dev.morphia.annotations.Property;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 煤炭指数基础数据实体
 * 存储爬取的原始数据和计算后的衍生数据
 */
@Entity(value = "coal_index_data", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("indexType"), @Field("dataDate"), @Field("calorificValue")}, 
               options = @IndexOptions(name = "idx_type_date_caloric", unique = true, background = true)),
        @Index(fields = {@Field("dataDate")},
               options = @IndexOptions(name = "idx_data_date", background = true)),
        @Index(fields = {@Field("indexType"), @Field("dataDate")}, 
               options = @IndexOptions(name = "idx_type_date", background = true))
})
public class CoalIndexData extends BaseMongoInfo {
    
    /**
     * 指数类型：SHENHUA/CCTD/CCI
     */
    @Property("indexType")
    private IndexType indexType;
    
    /**
     * 数据日期（如：2025-09-12）
     */
    @Property("dataDate")
    private Date dataDate;
    
    /**
     * 热值（大卡）：4500/5000/5500/5800
     */
    @Property("calorificValue")
    private Integer calorificValue;
    
    /**
     * 价格（元/吨）- 爬取的原始数据
     */
    @Property("price")
    private BigDecimal price;
    
    /**
     * 涨跌幅（%）- 计算得出
     */
    @Property("changeRate")
    private BigDecimal changeRate;
    
    /**
     * 涨跌趋势：UP/DOWN/FLAT
     */
    @Property("trendDirection")
    private String trendDirection;
    
    /**
     * 较上期变化金额（元）- 计算得出
     */
    @Property("changeAmount")
    private BigDecimal changeAmount;
    
    /**
     * 上期价格（元/吨）- 用于计算涨跌幅
     */
    @Property("previousPrice")
    private BigDecimal previousPrice;
    
    /**
     * 数据来源URL
     */
    @Property("sourceUrl")
    private String sourceUrl;
    
    /**
     * 爬取时间
     */
    @Property("crawlTime")
    private Date crawlTime;
    
    /**
     * 数据状态：1-有效，0-无效
     */
    @Property("status")
    private Integer status = 1;
    
    /**
     * 是否对会员可见：true-会员可见，false-非会员可见
     */
    @Property("memberVisible")
    private Boolean memberVisible = true;
    
    /**
     * 备注信息
     */
    @Property("remark")
    private String remark;
    
    // 构造函数
    public CoalIndexData() {}
    
    public CoalIndexData(IndexType indexType, Date dataDate, Integer calorificValue, BigDecimal price) {
        this.indexType = indexType;
        this.dataDate = dataDate;
        this.calorificValue = calorificValue;
        this.price = price;
        this.crawlTime = new Date();
    }
    
    // Getter和Setter方法
    public IndexType getIndexType() {
        return indexType;
    }
    
    public void setIndexType(IndexType indexType) {
        this.indexType = indexType;
    }
    
    public Date getDataDate() {
        return dataDate;
    }
    
    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }
    
    public Integer getCalorificValue() {
        return calorificValue;
    }
    
    public void setCalorificValue(Integer calorificValue) {
        this.calorificValue = calorificValue;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getChangeRate() {
        return changeRate;
    }
    
    public void setChangeRate(BigDecimal changeRate) {
        this.changeRate = changeRate;
    }
    
    public String getTrendDirection() {
        return trendDirection;
    }
    
    public void setTrendDirection(String trendDirection) {
        this.trendDirection = trendDirection;
    }
    
    public BigDecimal getChangeAmount() {
        return changeAmount;
    }
    
    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }
    
    public BigDecimal getPreviousPrice() {
        return previousPrice;
    }
    
    public void setPreviousPrice(BigDecimal previousPrice) {
        this.previousPrice = previousPrice;
    }
    
    public String getSourceUrl() {
        return sourceUrl;
    }
    
    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }
    
    public Date getCrawlTime() {
        return crawlTime;
    }
    
    public void setCrawlTime(Date crawlTime) {
        this.crawlTime = crawlTime;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Boolean getMemberVisible() {
        return memberVisible;
    }
    
    public void setMemberVisible(Boolean memberVisible) {
        this.memberVisible = memberVisible;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
