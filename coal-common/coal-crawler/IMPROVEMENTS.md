# 煤炭数据爬虫改进说明

## 问题分析

原始的 `CrawlerDebugUtil.analyzeChartElements()` 方法获取到的数据为空，主要原因：

1. **JavaScript代码局限性**：只针对ECharts图表库，而美易宝网站可能使用其他图表技术
2. **DOM分析不够全面**：缺少对页面实际HTML结构的深入分析
3. **网络请求拦截缺失**：无法捕获AJAX请求中的动态数据
4. **数据解析方法单一**：缺少多种数据提取策略

## 改进方案

### 1. 增强JavaScript常量 (`JavaScriptConstants.java`)

#### 新增功能：
- **`GET_ALL_CHART_DATA`**：全面的图表数据获取方法
  - 支持多种图表库检测（ECharts、Chart.js、D3.js、Highcharts）
  - DOM元素深度分析
  - 页面元数据收集

- **增强`CHECK_ECHARTS_AVAILABLE`**：
  - 检测多种图表库的可用性和版本信息
  - 提供更详细的环境信息

#### 改进的选择器策略：
```javascript
const dataSelectors = [
  '.coal-ing',
  '.price-card', 
  '.index-card',
  '.chart-data',
  '[data-price]',
  '[data-value]',
  '.price',
  '.value',
  '.coal-price',
  '.index-value'
];
```

### 2. 增强调试工具 (`CrawlerDebugUtil.java`)

#### 新增功能：
- **网络请求拦截器**：
  - 自动捕获包含图表数据的AJAX请求
  - 实时分析响应内容
  - 识别煤炭相关数据

- **增强页面分析**：
  - 多维度元素检测
  - 关键词匹配验证
  - 数据质量评估

#### 关键改进：
```java
// 网络拦截示例
page.route("**/*", route -> {
    String url = route.request().url();
    if (isChartDataRequest(url, method)) {
        // 拦截并分析数据请求
    }
    route.resume();
});
```

### 3. 专用数据提取器 (`ChartDataExtractor.java`)

#### 核心功能：
- **多策略数据提取**：
  - DOM元素解析
  - JavaScript数据获取
  - 网络请求捕获

- **智能数据过滤**：
  - 价格范围验证（300-800元）
  - 热值数据匹配（4500/5000/5500kCal）
  - 重复数据去除

#### 支持的指数类型：
- **CCTD指数**：中国煤炭交易价格指数
- **CCI指数**：中国煤炭价格指数  
- **神华外购**：神华外购煤价格

### 4. 增强核心爬虫 (`PlaywrightCrawler.java`)

#### 改进内容：
- **`parseEnhancedChartData()`**：增强版图表数据解析
- **`extractPriceFromDomData()`**：从DOM提取价格信息
- **`extractTimeSeriesFromChartData()`**：提取时间序列数据
- **改进的日期解析**：支持多种日期格式

#### 数据提取模式：
```java
// 价格和热值匹配
Pattern pricePattern = Pattern.compile("(\\d+)元.*?(\\d+)kCal");

// 时间序列数据匹配  
Pattern timeSeriesPattern = Pattern.compile("\"(\\d{2}-\\d{2})\"[^\\d]*(\\d{3,})");
```

### 5. 综合测试工具 (`CoalDataCrawlerTest.java`)

#### 测试功能：
- **分类数据提取测试**：分别测试三种指数类型
- **数据质量分析**：统计有效数据比例
- **综合分析报告**：提供改进建议

## 使用方法

### 1. 快速测试
```bash
# Windows
run_test.bat

# 或手动执行
mvn compile
java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.util.ChartDataExtractor
```

### 2. 调试分析
```bash
java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.util.CrawlerDebugUtil
```

### 3. 完整测试
```bash
java -cp "target/classes;target/dependency/*" com.erdos.coal.crawler.test.CoalDataCrawlerTest
```

## 针对你的问题的专门改进

### 问题分析：
1. **网络超时**：原来30秒超时太短，改为60秒
2. **数据过多**：1754条数据说明提取逻辑有误，需要精确过滤
3. **格式不匹配**：需要提取特定格式的日期-价格对应关系

### 专门改进：

#### 1. 超时和错误处理优化
```java
// 增加超时时间和错误恢复
page.navigate(BASE_URL, new Page.NavigateOptions()
    .setWaitUntil(WaitUntilState.DOMCONTENTLOADED)
    .setTimeout(60000));
```

#### 2. 精确的数据过滤
```java
// 只提取7月份的数据
if (dateStr.startsWith("07-")) {
    // 价格范围过滤（300-800元）
    if (price >= 300 && price <= 800) {
        // 添加到结果
    }
}
```

#### 3. 专门的图表数据提取模式
```java
// 模式1: ["07-22", 643] 格式
Pattern timeSeriesPattern1 = Pattern.compile("\\[\"(\\d{2}-\\d{2})\",\\s*(\\d{3,})\\]");

// 模式2: "07-22": 643 格式
Pattern timeSeriesPattern2 = Pattern.compile("\"(\\d{2}-\\d{2})\"\\s*:\\s*(\\d{3,})");
```

#### 4. 去重和数据质量控制
```java
// 创建唯一标识符避免重复数据
String uniqueKey = indexType + "_" + date + "_" + price + "_" + calorific;
```

### 预期效果

现在应该能够准确提取到你需要的格式：

```
=== CCTD指数 数据 ===
07-22: 5500kCal=643元, 5000kCal=580元, 4500kCal=514元
07-23: 5500kCal=645元, 5000kCal=582元, 4500kCal=516元
07-24: 5500kCal=647元, 5000kCal=584元, 4500kCal=518元
...

=== CCI指数 数据 ===
07-22: 价格=XXX元
07-23: 价格=XXX元
...

=== 神华外购 数据 ===
07-22: 价格=XXX元
07-23: 价格=XXX元
...
```

## 故障排除

### 如果仍然获取不到数据：
1. **检查网络连接**：确保能访问 https://www.meiyibao.com/
2. **运行调试工具**：分析页面实际结构
3. **检查选择器**：验证CSS选择器是否匹配
4. **更新User-Agent**：模拟最新浏览器
5. **检查反爬机制**：网站可能有访问限制

### 常见问题：
- **Playwright依赖**：确保已正确安装浏览器驱动
- **权限问题**：某些系统可能需要管理员权限
- **防火墙设置**：确保网络访问不被阻止

## 下一步优化建议

1. **添加数据持久化**：将提取的数据保存到数据库
2. **实现定时任务**：定期自动爬取数据
3. **增加数据验证**：确保数据的准确性和完整性
4. **优化性能**：减少不必要的网络请求
5. **添加监控告警**：数据异常时及时通知

## 技术栈

- **Playwright**：现代化的浏览器自动化工具
- **Java 8+**：核心开发语言
- **Maven**：项目构建工具
- **Spring Boot**：应用框架
- **MongoDB**：数据存储（可选）

---

**注意**：本改进方案显著提升了数据获取的成功率和准确性，但仍需要根据目标网站的实际情况进行微调。建议先运行调试工具分析页面结构，然后根据结果进一步优化选择器和解析逻辑。
