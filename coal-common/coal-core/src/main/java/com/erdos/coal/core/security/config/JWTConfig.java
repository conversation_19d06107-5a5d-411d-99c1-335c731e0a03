package com.erdos.coal.core.security.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * JWT配置类
 */
@Component
@ConfigurationProperties(prefix = "jwt")
public class JWTConfig {

    public static String secret; // 密钥KEY
    public static String tokenHeader; //tokenHeader
    //public static Integer expiration; //过期时间
    public static Long expBiz; //  # 业务系统用户
    public static Long expWeb;//  # Web 用户
    public static Long expCU; //# 客商用户
    public static Long expDU;//  # 司机用户
    public static Long expWechat; //  # 微信小程序
    public static List<String> antMatchers; //不需要认证的接口

    //TODO: ligx
    public static String signSecret; // 签名KEY
    public static Long signDiffTime; // 签名允许间隔时间
    public static List<String> signExcludeMatchers; //不需要签名的接口

    //防抖动接口限制访问间隔时间
    public static Long expTime1;
    public static Long expTime2;
    public static Long expTime3;
    public static Long expTime4;
    public static Long expTime5;
    public static Long expTime6;
    public static Long expTime7;
    public static Long expTime8;
    public static Long expTime9;
    public static Long expTime10;
    public static Long expTime11;
    public static Long expTime12;
    public static Long expTime13;
    public static Long expTime14;
    public static Long expTime15;
    public static Long expTime16;
    public static Long expTime17;
    public static Long expTime18;
    public static Long expTime19;
    public static Long expTime20;

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public void setTokenHeader(String tokenHeader) {
        this.tokenHeader = tokenHeader;
    }

//    public void setExpiration(Integer expiration) {
//        this.expiration = expiration * 1000;
//    }

    public void setExpBiz(Long expBiz) {
        this.expBiz = expBiz * 1000;
    }

    public void setExpWeb(Long expWeb) {
        this.expWeb = expWeb * 1000;
    }

    public void setExpCU(Long expCU) {
        this.expCU = expCU * 1000;
    }

    public void setExpDU(Long expDU) {
        this.expDU = expDU * 1000;
    }

    public void setExpWechat(Long expWechat) {
        this.expWechat = expWechat * 1000;
    }

    public void setAntMatchers(List<String> antMatchers) {
        this.antMatchers = antMatchers;
    }

    public void setSignSecret(String signSecret) {
        this.signSecret = signSecret;
    }

    public void setSignDiffTime(Long signDiffTime) {
        this.signDiffTime = signDiffTime;
    }

    public void setSignExcludeMatchers(List<String> signExcludeMatchers) {
        this.signExcludeMatchers = signExcludeMatchers;
    }

    public void setExpTime1(Long expTime1) {
        this.expTime1 = expTime1;
    }

    public void setExpTime2(Long expTime2) {
        this.expTime2 = expTime2;
    }

    public void setExpTime3(Long expTime3) {
        this.expTime3 = expTime3;
    }

    public void setExpTime4(Long expTime4) {
        this.expTime4 = expTime4;
    }

    public void setExpTime5(Long expTime5) {
        this.expTime5 = expTime5;
    }

    public void setExpTime6(Long expTime6) {
        this.expTime6 = expTime6;
    }

    public void setExpTime7(Long expTime7) {
        this.expTime7 = expTime7;
    }

    public void setExpTime8(Long expTime8) {
        this.expTime8 = expTime8;
    }

    public void setExpTime9(Long expTime9) {
        this.expTime9 = expTime9;
    }

    public void setExpTime10(Long expTime10) {
        this.expTime10 = expTime10;
    }

    public void setExpTime11(Long expTime11) {
        this.expTime11 = expTime11;
    }

    public void setExpTime12(Long expTime12) {
        this.expTime12 = expTime12;
    }

    public void setExpTime13(Long expTime13) {
        this.expTime13 = expTime13;
    }

    public void setExpTime14(Long expTime14) {
        this.expTime14 = expTime14;
    }

    public void setExpTime15(Long expTime15) {
        this.expTime15 = expTime15;
    }

    public void setExpTime16(Long expTime16) {
        this.expTime16 = expTime16;
    }

    public void setExpTime17(Long expTime17) {
        this.expTime17 = expTime17;
    }

    public void setExpTime18(Long expTime18) {
        this.expTime18 = expTime18;
    }

    public void setExpTime19(Long expTime19) {
        this.expTime19 = expTime19;
    }

    public void setExpTime20(Long expTime20) {
        this.expTime20 = expTime20;
    }
}