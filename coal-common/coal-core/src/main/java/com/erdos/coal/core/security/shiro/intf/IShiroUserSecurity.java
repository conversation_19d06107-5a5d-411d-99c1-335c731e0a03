package com.erdos.coal.core.security.shiro.intf;

import com.erdos.coal.core.security.jwt.JwtToken;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;

import java.util.List;

public interface IShiroUserSecurity {
    ShiroUser getUserByJwtToken(String userName,String userType);

    List<String> findPermissionByJwtToken(String userName, String userType);

    //异步任务，用户登录添加用户信息和权限信息 到 缓存
    void putShiroUserToCache(String userId, String username, String password, String userType);
}
