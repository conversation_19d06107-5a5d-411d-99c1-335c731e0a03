package com.erdos.coal.core.exception;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;

public class DataAccessException extends GlobalException {
    public DataAccessException(int code, String msg) {
        super(code, msg);
    }

    public DataAccessException(ResponseCode rc) {
        super(rc);
    }

    public DataAccessException(ServerResponse sr) {
        super(sr);
    }
}
