package com.erdos.coal.core.security.utils;

import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

public class ShiroUtils {

    public static ShiroUser getCurrentUser() {
        Subject currSubject = SecurityUtils.getSubject();
        return (ShiroUser) currSubject.getPrincipal();
    }

    /*public static Integer getUserId() {
        return getCurrentUser().getUserId();
    }*/
    public static String getUserId() {
        ShiroUser shiroUser = getCurrentUser();
        if (shiroUser == null) return null;
        return shiroUser.getUserId();
    }

    public static String getUserName() {
        ShiroUser shiroUser = getCurrentUser();
        if (shiroUser == null) return null;
        return shiroUser.getUsername();
    }

    public static String getUserType() {
        ShiroUser shiroUser = getCurrentUser();
        if (shiroUser == null) return null;
        return shiroUser.getUserType();
    }
}
