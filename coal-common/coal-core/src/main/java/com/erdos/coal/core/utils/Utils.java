package com.erdos.coal.core.utils;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.UUID;

public class Utils {
    private static Logger logger = LoggerFactory.getLogger(Utils.class);
    private static String startupInfo;

    public static void setStartupInfo(String host, String ip, String port) {
        if (strIsNull(startupInfo)) {
            SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            startupInfo = host + " | " + ip + ":" + port + " | " + sdfDateTime.format(new Date());
        }
    }

    public static String getStartupInfo() {
        return startupInfo;
    }

    /**
     * 生成一个UUID
     *
     * @return UUID
     */
    public static String getUUID() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }

    /**
     * @param str      被转化字符串
     * @param defValue 转化失败后的默认值
     * @return
     */
    public static int parseInt(String str, int defValue) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            return defValue;
        }
    }

    /**
     * @param str      被转化字符串
     * @param defValue 转化失败后的默认值
     * @return
     */
    public static double parseDouble(String str, double defValue) {
        try {
            return Double.parseDouble(str);
        } catch (Exception e) {
            return defValue;
        }
    }

    /**
     * @param str      被转化字符串
     * @param defValue 转化失败后的默认值
     * @return
     */
    public static Long parseLong(String str, Long defValue) {
        try {
            return Long.parseLong(str);
        } catch (Exception e) {
            return defValue;
        }
    }

    protected Boolean parseBoolean(String val) {
        if (val.equals("")) {
            return false;
        }
        return Boolean.valueOf(val);
    }

    /**
     * 判断字符串是否为空
     *
     * @param str
     * @return
     */
    public static boolean strIsNull(String str) {
        return ((str == null) || "".equals(str));
    }

    /**
     * 去空格，如为null则转化为空字符串
     *
     * @param str
     * @return
     */
    public static String trim(String str) {
        if (str == null) {
            return "";
        }
        return str.trim();
    }

    public static String trim(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return trim(obj.toString());
        }
    }

    /**
     * MD5加密
     *
     * @param str
     * @return
     */
    public static String md5(String str) {
        StringBuilder sb = new StringBuilder(32);
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] array = md.digest(str.getBytes("utf-8"));

            for (byte anArray : array) {
                sb.append(Integer.toHexString((anArray & 0xFF) | 0x100).toUpperCase().substring(1, 3));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

//    public static <T> T buildVoFromRequest(HttpServletRequest request, Class<T> clazz) {
//        T vo = null;
//        try {
//            vo = clazz.newInstance();
//            Field[] fields = clazz.getDeclaredFields();
//            for (Field field : fields) {
//
//                //不区分大小写处理【一种按实际大小写，另一种全部小写，都可以取值】
//                String rawFieldName = field.getName(); //实体中的属性名
//                String lowFieldName = field.getName().toLowerCase(); //属性全部小写
//
//                String value = request.getParameter(rawFieldName);
//                if (value == null) { //为空再按小写取一次
//                    value = request.getParameter(lowFieldName);
//                }
//
//                field.setAccessible(true);
//                if (!StringUtils.isBlank(value)) {
//                    value = value.trim();
//                    if (field.getType().equals(Long.class)) {
//                        field.set(vo, Long.valueOf(value));
//                    } else if (field.getType().equals(Integer.class)) {
//                        field.set(vo, Integer.valueOf(value));
//                    } else if (field.getType().equals(int.class)) {
//                        field.set(vo, Integer.valueOf(value));
//                    } else if (field.getType().equals(Double.class)) {
//                        field.set(vo, Double.valueOf(value));
//
//                    } else if (field.getType().equals(Boolean.class)) {
//                        field.set(vo, Boolean.valueOf(value));
//                    } else if (field.getType().equals(boolean.class)) {
//                        field.set(vo, Boolean.valueOf(value));
//
//                    } else if (field.getType().equals(String.class)) {
//                        field.set(vo, value);
//                    } else if (field.getType().equals(Date.class)) {
//                        Date d = DateTimeUtil.formatDateTime(value); //yyyy-MM-dd HH:mm:ss
//                        field.set(vo, d);
//                    }
//                }
//            }
//        } catch (InstantiationException | IllegalAccessException e) {
//            e.printStackTrace();
//        }
//
//        return vo;
//    }

    public static boolean isEmpty(Object obj) {
        return obj == null || String.valueOf(obj).trim().equals("");
    }

    public static String formatLike(String str) {
        if (StringUtils.isNotBlank(str)) {
            return "%" + str + "%";
        } else {
            return null;
        }
    }

    /**
     * 返回一定时间后的日期
     *
     * @param date   开始计时的时间
     * @param year   增加的年
     * @param month  增加的月
     * @param day    增加的日
     * @param hour   增加的小时
     * @param minute 增加的分钟
     * @param second 增加的秒
     * @return
     */
    public static Date getAfterDateTime(Date date, int year, int month, int day, int hour, int minute, int second) {
        if (date == null) date = new Date();
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        if (year != 0) cal.add(Calendar.YEAR, year);
        if (month != 0) cal.add(Calendar.MONTH, month);
        if (day != 0) cal.add(Calendar.DATE, day);
        if (hour != 0) cal.add(Calendar.HOUR_OF_DAY, hour);
        if (minute != 0) cal.add(Calendar.MINUTE, minute);
        if (second != 0) cal.add(Calendar.SECOND, second);
        return cal.getTime();
    }

    //TODO: 获得客户端的ip地址
    public static String getRemoteIP(HttpServletRequest request) {
        if (request.getHeader("x-forwarded-for") == null) {
            return request.getRemoteAddr();
        }
        return request.getHeader("x-forwarded-for");
    }

    //TODO: 获得客户端的主机名
    public static String getRemoteHost(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ip;
    }

    public static String getIpAddr(HttpServletRequest request) {
        String ipAddress = null;
        try {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (ipAddress.equals("127.0.0.1")) {
                    // 根据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();
                    } catch (UnknownHostException e) {
                        e.printStackTrace();
                    }
                    ipAddress = inet.getHostAddress();
                }
            }
            // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
                // = 15
                if (ipAddress.indexOf(",") > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
                }
            }

            if (ipAddress != null && ipAddress.equals("0:0:0:0:0:0:0:1")) {
                ipAddress = "127.0.0.1";
            }
        } catch (Exception e) {
            ipAddress = "";
        }
        // ipAddress = this.getRequest().getRemoteAddr();

        return ipAddress;
    }

    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr()的原因是有可能用户使用了代理软件方式避免真实IP地址,
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值
     *
     * @return ip
     */
    public static String getRealIP(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            if (ip.contains(",")) {
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
