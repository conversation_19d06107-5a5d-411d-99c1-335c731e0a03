package com.erdos.coal.core.security.shiro.service;

import com.erdos.coal.core.security.jwt.JwtToken;
import com.erdos.coal.core.security.jwt.JwtUtil;
import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import com.erdos.coal.core.security.shiro.intf.IShiroUserSecurity;
import com.erdos.coal.core.security.utils.ShiroUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ShiroRealmService extends AuthorizingRealm {

    @Resource
    private IShiroUserSecurity shiroUserSecurity;

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof JwtToken;
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        //ShiroUser user = ShiroUtils.getCurrentUser();
        SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
        //simpleAuthorizationInfo.addRole(sysUser.getRole());
        //Set<String> permission = new HashSet<String>(Arrays.asList(sysUser.getPermission().split(",")));

        String userName = ShiroUtils.getUserName();
        String userType = ShiroUtils.getUserType();
        List<String> permissions = shiroUserSecurity.findPermissionByJwtToken(userName, userType);
        simpleAuthorizationInfo.addStringPermissions(permissions);
        return simpleAuthorizationInfo;
    }

    /**
     * 用户身份识别(登录")
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) {
        JwtToken jwtToken = (JwtToken) authenticationToken;
        String token = (String) jwtToken.getCredentials();// 校验token有效性
        if (token == null) {
            //throw new AuthenticationException("token非法无效!");
            return null;
        }
        String username = JwtUtil.getUsername(token);
        String userType = JwtUtil.getUserType(token);
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(userType)) { //Token失效请重新登录
            //throw new AuthenticationException("token非法无效!");
            return null;
        }

        // 查询用户信息
        ShiroUser shiroUser = shiroUserSecurity.getUserByJwtToken(username, userType);
        if (shiroUser == null) {
            //throw new AuthenticationException("用户不存在!");
            return null;
        }

        if (!JwtUtil.verify(token, shiroUser.getPassword())) {
            // throw new AuthenticationException("认证失败");
            return null;
        }

        return new SimpleAuthenticationInfo(shiroUser, token, getName());
    }
}
