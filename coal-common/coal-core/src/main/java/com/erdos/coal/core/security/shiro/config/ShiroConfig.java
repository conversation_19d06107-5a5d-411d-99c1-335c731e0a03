package com.erdos.coal.core.security.shiro.config;

import com.erdos.coal.core.security.jwt.JwtFilter;
import com.erdos.coal.core.security.shiro.service.ShiroRealmService;
import org.apache.shiro.mgt.DefaultSessionStorageEvaluator;
import org.apache.shiro.mgt.DefaultSubjectDAO;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

// 权限配置文件
@Configuration
public class ShiroConfig {

    @Bean
    public DefaultWebSecurityManager getManager(ShiroRealmService realm) {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setRealm(realm);

        /*
         * 关闭shiro自带的session，详情见文档
         * http://shiro.apache.org/session-management.html#SessionManagement-StatelessApplications%28Sessionless%29
         */
        DefaultSubjectDAO subjectDAO = new DefaultSubjectDAO();
        DefaultSessionStorageEvaluator defaultSessionStorageEvaluator = new DefaultSessionStorageEvaluator();
        defaultSessionStorageEvaluator.setSessionStorageEnabled(false);
        subjectDAO.setSessionStorageEvaluator(defaultSessionStorageEvaluator);
        manager.setSubjectDAO(subjectDAO);

        return manager;
    }

    @Bean(name = "shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(org.apache.shiro.mgt.SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);

        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();//拦截器, 配置不会被拦截的链接 顺序判断

        //不需要认证的接口
        //filterChainDefinitionMap.put("/v2/**", "anon");
        filterChainDefinitionMap.put("/webjars/**", "anon");
        filterChainDefinitionMap.put("/images/**", "anon");
        filterChainDefinitionMap.put("/websocket/**", "anon");
        filterChainDefinitionMap.put("/configuration/**", "anon");
        filterChainDefinitionMap.put("/swagger**/**", "anon");
        filterChainDefinitionMap.put("/we_chat/small_pro/**", "anon");
        filterChainDefinitionMap.put("/api/manage/**", "anon");
        filterChainDefinitionMap.put("/web/sys/user/login", "anon");
        /*filterChainDefinitionMap.put("/api/bus/receive_car_msg/search_order", "anon");
        filterChainDefinitionMap.put("/api/bus/receive_car_msg/update_one_order2", "anon");
        filterChainDefinitionMap.put("/api/bus/receive_car_msg/update_one_order3", "anon");
        filterChainDefinitionMap.put("/api/bus/receive_car_msg/put_coal_ticket", "anon");*/
        filterChainDefinitionMap.put("/api/bus/syn_order/search", "anon");

        filterChainDefinitionMap.put("/api/bus/biz_login", "anon");
        filterChainDefinitionMap.put("/api/bus/biz_send_sms", "anon");
        filterChainDefinitionMap.put("/api/test/**", "anon");
        filterChainDefinitionMap.put("/web/test/**", "anon");
        filterChainDefinitionMap.put("/web/file/**", "anon");
        filterChainDefinitionMap.put("/api/file/**", "anon");
        filterChainDefinitionMap.put("/web/log/**", "anon");
        filterChainDefinitionMap.put("/api/bus/receive_car_msg/syn_quarantine", "anon");
        filterChainDefinitionMap.put("/api/bus/receive_car_msg/search_same_order", "anon");
        //filterChainDefinitionMap.put("/logout", "logout");

        //authc:所有url都必须认证通过才可以访问; anon:所有url都都可以匿名访问; user表示配置记住我或认证通过可以访问的地址

        // 添加自己的过滤器并且取名为jwt
        LinkedHashMap<String, Filter> filterMap = new LinkedHashMap<>();
        filterMap.put("jwt", new JwtFilter());
        shiroFilterFactoryBean.setFilters(filterMap);
        // 过滤链定义，从上向下顺序执行，一般将放在最为下边
        filterChainDefinitionMap.put("/**", "jwt");

        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }

    // SpringShiroFilter首先注册到spring容器,然后被包装成FilterRegistrationBean,最后通过FilterRegistrationBean注册到servlet容器
//    @SuppressWarnings("unchecked")
//    @Bean
//    public FilterRegistrationBean delegatingFilterProxy() {
//        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
//        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
//        proxy.setTargetFilterLifecycle(true);
//        proxy.setTargetBeanName("shiroFilter");
//        filterRegistrationBean.setFilter(proxy);
//        return filterRegistrationBean;
//    }

//    @Bean(name = "hashedCredentialsMatcher")
//    public HashedCredentialsMatcher hashedCredentialsMatcher() {
//        HashedCredentialsMatcher hashedCredentialsMatcher = new HashedCredentialsMatcher();
//        hashedCredentialsMatcher.setStoredCredentialsHexEncoded(true);
//        hashedCredentialsMatcher.setHashAlgorithmName("MD5");
//        hashedCredentialsMatcher.setHashIterations(1024);// 设置加密次数
//        return hashedCredentialsMatcher;
//    }

    /**
     * 下面的代码是添加注解支持
     */
    @Bean
    @DependsOn("lifecycleBeanPostProcessor")
    public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
        // 强制使用cglib，防止重复代理和可能引起代理出错的问题
        // https://zhuanlan.zhihu.com/p/29161098
        defaultAdvisorAutoProxyCreator.setProxyTargetClass(true);
        return defaultAdvisorAutoProxyCreator;
    }

    @Bean
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(DefaultWebSecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }
}
