package com.erdos.coal.core.security.jwt;

import com.erdos.coal.core.security.shiro.entity.ShiroUser;
import org.apache.shiro.authc.AuthenticationToken;

public class JwtToken implements AuthenticationToken {

    private static final long serialVersionUID = -8451637096112402805L;

    private ShiroUser shiroUser;
    private String token;

    public JwtToken(ShiroUser user, String token) {
        this.shiroUser = user;
        this.token = token;
    }

    @Override
    public Object getPrincipal() {
        return shiroUser;
    }

    @Override
    public Object getCredentials() {
        return token;
    }
}
