package com.erdos.coal.core.utils;

import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.entity.RestClassInfo;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.net.JarURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * @作者: LIGX
 * @时间: 2018/11/10
 * @描述: 反射类工具封装
 */

public class ReflectHelper {
    /**
     * 获取obj对象fieldName的Field
     *
     * @param obj
     * @param fieldName
     * @return
     */
    public static Field getFieldByFieldName(Object obj, String fieldName) {
        for (Class<?> superClass = obj.getClass();
             superClass != Object.class;
             superClass = superClass.getSuperclass()) {
            try {
                return superClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 获取obj对象fieldName的属性值
     *
     * @param obj
     * @param fieldName
     * @return
     * @throws SecurityException
     * @throws NoSuchFieldException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static Object getValueByFieldName(Object obj, String fieldName)
            throws SecurityException,
            NoSuchFieldException,
            IllegalArgumentException,
            IllegalAccessException {

        Field field = getFieldByFieldName(obj, fieldName);
        Object value = null;
        if (field != null) {
            if (field.isAccessible()) {
                value = field.get(obj);
            } else {
                field.setAccessible(true);
                value = field.get(obj);
                field.setAccessible(false);
            }
        }
        return value;
    }

    /**
     * 设置obj对象fieldName的属性值
     *
     * @param obj
     * @param fieldName
     * @param value
     * @throws SecurityException
     * @throws NoSuchFieldException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void setValueByFieldName(Object obj, String fieldName, Object value)
            throws SecurityException,
            NoSuchFieldException,
            IllegalArgumentException,
            IllegalAccessException {

        Field field = obj.getClass().getDeclaredField(fieldName);
        if (field.isAccessible()) {
            field.set(obj, value);
        } else {
            field.setAccessible(true);
            field.set(obj, value);
            field.setAccessible(false);
        }
    }

    //------------------------------------------------------------------------------------------------------------------

    //从包package中获取所有的Class
    public static Set<Class<?>> getEntityClasses(String packageName) {

        Set<Class<?>> classes = new HashSet<>();
        // 是否循环迭代
        boolean recursive = true;
        // 获取包的名字 并进行替换
        String packageDirName = packageName.replace('.', '/');
        //枚举的集合,循环来处理这个目录下的things
        Enumeration<URL> dirs;
        try {
            dirs = Thread.currentThread().getContextClassLoader().getResources(packageDirName);
            while (dirs.hasMoreElements()) { // 循环迭代
                URL url = dirs.nextElement();// 获取下一个元素
                String protocol = url.getProtocol();// 得到协议的名称
                if ("file".equals(protocol)) {// 如果是文件的形式
                    String filePath = URLDecoder.decode(url.getFile(), "UTF-8");// 获取包的物理路径
                    // 以文件的方式扫描整个包下的文件 并添加到集合中
                    findAndAddClassesInPackageByFile(packageName, filePath, recursive, classes);
                } else if ("jar".equals(protocol)) {
                    JarFile jar;// 定义一个JarFile
                    try {
                        // 获取jar
                        jar = ((JarURLConnection) url.openConnection()).getJarFile();
                        // 从此jar包 得到一个枚举类
                        Enumeration<JarEntry> entries = jar.entries();
                        while (entries.hasMoreElements()) {// 同样的进行循环迭代
                            // 获取jar里的一个实体 可以是目录 和一些jar包里的其他文件 如META-INF等文件
                            JarEntry entry = entries.nextElement();
                            String name = entry.getName();
                            // 如果是以/开头的
                            if (name.charAt(0) == '/') {
                                // 获取后面的字符串
                                name = name.substring(1);
                            }
                            // 如果前半部分和定义的包名相同
                            if (name.startsWith(packageDirName)) {
                                int idx = name.lastIndexOf('/');
                                // 如果以"/"结尾 是一个包
                                if (idx != -1) {
                                    // 获取包名 把"/"替换成"."
                                    packageName = name.substring(0, idx).replace('/', '.');
                                }
                                // 如果可以迭代下去 并且是一个包
                                //if ((idx != -1) || recursive) {
                                if ((idx != -1)) {
                                    // 如果是一个.class文件 而且不是目录
                                    if (name.endsWith(".class") && !entry.isDirectory()) {
                                        // 去掉后面的".class" 获取真正的类名
                                        String className = name.substring(packageName.length() + 1, name.length() - 6);
                                        try {
                                            // 添加到classes
                                            classes.add(Class.forName(packageName + '.' + className));
                                        } catch (ClassNotFoundException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return classes;
    }

    //以文件的形式来获取包下的所有Class
    private static void findAndAddClassesInPackageByFile(
            String packageName,
            String packagePath,
            final boolean recursive,
            Set<Class<?>> classes) {

        File dir = new File(packagePath);//// 获取此包的目录 建立一个File
        if (!dir.exists() || !dir.isDirectory()) { // 如果不存在或者 也不是目录就直接返回
            // event.warn("用户定义包名 " + packageName + " 下没有任何文件");
            return;
        }
        // 如果存在 就获取包下的所有文件 包括目录
        File[] dirFiles = dir.listFiles(new FileFilter() {
            // 自定义过滤规则 如果可以循环(包含子目录) 或则是以.class结尾的文件(编译好的java类文件)
            public boolean accept(File file) {
                return (recursive && file.isDirectory()) || (file.getName().endsWith(".class"));
            }
        });

        if (dirFiles == null)
            return;

        // 循环所有文件
        for (File file : dirFiles) {
            // 如果是目录 则继续扫描
            if (file.isDirectory()) {
                findAndAddClassesInPackageByFile(
                        packageName + "." + file.getName(),
                        file.getAbsolutePath(),
                        recursive,
                        classes);
            } else {
                // 如果是java类文件 去掉后面的.class 只留下类名
                String className = file.getName().substring(0, file.getName().length() - 6);
                try {
                    // 添加到集合中去
                    classes.add(Thread.currentThread().getContextClassLoader().loadClass(packageName + '.' + className));
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static Set<Class<?>> getCollectionClassByAnnotation(String basePackage, Class<? extends Annotation> clazz) {
        Set<Class<?>> classSet = new HashSet<>(); //找到的实体类注解
        Set<Class<?>> clsList = ReflectHelper.getEntityClasses(basePackage);
        if (clsList.size() > 0) {
            for (Class<?> cls : clsList) {
                if (cls.getAnnotation(clazz) != null) {
                    classSet.add(cls);
                }
            }
        }
        return classSet;
    }

    //------------------------------------------------------------------------------------------------------------------

    /**
     * 获取项目中所有接口信息
     *
     * @param basePackage 包名
     * @return
     */
    public static List<RestClassInfo> getRestControllerList(String basePackage) {
        //TODO: RestController
        List<RestClassInfo> restClassInfos = new ArrayList<>();

        //TODO: class
        Set<Class<?>> classSet = ReflectHelper.getCollectionClassByAnnotation(basePackage, RestController.class);
        for (Class<?> cls : classSet) {
            RestClassInfo restClassInfo = new RestClassInfo();
            restClassInfo.setName(getPackageLastPart(cls.getName()));

            RequestMapping classMapping = cls.getAnnotation(RequestMapping.class);
            if (classMapping != null && classMapping.value().length > 0) {
                //System.out.println(classMapping.value()[0]);
                restClassInfo.setMapping(classMapping.value()[0]); // /web/test
            } else {
                restClassInfo.setMapping("");
            }

            //TODO: method
            restClassInfo.setMethods(getRestCtrlMethodInfoList(cls));

            restClassInfos.add(restClassInfo);
        }
        return restClassInfos;
    }

    private static List<RestClassInfo.RestMethodInfo> getRestCtrlMethodInfoList(Class<?> cls) {
        List<RestClassInfo.RestMethodInfo> list = new ArrayList<>();

        Method[] methods = cls.getDeclaredMethods();
        for (Method method : methods) {

            boolean isAPI = false;

            RestClassInfo.RestMethodInfo info = new RestClassInfo.RestMethodInfo();

            //TODO: InvokeLog
            InvokeLog needLog = method.getAnnotation(InvokeLog.class);
            if (needLog != null) {
                info.setDesc(StringUtils.isEmpty(needLog.value()) ? needLog.description() : needLog.value());
            } else {
                info.setDesc("");
            }

            //TODO: Mapping
            PostMapping postMapping = method.getAnnotation(PostMapping.class);
            if (postMapping != null) {
                info.setMethodType("POST");
                info.setMapping(postMapping.value().length > 0 ? postMapping.value()[0] : ""); // /login
                isAPI = true;
            }
            GetMapping getMapping = method.getAnnotation(GetMapping.class);
            if (getMapping != null) {
                info.setMethodType("GET");
                info.setMapping(getMapping.value().length > 0 ? getMapping.value()[0] : ""); // /login
                isAPI = true;
            }
            PutMapping putMapping = method.getAnnotation(PutMapping.class);
            if (putMapping != null) {
                info.setMethodType("PUT");
                info.setMapping(putMapping.value().length > 0 ? putMapping.value()[0] : ""); // /login
                isAPI = true;
            }
            DeleteMapping deleteMapping = method.getAnnotation(DeleteMapping.class);
            if (deleteMapping != null) {
                info.setMethodType("DELETE");
                info.setMapping(deleteMapping.value().length > 0 ? deleteMapping.value()[0] : ""); // /login
                isAPI = true;
            }
            RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
            if (requestMapping != null) {
                info.setMethodType("ALL");
                info.setMapping(requestMapping.value().length > 0 ? requestMapping.value()[0] : ""); // /login
                isAPI = true;
            }

            // 名称: ServerResponse listHandler(Integer page, Integer rows);
            info.setName(getPackageLastPart(method.getReturnType().getName()) + " " //ServerResponse
                    + method.getName() //listHandler
                    + getParameter(method) + ";" //(Integer page, Integer rows);
            );

            if (isAPI) list.add(info);
        }
        return list;
    }

    private static String getPackageLastPart(String packageName) {
        return StringUtils.isEmpty(packageName) ? "" : packageName.substring(packageName.lastIndexOf(".") + 1);
    }

    private static String getParameter(Method method) {
        Parameter[] parameters = method.getParameters();
        //Arrays.deepToString(method.getParameters())
        if (parameters.length == 0) {
            return "()";
        }
        StringBuilder str = new StringBuilder("(");
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].getType().isArray()) {
                str.append(getPackageLastPart(parameters[i].toString())); // int[]
            } else {
                str.append(getPackageLastPart(parameters[i].getType().getName()));// java.lang.String
                str.append(" ");
                str.append(parameters[i].getName());
            }
            if (i < parameters.length - 1)
                str.append(", ");
        }
        str.append(")");
        return str.toString();
    }
}
