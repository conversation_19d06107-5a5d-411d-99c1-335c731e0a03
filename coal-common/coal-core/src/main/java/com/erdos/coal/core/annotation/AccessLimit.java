package com.erdos.coal.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AccessLimit {

    /**
     * 资源的key,唯一, 不同的接口，不同的流量控制
     */
    String key();

    /**
     * 最多的访问限制次数
     */
    double perSecond() default Double.MAX_VALUE;

    //获取令牌的等待时间, 默认0, 取值 >= 0
    int timeOut() default 0;

    //超时时间单位
    TimeUnit timeOutUnit() default TimeUnit.SECONDS;

    //说明
    String description() default "服务器繁忙";
}
