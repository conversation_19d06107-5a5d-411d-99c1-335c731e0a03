package com.erdos.coal.core.entity;

import java.io.Serializable;
import java.util.List;

public class RestClassInfo implements Serializable {

    //class
    private String name;
    private String mapping;

    //method
    private List<RestMethodInfo> methods;

    public static class RestMethodInfo implements Serializable {

        private String name; // method name
        private String desc; // description
        private String mapping; // mapping

        private String methodType; // get, post, put, delete

        public String getName() {
            return name;
        }

        public RestMethodInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getDesc() {
            return desc;
        }

        public RestMethodInfo setDesc(String desc) {
            this.desc = desc;
            return this;
        }

        public String getMapping() {
            return mapping;
        }

        public RestMethodInfo setMapping(String mapping) {
            this.mapping = mapping;
            return this;
        }

        public String getMethodType() {
            return methodType;
        }

        public RestMethodInfo setMethodType(String methodType) {
            this.methodType = methodType;
            return this;
        }
    }

    public String getName() {
        return name;
    }

    public RestClassInfo setName(String name) {
        this.name = name;
        return this;
    }

    public String getMapping() {
        return mapping;
    }

    public RestClassInfo setMapping(String mapping) {
        this.mapping = mapping;
        return this;
    }

    public List<RestMethodInfo> getMethods() {
        return methods;
    }

    public RestClassInfo setMethods(List<RestMethodInfo> methods) {
        this.methods = methods;
        return this;
    }
}
