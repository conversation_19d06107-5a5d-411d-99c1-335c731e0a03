package com.erdos.coal.core.exception;

import com.erdos.coal.core.common.ResponseCode;
import com.erdos.coal.core.common.ServerResponse;
import com.erdos.coal.core.enums.ResultCode;

/**
 * @作者: LIGX
 * @时间: 2018/10/3
 * @描述: 自定义异常类
 */
public class GlobalException extends RuntimeException {

    private int code;
    private String msg;

    public GlobalException(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public GlobalException setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public GlobalException(ResponseCode rc) {
        this.code = rc.getCode();
        this.msg = rc.getDesc();
    }

    public GlobalException(ServerResponse sr) {
        this.code = sr.getStatus();
        this.msg = sr.getMsg();
    }

    public GlobalException(ResultCode rc) {
        this.code = rc.getCode();
        this.msg = rc.getMsg();
    }
}
