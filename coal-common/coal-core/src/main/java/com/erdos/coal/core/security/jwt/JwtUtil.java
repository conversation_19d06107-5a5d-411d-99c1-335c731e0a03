package com.erdos.coal.core.security.jwt;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.erdos.coal.core.enums.UserType;
import com.erdos.coal.core.security.config.JWTConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class JwtUtil {

    private static Logger log = LoggerFactory.getLogger(JwtUtil.class);

    // 过期时间
    private static final long EXPIRE_TIME = 24 * 60 * 60 * 1000;

    private static final String UserNameKey = "un";
    private static final String UserTypeKey = "ut";

    /*public static boolean verify(String token, String username, String secret) {
        try {
            // 根据密码生成JWT效验器
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim(UserNameKey, username).build();
            // 效验TOKEN
            DecodedJWT jwt = verifier.verify(token);
            // log.info(jwt + ":-token is valid");
            return true;
        } catch (Exception e) {
            log.info("令牌无效 {}", e.getMessage());
            return false;
        }
    }*/

    public static boolean verify(String token, String secret) {
        try {
            // 根据密码生成JWT效验器
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim(UserNameKey, getUsername(token)).withClaim(UserTypeKey, getUserType(token)).build();
            // 效验TOKEN
            DecodedJWT jwt = verifier.verify(token);
            // log.info(jwt + ":-token is valid");
            return true;
        } catch (Exception e) {
            // log.warn("令牌无效：{}", e.getMessage());
            return false;
        }
    }

    private static DecodedJWT getDecodedJWT(String token) {
        try {
            return JWT.decode(token);
        } catch (JWTDecodeException e) {
            log.warn("令牌：{} 错误：{}", token, e.getMessage());
            return null;
        }
    }

    // 获得token中的信息无需secret解密也能获得
    public static String getUsername(String token) {
        //return Objects.requireNonNull(getDecodedJWT(token)).getClaim(UserNameKey).asString();
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(UserNameKey).asString();
        } catch (JWTDecodeException e) {
            log.warn("错误：getUsername，{}", e.getMessage());
            return null;
        }
    }

    public static String getUserType(String token) {
        //return Objects.requireNonNull(getDecodedJWT(token)).getClaim(UserTypeKey).asString();
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(UserTypeKey).asString();
        } catch (JWTDecodeException e) {
            log.warn("错误：getUserType，{}", e.getMessage());
            return null;
        }
    }

    // 生成签名
    public static String sign(UserType ut, String username, String secret) {
        long exp = EXPIRE_TIME;//默认值
        switch (ut) {
            case WEB:
                exp = JWTConfig.expWeb;
                break;
            case CU:
                exp = JWTConfig.expCU;
                break;
            case DU:
                exp = JWTConfig.expDU;
                break;
            case WECHAT:
                exp = JWTConfig.expWechat;
                break;
            case BIZ:
                exp = JWTConfig.expBiz;
                break;
        }
        Date date = new Date(System.currentTimeMillis() + exp);
        Algorithm algorithm = Algorithm.HMAC256(secret);
        // 附带username信息
        return JWT.create()
                .withClaim(UserNameKey, username)
                .withClaim(UserTypeKey, ut.toString())
                .withExpiresAt(date)
                .sign(algorithm);
    }
}
