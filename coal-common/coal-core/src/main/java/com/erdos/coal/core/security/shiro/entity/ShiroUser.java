package com.erdos.coal.core.security.shiro.entity;

import java.io.Serializable;

public class <PERSON><PERSON><PERSON><PERSON> implements Serializable {

    //private Integer userId;
    private String userId;
    private String username;
    private String password;
    private String userType;

    public String getUserId() {
        return userId;
    }

    public ShiroUser setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public String getUsername() {
        return username;
    }

    public ShiroUser setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getPassword() {
        return password;
    }

    public ShiroUser setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getUserType() {
        return userType;
    }

    public ShiroUser setUserType(String userType) {
        this.userType = userType;
        return this;
    }
}
