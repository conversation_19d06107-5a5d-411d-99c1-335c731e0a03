package com.erdos.coal.core.common;

import java.io.Serializable;
import java.util.List;

/**
 * @作者: LIGX
 * @时间: 2018/11/10
 * @描述: EasyUI DataGrid 结果集
 */
public class EGridResult<T> implements Serializable {

    private long total;
    private List<T> rows;

    public EGridResult() {
        super();
        // TODO Auto-generated constructor stub
    }

    public EGridResult(Integer total, List<T> rows) {
        super();
        this.total = total;
        this.rows = rows;
    }

    public EGridResult(Long total, List<T> rows) {
        this(total.intValue(), rows);
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    @Override
    public String toString() {
        return "EGridResult [total=" + total + ", rows=" + rows + "]";
    }

}
