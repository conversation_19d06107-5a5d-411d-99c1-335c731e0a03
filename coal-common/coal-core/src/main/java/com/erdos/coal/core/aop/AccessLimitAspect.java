package com.erdos.coal.core.aop;

import com.erdos.coal.core.annotation.AccessLimit;
import com.erdos.coal.core.exception.GlobalException;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

@Aspect
@Component
public class AccessLimitAspect {

    private final static Logger logger = LoggerFactory.getLogger(AccessLimitAspect.class);

    /**
     * 不同的接口，不同的流量控制
     * map的key为 Limiter.key
     */
    private final Map<String, RateLimiter> limitMap = Maps.newConcurrentMap();

    @Pointcut("@annotation(com.erdos.coal.core.annotation.AccessLimit)")
    public void checkPointcut() {
    }

    @Around(value = "checkPointcut()")
    public Object aroundNotice(ProceedingJoinPoint pjp) throws Throwable {

        Signature signature = pjp.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method.isAnnotationPresent(AccessLimit.class)) {
            AccessLimit limit = method.getAnnotation(AccessLimit.class);

            // logger.info("方法: {} 说明: {}.", pjp.getSignature().getName(), limit.description());

            //key：不同接口，不同流量控制
            String key = limit.key();
            RateLimiter rateLimiter;
            //验证缓存是否有命中key
            if (!limitMap.containsKey(key)) {
                // 创建令牌桶
                rateLimiter = RateLimiter.create(limit.perSecond());
                limitMap.put(key, rateLimiter);
                logger.info("新建了令牌桶={}，容量={}", key, limit.perSecond());
            }
            rateLimiter = limitMap.get(key);

            //判断能否在1秒内得到令牌，如果不能则立即返回false，不会阻塞程序
            if (!rateLimiter.tryAcquire(limit.timeOut(), limit.timeOutUnit())) {
                logger.info("令牌桶={}，获取令牌失败", key);
                throw new GlobalException(-1, limit.description());
            }
        }
        return pjp.proceed();
    }
}
