package com.erdos.coal.core.enums;

/**
 * @作者: LIGX
 * @时间: 2018/11/10
 * @描述: 返回结果
 */
public enum ResultCode {

    ERR_AUTH_FAIL(403, "未授权"),
    //    ERR_TOKEN_EXPIRE(0, "token is expire"),
    ERR_TOKEN(6401, "验证失败,请重新登录"),
    ERR_FAIL(0, "用户名密码不正确"),
    ERR_USER_NOT_EXISTS(0, "用户不存在"),
    ERR_USER_TYPE(0, "用户类型错误"),

    SUCCESS(200, "SUCCESS"),
    ERROR(0, "ERROR"),
    NEED_LOGIN(1, "NEED_LOGIN");

    private int code;
    private String msg;

    private ResultCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
