package com.erdos.coal.core.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.core.annotation.InvokeLog;
import com.erdos.coal.core.event.LogDTO;
import com.erdos.coal.core.event.LogEvent;
import com.erdos.coal.core.security.utils.ShiroUtils;
import com.erdos.coal.core.utils.Utils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.InputStreamSource;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;

/**
 * Created by LIGX on 2018/12/29.
 * 日志切面类
 */
@Aspect
@Component
public class LogAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    private static final String MSG = "\n" +
            " --请求: {}\n" +
            " --路径：{}\n" +
            " --方法：{}\n" +
            " --描述：{}\n" +
            " --参数：{}\n" +
            " --返回：{}\n" +
            " --耗时：{} ms";

    private int timeLimit = 3000;

    @Autowired
    private ApplicationContext applicationContext;

    @Value("${spring.application.name}")
    private String hostName;

    // 切点
    @Pointcut("@annotation(com.erdos.coal.core.annotation.InvokeLog)")
    public void executePointCut() {
    }

    // 切面强化
    @Around("executePointCut()")
    public Object execute(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (logger.isDebugEnabled() || logger.isWarnEnabled()) {
            StopWatch clock = new StopWatch();
            clock.start();
            Object obj = null;
            try {
                // 注意和finally中的执行顺序 finally是在return中的计算结束返回前执行
                return obj = joinPoint.proceed(args);
            } finally {
                clock.stop();
                long totalTime = clock.getTotalTimeMillis();
                // 打印日志
                handleLog(joinPoint, args, obj, totalTime);
            }
        } else {
            return joinPoint.proceed(args);
        }
    }

    /**
     * 日志处理
     *
     * @param joinPoint 位置
     * @param args      参数
     * @param returnObj 响应
     * @param totalTime 耗时ms
     */
    private void handleLog(ProceedingJoinPoint joinPoint, Object[] args, Object returnObj, long totalTime) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        InvokeLog needLog = method.getAnnotation(InvokeLog.class);
        String name = needLog.name(); //方法名称
        //boolean printReturn = needLog.printReturn(); //是否打印响应
        if (StringUtils.isEmpty(name))
            name = method.getName(); //空则取默认方法名

        //获取request
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String agent = request.getHeader("User-Agent");

        String uid = ShiroUtils.getUserId() == null ? "anonymous" : ShiroUtils.getUserId(); //MDC.get(Constant.CURRENT_USER_KEY), //ID
        String type = ShiroUtils.getUserType() == null ? "匿名用户" : ShiroUtils.getUserType(); //MDC.get(Constant.CURRENT_USER_TYPE_KEY),//类型

        LogDTO logDTO = new LogDTO();

        logDTO.setAgent(agent);

        logDTO.setUid(uid); //用户ID
        logDTO.setType(type); //用户类型
        logDTO.setName(name); //方法名称
        logDTO.setDesc(StringUtils.isEmpty(needLog.value()) ? needLog.description() : needLog.value()); // 描述
        logDTO.setPosition(request.getRequestURI()); // URI
        logDTO.setParameter(getParams(args)); //参数

        //返回值
        if (ObjectUtils.isEmpty(returnObj)) {
            // 返回值为 null, 判断返回类型
            if (method.getReturnType().equals(Void.TYPE)) {
                // void
                logDTO.setReturnObject("无需返回值");
            } else {
                logDTO.setReturnObject("返回类型: " + method.getReturnType().getName() + ", 返回值为: null");
            }
        } else {
            logDTO.setReturnObject(JSON.toJSONString(returnObj));
        }

        logDTO.setTotalTime(totalTime); //耗时

        logDTO.setHost(hostName);//主机
        logDTO.setThreadName(Thread.currentThread().getName());//线程
        logDTO.setIp(Utils.getRealIP(request)); //ip
        //logDTO.setPosition();

        //控制台
        if (totalTime < timeLimit)
            logger.info(MSG,
                    request.getMethod(),
                    request.getRequestURI(),
                    logDTO.getName(), logDTO.getDesc(),
                    logDTO.getParameter(), logDTO.getReturnObject(), totalTime);
        else
            logger.warn(MSG,
                    request.getMethod(),
                    request.getRequestURI(),
                    logDTO.getName(), logDTO.getDesc(),
                    logDTO.getParameter(), logDTO.getReturnObject(), totalTime);

        //TODO: 对增加排除标记的方法不进行日志记录
        if (needLog.exclude())
            return;

        applicationContext.publishEvent(new LogEvent(logDTO));
    }

    private String getPrintMsg(boolean printReturn, Object returnObj) {
        if (printReturn) {
            if (returnObj != null) {
                return JSONObject.toJSONString(returnObj);
            } else {
                return "null";
            }
        } else {
            return "[printReturn = false]";
        }
    }

    private Object[] getParams(Object[] args) {
        if (args == null || args.length == 0) {
            return new Object[]{};
        }
        Object[] params = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg instanceof ServletRequest || arg instanceof ServletResponse
                    || arg instanceof Model || arg instanceof ModelMap
                    || arg instanceof File || arg instanceof InputStreamSource) {
                params[i] = Arrays.toString(args);
            } else {
                params[i] = args[i];
            }
        }
        return params;
    }
}