package com.erdos.coal.core.common;

/**
 * @作者: LIGX
 * @时间: 2018/11/10
 * @描述: 返回结果
 */
public enum ResponseCode {

    ERR_USER_TYPE(-4, "用户类型不正确"),
    ERR_USER_NOT_EXISTS(-3, "用户不存在"),
    ERR_TOKEN_EXPIRE(-2, "令牌已过期"),
    ERR_TOKEN(-1, "令牌无效"), //无token，请重新登录

    SUCCESS(0, "SUCCESS"),
    ERROR(1, "ERROR"),
    NEED_LOGIN(2, "NEED_LOGIN"),

    //登录验证相关
    ERR_FAIL(3, "验证失败,请登陆"),
    ERR_REFRESH_TOKEN(4, "刷新token"),
    ERR_LOGIN_ELSEWHERE(5, "账号已在其它设备登录"),
    ERR_PERMISSION_DENIED(6, "请求无效或未经访问授权"),

    //支付相关
    PAY_BALANCE_LOW(7, "账户金额不足，请充值或选择其它方式支付"),
    PAY_QUERY_FAIL(8, "支付结果查询失败，请稍后重试"),

    NONE(9999, "");

    private int code;
    private String desc;

    private ResponseCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
