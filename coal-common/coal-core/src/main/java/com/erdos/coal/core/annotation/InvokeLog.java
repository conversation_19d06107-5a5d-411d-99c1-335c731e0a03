package com.erdos.coal.core.annotation;

import java.lang.annotation.*;

/**
 * Created by LIGX on 2018/12/31.
 * 日志
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface InvokeLog {
    String name() default "";

    String value() default "";

    String description() default "";

    boolean exclude() default false;

    boolean printReturn() default true;
}