package com.erdos.coal.core.common;

import com.alibaba.fastjson.JSON;

import javax.servlet.ServletResponse;
import java.io.PrintWriter;
import java.io.Serializable;

/**
 * @作者: LIGX
 * @时间: 2018/11/10
 * @描述: 返回结果对象
 */
public class ServerResponse<T> implements Serializable {

    //当前的状态 :如 成功 失败 未登录 无权限等(判断用)
    private Integer status;

    //描述信息
    private String msg;

    //后台返回前端的数据
    private T data;

    public static void responseJson(ServletResponse response, Object obj) {
        PrintWriter out = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json");
            out = response.getWriter();
            out.println(JSON.toJSONString(obj));
        } catch (Exception e) {
            //log.error("【JSON输出异常】" + e);
        } finally {
            if (out != null) {
                out.flush();
                out.close();
            }
        }
    }

    /*
     * 构造函数可能有四种情况
     */
    public ServerResponse() {
        super();
        // TODO Auto-generated constructor stub
    }

    public ServerResponse(Integer status) {
        super();
        this.status = status;
    }

    public ServerResponse(Integer status, T data) {
        this(status);
        this.data = data;
    }

    public ServerResponse(Integer status, String msg) {
        this(status);
        this.msg = msg;
    }

    public ServerResponse(Integer status, String msg, T data) {
        this(status, msg);
        this.data = data;
    }

    /*
     * 描述后台返回给前台的成功状态
     */

    //告诉前台成功的状态
    public static <T> ServerResponse<T> createSuccess() {
        return new ServerResponse<>(ResponseCode.SUCCESS.getCode());
    }

    //告诉前台：status 和 msg
    public static <T> ServerResponse<T> createSuccess(String msg) {
        return new ServerResponse<>(ResponseCode.SUCCESS.getCode(), msg);
    }

    //告诉前台：status 和 msg 及 data
    public static <T> ServerResponse<T> createSuccess(String msg, T data) {
        return new ServerResponse<>(ResponseCode.SUCCESS.getCode(), msg, data);
    }

    //告诉前台：status 和 msg 及 data
    public static <T> ServerResponse<T> createSuccess(T data) {
        return new ServerResponse<>(ResponseCode.SUCCESS.getCode(), data);
    }

    public static <T> ServerResponse<T> createSuccess(ResponseCode rc, T data) {
        return new ServerResponse<>(rc.getCode(), rc.getDesc(), data);
    }

    /*
     * 描述后台返回给前台的失败状态
     */

    //只是告诉前台成功的状态
    public static <T> ServerResponse<T> createError() {
        return new ServerResponse<>(ResponseCode.ERROR.getCode());
    }

    //告诉前台：status和msg
    public static <T> ServerResponse<T> createError(String msg) {
        return new ServerResponse<>(ResponseCode.ERROR.getCode(), msg);
    }

    //告诉前台：status和msg data
    public static <T> ServerResponse<T> createError(String msg, T data) {
        return new ServerResponse<>(ResponseCode.ERROR.getCode(), msg, data);
    }

    //告诉前台：status和msg data
    public static <T> ServerResponse<T> createError(T data) {
        return new ServerResponse<>(ResponseCode.ERROR.getCode(), data);
    }

    public static <T> ServerResponse<T> createError(ResponseCode rc, T data) {
        return new ServerResponse<>(rc.getCode(), rc.getDesc(), data);
    }

    public static <T> ServerResponse<T> result(Integer status, String msg) {
        return new ServerResponse<>(status, msg);
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

//    @Override
//    public String toString() {
//        //{status=0, msg='查询成功'}
//        //{status=0, msg='查询成功', data=...}
//        String res = data == null ? "" : ", data=...";
//        return "{" +
//                "status=" + status +
//                ", msg='" + msg + '\'' +
//                res +
//                '}';
//    }

    @Override
    public String toString() {
        return "{" +
                "status=" + status +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}