//package com.erdos.coal.core.config;
//
//import org.springframework.boot.web.server.ErrorPage;
//import org.springframework.boot.web.server.WebServerFactoryCustomizer;
//import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.HttpStatus;
//
///**
// * Created by LIGX on 2018/6/10.
// * 错误页处理
// */
//@Configuration
//public class ErrorPagesConfig {
//
//    @Bean
//    public WebServerFactoryCustomizer webServerFactoryCustomizer() {
//        return new WebServerFactoryCustomizer<ConfigurableServletWebServerFactory>() {
//            @Override
//            public void customize(ConfigurableServletWebServerFactory factory) {
//                // 对嵌入式servlet容器的配置
//                // factory.setPort(8081);
//                /* 注意：new ErrorPage(stat, path);中path必须是页面名称，并且必须“/”开始。
//                    底层调用了String.java中如下方法：
//                    public boolean startsWith(String prefix) {
//                        return startsWith(prefix, 0);
//                    }*/
//                ErrorPage errorPage400 = new ErrorPage(HttpStatus.BAD_REQUEST, "/error-400");
//                ErrorPage errorPage404 = new ErrorPage(HttpStatus.NOT_FOUND, "/error-404");
//                ErrorPage errorPage500 = new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/error-500");
//                factory.addErrorPages(errorPage400, errorPage404, errorPage500);
//            }
//        };
//    }
//}
