package com.erdos.coal.core.exception;

import com.erdos.coal.core.common.ServerResponse;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * @作者: LIGX
 * @时间: 2018/10/3
 * @描述: 自定义异常处理, RestController 限定返回 JSON 格式数据
 * 处理不同类型异常返回给调用方
 */
@RestControllerAdvice
public class ExceptionHandle {

    private Logger logger = LoggerFactory.getLogger(ExceptionHandle.class);

    @ExceptionHandler(Exception.class)
    public ServerResponse exceptionHandler(Exception ex) {
        logger.error("999-系统错误", ex);
        //ex.printStackTrace();
        return ServerResponse.result(999, "系统错误！");
    }

    // 客户端终止异常
    @ExceptionHandler(ClientAbortException.class)
    public void handleClientAbortException(ClientAbortException e) {
    }

    /**
     * 参数绑定异常捕捉处理
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({
            BindException.class,
            IllegalStateException.class,
            NoHandlerFoundException.class,
            HttpRequestMethodNotSupportedException.class,
            HttpMediaTypeNotSupportedException.class,
            MissingPathVariableException.class,
            MissingServletRequestParameterException.class,
            TypeMismatchException.class,
            HttpMessageNotReadableException.class,
            HttpMessageNotWritableException.class,
            // BindException.class,
            // MethodArgumentNotValidException.class
            HttpMediaTypeNotAcceptableException.class,
            ServletRequestBindingException.class,
            ConversionNotSupportedException.class,
            MissingServletRequestPartException.class,
            AsyncRequestTimeoutException.class
    })
    public ServerResponse paramHandler(Exception ex) {
        String msg;
        if (ex instanceof TypeMismatchException) {
            msg = "参数类型错误！";
        } else if (ex instanceof MissingServletRequestParameterException) {
            msg = "参数未找到！";
        } else {
            msg = "参数错误！";
        }

        //TODO: ...

        return ServerResponse.result(999, msg);
    }

    /**
     * 全局异常捕捉处理
     *
     * @param ge
     * @return
     */
    @ExceptionHandler(GlobalException.class)
    public ServerResponse globalExceptionHandler(GlobalException ge) {
        if (ge == null) ge = new GlobalException(999, "操作异常！");
        return ServerResponse.result(ge.getCode(), ge.getMsg());
    }

    /**
     * 业务异常捕捉处理
     *
     * @param be
     * @return
     */
    @ExceptionHandler(ServiceException.class)
    public ServerResponse serviceExceptionHandler(ServiceException be) {
        if (be == null) be = new ServiceException(999, "操作错误！");
        return ServerResponse.result(be.getCode(), be.getMsg());
    }

    /**
     * 数据访问异常捕捉处理
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(DataAccessException.class)
    public ServerResponse serviceExceptionExceptionHandler(DataAccessException ex) {
        return ServerResponse.result(-1, "资源操作受限！");
    }

    /* spring默认上传大小1MB 超出大小捕获异常MaxUploadSizeExceededException */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ServerResponse maxUploadSizeExceededExceptionHandler(MaxUploadSizeExceededException e) {
        return ServerResponse.result(-2, "文件大小超出限制, 请压缩或降低文件质量! ");
    }
}
