package com.erdos.coal.transaction.wxpay.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import com.squareup.okhttp.HttpUrl;
import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.*;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service("wxPayV3")
public class WXPayV3 {
    @Value("${wx.apiclient_key:''}")
    public String apiclient_key;//微信公钥文件路径

    /**
     * 获取私钥。
     * <p>
     *
     * @return 私钥对象
     */
    private PrivateKey getPrivateKey() throws IOException {
        /*String filename = "static/apiclient_key.pem";
        ClassLoader classLoader = getClass().getClassLoader();
        File file = new File(classLoader.getResource(filename).getFile());
        return PemUtil.loadPrivateKey(new FileInputStream(file));*/
        return PemUtil.loadPrivateKey(new FileInputStream(apiclient_key));
    }

    /**
     * 拼接http头Authorization需要的字符串。
     * <p>
     *
     * @return 认证类型 签名信息
     */
    public String getAuthorization(String method, String url, String body) throws Exception {
        String schema = "WECHATPAY2-SHA256-RSA2048";
        HttpUrl httpurl = HttpUrl.parse(url);

        // Authorization: <schema> <token>
        // GET - getToken("GET", httpurl, "")
        // POST - getToken("POST", httpurl, json)
        // HttpUrl httpurl = HttpUrl.parse(url);
        return schema + " " + getToken(method, httpurl, body);

    }

    /**
     * {"field":"signature",
     * "location":"authorization",
     * "sign_information":{"method":"GET","truncated_sign_message":"GET\n/v3/certificates\n1661326385\nfTyUIUCdkdXMaiscfFhXTqgguqRcEvuV\n\n","sign_message_length":66,"url":"/v3/certificates"},"detail":{"issue":"sign not match"}}
     */
    //生成签名信息
    private String getToken(String method, HttpUrl url, String body) throws Exception {
        String nonceStr = WXPayUtil.generateNonceStr(32); //生成随机数 "your nonce string";
        long timestamp = System.currentTimeMillis() / 1000;
        String message = buildMessage(method, url, timestamp, nonceStr, body);
        String signature = sign(message.getBytes("utf-8"));

        return "mchid=\"" + WXPayConstants.mch_id + "\","
                + "nonce_str=\"" + nonceStr + "\","
                + "timestamp=\"" + timestamp + "\","
                + "serial_no=\"" + WXPayConstants.serial_no + "\","
                + "signature=\"" + signature + "\"";
    }

    //计算签名值
    private String sign(byte[] message) throws Exception {
        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(getPrivateKey()); //商户的私钥，加密
        sign.update(message);

        return Base64.getEncoder().encodeToString(sign.sign());
    }

    //构造签名串
    private String buildMessage(String method, HttpUrl url, long timestamp, String nonceStr, String body) {
        String canonicalUrl = url.encodedPath();
        if (url.encodedQuery() != null) {
            canonicalUrl += "?" + url.encodedQuery();
        }

        return method + "\n"
                + canonicalUrl + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";
    }

    public String execute(String httpMethod, String url, String jsonParam, String wechatpaySerial) {
        try {
            HttpPost httpPost = new HttpPost(new URIBuilder(url).build());

            httpPost.addHeader("Authorization", getAuthorization(httpMethod, url, jsonParam));
            httpPost.addHeader("Accept", ContentType.APPLICATION_JSON.toString());//"application/json");
//            httpPost.addHeader("User-Agent", "WechatPay-Apache-HttpClient/0.4.8 (Mac OS X/10.14.5) Java/1.8.0_261, Accept-Encoding: gzip,deflate");//"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            if (!StringUtils.isEmpty(wechatpaySerial))
                httpPost.addHeader("Wechatpay-Serial", wechatpaySerial);

            StringEntity entity = new StringEntity(jsonParam, "UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            httpPost.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 微信支付平台证书
     * 请求微信接口，获取加密后的证书字符串
     */
    public Map<String, String> getCertificates() {
        try {
            String url = "https://api.mch.weixin.qq.com/v3/certificates";
            HttpGet httpGet = new HttpGet(new URIBuilder(url).build());

            httpGet.addHeader("Authorization", getAuthorization("GET", url, ""));
            httpGet.addHeader("Accept", "application/json");

            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");

            JSONObject object = JSON.parseObject(responseEntity);
            JSONArray data = object.getJSONArray("data");
            JSONObject WXCertificate = data.getJSONObject(0);
            String wechatpay_serial = WXCertificate.getString("serial_no");

            JSONObject encrypt_certificate = WXCertificate.getJSONObject("encrypt_certificate");
            String associated_data = encrypt_certificate.getString("associated_data");
            String nonce = encrypt_certificate.getString("nonce");
            String ciphertext = encrypt_certificate.getString("ciphertext");

            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("serialNo", wechatpay_serial);
            resultMap.put("cipherText", ciphertext);
            resultMap.put("associatedData", associated_data);
            resultMap.put("nonce", nonce);
            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 获取证书
     * GET 获取平台证书列表得到的证书字符串，解密后，得到微信支付平台证书
     */
    private X509Certificate getCertificate(String associatedData, String nonce, String cipherText) throws IOException {

        InputStream io;
        try {
            byte[] apiv3Key = WXPayConstants.v3_key.getBytes();
            AesUtil aesUtil = new AesUtil(apiv3Key);
            String decryptStr = aesUtil.decryptToString(associatedData.getBytes(), nonce.getBytes(), cipherText);

            io = new ByteArrayInputStream(decryptStr.getBytes(StandardCharsets.UTF_8));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException("证书解密失败", e);
        }


        BufferedInputStream bis = new BufferedInputStream(io);

        try {

            CertificateFactory cf = CertificateFactory.getInstance("X509");
            X509Certificate cert = (X509Certificate) cf.generateCertificate(bis);
            cert.checkValidity();
            return cert;
        } catch (CertificateExpiredException e) {
            throw new RuntimeException("证书已过期", e);
        } catch (CertificateNotYetValidException e) {
            throw new RuntimeException("证书尚未生效", e);
        } catch (CertificateException e) {
            throw new RuntimeException("无效的证书文件", e);
        } finally {
            bis.close();
        }
    }

    /**
     * 敏感信息加密
     */
    private String rsaEncryptOAEP(String message, X509Certificate certificate) throws IllegalBlockSizeException {
        try {

            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, certificate.getPublicKey());

            byte[] data = message.getBytes("utf-8");
            byte[] cipherdata = cipher.doFinal(data);
            return Base64.getEncoder().encodeToString(cipherdata);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            throw new RuntimeException("当前Java环境不支持RSA v1.5/OAEP", e);
        } catch (InvalidKeyException e) {
            throw new IllegalArgumentException("无效的证书", e);
        } catch (IllegalBlockSizeException | BadPaddingException e) {
            throw new IllegalBlockSizeException("加密原串的长度不能超过214字节");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public String rsaEncryptOAEP(String message, Map<String, String> map) {

        String associatedData = map.get("associatedData");
        String nonce = map.get("nonce");
        String cipherText = map.get("cipherText");

        try {
            X509Certificate cert = getCertificate(associatedData, nonce, cipherText);

            return rsaEncryptOAEP(message, cert);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return null;
    }
}
