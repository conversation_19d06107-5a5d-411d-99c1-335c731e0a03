package com.erdos.coal.transaction.alipay.bean;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
public class AlipayConfig {
    @Value("${alic.cert-path:''}")
    public String alipay_cert_path;//支付宝公钥证书文件路径
    @Value("${alic.alipay-cert-path:''}")
    public String cert_path;// 开发者应用公钥证书路径
    @Value("${alic.alipay-root-cert-path:''}")
    public String alipay_root_cert_path;//支付宝CA根证书文件路径

    public void setAlipay_cert_path(String alipay_cert_path) {
        this.alipay_cert_path = alipay_cert_path;
    }

    public void setCert_path(String cert_path) {
        this.cert_path = cert_path;
    }

    public void setAlipay_root_cert_path(String alipay_root_cert_path) {
        this.alipay_root_cert_path = alipay_root_cert_path;
    }

    public String app_id = "2019053165392894";
    //    public String public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqSzII5qxpH2/oMueO7XfNsGYMDQD63ZHW9K6zNzDmXEWRCINv4hLOa5rKL/twZVoNoxgfo0WGmEpOju/ku6X3bXQAyZfILJdQe4rq0NkG6XYhB99F3FLeH2baEU9CGXD09woDNR6vCSTYDEXDKEhxVzqqVxsi1fp0Hq6iFz8XH1A5WIdqUEfzzZaVe9bsVTNL7JNVfO6+pyw2LcRYgj07DbQLsXy5Q8QOZaxaDrn32DaEEys2kPzIiLVRhym8Z1BeZHmrtYEy6up3dptL3ldR9Yc9g9W/ATtNk486dNBVo7yilm24mYEP+6/XfRB+Hs2IvgNdVRG2cTcZF6H72VcRQIDAQAB";
    public String public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvEJCBuM1UhgDkCgeo0isxI1n4uj+B9E3wsBOnay+a//Rr7TIuKsArC/Rs5AtJmnM/zXbl2S8fXYK0wGSQGYTHzFicuTYvDG4bkCXrAcuccfleIt62I+iPcVwfTxQs4Pw1w78hgl0JVlwyRhpXnGYZDFXiGm/f9jW9xyxybqY7uRNNJMWJt8T3eBODyVdt83B1l1emQozhLCnuKsrgfnGGrDEztTQHK9mPFVc/fFYc0+tBjVAWUBNf7WCxqB2tEGRP6KQDhUz0WFC3LJ/zZgC5K4OrTtgrcFEUNgrsDj4ueQmp0MaqMnkq9AZyWtE8wOJzsQUAPEdtXO9HqAjx/QCywIDAQAB";
    //    public String private_key = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCpLMgjmrGkfb+gy547td82wZgwNAPrdkdb0rrM3MOZcRZEIg2/iEs5rmsov+3BlWg2jGB+jRYaYSk6O7+S7pfdtdADJl8gsl1B7iurQ2QbpdiEH30XcUt4fZtoRT0IZcPT3CgM1Hq8JJNgMRcMoSHFXOqpXGyLV+nQerqIXPxcfUDlYh2pQR/PNlpV71uxVM0vsk1V87r6nLDYtxFiCPTsNtAuxfLlDxA5lrFoOuffYNoQTKzaQ/MiItVGHKbxnUF5keau1gTLq6nd2m0veV1H1hz2D1b8BO02Tjzp00FWjvKKWbbiZgQ/7r9d9EH4ezYi+A11VEbZxNxkXofvZVxFAgMBAAECggEAEy/yDx+3cCmW91zAuJL7GxG/+g+cNjN3CnIPH7cWNCwAU3/tQUDrD0K6AqgqBh+07yLiuteznxBQBbvLEZYgAVRioEjU1zeDnjuuFkGRdBVORkXwAR2naqYMXzojHvWzzS6WGEZtGTOox2sp6xPHacQNFWJXKUKyUSPqThcFxFN89/6cdWtPwwAwXQOibAc6PQji8EAo+QYZvQf71MjxRoW5nYjPKfq06kFo94iPopldcXBEGZ4olklcHMiB1xAfzi54o+mZ+1DjoEgqy217QjCxsQ4Mzr9G3hGMx6X1g479PKr+4eQV4nO1hkc01RLCWbWrAIMyYFRDPAcuMXlA7QKBgQDm0tQsMsp+gtF15Ia4bfwtjTUyDBpU7FPNGnGzCXE/djCXoacw5UrP4zaMLzlw8YoEMXQ/Gh+hA5eAxW9e/BEJMJ2a2CwVN8PFE0Q4TohEUg474MmUGW7hc5kcEK6JDoqk9HlyLT+OQOWuL5eAhSGiFu8/NSvwD1NP7v0J9IiJ/wKBgQC7oJG3U0XJV7FFHKZFqf4RqOzD6CvO7B3hiNP+VR8ymoggrLjtF5HeJbvR18uGLlAQ+l+xxon7y3lxA86QZ0XuKEtwKRt1AUt/xR4kf6u+fq928c6icttJH1+4R0t13HmDSnE2LWtZxUHlRO4yPDlTse+jpBBoBxJ42gDO/0FxuwKBgQCB3xm2DJXWfLBxzrTmTCvZTVwi3qBqK9YyzrAudh+b/1nz1uaFIl3nfgYQ9fkxzRVEqranUoF9TNIPiAbTwKutKgdjOkIDZi7mUaLOs5EEL88GbcG9bFnwy+PX4KLzRpVHnq4+Sas61NTmHqz1iPKViqXuQHKKTXUowmTpFwdD7QKBgQCghWZFRowNDuZNr44dSathZnlxvf9E38NMwihEjoTLYWQXOE5aIXEdcoD2rb2lBZhgfiRCzw4cCt/SLaSa7I/qijTIN5MTDlGOdZEtPsErj+0EQSo81zD+pUXaPby0kDSxe43SARe3zMhsowV6mwWcsj3IbcPPjEVkeXZ5oe5WowKBgAVeBKVYqIYIlqsK2UhLdKt/IZBjvEilnnwDsCA8IL5d+G5tVaITnfjgJ98+AEQyK6HRBeeyn+8yYpcP54ZkGDcsZYicWDZR+qGTUaDavidHOPreFyw9PSzCu4AyY6Jaf6SPEAISBiPwcKdqwhQPu1pyPxkK/hr3n3vDoam2RzE4";
    public String private_key = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC8QkIG4zVSGAOQKB6jSKzEjWfi6P4H0TfCwE6drL5r/9GvtMi4qwCsL9GzkC0macz/NduXZLx9dgrTAZJAZhMfMWJy5Ni8MbhuQJesBy5xx+V4i3rYj6I9xXB9PFCzg/DXDvyGCXQlWXDJGGlecZhkMVeIab9/2Nb3HLHJupju5E00kxYm3xPd4E4PJV23zcHWXV6ZCjOEsKe4qyuB+cYasMTO1NAcr2Y8VVz98VhzT60GNUBZQE1/tYLGoHa0QZE/opAOFTPRYULcsn/NmALkrg6tO2CtwURQ2CuwOPi55CanQxqoyeSr0BnJa0TzA4nOxBQA8R21c70eoCPH9ALLAgMBAAECggEBAJ4CwuaDB/sKmtsBQY3tbs1enqaOxsEK8tFslc+z8PQ2+OmQRLTzGpJrZIhhZ/QI833114T/kVGApV8rv3R7+GrDCY3D1MJIvTMU1fkW4f2wOUNvVsGz9QoB0QPcfyPdh2UKeBuMjcbXhlnTOoWBtNXLsFuX3prRBsTMedEG5FwFIUJ+rJ3awuUOPeR1Zze1x5k21Y+eCRDrGCBAHqn8+lMmVr9HLJTEIoMkspFKTY2l5wl9OMm/mExjvx0GahvIa47ZUgsah++FLA7usIh2HLQpaqPrIM2mgs/zISuPNP28xM4SqjdAO59mMGRUzHqWfFhNiCpP8Zt5XQRm6hNyVIkCgYEA/HjZY+en5yGlAUcNtEjGayDVsayABp85gYdcQcknJ7pva0EICynsOOLH2MB1g5xtCKn3ydVjpagoL4HhT5xEGy4avdcC5a7WaFDRVq4H/69c8e46JD6hHgQwsadvZBS2fLhWsNrPyQruhydx3gY7gD92KizrLWIMMQ655Igb9yUCgYEAvuO0AQRszt2rbrlqyaEU+l67U3tyJzRsdEZoe3D+yuLEhG68MB16S4GSN+kmaKiSW6/gBQO90X2ENxNx5X7NgVpiVFQQHh/gC9hh5DdWKvOmJUVFv4qoKyWOfmRR0pNdOYSOIdYVA1T1GgvekEc1Nvlaw/NOevXg92p6x8kPJy8CgYEAvVveoMHk+sQPQDAMpUVYHI1Z+ZLJyy2tMetWVLkMNw3r92OlJspgtCHThkyDVx0B4leo06bKwDqpOUiOtukbY89mmzAiqiZCBadn+z04HptmeMYWsYyPxzWZWAWe1E98t3qC+s6CveUHFjONgdrm0b/HXsal9TnurvS+lBtJ7kkCgYAYiVxR5Se3T3cBvIOldzcjF5osmOmLcImgSIyVkWiu24c3HqTB6ogVvIn29dkiCskFUA4Tlu+HRgMAsVcqkKoJvnx1KnkH5kD+RMoQc6x3yOnKUKUugbwsD5/JT0vVLWwGQKukXchFrV30AyKQmRaWAmdQWK310DUimGU/2PBBsQKBgQC1TMMge9yvFX5ch3chcQ4fOG5HBx36grhB3IIEAfhj4UiivtpXRagvwFqCHFSRDS8Zu1kiMYfobTQkysfECipta8N6cSx4rJJFozOEZrkoRNIJfzQG7OrpwMQpT3+QhVE1TbFgQBNYUT6xKxRxFQLCjriJgdtCrTm1lP4hSiXgOQ==";

    // 1.商户appid
    //@Value("${spring.coal.pay.AliPay.APP_ID:''}")
    public String APP_ID_CUSTOMER = "2019061765624245";
    public String APP_ID_DRIVER = "2019061665619113";

    // 2.私钥 pkcs8格式的
    //@Value("${spring.coal.pay.AliPay.RSA_PRIVATE_KEY:''}")
    public String RSA_PRIVATE_KEY_CUSTOMER = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCD7y35zAmGhg68UwLXbphzqGfod3UAcTTs7dr+nbLolGTSVDUufVz1yNpZAHl1dBmEUr+ROD+qr7DisOP0d3Qb2MYsYP2hdNfPswooK67HWciVKOY1x1RYJkA7WktowsXia2iFjF3383UoX4D5MmHhVTZ99PU2gUCoB44yCvRbqpnkRJfbIqZ8lJxHi6mUPcbRYS5aXQETEEsqkEbtmeFiKz7I+uaYXBcRVavFPHiWnWnIYPl0awSGaWK1znKYx+BFW9I3AjLAXIP/K/HhULRJAJMLeEbkxdN1tRHuUZtu/cNx7tL0W/1dRElNOgcbBROed/Xe5+bYYASZLvDrYJCTAgMBAAECggEAe8d0TP3emfU/8fO3oMLECQ6gnfslLEsJqpfldCgj6A0BIkuasYkwI/BS2lmVmlpYbkrsK6SoJs06mRP4ai2AI3gXRR+iFU54AwcoqlOBYMqdY0PB6Aag3SrV7Flkkl7aJPk8UL9GkhA5hK1FZYadSjkWLMobHQ3ayUp9X/njE/P8IF2yvNQJKfAURJTG8mkD3CMjljFseAkUNIamDayQ49WzyyWNi6jiBEtSgQw6A+o9/R4X6iSDfNo4B/yjOtkpB3q3oxL1IMQZVw3XKBTqYfnlDLysY/0GoaRXnoWrssMjIjBabaVM97uay5kXum1Vg9GRy36l0s4sbG1FJrTA4QKBgQC4iHY29oGjz3Tr+WWBjaKkMijLcD4ULGKQIgDhYj3p7QO6uly63z+O0kgdxMiXSaahX0OBCH/zwMjvNIgUGNXIs45xMU5fEv18/r3jkBY9gV0Dchaw8why55m4PZ2kMsgA8O8ynr8vsLTXYImKUVQBoOBKK/YAbq3qgQO96hMR2wKBgQC3B8+7MKCNcGwhbzpvTyS9SwMxq70BGLfScUt6ejPtdqR3K+pRuRFquMogeVFEqtWVRsx+Ts4OMXbDFhGrkbjvdIq5aC9AEn2Jkvk+FPTfWy1jBYSfF9BNVVhkHLd8uaEYtXYeTGdkDqJv2ryUyd7WFKVLohbKvGJBr/CR5HmFqQKBgQCoKOLhT13wMzsvVwQjPTi7PGJkkZJgu9nN0s7OlMIrDCvb27xpJsw6kHHbftn0088fOzq+uUowjLwPD+X5unAHB2mEyRPMG/gYEeRlHYbG4b4rR6pZ8f7/BwLUHIItgYE1Klov3O4IvZTuuDBedaKCIWKC9PCmkDW4f30zcb2TBQKBgCRKFjPlJlAk1Cuk5A8hwAUQFGgKxwSqo/fDotV72UPh3CYMvP7LFNr2HI1nwGc+/+4DBZSdh1llEUkI3SQJVoOTtdWhBI1NnTpju6tFeG+4oj0Z4PuyOwi6PTZMQ00leP1vLSZAu9co16gZ1H4jsFz95vAiCSbVRx4DBk3G4RlRAoGAf83hcGjuOUBEI+qVgQwY1fXMoTMrELwxdj+WWwJg1u91AfOecqOvfMliNlW6GzD1A9LvJN9/cdKqZz05TtOupkeqWhhUEUnYaacOQHVJnjY+VyaY6Rmx57qHYRxakOYHNySLT2vREAoVOp2K0Yu+ETjayyUHuk5EZP+Pl/hZZR4=";
    public String RSA_PRIVATE_KEY_DRIVER = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCUbkHUGf/HmK3dxGMkIupONfycdIFkhwpoVIMCe//apeVD4EAYi7fR+IAUWHC9JLt9T68Zuixmdkdize+GFYRNVE/9TI2N7/fylLpS83daf9PG+qku1t9TzUQPd8vMtdyTSULrNW0t8vt+2RDYiiLdY1l9bB2KNAwYoOoIGJMDsztDyWQRaL6Cl3uJCnSfYruCC8bm+ir73Wy807ABGUh6mm9C54D+B/DOnOs6zI/h4jw3ioyPvoCLEfPmGFEWpm9aVAnLJH/OfvTpdlhVCkNfXlQeofzVUhyDwfD+nF5uZYsBVTqEF9IPjq7oZ59raVtgDh9E5E0ivS2V2w0Vi+FtAgMBAAECggEAQJ7P4ft/s/HwoAAD+ZOJ1osCc0r1mkKAcXN5pfc+KH7ZFXfn78fEO5GokTL/whjf12p9nQHEmscbxRRLF2D+vHvcZzaQ2h6DIAVIKRz99VR5asNuLIo3qFBRNTq+a10xrp3SmHwmED8c5tix0NQy4ZeDOmt7YNPQAFjuCqBIQO944Rz1umVv3n6iV/LfoxwmW9Fzze6OQf1z8fxGK/zH7iv7cjhp+HRigjiK01vbmQWad+4lcD6zL6J/5DVRbWg8ZrJOdVLNdqqRrbftf17f+uoopWSN3ORY7BjrdncrzNS6mZBxIQwVm7iOkWJMAWMq0HjB803Bo2Pgd8zJ0uWugQKBgQDQID9E2mYN/R1J+YNsQf3JZ+g5z18DN+Cz+QtBrsrJ0CYkYyKtV8f9VToYu//+kl9ASXRJCktD1tXvNt9jZZXYzCIRZ/CaHXs8HNHiRiXpaHby6mldT+xCKkzBOFwX0DGmI2rVaryY06gdfRCJYmC6RO4m/5MZD2lZpz66NwneiQKBgQC2ksfTkMcXtFOa1lzLXudRtkvANFUY5wSZniICgcxAn0sc2A3J2AHoSgU2hTMBdRz0alS8MkHJVYrxkSLXfMty2t3oGNQMFIABFAxVHi121ug1h8wQYXPelJZRDK239QPFcWTu+ScNYPSCL0oLs7IiPV4Jdhf8sAsd8G4C+14SxQKBgQDLRgOlcw3kMchOfPWgYZ/G+8Zz2Bc7ZHMCNIaLA3YqwDLQrUasOSUqR/hzOgNgl4OSnWuG/xfdzVGrz1NFmG1PjqjjiToUvCCl6JwRvWO/4Xjz7oE0UyA0y22l9i70TzJ+yu7IPPSw62CyVHk3Ra+lnjdgPZRlEIlbuB5q7+CiyQKBgBWPMa+nCgufjLvdEmolk9VPXJSe2WmuZhmp1cRMBY2gAeOvE7nBG0CN9R2iDwreBIRMRiNPH/uhcV3jUKT3pKK0e73bqLageRknf+lRPfcpm5QurAdHKsKe8z0w7RarBT38jPm3TEhFKbpx0SGen6txabheTKjXNQjdCnCptEstAoGAY1zSsC6woBdiVnHgMkaYEYHXUSyE7RfmssY07X8lWwD1n8Dwhd01uhDkSCo6GijPKZIr5T11AHuk/27SAZCpQigT5astGpGGCZfJGurCjv8KdrLa2+CqS/o2+EceQDZ3ZDASAJb2uXk76oUrGG/XzODRwI6FFxiIeQxbm6v0OrY=";

    // 3.支付宝公钥
    //@Value("${spring.coal.pay.AliPay.ALIPAY_PUBLIC_KEY:''}")
    public String ALIPAY_PUBLIC_KEY_CUSTOMER = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0UBFzPZ759hjidpcir/dhQw1L5rh45lzFV9J0WDebpd4bx4l95dvb5o13Jgmd4DkUNXWaVIgpJ7kZ1g7LBQ31l7AK4hGlxQJ3N9YsiT8qFsrFM24A7HxnjqCyKMR3RMJ0pg795NK5hGtvcWUnt71GDPWpEIIiV+90buTUhLDvd3v9kstmZHGAwJKR/y++FgX/ou96aczEV+q5D0vfbIoiXYJAF1ycvHREvRv6UaIMhrzOY2JHo2+K/PGXHDbzYKQVLkedCqXSpB3hPjQgtxQz0LPQvq3+VRTqq7NlTFtGbFChdH5+UvGmq4qs6tD7vdOStZ9LnXTHuXM++DV759JcQIDAQAB";
    public String ALIPAY_PUBLIC_KEY_DRIVER = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0UBFzPZ759hjidpcir/dhQw1L5rh45lzFV9J0WDebpd4bx4l95dvb5o13Jgmd4DkUNXWaVIgpJ7kZ1g7LBQ31l7AK4hGlxQJ3N9YsiT8qFsrFM24A7HxnjqCyKMR3RMJ0pg795NK5hGtvcWUnt71GDPWpEIIiV+90buTUhLDvd3v9kstmZHGAwJKR/y++FgX/ou96aczEV+q5D0vfbIoiXYJAF1ycvHREvRv6UaIMhrzOY2JHo2+K/PGXHDbzYKQVLkedCqXSpB3hPjQgtxQz0LPQvq3+VRTqq7NlTFtGbFChdH5+UvGmq4qs6tD7vdOStZ9LnXTHuXM++DV759JcQIDAQAB";

    // 4.服务器异步通知页面路径 需http://或者https://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
    //@Value("${spring.coal.pay.AliPay.NOTIFY_URL:''}")
    public String NOTIFY_URL = "https://heijinyun.net/api/manage/alipay/notify";
    //"http://*************:7070/api/manage/alipay/notify";

    // 5.页面跳转同步通知页面路径 需http://或者https://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问 商户可以自定义同步跳转地址
    //public String return_url;

    // 6.请求网关地址
    //@Value("${spring.coal.pay.AliPay.URL:''}")
    public String URL = "https://openapi.alipay.com/gateway.do";
    //https://openapi.alipay.com/gateway.do

    // 7.编码
    //@Value("${spring.coal.pay.AliPay.CHARSET:''}")
    public String CHARSET = "UTF-8";

    // 8.返回格式
    //@Value("${spring.coal.pay.AliPay.FORMAT:''}")
    public String FORMAT = "json";

    // 9.加密类型
    //@Value("${spring.coal.pay.AliPay.SIGN_TYPE:''}")
    public String SIGN_TYPE = "RSA2";

    /*
    该笔订单允许的最晚付款时间，逾期将关闭交易。
    取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。
    该参数数值不接受小数点， 如 1.5h，可转换为 90m。
    */
    //10.该笔订单允许的最晚付款时间
    //@Value("${spring.coal.pay.AliPay.TIME_OUT_EXPRESS:''}")
    public String TIME_OUT_EXPRESS = "30m";

    //11.销售产品码，商家和支付宝签约的产品码，为固定值
    //@Value("${spring.coal.pay.AliPay.PRODUCT_CODE:''}")
    public String PRODUCT_CODE = "QUICK_MSECURITY_PAY";

    //12.卖家支付宝用户ID,即为商户签约账号对应的支付宝用户ID
    //@Value("${spring.coal.pay.AliPay.SELLER_ID:''}")
    public String SELLER_ID = "2088531341851582";

    //13.应用配置的授权回调地址
    public String REDIRECT_URI_CUS = "";
    public String REDIRECT_URI_DRI = "";

}