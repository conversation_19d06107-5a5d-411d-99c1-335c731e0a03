package com.erdos.coal.transaction.alipay.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.Participant;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.erdos.coal.transaction.alipay.bean.AlipayConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service("aliPayService")
public class AliPayService {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    private static AlipayClient alipayClient_cus;
    private static AlipayClient alipayClient_dri;
    private static DefaultAlipayClient defaultAlipayClient;

    @Resource
    private AlipayConfig alipayConfig;

    private void createClient(int cusOrDri) {
        switch (cusOrDri) {
            case 0:
                alipayClient_cus = new DefaultAlipayClient(alipayConfig.URL, alipayConfig.APP_ID_CUSTOMER, alipayConfig.RSA_PRIVATE_KEY_CUSTOMER, alipayConfig.FORMAT,
                        alipayConfig.CHARSET, alipayConfig.ALIPAY_PUBLIC_KEY_CUSTOMER, alipayConfig.SIGN_TYPE);
                break;
            case 1:
                alipayClient_dri = new DefaultAlipayClient(alipayConfig.URL, alipayConfig.APP_ID_DRIVER, alipayConfig.RSA_PRIVATE_KEY_DRIVER, alipayConfig.FORMAT,
                        alipayConfig.CHARSET, alipayConfig.ALIPAY_PUBLIC_KEY_DRIVER, alipayConfig.SIGN_TYPE);
                break;
            default:
                break;
        }
    }

    private void createDefaultClient() throws AlipayApiException {
        CertAlipayRequest certAlipayRequest = new CertAlipayRequest();
        certAlipayRequest.setServerUrl(alipayConfig.URL);        //  https://openapi.alipay.com/gateway.do
        certAlipayRequest.setFormat("json");
        certAlipayRequest.setCharset(alipayConfig.CHARSET);
        certAlipayRequest.setSignType(alipayConfig.SIGN_TYPE);
        certAlipayRequest.setAppId(alipayConfig.app_id);
        certAlipayRequest.setPrivateKey(alipayConfig.private_key);
        certAlipayRequest.setCertPath(alipayConfig.cert_path);   //开发者应用公钥证书路径
        certAlipayRequest.setAlipayPublicCertPath(alipayConfig.alipay_cert_path);    //支付宝公钥证书文件路径
        certAlipayRequest.setRootCertPath(alipayConfig.alipay_root_cert_path);       //支付宝CA根证书文件路径

        defaultAlipayClient = new DefaultAlipayClient(certAlipayRequest);
    }

    //第1个创建订单接口
    public AlipayTradeAppPayResponse preOrder(String body, String subject, String outTradeNo, String totalAmount, Integer cusOrDri) {

        //实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
        AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
        //SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
        model.setBody(body);//("我是测试数据");
        model.setSubject(subject);//("App支付测试Java");
        model.setOutTradeNo(outTradeNo);
        model.setTimeoutExpress(alipayConfig.TIME_OUT_EXPRESS);//("30m");
        model.setTotalAmount(totalAmount);//("0.01");
        model.setProductCode(alipayConfig.PRODUCT_CODE);//("QUICK_MSECURITY_PAY");
        request.setBizModel(model);
        request.setNotifyUrl(alipayConfig.NOTIFY_URL);//("商户外网可以访问的异步地址");
        //request.setReturnUrl(AlipayConfig.return_url);

        AlipayTradeAppPayResponse response = null;

        switch (cusOrDri) {
            case 0:
                if (alipayClient_cus == null) createClient(0);
                try {
                    //这里和普通的接口调用不同，使用的是sdkExecute
                    response = alipayClient_cus.sdkExecute(request);

                    logger.info(response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。
                } catch (AlipayApiException e) {
                    e.printStackTrace();
                }
                break;
            case 1:
                if (alipayClient_dri == null) createClient(1);
                try {
                    response = alipayClient_dri.sdkExecute(request);
                    logger.info(response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。
                } catch (AlipayApiException e) {
                    e.printStackTrace();
                }
                break;
            default:
                logger.error("创建支付宝app可支付字符串方法（preOrder） 调用失败，原因：请求者既不是cus也不是dri");
                break;
        }

        return response;
    }

    //订单号查询支付结果
    public AlipayTradeQueryResponse searchOrder(String outTradeNo, String tradeNo, Integer cusOrDri) {
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        Map<String, Object> map = new HashMap<>();
        map.put("out_trade_no", outTradeNo);
        if (!StringUtils.isEmpty(tradeNo))
            map.put("trade_no", tradeNo);
        request.setBizContent(JSONObject.toJSONString(map));

        /*request.setBizContent("{" +
                "\"out_trade_no\":\"20150320010101001\"," +
                "\"trade_no\":\"2014112611001004680 073956707\"" +
                "  }");*/
        AlipayTradeQueryResponse response = null;

        switch (cusOrDri) {
            case 0:
                if (alipayClient_cus == null) createClient(0);
                try {
                    response = alipayClient_cus.execute(request);
                    //response.isSuccess();
                } catch (AlipayApiException e) {
                    logger.error("aliPay订单号查询支付结果,调用异常,订单号：" + outTradeNo);
                    e.printStackTrace();
                }
                break;
            case 1:
                if (alipayClient_dri == null) createClient(1);
                try {
                    response = alipayClient_dri.execute(request);
                } catch (AlipayApiException e) {
                    logger.error("aliPay订单号查询支付结果,调用异常,订单号：" + outTradeNo);
                    e.printStackTrace();
                }
                break;
            default:
                logger.warn("aliPay订单号查询支付结果,调用异常，原因：请求者既不是cus也不是dri");
                break;
        }

        return response;
    }

    public AlipaySystemOauthTokenResponse getOauthToken(Integer cusOrDri, String authCode) {
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);//("2e4248c2f50b4653bf18ecee3466UC18");
        request.setGrantType("authorization_code"); //值为authorization_code时，代表用code换取；值为refresh_token时，代表用refresh_token换取
        AlipaySystemOauthTokenResponse oauthTokenResponse = null;

        switch (cusOrDri) {
            case 0:
                if (alipayClient_cus == null) createClient(0);
                try {
                    oauthTokenResponse = alipayClient_cus.execute(request);
                    logger.info("accessToken:" + oauthTokenResponse.getAccessToken() + ",userId:" + oauthTokenResponse.getUserId());
                } catch (AlipayApiException e) {
                    //处理异常
                    e.printStackTrace();
                }
                break;
            case 1:
                if (alipayClient_dri == null) createClient(1);
                try {
                    oauthTokenResponse = alipayClient_dri.execute(request);
                    logger.info("accessToken:" + oauthTokenResponse.getAccessToken() + ",userId:" + oauthTokenResponse.getUserId());
                } catch (AlipayApiException e) {
                    //处理异常
                    e.printStackTrace();
                }
                break;
            default:
                logger.warn("aliPay生成授权字符串方法（getOauthToken）失败，原因：请求者既不是cus也不是dri");
                break;
        }

        return oauthTokenResponse;
    }

    //1.2:调用B2C转账接口（统一转账接口）alipay.fund.trans.uni.transfer
    public AlipayFundTransUniTransferResponse transfer(String out_biz_no, String trans_amount, String order_title, Participant payee_info, String remark) {
        AlipayFundTransUniTransferResponse response = null;

        Map<String, Object> requestParamMap = new HashMap<>();
        requestParamMap.put("out_biz_no", out_biz_no);
        requestParamMap.put("trans_amount", trans_amount);
        requestParamMap.put("product_code", "TRANS_ACCOUNT_NO_PWD"); //销售产品码，单笔无密转账固定为TRANS_ACCOUNT_NO_PWD
        requestParamMap.put("biz_scene", "DIRECT_TRANSFER");     //业务场景，单笔无密转账固定为DIRECT_TRANSFER
        requestParamMap.put("order_title", order_title);    //转账备注
        requestParamMap.put("payee_info", payee_info);      //理由
        requestParamMap.put("remark", remark);
        //requestParamMap.put("business_params",);    //转账业务请求的扩展参数
        String bizContent = JSON.toJSONString(requestParamMap);
        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        request.setBizContent(bizContent);

        try {
            if (defaultAlipayClient == null) createDefaultClient();
            response = defaultAlipayClient.certificateExecute(request);
            response.isSuccess();
            if (!response.isSuccess())
                logger.info("企业统一转账alipay.fund.trans.uni.transfer接口，调用失败; code:" + response.getSubCode() + "; msg:" + response.getSubMsg());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }

    //3：若调单或打款结果未知,调用alipay.fund.trans.common.query发起查询
    //单据查询接口 alipay.fund.trans.common.query
    public AlipayFundTransCommonQueryResponse transferQuery(String out_biz_no, String order_id) {
        AlipayFundTransCommonQueryResponse response = null;

        Map<String, Object> requestParamMap = new HashMap<>();
        requestParamMap.put("out_biz_no", out_biz_no);
        requestParamMap.put("product_code", "TRANS_ACCOUNT_NO_PWD"); //销售产品码，单笔无密转账固定为TRANS_ACCOUNT_NO_PWD
        requestParamMap.put("biz_scene", "DIRECT_TRANSFER");     //业务场景，单笔无密转账固定为DIRECT_TRANSFER
        //requestParamMap.put("order_id", order_id);
        String bizContent = JSON.toJSONString(requestParamMap);
        AlipayFundTransCommonQueryRequest request = new AlipayFundTransCommonQueryRequest();
        request.setBizContent(bizContent);

        try {
            if (defaultAlipayClient == null) createDefaultClient();
            response = defaultAlipayClient.certificateExecute(request);
            //response.isSuccess();
            if (!response.isSuccess())
                logger.info("企业转账单据查询接口 alipay.fund.trans.common.query,调用失败; code:" + response.getSubCode() + "; msg:" + response.getSubMsg());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }

    //账户余额查询接口alipay.fund.account.query
    //查询请求发起方（即商户自己）的支付宝账户余额，该接口只能查询调用账号的余额，不得查询非请求账号余额。
    public AlipayFundAccountQueryResponse query() {
        AlipayFundAccountQueryRequest request = new AlipayFundAccountQueryRequest();
        request.setBizContent("{" +
                "\"alipay_user_id\":\"****************\"," +
                "\"account_type\":\"ACCTRANS_ACCOUNT\"" +
                "  }");
        AlipayFundAccountQueryResponse response = null;
        try {
            if (defaultAlipayClient == null) createDefaultClient();
            response = defaultAlipayClient.certificateExecute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        if (response != null || response.isSuccess()) {
            logger.info("账户余额查询接口alipay.fund.account.query,调用成功");
        } else {
            logger.info("账户余额查询接口alipay.fund.account.query,调用失败");
        }
        return response;
    }
}
