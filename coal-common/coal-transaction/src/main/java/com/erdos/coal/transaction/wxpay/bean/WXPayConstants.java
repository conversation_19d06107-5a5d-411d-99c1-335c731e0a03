package com.erdos.coal.transaction.wxpay.bean;

import org.apache.http.client.HttpClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 常量
 */
@Configuration
@Component
public class WXPayConstants {

    public enum SignType {
        MD5, HMACSHA256
    }

    public static final String DOMAIN_API = "api.mch.weixin.qq.com";
    public static final String DOMAIN_API2 = "api2.mch.weixin.qq.com";
    public static final String DOMAIN_APIHK = "apihk.mch.weixin.qq.com";
    public static final String DOMAIN_APIUS = "apius.mch.weixin.qq.com";

    public static final String mch_id = "1540172321";
    public static final String key = "dabae0c78a6343f3b49cef45bda67c6f";
    public static final String sessionHost = "https://api.weixin.qq.com/sns/jscode2session";//登录
    public static final String wechat_grant_type = "authorization_code";
    public static final String serial_no = "6C40C1EF928BB1935659A9AF871A87A5752533FD";      //商户API证书序列号
    public static final String v3_key = "e6e6baed51b14dc3bdb5c4bbcf03af0d";

    //客商app - 客商黑金宝
    public static final String cus_app_id = "wx852930b40eccf3c3";
    public static final String cus_secret = "7b2e62f1cae58fe15139a370fbf96e99";

    //司机app - 司机黑金宝
    public static final String dri_app_id = "wxc1d89eaa685aaed2";
    public static final String dri_secret = "6d4ccbd78da2f69c0b86110aa38aa190";

    //司机微信小程序 - 黑金宝司机
    public static final String wechat_appid = "wx704518fd39b86f7a";
    public static final String wechat_secret = "715a1c8320639493612478b3638d3be2";

    //客商微信小程序 - 黑金宝客商
    public static final String wechat_appid_cus = "wx0b28a539d6d1017f";//"wx64f6f7dd17cbaca7";
    public static final String wechat_secret_cus = "2c3f7723a240e7ead480b4728f5f18cf";//"095b61c7affd2a1204464a94efabdeed";

    public static final String notifyUrl = "https://heijinyun.net/api/manage/wxpay/notify";
    public static final String notifyUrl_cost = "https://heijinyun.net/api/manage/wxpay/notify_cost";

    //public static final String routine_notifyUrl = "http://110.18.61.100:33381/api/manage/wxpay/routine_notify";

    public static final String FAIL = "FAIL";
    public static final String SUCCESS = "SUCCESS";
    public static final String HMACSHA256 = "HMAC-SHA256";
    public static final String MD5 = "MD5";

    public static final String FIELD_SIGN = "sign";
    public static final String FIELD_SIGN_TYPE = "sign_type";

    public static final String WXPAYSDK_VERSION = "WXPaySDK/3.0.9";
    public static final String USER_AGENT = WXPAYSDK_VERSION +
            " (" + System.getProperty("os.arch") + " " + System.getProperty("os.name") + " " + System.getProperty("os.version") +
            ") Java/" + System.getProperty("java.version") + " HttpClient/" + HttpClient.class.getPackage().getImplementationVersion();

    public static final String MICROPAY_URL_SUFFIX = "/pay/micropay";
    public static final String UNIFIEDORDER_URL_SUFFIX = "/pay/unifiedorder";
    public static final String ORDERQUERY_URL_SUFFIX = "/pay/orderquery";
    public static final String REVERSE_URL_SUFFIX = "/secapi/pay/reverse";
    public static final String CLOSEORDER_URL_SUFFIX = "/pay/closeorder";
    public static final String REFUND_URL_SUFFIX = "/secapi/pay/refund";
    public static final String REFUNDQUERY_URL_SUFFIX = "/pay/refundquery";
    public static final String DOWNLOADBILL_URL_SUFFIX = "/pay/downloadbill";
    public static final String REPORT_URL_SUFFIX = "/payitil/report";
    public static final String SHORTURL_URL_SUFFIX = "/tools/shorturl";
    public static final String AUTHCODETOOPENID_URL_SUFFIX = "/tools/authcodetoopenid";
    public static final String TRANSFERS_URL_SUFFIX = "/mmpaymkttransfers/promotion/transfers";//提现
    public static final String GETTRANSFERINFO_URL_SUFFIX = "/mmpaymkttransfers/gettransferinfo";//查询

    public static final String WECHATPAY_SERIAL = "PUB_KEY_ID_0115401723212024103100389200000123";//微信支付公钥ID
    public static final String RECEIVERIS_URL_SUFFIX = "/v3/profitsharing/receivers/add";//添加分账接收方
    public static final String PROFITSHARING_URL_SUFFIX = "/v3/profitsharing/orders";//请求分账
    public static final String PROFITSHARING_UNFREEZE_URL_SUFFIX = "/v3/profitsharing/orders/unfreeze";//分账解冻

    // sandbox
    public static final String SANDBOX_MICROPAY_URL_SUFFIX = "/sandboxnew/pay/micropay";
    public static final String SANDBOX_UNIFIEDORDER_URL_SUFFIX = "/sandboxnew/pay/unifiedorder";
    public static final String SANDBOX_ORDERQUERY_URL_SUFFIX = "/sandboxnew/pay/orderquery";
    public static final String SANDBOX_REVERSE_URL_SUFFIX = "/sandboxnew/secapi/pay/reverse";
    public static final String SANDBOX_CLOSEORDER_URL_SUFFIX = "/sandboxnew/pay/closeorder";
    public static final String SANDBOX_REFUND_URL_SUFFIX = "/sandboxnew/secapi/pay/refund";
    public static final String SANDBOX_REFUNDQUERY_URL_SUFFIX = "/sandboxnew/pay/refundquery";
    public static final String SANDBOX_DOWNLOADBILL_URL_SUFFIX = "/sandboxnew/pay/downloadbill";
    public static final String SANDBOX_REPORT_URL_SUFFIX = "/sandboxnew/payitil/report";
    public static final String SANDBOX_SHORTURL_URL_SUFFIX = "/sandboxnew/tools/shorturl";
    public static final String SANDBOX_AUTHCODETOOPENID_URL_SUFFIX = "/sandboxnew/tools/authcodetoopenid";

    public static final String SANDBOX_TRANSFERS_URL_SUFFIX = "/sandboxnew/mmpaymkttransfers/promotion/transfers";

}

