package com.erdos.coal.transaction.wxpay.service;

import com.erdos.coal.transaction.wxpay.bean.WXPayConstants;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

@Service
public class WXPayDriverConfig extends WXPayConfig {

    @Resource
    private WXPayConstants wxPayConstants;
    private byte[] certData;

    public WXPayDriverConfig() throws Exception {
//        String certPath = "E:/pingtai/apiclient_cert.p12";
//        File file = new File(certPath);
//        InputStream certStream = new FileInputStream(file);
//        this.certData = new byte[(int) file.length()];
//        certStream.read(this.certData);
//        certStream.close();

        //把证书文件放到 resources 目录下，用以下方式加载
        ClassPathResource classPathResource = new ClassPathResource("static/apiclient_cert.p12");
        this.certData = toByteArray(classPathResource.getInputStream());
    }

    private static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    public String getAppID() {
        return wxPayConstants.dri_app_id;
    }

    public String getMchID() {
        return wxPayConstants.mch_id;
    }

    public String getKey() {
        return wxPayConstants.key;
    }

    public InputStream getCertStream() {
        ByteArrayInputStream certBis = new ByteArrayInputStream(this.certData);
        return certBis;
    }

    public int getHttpConnectTimeoutMs() {
        return 8000;
    }

    public int getHttpReadTimeoutMs() {
        return 10000;
    }

    public IWXPayDomain getWXPayDomain() {
        // 这个方法需要这样实现, 否则无法正常初始化WXPay
        IWXPayDomain iwxPayDomain = new IWXPayDomain() {

            public void report(String domain, long elapsedTimeMillis, Exception ex) {

            }

            public DomainInfo getDomain(WXPayConfig config) {
                return new DomainInfo(WXPayConstants.DOMAIN_API, true);
            }
        };
        return iwxPayDomain;

    }
}
