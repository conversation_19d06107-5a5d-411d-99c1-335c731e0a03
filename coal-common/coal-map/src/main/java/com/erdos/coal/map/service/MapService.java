package com.erdos.coal.map.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

public class MapService {
    private static final String URL_GAODE = "http://restapi.amap.com/";
    private static final String KEY_GAODE = "209c9214120f1670b9053b298afdc619";

    private static final String URL_BAIDU = "https://api.map.baidu.com";
    private static final String URL_BAIDU_YINGYAN = "http://yingyan.baidu.com/api/v3";
    private static final String AK_BAIDU = "nU6xudLAx5ud0cnxFM1jepKVCz5i9z3Y";
    private static final String KEYSERVICEID_BAIDU = "230775";
    private static final String SK_BAIDU = "kbToGAG9DvvlswT7mgu4owPW7A3Kkam2";

    private static final String WGS84 = "wgs84";

    private final static Logger logger = LoggerFactory.getLogger(MapService.class);

    /*---------------------------高德地图------------------------*/
    //输入经纬度（经度，）返回地址
    public static String getLocationAddrGaode(String location) {
        List<NameValuePair> list = new LinkedList<>();
        list.add(new BasicNameValuePair("key", KEY_GAODE));
        list.add(new BasicNameValuePair("output", "JSON"));
        list.add(new BasicNameValuePair("location", location));
        String responseEntity = null;
        String addr = "";
        try {
            HttpGet httpGet = new HttpGet(new URIBuilder(URL_GAODE + "v3/geocode/regeo").setParameters(list).build());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (!jsonObject.getJSONObject("regeocode").getJSONObject("addressComponent").getString("country").equals("[]")) {//输入经纬度找不到地址
                addr = jsonObject.getJSONObject("regeocode").getJSONObject("addressComponent")
                        .getJSONObject("building").getString("name");
            } else {
                return "当前位置区域可能出界了";
            }

            if (addr.equals("[]")) {
                addr = jsonObject.getJSONObject("regeocode").getString("formatted_address");
//                if (addr.indexOf("省") > 0) {
//                    addr = addr.substring(addr.indexOf("省") + 1, addr.length());
//                }
//                if (addr.indexOf("市") > 0) {
//                    addr = addr.substring(addr.indexOf("市") + 1, addr.length());
//                }
            }
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--getLocationAddrGaode--输入经纬度查询地址---请求高德地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return "无此位置";
        }
        return addr;
    }

    /**
     * strategy
     * 驾车选择策略
     * 1，返回的结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致
     * 2，返回的结果不走高速，与高德地图“不走高速”策略一致
     * 3，返回的结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致
     * 4，返回的结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致
     * 5，返回的结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致
     * 6，返回路径规划结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致
     * 7，返回的结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致
     * 8，返回的结果会优先选择高速路，与高德地图的“高速优先”策略一致
     * 9，返回的结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致
     * 10，不考虑路况，返回速度优先的路线，此路线不一定距离最短
     * <p>
     * size
     * 车辆大小
     * 高德此分类依据国标
     * 1：微型车，2：轻型车（默认值），3：中型车，4：重型车
     * <p>
     * nosteps
     * 是否返回steps（详细路线步骤）字段内容
     * 当取值为0时，steps字段内容正常返回；
     * 当取值为1时，steps字段内容为空；
     */
    public static String truckRoundGaoDe(String startPoint, String endPoint) {
        //todo:校验经纬度格式
        String a = startPoint.split(",")[0];
        String b = startPoint.split(",")[1];
        String c = endPoint.split(",")[0];
        String d = endPoint.split(",")[1];
        //1.点要求在中国境内
        if (CoordinateTranService.outOfChina(Double.valueOf(b), Double.valueOf(a)) || CoordinateTranService.outOfChina(Double.valueOf(d), Double.valueOf(c)))
            return null;
        //2.高德地图的接口要求经纬度必须保留小数点后6位
        if ((a.length() - a.indexOf(".") - 1) != 6 || (b.length() - b.indexOf(".") - 1) != 6 || (c.length() - c.indexOf(".") - 1) != 6 || (d.length() - d.indexOf(".") - 1) != 6) {
            logger.warn("调用高德地图路线规划（round）方法，输入的经纬度数据格式错误");
            return null;
        }

        //todo:高德地图接口 规划货车路径
        List<NameValuePair> list = new LinkedList<>();
        list.add(new BasicNameValuePair("key", KEY_GAODE));
        list.add(new BasicNameValuePair("output", "JSON"));
        list.add(new BasicNameValuePair("origin", startPoint));
        list.add(new BasicNameValuePair("destination", endPoint));
        list.add(new BasicNameValuePair("strategy", String.valueOf(7)));
        list.add(new BasicNameValuePair("size", String.valueOf(4)));
        list.add(new BasicNameValuePair("nosteps", String.valueOf(1)));

        /*String url = URL + "v4/direction/truck";
        url += "&origin=" + startPoint;
        url += "&destination=" + endPoint;
        url += "&strategy=" + 7;
        url += "&size=" + 4;
        url += "&nosteps=" + 1;*/

        String responseEntity = null;
        try {
            HttpGet httpGet = new HttpGet(new URIBuilder(URL_GAODE + "v4/direction/truck").setParameters(list).build());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");

            /*JSONObject jsonObject = JSON.parseObject(responseEntity);
            JSONObject data = jsonObject.getJSONObject("data");
            JSONObject route = data.getJSONObject("route");
            JSONArray paths = route.getJSONArray("paths");
            Object path = paths.get(0);
            JSONObject pathObj = new JSONObject(path.toString());
            //("总里程：" + pathObj.get("distance") + "米");
            //("过路费：" + pathObj.get("tolls") + "元");
            //("耗时：" + pathObj.get("duration") + "秒");*/

        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("路程和运费的json数据" + responseEntity);
        return "路程和运费的json数据";
    }

    public static String drivingRoundGaode(String origin, String destination) {
        double distance;
        String tolls;

        List<NameValuePair> list = new LinkedList<>();
        list.add(new BasicNameValuePair("key", KEY_GAODE));
        list.add(new BasicNameValuePair("output", "JSON"));
        list.add(new BasicNameValuePair("origin", origin));
        list.add(new BasicNameValuePair("destination", destination));
        String responseEntity;
        String resultStr;
        try {
            HttpGet httpGet = new HttpGet(new URIBuilder(URL_GAODE + "v3/direction/driving").setParameters(list).build());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);
            if (!ObjectUtils.isEmpty(jsonObject.get("info")) && !"OK".equals(jsonObject.get("info")))
                return jsonObject.getString("info");
            JSONObject route = jsonObject.getJSONObject("route");
            JSONArray paths = route.getJSONArray("paths");
            Object path = paths.get(0);
            JSONObject pathObj = JSON.parseObject(path.toString());
            distance = Double.valueOf((String) pathObj.get("distance"));
            tolls = (String) pathObj.get("tolls");

            resultStr = "{\"distance\":" + distance / 1000 + ",\"tolls\":" + tolls + "}";
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--drivingRoubdGaode--路线规划---请求高德地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return "路线错误";
        }

        return resultStr;
    }

    //创建电子围栏 围栏的过期时间最长为90天
    public static String addGeoFence(String name, String center, String radius) {

        JSONObject param = new JSONObject();
        param.put("name", name);
        param.put("center", center);
        param.put("radius", radius);
        param.put("enable", "true");
        param.put("repeat", "Mon,Tues,Wed,Thur,Fri,Sat,Sun");
        String gid;

        try {   //v4/geofence/meta?key=用户key
            HttpPost httpPost = new HttpPost(new URIBuilder(URL_GAODE + "v4/geofence/meta").setParameter("key", KEY_GAODE).build());

            String strParam = param.toJSONString();
            StringEntity entity = new StringEntity(strParam, "UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            httpPost.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.get("errcode").equals(0) && jsonObject.getJSONObject("data").getString("status").equals("0")) {//错误码0，返回成功
                gid = jsonObject.getJSONObject("data").getString("gid");
            } else {
                // logger.error(jsonObject.getString("errcode"));
                return null;
            }

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--addGeoFence--创建电子围栏---请求高德地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return null;
        }

        return gid;
    }

    //查询电子围栏
    public static void searchGeoFence() {
        List<NameValuePair> list = new LinkedList<>();
        list.add(new BasicNameValuePair("key", KEY_GAODE));

        try {
            HttpGet httpGet = new HttpGet(new URIBuilder(URL_GAODE + "v4/geofence/meta").setParameters(list).build());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            System.out.println("0");

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--searchGeoFence--查询电子围栏---请求高德地图接口超时-------");
            } else {
                e.printStackTrace();
            }
        }
    }

    //更新电子围栏  或  电子围栏启动和停止
    public static Integer updataOrStopAndStartGeoFence(String gid, String name, String center, String radius, Boolean enable) {

        JSONObject param = new JSONObject();
        if (StringUtils.isEmpty(enable)) {
            if (!StringUtils.isEmpty(name)) param.put("name", name);
            if (!StringUtils.isEmpty(center)) param.put("center", center);
            if (!StringUtils.isEmpty(radius)) param.put("radius", radius);
        } else {
            param.put("enable", String.valueOf(enable));
        }

        try {   //v4/geofence/meta?key=用户key
            HttpPatch httpPatch = new HttpPatch(new URIBuilder(URL_GAODE + "v4/geofence/meta").setParameter("key", KEY_GAODE).setParameter("gid", gid).build());

            String strParam = param.toJSONString();
            StringEntity entity = new StringEntity(strParam, "UTF-8");
            entity.setContentType("application/json");
            httpPatch.setEntity(entity);
            httpPatch.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());

            HttpResponse response = HttpClients.createDefault().execute(httpPatch);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.get("errcode").equals(0) && jsonObject.getJSONObject("data").getString("status").equals("0")) {//错误码0，返回成功
                return 0; //"电子围栏修改 或 启动和停止成功";
            } else {
                return 1; //"电子围栏修改 或 启动和停止失败";
            }

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                if (StringUtils.isEmpty(enable)) {
                    logger.error("--updataOrStopAndStartGeoFence--修改电子围栏---请求高德地图接口超时-------");
                } else {
                    logger.error("--updataOrStopAndStartGeoFence--启动和停止电子围栏---请求高德地图接口超时-------");
                }
            } else {
                e.printStackTrace();
            }
            return 2; //"电子围栏修改 或 启动和停止错误";
        }
    }

    /*
    diu
    用户设备唯一标识符
    设备唯一标识符，作为记录依据，不影响判断结果。Android设备一般使用imei号码，iOS一般使用idfv号，其余设备可根据业务自定义。
    */
    public static Map<String, List<String>> geoFenceStatus(String diu, String locations) {
        List<NameValuePair> param = new ArrayList<>();
        param.add(new BasicNameValuePair("key", KEY_GAODE));
        param.add(new BasicNameValuePair("diu", diu));

        long timeNow = System.currentTimeMillis();
        String temp = String.valueOf(timeNow);
        temp = temp.substring(0, 10);

        param.add(new BasicNameValuePair("locations", locations + "," + temp));
        // String gid = null;
        Map<String, List<String>> result = new HashMap<>();
        List<String> gids = new ArrayList<>();
        List<String> names = new ArrayList<>();

        try {   //v4/geofence/meta?key=用户key
            HttpGet httpGet = new HttpGet(new URIBuilder(URL_GAODE + "v4/geofence/status").setParameters(param).build());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());

            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.get("errcode").equals(0)) {//错误码0，返回成功
                JSONObject data = jsonObject.getJSONObject("data");
                if (data.getString("status").equals("0")) {
                    String nearest_fence_distance = data.getString("nearest_fence_distance");
                    if (StringUtils.isEmpty(nearest_fence_distance)) {

                        JSONArray arr = data.getJSONArray("fencing_event_list");
                        int size = data.getJSONArray("fencing_event_list").size();
                        for (int i = 0; i < size; i++) {
                            JSONObject o = (JSONObject) arr.get(i);
                            gids.add(o.getJSONObject("fence_info").getString("fence_gid"));
                            names.add(o.getJSONObject("fence_info").getString("fence_name"));
                        }
                        result.put("gids", gids);
                        result.put("names", names);

                        /*JSONObject o = (JSONObject) data.getJSONArray("fencing_event_list").get(0);
                        gid = o.getJSONObject("fence_info").getString("fence_gid");*/
                    }
                }
            } else {
                return null;
            }

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--geoFenceStatus--围栏设备监控---请求高德地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return null;
        }

        return result;
    }

    //删除电子围栏
    public static Integer delGeoFence(String gid) {

        try {   //v4/geofence/meta?key=用户key
            HttpDelete httpDelete = new HttpDelete(new URIBuilder(URL_GAODE + "v4/geofence/meta").setParameter("key", KEY_GAODE).setParameter("gid", gid).build());
            httpDelete.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpDelete);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.get("errcode").equals(0)) {//错误码0，返回成功
                return 0; //"电子围栏删除成功";
            } else {
                return 1; //"电子围栏删除失败";
            }

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--delGeoFence--删除电子围栏---请求高德地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return 2; //"电子围栏删除错误";
        }
    }


    /*---------------------------百度地图------------------------*/
    public static String getLocationAddrBaiDu(String location) {
        String[] loc = location.split(",");
        String location_baidu = loc[1] + "," + loc[0];
        String responseEntity;
        String addr = "";
        try {

            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）。
            // 以get请求为例：http://api.map.baidu.com/geocoder/v2/?address=百度大厦&output=json&ak=yourak
            // paramsMap中先放入address，再放output，然后放ak，放入顺序必须跟get请求中对应参数的出现顺序保持一致。
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("output", "json");
            paramsMap.put("location", location_baidu);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/reverse_geocoding/v3?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            String url = URL_BAIDU + "/reverse_geocoding/v3";
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("ak", AK_BAIDU);
            uriBuilder.setParameter("output", "json");
            uriBuilder.setParameter("location", location_baidu);
            uriBuilder.setParameter("sn", sn);
            HttpGet httpGet = new HttpGet(uriBuilder.build().toString());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            int status = jsonObject.getInteger("status");
            if (status == 0) {
                JSONObject result = jsonObject.getJSONObject("result");
                addr = result.getString("formatted_address");
            }
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--getLocationAddrBaiDu--输入经纬度查询地址---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return "无此位置";
        }
        return addr;
    }

    public static String truckRoundBaiDu(String startPoint, String endPoint) {
        //todo:校验经纬度格式
        String a = startPoint.split(",")[1];
        String b = startPoint.split(",")[0];
        String c = endPoint.split(",")[1];
        String d = endPoint.split(",")[0];
        //1.点要求在中国境内
        if (CoordinateTranService.outOfChina(Double.valueOf(b), Double.valueOf(a)) || CoordinateTranService.outOfChina(Double.valueOf(d), Double.valueOf(c)))
            return null;

        //todo:百度地图接口 规划货车路径
        String responseEntity;
        try {
            //https://api.map.baidu.com/logistics_direction/v1/truck
            // ?origin=22.673186,114.065699&destination=22.6721,114.068886&height=1.8&width=1.9&weight=2.5&length=4.2
            // &axle_weight=2&axle_count=2&is_trailer=0&plate_province=&plate_number=&plate_color=0&ak=你的AK
            /*origin	    是必填	string	起点坐标	格式为：纬度,经度。如：21.22345,112.11478
             destination	是必填	string	终点坐标	格式与起点坐标相同
             ak	            是必填	string	用户的AK，授权使用
             is_trailer	    否必填	int32	是否是挂车	0：不是(默认)1：是
             */

            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("origin", startPoint);
            paramsMap.put("destination", endPoint);
            paramsMap.put("ak", AK_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/logistics_direction/v1/truck?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            String url = URL_BAIDU + "/logistics_direction/v1/truck";
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("origin", startPoint);
            uriBuilder.setParameter("destination", endPoint);
            uriBuilder.setParameter("ak", AK_BAIDU);
            uriBuilder.setParameter("sn", sn);
            HttpGet httpGet = new HttpGet(uriBuilder.build().toString());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            //JSONObject jsonObject = JSON.parseObject(responseEntity);

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--truckRoundBaiDu--货车路径规划---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return "路线错误";
        }
        logger.info("路程和运费的json数据" + responseEntity);
        return responseEntity;//"路程和运费的json数据";
    }

    public static String drivingRoundBaiDu(String origin, String destination) {
        String[] o = origin.split(",");
        String origin_bai = o[1] + "," + o[0];
        String[] d = destination.split(",");
        String destination_baidu = d[1] + "," + d[0];

        String responseEntity;
        String resultStr;
        double distance;
        Integer tolls;
        try {

            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）。
            // 以get请求为例：http://api.map.baidu.com/geocoder/v2/?address=百度大厦&output=json&ak=yourak
            // paramsMap中先放入address，再放output，然后放ak，放入顺序必须跟get请求中对应参数的出现顺序保持一致。
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("origin", origin_bai);
            paramsMap.put("destination", destination_baidu);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/direction/v2/driving?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);


            String url = URL_BAIDU + "/direction/v2/driving";
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("ak", AK_BAIDU);
            uriBuilder.setParameter("origin", origin_bai);
            uriBuilder.setParameter("destination", destination_baidu);
            uriBuilder.setParameter("sn", sn);
            HttpGet httpGet = new HttpGet(uriBuilder.build().toString());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            JSONObject result = jsonObject.getJSONObject("result");
            JSONArray routes = result.getJSONArray("routes");
            JSONObject route = (JSONObject) routes.get(0);
            distance = route.getDouble("distance");
            tolls = route.getInteger("toll");

            resultStr = "{\"distance\":" + distance / 1000 + ",\"tolls\":" + tolls + "}";
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--drivingRoundBaiDu--驾车路径规划---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return "路线错误";
        }
        return resultStr;
    }

    //查询entity
    public static String searchEntity() {
        try {
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/entity/list?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            String url = URL_BAIDU_YINGYAN + "/entity/list";
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("ak", AK_BAIDU);
            uriBuilder.setParameter("service_id", KEYSERVICEID_BAIDU);
            uriBuilder.setParameter("sn", sn);
            HttpGet httpGet = new HttpGet(uriBuilder.build().toString());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);
            System.out.println(jsonObject.getString("status"));
            return responseEntity;

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--searchFenceBaiDu--查询电子围栏---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
        }
        return "{}";
    }

    //添加entity
    private static void addEneity(String entityName) {
        try {

            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("entity_name", entityName);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/entity/add?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            HttpPost httpPost = new HttpPost(new URIBuilder(URL_BAIDU_YINGYAN + "/entity/add").build());
            List<NameValuePair> list = new ArrayList<NameValuePair>();
            list.add(new BasicNameValuePair("ak", AK_BAIDU));
            list.add(new BasicNameValuePair("entity_name", entityName));
            list.add(new BasicNameValuePair("service_id", KEYSERVICEID_BAIDU));
            list.add(new BasicNameValuePair("sn", sn));
            // 构造from表单对象
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(list, "UTF-8");
            // 把表单放到post里
            httpPost.setEntity(urlEncodedFormEntity);

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);
            System.out.println(jsonObject.getString("status"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //创建电子围栏 一个service默认最多创建1000个公共围栏
    public static String addFenceBaiDu(String name, String center, String radius) {
        String[] c = center.split(",");
        String lng = c[0];
        String lat = c[1];

        String fenceId;
        try {
            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("coord_type", "wgs84");    //wgs84：GPS经纬度, gcj02：国测局经纬度, bd09ll：百度经纬度
            paramsMap.put("fence_name", name);
            paramsMap.put("latitude", lat);
            paramsMap.put("longitude", lng);
            paramsMap.put("radius", radius);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/fence/createcirclefence?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            HttpPost httpPost = new HttpPost(new URIBuilder(URL_BAIDU_YINGYAN + "/fence/createcirclefence").build());

            List<NameValuePair> list = new ArrayList<NameValuePair>();
            list.add(new BasicNameValuePair("ak", AK_BAIDU));
            list.add(new BasicNameValuePair("coord_type", "wgs84"));
            list.add(new BasicNameValuePair("fence_name", name));
            list.add(new BasicNameValuePair("latitude", lat));
            list.add(new BasicNameValuePair("longitude", lng));
            list.add(new BasicNameValuePair("radius", radius));
            list.add(new BasicNameValuePair("service_id", KEYSERVICEID_BAIDU));
            list.add(new BasicNameValuePair("sn", sn));
            // 构造from表单对象
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(list, "UTF-8");
            // 把表单放到post里
            httpPost.setEntity(urlEncodedFormEntity);

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.getString("status").equals("0")) {//错误码0，返回成功
                fenceId = jsonObject.getString("fence_id");
                String resultEntity = searchEntity();
                JSONObject object = JSON.parseObject(resultEntity);
                if (!object.getString("status").equals("0") || object.getString("status").equals("0") && object.getInteger("size") <= 0) {
                    addEneity("打卡");
                }
                addMonitoredPerson(fenceId, "打卡");
            } else {
                return null;
            }
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--addFenceBaiDu--创建电子围栏---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return null;
        }
        return fenceId;
    }

    private static void addMonitoredPerson(String fenceId, String monitoredPerson) {
        try {
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("fence_id", fenceId);
            paramsMap.put("monitored_person", monitoredPerson);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/fence/addmonitoredperson?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            HttpPost httpPost = new HttpPost(new URIBuilder(URL_BAIDU_YINGYAN + "/fence/addmonitoredperson").build());
            List<NameValuePair> list = new ArrayList<NameValuePair>();
            list.add(new BasicNameValuePair("ak", AK_BAIDU));
            list.add(new BasicNameValuePair("fence_id", fenceId));
            list.add(new BasicNameValuePair("monitored_person", monitoredPerson));
            list.add(new BasicNameValuePair("service_id", KEYSERVICEID_BAIDU));
            list.add(new BasicNameValuePair("sn", sn));
            // 构造from表单对象
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(list, "UTF-8");
            // 把表单放到post里
            httpPost.setEntity(urlEncodedFormEntity);

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //查询电子围栏
    public static String searchFenceBaiDu(String fenceIds) {
        try {
            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("fence_ids", fenceIds);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/fence/list?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            String url = URL_BAIDU_YINGYAN + "/fence/list";
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("ak", AK_BAIDU);
            uriBuilder.setParameter("fence_ids", fenceIds);
            uriBuilder.setParameter("service_id", KEYSERVICEID_BAIDU);
            uriBuilder.setParameter("sn", sn);
            HttpGet httpGet = new HttpGet(uriBuilder.build().toString());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            //JSONObject jsonObject = JSON.parseObject(responseEntity);

            return responseEntity;

        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--searchFenceBaiDu--查询电子围栏---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
        }
        return "{}";
    }

    //更新电子围栏
    public static Integer updataCircleFenceBaidu(String fenceId, String name, String center, String radius) {
        String lat = null;
        String lng = null;
        if (!StringUtils.isEmpty(center)) {
            String[] c = center.split(",");
            lng = c[0];
            lat = c[1];
        }
        try {
            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            if (!StringUtils.isEmpty(center)) paramsMap.put("coord_type", "wgs84");
            paramsMap.put("fence_id", fenceId);
            if (!StringUtils.isEmpty(name)) paramsMap.put("fence_name", name);
            if (!StringUtils.isEmpty(center)) paramsMap.put("latitude", lat);
            if (!StringUtils.isEmpty(center)) paramsMap.put("longitude", lng);
            if (!StringUtils.isEmpty(radius)) paramsMap.put("radius", radius);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/fence/updatecirclefence?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            HttpPost httpPost = new HttpPost(new URIBuilder(URL_BAIDU_YINGYAN + "/fence/updatecirclefence").build());

            List<NameValuePair> list = new ArrayList<NameValuePair>();
            list.add(new BasicNameValuePair("ak", AK_BAIDU));
            if (!StringUtils.isEmpty(center)) list.add(new BasicNameValuePair("coord_type", "wgs84"));
            list.add(new BasicNameValuePair("fence_id", fenceId));
            if (!StringUtils.isEmpty(name)) list.add(new BasicNameValuePair("fence_name", name));
            if (!StringUtils.isEmpty(center)) list.add(new BasicNameValuePair("latitude", lat));
            if (!StringUtils.isEmpty(center)) list.add(new BasicNameValuePair("longitude", lng));
            if (!StringUtils.isEmpty(radius)) list.add(new BasicNameValuePair("radius", radius));
            list.add(new BasicNameValuePair("service_id", KEYSERVICEID_BAIDU));
            list.add(new BasicNameValuePair("sn", sn));
            // 构造from表单对象
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(list, "UTF-8");
            // 把表单放到post里
            httpPost.setEntity(urlEncodedFormEntity);

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.getString("status").equals("0")) {//错误码0，返回成功
                return 0;   //电子围栏修改成功
            } else {
                logger.error(jsonObject.getString("message"));
                return 1;   //电子围栏修改失败
            }
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--updataCircleFenceBaidu--修改电子围栏---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return 2;  //电子围栏修改错误
        }
    }


    /*
    monitored_person: 监控对象的 entity_name
    可以通过 查询电子围栏接口，查询到默认值
    */
    public static Map<String, List<String>> fenceStatusBaiDu(String fenceIds, String locations) {

        String[] loc = locations.split(",");
        String lng = loc[0];
        String lat = loc[1];
        try {
            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("coord_type", "wgs84");
            if (!StringUtils.isEmpty(fenceIds)) paramsMap.put("fence_ids", fenceIds);
            paramsMap.put("latitude", lat);
            paramsMap.put("longitude", lng);
            paramsMap.put("monitored_person", "打卡");
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/fence/querystatusbylocation?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            String url = URL_BAIDU_YINGYAN + "/fence/querystatusbylocation";
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("ak", AK_BAIDU);
            uriBuilder.setParameter("coord_type", "wgs84");
            if (!StringUtils.isEmpty(fenceIds)) paramsMap.put("fence_ids", fenceIds);
            uriBuilder.setParameter("latitude", lat);
            uriBuilder.setParameter("longitude", lng);
            uriBuilder.setParameter("monitored_person", "打卡");//monitored_person);
            uriBuilder.setParameter("service_id", KEYSERVICEID_BAIDU);
            uriBuilder.setParameter("sn", sn);
            HttpGet httpGet = new HttpGet(uriBuilder.build().toString());
            httpGet.setConfig(RequestConfig.custom().setConnectTimeout(2000).build());
            HttpResponse response = HttpClients.createDefault().execute(httpGet);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.getString("status").equals("0")) {
                Map<String, List<String>> result = new HashMap<>();
                List<String> gids = new ArrayList<>();
                JSONArray monitored_statuses = jsonObject.getJSONArray("monitored_statuses");
                for (int i = 0; i < monitored_statuses.size(); i++) {
                    JSONObject statuse = (JSONObject) monitored_statuses.get(i);
                    String fence_id = statuse.getString("fence_id");
                    String monitored_status = statuse.getString("monitored_status");
                    if (monitored_status.equals("in")) {
                        gids.add(fence_id);
                    }
                }
                result.put("gids", gids);
                return result;
            }


        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--fenceStatusBaiDu--电子围栏打卡---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
        }
        return null;
    }

    //删除电子围栏
    public static Integer delFenceBaiDu(String fenceIds) {
        try {
            // 计算sn跟参数对出现顺序有关，get请求请使用LinkedHashMap保存<key,value>，该方法根据key的插入顺序排序；
            // post请使用TreeMap保存<key,value>，该方法会自动将key按照字母a-z顺序排序。
            // 所以get请求可自定义参数顺序（sn参数必须在最后）发送请求，
            // 但是post请求必须按照字母a-z顺序填充body（sn参数必须在最后）
            Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ak", AK_BAIDU);
            paramsMap.put("fence_ids", fenceIds);
            paramsMap.put("service_id", KEYSERVICEID_BAIDU);
            MapService snCal = new MapService();
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果
            String paramsStr = snCal.toQueryString(paramsMap);
            //拼接url 和 参数
            String wholeStr = "/api/v3/fence/delete?" + paramsStr + SK_BAIDU;
            // 对上面wholeStr再作utf8编码
            String tempStr = URLEncoder.encode(wholeStr, "UTF-8");
            // 调用下面的MD5方法得到最后的sn签名
            String sn = snCal.MD5(tempStr);

            HttpPost httpPost = new HttpPost(new URIBuilder(URL_BAIDU_YINGYAN + "/fence/delete").build());

            List<NameValuePair> list = new ArrayList<NameValuePair>();
            list.add(new BasicNameValuePair("ak", AK_BAIDU));
            list.add(new BasicNameValuePair("fence_ids", fenceIds));
            list.add(new BasicNameValuePair("service_id", KEYSERVICEID_BAIDU));
            list.add(new BasicNameValuePair("sn", sn));
            // 构造from表单对象
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(list, "UTF-8");
            // 把表单放到post里
            httpPost.setEntity(urlEncodedFormEntity);

            HttpResponse response = HttpClients.createDefault().execute(httpPost);
            String responseEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(responseEntity);

            if (jsonObject.getString("status").equals("0")) {//错误码0，返回成功
                return 0;   //电子围栏删除成功
            } else {
                logger.error(jsonObject.getString("message"));
                return 1;   //电子围栏删除失败
            }
        } catch (Exception e) {
            if (e instanceof ConnectTimeoutException) {
                logger.error("--delFenceBaiDu--删除电子围栏---请求百度地图接口超时-------");
            } else {
                e.printStackTrace();
            }
            return 2;  //电子围栏删除错误
        }
    }

    public static void main(String[] arg) throws UnsupportedEncodingException {
        /*logger.info(getLocationAddr("113.242439,35.1863620"));
        logger.info(getLocationAddr("11,13"));*/

        MapService m = new MapService();
        //m.searchGeoFence();

        //109.761803,39.595912      内蒙古自治区鄂尔多斯市伊金霍洛旗苏布尔嘎街
        //38.76623,116.43213        河北省沧州市任丘市
        // getLocationAddrBaiDu("109.761803,39.595912");

        //String result = truckRoundBaiDu("39.5959123,109.7618033", "38.76623,116.43213");
        /*String result = drivingRoundBaiDu("109.7618033,39.5959123", "116.43213,38.76623");
        System.out.println(result);*/

        // addFenceBaiDu("围栏-test1", "105.761803,38.595912", "500");

        searchFenceBaiDu("1,2,3,4,5,6,7,8,9");

        // updataCircleFenceBaidu("3", "tiankong", null, null);

        // fenceStatusBaiDu(null, "106.761803,48.595912");
        // searchEntity();

        // delGeoFencebaiDu("1");

        /*String gid = m.addGeoFence("乌巴塔集装站", "99.788835,29.667976", "100");
        System.out.println(gid);*/
        // m.updataOrStopAndStartGeoFence("81d733ff-926a-4323-9114-4066baab7798", "测试地址", null, null, null);
        // m.updataOrStopAndStartGeoFence("5a0ccc90-e6a5-4057-83c2-b492e7702913", null, null, null, false);
        //m.delGeoFence("55622b66-eef2-445c-ace5-6aa0d1102f14");//("81d733ff-926a-4323-9114-4066baab7798");

        //c9366b07-ea6b-409c-8f6d-38ac28c6c35b

        /*Map<String, List<String>> result = m.geoFenceStatus("35862607126166", "99.788888,29.667966");
        System.out.println(result);*/

    }

    // 对Map内所有value作utf8编码，拼接返回结果
    private String toQueryString(Map<?, ?> data) throws UnsupportedEncodingException {
        StringBuffer queryString = new StringBuffer();
        for (Map.Entry<?, ?> pair : data.entrySet()) {
            queryString.append(pair.getKey() + "=");
            queryString.append(URLEncoder.encode((String) pair.getValue(), "UTF-8") + "&");
        }
        if (queryString.length() > 0) queryString.deleteCharAt(queryString.length() - 1);

        return queryString.toString();
    }

    // 来自stackoverflow的MD5计算方法，调用了MessageDigest库函数，并把byte数组结果转换成16进制
    private String MD5(String md5) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(md5.getBytes());
            StringBuffer sb = new StringBuffer();
            for (byte b : array) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
        }
        return null;
    }


}
