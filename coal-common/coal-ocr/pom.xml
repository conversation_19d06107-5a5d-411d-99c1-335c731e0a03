<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>coal-project</artifactId>
        <groupId>com.erdos.coal</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>coal-ocr</artifactId>
    <description>coal-ocr for erdos.coal</description>

    <properties>
        <baiduyun.sdk>4.12.0</baiduyun.sdk>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
            <version>${baiduyun.sdk}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baidubce</groupId>
            <artifactId>api-explorer-sdk</artifactId>
            <version>*******</version>
        </dependency>

        <!--================================================================================================-->
        <!-- https://mvnrepository.com/artifact/net.sourceforge.tess4j/tess4j -->
        <dependency>
            <groupId>net.sourceforge.tess4j</groupId>
            <artifactId>tess4j</artifactId>
            <version>4.5.5</version>
        </dependency>
        <!-- ======================================================================================================= -->
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.11.0</version>
            <!--最新版 2.16.0-->
        </dependency>
        <!-- ======================================================================================================= -->
        <!-- opencv-->
        <dependency>
            <groupId>org.opencv</groupId>
            <artifactId>opencv</artifactId>
            <version>4.1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/opencv-410.jar</systemPath>
        </dependency>
        <!-- ======================================================================================================= -->

    </dependencies>

</project>