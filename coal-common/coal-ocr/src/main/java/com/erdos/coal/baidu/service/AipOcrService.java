package com.erdos.coal.baidu.service;

import com.baidu.aip.ocr.AipOcr;
import com.baidu.aip.util.Util;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class AipOcrService {
    private static Logger logger = LoggerFactory.getLogger(AipOcrService.class);
    //设置APPID/AK/SK
    public static final String APP_ID = "25014905";//"17228622";
    public static final String API_KEY = "ed29cKaTtyIj92DfWphN4VDE";//"3UNY6kBRRcScD8avK7aKcGu7";
    public static final String SECRET_KEY = "GwT9u0G7gQG4PNWqtRxFxdSVFAgWHvhU";//"7SqXZAGZXTB1x5PIrXYRHqmO1r9QOyHX";

    public static AipOcr getAipClient() {
        // 初始化一个AipOcr
        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
//        client.setHttpProxy("proxy_host", proxy_port);  // 设置http代理
//        client.setSocketProxy("proxy_host", proxy_port);  // 设置socket代理

        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
        // 也可以直接通过jvm启动参数设置此环境变量
//        System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");
        return client;
    }

    /**
     * 获取身份证识别后的数据
     *
     * @param image
     * @param idCardSide
     * @return
     */
    public static Map<String, String> getIdCardInfo(MultipartFile image, String idCardSide) throws Exception {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<>();
        options.put("detect_direction", "true");
        options.put("detect_risk", "false");

        //MultipartFile转化为byte数组
        byte[] imgBytes = image.getBytes();

        return IdCard(imgBytes, idCardSide, options);
    }

    /**
     * 获取身份证识别后的数据(服务器上图片)
     *
     * @param file
     * @param idCardSide
     * @return
     */
    public static Map<String, String> getIdCardInfo(String file, String idCardSide) throws Exception {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<>();
        options.put("detect_direction", "true");
        options.put("detect_risk", "false");

        //file转化为byte数组
        byte[] imgBytes = Util.readFileByBytes(file);

        return IdCard(imgBytes, idCardSide, options);
    }

    /**
     * 获取身份证识别后的数据(服务器上图片)
     *
     * @param bytes
     * @param idCardSide
     * @return
     */
    public static Map<String, String> getIdCardInfo(byte[] bytes, String idCardSide) throws Exception {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<>();
        options.put("detect_direction", "true");
        options.put("detect_risk", "false");

        return IdCard(bytes, idCardSide, options);
    }

    private static Map<String, String> IdCard(byte[] imgBytes, String idCardSide, HashMap<String, String> options) throws Exception {
        AipOcr client = getAipClient();
        JSONObject res = client.idcard(imgBytes, idCardSide, options);
        JSONObject words_result = res.getJSONObject("words_result");
        Map<String, String> map = new HashMap<>();
        for (String key : words_result.keySet()) {
            JSONObject result = words_result.getJSONObject(key);
            String info = result.getString("words");
            switch (key) {
                case "姓名":
                    map.put("name", info);
                    break;
                case "公民身份号码":
                    map.put("idNumber", info);
                    break;
                case "住址":
                    map.put("address", info);
                case "出生":
                    map.put("birthday", info);
            }
        }
        logger.info("getIdCardInfo:", map);
        return map;
    }

    /**
     * 获取行驶证信息
     */
    public static Map<String, String> getVehicleLicense(String file) throws IOException {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("accuracy", "normal");

        AipOcr client = getAipClient();
        //File转化为byte数组
        byte[] imgBytes = Util.readFileByBytes(file);
        JSONObject res = client.vehicleLicense(imgBytes, options);
        JSONObject words_result = res.getJSONObject("words_result");
        Map<String, String> map = new HashMap<>();
        for (String key : words_result.keySet()) {
            JSONObject result = words_result.getJSONObject(key);
            String info = result.getString("words");
            switch (key) {
                case "号牌号码":
                    map.put("carNum", info);
                    break;
                case "所有人":
                    map.put("owner", info);
                    break;
            }
        }
        logger.info("getVehicleLicense:", map);
        return map;
    }

    /**
     * 获取行驶证信息
     */
    public static Map<String, String> getVehicleLicense(MultipartFile image) throws IOException {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("accuracy", "normal");

        AipOcr client = getAipClient();
        //MultipartFile转化为byte数组
        byte[] imgBytes = image.getBytes();
        JSONObject res = client.vehicleLicense(imgBytes, options);
        JSONObject words_result = res.getJSONObject("words_result");
        Map<String, String> map = new HashMap<>();
        for (String key : words_result.keySet()) {
            JSONObject result = words_result.getJSONObject(key);
            String info = result.getString("words");
            switch (key) {
                case "号牌号码":
                    map.put("carNum", info);
                    break;
                case "所有人":
                    map.put("owner", info);
                    break;
            }
        }
        logger.info("getVehicleLicense:", map);
        return map;
    }

    /**
     * 获取行驶证信息
     */
    public static Map<String, String> getVehicleLicense(byte[] bytes) throws IOException {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("accuracy", "normal");

        AipOcr client = getAipClient();
        JSONObject res = client.vehicleLicense(bytes, options);
        JSONObject words_result = res.getJSONObject("words_result");
        Map<String, String> map = new HashMap<>();
        for (String key : words_result.keySet()) {
            JSONObject result = words_result.getJSONObject(key);
            String info = result.getString("words");
            switch (key) {
                case "号牌号码":
                    map.put("carNum", info);
                    break;
                case "车辆识别代号":
                    map.put("VIN", info);
                case "发动机号码":
                    map.put("engineNo", info);
                    break;
                case "所有人":
                    map.put("owner", info);
                    break;
            }
        }
        logger.info("getVehicleLicense:", map);
        return map;
    }
    /**
     * 获取驾驶证信息
     */
    public static Map<String, String> getDrivingLicense(byte[] bytes) throws IOException {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("accuracy", "normal");

        AipOcr client = getAipClient();
        JSONObject res = client.drivingLicense(bytes, options);
        JSONObject words_result = res.getJSONObject("words_result");
        Map<String, String> map = new HashMap<>();
        for (String key : words_result.keySet()) {
            JSONObject result = words_result.getJSONObject(key);
            String info = result.getString("words");
            switch (key) {
                case "姓名":
                    map.put("name", info);
                    break;
                case "至":
                    map.put("validTo", info);
                    break;
                case "有效期限":
                    map.put("validToCQ", info);
            }
        }
        logger.warn("getDrivingLicense:", map);
        return map;
    }

    public static void main(String[] args) throws Exception {
        // 调用接口
//        File file = new File("E:/timg.jpg");‎⁨Macintosh HD⁩ ▸ ⁨用户⁩ ▸ ⁨panyingping⁩ ▸ ⁨桌面⁩
       /* File file = new File("/Users/<USER>/Desktop/61d4e3b410ad8237490097a2driIdentityPhoBef.jpg");//"E:/idCard.jpg");
        MultipartFile mulFile = null;
        try {
            mulFile = new MockMultipartFile("haha.jpg", new FileInputStream(file));
        } catch (IOException e) {
            e.printStackTrace();
        }
        Map<String, String> aipOcrMap = getIdCardInfo(mulFile, "front");
        String name = aipOcrMap.get("name");
        String idNumber = aipOcrMap.get("idNumber");*/

        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("accuracy", "normal");
        AipOcr aipOcr = getAipClient();
        JSONObject jsonObject = aipOcr.drivingLicense("/Users/<USER>/Desktop/zqyjszgq.jpg",options);
//        JSONObject jsonObject = aipOcr.drivingLicense("/Users/<USER>/Desktop/hqjsz.jpg",options);
        System.out.println(jsonObject);

//        getIdCardInfo("D:/log/upload/5e86f6e37e0c4f0b8c709748carIdentityPhoBef.jpg", "front");

//        getVehicleLicense(mulFile);
    }
}
