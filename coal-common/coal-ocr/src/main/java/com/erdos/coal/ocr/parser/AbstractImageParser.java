package com.erdos.coal.ocr.parser;

import java.awt.*;
import java.awt.image.BufferedImage;

public abstract class AbstractImageParser {

    /**
     * 获取图片亮度
     *
     * @param image
     * @return
     */
    protected int imageBrightness(BufferedImage image) {
        long totalRed = 0;
        long totalGreen = 0;
        long totalBlue = 0;
        for (int x = image.getMinX(); x < image.getWidth(); x++) {
            for (int y = image.getMinY(); y < image.getHeight(); y++) {
                Object data = image.getRaster().getDataElements(x, y, null);
                int dataRed = image.getColorModel().getRed(data);
                int dataBlue = image.getColorModel().getBlue(data);
                int dataGreen = image.getColorModel().getGreen(data);
                int dataAlpha = image.getColorModel().getAlpha(data);
                totalRed += dataRed;
                totalGreen += dataGreen;
                totalBlue += dataBlue;
                // totalBrightness += dataColor.getRGB();
            }
        }

        float avgRed = totalRed / (image.getHeight() * image.getWidth());
        float avgGreen = totalGreen / (image.getWidth() * image.getHeight());
        float avgBlue = totalBlue / (image.getWidth() * image.getHeight());

        int avgBrightNess = (int) ((avgRed + avgGreen + avgBlue) / 3);

        return avgBrightNess;
    }

    /**
     * 图片亮度调整
     *
     * @param image      需调整亮度的图像
     * @param brightness 需调整的亮度，参数为+、-分别表示调亮、调暗
     * @return
     */
    protected BufferedImage imageBrightness(BufferedImage image, int brightness) {
        for (int x = image.getMinX(); x < image.getWidth(); x++) {
            for (int y = image.getMinY(); y < image.getHeight(); y++) {
                Object data = image.getRaster().getDataElements(x, y, null);
                int dataRed = image.getColorModel().getRed(data);
                int dataBlue = image.getColorModel().getBlue(data);
                int dataGreen = image.getColorModel().getGreen(data);
                int dataAlpha = image.getColorModel().getAlpha(data);
                int newRed = Math.min(dataRed + brightness, 255);
                newRed = Math.max(newRed, 0);
                int newGreen = Math.min(dataGreen + brightness, 255);
                newGreen = Math.max(newGreen, 0);
                int newBlue = Math.min(dataBlue + brightness, 255);
                newBlue = Math.max(newBlue, 0);
                Color dataColor = new Color(newRed, newGreen, newBlue, dataAlpha);
                image.setRGB(x, y, dataColor.getRGB());
            }
        }
        return image;
    }

    /**
     * 图片灰度化
     *
     * @param image
     * @param grayStatus
     * @return
     */
    protected BufferedImage grayImage(BufferedImage image, int grayStatus) {

        int width = image.getWidth();
        int height = image.getHeight();

        BufferedImage grayImage = new BufferedImage(width, height, image.getType());
        //BufferedImage grayImage = new BufferedImage(width, height,  BufferedImage.TYPE_BYTE_GRAY);
        for (int i = 0; i < width; i++) {
            for (int j = 0; j < height; j++) {
                int color = image.getRGB(i, j);
                final int r = (color >> 16) & 0xff;
                final int g = (color >> 8) & 0xff;
                final int b = color & 0xff;
                int gray = 0;
                if (grayStatus == 1) {
                    gray = Math.max(Math.max(r, g), b); //最大值法灰度化
                } else if (grayStatus == 2) {
                    gray = Math.min(Math.min(r, g), b); //最小值法灰度化
                } else if (grayStatus == 3) {
                    gray = (r + g + b) / 3; //均值法灰度化
                } else if (grayStatus == 4) {
                    gray = (int) (0.3 * r + 0.59 * g + 0.11 * b);//加权法灰度化
                }
                // System.out.println("像素坐标：" + " x=" + i + "   y=" + j + "   灰度值=" + gray);
                grayImage.setRGB(i, j, this.colorToRGB(0, gray, gray, gray));
            }
        }
        return grayImage;
    }

    /**
     * 颜色分量转换为RGB值
     */
    private int colorToRGB(int alpha, int red, int green, int blue) {
        int newPixel = 0;
        newPixel += alpha;
        newPixel = newPixel << 8;
        newPixel += red;
        newPixel = newPixel << 8;
        newPixel += green;
        newPixel = newPixel << 8;
        newPixel += blue;
        return newPixel;
    }
}
