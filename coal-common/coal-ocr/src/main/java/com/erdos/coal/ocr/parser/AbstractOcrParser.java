package com.erdos.coal.ocr.parser;

import net.sourceforge.tess4j.TessAPI;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.image.BufferedImage;

public abstract class AbstractOcrParser extends AbstractImageParser {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    //private ITesseract instance;

    private String lanPath;
    private String language;

    // 必须要设置语言库路径
    public AbstractOcrParser(String lanPath) {
        this.lanPath = lanPath;
//        instance = new Tesseract();
//        instance.setDatapath(lanPath);
//        // instance.setLanguage("eng+chi_sim");
//        instance.setLanguage("chi_sim"); // 识别语言
//        // instance.setTessVariable("user_defined_dpi", "96"); // 设置分辨率
//        instance.setTessVariable("user_defined_dpi", "300"); // 设置分辨率
//        // 设置识别引擎
//        // OCR Engine modes:
//        // 0 Legacy engine only. // 原始模式
//        // 1 Neural nets LSTM engine only. // LSTM　神经网络
//        // 2 Legacy + LSTM engines. // 双引擎(较耗费资源)
//        // 3 Default, based on what is available. // 基于可用的默认值
//        instance.setOcrEngineMode(TessAPI.TessOcrEngineMode.OEM_DEFAULT);
    }

    protected void setLanguage(String language) {
        this.language = language;
    }

    protected String doOCR(BufferedImage image) {
        try {
            Tesseract instance = new Tesseract();
            instance.setDatapath(lanPath);
            // instance.setLanguage("eng+chi_sim");
            if (language == null)
                instance.setLanguage("chi_sim"); // 识别语言
            else
                instance.setLanguage(language);
            // instance.setTessVariable("user_defined_dpi", "96"); // 设置分辨率
            instance.setTessVariable("user_defined_dpi", "300"); // 设置分辨率
            // 设置识别引擎
            // OCR Engine modes:
            // 0 Legacy engine only. // 原始模式
            // 1 Neural nets LSTM engine only. // LSTM　神经网络
            // 2 Legacy + LSTM engines. // 双引擎(较耗费资源)
            // 3 Default, based on what is available. // 基于可用的默认值
            instance.setOcrEngineMode(TessAPI.TessOcrEngineMode.OEM_DEFAULT);
            return instance.doOCR(image);
        } catch (TesseractException e) {
            logger.error(e.getMessage());
        }
        return null;
    }
}
