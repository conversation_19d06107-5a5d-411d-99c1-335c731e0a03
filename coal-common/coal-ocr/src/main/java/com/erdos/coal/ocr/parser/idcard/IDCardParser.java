package com.erdos.coal.ocr.parser.idcard;

import com.erdos.coal.ocr.entity.IDCard;
import com.erdos.coal.ocr.parser.AbstractOcrParser;
import com.erdos.coal.ocr.parser.IOcrParser;
import com.erdos.coal.ocr.utils.OpenCVUtil;
import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.opencv.highgui.HighGui.imshow;
import static org.opencv.imgproc.Imgproc.CHAIN_APPROX_SIMPLE;

/**
 * 身份证图片解析器
 */
public class IDCardParser extends AbstractOcrParser implements IOcrParser<IDCard> {

    private final static String fileExtension = ".jpg";

    private static final int BLACK = 0;
    private static final int WHITE = 255;

    public IDCardParser(String dataPath) {
        super(dataPath);
    }

    /**
     * 作用：获取文字区域
     *
     * @param img 膨胀与腐蚀后的Mat矩阵图像
     * @return
     */
    private List<RotatedRect> findTextRegion(Mat img) {
        List<RotatedRect> rects = new ArrayList<>();
        //查找轮廓
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        //CV_CHAIN_APPROX_NONE存储所有的轮廓点，相邻的两个点的像素位置差不超过1，即max（abs（x1-x2），abs（y2-y1））==1
        //CV_CHAIN_APPROX_SIMPLE压缩水平方向，垂直方向，对角线方向的元素，只保留该方向的终点坐标，例如一个矩形轮廓只需4个点来保存轮廓信息

        //RETR_EXTERNAL表示只检测外轮廓
//        Imgproc.findContours(img, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(-1, -1));//20.png不能被倾斜校正

        //RETR_CCOMP建立两个等级的轮廓，上面的一层为外边界，里面的一层为内孔的边界信息。如果内孔内还有一个连通物体，这个物体的边界也在顶层。
        Imgproc.findContours(img, contours, hierarchy, Imgproc.RETR_CCOMP, CHAIN_APPROX_SIMPLE, new Point(-1, -1));//20.png可以被倾斜校正

//        Imgproc.findContours(img, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(0, 0));//20.png不能被倾斜校正

        int img_width = img.width();
        int img_height = img.height();
        int size = contours.size();

        //筛选面积小的矩形
        for (int i = 0; i < size; i++) {
            double area = Imgproc.contourArea(contours.get(i));
            if (area < 600)//原来是1000
                continue;

            //轮廓近似，approxPolyDP函数
            double epsilon = 0.001 * Imgproc.arcLength(new MatOfPoint2f(contours.get(i).toArray()), true);
            MatOfPoint2f approxCurve = new MatOfPoint2f();
            Imgproc.approxPolyDP(new MatOfPoint2f(contours.get(i).toArray()), approxCurve, epsilon, true);

            //找到可能有方向的最小矩形
            RotatedRect rect = Imgproc.minAreaRect(new MatOfPoint2f(contours.get(i).toArray()));
            //计算高和宽
            int m_width = rect.boundingRect().width;
            int m_height = rect.boundingRect().height;

            //筛选太窄的矩形，留下扁的
            if (m_width < m_height)
                continue;
            if (img_width == rect.boundingRect().br().x)
                continue;
            if (img_height == rect.boundingRect().br().y)
                continue;
            //将符合条件的rect添加到rects集合中
            rects.add(rect);
        }
        return rects;
    }

    private Mat imgCorrection(Mat src) {
        //灰度化
        Mat grayImg = OpenCVUtil.gray(src);
        //二值化
        Mat binaryImg = OpenCVUtil.binaryzation(grayImg);
        //膨胀与腐蚀
        Mat corrodedImg = OpenCVUtil.corrosion(binaryImg);
        //查找和筛选文字区域
        List<RotatedRect> rects = this.findTextRegion(corrodedImg);
        //倾斜校正
        Mat correctedImg = OpenCVUtil.correction(rects, src);
        //裁剪
        Mat cuttedImg = this.cutRect(correctedImg);
        //标准化,缩放图片
        Mat zoomedImg = new Mat();
        //区域插值(INTER_AREA):图像放大时类似于线性插值，图像缩小时可以避免波纹出现
        Imgproc.resize(cuttedImg, zoomedImg, new Size(673, 425), 0, 0);
        return zoomedImg;
    }

    private Mat cutRect(Mat correctMat) {
        //获取最大矩形
        RotatedRect rect = this.findMaxRect(correctMat);

        Point[] rectPoint = new Point[4];
        rect.points(rectPoint);

        for (Point p : rectPoint) {
            System.out.println(p.x + " , " + p.y);
        }

        int startLeft = (int) Math.abs(rectPoint[0].x);
        int startUp = (int) Math.abs(Math.abs(rectPoint[0].y) < Math.abs(rectPoint[1].y) ? rectPoint[0].y : rectPoint[1].y);
        int width = (int) Math.abs(rectPoint[2].x - rectPoint[0].x);
//        int height = (int) Math.abs(rectPoint[1].y - rectPoint[0].y);
        int height = (int) Math.abs(rectPoint[3].y - rectPoint[1].y);

        System.out.println("startLeft = " + startLeft);
        System.out.println("startUp = " + startUp);
        System.out.println("width = " + width);
        System.out.println("height = " + height);

        //检测的高度过低，则说明拍照时身份证边框没拍全，直接返回correctMat，如检测的不是身份证则不需要这个if()判断
        if (height < 0.5 * width)
            return correctMat;
        if (height > 2 * width)
            return correctMat;

        for (Point p : rectPoint) {
            System.out.println(p.x + " , " + p.y);
        }

        if (startLeft + width > correctMat.width()) {
            width = correctMat.width() - startLeft;
        }
        if (startUp + height > correctMat.height()) {
            height = correctMat.height() - startUp;
        }

        Mat cuttedMat = new Mat(correctMat, new Rect(startLeft, startUp, width, height));
        return cuttedMat;
    }

    /**
     * 作用：返回边缘检测之后的最大矩形轮廓
     *
     * @param cannyMat Canny之后的mat矩阵
     * @return
     */
    private RotatedRect findMaxRect(Mat cannyMat) {
        //边缘检测
        cannyMat = OpenCVUtil.canny(cannyMat);
//        Imgproc.dilate(cannyMat, cannyMat, new Mat(), new Point(-1, -1), 1, 1, new Scalar(0.5));
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();

        //RETR_EXTERNAL表示只检测外轮廓
//        Imgproc.findContours(cannyMat, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(0, 0));
//        Imgproc.findContours(cannyMat, contours, hierarchy, RETR_EXTERNAL, CHAIN_APPROX_SIMPLE);

        //RETR_CCOMP建立两个等级的轮廓，上面的一层为外边界，里面的一层为内孔的边界信息。如果内孔内还有一个连通物体，这个物体的边界也在顶层。
        Imgproc.findContours(cannyMat, contours, hierarchy, Imgproc.RETR_CCOMP, CHAIN_APPROX_SIMPLE, new Point(-1, -1));//20.png可以被倾斜校正

        //找出匹配到的最大轮廓
        double area = Imgproc.boundingRect(contours.get(0)).area();
        int index = 0;

        for (int i = 0; i < contours.size(); i++) {
            double tempArea = Imgproc.boundingRect(contours.get(i)).area();
            if (tempArea > area) {
                area = tempArea;
                index = i;
            }
        }

        MatOfPoint2f matOfPoint2f = new MatOfPoint2f(contours.get(index).toArray());
        RotatedRect rect = Imgproc.minAreaRect(matOfPoint2f);
        return rect;
    }

    /**
     * 根据二值化图片进行膨胀与腐蚀
     *
     * @param src 需膨胀腐蚀处理的灰度化后的Mat矩阵图像
     * @return
     */
    private Mat preprocess(Mat src) {
        //1.Sobel算子，x方向求梯度
        Mat sobel = new Mat();
        Imgproc.Sobel(src, sobel, 0, 1, 0, 3);
        imshow("x", sobel);

        //2.二值化
        Mat binaryImage = new Mat();
        Imgproc.threshold(sobel, binaryImage, 0, 255, Imgproc.THRESH_OTSU | Imgproc.THRESH_BINARY);
        // binaryImage = binaryzation(src);

        //3.腐蚀和膨胀操作核设定
        Mat element1 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(24, 9));
        //设置高度大小可以控制上下行的膨胀程度，例如3比4的区分能力更强,但也会造成漏检
        Mat element2 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(26, 9));

        //4.膨胀一次，让轮廓突出
        Mat dilate1 = new Mat();
        // Imgproc.dilate(binaryImage, dilate1, element2);
        Imgproc.dilate(binaryImage, dilate1, element2, new Point(-1, -1), 1, 1, new Scalar(1));

        //5.腐蚀一次，去掉细节，表格线等。这里去掉的是竖直的线
        Mat erode1 = new Mat();
        Imgproc.erode(dilate1, erode1, element1);

        //6.再次膨胀，让轮廓明显一些
        Mat dilate2 = new Mat();
        // Imgproc.dilate(erode1, dilate2, element2);
        Imgproc.dilate(erode1, dilate2, element2, new Point(-1, -1), 1, 1, new Scalar(1));

        return dilate2;
    }

    /**
     * 作用：获取文字区域矩形框
     *
     * @param img 膨胀与腐蚀后的Mat矩阵图像
     * @return
     */
    public List<RotatedRect> findTextRegionRect(Mat img) {
        //保存姓名、名族、地址、身份证号信息的矩形框
        List<RotatedRect> rects = new ArrayList<>();
        //保存性别、名族、出生年月日信息的矩形框，并将名族信息矩形框添加到rects中
        // List<RotatedRect> _rects = new ArrayList<>();

        //查找轮廓
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();

        //TODO: 边缘提取
        //image: 已经二值化的原图
        //contours: 参数为检测的轮廓数组，每一个轮廓用一个MatOfPoint类型的List表示
        //hiararchy: 参数和轮廓个数相同，每个轮廓contours[i]对应4个hierarchy元素hierarchy[i][0] ~hierarchy[i][3]，分别表
        // 示后一个轮廓、前一个轮廓、父轮廓、内嵌轮廓的索引编号，如果没有对应项，该值设置为负数。
        //
        //mode：提取模式.
        //CV_RETR_EXTERNAL - 只提取最外层的轮廓
        //CV_RETR_LIST - 提取所有轮廓，并且放置在 list 中
        //CV_RETR_CCOMP - 提取所有轮廓，并且将其组织为两层的 hierarchy: 顶层为连通域的外围边界，次层为洞的内层边界。
        //CV_RETR_TREE - 提取所有轮廓，并且重构嵌套轮廓的全部 hierarchy
        //
        //method：逼近方法 (对所有节点, 不包括使用内部逼近的 CV_RETR_RUNS).
        //CV_CHAIN_CODE - Freeman 链码的输出轮廓. 其它方法输出多边形(定点序列).
        //CV_CHAIN_APPROX_NONE - 将所有点由链码形式翻译(转化）为点序列形式
        //CV_CHAIN_APPROX_SIMPLE - 压缩水平、垂直和对角分割，即函数只保留末端的象素点;
        //CV_CHAIN_APPROX_TC89_L1,
        //CV_CHAIN_APPROX_TC89_KCOS - 应用 Teh-Chin 链逼近算法. CV_LINK_RUNS - 通过连接为 1 的水平碎片使用完全不同的轮廓提取算法。
        // 仅有 CV_RETR_LIST 提取模式可以在本方法中应用.
        //
        //offset：
        //每一个轮廓点的偏移量. 当轮廓是从图像 ROI 中提取出来的时候，使用偏移量有用，因为可以从整个图像上下文来对轮廓做分析.

        // Imgproc.findContours(img, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(-1, -1));//11.png不能被倾斜校正
        Imgproc.findContours(img, contours, hierarchy, Imgproc.RETR_CCOMP, CHAIN_APPROX_SIMPLE, new Point(-1, -1));//11.png可以被倾斜校正
        // Imgproc.findContours(img, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(0, 0));//11.png不能被倾斜校正

        int img_width = img.width();
        int img_height = img.height();
        int size = contours.size();

        //身份证号宽度
        RotatedRect idRect = Imgproc.minAreaRect(new MatOfPoint2f(contours.get(0).toArray()));
        int idWidth = idRect.boundingRect().width;

        //筛选面积小的矩形
        for (int i = 0; i < size; i++) {
            // 计算轮廓面积
            //contour，输入的二维点集（轮廓顶点），可以是 vector 或 Mat 类型。
            //oriented，面向区域标识符。有默认值 false。若为 true，该函数返回一个带符号的面积值，
            // 正负取决于轮廓的方向（顺时针还是逆时针）。若为 false，表示以绝对值返回。
            double area = Imgproc.contourArea(contours.get(i));
            if (area < 600)//原来是1000
                continue;

            //轮廓近似，approxPolyDP函数
            //arcLength 函数用于计算封闭轮廓的周长或曲线的长度。
            //curve，输入的二维点集（轮廓顶点），可以是 vector 或 Mat 类型。
            //closed，用于指示曲线是否封闭。
            double epsilon = 0.001 * Imgproc.arcLength(new MatOfPoint2f(contours.get(i).toArray()), true);
            MatOfPoint2f approxCurve = new MatOfPoint2f();
            //以指定的精度近似生成多边形曲线。
            //函数逼近一条曲线或另一条曲线/顶点较少的多边形，使它们之间的距离小于或等于指定的精度。它使用Douglas-Peucker算法
            //curve, 输入的点集（存储在std::vector或Mat中的二维点的输入向量）
            //approxCurve, 输出的点集，当前点集是能最小包容指定点集的。draw出来即是一个多边形；
            //epsilon, 指定的精度，也即是原始曲线与近似曲线之间的最大距离。
            //closed, 若为true,则说明近似曲线是闭合的，它的首位都是相连，反之，若为false，则断开。
            Imgproc.approxPolyDP(new MatOfPoint2f(contours.get(i).toArray()), approxCurve, epsilon, true);

            //找到最小矩形
            RotatedRect rect = Imgproc.minAreaRect(new MatOfPoint2f(contours.get(i).toArray()));
            //计算高和宽
            int m_width = rect.boundingRect().width;
            int m_height = rect.boundingRect().height;

            System.out.println("width = " + m_width);

            if (m_height < 20)
                continue;
//            if (m_width < m_height * 1.2)
//                continue;

            //过滤太窄的矩形，留下扁的
            if (m_width * 1.2 < m_height)
                continue;
            if (img_width == rect.boundingRect().br().x)
                continue;
            if (img_height == rect.boundingRect().br().y)
                continue;

            //将符合条件的rect添加到rects集合中
            rects.add(rect);
        }

        //身份证号矩形框在rects中的索引下标
        int index = 0;
        //遍历找到身份证矩形框的宽度大小及在rects中的索引下标index
        for (int i = 0; i < rects.size(); i++) {
            int tempIdWidth = rects.get(i).boundingRect().width;
            if (tempIdWidth > idWidth) {
                idWidth = tempIdWidth;
                index = i;
            }
        }
        System.out.println("身份证号下标：" + index);

        //将身份证矩形框存储到索引为0的位置
        if (index != 0) {
            RotatedRect rotatedRect = rects.get(index);
            rects.set(index, rects.get(0));
            rects.set(0, rotatedRect);
            index = 0;
        }
        System.out.println("修改索引后的身份证号下标：" + index);

        //如果身份证号周围有矩形框（公民身份证号码文本矩形框），则将其从rects中移除
        while (idWidth == rects.get(index).boundingRect().width) {
            if (Math.abs(rects.get(index).center.y - rects.get(index + 1).center.y) < 10) {
                rects.remove(index + 1);
            }
            break;
        }

        //使用下面的迭代器实现循环rects并删除rects的元素
        Iterator<RotatedRect> iterator = rects.iterator();
        while (iterator.hasNext()) {
            RotatedRect rect = iterator.next();
            //删除身份证号矩形框右边的矩形框，删除身份证号码下边的矩形框
            if (rect.center.x > rects.get(index).center.x || rect.center.y > rects.get(index).center.y)
                iterator.remove();
        }

        return rects;
    }

    private List<BufferedImage> getImageList(BufferedImage originalBufferedImage) {
        // 利用 OpenCV 对图像进行处理
        //TODO: 将bufferedImage转换为Mat矩阵图像
        Mat image = OpenCVUtil.BufImg2Mat(originalBufferedImage);
        //TODO: 倾斜校正并标准化
        Mat correctedImg = this.imgCorrection(image);
        //TODO: 将倾斜校正标准化后的Mat矩阵图像转换为BufferedImage格式
        BufferedImage newBufferedImage = OpenCVUtil.Mat2BufImg(correctedImg, fileExtension);

        //OpenCVUtil.bufferedImageToFile(newBufferedImage, "D:\\Desktop\\aaa\\a1" + fileExtension);

        //TODO: 调节亮度
        int brightness = this.imageBrightness(newBufferedImage);
        BufferedImage brightnessImg;
        //如果亮度>180，则亮度减少60
        if (brightness > 180)
            brightnessImg = this.imageBrightness(newBufferedImage, -60);
        else
            brightnessImg = newBufferedImage;

        //OpenCVUtil.bufferedImageToFile(brightnessImg, "D:\\Desktop\\aaa\\a2" + fileExtension);

        //TODO: 灰度化处理 1最大值法，2最小值法，3均值法，4加权法
        //BufferedImage grayImage = this.gray(brightnessImg);
        BufferedImage grayImage = this.grayImage(brightnessImg, 1);

        //OpenCVUtil.bufferedImageToFile(grayImage, "D:\\Desktop\\aaa\\a3" + fileExtension);

        //TODO: 灰度化后的图片转换为Mat矩阵图像
        Mat matImg = OpenCVUtil.BufImg2Mat(grayImage);

        //TODO: 均值漂移滤波
        //opencv非局部均值去噪（需要三通道的Mat图像）--去噪后仍为三通道
        Mat denoiseImg = OpenCVUtil.pyrMeanShiftFiltering(matImg);
        //TODO: opencv灰度化--转为单通道
        Mat grayImg = OpenCVUtil.gray(denoiseImg);
        // imshow("grayImg", grayImg);

        //TODO: 膨胀与腐蚀
        Mat dilationImg = this.preprocess(grayImg);
        // imshow("dilation", dilationImg);

        //TODO: 查找和筛选文字区域
//        List<RotatedRect> rects = OpenCVUtil.findTextRegionRect(dilationImg);
        List<RotatedRect> rects = findTextRegionRect(dilationImg);
        if (rects.size() > 10)
            logger.info("身份证信息文本框获取错误！！！");

        //用红线画出找到的轮廓
//        for (RotatedRect rotatedRect : rects) {
//            Point[] rectPoint = new Point[4];
//            rotatedRect.points(rectPoint);
//            for (int j = 0; j <= 3; j++) {
//                Imgproc.line(correctedImg, rectPoint[j], rectPoint[(j + 1) % 4], new Scalar(0, 0, 255), 2);
//            }
//        }
        //显示带轮廓的图像
        // imshow("Contour Image", correctedImg);

        logger.info("rects.size:" + rects.size());

        //TODO: 截取并显示轮廓图片
        List<Mat> matList = new ArrayList<>();
        for (int i = 0; i < rects.size(); i++) {
            //裁剪识别区域
            Mat dst = OpenCVUtil.cropImage(matImg, rects.get(i).boundingRect());
            matList.add(dst);
            // imshow("croppedImg" + i, dst);
        }

        List<BufferedImage> result = new ArrayList<>();
        for (int i = matList.size() - 1; i >= 0; i--) {
            BufferedImage tmpBufferedImage = OpenCVUtil.Mat2BufImg(matList.get(i), fileExtension);
            result.add(tmpBufferedImage);
        }

        return result;
    }

    @Override
    public IDCard doRecognition(BufferedImage src) {

        List<BufferedImage> images = getImageList(src);

//        AtomicInteger idx = new AtomicInteger();
//        images.forEach((item) -> {
//            OpenCVUtil.bufferedImageToFile(item, "D:\\Desktop\\aaa\\" + idx + fileExtension);
//            idx.getAndIncrement();
//        });

        IDCard result = new IDCard();

        logger.info("识别结果如下：");

        //识别姓名
        this.setLanguage("chi_sim");    //中英文混合识别需用 + 分隔，chi_sim：简体中文，eng：英文
        String name = this.doOCR(images.get(0)).replaceAll("[^\\u4e00-\\u9fa5]", "").trim();
        logger.info("姓名：" + name);
        result.setName(name);

        //识别身份证号
        this.setLanguage("eng");
        String idNumber = this.doOCR(images.get(images.size() - 1)).replaceAll("[^0-9xX]", "");

        if (idNumber.length() != 18) {
            logger.warn("身份证号码识别有误: [{}]", idNumber);
        }

        //根据身份证号获取性别
        char sex;
        if (Integer.parseInt(idNumber.substring(16, 17)) % 2 == 0) {
            sex = '女';
        } else {
            sex = '男';
        }
        logger.info("性别：" + sex);
        result.setSex(sex);

        //民族
        this.setLanguage("chi_sim");
        String nation = this.doOCR(images.get(2)).trim();
        if (nation.equals("汊") || (nation.equals("池")) || nation.contains("汉"))
            nation = "汉";
        logger.info("名族：" + nation);
        result.setNation(nation);

        //根据身份证号获得出生年、月、日
        int year = Integer.parseInt(idNumber.substring(6, 10));
        int month = Integer.parseInt(idNumber.substring(10, 12));
        int day = Integer.parseInt(idNumber.substring(12, 14));
        logger.info("出生：" + year + "年" + month + "月" + day + "日");
        result.setYear(year);
        result.setMonth(month);
        result.setDay(day);

        //识别地址
        String address = "";
        this.setLanguage("chi_sim");
        for (int i = images.size() - 3; i < images.size() - 1; i++) {
            address += this.doOCR(images.get(i)).trim();
        }
        address = address.replaceAll("[^\\s\\u4e00-\\u9fa5\\-0-9]+", "").replaceAll(" +", "").trim();
        logger.info("地址：" + address);
        result.setAddress(address);

        logger.info("身份证号：" + idNumber);
        result.setIdNumber(idNumber);

        return result;
    }
}
