package com.erdos.coal.ocr.entity;

import java.io.Serializable;

public class MonggoliaHealthCode implements Serializable {

    private String aTime = ""; // 时间
    private String bName = ""; // 姓名
    private String cCheckAcid = ""; // 核酸检测结果
    private String dCheckTime = ""; // 核酸检测时间
    private String eVacc = ""; // 疫苗接种结果
    private String fTrip = ""; // 大数据行程卡
    private String gFile = ""; // 文件名

    public String getaTime() {
        return aTime;
    }

    public MonggoliaHealthCode setaTime(String aTime) {
        this.aTime = aTime;
        return this;
    }

    public String getbName() {
        return bName;
    }

    public MonggoliaHealthCode setbName(String bName) {
        this.bName = bName;
        return this;
    }

    public String getcCheckAcid() {
        return cCheckAcid;
    }

    public MonggoliaHealthCode setcCheckAcid(String cCheckAcid) {
        this.cCheckAcid = cCheckAcid;
        return this;
    }

    public String getdCheckTime() {
        return dCheckTime;
    }

    public MonggoliaHealthCode setdCheckTime(String dCheckTime) {
        this.dCheckTime = dCheckTime;
        return this;
    }

    public String geteVacc() {
        return eVacc;
    }

    public MonggoliaHealthCode seteVacc(String eVacc) {
        this.eVacc = eVacc;
        return this;
    }

    public String getfTrip() {
        return fTrip;
    }

    public MonggoliaHealthCode setfTrip(String fTrip) {
        this.fTrip = fTrip;
        return this;
    }

    public String getgFile() {
        return gFile;
    }

    public MonggoliaHealthCode setgFile(String gFile) {
        this.gFile = gFile;
        return this;
    }

    @Override
    public String toString() {
        return "MonggoliaHealthCode{" +
                "aTime='" + aTime + '\'' +
                ", bName='" + bName + '\'' +
                ", cCheckAcid='" + cCheckAcid + '\'' +
                ", dCheckTime='" + dCheckTime + '\'' +
                ", eVacc='" + eVacc + '\'' +
                ", fTrip='" + fTrip + '\'' +
                ", gFile='" + gFile + '\'' +
                '}';
    }
}
