package com.erdos.coal.ocr.parser.healthcode;

import com.erdos.coal.ocr.entity.HealthCode;
import com.erdos.coal.ocr.image.ImageKit;
import com.erdos.coal.ocr.parser.AbstractOcrParser;
import com.erdos.coal.ocr.parser.IOcrParser;

import java.awt.image.BufferedImage;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HealthCodeParser extends AbstractOcrParser implements IOcrParser<HealthCode> {

    private final static String fileExtension = ".jpg";

    public HealthCodeParser(String lanPath) {
        super(lanPath);
        this.setLanguage("chi_sim");
    }

    @Override
    public HealthCode doRecognition(BufferedImage src) {
        System.out.println("开始");
        long start = System.currentTimeMillis();

        ImageKit imageKit = ImageKit.newInstance();

        // 二维码颜色
        boolean isGreenQRCode;
        int w = src.getWidth();
        int h = src.getHeight();
        BufferedImage img = imageKit.cutImage(src, (w / 3), (h / 2), w - (2 * (w / 3)), 10);
        //        try {
        //             imageKit.saveImageFile(img, "D:\\Desktop\\aaa\\xx.jpg");
        //        } catch (IOException e) {
        //            e.printStackTrace();
        //        }
//        Color color = imageKit.colorRecognition(img); // 图片主色调
        //Color color = imageKit.getAvgRGB(img); // 图片颜色平均值
        isGreenQRCode = imageKit.getColorRGBValue(img) == 1;

        HealthCode healthCode1, healthCode2;
        try {
            // 图片预处理, 灰度化，二值化，降噪处理
            // setGrayStatus(1); // 针对灰度化处理 1最大值法，2最小值法，3均值法，4加权法
            // setBinarySW(160); // 二值化阀值

            // 识别
            BufferedImage image1, image2;

            //第一次
            image1 = imageKit.grayImage(src, 4);// 灰度化
            image1 = imageKit.binaryImage(image1, 108); // 二值化
            image1 = imageKit.denoiseImage(image1); // 降噪
            healthCode1 = this.doParse(this.doOCR(image1));

            //第二次
            image2 = imageKit.grayImage(src, 2);// 灰度化
            image2 = imageKit.binaryImage(image2, 135); // 二值化
            image2 = imageKit.denoiseImage(image2); // 降噪
            healthCode2 = this.doParse(this.doOCR(image2));

            if (healthCode1 != null && healthCode2 != null) {
                healthCode2.setTime(healthCode1.getTime());
                healthCode2.setAcidTime(healthCode1.getAcidTime());

                healthCode2.setGreenCode(isGreenQRCode);
                return healthCode2;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("共耗时：" + (System.currentTimeMillis() - start));
        return null;
    }

    private HealthCode doParse(String raw) {
        if (Objects.isNull(raw)) return null;

        System.out.print(raw);
        System.out.println("-----------------------------------------------------------------------------------------");

        List<String> rawList = Arrays.asList(raw.split("\n"));

        HealthCode healthCode = new HealthCode();

        for (int row = 0; row < rawList.size(); row++) {
            // 按行处理
            String s = rawList.get(row);
            s = s.replaceAll(" ", ""); // 去除空格

            // TODO: 时间 ----------------------------------------------------------------------------------------------
            String rTime;
            if (row < 10) {
                // 或者按当前年月去匹配 ???
                if (s.contains("2021") || s.contains("2022") || s.contains("2023") || s.contains("2024")) {
                    rTime = s.replaceAll("[^0-9]", "");
                    //codeInfo.setaTime(rTime);
                    healthCode.setTime(rTime);
                }
            }

            //TODO: 姓名 -----------------------------------------------------------------------------------------------
            //吕 东 清 内 蒙 古 自 治 区
            if (s.contains("内") && s.contains("古自治区")) { //内蒙古自治区,内蔡古自治区,内敲古自治区
                String rName; // // 做个容错处理 //张 建 富 内 蔡 古 自 治 区
                rName = s.substring(0, s.indexOf("古自治区") - 2);
                //rName = getChinese(rName);
                //codeInfo.setbName(rName);
                healthCode.setName(rName);
            }

            //TODO: 核酸检测结果 ---------------------------------------------------------------------------------------
            //核 酸 检 测 结 果 : 阴 性
            String rCheckAcid;
            if (s.contains("核酸检测结果")) {
                rCheckAcid = s.replaceAll("核酸检测结果", "")
                        .replaceAll(":", "")
                        .replaceAll(";", "")
                        .trim();
                rCheckAcid = s.contains("阴") ? "阴性" : (s.contains("未") ? "未查到" : rCheckAcid);
                //codeInfo.setcCheckAcid(rCheckAcid);
                healthCode.setAcidResult(rCheckAcid);
            }

            //TODO: 检测时间 -------------------------------------------------------------------------------------------
            //( 检 测 时 间 : 2021-10-30 23:05:00)
            if (s.contains("检测") || s.contains("时间")) {
                String rCheckTime;
                rCheckTime = s.replaceAll("检测时间", "")
                        .replaceAll(":", "")
                        .replaceAll("\\(", "").replaceAll("\\)", "")
                        .replaceAll("\\（", "").replaceAll("\\）", "")
                        .replaceAll("[^0-9]", "")
                        .trim();
                //codeInfo.setdCheckTime(rCheckTime);
                healthCode.setAcidTime(rCheckTime);
            }

            //TODO: 疫苗接种结果 ---------------------------------------------------------------------------------------
            //疫 苗 接 种 结 果 : 已 全 程 接 种 e
            if (s.contains("接种结果")) {
                String rVacc;
                rVacc = s.substring(s.indexOf("接种结果") + 4)
                        .replaceAll(":", "").trim();
                rVacc = rVacc.contains("已") ? "已全程接种" : (rVacc.contains("未") ? "未查到" : rVacc);
                //codeInfo.seteVacc(rVacc);
                healthCode.setVaccination(rVacc);
            }

//            //TODO: 14天内 到访过 中高风险地区所在的城市 -------------------------------------------------------------------
//            //14 天 内 到 访 过 中 高 风 险 地 区 所 在 的 城 市
//            if (s.contains("14天内") && s.contains("中高风险地区所在的城市")) {
//                String rTrip; // 大数据行程卡
//                rTrip = s.replaceAll("14天内", "")
//                        .replaceAll("“", "").replaceAll("”", "")
//                        .replaceAll("中高风险地区所在的城市", "")
//                        .trim();
//                rTrip = s.contains("未") ? "未到访过" : rTrip;
//                codeInfo.setfTrip(rTrip);
//            }
        }

        return healthCode;
    }

    private static String getChinese(String paramValue) {
        String regex = "([\u4e00-\u9fa5]+)";
        StringBuilder str = new StringBuilder();
        Matcher matcher = Pattern.compile(regex).matcher(paramValue);
        while (matcher.find()) {
            str.append(matcher.group(0));
        }
        return str.toString();
    }

}
