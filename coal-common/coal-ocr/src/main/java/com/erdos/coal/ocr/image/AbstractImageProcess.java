package com.erdos.coal.ocr.image;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.List;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

abstract class AbstractImageProcess implements IImageHandler {

    public BufferedImage grayImage(BufferedImage image, int grayStatus) {

        int width = image.getWidth();
        int height = image.getHeight();

        BufferedImage grayImage = new BufferedImage(width, height, image.getType());
        //BufferedImage grayImage = new BufferedImage(width, height,  BufferedImage.TYPE_BYTE_GRAY);
        for (int i = 0; i < width; i++) {
            for (int j = 0; j < height; j++) {
                int color = image.getRGB(i, j);
                final int r = (color >> 16) & 0xff;
                final int g = (color >> 8) & 0xff;
                final int b = color & 0xff;
                int gray = 0;
                if (grayStatus == 1) {
                    gray = Math.max(Math.max(r, g), b); //最大值法灰度化
                } else if (grayStatus == 2) {
                    gray = Math.min(Math.min(r, g), b); //最小值法灰度化
                } else if (grayStatus == 3) {
                    gray = (r + g + b) / 3; //均值法灰度化
                } else if (grayStatus == 4) {
                    gray = (int) (0.3 * r + 0.59 * g + 0.11 * b);//加权法灰度化
                }
                // System.out.println("像素坐标：" + " x=" + i + "   y=" + j + "   灰度值=" + gray);
                grayImage.setRGB(i, j, this.colorToRGB(0, gray, gray, gray));
            }
        }
        return grayImage;
    }

    /**
     * 颜色分量转换为RGB值
     */
    private int colorToRGB(int alpha, int red, int green, int blue) {
        int newPixel = 0;
        newPixel += alpha;
        newPixel = newPixel << 8;
        newPixel += red;
        newPixel = newPixel << 8;
        newPixel += green;
        newPixel = newPixel << 8;
        newPixel += blue;
        return newPixel;
    }

    // =================================================================================================================
    public BufferedImage binaryImage(BufferedImage image, int binarySW) {
        int w = image.getWidth();
        int h = image.getHeight();
        float[] rgb = new float[3];
        double[][] position = new double[w][h];
        int black = new Color(0, 0, 0).getRGB();
        int white = new Color(255, 255, 255).getRGB();
        BufferedImage bi = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);

        for (int x = 0; x < w; x++) {
            for (int y = 0; y < h; y++) {
                int pixel = image.getRGB(x, y);
                rgb[0] = (pixel & 0xff0000) >> 16;
                rgb[1] = (pixel & 0xff00) >> 8;
                rgb[2] = (pixel & 0xff);
                float avg = (rgb[0] + rgb[1] + rgb[2]) / 3;
                position[x][y] = avg;
            }
        }
        // 阈值，白底黑字还是黑底白字，建议白底黑字
        // 默认 192;
        int SW = binarySW;
        for (int x = 0; x < w; x++) {
            for (int y = 0; y < h; y++) {
                if (position[x][y] < SW) {
                    bi.setRGB(x, y, black);
                } else {
                    bi.setRGB(x, y, white);
                }

            }
        }
        return bi;
    }

    // =================================================================================================================

    public BufferedImage denoiseImage(BufferedImage image) {
        int w = image.getWidth();
        int h = image.getHeight();
        int white = new Color(255, 255, 255).getRGB();

        if (isWhite(image.getRGB(1, 0)) && isWhite(image.getRGB(0, 1)) && isWhite(image.getRGB(1, 1))) {
            image.setRGB(0, 0, white);
        }
        if (isWhite(image.getRGB(w - 2, 0)) && isWhite(image.getRGB(w - 1, 1)) && isWhite(image.getRGB(w - 2, 1))) {
            image.setRGB(w - 1, 0, white);
        }
        if (isWhite(image.getRGB(0, h - 2)) && isWhite(image.getRGB(1, h - 1)) && isWhite(image.getRGB(1, h - 2))) {
            image.setRGB(0, h - 1, white);
        }
        if (isWhite(image.getRGB(w - 2, h - 1)) && isWhite(image.getRGB(w - 1, h - 2)) && isWhite(image.getRGB(w - 2, h - 2))) {
            image.setRGB(w - 1, h - 1, white);
        }

        for (int x = 1; x < w - 1; x++) {
            int y = 0;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x - 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x - 1, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y + 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }
        for (int x = 1; x < w - 1; x++) {
            int y = h - 1;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x - 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x - 1, y - 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }

        for (int y = 1; y < h - 1; y++) {
            int x = 0;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x + 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y + 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }

        for (int y = 1; y < h - 1; y++) {
            int x = w - 1;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x - 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y - 1))) {
                    size++;
                }
                // 斜上下为空时，去掉此点
                if (isWhite(image.getRGB(x - 1, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x - 1, y - 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }

        // 降噪，以1个像素点为单位
        for (int y = 1; y < h - 1; y++) {
            for (int x = 1; x < w - 1; x++) {
                if (isBlack(image.getRGB(x, y))) {
                    int size = 0;
                    //上下左右均为空时，去掉此点
                    if (isWhite(image.getRGB(x - 1, y))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x + 1, y))) {
                        size++;
                    }
                    //上下均为空时，去掉此点
                    if (isWhite(image.getRGB(x, y + 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x, y - 1))) {
                        size++;
                    }
                    //斜上下为空时，去掉此点
                    if (isWhite(image.getRGB(x - 1, y + 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x + 1, y - 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x + 1, y + 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x - 1, y - 1))) {
                        size++;
                    }
                    if (size >= 8) {
                        image.setRGB(x, y, white);
                    }
                }
            }
        }
        return image;
    }

    private boolean isBlack(int colorInt) {
        Color color = new Color(colorInt);
        if (color.getRed() + color.getGreen() + color.getBlue() <= 300) {
            return true;
        }
        return false;
    }

    private boolean isWhite(int colorInt) {
        Color color = new Color(colorInt);
        if (color.getRed() + color.getGreen() + color.getBlue() > 300) {
            return true;
        }
        return false;
    }

    private int isBlack(int colorInt, int whiteThreshold) {
        final Color color = new Color(colorInt);
        if (color.getRed() + color.getGreen() + color.getBlue() <= whiteThreshold) {
            return 1;
        }
        return 0;
    }

    // =================================================================================================================

    public BufferedImage rotateImage(BufferedImage image, int angel) {
        int src_width = image.getWidth(null);
        int src_height = image.getHeight(null);
        // calculate the new image size
        Rectangle rect_des = CalcRotatedSize(new Rectangle(new Dimension(src_width, src_height)), angel);
        BufferedImage res = new BufferedImage(rect_des.width, rect_des.height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = res.createGraphics();
        // transform
        g2.translate((rect_des.width - src_width) / 2, (rect_des.height - src_height) / 2);
        g2.rotate(Math.toRadians(angel), src_width / 2, src_height / 2);

        g2.drawImage(image, null, null);
        return res;
    }

    /**
     * 计算旋转参数
     *
     * @param src   范围
     * @param angel 角度
     * @return
     */
    private Rectangle CalcRotatedSize(Rectangle src, int angel) {
        // 如果大于90，需要做转换处理
        if (angel >= 90) {
            if (angel / 90 % 2 == 1) {
                int temp = src.height;
                src.height = src.width;
                src.width = temp;
            }
            angel = angel % 90;
        }

        double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
        double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
        double angel_alpha = (Math.PI - Math.toRadians(angel)) / 2;
        double angel_dalta_width = Math.atan((double) src.height / src.width);
        double angel_dalta_height = Math.atan((double) src.width / src.height);

        int len_dalta_width = (int) (len * Math.cos(Math.PI - angel_alpha - angel_dalta_width));
        int len_dalta_height = (int) (len * Math.cos(Math.PI - angel_alpha - angel_dalta_height));
        int des_width = src.width + len_dalta_width * 2;
        int des_height = src.height + len_dalta_height * 2;
        return new Rectangle(new Dimension(des_width, des_height));
    }

    // =================================================================================================================

    public BufferedImage zoomImage(BufferedImage image, int w, int h) {
        //新生成结果图片
        BufferedImage zoomImg = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
        //缩放 getScaledInstance(width,height,hints)
        Image img = image.getScaledInstance(w, h, Image.SCALE_SMOOTH);
        zoomImg.getGraphics().drawImage(img, 0, 0, null);
        return zoomImg;
    }

    // =================================================================================================================

    public BufferedImage cutImage(BufferedImage image, int x, int y, int w, int h) {
        return image.getSubimage(x, y, w, h);
    }

    // =================================================================================================================

    public BufferedImage waterMarkText(BufferedImage image, String text, int x, int y, Font font, Color color) {
        BufferedImage dstImage = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());
        dstImage.setData(image.getData());
        Graphics2D graphics2D = dstImage.createGraphics();
        // 设置"抗锯齿"的属性
        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        // 设置字体类型和大小
        graphics2D.setFont(font);
        // 设置颜色
        graphics2D.setColor(color);
        // 添加文字
        graphics2D.drawString(text, x, y);
        graphics2D.dispose();
        return dstImage;
    }

    public BufferedImage waterMarkImage(BufferedImage image, BufferedImage waterImage, int x, int y, float alpha) {
        BufferedImage dstImage = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());
        dstImage.setData(image.getData());
        Graphics2D graphics2D = dstImage.createGraphics();
        // 设置设置透明度为0.5
        graphics2D.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));
        // 添加水印
        graphics2D.drawImage(waterImage, x, y, null);
        // 关闭透明度
        // graphics2D.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
        graphics2D.dispose();
        return dstImage;
    }

    // =================================================================================================================
    //输入一张图片 返回该图片的均值色彩
    public Color getAvgRGB(BufferedImage src) {
        int w = src.getWidth();
        int h = src.getHeight();
        float[] dots = new float[]{0.15f, 0.35f, 0.5f, 0.7f, 0.85f};
        int R = 0;
        int G = 0;
        int B = 0;
        for (float dw : dots) {
            for (float dh : dots) {
                int rgbVal = src.getRGB((int) (w * dw), (int) (h * dh));
                //Color color = ImgUtil.getColor(rgbVal);
                Color color = new Color(rgbVal, true);
                R += color.getRed();
                G += color.getGreen();
                B += color.getBlue();
            }
        }
        int cn = dots.length * dots.length;
        return new Color(R / cn, G / cn, B / cn);
    }

    //根据平均值判断 红、黄、绿
    public int getColorRGBValue(BufferedImage src) {
        Color color = this.getAvgRGB(src);
        //红:java.awt.Color[r=247,g=132,b=140]
        //黄:java.awt.Color[r=249,g=226,b=159]
        //绿:java.awt.Color[r=186,g=225,b=193]
        int r = color.getRed();
        int g = color.getGreen();
        int b = color.getBlue();
        if (g >= r && g >= b) { // 绿大于红,并且绿大于蓝
            // 绿
            return 1;
        } else if (r > g && r >= b && (Math.abs(r - g) > Math.abs(g - b))) { // 红大于绿,并且红大于蓝,并且红减绿的绝对值大于绿减蓝的绝对值
            //红
            return 2;
        } else if (r > g && r >= b && (Math.abs(r - g) < Math.abs(g - b))) { // 红大于绿,并且红大于蓝,并且红减绿的绝对值小于绿减蓝的绝对值
            //黄
            return 3;
        } else {
            return 0;
        }
    }

    // =================================================================================================================

    // 颜色标准数（固定差值）
    private static float colorNum = 51.0f;

    // 颜色标准（倍数关系）
    private static Map<Integer, String> colorStandard = new HashMap<>();

    static {
        colorStandard.put(0, "00");
        colorStandard.put(1, "33");
        colorStandard.put(2, "66");
        colorStandard.put(3, "99");
        colorStandard.put(4, "cc");
        colorStandard.put(5, "ff");
    }

    // 十进制转十六进制
    public String tenToHex(int x) {
        return Integer.toHexString(x);
    }

    // 十六进制转十进制
    public Integer hexToTen(String hex) {
        return Integer.parseInt(hex, 16);
    }

    // 将十进制形式rgb转为符合标准颜色的十六进制字符串形式
    private String changeRgb(int r, int g, int b) {
        // 通过颜色标准数计算颜色标准key
        int rk = Math.round(Float.parseFloat(r + "") / colorNum);
        int gk = Math.round(Float.parseFloat(g + "") / colorNum);
        int bk = Math.round(Float.parseFloat(b + "") / colorNum);
        // 通过颜色标准key取出标准颜色
        String rs = colorStandard.get(rk);
        String gs = colorStandard.get(gk);
        String bs = colorStandard.get(bk);
        return rs + gs + bs;
    }

    // 将BufferImage取出来的rgb int 值转换为三位数的rgb
    private int[] changeToRgb(int pixel) {
        int[] rgb = new int[3];
        rgb[0] = (pixel & 0xff0000) >> 16;
        rgb[1] = (pixel & 0xff00) >> 8;
        rgb[2] = (pixel & 0xff);
        return rgb;
    }

    /**
     * 判断图片主色调
     *
     * @param bi
     * @return
     */
    public Color colorRecognition(BufferedImage bi) {
        int[] rgb;
        Map<String, Integer> colorCount = new HashMap<>();
        // 统计图中颜色标准的数量分布情况
        for (int w = 0; w < bi.getWidth(); w = w + 2) {
            for (int h = 0; h < bi.getHeight(); h = h + 2) {
                // 取出每一个像素点的rgb值
                rgb = changeToRgb(bi.getRGB(w, h));
                // 将rbg转换为符合颜色标准的十六进制形式
                String hexColor = changeRgb(rgb[0], rgb[1], rgb[2]);
                // 统计下每一个颜色对应的像素数量
                Integer count = colorCount.get(hexColor);
                if (count == null) {
                    count = 0;
                }
                count++;
                colorCount.put(hexColor, count);
            }
        }
        Map<Integer, String> colorCountRev = new HashMap<>();
        List<Integer> countList = new ArrayList<>();
        for (String key : colorCount.keySet()) {
            colorCountRev.put(colorCount.get(key), key);
            countList.add(colorCount.get(key));
        }
        // 排序
        Collections.sort(countList);
        int length = countList.size();
        if (length > 6) {
            length = 6;
        }

        // 取出数量最多的前五种颜色
        AtomicInteger red = new AtomicInteger();
        AtomicInteger green = new AtomicInteger();
        AtomicInteger blue = new AtomicInteger();
        String colors = "";
        for (int i = 1; i <= length; i++) {
            colors = String.valueOf(colorCountRev.get(countList.get(countList.size() - i)));
            //输出结果
            // System.out.println(colors);
            //        colorStandard.put(0, "00");
            //        colorStandard.put(1, "33");
            //        colorStandard.put(2, "66");
            //        colorStandard.put(3, "99");
            //        colorStandard.put(4, "cc");
            //        colorStandard.put(5, "ff");
            String finalColors = colors; //339900
            colorStandard.forEach((k, v) -> {
                String sRed = finalColors.substring(0, finalColors.length() - 4); //33
                String sGreen = finalColors.substring(2, finalColors.length() - 2); //99
                String sBlue = finalColors.substring(4); //00
                if (v.equals(sRed)) {
                    red.addAndGet(k);
                }
                if (v.equals(sGreen)) {
                    green.addAndGet(k);
                }
                if (v.equals(sBlue)) {
                    blue.addAndGet(k);
                }
            });
        }
        return new Color(red.get(), green.get(), blue.get());
    }
}
