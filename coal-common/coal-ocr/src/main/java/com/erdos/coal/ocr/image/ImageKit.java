package com.erdos.coal.ocr.image;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;

/**
 * 图片处理
 * 灰度化，二值化，降噪
 * 旋转，裁剪，缩放
 * 添加水印
 */
public class ImageKit extends AbstractImageProcess {

    private ImageKit() {
    }

    public static ImageKit newInstance() {
        return new ImageKit();
    }

    public BufferedImage loadImageFile(File file) throws IOException {
        if (!file.exists())
            throw new FileNotFoundException("文件不存在 " + file.getName());

        return ImageIO.read(file);
    }

    public BufferedImage loadImageFile(String src) throws IOException {
        return this.loadImageFile(new File(src));
    }

    public void saveImageFile(BufferedImage image, String file) throws IOException {
        ImageIO.write(image, "jpg", new File(file));
    }

    // 文字水印
    public final BufferedImage waterMarkText(BufferedImage image, String text, int x, int y) {
        Font font = new Font("微软雅黑", Font.PLAIN, 20);
        Color color = new Color(0, 0, 255);
        return super.waterMarkText(image, text, x, y, font, color);
    }

    // 图片水印
    public final BufferedImage waterMarkImage(BufferedImage image, BufferedImage waterImage, int x, int y) {
        return super.waterMarkImage(image, waterImage, x, y, 0.6f);
    }

    //------------------------------------------------------------------------------------------------------------------

//    public static void main(String[] args) throws Exception {
//
//        String srcFile = "D:\\Desktop\\sfz\\a\\20211102061820oPSRC5DkXa2-X6CGprfYfj6UnkOo.jpg";
//        String dstPath = "D:\\Desktop\\sfz\\tmp\\";
//
//        ImageKit kit = ImageKit.newInstance();
//        BufferedImage src = kit.loadImageFile(srcFile);
//
//        // TODO: 旋转
//        //顺时针旋转90度
//        BufferedImage des11 = kit.rotateImage(src, 90);
//        kit.saveImageFile(des11, dstPath + "90.jpg");
//
//        //顺时针旋转180度
//        BufferedImage des12 = kit.rotateImage(src, 180);
//        kit.saveImageFile(des12, dstPath + "180.jpg");
//
//        //顺时针旋转270度
//        BufferedImage des13 = kit.rotateImage(src, 270);
//        kit.saveImageFile(des13, dstPath + "270.jpg");
//
//        // TODO: 缩放
//        BufferedImage des21 = kit.zoomImage(des13, 3840, 1728); // 放大
//        kit.saveImageFile(des21, dstPath + "270_21.jpg");
//
//        BufferedImage des22 = kit.zoomImage(des13, 120, 54); // 缩小
//        kit.saveImageFile(des22, dstPath + "270_22.jpg");
//
//        // TODO: 裁剪
//        BufferedImage des31 = kit.cutImage(des13, 400, 100, 1100, 680);
//        kit.saveImageFile(des31, dstPath + "270_31.jpg");
//
//        // TODO: 添加文字水印
//        BufferedImage des41 = kit.waterMarkText(des31, "添加的水印", 20, 20);
//        kit.saveImageFile(des41, dstPath + "270_41.jpg");
//
//        // TODO: 添加文字水印
//        BufferedImage des51 = kit.waterMarkImage(des31, des22, 40, 40);
//        kit.saveImageFile(des51, dstPath + "270_51.jpg");
//
//        // TODO: 身份证识别
//        // 图片预处理, 灰度化，二值化，降噪处理
//        BeforeImageHandler beforeHandler = new BeforeImageHandler();
//        // setGrayStatus(1); // 针对灰度化处理 1最大值法，2最小值法，3均值法，4加权法
//        // setBinarySW(160); // 二值化阀值
//        BufferedImage des61 = beforeHandler.handleImage(des31, 2, 160);
//        kit.saveImageFile(des61, dstPath + "270_61.jpg");
//
//        String languagePath = "D:\\tessdata-main\\";
//
//        // TODO: 图片识别
//        OcrRecognitionHandler.newInstance(languagePath, (raw) -> {
//            System.out.println(raw);
//            return null;
//        }).execOCR(des61);
//
//        System.out.println("==End==");
//    }

    public static void main(String[] args) throws IOException {
        ImageKit imageKit = ImageKit.newInstance();

        BufferedImage src = ImageIO.read(new File("D:\\Desktop\\ocr\\sss.jpg"));
        BufferedImage imgRed = imageKit.cutImage(src, 0, 100, 280, 10);
        BufferedImage imgYellow = imageKit.cutImage(src, 280, 100, 280, 10);
        BufferedImage imgGreen = imageKit.cutImage(src, 580, 100, 280, 10);

        System.out.println("绿:" + imageKit.getColorRGBValue(imgGreen));
        System.out.println("红:" + imageKit.getColorRGBValue(imgRed));
        System.out.println("黄:" + imageKit.getColorRGBValue(imgYellow));
    }
}
