package com.erdos.coal.ocr.image;

import com.erdos.coal.ocr.image.ImageKit;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * 图片前置(预处理)理器
 * 现主要做 灰度化，二值化，降噪处理
 */
public class BeforeImageHandler {

    private ImageKit imageKit;

    public BeforeImageHandler() {
        this.imageKit = ImageKit.newInstance();
    }

    /**
     * 灰度化，二值化，降噪
     *
     * @param image
     * @return
     * @throws IOException
     */
    public BufferedImage handleImage(BufferedImage image, int grayStatus, int binarySW) {
        // 灰度化
        image = imageKit.grayImage(image, grayStatus);
        // 二值化
        image = imageKit.binaryImage(image, binarySW);
        // 降噪
        image = imageKit.denoiseImage(image);

        return image;
    }

    public BufferedImage handleImage(String file, int grayStatus, int binarySW) throws IOException {
        BufferedImage image = imageKit.loadImageFile(file);
        return this.handleImage(image, grayStatus, binarySW);
    }

    public void handleImage(String srcFile, int grayStatus, int binarySW, String dstFile) throws IOException {
        BufferedImage image = this.handleImage(srcFile, grayStatus, binarySW);
        ImageIO.write(image, "jpg", new File(dstFile));
    }


}
