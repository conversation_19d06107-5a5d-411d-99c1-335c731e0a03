package com.erdos.coal.ocr.entity;

import java.io.Serializable;

/**
 * 各地健康码
 */
public class HealthCode implements Serializable {

    private String time = ""; //截屏时间
    private String name = ""; //姓名
    private String idno = ""; //身份证号
    private String acidResult = ""; //核酸检测结果
    private String acidTime = ""; //核酸检测时间
    private String vaccination = ""; //疫苗接种情况
    private boolean greenCode = false; // 是否绿码

    public HealthCode() {
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdno() {
        return idno;
    }

    public void setIdno(String idno) {
        this.idno = idno;
    }

    public String getAcidResult() {
        return acidResult;
    }

    public void setAcidResult(String acidResult) {
        this.acidResult = acidResult;
    }

    public String getAcidTime() {
        return acidTime;
    }

    public void setAcidTime(String acidTime) {
        this.acidTime = acidTime;
    }

    public String getVaccination() {
        return vaccination;
    }

    public void setVaccination(String vaccination) {
        this.vaccination = vaccination;
    }

    public boolean isGreenCode() {
        return greenCode;
    }

    public void setGreenCode(boolean greenCode) {
        this.greenCode = greenCode;
    }

    @Override
    public String toString() {
        return "HealthCode{" +
                "time='" + time + '\'' +
                ", name='" + name + '\'' +
                ", idno='" + idno + '\'' +
                ", acidResult='" + acidResult + '\'' +
                ", acidTime='" + acidTime + '\'' +
                ", vaccination='" + vaccination + '\'' +
                ", greenCode=" + greenCode +
                '}';
    }
}
