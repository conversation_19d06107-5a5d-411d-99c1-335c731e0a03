package com.erdos.coal.ocr.utils;

import java.awt.*;
import java.awt.image.BufferedImage;

public class ImageUtils {

    // =================================================================================================================
    public BufferedImage binaryImage(BufferedImage image, int binarySW) {
        int w = image.getWidth();
        int h = image.getHeight();
        float[] rgb = new float[3];
        double[][] position = new double[w][h];
        int black = new Color(0, 0, 0).getRGB();
        int white = new Color(255, 255, 255).getRGB();
        BufferedImage bi = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);

        for (int x = 0; x < w; x++) {
            for (int y = 0; y < h; y++) {
                int pixel = image.getRGB(x, y);
                rgb[0] = (pixel & 0xff0000) >> 16;
                rgb[1] = (pixel & 0xff00) >> 8;
                rgb[2] = (pixel & 0xff);
                float avg = (rgb[0] + rgb[1] + rgb[2]) / 3;
                position[x][y] = avg;
            }
        }
        // 阈值，白底黑字还是黑底白字，建议白底黑字
        // 默认 192;
        int SW = binarySW;
        for (int x = 0; x < w; x++) {
            for (int y = 0; y < h; y++) {
                if (position[x][y] < SW) {
                    bi.setRGB(x, y, black);
                } else {
                    bi.setRGB(x, y, white);
                }

            }
        }
        return bi;
    }

    // =================================================================================================================

    public static BufferedImage denoiseImage(BufferedImage image) {
        int w = image.getWidth();
        int h = image.getHeight();
        int white = new Color(255, 255, 255).getRGB();

        if (isWhite(image.getRGB(1, 0)) && isWhite(image.getRGB(0, 1)) && isWhite(image.getRGB(1, 1))) {
            image.setRGB(0, 0, white);
        }
        if (isWhite(image.getRGB(w - 2, 0)) && isWhite(image.getRGB(w - 1, 1)) && isWhite(image.getRGB(w - 2, 1))) {
            image.setRGB(w - 1, 0, white);
        }
        if (isWhite(image.getRGB(0, h - 2)) && isWhite(image.getRGB(1, h - 1)) && isWhite(image.getRGB(1, h - 2))) {
            image.setRGB(0, h - 1, white);
        }
        if (isWhite(image.getRGB(w - 2, h - 1)) && isWhite(image.getRGB(w - 1, h - 2)) && isWhite(image.getRGB(w - 2, h - 2))) {
            image.setRGB(w - 1, h - 1, white);
        }

        for (int x = 1; x < w - 1; x++) {
            int y = 0;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x - 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x - 1, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y + 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }
        for (int x = 1; x < w - 1; x++) {
            int y = h - 1;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x - 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x - 1, y - 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }

        for (int y = 1; y < h - 1; y++) {
            int x = 0;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x + 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y - 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x + 1, y + 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }

        for (int y = 1; y < h - 1; y++) {
            int x = w - 1;
            if (isBlack(image.getRGB(x, y))) {
                int size = 0;
                if (isWhite(image.getRGB(x - 1, y))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x, y - 1))) {
                    size++;
                }
                // 斜上下为空时，去掉此点
                if (isWhite(image.getRGB(x - 1, y + 1))) {
                    size++;
                }
                if (isWhite(image.getRGB(x - 1, y - 1))) {
                    size++;
                }
                if (size >= 5) {
                    image.setRGB(x, y, white);
                }
            }
        }

        // 降噪，以1个像素点为单位
        for (int y = 1; y < h - 1; y++) {
            for (int x = 1; x < w - 1; x++) {
                if (isBlack(image.getRGB(x, y))) {
                    int size = 0;
                    //上下左右均为空时，去掉此点
                    if (isWhite(image.getRGB(x - 1, y))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x + 1, y))) {
                        size++;
                    }
                    //上下均为空时，去掉此点
                    if (isWhite(image.getRGB(x, y + 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x, y - 1))) {
                        size++;
                    }
                    //斜上下为空时，去掉此点
                    if (isWhite(image.getRGB(x - 1, y + 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x + 1, y - 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x + 1, y + 1))) {
                        size++;
                    }
                    if (isWhite(image.getRGB(x - 1, y - 1))) {
                        size++;
                    }
                    if (size >= 8) {
                        image.setRGB(x, y, white);
                    }
                }
            }
        }
        return image;
    }

    private static boolean isBlack(int colorInt) {
        Color color = new Color(colorInt);
        if (color.getRed() + color.getGreen() + color.getBlue() <= 300) {
            return true;
        }
        return false;
    }

    private static boolean isWhite(int colorInt) {
        Color color = new Color(colorInt);
        if (color.getRed() + color.getGreen() + color.getBlue() > 300) {
            return true;
        }
        return false;
    }

    private int isBlack(int colorInt, int whiteThreshold) {
        final Color color = new Color(colorInt);
        if (color.getRed() + color.getGreen() + color.getBlue() <= whiteThreshold) {
            return 1;
        }
        return 0;
    }

    // =================================================================================================================

    public static BufferedImage rotateImage(BufferedImage image, int angel) {
        int src_width = image.getWidth(null);
        int src_height = image.getHeight(null);
        // calculate the new image size
        Rectangle rect_des = CalcRotatedSize(new Rectangle(new Dimension(src_width, src_height)), angel);
        BufferedImage res = new BufferedImage(rect_des.width, rect_des.height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = res.createGraphics();
        // transform
        g2.translate((rect_des.width - src_width) / 2, (rect_des.height - src_height) / 2);
        g2.rotate(Math.toRadians(angel), src_width / 2, src_height / 2);

        g2.drawImage(image, null, null);
        return res;
    }

    /**
     * 计算旋转参数
     *
     * @param src   范围
     * @param angel 角度
     * @return
     */
    private static Rectangle CalcRotatedSize(Rectangle src, int angel) {
        // 如果大于90，需要做转换处理
        if (angel >= 90) {
            if (angel / 90 % 2 == 1) {
                int temp = src.height;
                src.height = src.width;
                src.width = temp;
            }
            angel = angel % 90;
        }

        double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
        double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
        double angel_alpha = (Math.PI - Math.toRadians(angel)) / 2;
        double angel_dalta_width = Math.atan((double) src.height / src.width);
        double angel_dalta_height = Math.atan((double) src.width / src.height);

        int len_dalta_width = (int) (len * Math.cos(Math.PI - angel_alpha - angel_dalta_width));
        int len_dalta_height = (int) (len * Math.cos(Math.PI - angel_alpha - angel_dalta_height));
        int des_width = src.width + len_dalta_width * 2;
        int des_height = src.height + len_dalta_height * 2;
        return new Rectangle(new Dimension(des_width, des_height));
    }

    // =================================================================================================================

    public static BufferedImage zoomImage(BufferedImage image, int w, int h) {
        //新生成结果图片
        BufferedImage zoomImg = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
        //缩放 getScaledInstance(width,height,hints)
        Image img = image.getScaledInstance(w, h, Image.SCALE_SMOOTH);
        zoomImg.getGraphics().drawImage(img, 0, 0, null);
        return zoomImg;
    }

    // =================================================================================================================

    public static BufferedImage cutImage(BufferedImage image, int x, int y, int w, int h) {
        return image.getSubimage(x, y, w, h);
    }

    // =================================================================================================================

    public static BufferedImage waterMarkText(BufferedImage image, String text, int x, int y, Font font, Color color) {
        BufferedImage dstImage = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());
        dstImage.setData(image.getData());
        Graphics2D graphics2D = dstImage.createGraphics();
        // 设置"抗锯齿"的属性
        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        // 设置字体类型和大小
        graphics2D.setFont(font);
        // 设置颜色
        graphics2D.setColor(color);
        // 添加文字
        graphics2D.drawString(text, x, y);
        graphics2D.dispose();
        return dstImage;
    }

    public static BufferedImage waterMarkImage(BufferedImage image, BufferedImage waterImage, int x, int y, float alpha) {
        BufferedImage dstImage = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());
        dstImage.setData(image.getData());
        Graphics2D graphics2D = dstImage.createGraphics();
        // 设置设置透明度为0.5
        graphics2D.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));
        // 添加水印
        graphics2D.drawImage(waterImage, x, y, null);
        // 关闭透明度
        // graphics2D.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
        graphics2D.dispose();
        return dstImage;
    }

    // =================================================================================================================

    /**
     * 图片对比度设置
     *
     * @param image 原始图片
     * @param rate  对比率
     * @return 调整后的图片(引用原始图片)
     */
    public static BufferedImage imageContrast(BufferedImage image, float rate) {
        for (int x = image.getMinX(); x < image.getWidth(); x++) {
            for (int y = image.getMinY(); y < image.getHeight(); y++) {
                Object data = image.getRaster().getDataElements(x, y, null);
                int dataRed = image.getColorModel().getRed(data);
                int dataBlue = image.getColorModel().getBlue(data);
                int dataGreen = image.getColorModel().getGreen(data);

                float newRed = dataRed * rate > 255 ? 255 : dataRed * rate;
                newRed = newRed < 0 ? 0 : newRed;
                float newGreen = dataGreen * rate > 255 ? 255 : dataGreen * rate;
                newGreen = newGreen < 0 ? 0 : newGreen;
                float newBlue = dataBlue * rate > 255 ? 255 : dataBlue * rate;
                newBlue = newBlue < 0 ? 0 : newBlue;
                Color dataColor = new Color(newRed, newGreen, newBlue);
                image.setRGB(x, y, dataColor.getRGB());
            }
        }

        return image;
    }

}
