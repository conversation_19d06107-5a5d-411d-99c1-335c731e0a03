package com.erdos.coal.ocr.image;

import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.TessAPI;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * 图片识别处理器
 */
public class OcrRecognitionHandler<T> {

    private String lanEnvPath; //语言库路径
    private IOcrRecognitionParser<T> ocrParser; //解析器

    public static <T> OcrRecognitionHandler<T> newInstance(String lanEvn, IOcrRecognitionParser<T> parser) {
        return new OcrRecognitionHandler<>(lanEvn, parser);
    }

    public OcrRecognitionHandler(String lanEvn, IOcrRecognitionParser<T> parser) {
        this.lanEnvPath = lanEvn;
        this.ocrParser = parser;
    }

    private String doOCR(String filePath) throws IOException {
        return getOcrResult(ImageIO.read(new File(filePath)));
    }

    private String doOCR(File file) throws IOException {
        return getOcrResult(ImageIO.read(file));
    }

    private String doOCR(BufferedImage image) throws IOException {
        return getOcrResult(image);
    }

    private String getOcrResult(BufferedImage image) {
        try {
            ITesseract instance = new Tesseract();
            instance.setDatapath(lanEnvPath);
            // instance.setLanguage("eng+chi_sim");
            instance.setLanguage("chi_sim"); // 识别语言
            // instance.setTessVariable("user_defined_dpi", "96"); // 设置分辨率
            instance.setTessVariable("user_defined_dpi", "300"); // 设置分辨率
            // 设置识别引擎
            // OCR Engine modes:
            // 0 Legacy engine only. // 原始模式
            // 1 Neural nets LSTM engine only. // LSTM　神经网络
            // 2 Legacy + LSTM engines. // 双引擎(较耗费资源)
            // 3 Default, based on what is available. // 基于可用的默认值
            instance.setOcrEngineMode(TessAPI.TessOcrEngineMode.OEM_DEFAULT);
            return instance.doOCR(image);
        } catch (TesseractException e) {
            System.err.println(e.getMessage());
        }
        return null;
    }

    public T execOCR(String path) throws IOException {
        if (ocrParser != null)
            return ocrParser.doParse(this.doOCR(path));
        return null;
    }

    public T execOCR(File file) throws IOException {
        if (ocrParser != null)
            return ocrParser.doParse(this.doOCR(file));
        return null;
    }

    public T execOCR(BufferedImage image) throws IOException {
        if (ocrParser != null)
            return ocrParser.doParse(this.doOCR(image));
        return null;
    }
}
