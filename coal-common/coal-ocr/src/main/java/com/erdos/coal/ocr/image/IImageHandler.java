package com.erdos.coal.ocr.image;

import java.awt.*;
import java.awt.image.BufferedImage;

/**
 * 图片文件处理集
 * <p>
 * 灰度化，二值化，降噪
 * 旋转，裁剪，缩放
 * 文本水印，图片水印
 */
interface IImageHandler {

    /**
     * 图片灰度化
     *
     * @param image       源
     * @param grayStatus: 1最大值法，2最小值法，3均值法，4加权法
     */
    BufferedImage grayImage(BufferedImage image, int grayStatus);

    /**
     * 二值化
     *
     * @param image     源
     * @param binarySW: 二值化阀值
     */
    BufferedImage binaryImage(BufferedImage image, int binarySW);

    /**
     * 降噪
     *
     * @param image 源
     */
    BufferedImage denoiseImage(BufferedImage image);

    /**
     * 调整图片角度(旋转)
     *
     * @param image 源
     * @param angel 角度
     * @return
     */
    BufferedImage rotateImage(BufferedImage image, int angel);

    /**
     * 缩放图片
     *
     * @param w 缩放后宽度
     * @param h 缩放后的高度
     */
    BufferedImage zoomImage(BufferedImage image, int w, int h);

    /**
     * 图片裁剪通用接口
     *
     * @param image 源
     * @param x     x坐标
     * @param y     y坐标
     * @param w     裁剪后图片宽度
     * @param h     裁剪后图片高度
     */
    BufferedImage cutImage(BufferedImage image, int x, int y, int w, int h);

    /**
     * 文本水印
     *
     * @param image 源
     * @param text  文本
     * @param x     水印x坐标
     * @param y     水印y坐标
     */
    BufferedImage waterMarkText(BufferedImage image, String text, int x, int y, Font font, Color color);

    /**
     * 图片水印
     *
     * @param image      源
     * @param waterImage 水印图片
     * @param x          水印x坐标
     * @param y          水印y坐标
     * @param alpha      透明度
     */
    BufferedImage waterMarkImage(BufferedImage image, BufferedImage waterImage, int x, int y, float alpha);
}
