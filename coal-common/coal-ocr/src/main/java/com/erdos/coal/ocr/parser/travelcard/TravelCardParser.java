package com.erdos.coal.ocr.parser.travelcard;

import com.erdos.coal.ocr.entity.TravelCard;
import com.erdos.coal.ocr.parser.AbstractOcrParser;
import com.erdos.coal.ocr.parser.IOcrParser;
import com.erdos.coal.ocr.utils.OpenCVUtil;
import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;

/**
 * 行程卡解析器
 */
public class TravelCardParser extends AbstractOcrParser implements IOcrParser<TravelCard> {

    private final static String fileExtension = ".jpg";

    public TravelCardParser(String dataPath) {
        super(dataPath);
    }

    /**
     * 裁剪
     *
     * @param correctMat
     * @return
     */
    private Mat cutRect(Mat correctMat) {
        //获取最大矩形
        RotatedRect rect = OpenCVUtil.findMaxRect(correctMat);

        Point[] rectPoint = new Point[4];
        rect.points(rectPoint);

        System.out.println("(" + rectPoint[0].x + ", " + rectPoint[0].y + ")");
        System.out.println("(" + rectPoint[1].x + ", " + rectPoint[1].y + ")");
        System.out.println("(" + rectPoint[2].x + ", " + rectPoint[2].y + ")");
        System.out.println("(" + rectPoint[3].x + ", " + rectPoint[3].y + ")");

        int left = (int) Math.abs(rectPoint[0].x);
        int up = (int) Math.abs(Math.abs(rectPoint[1].y) < Math.abs(rectPoint[2].y) ? rectPoint[1].y : rectPoint[2].y);
        int width = (int) Math.abs(rectPoint[2].x - rectPoint[0].x);
        int height = (int) Math.abs(rectPoint[3].y - rectPoint[1].y);

        System.out.println("Left: " + left);
        System.out.println("Up: " + up);
        System.out.println("Width: " + width);
        System.out.println("Height: " + height);

        if (left + width > correctMat.width()) {
            width = correctMat.width() - left;
        }
        if (up + height > correctMat.height()) {
            height = correctMat.height() - up;
        }

        Mat result = new Mat(correctMat, new Rect(left, up, width, height));
        return result;
    }

    /**
     * 作用：获取文字区域矩形框
     *
     * @param img 膨胀与腐蚀后的Mat矩阵图像
     * @return
     */
    private List<RotatedRect> findTextRegionRect(Mat img) {
        List<RotatedRect> rects = new ArrayList<>();
        //查找轮廓
        List<MatOfPoint> contours = new ArrayList<>();

        //TODO: 边缘提取
        //image: 已经二值化的原图
        //contours: 参数为检测的轮廓数组，每一个轮廓用一个MatOfPoint类型的List表示
        //hiararchy: 参数和轮廓个数相同，每个轮廓contours[i]对应4个hierarchy元素hierarchy[i][0] ~hierarchy[i][3]，分别表
        // 示后一个轮廓、前一个轮廓、父轮廓、内嵌轮廓的索引编号，如果没有对应项，该值设置为负数。
        //
        //mode：提取模式.
        //CV_RETR_EXTERNAL - 只提取最外层的轮廓
        //CV_RETR_LIST - 提取所有轮廓，并且放置在 list 中
        //CV_RETR_CCOMP - 提取所有轮廓，并且将其组织为两层的 hierarchy: 顶层为连通域的外围边界，次层为洞的内层边界。
        //CV_RETR_TREE - 提取所有轮廓，并且重构嵌套轮廓的全部 hierarchy
        //
        //method：逼近方法 (对所有节点, 不包括使用内部逼近的 CV_RETR_RUNS).
        //CV_CHAIN_CODE - Freeman 链码的输出轮廓. 其它方法输出多边形(定点序列).
        //CV_CHAIN_APPROX_NONE - 将所有点由链码形式翻译(转化）为点序列形式
        //CV_CHAIN_APPROX_SIMPLE - 压缩水平、垂直和对角分割，即函数只保留末端的象素点;
        //CV_CHAIN_APPROX_TC89_L1,
        //CV_CHAIN_APPROX_TC89_KCOS - 应用 Teh-Chin 链逼近算法. CV_LINK_RUNS - 通过连接为 1 的水平碎片使用完全不同的轮廓提取算法。
        // 仅有 CV_RETR_LIST 提取模式可以在本方法中应用.
        //
        //offset：
        //每一个轮廓点的偏移量. 当轮廓是从图像 ROI 中提取出来的时候，使用偏移量有用，因为可以从整个图像上下文来对轮廓做分析.

        // Imgproc.findContours(img, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(-1, -1));// 不能被倾斜校正

        //Imgproc.findContours(img, contours, hierarchy, Imgproc.RETR_CCOMP, Imgproc.CHAIN_APPROX_SIMPLE, new Point(-1, -1));
        Imgproc.findContours(img, contours, new Mat(), Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        // Imgproc.findContours(img, contours, hierarchy, RETR_EXTERNAL, Imgproc.CHAIN_APPROX_NONE, new Point(0, 0));// 不能被倾斜校正

        int img_width = img.width();
        int img_height = img.height();

        //筛选符合条件的矩形
        for (int i = contours.size() - 1; i >= 0; i--) {
            MatOfPoint contour = contours.get(i);

            // 计算轮廓面积
            //contour，输入的二维点集（轮廓顶点），可以是 vector 或 Mat 类型。
            //oriented，面向区域标识符。有默认值 false。若为 true，该函数返回一个带符号的面积值，
            // 正负取决于轮廓的方向（顺时针还是逆时针）。若为 false，表示以绝对值返回。
            double area = Imgproc.contourArea(contour);
            if (area < 1000)//原来是1000
                continue;

            //轮廓近似，approxPolyDP函数
            //arcLength 函数用于计算封闭轮廓的周长或曲线的长度。
            //curve，输入的二维点集（轮廓顶点），可以是 vector 或 Mat 类型。
            //closed，用于指示曲线是否封闭。
            double epsilon = 0.001 * Imgproc.arcLength(new MatOfPoint2f(contour.toArray()), true);
            MatOfPoint2f approxCurve = new MatOfPoint2f();
            //以指定的精度近似生成多边形曲线。
            //函数逼近一条曲线或另一条曲线/顶点较少的多边形，使它们之间的距离小于或等于指定的精度。它使用Douglas-Peucker算法
            //curve, 输入的点集（存储在std::vector或Mat中的二维点的输入向量）
            //approxCurve, 输出的点集，当前点集是能最小包容指定点集的。draw出来即是一个多边形；
            //epsilon, 指定的精度，也即是原始曲线与近似曲线之间的最大距离。
            //closed, 若为true,则说明近似曲线是闭合的，它的首位都是相连，反之，若为false，则断开。
            Imgproc.approxPolyDP(new MatOfPoint2f(contour.toArray()), approxCurve, epsilon, true);

            //找到最小矩形
            RotatedRect rect = Imgproc.minAreaRect(new MatOfPoint2f(contour.toArray()));
            //计算高和宽
            int m_width = rect.boundingRect().width;
            int m_height = rect.boundingRect().height;

            System.out.println("width = " + m_width);

            if (m_height < 20)
                continue;

            //过滤太窄的矩形，留下扁的
//            if (m_width * 1.2 < m_height)
//                continue;
            if (m_width < 2 * m_height)
                continue;

            if (img_width == rect.boundingRect().br().x)
                continue;
            if (img_height == rect.boundingRect().br().y)
                continue;

            //将符合条件的rect添加到rects集合中
            rects.add(rect);
        }

        return rects;
    }

    private List<BufferedImage> getImageList(BufferedImage originalBufferedImage) {
        // 利用 OpenCV 对图像进行处理

        //TODO: 将bufferedImage转换为Mat矩阵图像
        Mat image = OpenCVUtil.BufImg2Mat(originalBufferedImage);

        //TODO: 裁剪
        Mat cuttedImg = this.cutRect(image);
        //TODO: 标准化,缩放图片
        Mat zoomedImg = OpenCVUtil.zoom(cuttedImg);
        //TODO: 均值漂移滤波
        //opencv非局部均值去噪（需要三通道的Mat图像）--去噪后仍为三通道
        Mat denoiseImg = OpenCVUtil.pyrMeanShiftFiltering(zoomedImg);
        //TODO: 灰度化--转为单通道
        Mat grayImg = OpenCVUtil.gray(denoiseImg);
        //TODO: 二值化
        //Mat binaryImg = OpenCVUtil.binaryzation(grayImg);
        Mat binaryImg = OpenCVUtil.binarization(grayImg);
        //TODO: 膨胀与腐蚀
        //Mat corrodedImg = OpenCVUtil.corrosion(binaryImg);
        Mat dilationImg = OpenCVUtil.preprocess(binaryImg);
        //TODO: 查找和筛选文字区域
        List<RotatedRect> rects = this.findTextRegionRect(dilationImg);
        //TODO: 倾斜校正
        Mat correctedImg = OpenCVUtil.correction(rects, binaryImg);

        //TODO: 将倾斜校正标准化后的Mat矩阵图像输出
        //OpenCVUtil.matToFile(correctedImg, "D:\\Desktop\\aaa\\a0" + fileExtension);

        //TODO: 调节亮度
//        int brightness = this.imageBrightness(newBufferedImage);
//        BufferedImage brightnessImg;
//        //如果亮度>180，则亮度减少60
//        if (brightness > 180)
//            brightnessImg = this.imageBrightness(newBufferedImage, -60);
//        else
//            brightnessImg = newBufferedImage;
//        OpenCVUtil.bufferedImageToFile(brightnessImg, "D:\\Desktop\\aaa\\a2" + fileExtension);

        logger.info("rects.size:" + rects.size());

        //TODO: 截取并显示轮廓图片
        List<BufferedImage> result = new ArrayList<>();
        for (RotatedRect rect : rects) {
            //裁剪识别区域
            Mat dst = OpenCVUtil.cropImage(correctedImg, rect.boundingRect());
            BufferedImage tmpBufferedImage = OpenCVUtil.Mat2BufImg(dst, fileExtension);
            result.add(tmpBufferedImage);
        }

        //用红线画出找到的轮廓
//        Mat lineImg = correctedImg.clone(); //线框
//        for (RotatedRect rect : rects) {
//            Point[] point = new Point[4];
//            rect.points(point);
//            Imgproc.line(lineImg, point[0], point[1], new Scalar(0, 0, 255));
//            Imgproc.line(lineImg, point[1], point[2], new Scalar(0, 0, 255));
//            Imgproc.line(lineImg, point[2], point[3], new Scalar(0, 0, 255));
//            Imgproc.line(lineImg, point[3], point[0], new Scalar(0, 0, 255));
//        }
//        OpenCVUtil.matToFile(lineImg, "D:\\Desktop\\aaa\\a1" + fileExtension);

        return result;
    }

    @Override
    public TravelCard doRecognition(BufferedImage src) {

        // =========================================================
        List<BufferedImage> images = this.getImageList(src);

//        AtomicInteger idx = new AtomicInteger();
//        images.forEach((item) -> {
//            OpenCVUtil.bufferedImageToFile(item, "D:\\Desktop\\aaa\\" + idx + fileExtension);
//            idx.getAndIncrement();
//        });

        TravelCard result = new TravelCard();

        logger.info("识别结果如下：");

        String s = "";
        //String mStr = s.replaceAll("[^\\u4e00-\\u9fa5]", "").trim();

        //动态形程卡
        this.setLanguage("eng+chi_sim");    //中英文混合识别需用 + 分隔，chi_sim：简体中文，eng：英文
        s = this.doOCR(images.get(1)).replaceAll(" ", "");
        //logger.info(s);
        //String mStr = s.replaceAll("[^\\u4e00-\\u9fa5]", "").trim();
        if (s.contains("动态行程卡")) {
            String mStr = s.trim();
            result.setMobileString(mStr);
        }

        //更新于:
        this.setLanguage("eng+chi_sim");    //中英文混合识别需用 + 分隔，chi_sim：简体中文，eng：英文
        s = this.doOCR(images.get(2)).replaceAll(" ", "");
        if (s.contains("更新于:")) {
            String mStr = s.replaceAll("更新于:", "").trim();
            result.setTimeString(mStr);
        }

        //处理 您于前14天内到达或途经:
        // 内蒙古自治您于前14天内到达或途经二区鄂尔多斯市,宁豆回族自治区石嘴山市，内蒙古自治区乌海市
        result.setTravelString("");
        for (int i = 3; i < images.size(); i++) {
            this.setLanguage("eng+chi_sim");    //中英文混合识别需用 + 分隔，chi_sim：简体中文，eng：英文
            s = this.doOCR(images.get(i)).replaceAll(" ", "");

            String mStr = s.trim();
            mStr = result.getTravelString() + mStr;
            result.setTravelString(mStr);
        }
        s = result.getTravelString()
                .replaceAll("您于前14天内到达或途经:", "")
                .replaceAll("您于前14天内到达或途经：", "")
                .replaceAll("您于前14天内到达或途经二", "");

        result.setTravelString(s);

        return result;
    }
}
