@echo off 
echo �������ļ�...

del /s *.iml

del /f /s /q .idea\*.*

rd /s /q .idea

rd /s /q logs

echo �����ļ��������.

echo ��������ļ�...


for %%a in (
	coal-common\coal-aliyun
	coal-common\coal-core
	coal-common\coal-ds-mongo
	coal-common\coal-map
	coal-common\coal-ocr
	coal-common\coal-transaction
	coal-park
	coal-gateway
) do (
	echo ���� %%a
	rd /s /q %%a\logs
	del /f /s /q %%a\target\*.*
	rd /s /q %%a\target
)

echo �������!